---
tags: [problem/leetcode, lc/hard, topic/array, pattern/two_pointers, pattern/dynamic_programming, course/labuladong, lc/lc42]
aliases: [LC42, Trapping Rain Water]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/如何高效解决接雨水问题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 42. Trapping Rain Water
> Solution approaches detailed in [[Interview/Concept/Algorithms/Array Manipulation/Trapping Rain Water Pattern|Trapping Rain Water Pattern]].

# LeetCode 42: Trapping Rain Water

## Problem Statement
Given `n` non-negative integers representing an elevation map where the width of each bar is 1, compute how much water it can trap after raining.

**Official Link:** [LeetCode 42. Trapping Rain Water](https://leetcode.com/problems/trapping-rain-water/)

## Solution Approach: Two Pointers (Optimized)
The most optimal solution uses a two-pointer approach with $O(N)$ time and $O(1)$ space. It relies on maintaining `l_max` (max height from left up to `left` pointer) and `r_max` (max height from right up to `right` pointer) and calculating trapped water based on the smaller of these two boundary maximums.

See [[Interview/Concept/Algorithms/Array Manipulation/Trapping Rain Water Pattern|Trapping Rain Water Pattern]] for detailed explanation of all approaches (Brute-Force, DP with precomputed maxes, and Two-Pointers).

### Python Solution (Two Pointers)
```python
class Solution:
    def trap(self, height: list[int]) -> int:
        n = len(height)
        if n <= 2: # Cannot trap water with 0, 1, or 2 bars
            return 0

        left, right = 0, n - 1
        l_max_height = 0 # Max height seen from the left up to 'left' pointer
        r_max_height = 0 # Max height seen from the right up to 'right' pointer
        trapped_water = 0

        while left < right:
            # Update current max heights from both ends
            l_max_height = max(l_max_height, height[left])
            r_max_height = max(r_max_height, height[right])

            # The crucial insight:
            # If l_max_height < r_max_height, then the water level at the `left` pointer
            # is determined by `l_max_height`. Any water trapped at `height[left]`
            # will be `l_max_height - height[left]`. We can safely process `left`
            # because we know `r_max_height` is greater, so it won't be the limiting factor
            # for the current `left` position.
            if l_max_height < r_max_height:
                trapped_water += l_max_height - height[left]
                left += 1
            # Else (l_max_height >= r_max_height), the water level at the `right` pointer
            # is determined by `r_max_height`.
            else:
                trapped_water += r_max_height - height[right]
                right -= 1

        return trapped_water
```

## Complexity Analysis
- **Time Complexity:** $O(N)$, one pass with two pointers.
- **Space Complexity:** $O(1)$.

## 总结 (Summary)
LC42 can be solved optimally using a two-pointer approach. By maintaining the maximum heights encountered from both left and right sides, we can determine the trapped water at each step by considering the shorter of the two "walls" (`l_max_height` and `r_max_height`) relative to the current bar being processed.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
