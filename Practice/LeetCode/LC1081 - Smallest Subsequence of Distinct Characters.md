---
tags: [problem/leetcode, lc/medium, topic/string, topic/stack, pattern/monotonic_stack]
aliases: [LC1081, Smallest Subsequence of Distinct Characters]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/拓展：数组去重问题（困难版）.md
---

# LC1081 - Smallest Subsequence of Distinct Characters

## Problem Statement
(To be filled from LeetCode - Visit [https://leetcode.com/problems/smallest-subsequence-of-distinct-characters/](https://leetcode.com/problems/smallest-subsequence-of-distinct-characters/))

## Solution Approach
This problem is identical to LC316 and is solved using the monotonic stack technique described in [[Interview/Concept/Algorithms/Stack Applications/02 - Monotonic Stack for Smallest Subsequence|Monotonic Stack for Smallest Subsequence]]. The solution code for LC316 can be used directly.

### Python Solution
```python
# Same as LC316
import collections

class Solution:
    def smallestSubsequence(self, s: str) -> str: # Method name matches LC1081
        result_stack = []
        # Assuming only lowercase English letters
        in_stack_flags = [False] * 26 
        char_counts = collections.Counter(s)

        char_to_idx = lambda char_val: ord(char_val) - ord('a')
        
        for char_val in s:
            char_idx = char_to_idx(char_val)
            char_counts[char_val] -= 1

            if in_stack_flags[char_idx]:
                continue

            while (result_stack and 
                   char_val < result_stack[-1] and 
                   char_counts[result_stack[-1]] > 0):
                
                popped_char_val = result_stack.pop()
                in_stack_flags[char_to_idx(popped_char_val)] = False
            
            result_stack.append(char_val)
            in_stack_flags[char_idx] = True
            
        return "".join(result_stack)
```

## Complexity Analysis
- Time: $O(N)$
- Space: $O(K)$ where K is alphabet size (26)

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
