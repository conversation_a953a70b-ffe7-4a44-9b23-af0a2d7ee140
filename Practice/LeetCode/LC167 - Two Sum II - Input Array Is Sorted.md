---
tags: [problem/leetcode, lc/medium, topic/array, topic/two_pointers, pattern/two_sum_sorted]
aliases: [LC167, LeetCode 167. Two Sum II - Input Array Is Sorted]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 167. Two Sum II - Input Array Is Sorted
> Solution from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道数组题目.md]].

# LeetCode 167: Two Sum II - Input Array Is Sorted

## Problem Statement

Given a **1-indexed** array of integers `numbers` that is already **sorted in non-decreasing order**, find two numbers such that they add up to a specific `target` number. Let these two numbers be `numbers[index1]` and `numbers[index2]` where `1 <= index1 < index2 <= numbers.length`.

Return the indices of the two numbers, `index1` and `index2`, **added by one** as an integer array `[index1, index2]` of length 2.

You may assume that each input would have **exactly one solution** and you may not use the same element twice. Your solution must use only constant extra space.

**Official Link:** [LeetCode 167. Two Sum II - Input Array Is Sorted](https://leetcode.com/problems/two-sum-ii-input-array-is-sorted/)

**Example 1:**
Input: `numbers = [2,7,11,15]`, `target = 9`
Output: `[1,2]`
Explanation: The sum of 2 and 7 is 9. Therefore, index1 = 1, index2 = 2. We return `[1, 2]`.

## Solution Approach: Two Pointers (Left-Right)

Since the input array `numbers` is sorted, this problem is a prime candidate for the [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Left-Right Pointers technique for arrays]].

1.  Initialize `left` pointer to the start of the array (index 0).
2.  Initialize `right` pointer to the end of the array (index `len(numbers) - 1`).
3.  While `left < right`:
    a.  Calculate `current_sum = numbers[left] + numbers[right]`.
    b.  If `current_sum == target`, we found the pair. Return `[left + 1, right + 1]` (since the problem asks for 1-indexed results).
    c.  If `current_sum < target`, we need a larger sum. Since the array is sorted, increment `left` to consider a larger number for the first element.
    d.  If `current_sum > target`, we need a smaller sum. Decrement `right` to consider a smaller number for the second element.
4.  If the loop finishes, it means no solution was found (though the problem guarantees one).

### Python Solution

```python
class Solution:
    def twoSum(self, numbers: list[int], target: int) -> list[int]:
        left, right = 0, len(numbers) - 1

        while left < right:
            current_sum = numbers[left] + numbers[right]
            if current_sum == target:
                # Problem asks for 1-indexed results
                return [left + 1, right + 1]
            elif current_sum < target:
                left += 1  # Need a larger sum, move left pointer right
            else: # current_sum > target
                right -= 1 # Need a smaller sum, move right pointer left
        
        return [] # Should not be reached due to problem constraints (exactly one solution)
```

## Complexity Analysis
-   **Time Complexity:** $O(N)$, where $N$ is the length of the `numbers` array. In the worst case, the `left` and `right` pointers traverse the entire array once.
-   **Space Complexity:** $O(1)$, as we only use a constant amount of extra space for the pointers and `current_sum` variable. This meets the problem's constraint.

## Visualization

Let `numbers = [2,7,11,15]`, `target = 9`.

```tikz
\begin{tikzpicture}[
    arr_cell/.style={draw, rectangle, minimum size=0.8cm, font=\sffamily\small},
    ptr_label/.style={font=\sffamily\bfseries\tiny, below=0.1cm},
    highlight_pair/.style={fill=green!20},
    iteration_note/.style={font=\sffamily\scriptsize, align=center, text width=4cm}
]

% Initial state
\node at (2*1.2, 1) [iteration_note] {Initial: L=0, R=3\\`numbers[0]`=2, `numbers[3]`=15\\Sum = 2+15=17};
\node[arr_cell] (c0_1) at (0*1.2, 0) {2}; \node[ptr_label, red] at (c0_1.south) {L};
\node[arr_cell] (c1_1) at (1*1.2, 0) {7};
\node[arr_cell] (c2_1) at (2*1.2, 0) {11};
\node[arr_cell] (c3_1) at (3*1.2, 0) {15}; \node[ptr_label, blue] at (c3_1.south) {R};
\node at (5*1.2, 0) [iteration_note] {$17 > 9 \implies R--$};

% Iteration 2
\begin{scope}[yshift=-2.5cm]
    \node at (2*1.2, 1) [iteration_note] {L=0, R=2\\`numbers[0]`=2, `numbers[2]`=11\\Sum = 2+11=13};
    \node[arr_cell] (c0_2) at (0*1.2, 0) {2}; \node[ptr_label, red] at (c0_2.south) {L};
    \node[arr_cell] (c1_2) at (1*1.2, 0) {7};
    \node[arr_cell] (c2_2) at (2*1.2, 0) {11}; \node[ptr_label, blue] at (c2_2.south) {R};
    \node[arr_cell] (c3_2) at (3*1.2, 0) {15};
    \node at (5*1.2, 0) [iteration_note] {$13 > 9 \implies R--$};
\end{scope}

% Iteration 3
\begin{scope}[yshift=-5cm]
    \node at (2*1.2, 1) [iteration_note] {L=0, R=1\\`numbers[0]`=2, `numbers[1]`=7\\Sum = 2+7=9};
    \node[arr_cell, highlight_pair] (c0_3) at (0*1.2, 0) {2}; \node[ptr_label, red] at (c0_3.south) {L};
    \node[arr_cell, highlight_pair] (c1_3) at (1*1.2, 0) {7}; \node[ptr_label, blue] at (c1_3.south) {R};
    \node[arr_cell] (c2_3) at (2*1.2, 0) {11};
    \node[arr_cell] (c3_3) at (3*1.2, 0) {15};
    \node at (5*1.2, 0) [iteration_note] {$9 == 9$. Found!\\Return `[L+1, R+1]` = `[1,2]`};
\end{scope}

\end{tikzpicture}
```

## 总结 (Summary)
- For a sorted array, the Two Sum problem can be solved efficiently in $O(N)$ time and $O(1)$ space using the left-right two-pointer approach.
- This method relies on the sorted property to intelligently move pointers and narrow down the search space.
- This is a common pattern for problems involving finding pairs or subsequences in sorted arrays.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Previous: [[Interview/Practice/LeetCode/LC283 - Move Zeroes|LC283 - Move Zeroes]] (Example - order may vary)
Next: [[Interview/Practice/LeetCode/LC344 - Reverse String|LC344 - Reverse String]] (Example - order may vary)
Concepts: [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]]
