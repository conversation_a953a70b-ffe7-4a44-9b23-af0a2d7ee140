---
tags: [problem/leetcode, lc/easy, topic/tree, topic/bst, pattern/lca]
aliases: [LC235, LCA of BST]
---
> [!NOTE] Source Annotation
> Problem: LC235 - Lowest Common Ancestor of a Binary Search Tree
> This is a placeholder note. Solution uses [[Interview/Concept/Algorithms/Tree Traversal/07 - Lowest Common Ancestor (LCA) Framework|LCA Framework]].

# LC235 - Lowest Common Ancestor of a Binary Search Tree

## Problem Statement
*(To be filled from LeetCode)*

## Solution Approach
*(To be filled based on [[Interview/Concept/Algorithms/Tree Traversal/07 - Lowest Common Ancestor (LCA) Framework|LCA Framework]] and specific problem constraints)*

### Python Solution (Placeholder)
```python
# Definition for a binary tree node.
# class TreeNode:
#     def __init__(self, x):
#         self.val = x
#         self.left = None
#         self.right = None

class Solution:
    def lowestCommonAncestor(self, root, p, q): # Signature might vary
        # TODO: Implement solution
        pass
```

## Complexity Analysis
*(To be filled)*

## 总结 (Summary)
*(To be filled)*

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Tree Traversal/07 - Lowest Common Ancestor (LCA) Framework|LCA Framework]]
