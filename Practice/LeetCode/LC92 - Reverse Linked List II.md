---
tags: [problem/leetcode, lc/medium, topic/linked_list, pattern/reversal, concept/recursion]
aliases: [LC92, LeetCode 92, Reverse Linked List II, 反转链表 II]
---

# LeetCode 92: Reverse Linked List II

## Problem Statement

Given the head of a singly linked list and two integers `left` and `right` where `left <= right`, reverse the nodes of the list from position `left` to position `right`, and return the reversed list.

**Examples:**
- Input: `head = [1,2,3,4,5]`, `left = 2`, `right = 4`, Output: `[1,4,3,2,5]`
- Input: `head = [5]`, `left = 1`, `right = 1`, Output: `[5]`

**Constraints:**
- The number of nodes in the list is `n`
- `1 <= n <= 500`
- `-500 <= Node.val <= 500`
- `1 <= left <= right <= n`

**Follow up:** Could you do it in one pass?

**Official Link:** [LeetCode 92. Reverse Linked List II](https://leetcode.com/problems/reverse-linked-list-ii/)

## 🎯 Understanding the Partial Reversal Problem

### Visualization of the Challenge

```tikz
\begin{tikzpicture}[
    node/.style={circle, draw, minimum width=0.8cm, font=\tiny, fill=blue!30},
    reverse_node/.style={circle, draw, minimum width=0.8cm, font=\tiny, fill=red!30},
    link/.style={->, thick, blue},
    reverse_link/.style={->, thick, red},
    example_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=4cm, align=center}
]

% Original list: [1,2,3,4,5], reverse from position 2 to 4
\node at (6, 8) {\bfseries Partial Reversal: Positions 2 to 4};

\node at (1, 7) {\small Original: [1,2,3,4,5]};
\node[node] (o1) at (1, 6.5) {1};
\node[reverse_node] (o2) at (2.5, 6.5) {2};
\node[reverse_node] (o3) at (4, 6.5) {3};
\node[reverse_node] (o4) at (5.5, 6.5) {4};
\node[node] (o5) at (7, 6.5) {5};

\draw[link] (o1) -- (o2);
\draw[link] (o2) -- (o3);
\draw[link] (o3) -- (o4);
\draw[link] (o4) -- (o5);

% Highlight reversal section
\draw[dashed, red, thick] (2, 6) rectangle (6, 7);
\node at (4, 6.2) {\tiny Reverse this section};

\node at (1, 5) {\small Result: [1,4,3,2,5]};
\node[node] (r1) at (1, 4.5) {1};
\node[reverse_node] (r4) at (2.5, 4.5) {4};
\node[reverse_node] (r3) at (4, 4.5) {3};
\node[reverse_node] (r2) at (5.5, 4.5) {2};
\node[node] (r5) at (7, 4.5) {5};

\draw[link] (r1) -- (r4);
\draw[reverse_link] (r4) -- (r3);
\draw[reverse_link] (r3) -- (r2);
\draw[link] (r2) -- (r5);

\node[example_box] at (9, 6) {
    \textbf{Challenge:}\\
    Only reverse the\\
    specified portion\\
    while maintaining\\
    connections\\[0.5em]
    \textbf{Key Insight:}\\
    Reuse basic reversal\\
    with careful positioning
};

\end{tikzpicture}
```

## 💡 Solution Approaches

### Approach 1: Iterative with Position Tracking

```python
class Solution:
    def reverseBetween(self, head: ListNode, left: int, right: int) -> ListNode:
        """
        Iterative approach with explicit position tracking.
        
        Time: O(n), Space: O(1)
        """
        if not head or left == right:
            return head
        
        # Create dummy node to handle edge case where left = 1
        dummy = ListNode(0)
        dummy.next = head
        
        # Find the node before the reversal start
        prev_start = dummy
        for _ in range(left - 1):
            prev_start = prev_start.next
        
        # Start of reversal section
        start = prev_start.next
        
        # Reverse the section from left to right
        prev = None
        curr = start
        for _ in range(right - left + 1):
            next_temp = curr.next
            curr.next = prev
            prev = curr
            curr = next_temp
        
        # Connect the reversed section back
        prev_start.next = prev  # Connect to new head of reversed section
        start.next = curr       # Connect old head to remaining list
        
        return dummy.next
```

### Approach 2: Recursive Decomposition

```python
class Solution:
    def __init__(self):
        self.successor = None
    
    def reverseBetween(self, head: ListNode, left: int, right: int) -> ListNode:
        """
        Recursive approach using problem decomposition.
        
        Strategy: Reduce to reverseN problem.
        """
        if left == 1:
            return self.reverseN(head, right)
        
        # Move closer to the target position
        head.next = self.reverseBetween(head.next, left - 1, right - 1)
        return head
    
    def reverseN(self, head: ListNode, n: int) -> ListNode:
        """
        Reverse first n nodes of the linked list.
        """
        if n == 1:
            # Record the successor (n+1 node)
            self.successor = head.next
            return head
        
        # Recursively reverse the rest
        last = self.reverseN(head.next, n - 1)
        
        # Reverse current connection
        head.next.next = head
        head.next = self.successor
        
        return last
```

### Approach 3: One-Pass Iterative (Most Efficient)

```python
class Solution:
    def reverseBetween(self, head: ListNode, left: int, right: int) -> ListNode:
        """
        One-pass iterative solution with in-place reversal.
        
        Time: O(n), Space: O(1)
        Most efficient approach for interviews.
        """
        if not head or left == right:
            return head
        
        dummy = ListNode(0)
        dummy.next = head
        prev = dummy
        
        # Move to position left-1
        for _ in range(left - 1):
            prev = prev.next
        
        # Start reversing from position left
        curr = prev.next
        
        # Perform right-left reversals
        for _ in range(right - left):
            next_node = curr.next
            curr.next = next_node.next
            next_node.next = prev.next
            prev.next = next_node
        
        return dummy.next
```

## 🔍 Visual Algorithm Trace

```tikz
\begin{tikzpicture}[
    node/.style={circle, draw, minimum width=0.7cm, font=\tiny, fill=blue!30},
    dummy/.style={rectangle, draw, minimum width=0.7cm, minimum height=0.7cm, font=\tiny, fill=gray!30},
    pointer/.style={->, thick, red, line width=2pt},
    link/.style={->, thick, blue},
    step_box/.style={rectangle, draw, fill=yellow!20, font=\tiny, text width=3cm, align=center}
]

% Trace for reverseBetween([1,2,3,4,5], 2, 4)
\node at (6, 9) {\bfseries Algorithm Trace: Reverse positions 2-4};

% Initial state
\node at (1, 8) {\tiny Initial State};
\node[dummy] (d0) at (0.5, 7.5) {D};
\node[node] (n1_0) at (1.5, 7.5) {1};
\node[node] (n2_0) at (2.5, 7.5) {2};
\node[node] (n3_0) at (3.5, 7.5) {3};
\node[node] (n4_0) at (4.5, 7.5) {4};
\node[node] (n5_0) at (5.5, 7.5) {5};

\draw[link] (d0) -- (n1_0);
\draw[link] (n1_0) -- (n2_0);
\draw[link] (n2_0) -- (n3_0);
\draw[link] (n3_0) -- (n4_0);
\draw[link] (n4_0) -- (n5_0);

\node[step_box] at (8, 7.5) {
    dummy → 1 → 2 → 3 → 4 → 5\\
    Find position left-1\\
    Start reversal process
};

% After first reversal step
\node at (1, 6) {\tiny After Step 1};
\node[dummy] (d1) at (0.5, 5.5) {D};
\node[node] (n1_1) at (1.5, 5.5) {1};
\node[node] (n3_1) at (2.5, 5.5) {3};
\node[node] (n2_1) at (3.5, 5.5) {2};
\node[node] (n4_1) at (4.5, 5.5) {4};
\node[node] (n5_1) at (5.5, 5.5) {5};

\draw[link] (d1) -- (n1_1);
\draw[link] (n1_1) -- (n3_1);
\draw[link] (n3_1) -- (n2_1);
\draw[link] (n2_1) -- (n4_1);
\draw[link] (n4_1) -- (n5_1);

\node[step_box] at (8, 5.5) {
    Move node 3 to front\\
    1 → 3 → 2 → 4 → 5\\
    Continue reversal
};

% Final result
\node at (1, 4) {\tiny Final Result};
\node[dummy] (d2) at (0.5, 3.5) {D};
\node[node] (n1_2) at (1.5, 3.5) {1};
\node[node] (n4_2) at (2.5, 3.5) {4};
\node[node] (n3_2) at (3.5, 3.5) {3};
\node[node] (n2_2) at (4.5, 3.5) {2};
\node[node] (n5_2) at (5.5, 3.5) {5};

\draw[link] (d2) -- (n1_2);
\draw[link] (n1_2) -- (n4_2);
\draw[link] (n4_2) -- (n3_2);
\draw[link] (n3_2) -- (n2_2);
\draw[link] (n2_2) -- (n5_2);

\node[step_box] at (8, 3.5) {
    Final: 1 → 4 → 3 → 2 → 5\\
    Positions 2-4 reversed\\
    Return dummy.next
};

\end{tikzpicture}
```

## 🧠 Key Insights & Patterns

### Pattern Recognition
- **Partial reversal** = Find position + Apply basic reversal + Reconnect
- **Dummy node technique** handles edge case where left = 1
- **One-pass optimization** uses iterative node moving instead of full reversal

### Related Problems
- **[[LC206 - Reverse Linked List]]**: Basic reversal foundation
- **[[LC25 - Reverse Nodes in k-Group]]**: Multiple partial reversals
- **[[LC24 - Swap Nodes in Pairs]]**: Special case of partial reversal

### Complexity Analysis
- **Time Complexity:** O(n) - single pass through the list
- **Space Complexity:** O(1) for iterative, O(n) for recursive

This problem demonstrates how to **extend basic patterns** to handle **constrained scenarios**, showcasing the power of **building upon fundamental techniques**!
