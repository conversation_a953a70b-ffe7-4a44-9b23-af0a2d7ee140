---
tags: [problem/leetcode, lc/easy, topic/array, topic/queue, topic/simulation, pattern/queue_simulation]
aliases: [LC2073, LeetCode 2073. Time Needed to Buy Tickets]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 2073. Time Needed to Buy Tickets
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/入门：编程语言基础及练习/编程语言刷题实践.md]].

# LeetCode 2073: Time Needed to Buy Tickets

## Problem Statement

There are `n` people in a line queuing to buy tickets, where the `0`-th person is at the **front** of the line and the `(n - 1)`-th person is at the **back**.

You are given a **0-indexed** integer array `tickets` of length `n` where `tickets[i]` is the number of tickets that the `i`-th person wants to buy.

Each person takes **exactly 1 second** to buy a ticket. A person can only buy **1 ticket at a time** and has to go to the **back of the line** (which happens **instantaneously**) if they need to buy more tickets. If a person has no more tickets to buy, they will **leave the line**.

Return *the **time taken** for the person at position `k` (0-indexed) to finish buying all their tickets*.

**Official Link:** [LeetCode 2073. Time Needed to Buy Tickets](https://leetcode.com/problems/time-needed-to-buy-tickets/)

**Example 1:**
Input: `tickets = [2,3,2]`, `k = 2`
Output: `6`
Explanation:
- In the first pass, everyone buys one ticket and the line becomes `[1, 2, 1]`. Time = 3.
- In the second pass, everyone buys one ticket and the line becomes `[0, 1, 0]`. Time = 3 + 3 = 6.
The person at position 2 has successfully bought 2 tickets and it took 6 seconds.

## Solution (Python)

This problem can be solved by directly simulating the process using a queue. This is an application of the [[Interview/Concept/Data Structures/Queue/Applications/Queue for Simulation|Queue for Simulation pattern]]. We can use `collections.deque` as our queue.

```python
from collections import deque

class Solution:
    def timeRequiredToBuy(self, tickets: list[int], k: int) -> int:
        n = len(tickets)
        # The queue will store the indices of people in line.
        queue = deque(range(n))

        time_taken = 0

        while queue:
            # Person at the front of the queue
            person_index = queue.popleft()

            # This person buys one ticket
            tickets[person_index] -= 1
            time_taken += 1

            # Check if the person at index k has finished buying tickets
            if person_index == k and tickets[person_index] == 0:
                return time_taken

            # If the person still needs to buy more tickets, they go to the back of the line
            if tickets[person_index] > 0:
                queue.append(person_index)

        return time_taken # Should be reached if k is valid and tickets[k] > 0
```

**Explanation:**
1.  Get the number of people `n`.
2.  Initialize a `queue` (using `collections.deque`, a [[Interview/Concept/Programming Languages/Python/03 - Python Deque for Interviews|Python Deque]]) with the indices of people from `0` to `n-1`. This represents the initial line.
3.  Initialize `time_taken = 0`.
4.  Start a `while` loop that continues as long as the `queue` is not empty (though in this problem, it will terminate once person `k` finishes).
5.  In each iteration of the loop:
    a.  `person_index = queue.popleft()`: The person at the front of the line comes to buy a ticket.
    b.  `tickets[person_index] -= 1`: This person buys one ticket.
    c.  `time_taken += 1`: One second passes.
    d.  Check for termination: If the current `person_index` is `k` (our target person) AND their remaining `tickets[person_index]` count becomes `0`, then person `k` has finished. Return `time_taken`.
    e.  Re-queue if necessary: If `tickets[person_index]` is still greater than `0`, this person needs more tickets, so add their `person_index` back to the end of the `queue` (`queue.append(person_index)`). If `tickets[person_index]` is `0`, they leave the line and are not re-queued.

## Complexity Analysis
-   **Time Complexity:** $O(N \cdot M)$ in the worst-case, where $N$ is the number of people and $M$ is the maximum number of tickets any single person wants to buy (or more precisely, sum of all tickets up to a point). In the absolute worst case, if `tickets = [100, 100, ..., 100]` and `k` is the last person, each pass reduces one ticket for everyone. The number of passes is `max(tickets)`. Each pass involves `N` operations.
    More tightly, the total number of `popleft` and `append` operations is bounded by the total number of tickets sold until person `k` finishes. Let $S = \sum \text{tickets}[i]$. The complexity is closer to $O(S_{until\_k\_finishes})$. Since `tickets[i] <= 100` and `n <= 100`, the max sum is $100 \times 100 = 10000$. So, it's efficient enough.
-   **Space Complexity:** $O(N)$ for storing the indices in the `queue`.

## Visualizing Queue Operations

Let `tickets = [2,3,2]`, `k = 2`.
Queue stores indices: `Q = deque([0, 1, 2])`
`time = 0`

```tikz
\begin{tikzpicture}[
    queue_cell/.style={draw, rectangle, minimum width=0.8cm, minimum height=0.6cm, fill=yellow!20},
    label_style/.style={font=\sffamily\scriptsize, align=center},
    arrow/.style={->, >=stealth, thick},
    table_cell/.style={rectangle, draw, minimum width=1cm, minimum height=0.6cm, font=\tiny},
    header_cell/.style={table_cell, fill=gray!20, font=\tiny\bfseries}
]

% Table Headers
\node[header_cell] at (0,0) {Time};
\node[header_cell] at (1.5,0) {Action};
\node[header_cell] (q_header) at (4,0) {Queue (Front->Rear)};
\node[header_cell] (t_header) at (7.5,0) {Tickets Array};

% Initial State
\node[table_cell] at (0,-1) {0};
\node[table_cell, text width=2cm] at (1.5,-1) {Init};
\node[table_cell, text width=3cm] at (4,-1) {[0, 1, 2]};
\node[table_cell, text width=3cm] at (7.5,-1) {[2, 3, 2]};

% Iteration 1: Person 0
\draw[arrow, shorten >=2pt] (0,-1.3) -- (0,-1.7);
\node[table_cell] at (0,-2) {1};
\node[table_cell, text width=2cm] at (1.5,-2) {P0 buys (1s)\\t[0]=1\\Re-queue P0};
\node[table_cell, text width=3cm] at (4,-2) {[1, 2, 0]};
\node[table_cell, text width=3cm] at (7.5,-2) {[1, 3, 2]};

% Iteration 2: Person 1
\draw[arrow, shorten >=2pt] (0,-2.3) -- (0,-2.7);
\node[table_cell] at (0,-3) {2};
\node[table_cell, text width=2cm] at (1.5,-3) {P1 buys (1s)\\t[1]=2\\Re-queue P1};
\node[table_cell, text width=3cm] at (4,-3) {[2, 0, 1]};
\node[table_cell, text width=3cm] at (7.5,-3) {[1, 2, 2]};

% Iteration 3: Person 2 (k)
\draw[arrow, shorten >=2pt] (0,-3.3) -- (0,-3.7);
\node[table_cell] at (0,-4) {3};
\node[table_cell, text width=2cm] at (1.5,-4) {P2 buys (1s)\\t[2]=1\\Re-queue P2};
\node[table_cell, text width=3cm] at (4,-4) {[0, 1, 2]}; % Queue after P2 re-queued
\node[table_cell, text width=3cm] at (7.5,-4) {[1, 2, 1]}; % Tickets after P2 bought one

\node at (4, -4.5) {... pass 1 ends, time = 3. tickets = [1,2,1] ...};

% Iteration 4: Person 0
\draw[arrow, shorten >=2pt] (0,-4.8) -- (0,-5.2);
\node[table_cell] at (0,-5.5) {4};
\node[table_cell, text width=2cm] at (1.5,-5.5) {P0 buys (1s)\\t[0]=0\\P0 leaves};
\node[table_cell, text width=3cm] at (4,-5.5) {[1, 2]};
\node[table_cell, text width=3cm] at (7.5,-5.5) {[0, 2, 1]};

% Iteration 5: Person 1
\draw[arrow, shorten >=2pt] (0,-5.8) -- (0,-6.2);
\node[table_cell] at (0,-6.5) {5};
\node[table_cell, text width=2cm] at (1.5,-6.5) {P1 buys (1s)\\t[1]=1\\Re-queue P1};
\node[table_cell, text width=3cm] at (4,-6.5) {[2, 1]};
\node[table_cell, text width=3cm] at (7.5,-6.5) {[0, 1, 1]};

% Iteration 6: Person 2 (k)
\draw[arrow, shorten >=2pt] (0,-6.8) -- (0,-7.2);
\node[table_cell, fill=green!20] at (0,-7.5) {6};
\node[table_cell, text width=2cm, fill=green!20] at (1.5,-7.5) {P2 buys (1s)\\t[2]=0\\P2 (k) finishes!};
\node[table_cell, text width=3cm, fill=green!20] at (4,-7.5) {[1] (P2 not re-queued)};
\node[table_cell, text width=3cm, fill=green!20] at (7.5,-7.5) {[0, 1, 0]};

\node at (4,-8) {Return time = 6};

\end{tikzpicture}
```

## 总结 (Summary)
- This problem models a queuing system where people buy tickets one by one and re-queue if they need more.
- A direct simulation using a queue (specifically `collections.deque` for efficient `popleft` and `append`) is a straightforward and effective approach.
- The state in the queue should be the indices of the people, allowing access to their remaining ticket counts in the `tickets` array.
- The simulation proceeds, incrementing time, until the target person `k` has bought all their tickets.

---
Previous: [[Interview/Practice/LeetCode/LC20 - Valid Parentheses|LC20 - Valid Parentheses]]
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Data Structures/Queue/Applications/Queue for Simulation|Queue for Simulation]], [[Interview/Concept/Programming Languages/Python/03 - Python Deque for Interviews|Python Deque]]
