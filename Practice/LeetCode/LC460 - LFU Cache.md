---
tags: [problem/leetcode, lc/hard, topic/data_structure_design, pattern/lfu, course/labuladong]
aliases: [LC460, LFU Cache Problem]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 460. LFU Cache
> Discussed in [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/算法就像搭乐高：手撸 LFU 算法.md|算法就像搭乐高：手撸 LFU 算法 by Labuladong]].

# LeetCode 460: LFU Cache

## Problem Statement
Design and implement a data structure for the **Least Frequently Used (LFU)** cache.

Implement the `LFUCache` class:
- `LFUCache(int capacity)` Initializes the object with the `capacity` of the data structure.
- `int get(int key)` Gets the value of the `key` if the key exists in the cache. Otherwise, returns -1.
- `void put(int key, int value)` Sets or inserts the value if the `key` is not already present. When the cache reaches its `capacity`, it should invalidate and remove the **least frequently used** key before inserting a new item. If there is a tie (i.e., two or more keys with the same frequency), the **least recently used** key should be invalidated.

The functions `get` and `put` must each run in $O(1)$ average time complexity.

**Official Link:** [LeetCode 460. LFU Cache](https://leetcode.com/problems/lfu-cache/)

## Solution Approach
This problem requires implementing the [[Interview/Concept/Algorithms/Caching/LFU Cache|LFU Cache Algorithm]]. This involves:
1.  A hash map `key_to_val` for `key -> value`.
2.  A hash map `key_to_freq` for `key -> frequency`.
3.  A hash map `freq_to_keys` mapping `frequency -> OrderedDict<key, None>`. The `OrderedDict` stores keys of a particular frequency, maintaining their insertion (recency) order.
4.  A variable `min_freq` to track the current minimum frequency in the cache.

### Python Solution
(Adapted from the concept note [[Interview/Concept/Algorithms/Caching/LFU Cache|LFU Cache Algorithm]])
```python
import collections

class LFUCache:
    def __init__(self, capacity: int):
        self.capacity = capacity
        self.key_to_val = {}
        self.key_to_freq = {}
        self.freq_to_keys = collections.defaultdict(collections.OrderedDict)
        self.min_freq = 0 

    def _increase_freq(self, key: int):
        freq = self.key_to_freq[key]

        # Remove key from current frequency list
        del self.freq_to_keys[freq][key] 
        if not self.freq_to_keys[freq]: # If list becomes empty
            del self.freq_to_keys[freq]
            # If this was the min_freq and its list is now empty,
            # min_freq must be updated.
            if freq == self.min_freq:
                self.min_freq += 1

        # Add key to new frequency list (freq + 1)
        new_freq = freq + 1
        self.freq_to_keys[new_freq][key] = None # Add key to maintain order
        self.key_to_freq[key] = new_freq


    def get(self, key: int) -> int:
        if key not in self.key_to_val:
            return -1

        self._increase_freq(key)
        return self.key_to_val[key]

    def put(self, key: int, value: int) -> None:
        if self.capacity <= 0:
            return

        if key in self.key_to_val:
            self.key_to_val[key] = value # Update value
            self._increase_freq(key)    # Increase frequency
            return

        # New key
        if len(self.key_to_val) >= self.capacity:
            # Cache is full, remove LFU (and LRU among them) key
            keys_at_min_freq = self.freq_to_keys[self.min_freq]
            # OrderedDict.popitem(last=False) removes and returns the (key, value) pair that was first inserted.
            oldest_key_at_min_freq, _ = keys_at_min_freq.popitem(last=False) 

            if not keys_at_min_freq: # If this frequency list is now empty
                del self.freq_to_keys[self.min_freq]
                # min_freq will be reset to 1 when the new item is added,
                # or if another item becomes min_freq. This update is critical.
                # If all items were at min_freq, and we remove one, and the list is empty,
                # then min_freq is no longer valid. It will be set to 1 by the new item.

            del self.key_to_val[oldest_key_at_min_freq]
            del self.key_to_freq[oldest_key_at_min_freq]

        # Add the new key
        self.key_to_val[key] = value
        self.key_to_freq[key] = 1
        self.freq_to_keys[1][key] = None # Add to OrderedDict for freq 1
        self.min_freq = 1 # A new key always starts with freq 1, becoming the new min_freq
```

## Complexity Analysis
- **`get(key)`:** $O(1)$ average.
- **`put(key, value)`:** $O(1)$ average.
- **Space Complexity:** $O(capacity)$.

## 总结 (Summary)
LC460 requires designing an LFU cache. The solution involves multiple hash maps to track key-value pairs, key frequencies, and groups of keys for each frequency (maintaining LRU order within frequency groups using `OrderedDict`). Operations achieve $O(1)$ average time complexity.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
