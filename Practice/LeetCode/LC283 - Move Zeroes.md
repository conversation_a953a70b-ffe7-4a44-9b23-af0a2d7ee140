---
tags: [problem/leetcode, lc/easy, topic/array, topic/two_pointers, pattern/fast_slow_pointers]
aliases: [LC283, LeetCode 283. Move Zeroes, 移动零]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 283. Move Zeroes
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道数组题目.md]].

# LeetCode 283: Move Zeroes

## Problem Statement

Given an integer array `nums`, move all `0`'s to the end of it while maintaining the relative order of the non-zero elements.

**Note** that you must do this in-place without making a copy of the array.

**Official Link:** [LeetCode 283. Move Zeroes](https://leetcode.com/problems/move-zeroes/)

## Solution Approach: Fast-Slow Pointers (Labuladong's Method)

This problem can be solved by adapting the logic from [[Interview/Practice/LeetCode/LC27 - Remove Element|LC27 Remove Element]]. The idea is:
1.  First, "remove" all the zeros by moving all non-zero elements to the front of the array, preserving their relative order. This is equivalent to `removeElement(nums, 0)`. Let `p` be the number of non-zero elements (which is also the index after the last non-zero element).
2.  Then, fill the rest of the array from index `p` to the end with zeros.

### Python Solution (Labuladong's version)
```python
class Solution:
    def moveZeroes(self, nums: list[int]) -> None:
        '''
        Do not return anything, modify nums in-place instead.
        '''
        # p stores the count of non-zero elements
        # and also the index where the next non-zero element should be placed.
        p = self.removeElement(nums, 0)

        # Fill remaining part of the array with zeros
        for i in range(p, len(nums)):
            nums[i] = 0

    # Helper function (from LC27 logic)
    def removeElement(self, nums: list[int], val: int) -> int:
        slow = 0
        fast = 0
        while fast < len(nums):
            if nums[fast] != val:
                nums[slow] = nums[fast]
                slow += 1
            fast += 1
        return slow

# Example: nums = [0,1,0,3,12]
# 1. removeElement(nums, 0):
#    - slow=0, fast=0. nums[0](0)==0. fast=1.
#    - slow=0, fast=1. nums[1](1)!=0. nums[0]=1. slow=1. fast=2. (nums=[1,1,0,3,12])
#    - slow=1, fast=2. nums[2](0)==0. fast=3.
#    - slow=1, fast=3. nums[3](3)!=0. nums[1]=3. slow=2. fast=4. (nums=[1,3,0,3,12])
#    - slow=2, fast=4. nums[4](12)!=0. nums[2]=12. slow=3. fast=5. (nums=[1,3,12,3,12])
#    Returns p = 3. nums is now [1,3,12,3,12] (first p elements are non-zero)
# 2. Fill nums[p:] with 0:
#    - nums[3]=0, nums[4]=0
# Final nums: [1,3,12,0,0]
```
Labuladong's visualizer `div_move-zeroes` illustrates this two-step process.

## Alternative In-Place Swap Approach
Another common way to solve this with two pointers:
- `slow` pointer: Tracks the position for the next non-zero element.
- `fast` pointer: Iterates through the array.
- If `nums[fast]` is non-zero:
    - Swap `nums[slow]` and `nums[fast]`.
    - Increment `slow`.
- Increment `fast` in every iteration.

```python
class Solution_Alternative:
    def moveZeroes(self, nums: list[int]) -> None:
        slow = 0
        for fast in range(len(nums)):
            if nums[fast] != 0:
                # Swap nums[slow] with nums[fast]
                # Only swap if slow and fast are different to avoid swapping an element with itself
                # or if the element at slow is actually a zero that needs to be moved.
                if nums[slow] == 0 or slow != fast: # More robust: if nums[fast] is non-zero and nums[slow] is zero, swap.
                                                 # Simpler: if nums[fast] != 0, swap and advance slow.
                                                 # If nums[slow] is already non-zero, slow will advance with fast.
                    nums[slow], nums[fast] = nums[fast], nums[slow]
                slow += 1 
        # After this loop, all non-zeros are at the front, preserving order,
        # and all zeros are at the end.
```
This alternative also works in $O(N)$ time and $O(1)$ space and maintains relative order of non-zero elements. The key difference is it uses swaps rather than overwrites followed by filling. Labuladong's primary described method is the removeElement + fill.

## Complexity Analysis
- **Time Complexity:** $O(N)$, where $N$ is the length of `nums`. The `removeElement` part is $O(N)$, and the subsequent filling with zeros is also $O(N)$ in the worst case.
- **Space Complexity:** $O(1)$, as the modification is done in-place.

## 总结 (Summary)
- LC283 requires moving all zeros to the end of an array in-place while maintaining the relative order of non-zero elements.
- Labuladong's solution effectively uses the `removeElement` pattern to first compact all non-zero elements to the front, then fills the remainder of the array with zeros.
- An alternative approach uses two pointers with swaps to achieve the same result. Both are $O(N)$ time and $O(1)$ space.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]] (Fast-Slow Pointers)
