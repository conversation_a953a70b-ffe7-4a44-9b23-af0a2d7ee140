---
tags: [problem/leetcode, lc/easy, topic/binary_tree, topic/dfs, pattern/tree_traversal, pattern/post_order, pattern/tree_diameter]
aliases: [LC543, LeetC<PERSON> 543, Diameter of Binary Tree, 二叉树的直径]
---

# LeetCode 543: Diameter of Binary Tree

## Problem Statement

Given the `root` of a binary tree, return the **length of the diameter** of the tree.

The **diameter** of a binary tree is the **length of the longest path** between any two nodes in a tree. This path may or may not pass through the `root`.

The **length of a path** between two nodes is represented by the number of edges between them.

**Official Link:** [LeetCode 543. Diameter of Binary Tree](https://leetcode.com/problems/diameter-of-binary-tree/)

## 🌳 Understanding the Tree Diameter

Think of the diameter as the "widest span" across the tree - the longest possible journey between any two nodes:

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=0.8cm, font=\sffamily\small, fill=blue!30},
    highlight/.style={treenode, fill=red!40},
    path_edge/.style={very thick, red, line width=2pt},
    normal_edge/.style={thick},
    example_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=4cm, align=center}
]

% Example tree
\node at (4, 6) {\bfseries Example: Finding the Diameter};

\node[treenode] (root) at (4, 4.5) {1};
\node[highlight] (left) at (2, 3) {2};
\node[treenode] (right) at (6, 3) {3};
\node[highlight] (ll) at (1, 1.5) {4};
\node[highlight] (lr) at (3, 1.5) {5};

% Normal edges
\draw[normal_edge] (root) -- (right);
\draw[normal_edge] (left) -- (ll);

% Highlighted path (diameter)
\draw[path_edge] (root) -- (left);
\draw[path_edge] (left) -- (lr);

\node[example_box] at (8, 3) {
    \textbf{Diameter Path:}\\
    4 → 2 → 1 → 3\\
    Length: 3 edges\\[0.5em]
    \textbf{Key Insight:}\\
    Diameter passes through\\
    some node as "root"
};

% Labels for path
\node at (0.5, 1.5) {\tiny 4};
\node at (1.5, 3) {\tiny 2};
\node at (4, 4.5) {\tiny 1};
\node at (6, 3) {\tiny 3};

\end{tikzpicture}
```

**Key Observations:**
- **Diameter = Longest path** between any two nodes
- **Path length = Number of edges** (not nodes)
- **May or may not pass through root** of the entire tree

## 🧠 The Critical Insight: Local Diameter Analysis

The breakthrough insight transforms this from a complex global search to a simple local analysis:

```tikz
\begin{tikzpicture}[
    insight_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4.5cm, align=center, minimum height=2.5cm},
    arrow/.style={->, thick, red, line width=2pt},
    conclusion_box/.style={rectangle, draw, fill=orange!20, font=\sffamily\small, text width=6cm, align=center}
]

\node[insight_box] (global) at (0, 3) {
    \textbf{Global Thinking}\\[0.5em]
    "Check all possible paths\\
    between all pairs of nodes\\
    and find the longest"\\[0.5em]
    Exponential complexity!
};

\node[insight_box] (local) at (8, 3) {
    \textbf{Local Insight}\\[0.5em]
    "For each node, the longest\\
    path through it is:\\
    left\_height + right\_height"\\[0.5em]
    Linear complexity!
};

\draw[arrow] (global) -- (local);

\node[conclusion_box] at (4, 0.5) {
    \textbf{Algorithm Strategy:}\\
    1. For each node, calculate left and right subtree heights\\
    2. Diameter through this node = left\_height + right\_height\\
    3. Global diameter = max of all local diameters\\
    \\
    Use post-order traversal to compute efficiently!
};

\end{tikzpicture}
```

**Why This Works:**
- Every diameter path must pass through some node as its "highest point"
- At that node, the diameter equals the sum of left and right subtree heights
- We can check all nodes efficiently using post-order traversal

## 🔍 Problem Decomposition: [[Post-Order Traversal]]

The solution elegantly combines height calculation with diameter tracking:

```tikz
\begin{tikzpicture}[
    concept_box/.style={rectangle, draw, rounded corners, fill=green!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2cm},
    formula_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=3.5cm, align=center}
]

\node[concept_box] (height) at (0, 3) {
    \textbf{Height Calculation}\\[0.5em]
    height(node) =\\
    1 + max(height(left),\\
    height(right))\\[0.5em]
    Bottom-up computation
};

\node[concept_box] (diameter) at (5, 3) {
    \textbf{Diameter Tracking}\\[0.5em]
    diameter\_through(node) =\\
    height(left) + height(right)\\[0.5em]
    Update global maximum
};

\node[formula_box] at (0, 0.5) {
    \textbf{Base Case:}\\
    height(null) = 0\\
    diameter\_through(null) = 0
};

\node[formula_box] at (5, 0.5) {
    \textbf{Combination:}\\
    One DFS computes both\\
    height and diameter\\
    simultaneously
};

\end{tikzpicture}
```

## 💡 Step-by-Step Algorithm

```python
class Solution:
    def diameterOfBinaryTree(self, root: Optional[TreeNode]) -> int:
        self.max_diameter = 0

        def height(node):
            """Calculate height and update diameter simultaneously"""
            if not node:
                return 0

            # Post-order: process children first
            left_height = height(node.left)
            right_height = height(node.right)

            # Diameter through current node
            current_diameter = left_height + right_height
            self.max_diameter = max(self.max_diameter, current_diameter)

            # Return height for parent calculation
            return 1 + max(left_height, right_height)

        height(root)
        return self.max_diameter
```

## 🔍 Visual Algorithm Trace

Let's trace through the algorithm step-by-step with a concrete example:

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=1cm, font=\sffamily\small, fill=blue!30},
    processing/.style={treenode, fill=yellow!50},
    completed/.style={treenode, fill=green!40},
    step_box/.style={rectangle, draw, fill=yellow!20, font=\tiny, text width=3.5cm, align=center},
    height_label/.style={font=\tiny, red},
    diameter_label/.style={font=\tiny, blue}
]

% Step 1: Initial tree
\node at (4, 7) {\bfseries Step 1: Start Post-Order Traversal};

\node[treenode] (n1) at (4, 5.5) {1};
\node[treenode] (n2) at (2, 4) {2};
\node[treenode] (n3) at (6, 4) {3};
\node[treenode] (n4) at (1, 2.5) {4};
\node[treenode] (n5) at (3, 2.5) {5};

\draw[thick] (n1) -- (n2);
\draw[thick] (n1) -- (n3);
\draw[thick] (n2) -- (n4);
\draw[thick] (n2) -- (n5);

\node[step_box] at (8.5, 5) {
    Post-order: visit\\
    children before parent\\
    \\
    Order: 4, 5, 2, 3, 1\\
    max\_diameter = 0
};

% Step 2: Process leaf nodes
\node at (4, 3.5) {\bfseries Step 2: Process Leaf Nodes (4, 5, 3)};

\node[completed] (l4) at (1, 1.5) {4};
\node[completed] (l5) at (3, 1.5) {5};
\node[treenode] (l2) at (2, 0.5) {2};
\node[completed] (l3) at (6, 1.5) {3};
\node[treenode] (l1) at (4, 0.5) {1};

\draw[thick] (l2) -- (l4);
\draw[thick] (l2) -- (l5);
\draw[thick] (l1) -- (l2);
\draw[thick] (l1) -- (l3);

% Height labels
\node[height_label] at (0.5, 1.5) {h=1};
\node[height_label] at (3.5, 1.5) {h=1};
\node[height_label] at (6.5, 1.5) {h=1};

\node[step_box] at (8.5, 1) {
    Leaf nodes:\\
    height = 1\\
    diameter = 0 + 0 = 0\\
    \\
    max\_diameter = 0
};

% Step 3: Process node 2
\node at (4, -1) {\bfseries Step 3: Process Node 2};

\node[completed] (p4) at (1, -2.5) {4};
\node[completed] (p5) at (3, -2.5) {5};
\node[processing] (p2) at (2, -3.5) {2};
\node[completed] (p3) at (6, -2.5) {3};
\node[treenode] (p1) at (4, -3.5) {1};

\draw[thick] (p2) -- (p4);
\draw[thick] (p2) -- (p5);
\draw[thick] (p1) -- (p2);
\draw[thick] (p1) -- (p3);

% Height and diameter calculations
\node[height_label] at (0.5, -2.5) {h=1};
\node[height_label] at (3.5, -2.5) {h=1};
\node[height_label] at (1.5, -3.5) {h=2};
\node[diameter_label] at (2, -4) {d=1+1=2};

\node[step_box] at (8.5, -3) {
    Node 2:\\
    left\_height = 1 (from 4)\\
    right\_height = 1 (from 5)\\
    diameter = 1 + 1 = 2\\
    height = 1 + max(1,1) = 2\\
    \\
    max\_diameter = 2
};

% Step 4: Process node 1 (root)
\node at (4, -5.5) {\bfseries Step 4: Process Root Node 1};

\node[completed] (f4) at (1, -7) {4};
\node[completed] (f5) at (3, -7) {5};
\node[completed] (f2) at (2, -8) {2};
\node[completed] (f3) at (6, -7) {3};
\node[processing] (f1) at (4, -8) {1};

\draw[thick] (f2) -- (f4);
\draw[thick] (f2) -- (f5);
\draw[thick] (f1) -- (f2);
\draw[thick] (f1) -- (f3);

% Final calculations
\node[height_label] at (1.5, -8) {h=2};
\node[height_label] at (6.5, -7) {h=1};
\node[height_label] at (4, -8.5) {h=3};
\node[diameter_label] at (4, -9) {d=2+1=3};

\node[step_box] at (8.5, -7.5) {
    Root 1:\\
    left\_height = 2 (from 2)\\
    right\_height = 1 (from 3)\\
    diameter = 2 + 1 = 3\\
    height = 1 + max(2,1) = 3\\
    \\
    max\_diameter = 3\\
    \\
    \textbf{Answer: 3}
};

\end{tikzpicture}
```

**Key Observations:**
1. **Post-order traversal**: Process children before parent
2. **Height calculation**: Bottom-up, each node's height = 1 + max(children)
3. **Diameter tracking**: At each node, diameter = left_height + right_height
4. **Global maximum**: Track the maximum diameter seen across all nodes

## 🚀 Complete Solution with Variations

### Approach 1: Class Variable (Most Common)

```python
class Solution:
    def diameterOfBinaryTree(self, root: Optional[TreeNode]) -> int:
        self.max_diameter = 0

        def height(node):
            if not node:
                return 0

            left_height = height(node.left)
            right_height = height(node.right)

            # Update global diameter
            self.max_diameter = max(self.max_diameter, left_height + right_height)

            return 1 + max(left_height, right_height)

        height(root)
        return self.max_diameter
```

### Approach 2: Return Tuple (Functional Style)

```python
class Solution:
    def diameterOfBinaryTree(self, root: Optional[TreeNode]) -> int:
        def height_and_diameter(node):
            """Returns (height, max_diameter_in_subtree)"""
            if not node:
                return 0, 0

            left_height, left_diameter = height_and_diameter(node.left)
            right_height, right_diameter = height_and_diameter(node.right)

            current_height = 1 + max(left_height, right_height)
            current_diameter = left_height + right_height
            max_diameter = max(left_diameter, right_diameter, current_diameter)

            return current_height, max_diameter

        _, diameter = height_and_diameter(root)
        return diameter
```

### Approach 3: Nonlocal Variable

```python
class Solution:
    def diameterOfBinaryTree(self, root: Optional[TreeNode]) -> int:
        max_diameter = 0

        def height(node):
            nonlocal max_diameter
            if not node:
                return 0

            left_height = height(node.left)
            right_height = height(node.right)

            max_diameter = max(max_diameter, left_height + right_height)
            return 1 + max(left_height, right_height)

        height(root)
        return max_diameter
```

## 🧠 Key Takeaways & Problem-Solving Framework

### Why This Problem is Educational

```tikz
\begin{tikzpicture}[
    concept_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=1.8cm},
    arrow/.style={->, thick, green}
]

\node[concept_box] (decomp) at (0, 3) {
    \textbf{Problem Decomposition}\\[0.3em]
    Break global problem\\
    into local subproblems\\
    at each node
};

\node[concept_box] (traversal) at (4.5, 3) {
    \textbf{Traversal Pattern}\\[0.3em]
    Post-order traversal\\
    enables bottom-up\\
    computation
};

\node[concept_box] (optimization) at (9, 3) {
    \textbf{Optimization}\\[0.3em]
    Single pass computes\\
    both height and\\
    diameter efficiently
};

\draw[arrow] (decomp) -- (traversal);
\draw[arrow] (traversal) -- (optimization);

\node at (4.5, 1.5) {\bfseries Core Learning: Decompose → Traverse → Optimize};

\end{tikzpicture}
```

### Problem-Solving Pattern: [[Tree Diameter]]

This problem exemplifies the **tree diameter** pattern:

1. **Local analysis**: At each node, consider paths passing through it
2. **Height dependency**: Diameter depends on subtree heights
3. **Post-order traversal**: Compute children before parent
4. **Global tracking**: Maintain maximum across all local computations

### Complexity Analysis
- **Time Complexity:** O(n) - visit each node exactly once
- **Space Complexity:** O(h) - recursion stack depth equals tree height

### Related Concepts for Obsidian

This problem connects to several important algorithmic concepts:

- **[[Post-Order Traversal]]**: Bottom-up tree processing
- **[[Tree Height]]**: Fundamental tree property calculation
- **[[Tree Diameter]]**: Longest path in tree structures
- **[[Dynamic Programming on Trees]]**: Optimal substructure in trees
- **[[Divide and Conquer]]**: Break problem into subproblems

### Related Problems
- **LC124. Binary Tree Maximum Path Sum**: Similar pattern, different objective
- **LC687. Longest Univalue Path**: Diameter with value constraints
- **LC1245. Tree Diameter**: Diameter in general trees
- **LC104. Maximum Depth of Binary Tree**: Simpler height calculation

This problem beautifully demonstrates how **post-order traversal** enables efficient computation of global tree properties through local analysis!
