---
tags: [problem/leetcode, lc/easy, topic/array, topic/hash_table, topic/set, pattern/detect_duplicates]
aliases: [LC217, LeetCode 217. Contains Duplicate]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 217. Contains Duplicate
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/入门：编程语言基础及练习/编程语言刷题实践.md]].

# LeetCode 217: Contains Duplicate

## Problem Statement

Given an integer array `nums`, return `true` if any value appears **at least twice** in the array, and `false` if every element is distinct.

**Official Link:** [LeetCode 217. Contains Duplicate](https://leetcode.com/problems/contains-duplicate/)

**Example 1:**
Input: `nums = [1,2,3,1]`
Output: `true`

**Example 2:**
Input: `nums = [1,2,3,4]`
Output: `false`

## Solution (Python)

This problem can be efficiently solved using a hash set to keep track of the numbers encountered so far. This leverages the [[Interview/Concept/Algorithms/Hashing/Detecting Duplicates with Sets|Detecting Duplicates with Sets]] pattern.

```python
class Solution:
    def containsDuplicate(self, nums: list[int]) -> bool:
        seen = set() # Use a set to store numbers encountered
        for num in nums:
            if num in seen:
                # If the number is already in the set, it's a duplicate
                return True
            seen.add(num) # Add the current number to the set
        # If the loop finishes without finding duplicates, all elements are distinct
        return False
```

**Explanation:**
1.  Initialize an empty set called `seen`. A Python `set` is an implementation of a [[Interview/Concept/Programming Languages/Python/06 - Python Set (Hash Set) for Interviews|Python Set (Hash Set)]].
2.  Iterate through each `num` in the input list `nums`.
3.  For each `num`:
    -   Check if `num` is already present in the `seen` set. The `in` operator for sets provides an average time complexity of $O(1)$ for this check.
    -   If `num` is in `seen`, it means we have encountered this number before, so a duplicate exists. Return `True` immediately.
    -   If `num` is not in `seen`, add `num` to the `seen` set. This also takes $O(1)$ on average.
4.  If the loop completes without returning `True`, it means no duplicates were found in the entire array. In this case, return `False`.

**Alternative using set properties:**
A more concise way in Python is to compare the length of the list with the length of a set constructed from the list. If they are different, it means duplicates were removed when creating the set.

```python
class Solution:
    def containsDuplicate_alternative(self, nums: list[int]) -> bool:
        return len(nums) != len(set(nums))
```
This alternative is very Pythonic but might be slightly less efficient as it involves creating a new set with all elements first. The iterative approach can return early.

## Complexity Analysis

**For the iterative approach using a hash set:**
-   **Time Complexity:** $O(N)$, where $N$ is the number of elements in `nums`. In the worst case, we iterate through all elements once. Each set operation (`in` check and `add`) takes $O(1)$ on average.
-   **Space Complexity:** $O(N)$ in the worst case, where all elements are unique and are added to the `seen` set.

**For the `len(nums) != len(set(nums))` approach:**
-   **Time Complexity:** $O(N)$ on average, as building the set from `nums` takes $O(N)$ time.
-   **Space Complexity:** $O(N)$ to store the elements in the new set.

## Visualizing the Iterative Approach

Let `nums = [1, 2, 3, 1]`

```tikz
\begin{tikzpicture}[
    node/.style={draw, rectangle, minimum height=0.8cm, minimum width=1cm},
    set_entry/.style={draw, ellipse, fill=blue!10, minimum height=0.6cm, text width=1.5cm, align=center},
    arrow/.style={->, >=stealth, thick},
    highlight/.style={fill=green!20}
]
    % Array
    \node[node] (n0) at (0,0) {1}; \node at (0,-0.7) {idx 0};
    \node[node] (n1) at (1.5,0) {2}; \node at (1.5,-0.7) {idx 1};
    \node[node] (n2) at (3,0) {3}; \node at (3,-0.7) {idx 2};
    \node[node] (n3) at (4.5,0) {1}; \node at (4.5,-0.7) {idx 3};

    % Set state
    \node at (2.25, -1.5) {seen (Set)};

    % Step 1: num=1
    \node at (-1.5, -3) {Step 1: num=1};
    \node at (-1.5, -3.5) {1 not in seen};
    \node[set_entry] (s1) at (2.25, -3.5) {\{1\}};
    \draw[arrow, red!70] (n0) -- (-1.5, -2.5);

    % Step 2: num=2
    \node at (-1.5, -5) {Step 2: num=2};
    \node at (-1.5, -5.5) {2 not in seen};
    \node[set_entry] (s2) at (2.25, -5.5) {\{1, 2\}};
    \draw[arrow, red!70] (n1) -- (-1.5, -4.5);

    % Step 3: num=3
    \node at (-1.5, -7) {Step 3: num=3};
    \node at (-1.5, -7.5) {3 not in seen};
    \node[set_entry] (s3) at (2.25, -7.5) {\{1, 2, 3\}};
    \draw[arrow, red!70] (n2) -- (-1.5, -6.5);

    % Step 4: num=1
    \node at (-1.5, -9) {Step 4: num=1};
    \node[highlight] at (-1.5, -9.5) {1 in seen! Return True};
    \node[set_entry, fill=yellow!30] (s4) at (2.25, -9.5) {\{1, 2, 3\} (current state)};
    \draw[arrow, red!70] (n3) -- (-1.5, -8.5);
    \draw[arrow, dashed, blue] (s4.west) .. controls (0.5,-9.5) .. (-0.5, -9.5); % Link check to set

\end{tikzpicture}
```

## 总结 (Summary)
- The "Contains Duplicate" problem is efficiently solved by leveraging the properties of a hash set.
- By iterating through the array and adding elements to a set, we can quickly detect if an element has been seen before in $O(1)$ average time.
- This leads to an overall $O(N)$ time complexity solution with $O(N)$ space complexity.

---
Previous: [[Interview/Practice/LeetCode/LC1 - Two Sum|LC1 - Two Sum]]
Next: [[Interview/Practice/LeetCode/LC136 - Single Number|LC136 - Single Number]]
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Hashing/Detecting Duplicates with Sets|Detecting Duplicates with Sets]], [[Interview/Concept/Programming Languages/Python/06 - Python Set (Hash Set) for Interviews|Python Set]]
