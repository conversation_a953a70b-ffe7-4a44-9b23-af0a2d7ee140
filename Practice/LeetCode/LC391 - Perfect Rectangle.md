---
tags: [problem/leetcode, lc/hard, topic/array, topic/geometry, pattern/area_counting, pattern/vertex_counting, pattern/hashing, course/labuladong, lc/lc391]
aliases: [LC391, Perfect Rectangle]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/如何判定完美矩形.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 391. Perfect Rectangle
> Solution based on [[Interview/Concept/Algorithms/Geometric Problems/00 - Perfect Rectangle Detection|Perfect Rectangle Detection Pattern]] derived from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/如何判定完美矩形.md|Labuladong's article]].

# LeetCode 391: Perfect Rectangle

## Problem Statement
Given an array `rectangles` where `rectangles[i] = [xi, yi, ai, bi]` represents an axis-aligned rectangle. The bottom-left point of the rectangle is `(xi, yi)` and the top-right point is `(ai, bi)`.
Return `true` if all the rectangles together form an exact cover of a rectangular region. Otherwise, return `false`.

**Official Link:** [LeetCode 391. Perfect Rectangle](https://leetcode.com/problems/perfect-rectangle/)

## Solution Approach
The solution involves checking two main conditions:
1.  **Area Conservation:** The sum of the areas of the small rectangles must equal the area of the bounding box they form.
2.  **Vertex Property:** Exactly four points (the corners of the bounding box) must have an odd count of appearances among all corners of the small rectangles. All other (internal) points must have an even count.

This is detailed in the [[Interview/Concept/Algorithms/Geometric Problems/00 - Perfect Rectangle Detection|Perfect Rectangle Detection Pattern]].

### Python Solution
```python
class Solution:
    def isRectangleCover(self, rectangles: list[list[int]]) -> bool:
        min_x1, min_y1 = float('inf'), float('inf')
        max_x2, max_y2 = float('-inf'), float('-inf')

        actual_area_sum = 0
        points_set = set() # Stores points that have appeared an odd number of times

        for x1, y1, x2, y2 in rectangles:
            # Update bounding box coordinates
            min_x1 = min(min_x1, x1)
            min_y1 = min(min_y1, y1)
            max_x2 = max(max_x2, x2)
            max_y2 = max(max_y2, y2)

            # Accumulate area of small rectangles
            actual_area_sum += (x2 - x1) * (y2 - y1)

            # Process vertices (store as tuples for hashability)
            p1 = (x1, y1)
            p2 = (x1, y2)
            p3 = (x2, y1)
            p4 = (x2, y2)

            for point in [p1, p2, p3, p4]:
                if point in points_set:
                    points_set.remove(point) # Seen even times
                else:
                    points_set.add(point)    # Seen odd times

        # Check Area Conservation
        expected_bounding_box_area = (max_x2 - min_x1) * (max_y2 - min_y1)
        if actual_area_sum != expected_bounding_box_area:
            return False

        # Check Vertex Property
        if len(points_set) != 4:
            return False

        # Check if the 4 points in the set are indeed the corners of the bounding box
        if (min_x1, min_y1) not in points_set: return False
        if (min_x1, max_y2) not in points_set: return False
        if (max_x2, min_y1) not in points_set: return False
        if (max_x2, max_y2) not in points_set: return False

        return True
```

## Complexity Analysis
- **Time Complexity:** $O(N)$, where $N$ is the number of rectangles. Each rectangle is processed once. Set operations take average $O(1)$.
- **Space Complexity:** $O(N)$ in the worst case for `points_set`.

## 总结 (Summary)
LC391 is solved by verifying area conservation and a specific vertex property. The sum of areas of input rectangles must match the area of their computed bounding box. Additionally, after "XORing" all vertices of the input rectangles (add if not present, remove if present), exactly four vertices must remain, and these must be the corners of the bounding box.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
