---
tags: [problem/leetcode, lc/easy, topic/array, topic/two_pointers, pattern/fast_slow_pointers]
aliases: [LC26, LeetCode 26. Remove Duplicates from Sorted Array, 删除有序数组中的重复项]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 26. Remove Duplicates from Sorted Array
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道数组题目.md]].

# LeetCode 26: Remove Duplicates from Sorted Array

## Problem Statement

Given an integer array `nums` sorted in **non-decreasing order**, remove the duplicates **in-place** such that each unique element appears only once. The relative order of the elements should be kept the same. Then return *the number of unique elements in `nums`*.

Consider the number of unique elements of `nums` to be `k`, to get accepted, you need to do the following things:
- Change the array `nums` such that the first `k` elements of `nums` contain the unique elements in the order they were present in `nums` initially.
- The remaining elements of `nums` beyond the first `k` elements, as well as the size of `nums`, do not matter.
- Return `k`.

**Official Link:** [LeetCode 26. Remove Duplicates from Sorted Array](https://leetcode.com/problems/remove-duplicates-from-sorted-array/)

## Solution Approach: Fast-Slow Pointers

This problem is a classic example of using fast and slow pointers for in-place array modification.
- `slow` pointer: `nums[0...slow]` will store the unique elements.
- `fast` pointer: `nums[fast]` is the current element being examined.

1. Initialize `slow = 0`. The `fast` pointer will iterate from `1` to `len(nums)-1`.
   (Labuladong's Java version: `slow = 0, fast = 0`, then inside loop `if (nums[fast] != nums[slow]) { slow++; nums[slow] = nums[fast]; } fast++;`. The Python version in the article: `slow = 0, fast = 1`.)
2. Iterate with `fast` through the array.
3. If `nums[fast]` is different from `nums[slow]`, it means `nums[fast]` is a new unique element.
   - Increment `slow`.
   - Copy `nums[fast]` to `nums[slow]`.
4. If `nums[fast]` is the same as `nums[slow]`, it's a duplicate. Do nothing with `slow`, just increment `fast`.
5. After the loop, `nums[0...slow]` contains all unique elements. The number of unique elements is `slow + 1`.

### Python Solution (Labuladong's version)
```python
class Solution:
    def removeDuplicates(self, nums: list[int]) -> int:
        if not nums: # Or len(nums) == 0
            return 0

        slow = 0
        fast = 0 # Java/C++ style initialization from Labuladong

        # Labuladong's article uses fast = 0 for Java/C++, 
        # but Python version in article uses fast = 1. Let's use the fast=0 logic for consistency with the general explanation.
        # If fast = 0 initially:
        # while fast < len(nums):
        #     if nums[fast] != nums[slow]:
        #         slow += 1
        #         nums[slow] = nums[fast]
        #     fast += 1
        # return slow + 1

        # Using the Python article's version: slow=0, fast=1
        slow = 0 
        # fast pointer starts from the second element
        for fast in range(1, len(nums)):
            if nums[fast] != nums[slow]:
                slow += 1
                nums[slow] = nums[fast]

        # The length of the array with unique elements is slow + 1
        return slow + 1
```
The `div_remove-duplicates-from-sorted-array` visualization in Labuladong's article helps illustrate this.

## Complexity Analysis
- **Time Complexity:** $O(N)$, where $N$ is the length of `nums`. Both `slow` and `fast` pointers traverse the array at most once.
- **Space Complexity:** $O(1)$, as the modification is done in-place.

## Visualization (Labuladong's Python style logic: slow=0, fast iterates from 1)

`nums = [0,0,1,1,1,2,2]`

1.  `slow=0`, `fast=1`. `nums[1](0) == nums[0](0)`. `fast` becomes 2.
    - `nums = [0,0,1,1,1,2,2]`, `slow=0`, `fast=2`
2.  `nums[2](1) != nums[0](0)`.
    - `slow` becomes 1. `nums[1] = nums[2] = 1`.
    - `nums = [0,1,1,1,1,2,2]`, `slow=1`, `fast=2`. Then `fast` becomes 3.
3.  `nums[3](1) == nums[1](1)`. `fast` becomes 4.
    - `nums = [0,1,1,1,1,2,2]`, `slow=1`, `fast=4`
4.  `nums[4](1) == nums[1](1)`. `fast` becomes 5.
    - `nums = [0,1,1,1,1,2,2]`, `slow=1`, `fast=5`
5.  `nums[5](2) != nums[1](1)`.
    - `slow` becomes 2. `nums[2] = nums[5] = 2`.
    - `nums = [0,1,2,1,1,2,2]`, `slow=2`, `fast=5`. Then `fast` becomes 6.
6.  `nums[6](2) == nums[2](2)`. `fast` becomes 7.
    - `nums = [0,1,2,1,1,2,2]`, `slow=2`, `fast=7`. Loop ends.

Return `slow + 1 = 2 + 1 = 3`. `nums` becomes `[0,1,2,_,_,_,_]`.

## 总结 (Summary)
- LC26 uses the fast-slow pointer technique for in-place removal of duplicates from a sorted array.
- The `slow` pointer maintains the end of the unique elements segment, while `fast` explores the array.
- The solution is $O(N)$ time and $O(1)$ space.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]] (Fast-Slow Pointers)
