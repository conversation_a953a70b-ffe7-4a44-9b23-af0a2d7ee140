---
tags: [problem/leetcode, lc/medium, topic/graph, topic/union_find, topic/dfs, pattern/connected_components, pattern/graph_traversal]
aliases: [LC547, LeetCode 547, Number of Provinces, Friend Circles, 省份数量]
---

# LeetCode 547: Number of Provinces

## Problem Statement

There are `n` cities. Some of them are connected, while some are not. If city `a` is connected directly with city `b`, and city `b` is connected directly with city `c`, then city `a` is connected indirectly with city `c`.

A **province** is a group of directly or indirectly connected cities and no other cities outside of the group.

You are given an `n x n` matrix `isConnected` where `isConnected[i][j] = 1` if the `ith` city and `jth` city are directly connected, and `isConnected[i][j] = 0` otherwise.

Return the total number of **provinces**.

**Official Link:** [LeetCode 547. Number of Provinces](https://leetcode.com/problems/number-of-provinces/)

## 🏙️ Understanding the Province Problem

Think of this as finding separate groups of interconnected cities - each group forms a province:

```tikz
\begin{tikzpicture}[
    city/.style={circle, draw, minimum size=1cm, font=\sffamily\bfseries, fill=blue!30},
    province1/.style={city, fill=red!40},
    province2/.style={city, fill=green!40},
    province3/.style={city, fill=yellow!40},
    connection/.style={thick, blue},
    province_boundary/.style={rectangle, draw, dashed, thick},
    example_box/.style={rectangle, draw, fill=orange!20, font=\sffamily\small, text width=4cm, align=center}
]

% Example with 3 provinces
\node at (6, 6) {\bfseries Example: Finding Provinces};

% Province 1: Cities 0, 1, 2
\node[province1] (c0) at (1, 4) {0};
\node[province1] (c1) at (3, 4) {1};
\node[province1] (c2) at (2, 2) {2};

% Province 2: Cities 3, 4
\node[province2] (c3) at (6, 4) {3};
\node[province2] (c4) at (8, 4) {4};

% Province 3: City 5 (isolated)
\node[province3] (c5) at (5, 1) {5};

% Connections within provinces
\draw[connection] (c0) -- (c1);
\draw[connection] (c1) -- (c2);
\draw[connection] (c3) -- (c4);

% Province boundaries
\draw[province_boundary, red] (0.5, 1.5) rectangle (3.5, 4.5);
\draw[province_boundary, green] (5.5, 3.5) rectangle (8.5, 4.5);
\draw[province_boundary, yellow] (4.5, 0.5) rectangle (5.5, 1.5);

\node[example_box] at (10, 3) {
    \textbf{Input Matrix:}\\
    isConnected[0][1] = 1\\
    isConnected[1][2] = 1\\
    isConnected[3][4] = 1\\[0.5em]
    \textbf{Output: 3 provinces}\\
    \{0,1,2\}, \{3,4\}, \{5\}
};

% Labels
\node at (2, 1) {\small Province 1};
\node at (7, 3) {\small Province 2};
\node at (5, 0.2) {\small Province 3};

\end{tikzpicture}
```

**Key Insight:** This is a classic **[[Connected Components]]** problem - count the number of separate connected groups in the graph.

## 🧠 Problem Analysis: Graph Representation

The adjacency matrix represents an undirected graph where we need to find connected components:

```tikz
\begin{tikzpicture}[
    matrix_box/.style={rectangle, draw, fill=blue!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2.5cm},
    graph_box/.style={rectangle, draw, fill=green!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2.5cm},
    arrow/.style={->, thick, red, line width=2pt},
    insight_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=6cm, align=center}
]

\node[matrix_box] (matrix) at (0, 3) {
    \textbf{Adjacency Matrix}\\[0.5em]
    isConnected[i][j] = 1\\
    means cities i and j\\
    are directly connected\\[0.5em]
    Symmetric matrix\\
    (undirected graph)
};

\node[graph_box] (graph) at (8, 3) {
    \textbf{Graph Interpretation}\\[0.5em]
    Cities = Vertices\\
    Connections = Edges\\
    Provinces = Connected\\
    Components\\[0.5em]
    Count components!
};

\draw[arrow] (matrix) -- (graph);

\node[insight_box] at (4, 0.5) {
    \textbf{Algorithm Strategy:}\\
    1. Treat the matrix as an undirected graph\\
    2. Use graph traversal (DFS/BFS) or Union-Find\\
    3. Count the number of connected components\\[0.5em]
    Each component = One province
};

\end{tikzpicture}
```

**Why This Works:**
- Each city is a vertex in the graph
- Direct connections form edges between vertices
- Provinces are exactly the connected components
- We just need to count how many separate components exist

## 💡 Solution Approaches

### Approach 1: Depth-First Search ([[Graph Traversal]])

```python
class Solution:
    def findCircleNum(self, isConnected: List[List[int]]) -> int:
        n = len(isConnected)
        visited = [False] * n
        provinces = 0

        def dfs(city):
            """Mark all cities in the same province as visited"""
            visited[city] = True

            # Visit all directly connected cities
            for neighbor in range(n):
                if isConnected[city][neighbor] == 1 and not visited[neighbor]:
                    dfs(neighbor)

        # Count connected components
        for city in range(n):
            if not visited[city]:
                # Found a new province
                dfs(city)
                provinces += 1

        return provinces
```

### Approach 2: Breadth-First Search

```python
from collections import deque

class Solution:
    def findCircleNum(self, isConnected: List[List[int]]) -> int:
        n = len(isConnected)
        visited = [False] * n
        provinces = 0

        def bfs(start_city):
            """BFS to mark all cities in the same province"""
            queue = deque([start_city])
            visited[start_city] = True

            while queue:
                city = queue.popleft()

                # Check all possible connections
                for neighbor in range(n):
                    if isConnected[city][neighbor] == 1 and not visited[neighbor]:
                        visited[neighbor] = True
                        queue.append(neighbor)

        # Count connected components
        for city in range(n):
            if not visited[city]:
                bfs(city)
                provinces += 1

        return provinces
```

### Approach 3: Union-Find ([[Disjoint Set Union]])

```python
class UnionFind:
    def __init__(self, n):
        self.parent = list(range(n))
        self.rank = [0] * n
        self.components = n  # Initially n separate components

    def find(self, x):
        if self.parent[x] != x:
            self.parent[x] = self.find(self.parent[x])  # Path compression
        return self.parent[x]

    def union(self, x, y):
        px, py = self.find(x), self.find(y)
        if px == py:
            return False  # Already in same component

        # Union by rank
        if self.rank[px] < self.rank[py]:
            px, py = py, px

        self.parent[py] = px
        if self.rank[px] == self.rank[py]:
            self.rank[px] += 1

        self.components -= 1  # Merged two components
        return True

class Solution:
    def findCircleNum(self, isConnected: List[List[int]]) -> int:
        n = len(isConnected)
        uf = UnionFind(n)

        # Process all connections
        for i in range(n):
            for j in range(i + 1, n):  # Only check upper triangle
                if isConnected[i][j] == 1:
                    uf.union(i, j)

        return uf.components
```

## 🔍 Visual Algorithm Trace

Let's trace through the DFS approach with `isConnected = [[1,1,0],[1,1,0],[0,0,1]]`:

```tikz
\begin{tikzpicture}[
    city/.style={circle, draw, minimum size=0.8cm, font=\sffamily\small, fill=blue!30},
    visited/.style={city, fill=green!40},
    current/.style={city, fill=yellow!50},
    unvisited/.style={city, fill=gray!30},
    step_box/.style={rectangle, draw, fill=yellow!20, font=\tiny, text width=3cm, align=center},
    matrix_cell/.style={rectangle, draw, minimum size=0.4cm, font=\tiny}
]

% Step 1: Initial state
\node at (4, 8) {\bfseries Step 1: Initialize (provinces = 0)};

% Matrix representation
\node at (1, 7) {\tiny Matrix:};
\node[matrix_cell, fill=red!30] at (0.5, 6.5) {1};
\node[matrix_cell, fill=red!30] at (1, 6.5) {1};
\node[matrix_cell] at (1.5, 6.5) {0};
\node[matrix_cell, fill=red!30] at (0.5, 6) {1};
\node[matrix_cell, fill=red!30] at (1, 6) {1};
\node[matrix_cell] at (1.5, 6) {0};
\node[matrix_cell] at (0.5, 5.5) {0};
\node[matrix_cell] at (1, 5.5) {0};
\node[matrix_cell, fill=blue!30] at (1.5, 5.5) {1};

% Graph representation
\node[unvisited] (c0) at (4, 6.5) {0};
\node[unvisited] (c1) at (6, 6.5) {1};
\node[unvisited] (c2) at (8, 6.5) {2};

\draw[thick] (c0) -- (c1);

\node[step_box] at (10, 6.5) {
    visited = [F,F,F]\\
    provinces = 0\\
    Start with city 0
};

% Step 2: DFS from city 0
\node at (4, 5) {\bfseries Step 2: DFS from City 0 (New Province!)};

\node[current] (d0) at (4, 4) {0};
\node[visited] (d1) at (6, 4) {1};
\node[unvisited] (d2) at (8, 4) {2};

\draw[thick, red] (d0) -- (d1);

\node[step_box] at (10, 4) {
    DFS(0): visit 0→1\\
    visited = [T,T,F]\\
    provinces = 1\\
    Province 1: \{0,1\}
};

% Step 3: Check city 1 (already visited)
\node at (4, 2.5) {\bfseries Step 3: Check City 1 (Already Visited)};

\node[visited] (v0) at (4, 1.5) {0};
\node[visited] (v1) at (6, 1.5) {1};
\node[unvisited] (v2) at (8, 1.5) {2};

\draw[thick] (v0) -- (v1);

\node[step_box] at (10, 1.5) {
    City 1 already visited\\
    Skip to next city\\
    provinces = 1
};

% Step 4: DFS from city 2
\node at (4, 0) {\bfseries Step 4: DFS from City 2 (New Province!)};

\node[visited] (f0) at (4, -1) {0};
\node[visited] (f1) at (6, -1) {1};
\node[current] (f2) at (8, -1) {2};

\draw[thick] (f0) -- (f1);

\node[step_box] at (10, -1) {
    DFS(2): only city 2\\
    visited = [T,T,T]\\
    provinces = 2\\
    Province 2: \{2\}
};

% Final result
\node[step_box, fill=green!30] at (4, -2.5) {
    \textbf{Final Answer: 2 provinces}\\
    Province 1: Cities 0,1\\
    Province 2: City 2
};

\end{tikzpicture}
```

**Key Observations:**
1. **Component discovery**: Each unvisited city starts a new province
2. **DFS exploration**: From each starting city, visit all reachable cities
3. **Visited tracking**: Prevents counting the same city multiple times
4. **Province counting**: Number of DFS calls = number of provinces

## 🚀 Complete Solution Comparison

### Algorithm Comparison

```tikz
\begin{tikzpicture}[
    algo_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=2.5cm},
    pro_con_box/.style={rectangle, draw, fill=yellow!20, font=\tiny, text width=3cm, align=left}
]

\node[algo_box] (dfs) at (0, 3) {
    \textbf{DFS Approach}\\[0.5em]
    • Recursive traversal\\
    • Mark visited cities\\
    • Count components\\[0.5em]
    Time: O(n²)\\
    Space: O(n)
};

\node[algo_box] (bfs) at (4.5, 3) {
    \textbf{BFS Approach}\\[0.5em]
    • Queue-based traversal\\
    • Level-by-level exploration\\
    • Same logic as DFS\\[0.5em]
    Time: O(n²)\\
    Space: O(n)
};

\node[algo_box] (uf) at (9, 3) {
    \textbf{Union-Find}\\[0.5em]
    • Disjoint set operations\\
    • Path compression\\
    • Union by rank\\[0.5em]
    Time: O(n² a(n))\\
    Space: O(n)
};

\node[pro_con_box] at (0, 0.5) {
    \textbf{Pros:}\\
    • Simple to implement\\
    • Intuitive logic\\
    • Good for sparse graphs\\
    \\
    \textbf{Cons:}\\
    • Recursion stack depth\\
    • Not easily parallelizable
};

\node[pro_con_box] at (4.5, 0.5) {
    \textbf{Pros:}\\
    • No recursion\\
    • Iterative approach\\
    • Memory predictable\\
    \\
    \textbf{Cons:}\\
    • Queue overhead\\
    • More complex code
};

\node[pro_con_box] at (9, 0.5) {
    \textbf{Pros:}\\
    • Excellent for dynamic\\
    • Near-constant operations\\
    • Highly optimized\\
    \\
    \textbf{Cons:}\\
    • More complex to implement\\
    • Overkill for static problem
};

\end{tikzpicture}
```

### When to Use Each Approach

1. **DFS**: Best for most cases - simple and efficient
2. **BFS**: When you need iterative approach or level-by-level processing
3. **Union-Find**: When you have dynamic connectivity queries or need to track component merging

## 🧠 Key Takeaways & Problem-Solving Framework

### Why This Problem is Educational

```tikz
\begin{tikzpicture}[
    concept_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=1.8cm},
    arrow/.style={->, thick, green}
]

\node[concept_box] (recognition) at (0, 3) {
    \textbf{Pattern Recognition}\\[0.3em]
    Identify connected\\
    components problem\\
    from description
};

\node[concept_box] (representation) at (4.5, 3) {
    \textbf{Graph Representation}\\[0.3em]
    Convert adjacency matrix\\
    to graph thinking\\
    mindset
};

\node[concept_box] (algorithms) at (9, 3) {
    \textbf{Algorithm Selection}\\[0.3em]
    Choose appropriate\\
    traversal method\\
    for the problem
};

\draw[arrow] (recognition) -- (representation);
\draw[arrow] (representation) -- (algorithms);

\node at (4.5, 1.5) {\bfseries Core Learning: Recognize → Represent → Solve};

\end{tikzpicture}
```

### Problem-Solving Pattern: [[Connected Components Analysis]]

This problem exemplifies the **connected components** pattern:

1. **Graph identification**: Recognize the underlying graph structure
2. **Component definition**: Understand what constitutes a connected group
3. **Traversal strategy**: Choose DFS, BFS, or Union-Find
4. **Counting logic**: Track the number of separate components

### Complexity Analysis
- **Time Complexity:** O(n²) - need to check all matrix entries
- **Space Complexity:** O(n) - visited array and recursion stack

### Related Concepts for Obsidian

This problem connects to several important algorithmic concepts:

- **[[Connected Components]]**: Core concept being applied
- **[[Graph Traversal]]**: DFS and BFS implementation techniques
- **[[Disjoint Set Union]]**: Union-Find data structure
- **[[Graph Representation]]**: Adjacency matrix interpretation
- **[[Depth-First Search]]**: Recursive graph exploration

### Related Problems
- **LC200. Number of Islands**: 2D grid connected components
- **LC323. Number of Connected Components in an Undirected Graph**: Direct variation
- **LC1319. Number of Operations to Make Network Connected**: Union-Find with modifications
- **LC684. Redundant Connection**: Finding cycles in connected components

### Common Variations
1. **Weighted connections**: Add edge weights to the problem
2. **Dynamic connections**: Add/remove connections over time
3. **Constrained connections**: Only certain types of connections count
4. **Path queries**: Find if two cities are in the same province

## 🎯 Implementation Tips

### DFS Optimization
```python
# Optimized DFS - only check upper triangle of matrix
def findCircleNum_optimized(self, isConnected):
    n = len(isConnected)
    visited = [False] * n
    provinces = 0

    def dfs(city):
        visited[city] = True
        for neighbor in range(n):
            if isConnected[city][neighbor] == 1 and not visited[neighbor]:
                dfs(neighbor)

    for city in range(n):
        if not visited[city]:
            dfs(city)
            provinces += 1

    return provinces
```

### Memory-Efficient Approach
```python
# Use the input matrix itself to mark visited cities
def findCircleNum_inplace(self, isConnected):
    n = len(isConnected)
    provinces = 0

    def dfs(city):
        for neighbor in range(n):
            if isConnected[city][neighbor] == 1:
                isConnected[city][neighbor] = 0  # Mark as visited
                isConnected[neighbor][city] = 0  # Symmetric
                dfs(neighbor)

    for city in range(n):
        if isConnected[city][city] == 1:  # Unvisited city
            dfs(city)
            provinces += 1

    return provinces
```

This problem beautifully demonstrates how **graph theory concepts** apply to real-world scenarios and showcases multiple algorithmic approaches to the same fundamental problem!
