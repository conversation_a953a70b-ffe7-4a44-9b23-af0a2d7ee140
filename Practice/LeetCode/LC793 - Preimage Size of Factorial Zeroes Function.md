---
tags: [problem/leetcode, lc/hard, topic/math, topic/number_theory, topic/factorial, algorithm/binary_search, course/labuladong]
aliases: [LC793, Preimage Size of Factorial Zeroes Function]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 793. Preimage Size of Factorial Zeroes Function
> Solution detailed in [[Interview/Concept/Algorithms/Mathematical Techniques/03 - Factorial Algorithm Problems|Factorial Algorithm Problems]].

# LeetCode 793: Preimage Size of Factorial Zeroes Function

## Problem Statement
Let `f(x)` be the number of trailing zeros in `x!`. For example, `f(3) = 0` because `3! = 6` has no trailing zeros, while `f(5) = 1` because `5! = 120` has one trailing zero.
Given an integer `k`, return *the number of non-negative integers `x` such that `f(x) = k`*.

**Official Link:** [LeetCode 793. Preimage Size of Factorial Zeroes Function](https://leetcode.com/problems/preimage-size-of-factorial-zeroes-function/)

## Solution Approach
The number of `x` such that `f(x) = k` is always either 0 or 5.
We use binary search to find the smallest `x_left` such that `f(x_left) >= k` and the smallest `x_right` such that `f(x_right) >= k+1`. The number of `x` values is `x_right - x_left`.
Alternatively, find the smallest `x` such that `f(x) = k`. If such an `x` exists, the answer is 5; otherwise, it's 0.

### Python Solution
```python
class Solution:
    def _count_trailing_zeros(self, n: int) -> int:
        '''Helper function: counts trailing zeros in n! (same as LC172)'''
        if n < 0: return 0 # Or handle error
        count = 0
        while n > 0:
            n //= 5
            count += n
        return count

    def _left_bound_n_for_k_zeros(self, k_target_zeros: int) -> int:
        '''Smallest n such that _count_trailing_zeros(n) >= k_target_zeros'''
        low = 0
        # A sufficiently large upper bound. Since K is at most 10^9,
        # N is roughly 5*K. Max N can be around 5 * 10^9.
        high = 5 * k_target_zeros + 5 # Add a small margin for safety if K=0 etc.
                                      # if K=0, N=0 is smallest. 5*0+5 = 5. Correct range.

        ans_n = high + 1 # Initialize to a value outside search space

        while low <= high:
            mid_n = low + (high - low) // 2
            zeros_for_mid_n = self._count_trailing_zeros(mid_n)

            if zeros_for_mid_n >= k_target_zeros:
                ans_n = mid_n
                high = mid_n - 1
            else: # zeros_for_mid_n < k_target_zeros
                low = mid_n + 1
        return ans_n

    def preimageSizeFZF(self, k: int) -> int:
        # Find smallest n_k such that f(n_k) = k
        # This is equivalent to finding smallest n_k where f(n_k) >= k,
        # and then checking if f(n_k) is actually == k.

        # Smallest n such that n! has at least k zeros
        n_for_k_zeros = self._left_bound_n_for_k_zeros(k)

        if self._count_trailing_zeros(n_for_k_zeros) == k:
            return 5 # If such an n exists, there are exactly 5 such n's
        else:
            return 0 # No n results in k zeros

    # Alternative method using the property left_bound(K+1) - left_bound(K)
    # def preimageSizeFZF_alternative(self, k: int) -> int:
    #     return self._left_bound_n_for_k_zeros(k + 1) - self._left_bound_n_for_k_zeros(k)

```

## Complexity Analysis
- `_count_trailing_zeros(n)`: $O(\log n)$.
- `_left_bound_n_for_k_zeros(k_target_zeros)`: Binary search runs $O(\log(\text{SearchRange}))$ times. Search range is $O(K)$. Each call inside is $O(\log K)$. So, $O((\log K)^2)$.
- **Overall Time Complexity:** $O((\log K)^2)$.
- **Space Complexity:** $O(1)$ (if `_count_trailing_zeros` is iterative).

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
