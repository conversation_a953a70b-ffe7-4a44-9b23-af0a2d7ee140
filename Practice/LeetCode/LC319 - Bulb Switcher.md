---
tags: [problem/leetcode, lc/medium, topic/math, topic/brain_teaser, topic/number_theory, course/labuladong]
aliases: [<PERSON><PERSON>319, <PERSON><PERSON><PERSON> Switcher]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 319. Bulb Switcher
> Discussed in [[Interview/Concept/Algorithms/Mathematical Techniques/04 - Brain Teaser Algorithm Problems|Brain Teaser Algorithm Problems]].

# LeetCode 319: Bulb Switcher

## Problem Statement
There are `n` light bulbs that are initially off. You first turn on all the bulbs. Then, you turn off every second bulb. On the third round, you toggle every third bulb (turning on if it's off or turning off if it's on). For the `i`-th round, you toggle every `i`-th bulb. For the `n`-th round, you only toggle the last bulb.
Return *the number of bulbs that are on after `n` rounds*.

**Official Link:** [LeetCode 319. Bulb Switcher](https://leetcode.com/problems/bulb-switcher/)

## Solution Approach: Divisors and Perfect Squares
A bulb `k` is toggled in round `i` if `i` is a divisor of `k`. The final state of a bulb (on/off) depends on whether it was toggled an odd or even number of times.
- Initially off, toggled once -> ON.
- Toggled twice -> OFF.
- Toggled odd times -> ON.
- Toggled even times -> OFF.
The number of times bulb `k` is toggled is equal to the number of its distinct divisors.
Only numbers that are **perfect squares** have an odd number of divisors.
(e.g., Divisors of 9: 1, 3, 9 (3 divisors). Divisors of 16: 1, 2, 4, 8, 16 (5 divisors)).
(e.g., Divisors of 6: 1, 2, 3, 6 (4 divisors)).
So, we need to count the number of perfect squares less than or equal to `n`. This is simply `floor(sqrt(n))`.

### Python Solution
```python
import math

class Solution:
    def bulbSwitch(self, n: int) -> int:
        # The number of bulbs on is the number of perfect squares <= n.
        # This is equivalent to floor(sqrt(n)).
        if n < 0: return 0 # Or based on constraints
        return int(math.sqrt(n))
```

## Complexity Analysis
- **Time Complexity:** $O(\log n)$ or $O(1)$ depending on `sqrt` implementation, effectively constant for typical integer sizes.
- **Space Complexity:** $O(1)$.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
