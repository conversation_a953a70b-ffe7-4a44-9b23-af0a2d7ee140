---
tags: [problem/leetcode, lc/medium, topic/data_structure_design, pattern/ordered_set, pattern/heap]
aliases: [LC855, Exam Room]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/设计考场座位分配算法.md
---

# LC855 - Exam Room

## Problem Statement
(To be filled from LeetCode - Visit [https://leetcode.com/problems/exam-room/](https://leetcode.com/problems/exam-room/))

## Solution Approach
This problem is solved by managing intervals of empty seats and choosing the best interval to place a new student. This is detailed in [[Interview/Concept/Data Structures/Custom Design/00 - Exam Room Seating (TreeSet Intervals)|Exam Room Seating Algorithm]].
The Java solution in the Labuladong article uses `TreeSet` and `HashMap`. A Python equivalent would typically use `heapq` for the priority queue of intervals and dictionaries for `startMap`/`endMap`.

### Python Implementation (Conceptual, adapting Java logic)
The core idea is to use a min-heap. To get the "best" interval (max distance, then smallest seat index), we store tuples like `(-distance, seat_index, start_of_interval, end_of_interval)` in the min-heap.
Python's `heapq` is a min-heap.
The `distance` here is the distance to the closest person if a student sits in this interval.
The `seat_index` is where they would sit.

```python
import heapq

class ExamRoom:
    def __init__(self, n: int):
        self.N = n
        # Min-heap storing tuples: (-distance, seat_idx, start, end)
        # Negative distance for max-heap behavior on distance.
        # seat_idx for tie-breaking.
        self.pq = [] 
        
        # To quickly find intervals when a person leaves
        # map p -> interval_starting_at_p : [p, q]
        self.start_map = {} 
        # map p -> interval_ending_at_p : [q, p]
        self.end_map = {}

        # Initial state: one large interval [-1, N]
        # representing the whole empty room.
        # If seat at 0, distance is N-1 (to imaginary person at N).
        # If N=10, seat=0, dist=9. If N=10, seat=9, dist=9
        # For interval [-1, N]: seat at 0, dist=N-1. Or seat at N-1, dist=N-1.
        # Let's handle initial interval carefully.
        # The effective first interval for seat() is [0, N-1] if empty
        # Or, use virtual students at -1 and N.
        
        # Add initial interval [-1, N]
        # dist for [-1, N] is tricky. if N=1, dist=0, seat=0.
        # If N=10, seats 0...9. If seat at 0, dist to right wall N-1.
        # If seated at 0, actual dist from student at 0 to student at N-1 (if N-1 also taken)
        # is (N-1)-0 = N-1
        # Distance from student at 0 to wall at -1 is 0 - (-1) = 1
        # Distance from student at N-1 to wall at N is N - (N-1) = 1

        # Add the initial "empty" interval. 
        # The first student will sit at 0.
        # The interval [-1, N] represents the entire empty space.
        # The "distance" for this interval (if seating at 0) is N-1 if we think of the "student" at N.
        # Or, if we seat at 0, the actual smallest dist is to the wall at N-1, which is N-1.
        # Let's use the article's distance logic for interval [-1, N]. seat=0.
        # The distance function in article: for x=-1, return y. for y=N, return N-1-x.
        # For [-1,N], distA = distance([-1,N]) = N. seat = 0.
        # So we add (-N, 0, -1, N)
        if n > 0 : # Only if room exists
            self._add_interval_to_pq([-1, self.N])


    def _calculate_seat_and_distance(self, interval):
        x, y = interval[0], interval[1]
        seat, dist = -1, -1
        if x == -1: # Interval is like [-1, y], student sits at 0
            seat = 0
            dist = y # Distance to student at y (or wall at N if y=N)
        elif y == self.N: # Interval is like [x, N], student sits at N-1
            seat = self.N - 1
            dist = (self.N - 1) - x # Distance to student at x
        else: # Interval [x, y], student sits at (x+y)//2
            seat = x + (y - x) // 2
            dist = (y - x) // 2 # Distance to x or y
        return seat, dist

    def _add_interval_to_pq(self, interval):
        # Interval is [start, end]
        # Add to PQ: (-max_dist_achievable, min_seat_idx, start, end)
        if interval[0] >= interval[1]: # Invalid or zero-length interval between students
            return

        seat, dist = self._calculate_seat_and_distance(interval)
        
        # Store in heap: (-dist, seat_idx, start, end)
        # dist is the actual distance to nearest person if someone sits there.
        # For pq ordering: Maximize 'dist', then minimize 'seat'.
        # So, store (-dist, seat) in min-heap.
        entry = (-dist, seat, interval[0], interval[1])
        heapq.heappush(self.pq, entry)
        
        self.start_map[interval[0]] = interval
        self.end_map[interval[1]] = interval

    def _remove_interval_from_pq(self, interval):
        # Removing arbitrary element from heapq is O(N). This is a known issue
        # with Python's heapq vs Java's TreeSet.
        # A common workaround: mark as invalid and ignore when popped, or use more complex structure.
        # For this problem, given constraints, N is small enough (10^4 for ExamRoom which is N seats,
        # number of operations 5*10^4). If K intervals, O(K) to rebuild heap or O(K) to find and remove.
        # Let's assume we can effectively remove or handle invalid entries.
        # A practical way is to keep a set of "removed_intervals"
        # For ExamRoom problem on LC, the number of intervals is N at most.
        
        # Simplified: just remove from maps. PQ handles best valid option.
        if interval[0] in self.start_map:
            del self.start_map[interval[0]]
        if interval[1] in self.end_map:
            del self.end_map[interval[1]]
        # Actual removal from self.pq is harder. We rely on popping valid ones.
        # A better Python way: pq stores (neg_dist, seat_idx, start, end, IS_VALID_FLAG)
        # When removing, find it in pq, mark IS_VALID_FLAG=False. Then when popping, ignore if False.
        # This needs pq items to be mutable, or store them in a dict and heap stores keys.
        # For now, we assume an idealized "removable" PQ or that popping invalid entries is fine.
        # The true solution for LC855 in Python uses a more complex management of pq
        # often by invalidating entries and filtering at pop, or by rebuilding the heap
        # if direct removal is too slow for that test case.
        # A common pattern: heap stores (priority_val, unique_id). A dict maps unique_id to (entry_data, valid_flag).
        # When removing, mark valid_flag=False. When popping from heap, check valid_flag via dict.


    def seat(self) -> int:
        # Get best interval (largest distance, smallest seat_idx)
        # Because we store -dist, min-heap gives largest dist. seat_idx is secondary.
        
        # Need to handle invalid entries if _remove_interval_from_pq doesn't truly remove
        best_interval_data = None
        temp_store = []
        while self.pq:
            neg_dist, seat_idx, start, end = heapq.heappop(self.pq)
            # Check if this interval is still valid (i.e., exists in maps)
            # This is a common way to handle "deletion" from heapq:
            # only process if it's still consistent with current state.
            if self.start_map.get(start) == [start, end] and \
               self.end_map.get(end) == [start, end]:
                best_interval_data = (neg_dist, seat_idx, start, end)
                break # Found the current best valid interval
            # Else, this interval was "removed" (or split), ignore it.
        
        # Push back other valid entries popped during search for best_interval_data
        for item in temp_store:
            heapq.heappush(self.pq, item)

        if best_interval_data is None:
            # This case might happen if all intervals are invalidated and pq becomes empty
            # Or if N=0 initially. Let's assume N > 0.
            # If N=1, pq initially contains (-1,0,-1,1). Popped. Seat is 0.
            # New intervals: [-1,0] -> seat 0, dist 0. [0,1] -> seat 0, dist 0.
            # This implies problem asks for index, not node state.
            # The seat logic needs to be from the problem description.
            # Original interval: [-1, N]
            # first seat() should take from this.
            # Labuladong's Java code has student as endpoints.
            # Seat p is taken. interval [x,p] and [p,y].
            pass # This indicates an issue or edge case not fully handled by simplified pq.

        neg_dist, actual_seat_to_take, start_x, end_y = best_interval_data
        
        # "Remove" the chosen interval [start_x, end_y] from maps
        # This logic of removal assumes that start_map/end_map store the Python list objects directly
        # And we are comparing object identity. More robust is to just del by key.
        if start_x in self.start_map and self.start_map[start_x] == [start_x, end_y]:
            del self.start_map[start_x]
        if end_y in self.end_map and self.end_map[end_y] == [start_x, end_y]:
            del self.end_map[end_y]

        # Add two new intervals formed by taking this seat
        # Interval 1: [start_x, actual_seat_to_take]
        # Interval 2: [actual_seat_to_take, end_y]
        self._add_interval_to_pq([start_x, actual_seat_to_take])
        self._add_interval_to_pq([actual_seat_to_take, end_y])
        
        return actual_seat_to_take

    def leave(self, p: int) -> None:
        # Student at p leaves. Merge intervals [x,p] and [p,y] into [x,y].
        
        # Interval ending at p: [left_start, p]
        interval_left = self.end_map.get(p)
        # Interval starting at p: [p, right_end]
        interval_right = self.start_map.get(p)

        # "Remove" these two intervals from maps (PQ entries will be invalidated by this)
        if interval_left:
            if interval_left[0] in self.start_map and self.start_map[interval_left[0]] == interval_left:
                 del self.start_map[interval_left[0]]
            if interval_left[1] in self.end_map and self.end_map[interval_left[1]] == interval_left:
                 del self.end_map[interval_left[1]]
        
        if interval_right:
            if interval_right[0] in self.start_map and self.start_map[interval_right[0]] == interval_right:
                del self.start_map[interval_right[0]]
            if interval_right[1] in self.end_map and self.end_map[interval_right[1]] == interval_right:
                 del self.end_map[interval_right[1]]
        
        # Add merged interval [interval_left[0], interval_right[1]]
        merged_start = interval_left[0] if interval_left else p # Should always find interval_left and right
        merged_end = interval_right[1] if interval_right else p
        
        self._add_interval_to_pq([merged_start, merged_end])

# Note: The Python heapq implementation here is a simplification.
# A robust Python solution for LC855 often uses a sorted list (like SortedList from sortedcontainers
# or manages a list of lists and re-sorts/filters, or a more complex heap entry management).
# The Labuladong's Java solution is more direct due to TreeSet's capabilities.
```

## Complexity Analysis
- Time: $O(\log K)$ for `seat` and `leave` if PQ operations are efficient (where K is number of intervals, max N). Python `heapq`'s lack of direct removal makes it $O(K)$ if rebuilding or filtering invalid entries becomes dominant.
- Space: $O(N)$

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
