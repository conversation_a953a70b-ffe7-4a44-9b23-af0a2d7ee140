---
tags: [problem/leetcode, lc/hard, topic/linked_list, pattern/reversal, concept/recursion, concept/divide_conquer]
aliases: [LC25, LeetCode 25, Reverse Nodes in k-Group, K个一组翻转链表]
---

# LeetCode 25: Reverse Nodes in k-Group

## Problem Statement

Given the head of a linked list, reverse the nodes of the list k at a time, and return the modified list.

k is a positive integer and is less than or equal to the length of the linked list. If the number of nodes is not a multiple of k, then left-out nodes, in the end, should remain as is.

You may not alter the values in the list's nodes, only nodes themselves may be changed.

**Examples:**
- Input: `head = [1,2,3,4,5]`, `k = 2`, Output: `[2,1,4,3,5]`
- Input: `head = [1,2,3,4,5]`, `k = 3`, Output: `[3,2,1,4,5]`

**Constraints:**
- The number of nodes in the list is `n`
- `1 <= k <= n <= 5000`
- `0 <= Node.val <= 1000`

**Follow up:** Can you solve the problem in O(1) extra memory space?

**Official Link:** [LeetCode 25. Reverse Nodes in k-Group](https://leetcode.com/problems/reverse-nodes-in-k-group/)

## 🎯 Understanding the K-Group Reversal Problem

### Visualization of K-Group Processing

```tikz
\begin{tikzpicture}[
    node/.style={circle, draw, minimum width=0.8cm, font=\tiny, fill=blue!30},
    group1/.style={circle, draw, minimum width=0.8cm, font=\tiny, fill=red!30},
    group2/.style={circle, draw, minimum width=0.8cm, font=\tiny, fill=green!30},
    remaining/.style={circle, draw, minimum width=0.8cm, font=\tiny, fill=gray!30},
    link/.style={->, thick, blue},
    reverse_link/.style={->, thick, red},
    group_box/.style={rectangle, draw, dashed, fill=yellow!10},
    example_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=4cm, align=center}
]

% Original list: [1,2,3,4,5], k=2
\node at (6, 8) {\bfseries K-Group Reversal: k=2};

\node at (1, 7) {\small Original: [1,2,3,4,5]};
\node[group1] (o1) at (1, 6.5) {1};
\node[group1] (o2) at (2.5, 6.5) {2};
\node[group2] (o3) at (4, 6.5) {3};
\node[group2] (o4) at (5.5, 6.5) {4};
\node[remaining] (o5) at (7, 6.5) {5};

\draw[link] (o1) -- (o2);
\draw[link] (o2) -- (o3);
\draw[link] (o3) -- (o4);
\draw[link] (o4) -- (o5);

% Group boundaries
\draw[group_box] (0.5, 6) rectangle (3, 7);
\draw[group_box] (3.5, 6) rectangle (6, 7);
\node at (1.75, 6.2) {\tiny Group 1 (k=2)};
\node at (4.75, 6.2) {\tiny Group 2 (k=2)};

\node at (1, 5) {\small Result: [2,1,4,3,5]};
\node[group1] (r2) at (1, 4.5) {2};
\node[group1] (r1) at (2.5, 4.5) {1};
\node[group2] (r4) at (4, 4.5) {4};
\node[group2] (r3) at (5.5, 4.5) {3};
\node[remaining] (r5) at (7, 4.5) {5};

\draw[reverse_link] (r2) -- (r1);
\draw[link] (r1) -- (r4);
\draw[reverse_link] (r4) -- (r3);
\draw[link] (r3) -- (r5);

\node[example_box] at (9, 6) {
    \textbf{Strategy:}\\
    1. Check if k nodes exist\\
    2. Reverse the k nodes\\
    3. Recursively process rest\\
    4. Connect results\\[0.5em]
    \textbf{Key:}\\
    Divide and conquer!
};

\end{tikzpicture}
```

## 🧠 The Breakthrough: Recursive Decomposition

### Step 1: Understanding the Recursive Structure

```tikz
\begin{tikzpicture}[
    step_box/.style={rectangle, draw, rounded corners, fill=green!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=2cm},
    arrow/.style={->, thick, purple, line width=2pt}
]

\node[step_box] (identify) at (0, 3) {
    \textbf{Step 1: Identify}\\[0.5em]
    Find first k nodes\\
    Check if we have\\
    enough nodes
};

\node[step_box] (reverse) at (4.5, 3) {
    \textbf{Step 2: Reverse}\\[0.5em]
    Reverse the first\\
    k nodes using\\
    reverseN function
};

\node[step_box] (recurse) at (9, 3) {
    \textbf{Step 3: Recurse}\\[0.5em]
    Recursively process\\
    remaining nodes\\
    and connect
};

\draw[arrow] (identify) -- (reverse);
\draw[arrow] (reverse) -- (recurse);

\node at (4.5, 1) {\bfseries Divide and Conquer: Process k nodes at a time};

\end{tikzpicture}
```

### Step 2: Detailed Algorithm Visualization

```tikz
\begin{tikzpicture}[
    node/.style={circle, draw, minimum width=0.7cm, font=\tiny, fill=blue!30},
    current_group/.style={circle, draw, minimum width=0.7cm, font=\tiny, fill=red!30},
    processed/.style={circle, draw, minimum width=0.7cm, font=\tiny, fill=green!30},
    link/.style={->, thick, blue},
    reverse_link/.style={->, thick, red},
    recursive_link/.style={->, thick, green, dashed},
    step_box/.style={rectangle, draw, fill=yellow!20, font=\tiny, text width=3cm, align=center}
]

% Step-by-step breakdown for [1,2,3,4,5], k=3
\node at (6, 9) {\bfseries Recursive Breakdown: k=3};

% Step 1: Identify first group
\node at (1, 8) {\tiny Step 1: Check first 3 nodes};
\node[current_group] (s1n1) at (1, 7.5) {1};
\node[current_group] (s1n2) at (2, 7.5) {2};
\node[current_group] (s1n3) at (3, 7.5) {3};
\node[node] (s1n4) at (4, 7.5) {4};
\node[node] (s1n5) at (5, 7.5) {5};

\draw[link] (s1n1) -- (s1n2);
\draw[link] (s1n2) -- (s1n3);
\draw[link] (s1n3) -- (s1n4);
\draw[link] (s1n4) -- (s1n5);

\node[step_box] at (7.5, 7.5) {
    Found 3 nodes\\
    a = 1, b = 4\\
    Proceed with reversal
};

% Step 2: Reverse first group
\node at (1, 6) {\tiny Step 2: Reverse [1,2,3]};
\node[processed] (s2n3) at (1, 5.5) {3};
\node[processed] (s2n2) at (2, 5.5) {2};
\node[processed] (s2n1) at (3, 5.5) {1};
\node[node] (s2n4) at (4, 5.5) {4};
\node[node] (s2n5) at (5, 5.5) {5};

\draw[reverse_link] (s2n3) -- (s2n2);
\draw[reverse_link] (s2n2) -- (s2n1);
\draw[link] (s2n4) -- (s2n5);

\node[step_box] at (7.5, 5.5) {
    Reversed: 3→2→1\\
    newHead = 3\\
    a (now 1) ready for connection
};

% Step 3: Recursive call
\node at (1, 4) {\tiny Step 3: Recurse on [4,5]};
\node[processed] (s3n3) at (1, 3.5) {3};
\node[processed] (s3n2) at (2, 3.5) {2};
\node[processed] (s3n1) at (3, 3.5) {1};
\node[node] (s3n4) at (4, 3.5) {4};
\node[node] (s3n5) at (5, 3.5) {5};

\draw[reverse_link] (s3n3) -- (s3n2);
\draw[reverse_link] (s3n2) -- (s3n1);
\draw[recursive_link] (s3n1) to[bend left=30] (s3n4);
\draw[link] (s3n4) -- (s3n5);

\node[step_box] at (7.5, 3.5) {
    k=3, but only 2 nodes left\\
    Return [4,5] unchanged\\
    Connect 1 → 4
};

% Final result
\node at (1, 2) {\tiny Final: [3,2,1,4,5]};

\end{tikzpicture}
```

## 💡 Solution Implementations

### Approach 1: Recursive with Helper Function

```python
class Solution:
    def reverseKGroup(self, head: ListNode, k: int) -> ListNode:
        """
        Recursive approach using divide and conquer.
        
        Time: O(n), Space: O(n/k) for recursion stack
        """
        if not head:
            return None
        
        # Check if we have k nodes to reverse
        a = b = head
        for _ in range(k):
            if not b:
                return head  # Not enough nodes, return unchanged
            b = b.next
        
        # Reverse first k nodes
        new_head = self.reverseN(a, k)
        
        # Recursively reverse remaining nodes and connect
        a.next = self.reverseKGroup(b, k)
        
        return new_head
    
    def reverseN(self, head: ListNode, n: int) -> ListNode:
        """
        Reverse first n nodes of the linked list.
        """
        prev = None
        current = head
        
        while n > 0:
            next_temp = current.next
            current.next = prev
            prev = current
            current = next_temp
            n -= 1
        
        # Connect to the remaining part
        head.next = current
        return prev
```

### Approach 2: Iterative with Stack

```python
class Solution:
    def reverseKGroup(self, head: ListNode, k: int) -> ListNode:
        """
        Iterative approach using stack for reversal.
        
        Time: O(n), Space: O(k) for stack
        """
        if not head or k == 1:
            return head
        
        # Count total nodes
        count = 0
        curr = head
        while curr:
            count += 1
            curr = curr.next
        
        dummy = ListNode(0)
        dummy.next = head
        prev_group_end = dummy
        
        while count >= k:
            # Collect k nodes in stack
            stack = []
            curr = prev_group_end.next
            
            for _ in range(k):
                stack.append(curr)
                curr = curr.next
            
            # Reverse by popping from stack
            while stack:
                prev_group_end.next = stack.pop()
                prev_group_end = prev_group_end.next
            
            # Connect to next group
            prev_group_end.next = curr
            count -= k
        
        return dummy.next
```

### Approach 3: Iterative In-Place (Most Efficient)

```python
class Solution:
    def reverseKGroup(self, head: ListNode, k: int) -> ListNode:
        """
        Iterative in-place reversal without extra space.
        
        Time: O(n), Space: O(1)
        Most efficient for interviews.
        """
        if not head or k == 1:
            return head
        
        # Count nodes
        length = 0
        curr = head
        while curr:
            length += 1
            curr = curr.next
        
        dummy = ListNode(0)
        dummy.next = head
        prev_group = dummy
        
        while length >= k:
            # Save group boundaries
            group_start = prev_group.next
            group_end = group_start
            
            # Find group end
            for _ in range(k - 1):
                group_end = group_end.next
            
            next_group = group_end.next
            
            # Reverse current group
            self.reverseGroup(group_start, group_end)
            
            # Connect with previous and next groups
            prev_group.next = group_end
            group_start.next = next_group
            
            # Move to next group
            prev_group = group_start
            length -= k
        
        return dummy.next
    
    def reverseGroup(self, start: ListNode, end: ListNode) -> None:
        """
        Reverse nodes from start to end (inclusive).
        """
        prev = end.next
        curr = start
        
        while curr != end.next:
            next_temp = curr.next
            curr.next = prev
            prev = curr
            curr = next_temp
```

## 🔍 Visual Algorithm Trace

```tikz
\begin{tikzpicture}[
    node/.style={circle, draw, minimum width=0.6cm, font=\tiny, fill=blue!30},
    group/.style={circle, draw, minimum width=0.6cm, font=\tiny, fill=red!30},
    link/.style={->, thick, blue},
    reverse_link/.style={->, thick, red},
    step_box/.style={rectangle, draw, fill=yellow!20, font=\tiny, text width=2.5cm, align=center}
]

% Complete trace for [1,2,3,4,5], k=2
\node at (6, 9) {\bfseries Complete Trace: k=2};

% Original
\node at (1, 8) {\tiny Original};
\node[node] (o1) at (1, 7.5) {1};
\node[node] (o2) at (2, 7.5) {2};
\node[node] (o3) at (3, 7.5) {3};
\node[node] (o4) at (4, 7.5) {4};
\node[node] (o5) at (5, 7.5) {5};

\draw[link] (o1) -- (o2);
\draw[link] (o2) -- (o3);
\draw[link] (o3) -- (o4);
\draw[link] (o4) -- (o5);

% After first group
\node at (1, 6.5) {\tiny After Group 1};
\node[group] (g1_2) at (1, 6) {2};
\node[group] (g1_1) at (2, 6) {1};
\node[node] (g1_3) at (3, 6) {3};
\node[node] (g1_4) at (4, 6) {4};
\node[node] (g1_5) at (5, 6) {5};

\draw[reverse_link] (g1_2) -- (g1_1);
\draw[link] (g1_1) -- (g1_3);
\draw[link] (g1_3) -- (g1_4);
\draw[link] (g1_4) -- (g1_5);

% After second group
\node at (1, 4.5) {\tiny After Group 2};
\node[group] (g2_2) at (1, 4) {2};
\node[group] (g2_1) at (2, 4) {1};
\node[group] (g2_4) at (3, 4) {4};
\node[group] (g2_3) at (4, 4) {3};
\node[node] (g2_5) at (5, 4) {5};

\draw[reverse_link] (g2_2) -- (g2_1);
\draw[link] (g2_1) -- (g2_4);
\draw[reverse_link] (g2_4) -- (g2_3);
\draw[link] (g2_3) -- (g2_5);

% Final result
\node at (1, 2.5) {\tiny Final: [2,1,4,3,5]};

\node[step_box] at (7.5, 6) {
    \textbf{Process:}\\
    1. Reverse [1,2]\\
    2. Reverse [3,4]\\
    3. Leave [5] alone\\
    4. Connect all parts
};

\end{tikzpicture}
```

## 🧠 Key Insights & Patterns

### Pattern Recognition
- **K-group reversal** = Recursive decomposition + Basic reversal
- **Boundary checking** essential to avoid processing incomplete groups
- **Connection management** critical for maintaining list integrity

### Complexity Analysis
- **Time Complexity:** O(n) - each node processed exactly once
- **Space Complexity:** O(1) for iterative, O(n/k) for recursive

### Related Problems
- **[[LC206 - Reverse Linked List]]**: Foundation pattern
- **[[LC92 - Reverse Linked List II]]**: Single range reversal
- **[[LC24 - Swap Nodes in Pairs]]**: Special case where k=2

This problem demonstrates the power of **recursive decomposition** and how **complex problems** can be solved by **combining simple patterns** effectively!
