---
tags: [problem/leetcode, lc/easy, topic/bit_manipulation, pattern/hamming_weight, course/labuladong]
aliases: [LC191, Number of 1 Bits, Hamming Weight]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 191. Number of 1 Bits
> Discussed as an application of `n & (n-1)` in [[Interview/Concept/Algorithms/Bit Manipulation/00 - Common Bitwise Operations|Common Bitwise Operations]].

# LeetCode 191: Number of 1 Bits (Hamming Weight)

## Problem Statement
Write a function that takes an unsigned integer and returns the number of '1' bits it has (also known as the Hamming weight).

**Official Link:** [LeetCode 191. Number of 1 Bits](https://leetcode.com/problems/number-of-1-bits/)

## Solution Approach: Using `n & (n-1)`

The bitwise trick `n = n & (n-1)` clears the least significant '1' bit of `n`. We can repeatedly apply this operation and count how many times it's done until `n` becomes 0. This count will be the number of set bits.

### Python Solution
```python
class Solution:
    def hammingWeight(self, n: int) -> int:
        count = 0
        while n != 0:
            n = n & (n - 1) # Clear the least significant '1' bit
            count += 1
        return count
```

## Complexity Analysis
- **Time Complexity:** $O(k)$, where $k$ is the number of set bits in `n`. In the worst case (e.g., `n` is all ones like `0xFFFFFFFF`), $k$ can be up to the number of bits in the integer type (e.g., 32 or 64). So, it's effectively $O(1)$ in terms of input value size, or $O(\text{num_bits})$.
- **Space Complexity:** $O(1)$.

## Alternative: Python's `bin().count()`
Python offers a very concise way:
```python
# class Solution:
#    def hammingWeight(self, n: int) -> int:
#        return bin(n).count('1')
```
This converts the number to its binary string representation (e.g., `0b1011`) and then counts occurrences of '1'. While convenient, the bit manipulation method is often what interviewers look for to test understanding of bitwise ops.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
