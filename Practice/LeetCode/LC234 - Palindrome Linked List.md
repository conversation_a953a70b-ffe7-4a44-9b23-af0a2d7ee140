---
tags: [problem/leetcode, lc/easy, topic/linked_list, pattern/palindrome_check, pattern/fast_slow_pointers, pattern/list_reversal, course/labuladong]
aliases: [LC234, LeetCode 234. Palindrome Linked List, 回文链表]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 234. Palindrome Linked List
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷链表算法/如何判断回文链表.md]].

# LeetCode 234: Palindrome Linked List

## Problem Statement

Given the `head` of a singly linked list, return `true` if it is a palindrome or `false` otherwise.

**Official Link:** [LeetCode 234. Palindrome Linked List](https://leetcode.com/problems/palindrome-linked-list/)

**Example 1:**
Input: `head = [1,2,2,1]`
Output: `true`

## Solution Approach: Find Middle + Reverse Second Half + Compare

The optimized approach to solve this in $O(N)$ time and $O(1)$ space involves these steps (detailed in [[Interview/Concept/Algorithms/Linked List/02 - Palindrome Linked List Detection|Palindrome Linked List Detection]]):
1.  **Find Middle:** Use fast and slow pointers to find the middle of the list.
2.  **Reverse Second Half:** Reverse the linked list starting from the node after the middle.
3.  **Compare:** Compare the first half with the reversed second half.
4.  **(Optional but good practice) Restore List:** Reverse the second half again to restore the original list structure.

### Python Solution
```python
# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, val=0, next=None):
#         self.val = val
#         self.next = next

class Solution:
    def isPalindrome(self, head: [ListNode]) -> bool:
        if not head or not head.next:
            return True

        # 1. Find the middle of the list
        # slow will point to the end of the first half (or the middle node in odd length list)
        slow, fast = head, head
        while fast.next and fast.next.next:
            slow = slow.next
            fast = fast.next.next

        # head_second_half is the start of the second half
        head_second_half = slow.next

        # 2. Reverse the second half
        reversed_second_half_head = self._reverse_list(head_second_half)

        # (Optional but clean) Disconnect the first half from the (original) second half
        # slow.next = None 
        # For comparison, we only need p1 to go up to slow, and p2 through reversed_second_half

        # 3. Compare the first half with the reversed second half
        p1 = head
        p2 = reversed_second_half_head
        is_palindrome_result = True
        while p2: # Iterate while the reversed second half has nodes
            if p1.val != p2.val:
                is_palindrome_result = False
                break
            p1 = p1.next
            p2 = p2.next

        # 4. (Optional) Restore the list: reverse the second half again and connect
        # slow.next = self._reverse_list(reversed_second_half_head)

        return is_palindrome_result

    def _reverse_list(self, node: ListNode) -> ListNode:
        prev = None
        curr = node
        while curr:
            next_node = curr.next
            curr.next = prev
            prev = curr
            curr = next_node
        return prev
```
Labuladong's article uses visualizations like `![](/algo/images/palindrome-linkedlist/3.gif)` for finding the middle.

## Complexity Analysis
-   **Time Complexity:** $O(N)$.
    - Finding middle: $O(N)$.
    - Reversing second half: $O(N/2) = O(N)$.
    - Comparing halves: $O(N/2) = O(N)$.
    - Restoring (optional): $O(N/2) = O(N)$.
-   **Space Complexity:** $O(1)$ (if iterative reversal is used). The pointers take constant space.

## Alternative Approach: Using a List (O(N) space)
A simpler but less space-efficient approach:
1. Traverse the linked list and store all node values in an array/list.
2. Use two pointers (start and end) on the array to check if it's a palindrome.
- Time: $O(N)$ (for traversal and for checking array palindrome).
- Space: $O(N)$ (to store node values in the array).
This does not meet the "follow-up" often implied for $O(1)$ space.

## 总结 (Summary)
- Checking if a linked list is a palindrome with $O(1)$ space involves finding the middle, reversing the second half, and then comparing the two halves.
- This method cleverly combines fast-slow pointers and list reversal techniques.
- Restoring the list by re-reversing the second half is good practice if the original list structure needs to be preserved.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Linked List/02 - Palindrome Linked List Detection|Palindrome Linked List Detection]], [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Fast-Slow Pointers]], [[Interview/Concept/Algorithms/Linked List/01 - Linked List Reversal Techniques|List Reversal]]
