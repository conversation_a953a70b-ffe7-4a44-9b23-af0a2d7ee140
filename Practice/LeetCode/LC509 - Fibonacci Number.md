---
tags: [problem/leetcode, lc/easy, topic/dynamic_programming, topic/recursion, pattern/memoization, pattern/tabulation]
aliases: [LC509, LeetCode 509, Fibon<PERSON>ci Number]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 509. Fibonacci Number
> Solution approaches (brute-force, memoization, tabulation, space-optimized tabulation) adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/动态规划解题套路框架.md]].

# LeetCode 509: Fibonacci Number

## Problem Statement

The Fibonacci numbers, commonly denoted `F(n)`, form a sequence, called the <PERSON><PERSON><PERSON><PERSON> sequence, such that each number is the sum of the two preceding ones, starting from 0 and 1. That is:
- `F(0) = 0`
- `F(1) = 1`
- `F(N) = F(N - 1) + F(N - 2)`, for `N > 1`.

Given `n`, calculate `F(n)`.

**Official Link:** [LeetCode 509. Fibonacci Number](https://leetcode.com/problems/fibonacci-number/)

**Example 1:**
Input: `n = 2`
Output: `1`
Explanation: `F(2) = F(1) + F(0) = 1 + 0 = 1`.

## Solution Approaches (Python)

This problem is a classic introduction to dynamic programming concepts, primarily illustrating **overlapping subproblems** and how to optimize them. While not strictly an "optimization" problem (no min/max), it demonstrates DP techniques. See [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|DP Introduction and Framework]].

### 1. Brute-Force Recursion
Directly translate the mathematical recurrence.
```python
class Solution:
    def fib_recursive(self, n: int) -> int:
        if n == 0:
            return 0
        if n == 1:
            return 1
        return self.fib_recursive(n - 1) + self.fib_recursive(n - 2)
```
- **Time Complexity:** $O(2^N)$. The recursion tree has $2^N$ nodes in the worst case, and each node does $O(1)$ work. This is highly inefficient due to recomputing the same Fibonacci numbers multiple times (overlapping subproblems).
- **Space Complexity:** $O(N)$ due to recursion stack depth.
- Labuladong's visualizer `div_mydata-fib` shows this exponential tree.

### 2. Top-Down with Memoization (Recursive DP)
Store results of subproblems to avoid recomputation.
```python
class Solution:
    def fib_memoization(self, n: int) -> int:
        memo = {} # Using a dictionary for memoization
        # Alternatively, an array `memo = [-1] * (n + 1)` could be used if n is not too large.
        
        def dp(k: int) -> int:
            if k == 0: return 0
            if k == 1: return 1
            if k in memo:
                return memo[k]
            
            memo[k] = dp(k - 1) + dp(k - 2)
            return memo[k]
            
        return dp(n)
```
- **Time Complexity:** $O(N)$. Each Fibonacci number `F(i)` for `i` from 0 to `n` is computed only once.
- **Space Complexity:** $O(N)$ for the `memo` dictionary/array and $O(N)$ for the recursion stack.
- Labuladong's visualizer `div_mydata-fib2` shows the "pruned" recursion tree.

### 3. Bottom-Up with Tabulation (Iterative DP)
Build up solutions from base cases.
```python
class Solution:
    def fib_tabulation(self, n: int) -> int:
        if n == 0: return 0
        if n == 1: return 1
        
        dp_table = [0] * (n + 1) # dp_table[i] will store F(i)
        dp_table[0] = 0
        dp_table[1] = 1
        
        for i in range(2, n + 1):
            dp_table[i] = dp_table[i-1] + dp_table[i-2]
            
        return dp_table[n]
```
- **Time Complexity:** $O(N)$. A single loop iterates `n` times.
- **Space Complexity:** $O(N)$ for the `dp_table`.
- Labuladong's visualizer `div_mydata-fib3` shows the filling of the `dp_table`.

### 4. Bottom-Up with Space Optimization
Notice that `F(n)` only depends on `F(n-1)` and `F(n-2)`. We only need to store the last two values.
```python
class Solution:
    def fib(self, n: int) -> int: # This is the final optimized version
        if n == 0: return 0
        if n == 1: return 1
        
        prev2 = 0  # Represents F(i-2)
        prev1 = 1  # Represents F(i-1)
        current = 0 # Represents F(i)
        
        for _ in range(2, n + 1): # Loop from i=2 up to n
            current = prev1 + prev2
            prev2 = prev1
            prev1 = current
            
        return current # Which is F(n)
```
- **Time Complexity:** $O(N)$.
- **Space Complexity:** $O(1)$. Only constant extra space for `prev1`, `prev2`, `current`.
- Labuladong's visualizer `div_fibonacci-number` refers to this optimized approach.

## Visualization Summary
- **Brute-Force Recursion:** Exponentially large tree with many repeated sub-computations.
    ![](/algo/images/dynamic-programming/1.jpg)
- **Memoized Recursion:** "Prunes" the recursion tree; computes each unique subproblem once.
    ![](/algo/images/dynamic-programming/3.jpg) (Conceptual graph of computed subproblems)
- **Tabulation:** Iteratively fills a DP table, equivalent to the memoized results but bottom-up.
    ![](/algo/images/dynamic-programming/4.jpg) (DP table filling, analogous to pruned tree)

## 总结 (Summary)
- The Fibonacci sequence problem is a classic example to demonstrate dynamic programming concepts, especially handling **overlapping subproblems**.
- **Brute-force recursion** is simple but inefficient ($O(2^N)$).
- **Memoization (Top-Down DP)** optimizes recursion by storing subproblem results, achieving $O(N)$ time and $O(N)$ space.
- **Tabulation (Bottom-Up DP)** iteratively builds solutions, also $O(N)$ time and $O(N)$ space.
- **Space-Optimized Tabulation** can reduce space to $O(1)$ for Fibonacci by only keeping track of the necessary previous two states.
- This problem helps build intuition for identifying and optimizing recursive solutions that involve re-computing the same work.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|DP Introduction and Framework]], [[Interview/Concept/Algorithms/Dynamic Programming/Memoization|Memoization]], [[Interview/Concept/Algorithms/Dynamic Programming/Tabulation|Tabulation]]
Related Problems: Many DP problems build on these foundational optimization ideas.
