---
tags: [problem/leetcode, lc/easy, topic/data_structure_design, pattern/queue_from_stacks, course/labuladong]
aliases: [LC232, Implement Queue using Stacks]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典队列_栈算法/队列实现栈以及栈实现队列.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 232. Implement Queue using Stacks
> Solution approach adapted from [[Interview/Concept/Data Structures/Stack/Applications/00 - Stack and Queue Inter-conversion|Stack and Queue Inter-conversion]].

# LeetCode 232: Implement Queue using Stacks

## Problem Statement
Implement a first-in, first-out (FIFO) queue using only two stacks. The implemented queue should support all the functions of a normal queue (`push`, `peek`, `pop`, and `empty`).

Implement the `MyQueue` class:
- `void push(int x)` Pushes element x to the back of the queue.
- `int pop()` Removes the element from the front of the queue and returns it.
- `int peek()` Returns the element at the front of the queue.
- `boolean empty()` Returns true if the queue is empty, false otherwise.

**Official Link:** [LeetCode 232. Implement Queue using Stacks](https://leetcode.com/problems/implement-queue-using-stacks/)

## Solution Approach: Two Stacks

As detailed in [[Interview/Concept/Data Structures/Stack/Applications/00 - Stack and Queue Inter-conversion|Stack and Queue Inter-conversion]], we use two stacks: `s1` for enqueuing (pushing) elements and `s2` for dequeuing (popping/peeking) elements.

1.  **`push(x)`:** Push `x` onto `s1`.
2.  **`pop()` / `peek()`:**
    - If `s2` is empty, transfer all elements from `s1` to `s2`. This effectively reverses the order of elements, placing the oldest elements from `s1` at the top of `s2`.
    - Then, perform `pop` or `peek` on `s2`.
3.  **`empty()`:** The queue is empty if both `s1` and `s2` are empty.

### Python Solution
```python
class MyQueue:
    def __init__(self):
        self.s1 = [] # For push operations
        self.s2 = [] # For pop/peek operations

    def push(self, x: int) -> None:
        self.s1.append(x)

    def _transfer_if_needed(self) -> None:
        # If s2 is empty, transfer elements from s1 to s2
        if not self.s2:
            while self.s1:
                self.s2.append(self.s1.pop())

    def pop(self) -> int:
        self._transfer_if_needed()
        if not self.s2:
            return -1 # Or raise error for empty queue
        return self.s2.pop()

    def peek(self) -> int:
        self._transfer_if_needed()
        if not self.s2:
            return -1 # Or raise error
        return self.s2[-1] # Peek at top of s2

    def empty(self) -> bool:
        return not self.s1 and not self.s2

# Your MyQueue object will be instantiated and called as such:
# obj = MyQueue()
# obj.push(x)
# param_2 = obj.pop()
# param_3 = obj.peek()
# param_4 = obj.empty()
```

## Complexity Analysis
- **`push`**: $O(1)$.
- **`pop`**: Amortized $O(1)$. A single `pop` might take $O(N)$ if transfer occurs, but each element is moved at most twice (once into `s1`, once from `s1` to `s2`, once out of `s2`).
- **`peek`**: Amortized $O(1)$. Similar reasoning to `pop`.
- **`empty`**: $O(1)$.

## 总结 (Summary)
- Implementing a queue using two stacks is a common data structure design problem.
- One stack (`s1`) handles incoming elements (enqueue).
- The other stack (`s2`) handles outgoing elements (dequeue/peek). Elements are transferred from `s1` to `s2` only when `s2` is empty, which reverses their order to achieve FIFO.
- All operations have an amortized time complexity of $O(1)$.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
