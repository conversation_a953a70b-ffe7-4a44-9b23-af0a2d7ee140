---
tags: [problem/leetcode, lc/easy, topic/array, topic/hash_table, topic/math, course/labuladong]
aliases: [LC645, Set Mismatch]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 645. Set Mismatch
> Solutions discussed in [[Interview/Concept/Algorithms/Mathematical Techniques/05 - Finding Missing and Duplicate Elements|Finding Missing and Duplicate Elements]].

# LeetCode 645: Set Mismatch

## Problem Statement
You have a set of integers `s` which originally contained all the numbers from `1` to `n`. Unfortunately, due to some error, one of the numbers in `s` got duplicated to another number in the set, which results in **repetition of one number** and **loss of another number**.
You are given an integer array `nums` representing the data status of this set after the error.
Find the number that occurs twice and the number that is missing and return them in the form of an array.

**Official Link:** [LeetCode 645. Set Mismatch](https://leetcode.com/problems/set-mismatch/)

## Solution Approach: In-place Modification (Cyclic Sort like)

One efficient way is to try to place numbers in their correct positions.
1. Iterate through `nums`. For `nums[i]`:
   - While `nums[i]` is not in its correct place (`nums[i] != i + 1`) AND `nums[i]` is not already equal to the number at its target spot (`nums[i] != nums[nums[i]-1]` to prevent infinite loops on the duplicate number):
     - Swap `nums[i]` with `nums[nums[i]-1]`.
2. After rearrangement, iterate through `nums` again:
   - If `nums[i] != i + 1`, then `nums[i]` is the duplicate number, and `i + 1` is the missing number.

### Python Solution
```python
class Solution:
    def findErrorNums(self, nums: list[int]) -> list[int]:
        n = len(nums)
        i = 0
        while i < n:
            # Target index for nums[i] should be nums[i] - 1
            correct_idx = nums[i] - 1
            # If nums[i] is not in its correct place AND
            # it's not a duplicate that's already blocking its correct place
            if nums[i] != nums[correct_idx]: # Simpler check after ensuring num is in 1..n range
                nums[i], nums[correct_idx] = nums[correct_idx], nums[i]
            else:
                i += 1

        # After sorting, find the mismatch
        for j in range(n):
            if nums[j] != j + 1:
                return [nums[j], j + 1] # [duplicate, missing]

        return [] # Should not be reached given problem constraints
```

## Complexity Analysis
- **Time Complexity:** $O(N)$. Each number is swapped at most once into its correct position in the first loop. The second loop is $O(N)$.
- **Space Complexity:** $O(1)$ for in-place modification.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
