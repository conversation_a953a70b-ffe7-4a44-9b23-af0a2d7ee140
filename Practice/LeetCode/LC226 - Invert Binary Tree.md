---
tags: [problem/leetcode, lc/easy, topic/tree, topic/binary_tree, pattern/tree_traversal, pattern/dfs, pattern/decomposition, course/labuladong]
aliases: [LC226, Invert Binary Tree, Flip Binary Tree, 翻转二叉树]
---
> [!NOTE] Source Annotation
> Problem: LeetCode 226. Invert Binary Tree
> Solutions and discussion adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷二叉树算法/二叉树心法（思路篇）.md]].

# LeetCode 226: Invert Binary Tree

## Problem Statement
Given the `root` of a binary tree, invert the tree, and return *its root*.

**Official Link:** [LeetCode 226. Invert Binary Tree](https://leetcode.com/problems/invert-binary-tree/)

**Example:**
Input: `root = [4,2,7,1,3,6,9]`
Output: `[4,7,2,9,6,3,1]`
Original:
```
    4
   / \
  2   7
 / \ / \
1  3 6  9
```
Inverted:
```
    4
   / \
  7   2
 / \ / \
9  6 3  1
```

## Solution Approaches
This problem can be solved using either "Traversal Thinking" or "Decomposition Thinking" as outlined in [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]].

### 1. Traversal Thinking (Iterative DFS-like action at each node)
The idea is to visit every node and swap its left and right children. This can be done in pre-order, post-order, or even level-order (BFS).

**Pre-order Traversal Approach:**
```python
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None):
#         self.val = val
#         self.left = left
#         self.right = right

class SolutionTraversal:
    def invertTree(self, root: TreeNode) -> TreeNode:
        self._traverse_and_swap(root)
        return root

    def _traverse_and_swap(self, node: TreeNode):
        if not node:
            return
        
        # Pre-order action: Swap current node's children
        node.left, node.right = node.right, node.left
        
        # Recursively call for (the new) left and right children
        self._traverse_and_swap(node.left) 
        self._traverse_and_swap(node.right)
```
- **Logic:** At each node, before going deeper, swap its children. Then, the recursive calls will operate on the subtrees which now have their roots (the original children) in swapped positions.
- Labuladong visualizer: `div_mydata-invert-tree`

### 2. Decomposition Thinking
Define `invertTree(root)` as a function that inverts the subtree rooted at `root` and returns the (inverted) `root`.
1. Base Case: If `root` is `None`, return `None`.
2. Recursively invert the left subtree: `inverted_left = invertTree(root.left)`.
3. Recursively invert the right subtree: `inverted_right = invertTree(root.right)`.
4. Post-order step: Now that the children subtrees are themselves inverted, connect them in swapped positions to the current `root`:
   `root.left = inverted_right`
   `root.right = inverted_left`
5. Return `root`.

```python
class SolutionDecomposition:
    def invertTree(self, root: TreeNode) -> TreeNode:
        if not root:
            return None
        
        # Recursively invert subtrees
        # Note: these calls modify root.left and root.right subtrees in place if they exist,
        # and return the roots of these inverted subtrees.
        # Storing them in temp variables is clearer before swapping.
        
        left_inverted_subtree_root = self.invertTree(root.left)
        right_inverted_subtree_root = self.invertTree(root.right)
        
        # Swap the (now inverted) children of the current root
        root.left = right_inverted_subtree_root
        root.right = left_inverted_subtree_root
        
        return root
```
- **Logic:** The function relies on its definition to handle subproblems. Once `root.left` and `root.right` subtrees are inverted by recursive calls, the current `root` only needs to swap these fully inverted subtrees.
- Labuladong visualizer: `div_mydata-invert-tree2`

## Complexity Analysis (for both approaches)
- **Time Complexity:** $O(N)$, where $N$ is the number of nodes in the tree. Each node is visited once.
- **Space Complexity:** $O(H)$, where $H$ is the height of the tree, due to the recursion stack. In the worst case (skewed tree), $H=N$. In the best case (balanced tree), $H=\log N$.

## 总结 (Summary)
- Inverting a binary tree involves swapping the left and right children of every node.
- Both "traversal thinking" (performing swaps at each node during a DFS) and "decomposition thinking" (recursively inverting subtrees and then swapping them at the parent) lead to correct $O(N)$ solutions.
- The decomposition approach can sometimes feel more aligned with the recursive definition of tree operations.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/13 - Tree Problems - Combining Traversal and Decomposition|Combining Traversal and Decomposition]], [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Recursive DFS]]
