---
tags: [problem/leetcode, lc/medium, topic/sliding_window, pattern/two_pointers, pattern/window_optimization]
aliases: [LC1004, LeetCode 1004, Max Consecutive Ones III, 最大连续1的个数III]
---

# LeetCode 1004: Max Consecutive Ones III

## Problem Statement

Given a binary array `nums` and an integer `k`, return the maximum number of consecutive 1's in the array if you can flip at most `k` 0's.

**Official Link:** [LeetCode 1004. Max Consecutive Ones III](https://leetcode.com/problems/max-consecutive-ones-iii/)

## 🎯 Understanding the Problem

Think of this as a game: you have a string of 0s and 1s, and you get `k` "magic flips" to turn 0s into 1s. What's the longest sequence of 1s you can create?

```tikz
\begin{tikzpicture}[
    bit/.style={rectangle, draw, minimum size=0.8cm, font=\sffamily\bfseries},
    zero/.style={bit, fill=red!30},
    one/.style={bit, fill=green!30},
    flipped/.style={bit, fill=blue!30},
    window/.style={rectangle, draw, thick, red, dashed},
    arrow/.style={->, thick, blue}
]

% Original array
\node at (4, 4) {\bfseries Original Array: [1,1,1,0,0,1,1,1,1,0,1,1]};
\node[one] at (0, 3) {1};
\node[one] at (1, 3) {1};
\node[one] at (2, 3) {1};
\node[zero] at (3, 3) {0};
\node[zero] at (4, 3) {0};
\node[one] at (5, 3) {1};
\node[one] at (6, 3) {1};
\node[one] at (7, 3) {1};
\node[one] at (8, 3) {1};
\node[zero] at (9, 3) {0};
\node[one] at (10, 3) {1};
\node[one] at (11, 3) {1};

% With k=2 flips
\node at (4, 1.5) {\bfseries With k=2 flips: Maximum window length = 6};
\node[one] at (0, 1) {1};
\node[one] at (1, 1) {1};
\node[one] at (2, 1) {1};
\node[flipped] at (3, 1) {1};
\node[flipped] at (4, 1) {1};
\node[one] at (5, 1) {1};
\node[one] at (6, 1) {1};
\node[one] at (7, 1) {1};
\node[one] at (8, 1) {1};
\node[zero] at (9, 1) {0};
\node[one] at (10, 1) {1};
\node[one] at (11, 1) {1};

% Window highlight
\draw[window] (-0.4, 0.6) rectangle (5.4, 1.4);
\node at (2.5, 0.2) {\small Window: indices 0-5 (length 6)};

% Key insight
\node[rectangle, draw, fill=yellow!20, text width=6cm, align=center] at (6, -0.5) {
    \textbf{Key Insight:}\\
    Find the longest subarray that\\
    contains at most k zeros!
};

\end{tikzpicture}
```

**Example Analysis:**
- **Input:** `nums = [1,1,1,0,0,1,1,1,1,0,1,1]`, `k = 2`
- **Output:** `6` (flip the two 0s at indices 3,4 to get 6 consecutive 1s)
- **Strategy:** Use sliding window to find longest subarray with ≤ k zeros

## 🧠 Problem Transformation: The Key Insight

The brilliant insight is to **reframe the problem**:

```tikz
\begin{tikzpicture}[
    problem_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2cm},
    arrow/.style={->, thick, green, line width=2pt},
    insight_box/.style={rectangle, draw, fill=orange!20, font=\sffamily\small, text width=5cm, align=center}
]

\node[problem_box] (original) at (0, 2) {
    \textbf{Original Problem}\\[0.5em]
    "Find max consecutive 1s\\
    after flipping at most\\
    k zeros"
};

\node[problem_box] (transformed) at (8, 2) {
    \textbf{Transformed Problem}\\[0.5em]
    "Find longest subarray\\
    containing at most\\
    k zeros"
};

\draw[arrow] (original) -- (transformed);

\node[insight_box] at (4, 0) {
    \textbf{Why This Works:}\\
    If we flip all zeros in a subarray,\\
    the entire subarray becomes 1s!\\
    So we just need the longest subarray\\
    with <= k zeros.
};

\end{tikzpicture}
```

This transformation is **powerful** because:
1. **Simplifies the problem**: No need to track actual flips
2. **Enables sliding window**: Perfect fit for the sliding window pattern
3. **Clear constraint**: Window is valid when zeros ≤ k

## 🪟 Sliding Window Framework

Let's apply the systematic sliding window approach:

```tikz
\begin{tikzpicture}[
    framework_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4.5cm, align=left, minimum height=2cm},
    arrow/.style={->, thick, blue}
]

% Framework questions
\node[framework_box] (q1) at (0, 4) {
    \textbf{1. When to expand window?}\\[0.3em]
    Always move right pointer.\\
    Add current element to window.
};

\node[framework_box] (q2) at (5.5, 4) {
    \textbf{2. When to shrink window?}\\[0.3em]
    When zeros in window > k.\\
    Move left pointer until valid.
};

\node[framework_box] (q3) at (0, 1.5) {
    \textbf{3. When is window valid?}\\[0.3em]
    When zeros in window ≤ k.\\
    All zeros can be flipped.
};

\node[framework_box] (q4) at (5.5, 1.5) {
    \textbf{4. How to update result?}\\[0.3em]
    Track max window size\\
    when window is valid.
};

% Arrows
\draw[arrow] (q1) -- (q2);
\draw[arrow] (q2) -- (q4);
\draw[arrow] (q4) -- (q3);
\draw[arrow] (q3) -- (q1);

\end{tikzpicture}
```

### Window State Variables:
- **`left`, `right`**: Window boundaries
- **`zeros_count`**: Number of zeros in current window
- **`max_length`**: Maximum valid window size seen

## 💡 Step-by-Step Algorithm

```python
def longestOnes(self, nums: List[int], k: int) -> int:
    left = 0
    zeros_count = 0
    max_length = 0

    for right in range(len(nums)):
        # Expand window: add nums[right]
        if nums[right] == 0:
            zeros_count += 1

        # Shrink window if invalid
        while zeros_count > k:
            if nums[left] == 0:
                zeros_count -= 1
            left += 1

        # Update result with current valid window
        max_length = max(max_length, right - left + 1)

    return max_length
```

## 🔍 Visual Algorithm Trace

Let's trace through `nums = [1,1,1,0,0,1,1,1,1,0,1,1]`, `k = 2`:

```tikz
\begin{tikzpicture}[
    bit/.style={rectangle, draw, minimum size=0.6cm, font=\tiny},
    zero/.style={bit, fill=red!30},
    one/.style={bit, fill=green!30},
    window/.style={rectangle, draw, thick, blue, dashed},
    pointer/.style={->, thick, red},
    state_box/.style={rectangle, draw, fill=yellow!20, font=\tiny, text width=3cm}
]

% Array indices
\foreach \i in {0,1,2,3,4,5,6,7,8,9,10,11} {
    \node at (\i*0.7, 5.5) {\tiny \i};
}

% Step 1: right=3, left=0
\node at (-1, 4.5) {\tiny Step 1:};
\node[one] at (0, 4.5) {1};
\node[one] at (0.7, 4.5) {1};
\node[one] at (1.4, 4.5) {1};
\node[zero] at (2.1, 4.5) {0};
\node[zero] at (2.8, 4.5) {0};
\node[one] at (3.5, 4.5) {1};
\node[one] at (4.2, 4.5) {1};
\node[one] at (4.9, 4.5) {1};
\node[one] at (5.6, 4.5) {1};
\node[zero] at (6.3, 4.5) {0};
\node[one] at (7, 4.5) {1};
\node[one] at (7.7, 4.5) {1};

\draw[window] (-0.3, 4.2) rectangle (2.4, 4.8);
\draw[pointer] (0, 4) -- (0, 4.2) node[below] {\tiny L};
\draw[pointer] (2.1, 4) -- (2.1, 4.2) node[below] {\tiny R};

\node[state_box] at (9.5, 4.5) {
    zeros = 1\\
    length = 4\\
    max = 4
};

% Step 2: right=4, left=0
\node at (-1, 3.5) {\tiny Step 2:};
\node[one] at (0, 3.5) {1};
\node[one] at (0.7, 3.5) {1};
\node[one] at (1.4, 3.5) {1};
\node[zero] at (2.1, 3.5) {0};
\node[zero] at (2.8, 3.5) {0};
\node[one] at (3.5, 3.5) {1};
\node[one] at (4.2, 3.5) {1};
\node[one] at (4.9, 3.5) {1};
\node[one] at (5.6, 3.5) {1};
\node[zero] at (6.3, 3.5) {0};
\node[one] at (7, 3.5) {1};
\node[one] at (7.7, 3.5) {1};

\draw[window] (-0.3, 3.2) rectangle (3.1, 3.8);
\draw[pointer] (0, 3) -- (0, 3.2) node[below] {\tiny L};
\draw[pointer] (2.8, 3) -- (2.8, 3.2) node[below] {\tiny R};

\node[state_box] at (9.5, 3.5) {
    zeros = 2\\
    length = 5\\
    max = 5
};

% Step 3: right=9, left=0 (invalid, need to shrink)
\node at (-1, 2.5) {\tiny Step 3:};
\node[one] at (0, 2.5) {1};
\node[one] at (0.7, 2.5) {1};
\node[one] at (1.4, 2.5) {1};
\node[zero] at (2.1, 2.5) {0};
\node[zero] at (2.8, 2.5) {0};
\node[one] at (3.5, 2.5) {1};
\node[one] at (4.2, 2.5) {1};
\node[one] at (4.9, 2.5) {1};
\node[one] at (5.6, 2.5) {1};
\node[zero] at (6.3, 2.5) {0};
\node[one] at (7, 2.5) {1};
\node[one] at (7.7, 2.5) {1};

\draw[window] (-0.3, 2.2) rectangle (6.6, 2.8);
\draw[pointer] (0, 2) -- (0, 2.2) node[below] {\tiny L};
\draw[pointer] (6.3, 2) -- (6.3, 2.2) node[below] {\tiny R};

\node[state_box, fill=red!20] at (9.5, 2.5) {
    zeros = 3 > k!\\
    Need to shrink\\
    window
};

% Step 4: After shrinking, right=9, left=3
\node at (-1, 1.5) {\tiny Step 4:};
\node[one] at (0, 1.5) {1};
\node[one] at (0.7, 1.5) {1};
\node[one] at (1.4, 1.5) {1};
\node[zero] at (2.1, 1.5) {0};
\node[zero] at (2.8, 1.5) {0};
\node[one] at (3.5, 1.5) {1};
\node[one] at (4.2, 1.5) {1};
\node[one] at (4.9, 1.5) {1};
\node[one] at (5.6, 1.5) {1};
\node[zero] at (6.3, 1.5) {0};
\node[one] at (7, 1.5) {1};
\node[one] at (7.7, 1.5) {1};

\draw[window] (2.0, 1.2) rectangle (6.6, 1.8);
\draw[pointer] (2.1, 1) -- (2.1, 1.2) node[below] {\tiny L};
\draw[pointer] (6.3, 1) -- (6.3, 1.2) node[below] {\tiny R};

\node[state_box] at (9.5, 1.5) {
    zeros = 2\\
    length = 7\\
    max = 7
};

% Final result
\node[state_box, fill=green!30] at (4, 0.5) {
    \textbf{Final Answer: 7}\\
    (Window from index 3 to 9)
};

\end{tikzpicture}
```

**Key Observations:**
1. **Window expansion**: Right pointer always moves forward
2. **Window contraction**: Left pointer moves when zeros > k
3. **Result tracking**: Update max length whenever window is valid
4. **Efficiency**: Each element visited at most twice (once by each pointer)

## 🚀 Complete Solution

```python
class Solution:
    def longestOnes(self, nums: List[int], k: int) -> int:
        left = 0
        zeros_count = 0
        max_length = 0

        for right in range(len(nums)):
            # Expand window: add nums[right]
            if nums[right] == 0:
                zeros_count += 1

            # Shrink window while invalid
            while zeros_count > k:
                if nums[left] == 0:
                    zeros_count -= 1
                left += 1

            # Update result with current valid window
            max_length = max(max_length, right - left + 1)

        return max_length
```

## 🎯 Alternative Approach: Optimized Sliding Window

There's an even more elegant approach that avoids the inner while loop:

```python
class Solution:
    def longestOnes(self, nums: List[int], k: int) -> int:
        left = 0

        for right in range(len(nums)):
            # Expand window
            if nums[right] == 0:
                k -= 1

            # If window becomes invalid, shrink from left
            if k < 0:
                if nums[left] == 0:
                    k += 1
                left += 1

        # Window size is always right - left + 1
        return right - left + 1
```

**Why This Works:**
- We maintain a window that never shrinks below the maximum valid size
- When k < 0, we slide the window (both pointers move)
- The final window size equals the maximum length found

## 🧠 Key Takeaways & Problem-Solving Framework

### Why This Problem is Educational

```tikz
\begin{tikzpicture}[
    concept_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=1.8cm},
    arrow/.style={->, thick, green}
]

\node[concept_box] (transform) at (0, 3) {
    \textbf{Problem Transformation}\\[0.3em]
    "Flip k zeros" becomes\\
    "Find subarray with ≤ k zeros"
};

\node[concept_box] (pattern) at (4.5, 3) {
    \textbf{Pattern Recognition}\\[0.3em]
    Sliding window pattern\\
    for subarray problems
};

\node[concept_box] (optimization) at (9, 3) {
    \textbf{Optimization}\\[0.3em]
    Two-pointer technique\\
    for O(n) solution
};

\draw[arrow] (transform) -- (pattern);
\draw[arrow] (pattern) -- (optimization);

\node at (4.5, 1.5) {\bfseries Core Learning: Transform → Recognize → Optimize};

\end{tikzpicture}
```

### Sliding Window Template

**When to Use:**
- Find optimal subarray/substring
- Constraint can be maintained incrementally
- Need to explore all possible windows efficiently

**Template Structure:**
1. **Initialize** window boundaries and state
2. **Expand** window by moving right pointer
3. **Contract** window when constraint violated
4. **Update** result when window is valid

### Complexity Analysis
- **Time Complexity:** O(n) - each element visited at most twice
- **Space Complexity:** O(1) - only using constant extra space

### Related Problems
- **LC3. Longest Substring Without Repeating Characters**
- **LC424. Longest Repeating Character Replacement**
- **LC76. Minimum Window Substring**
- **LC209. Minimum Size Subarray Sum**

This problem beautifully demonstrates how **problem transformation** and **pattern recognition** can turn a seemingly complex problem into a straightforward sliding window application!
