---
tags: [index, practice/leetcode, interview_prep]
aliases: [LeetCode Problem Solving, LC Practice]
---

# LeetCode Practice Problems

This section contains solutions, explanations, and conceptual links for various LeetCode problems, aimed at building practical problem-solving skills for interviews.

## Getting Started
- [[Interview/Practice/LeetCode/00 - LeetCode Platform Guide and Tips|LeetCode Platform Guide and Tips]]

## Problems by Topic / Difficulty (Example Structure)

### Easy
- [[Interview/Practice/LeetCode/LC1 - Two Sum|LC1 - Two Sum]]
- [[Interview/Practice/LeetCode/LC20 - Valid Parentheses|LC20 - Valid Parentheses]]
- [[Interview/Practice/LeetCode/LC136 - Single Number|LC136 - Single Number]]
- [[Interview/Practice/LeetCode/LC217 - Contains Duplicate|LC217 - Contains Duplicate]]
- [[Interview/Practice/LeetCode/LC2073 - Time Needed to Buy Tickets|LC2073 - Time Needed to Buy Tickets]]
- [[Interview/Practice/LeetCode/LC752 - Open the Lock|LC752 - Open the Lock]]
- [[Interview/Practice/LeetCode/LC773 - Sliding Puzzle|LC773 - Sliding Puzzle]]
### Medium (Placeholders)
- `[[Interview/Practice/LeeCode/LCXXX - Problem Name Medium|LCXXX - Problem Name Medium]]`

### Hard (Placeholders)
- `[[Interview/Practice/LeeCode/LCXXX - Problem Name Hard|LCXXX - Problem Name Hard]]`

## Focus Areas
- **Arrays & Hashing:** [[Interview/Practice/LeetCode/LC1 - Two Sum|LC1]], [[Interview/Practice/LeetCode/LC217 - Contains Duplicate|LC217]]
- **Stack:** [[Interview/Practice/LeetCode/LC20 - Valid Parentheses|LC20]]
- **Queue / Simulation:** [[Interview/Practice/LeetCode/LC2073 - Time Needed to Buy Tickets|LC2073]]
- **Bit Manipulation:** [[Interview/Practice/LeetCode/LC136 - Single Number|LC136]] (Optimal Solution)


## Visualization of Practice Areas

```mermaid
graph TD
    Practice["LeetCode Practice"] --> Guide["[[Interview/Practice/LeetCode/00 - LeetCode Platform Guide and Tips|Platform Guide]]"]
    Practice --> Easy["Easy Problems"]
    Practice --> Medium["(Medium Problems)"]
    Practice --> Hard["(Hard Problems)"]
    Hard --> LC23["[[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Lists]]"]

    Easy --> LC1["[[Interview/Practice/LeetCode/LC1 - Two Sum|LC1 - Two Sum]]"]
    Easy --> LC20["[[Interview/Practice/LeetCode/LC20 - Valid Parentheses|LC20 - Valid Parentheses]]"]
    Easy --> LC136["[[Interview/Practice/LeetCode/LC136 - Single Number|LC136 - Single Number]]"]
    Easy --> LC217["[[Interview/Practice/LeetCode/LC217 - Contains Duplicate|LC217 - Contains Duplicate]]"]
    Easy --> LC2073["[[Interview/Practice/LeetCode/LC2073 - Time Needed to Buy Tickets|LC2073 - Time to Buy Tickets]]"]
    Easy --> LC752["[[Interview/Practice/LeetCode/LC752 - Open the Lock|LC752 - Open the Lock]]"]
    Easy --> LC773["[[Interview/Practice/LeetCode/LC773 - Sliding Puzzle|LC773 - Sliding Puzzle]]"]

    subgraph "Problem Categories"
        CatArrays["Arrays/Hashing"]
        CatStack["Stack"]
        CatQueue["Queue/Simulation"]
        CatBitMan["Bit Manipulation"]
    end

    LC1 --> CatArrays
    LC217 --> CatArrays
    LC20 --> CatStack
    LC2073 --> CatQueue
    LC136 --> CatArrays
    LC136 --> CatBitMan

    classDef category fill:#fffacd,stroke:#ffd700,stroke-width:2px;
    class Easy, Medium, Hard category;
        CatGraphBFS["Graph/BFS"]
    LC752 --> CatGraphBFS
    LC773 --> CatGraphBFS
        CatLinkedList["Linked List"]
        CatHeap["Heap/Priority Queue"]
        CatDC["Divide & Conquer"]
    LC23 --> CatLinkedList
    LC23 --> CatHeap
    LC23 --> CatDC
```

This index will be updated as more problems are added and categorized.
All original LeeCode questions should go to here.- [[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Sorted Lists]]
