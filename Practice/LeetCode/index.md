---
tags: [index, practice/leetcode, interview_prep]
aliases: [LeetCode Problem Solving, LC Practice]
---

# LeetCode Practice Problems

This section contains solutions, explanations, and conceptual links for various LeetCode problems, aimed at building practical problem-solving skills for interviews.

## Getting Started
- [[Interview/Practice/LeetCode/00 - LeetCode Platform Guide and Tips|LeetCode Platform Guide and Tips]]

## Problems by Topic / Difficulty (Example Structure)

### Easy
- [[Interview/Practice/LeetCode/LC1 - Two Sum|LC1 - Two Sum]]
- [[Interview/Practice/LeetCode/LC121 - Best_Time_to_Buy_and_Sell_Stock|LC121 - Best Time to Buy and Sell Stock]]
- [[Interview/Practice/LeetCode/LC136 - Single Number|LC136 - Single Number]]
- [[Interview/Practice/LeetCode/LC141 - Linked List Cycle|LC141 - Linked List Cycle]]
- [[Interview/Practice/LeetCode/LC172 - Factorial_Trailing_Zeroes|LC172 - Factorial Trailing Zeroes]]
- [[Interview/Practice/LeetCode/LC19 - Remove_Nth_Node_From_End_of_List|LC19 - Remove Nth Node From End of List]]
- [[Interview/Practice/LeetCode/LC191 - Number_of_1_Bits|LC191 - Number of 1 Bits]]
- [[Interview/Practice/LeetCode/LC20 - Valid Parentheses|LC20 - Valid Parentheses]]
- [[Interview/Practice/LeetCode/LC206 - Reverse Linked List|LC206 - Reverse Linked List]]
- [[Interview/Practice/LeetCode/LC2073 - Time Needed to Buy Tickets|LC2073 - Time Needed to Buy Tickets]]
- [[Interview/Practice/LeetCode/LC21 - Merge_Two_Sorted_Lists|LC21 - Merge Two Sorted Lists]]
- [[Interview/Practice/LeetCode/LC217 - Contains Duplicate|LC217 - Contains Duplicate]]
- [[Interview/Practice/LeetCode/LC225 - Implement Stack using Queues|LC225 - Implement Stack using Queues]]
- [[Interview/Practice/LeetCode/LC231 - Power_of_Two|LC231 - Power of Two]]
- [[Interview/Practice/LeetCode/LC232 - Implement Queue using Stacks|LC232 - Implement Queue using Stacks]]
- [[Interview/Practice/LeetCode/LC234 - Palindrome Linked List|LC234 - Palindrome Linked List]]
- [[Interview/Practice/LeetCode/LC26 - Remove Duplicates from Sorted Array|LC26 - Remove Duplicates from Sorted Array]]
- [[Interview/Practice/LeetCode/LC27 - Remove Element|LC27 - Remove Element]]
- [[Interview/Practice/LeetCode/LC28 - Find the Index of the First Occurrence in a String|LC28 - Find the Index of the First Occurrence in a String]]
- [[Interview/Practice/LeetCode/LC283 - Move Zeroes|LC283 - Move Zeroes]]
- [[Interview/Practice/LeetCode/LC292 - Nim_Game|LC292 - Nim Game]]
- [[Interview/Practice/LeetCode/LC344 - Reverse String|LC344 - Reverse String]]
- [[Interview/Practice/LeetCode/LC496 - Next Greater Element I|LC496 - Next Greater Element I]]
- [[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 - Fibonacci Number]]
- [[Interview/Practice/LeetCode/LC645 - Set_Mismatch|LC645 - Set Mismatch]]
- [[Interview/Practice/LeetCode/LC704 - Binary Search|LC704 - Binary Search]]
- [[Interview/Practice/LeetCode/LC83 - Remove Duplicates from Sorted List|LC83 - Remove Duplicates from Sorted List]]
- [[Interview/Practice/LeetCode/LC876 - Middle_of_the_Linked_List|LC876 - Middle of the Linked List]]
- [[Interview/Practice/LeetCode/LCR181 - Max Value of Queue|LCR181 - Max Value of Queue]]
- [[Interview/Practice/LeetCode/LCLC509-Fibonacci_Number|LCLC509 - Fibonacci Number]]
- [[Interview/Practice/LeetCode/LCLC121-Best_Time_to_Buy_and_Sell_Stock|LCLC121 - Best Time to Buy and Sell Stock]]
### Medium
- [[Interview/Practice/LeetCode/LC1004 - Max Consecutive Ones III|LC1004 - Max Consecutive Ones III]]
- [[Interview/Practice/LeetCode/LC1011 - Capacity_To_Ship_Packages_Within_D_Days|LC1011 - Capacity to Ship Packages]]
- [[Interview/Practice/LeetCode/LC1024 - Video_Stitching|LC1024 - Video Stitching]]
- [[Interview/Practice/LeetCode/LC1038|LC1038 - BST to Greater Sum Tree]]
- [[Interview/Practice/LeetCode/LC105|LC105 - Construct from Pre/In]]
- [[Interview/Practice/LeetCode/LC106|LC106 - Construct from In/Post]]
- [[Interview/Practice/LeetCode/LC1081 - Smallest Subsequence of Distinct Characters|LC1081 - Smallest Subsequence]]
- [[Interview/Practice/LeetCode/LC1094 - Car_Pooling|LC1094 - Car Pooling]]
- [[Interview/Practice/LeetCode/LC1109 - Corporate_Flight_Bookings|LC1109 - Corporate Flight Bookings]]
- [[Interview/Practice/LeetCode/LC1135 - Connecting_Cities_With_Minimum_Cost|LC1135 - Connecting Cities Min Cost]]
- [[Interview/Practice/LeetCode/LC1143 - Longest_Common_Subsequence|LC1143 - Longest Common Subsequence]]
- [[Interview/Practice/LeetCode/LC114|LC114 - Flatten Binary Tree]]
- [[Interview/Practice/LeetCode/LC116|LC116 - Populating Next Right Pointers]]
- [[Interview/Practice/LeetCode/LC122 - Best_Time_to_Buy_and_Sell_Stock_II|LC122 - Best Time to Buy and Sell Stock II]]
- [[Interview/Practice/LeetCode/LC134 - Gas_Station|LC134 - Gas Station]]
- [[Interview/Practice/LeetCode/LC1373|LC1373 - Max Sum BST]]
- [[Interview/Practice/LeetCode/LC139 - Word_Break|LC139 - Word Break]]
- [[Interview/Practice/LeetCode/LC142 - Linked List Cycle II|LC142 - Linked List Cycle II]]
- [[Interview/Practice/LeetCode/LC144|LC144 - Preorder Traversal]]
- [[Interview/Practice/LeetCode/LC146 - LRU Cache|LC146 - LRU Cache]]
- [[Interview/Practice/LeetCode/LC15 - 3Sum|LC15 - 3Sum]]
- [[Interview/Practice/LeetCode/LC151 - Reverse_Words_in_a_String|LC151 - Reverse Words in a String]]
- [[Interview/Practice/LeetCode/LC160 - Intersection_of_Two_Linked_Lists|LC160 - Intersection of Two Linked Lists]]
- [[Interview/Practice/LeetCode/LC167 - Two Sum II - Input Array Is Sorted|LC167 - Two Sum II]]
- [[Interview/Practice/LeetCode/LC18 - 4Sum|LC18 - 4Sum]]
- [[Interview/Practice/LeetCode/LC1804_-_Implement_Trie_II_(Prefix_Tree)|LC1804 - Implement Trie II]] (🔒 Premium)
- [[Interview/Practice/LeetCode/LC187 - Repeated DNA Sequences|LC187 - Repeated DNA Sequences]]
- [[Interview/Practice/LeetCode/LC198 - House_Robber|LC198 - House Robber]]
- [[Interview/Practice/LeetCode/LC204 - Count_Primes|LC204 - Count Primes]]
- [[Interview/Practice/LeetCode/LC207 - Course_Schedule|LC207 - Course Schedule]]
- [[Interview/Practice/LeetCode/LC208_-_Implement_Trie_(Prefix_Tree)|LC208 - Implement Trie]]
- [[Interview/Practice/LeetCode/LC210 - Course_Schedule_II|LC210 - Course Schedule II]]
- [[Interview/Practice/LeetCode/LC211_-_Design_Add_and_Search_Words_Data_Structure|LC211 - Add/Search Words DS]]
- [[Interview/Practice/LeetCode/LC213 - House_Robber_II|LC213 - House Robber II]]
- [[Interview/Practice/LeetCode/LC215 - Kth Largest Element in an Array|LC215 - Kth Largest Element]]
- [[Interview/Practice/LeetCode/LC222 - Count Complete Tree Nodes|LC222 - Count Complete Tree Nodes]]
- [[Interview/Practice/LeetCode/LC224_-_Basic_Calculator|LC224 - Basic Calculator]]
- [[Interview/Practice/LeetCode/LC227_-_Basic_Calculator_II|LC227 - Basic Calculator II]]
- [[Interview/Practice/LeetCode/LC23 - Merge_K_Sorted_Lists|LC23 - Merge K Sorted Lists]]
- [[Interview/Practice/LeetCode/LC230|LC230 - Kth Smallest in BST]]
- [[Interview/Practice/LeetCode/LC235 - Lowest Common Ancestor of a Binary Search Tree|LC235 - LCA of BST]]
- [[Interview/Practice/LeetCode/LC236 - Lowest Common Ancestor of a Binary Tree|LC236 - LCA of Binary Tree]]
- [[Interview/Practice/LeetCode/LC239 - Sliding Window Maximum|LC239 - Sliding Window Maximum]]
- [[Interview/Practice/LeetCode/LC240 - Search a 2D Matrix II|LC240 - Search a 2D Matrix II]]
- [[Interview/Practice/LeetCode/LC2492 - Minimum Score of a Path Between Two Cities|LC2492 - Minimum Score of Path]]
- [[Interview/Practice/LeetCode/LC253 - Meeting_Rooms_II|LC253 - Meeting Rooms II]]
- [[Interview/Practice/LeetCode/LC295 - Find Median from Data Stream|LC295 - Find Median from Data Stream]]
- [[Interview/Practice/LeetCode/LC297|LC297 - Serialize/Deserialize BT]]
- [[Interview/Practice/LeetCode/LC3 - Longest Substring Without Repeating Characters|LC3 - Longest Substring Without Repeating Characters]]
- [[Interview/Practice/LeetCode/LC300 - Longest_Increasing_Subsequence|LC300 - Longest Increasing Subsequence]]
- [[Interview/Practice/LeetCode/LC303 - Range_Sum_Query_-_Immutable|LC303 - Range Sum Query Immutable]]
- [[Interview/Practice/LeetCode/LC304 - Range_Sum_Query_2D_-_Immutable|LC304 - Range Sum Query 2D Immutable]]
- [[Interview/Practice/LeetCode/LC307_-_Range_Sum_Query_-_Mutable|LC307 - Range Sum Query Mutable]]
- [[Interview/Practice/LeetCode/LC309 - Best_Time_to_Buy_and_Sell_Stock_with_Cooldown|LC309 - Best Time to Buy and Sell Stock with Cooldown]]
- [[Interview/Practice/LeetCode/LC316 - Remove Duplicate Letters|LC316 - Remove Duplicate Letters]]
- [[Interview/Practice/LeetCode/LC319 - Bulb_Switcher|LC319 - Bulb Switcher]]
- [[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 - Coin Change]]
- [[Interview/Practice/LeetCode/LC323 - Number_of_Connected_Components_in_an_Undirected_Graph|LC323 - Number of Connected Components]]
- [[Interview/Practice/LeetCode/LC3335 - Total Characters in String After Transformations I|LC3335 - Total Chars After Transformations I]]
- [[Interview/Practice/LeetCode/LC337 - House_Robber_III|LC337 - House Robber III]]
- [[Interview/Practice/LeetCode/LC34 - Find First and Last Position of Element in Sorted Array|LC34 - Find First and Last Position]]
- [[Interview/Practice/LeetCode/LC355_-_Design_Twitter|LC355 - Design Twitter]]
- [[Interview/Practice/LeetCode/LC370 - Range_Addition|LC370 - Range Addition]]
- [[Interview/Practice/LeetCode/LC372 - Super_Pow|LC372 - Super Pow]]
- [[Interview/Practice/LeetCode/LC380_-_Insert_Delete_GetRandom_O(1)|LC380 - Insert Delete GetRandom O(1)]]
- [[Interview/Practice/LeetCode/LC382 - Linked_List_Random_Node|LC382 - Linked List Random Node]]
- [[Interview/Practice/LeetCode/LC384 - Shuffle_an_Array|LC384 - Shuffle an Array]]
- [[Interview/Practice/LeetCode/LC398 - Random_Pick_Index|LC398 - Random Pick Index]]
- [[Interview/Practice/LeetCode/LC416 - Partition_Equal_Subset_Sum|LC416 - Partition Equal Subset Sum]]
- [[Interview/Practice/LeetCode/LC435 - Non-overlapping_Intervals|LC435 - Non-overlapping Intervals]]
- [[Interview/Practice/LeetCode/LC438 - Find All Anagrams in a String|LC438 - Find All Anagrams]]
- [[Interview/Practice/LeetCode/LC45 - Jump_Game_II|LC45 - Jump Game II]]
- [[Interview/Practice/LeetCode/LC450|LC450 - Delete Node in BST]]
- [[Interview/Practice/LeetCode/LC452 - Minimum_Number_of_Arrows_to_Burst_Balloons|LC452 - Minimum Number of Arrows to Burst Balloons]]
- [[Interview/Practice/LeetCode/LC46 - Permutations|LC46 - Permutations]]
- [[Interview/Practice/LeetCode/LC48 - Rotate Image|LC48 - Rotate Image]]
- [[Interview/Practice/LeetCode/LC494 - Target_Sum|LC494 - Target Sum]]
- [[Interview/Practice/LeetCode/LC5 - Longest Palindromic Substring|LC5 - Longest Palindromic Substring]]
- [[Interview/Practice/LeetCode/LC503 - Next Greater Element II|LC503 - Next Greater Element II]]
- [[Interview/Practice/LeetCode/LC516 - Longest_Palindromic_Subsequence|LC516 - Longest Palindromic Subsequence]]
- [[Interview/Practice/LeetCode/LC518 - Coin_Change_II|LC518 - Coin Change II]]
- [[Interview/Practice/LeetCode/LC528 - Random_Pick_with_Weight|LC528 - Random Pick with Weight]]
- [[Interview/Practice/LeetCode/LC53 - Maximum_Subarray|LC53 - Maximum Subarray]]
- [[Interview/Practice/LeetCode/LC538|LC538 - Convert BST to Greater Tree]]
- [[Interview/Practice/LeetCode/LC54 - Spiral_Matrix|LC54 - Spiral Matrix]]
- [[Interview/Practice/LeetCode/LC543|LC543 - Diameter of Binary Tree]]
- [[Interview/Practice/LeetCode/LC547 - Number of Provinces|LC547 - Number of Provinces]]
- [[Interview/Practice/LeetCode/LC55 - Jump_Game|LC55 - Jump Game]]
- [[Interview/Practice/LeetCode/LC567 - Permutation in String|LC567 - Permutation in String]]
- [[Interview/Practice/LeetCode/LC583 - Delete_Operation_for_Two_Strings|LC583 - Delete Operation for Two Strings]]
- [[Interview/Practice/LeetCode/LC59 - Spiral_Matrix_II|LC59 - Spiral Matrix II]]
- [[Interview/Practice/LeetCode/LC61 - Rotate_List|LC61 - Rotate List]]
- [[Interview/Practice/LeetCode/LC64 - Minimum_Path_Sum|LC64 - Minimum Path Sum]]
- [[Interview/Practice/LeetCode/LC648_-_Replace_Words|LC648 - Replace Words]]
- [[Interview/Practice/LeetCode/LC652|LC652 - Find Duplicate Subtrees]]
- [[Interview/Practice/LeetCode/LC654|LC654 - Maximum Binary Tree]]
- [[Interview/Practice/LeetCode/LC677_-_Map_Sum_Pairs|LC677 - Map Sum Pairs]]
- [[Interview/Practice/LeetCode/LC684 - Redundant_Connection|LC684 - Redundant Connection]]
- [[Interview/Practice/LeetCode/LC700|LC700 - Search in BST]]
- [[Interview/Practice/LeetCode/LC701|LC701 - Insert into BST]]
- [[Interview/Practice/LeetCode/LC712 - Minimum_ASCII_Delete_Sum_for_Two_Strings|LC712 - Minimum ASCII Delete Sum for Two Strings]]
- [[Interview/Practice/LeetCode/LC714 - Best_Time_to_Buy_and_Sell_Stock_with_Transaction_Fee|LC714 - Best Time to Buy and Sell Stock with Transaction Fee]]
- [[Interview/Practice/LeetCode/LC739 - Daily Temperatures|LC739 - Daily Temperatures]]
- [[Interview/Practice/LeetCode/LC74 - Search a 2D Matrix|LC74 - Search a 2D Matrix]]
- [[Interview/Practice/LeetCode/LC752 - Open the Lock|LC752 - Open the Lock]]
- [[Interview/Practice/LeetCode/LC773 - Sliding Puzzle|LC773 - Sliding Puzzle]]
- [[Interview/Practice/LeetCode/LC785 - Is_Graph_Bipartite|LC785 - Is Graph Bipartite?]]
- [[Interview/Practice/LeetCode/LC787 - Cheapest_Flights_Within_K_Stops|LC787 - Cheapest Flights Within K Stops]]
- [[Interview/Practice/LeetCode/LC855_-_Exam_Room|LC855 - Exam Room]]
- [[Interview/Practice/LeetCode/LC86 - Partition_List|LC86 - Partition List]]
- [[Interview/Practice/LeetCode/LC875 - Koko_Eating_Bananas|LC875 - Koko Eating Bananas]]
- [[Interview/Practice/LeetCode/LC877 - Stone_Game|LC877 - Stone Game]]
- [[Interview/Practice/LeetCode/LC886 - Possible_Bipartition|LC886 - Possible Bipartition]]
- [[Interview/Practice/LeetCode/LC889|LC889 - Construct from Pre/Post]]
- [[Interview/Practice/LeetCode/LC912 - Sort an Array|LC912 - Sort an Array]]
- [[Interview/Practice/LeetCode/LC92 - Reverse_Linked_List_II|LC92 - Reverse Linked List II]]
- [[Interview/Practice/LeetCode/LC931 - Minimum_Falling_Path_Sum|LC931 - Minimum Falling Path Sum]]
- [[Interview/Practice/LeetCode/LC990 - Satisfiability_of_Equality_Equations|LC990 - Satisfiability of Equality Equations]]
- `[[Interview/Practice/LeetCode/LC1644 - Lowest Common Ancestor of a Binary Tree II|LC1644 - LCA II]]` (🔒 Premium)
- `[[Interview/Practice/LeetCode/LC1650 - Lowest Common Ancestor of a Binary Tree III|LC1650 - LCA III]]` (🔒 Premium)
- `[[Interview/Practice/LeetCode/LC1676 - Lowest Common Ancestor of a Binary Tree IV|LC1676 - LCA IV]]` (🔒 Premium)
- [[Interview/Practice/LeetCode/LC11-Container_With_Most_Water|LC11 - Container With Most Water]]
- [[Interview/Practice/LeetCode/LC1288-Remove_Covered_Intervals|LC1288 - Remove Covered Intervals]]
- [[Interview/Practice/LeetCode/LC56-Merge_Intervals|LC56 - Merge Intervals]]
- [[Interview/Practice/LeetCode/LC986-Interval_List_Intersections|LC986 - Interval List Intersections]]
- [[Interview/Practice/LeetCode/LC659-Split_Array_into_Consecutive_Subsequences|LC659 - Split Array into Consecutive Subsequences]]
- [[Interview/Practice/LeetCode/LCLC322-Coin_Change|LCLC322 - Coin Change]]
- [[Interview/Practice/LeetCode/LCLC139-Word_Break|LCLC139 - Word Break]]
- [[Interview/Practice/LeetCode/LCLC300-Longest_Increasing_Subsequence|LCLC300 - Longest Increasing Subsequence]]
- [[Interview/Practice/LeetCode/LCLC53-Maximum_Subarray|LCLC53 - Maximum Subarray]]
- [[Interview/Practice/LeetCode/LCLC1143-Longest_Common_Subsequence|LCLC1143 - Longest Common Subsequence]]
- [[Interview/Practice/LeetCode/LCLC583-Delete_Operation_for_Two_Strings|LCLC583 - Delete Operation for Two Strings]]
- [[Interview/Practice/LeetCode/LCLC712-Minimum_ASCII_Delete_Sum_for_Two_Strings|LCLC712 - Minimum ASCII Delete Sum for Two Strings]]
- [[Interview/Practice/LeetCode/LCLC516-Longest_Palindromic_Subsequence|LCLC516 - Longest Palindromic Subsequence]]
- [[Interview/Practice/LeetCode/LCLC122-Best_Time_to_Buy_and_Sell_Stock_II|LCLC122 - Best Time to Buy and Sell Stock II]]
- [[Interview/Practice/LeetCode/LCLC309-Best_Time_to_Buy_and_Sell_Stock_with_Cooldown|LCLC309 - Best Time to Buy and Sell Stock with Cooldown]]
- [[Interview/Practice/LeetCode/LCLC714-Best_Time_to_Buy_and_Sell_Stock_with_Transaction_Fee|LCLC714 - Best Time to Buy and Sell Stock with Transaction Fee]]
- [[Interview/Practice/LeetCode/LCLC787-Cheapest_Flights_Within_K_Stops|LCLC787 - Cheapest Flights Within K Stops]]
- [[Interview/Practice/LeetCode/LCLC64-Minimum_Path_Sum|LCLC64 - Minimum Path Sum]]
- [[Interview/Practice/LeetCode/LCLC198-House_Robber|LCLC198 - House Robber]]
- [[Interview/Practice/LeetCode/LCLC213-House_Robber_II|LCLC213 - House Robber II]]
- [[Interview/Practice/LeetCode/LCLC337-House_Robber_III|LCLC337 - House Robber III]]
### Hard
---
Parent: [[Interview/Practice/index|Practice Problems]]
- [[Interview/Practice/LeetCode/LC10 - Regular_Expression_Matching|LC10 - Regular Expression Matching]]
- [[Interview/Practice/LeetCode/LC115 - Distinct_Subsequences|LC115 - Distinct Subsequences]]
- [[Interview/Practice/LeetCode/LC123 - Best_Time_to_Buy_and_Sell_Stock_III|LC123 - Best Time to Buy and Sell Stock III]]
- [[Interview/Practice/LeetCode/LC1312 - Minimum_Insertion_Steps_to_Make_a_String_Palindrome|LC1312 - Minimum Insertion Steps to Make a String Palindrome]]
- [[Interview/Practice/LeetCode/LC140 - Word_Break_II|LC140 - Word Break II]]
- [[Interview/Practice/LeetCode/LC188 - Best_Time_to_Buy_and_Sell_Stock_IV|LC188 - Best Time to Buy and Sell Stock IV]]
- [[Interview/Practice/LeetCode/LC25 - Reverse_Nodes_in_k-Group|LC25 - Reverse Nodes in k-Group]]
- [[Interview/Practice/LeetCode/LC312 - Burst_Balloons|LC312 - Burst Balloons]]
- [[Interview/Practice/LeetCode/LC315 - Count of Smaller Numbers After Self|LC315 - Count Smaller After Self]]
- [[Interview/Practice/LeetCode/LC327 - Count of Range Sum|LC327 - Count of Range Sum]]
- [[Interview/Practice/LeetCode/LC354 - Russian_Doll_Envelopes|LC354 - Russian Doll Envelopes]]
- [[Interview/Practice/LeetCode/LC410 - Split_Array_Largest_Sum|LC410 - Split Array Largest Sum]]
- [[Interview/Practice/LeetCode/LC460 - LFU Cache|LC460 - LFU Cache]]
- [[Interview/Practice/LeetCode/LC493 - Reverse Pairs|LC493 - Reverse Pairs]]
- [[Interview/Practice/LeetCode/LC710_-_Random_Pick_with_Blacklist|LC710 - Random Pick with Blacklist]]
- [[Interview/Practice/LeetCode/LC72 - Edit_Distance|LC72 - Edit Distance]]
- [[Interview/Practice/LeetCode/LC76 - Minimum Window Substring|LC76 - Minimum Window Substring]]
- [[Interview/Practice/LeetCode/LC772_-_Basic_Calculator_III|LC772 - Basic Calculator III]] (🔒 Premium)
- [[Interview/Practice/LeetCode/LC793 - Preimage_Size_of_Factorial_Zeroes_Function|LC793 - Preimage Size of Factorial Zeroes Function]]
- [[Interview/Practice/LeetCode/LC887 - Super_Egg_Drop|LC887 - Super Egg Drop]]
- [[Interview/Practice/LeetCode/LC391-Perfect_Rectangle|LC391 - Perfect Rectangle]]
- [[Interview/Practice/LeetCode/LC42-Trapping_Rain_Water|LC42 - Trapping Rain Water]]
- [[Interview/Practice/LeetCode/LCLC140-Word_Break_II|LCLC140 - Word Break II]]
- [[Interview/Practice/LeetCode/LCLC354-Russian_Doll_Envelopes|LCLC354 - Russian Doll Envelopes]]
- [[Interview/Practice/LeetCode/LCLC1312-Minimum_Insertion_Steps_to_Make_a_String_Palindrome|LCLC1312 - Minimum Insertion Steps to Make a String Palindrome]]
- [[Interview/Practice/LeetCode/LCLC123-Best_Time_to_Buy_and_Sell_Stock_III|LCLC123 - Best Time to Buy and Sell Stock III]]
- [[Interview/Practice/LeetCode/LCLC188-Best_Time_to_Buy_and_Sell_Stock_IV|LCLC188 - Best Time to Buy and Sell Stock IV]]
- [[Interview/Practice/LeetCode/LCLC312-Burst_Balloons|LCLC312 - Burst Balloons]]
- [[Interview/Practice/LeetCode/LCLC887-Super_Egg_Drop|LCLC887 - Super Egg Drop]]
- [[Interview/Practice/LeetCode/LCLC10-Regular_Expression_Matching|LCLC10 - Regular Expression Matching]]
### Newly Added (Categorize Manually if needed)
- [[Interview/Practice/LeetCode/LC46_Permutations|LC46 - Permutations]]
- [[Interview/Practice/LeetCode/LC1020_Number_of_Enclaves|LC1020 - Number of Enclaves]]
- [[Interview/Practice/LeetCode/LC1254_Number_of_Closed_Islands|LC1254 - Number of Closed Islands]]
- [[Interview/Practice/LeetCode/LC1905_Count_Sub_Islands|LC1905 - Count Sub Islands]]
- [[Interview/Practice/LeetCode/LC200_Number_of_Islands|LC200 - Number of Islands]]
- [[Interview/Practice/LeetCode/LC694_Number_of_Distinct_Islands|LC694 - Number of Distinct Islands]]
- [[Interview/Practice/LeetCode/LC695_Max_Area_of_Island|LC695 - Max Area of Island]]
- [[Interview/Practice/LeetCode/LC22_Generate_Parentheses|LC22 - Generate Parentheses]]
- [[Interview/Practice/LeetCode/LC37_Sudoku_Solver|LC37 - Sudoku Solver]]
- [[Interview/Practice/LeetCode/LC51_N-Queens|LC51 - N-Queens]]
- [[Interview/Practice/LeetCode/LC52_N-Queens_II|LC52 - N-Queens II]]
- [[Interview/Practice/LeetCode/LC216_Combination_Sum_III|LC216 - Combination Sum III]]
- [[Interview/Practice/LeetCode/LC39_Combination_Sum|LC39 - Combination Sum]]
- [[Interview/Practice/LeetCode/LC40_Combination_Sum_II|LC40 - Combination Sum II]]
- [[Interview/Practice/LeetCode/LC47_Permutations_II|LC47 - Permutations II]]
- [[Interview/Practice/LeetCode/LC77_Combinations|LC77 - Combinations]]
- [[Interview/Practice/LeetCode/LC78_Subsets|LC78 - Subsets]]
- [[Interview/Practice/LeetCode/LC90_Subsets_II|LC90 - Subsets II]]