---
tags: [problem/leetcode, lc/medium, topic/linked_list, topic/two_pointers, pattern/fast_slow_pointers, course/labuladong]
aliases: [LC142, LeetCode 142. Linked List Cycle II, 环形链表 II]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 142. Linked List Cycle II
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道链表题目.md]].

# LeetCode 142: Linked List Cycle II

## Problem Statement

Given the `head` of a linked list, return *the node where the cycle begins. If there is no cycle, return `null`*.

There is a cycle in a linked list if there is some node in the list that can be reached again by continuously following the `next` pointer. Internally, `pos` is used to denote the index of the node that tail's `next` pointer is connected to (**0-indexed**). It is `-1` if there is no cycle. **Note that `pos` is not passed as a parameter.**

**Do not modify** the linked list.

**Official Link:** [LeetCode 142. Linked List Cycle II](https://leetcode.com/problems/linked-list-cycle-ii/)

**Example 1:**
Input: `head = [3,2,0,-4]`, `pos = 1`
Output: `Node with value 2` (where cycle begins)

![](/algo/images/linked-list-two-pointer/circularlinkedlist.png)
*(Source: Labuladong, example visual for a cycle)*

## Solution Approach: Fast and Slow Pointers (Two Phases)

This problem builds upon [[Interview/Practice/LeetCode/LC141 - Linked List Cycle|LC141 - Linked List Cycle]].
1.  **Phase 1: Detect Cycle and Find Meeting Point**
    - Use fast and slow pointers (`slow` moves 1 step, `fast` moves 2 steps).
    - If they meet, a cycle exists. Let the meeting point be `M`.
    - If `fast` reaches `null`, no cycle exists; return `null`.

2.  **Phase 2: Find Cycle Start**
    - After `slow` and `fast` meet at point `M`, reset one pointer (say, `slow`) to the `head` of the list.
    - Keep the other pointer (`fast`) at the meeting point `M`.
    - Now, move both `slow` (from `head`) and `fast` (from `M`) one step at a time.
    - The node where they meet again is the start of the cycle.

**Mathematical Intuition for Phase 2:**
Let distance from `head` to cycle start `S` be $X$.
Let distance from `S` to meeting point `M` be $Y$.
Let length of cycle be $L$.
When `slow` and `fast` meet at `M`:
- `slow` traveled: $X + Y$
- `fast` traveled: $X + Y + kL$ (for some integer $k \ge 1$, `fast` lapped `slow` $k$ times within the cycle)
Since `fast` moves twice as fast as `slow`: $2 \times \text{dist(slow)} = \text{dist(fast)}$
$2(X+Y) = X+Y+kL \implies X+Y = kL$
This means $X = kL - Y$.
Now, if we move one pointer from `head` (distance $X$ to `S`) and another from `M` (distance $L-Y$ to `S` along the cycle, or $kL-Y$ if it laps more), they will meet at `S`.
- Distance from `head` to `S` is $X$.
- Distance from `M` to `S` (going forward in the cycle) is $L-Y$.
Since $X = kL - Y = (k-1)L + (L-Y)$, if pointer 1 moves $X$ steps from `head` and pointer 2 moves $X$ steps from `M`, pointer 2 will have effectively moved $L-Y$ steps from `M` plus some full cycle laps, landing at `S`.
Thus, moving them one step at a time will make them meet at `S`.

Labuladong's article uses images `![](/algo/images/linked-two-pointer/3.jpeg)` (fast/slow meet) and `![](/algo/images/linked-two-pointer/2.jpeg)` (second phase to find start) to visualize this.

### Python Solution (from Labuladong's article)
```python
# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, x):
#         self.val = x
#         self.next = None

class Solution:
    def detectCycle(self, head: [ListNode]) -> [ListNode]:
        slow, fast = head, head

        # Phase 1: Detect cycle and find meeting point
        while fast is not None and fast.next is not None:
            slow = slow.next
            fast = fast.next.next
            if slow == fast: # Cycle detected, break to find start
                break

        # If no cycle (fast reached end)
        if fast is None or fast.next is None:
            return None

        # Phase 2: Find cycle start
        # Reset slow to head, keep fast at meeting point
        slow = head
        while slow != fast:
            slow = slow.next
            fast = fast.next

        # Both pointers now meet at the start of the cycle
        return slow
```
Labuladong's visualization panel `div_linked-list-cycle-ii` demonstrates both phases.

## Complexity Analysis
-   **Time Complexity:** $O(N)$, where $N$ is the number of nodes. Both phases involve traversing the list at most a constant number of times.
-   **Space Complexity:** $O(1)$, using only a few pointers.

## 总结 (Summary)
- Finding the start of a cycle in a linked list is a two-phase process using fast and slow pointers.
- Phase 1 detects the cycle and the meeting point of `slow` and `fast`.
- Phase 2 resets one pointer to `head` and moves both one step at a time from `head` and the meeting point, respectively. Their next meeting point is the cycle's start.
- This solution is $O(N)$ time and $O(1)$ space, and does not modify the list.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Previous: [[Interview/Practice/LeetCode/LC141 - Linked List Cycle|LC141 - Linked List Cycle]]
Related Concepts: [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]]
