---
tags: [problem/leetcode, lc/medium, topic/linked_list, topic/two_pointers, pattern/floyd_cycle, pattern/mathematical_insight]
aliases: [LC142, LeetCode 142, Linked List Cycle II, 环形链表 II]
---

# LeetCode 142: Linked List Cycle II

## Problem Statement

Given the `head` of a linked list, return *the node where the cycle begins*. If there is no cycle, return `null`.

There is a cycle in a linked list if there is some node in the list that can be reached again by continuously following the `next` pointer. Internally, `pos` is used to denote the index of the node that tail's `next` pointer is connected to (**0-indexed**). It is `-1` if there is no cycle. **Note that `pos` is not passed as a parameter**.

**Do not modify** the linked list.

**Official Link:** [LeetCode 142. Linked List Cycle II](https://leetcode.com/problems/linked-list-cycle-ii/)

## 🔄 Understanding Cycle Detection

Think of this as finding where a "loop" begins in a chain of connected nodes:

```tikz
\begin{tikzpicture}[
    node_box/.style={circle, draw, minimum size=1cm, font=\sffamily\bfseries, fill=blue!30},
    cycle_node/.style={circle, draw, minimum size=1cm, font=\sffamily\bfseries, fill=red!40},
    start_node/.style={circle, draw, minimum size=1cm, font=\sffamily\bfseries, fill=green!40},
    arrow/.style={->, thick, blue},
    cycle_arrow/.style={->, thick, red, line width=2pt},
    example_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=4cm, align=center}
]

% Example linked list with cycle
\node at (6, 6) {\bfseries Example: Linked List with Cycle};

% Linear part
\node[node_box] (n1) at (0, 4) {1};
\node[node_box] (n2) at (2, 4) {2};
\node[start_node] (n3) at (4, 4) {3};
\node[cycle_node] (n4) at (6, 4) {4};

% Cycle part
\node[cycle_node] (n5) at (8, 4) {5};
\node[cycle_node] (n6) at (8, 2) {6};
\node[cycle_node] (n7) at (6, 2) {7};

% Arrows
\draw[arrow] (n1) -- (n2);
\draw[arrow] (n2) -- (n3);
\draw[arrow] (n3) -- (n4);
\draw[arrow] (n4) -- (n5);
\draw[arrow] (n5) -- (n6);
\draw[arrow] (n6) -- (n7);
\draw[cycle_arrow] (n7) -- (n3);

% Labels
\node at (2, 5) {\small Linear Part};
\node at (7, 3) {\small Cycle Part};
\node at (4, 3.3) {\tiny Cycle Start};

\node[example_box] at (11, 3.5) {
    \textbf{Goal:}\\
    Find the node where\\
    the cycle begins\\[0.5em]
    \textbf{Challenge:}\\
    Do it in O(1) space\\
    without modifying\\
    the list
};

\end{tikzpicture}
```

**Key Insight:** This builds on **[[Floyd's Cycle Detection]]** (LC141) but requires mathematical insight to find the exact starting point of the cycle.

## 🧠 The Mathematical Breakthrough: Two-Phase Algorithm

The key realization is that cycle detection has beautiful mathematical properties:

```tikz
\begin{tikzpicture}[
    insight_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4.5cm, align=center, minimum height=2.5cm},
    arrow/.style={->, thick, red, line width=2pt},
    strategy_box/.style={rectangle, draw, fill=orange!20, font=\sffamily\small, text width=6cm, align=center}
]

\node[insight_box] (phase1) at (0, 3) {
    \textbf{Phase 1: Detect Cycle}\\[0.5em]
    "Use Floyd's algorithm\\
    (fast/slow pointers)\\
    to detect if cycle exists"\\[0.5em]
    Same as LC141!
};

\node[insight_box] (phase2) at (8, 3) {
    \textbf{Phase 2: Find Start}\\[0.5em]
    "Reset one pointer to head,\\
    move both at same speed\\
    until they meet again"\\[0.5em]
    Mathematical magic!
};

\draw[arrow] (phase1) -- (phase2);

\node[strategy_box] at (4, 0.5) {
    \textbf{Mathematical Insight:}\\
    When fast/slow meet, the distance from head to cycle start\\
    equals distance from meeting point to cycle start\\[0.5em]
    This enables O(1) space solution!
};

\end{tikzpicture}
```

**Why This Works:** The mathematical relationship between pointer speeds creates a beautiful geometric property that we can exploit.

## 🔍 Mathematical Analysis: The Core Insight

Let's understand the mathematics behind this elegant solution:

```tikz
\begin{tikzpicture}[
    node_box/.style={circle, draw, minimum size=0.8cm, font=\tiny, fill=blue!30},
    cycle_node/.style={circle, draw, minimum size=0.8cm, font=\tiny, fill=red!30},
    start_node/.style={circle, draw, minimum size=0.8cm, font=\tiny, fill=green!40},
    meet_node/.style={circle, draw, minimum size=0.8cm, font=\tiny, fill=yellow!50},
    arrow/.style={->, thick, blue},
    distance_line/.style={<->, thick, purple},
    label_box/.style={rectangle, draw, fill=yellow!20, font=\tiny, text width=2.5cm, align=center}
]

% Mathematical diagram
\node at (6, 7) {\bfseries Mathematical Analysis of Cycle Detection};

% Linear part (distance a)
\node[node_box] (head) at (0, 5) {H};
\node[node_box] (n1) at (1.5, 5) {};
\node[start_node] (start) at (3, 5) {S};

% Cycle part
\node[cycle_node] (c1) at (4.5, 5) {};
\node[meet_node] (meet) at (6, 5) {M};
\node[cycle_node] (c2) at (7.5, 5) {};
\node[cycle_node] (c3) at (7.5, 3.5) {};
\node[cycle_node] (c4) at (6, 3.5) {};
\node[cycle_node] (c5) at (4.5, 3.5) {};

% Arrows
\draw[arrow] (head) -- (n1);
\draw[arrow] (n1) -- (start);
\draw[arrow] (start) -- (c1);
\draw[arrow] (c1) -- (meet);
\draw[arrow] (meet) -- (c2);
\draw[arrow] (c2) -- (c3);
\draw[arrow] (c3) -- (c4);
\draw[arrow] (c4) -- (c5);
\draw[arrow] (c5) -- (start);

% Distance annotations
\draw[distance_line] (0, 4.5) -- (3, 4.5);
\node at (1.5, 4.2) {\tiny distance = a};

\draw[distance_line] (3, 4.5) -- (6, 4.5);
\node at (4.5, 4.2) {\tiny distance = b};

\draw[distance_line] (6, 4.5) -- (3, 4.5);
\node at (4.5, 3) {\tiny cycle length = c};

% Key insight boxes
\node[label_box] at (9, 5.5) {
    \textbf{When pointers meet:}\\
    slow traveled: a + b\\
    fast traveled: 2(a + b)
};

\node[label_box] at (9, 3.5) {
    \textbf{Key insight:}\\
    fast = slow + k×c\\
    Therefore: a = c - b\\
    (mod cycle length)
};

\node[label_box] at (1.5, 2) {
    \textbf{Magic property:}\\
    Distance from head to start\\
    = Distance from meet to start\\
    Both equal 'a'!
};

\end{tikzpicture}
```

**Mathematical Proof:**
1. When pointers meet: `slow = a + b`, `fast = 2(a + b)`
2. Fast pointer is `k` cycles ahead: `fast = slow + k×c`
3. Therefore: `2(a + b) = (a + b) + k×c` → `a + b = k×c`
4. This gives us: `a = k×c - b = (k-1)×c + (c - b)`
5. Since `c - b` is distance from meeting point back to start, `a` equals that distance!

## 💡 Clean, Elegant Algorithm

Here's the beautifully structured solution that maps directly to our mathematical insight:

```python
# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, x):
#         self.val = x
#         self.next = None

class Solution:
    def detectCycle(self, head: ListNode) -> ListNode:
        if not head or not head.next:
            return None

        # Phase 1: Detect cycle using Floyd's algorithm
        slow = fast = head

        # Move pointers until they meet or fast reaches end
        while fast and fast.next:
            slow = slow.next
            fast = fast.next.next

            if slow == fast:
                break
        else:
            # No cycle found (fast reached end)
            return None

        # Phase 2: Find cycle start using mathematical insight
        # Reset slow to head, keep fast at meeting point
        slow = head

        # Move both pointers one step at a time
        while slow != fast:
            slow = slow.next
            fast = fast.next

        # Meeting point is the cycle start
        return slow
```

## 🔍 Visual Algorithm Trace

Let's trace through the algorithm with a concrete example:

```tikz
\begin{tikzpicture}[
    node_box/.style={circle, draw, minimum size=0.8cm, font=\tiny, fill=blue!30},
    cycle_node/.style={circle, draw, minimum size=0.8cm, font=\tiny, fill=red!30},
    start_node/.style={circle, draw, minimum size=0.8cm, font=\tiny, fill=green!40},
    meet_node/.style={circle, draw, minimum size=0.8cm, font=\tiny, fill=yellow!50},
    slow_pointer/.style={->, thick, blue, line width=2pt},
    fast_pointer/.style={->, thick, red, line width=2pt},
    step_box/.style={rectangle, draw, fill=yellow!20, font=\tiny, text width=3cm, align=center}
]

% Phase 1: Cycle Detection
\node at (6, 8) {\bfseries Phase 1: Cycle Detection};

% Linked list structure
\node[node_box] (h) at (0, 6.5) {1};
\node[node_box] (n1) at (2, 6.5) {2};
\node[start_node] (s) at (4, 6.5) {3};
\node[cycle_node] (c1) at (6, 6.5) {4};
\node[cycle_node] (c2) at (8, 6.5) {5};
\node[cycle_node] (c3) at (8, 5) {6};
\node[meet_node] (m) at (6, 5) {7};
\node[cycle_node] (c4) at (4, 5) {8};

% Arrows
\draw[->] (h) -- (n1);
\draw[->] (n1) -- (s);
\draw[->] (s) -- (c1);
\draw[->] (c1) -- (c2);
\draw[->] (c2) -- (c3);
\draw[->] (c3) -- (m);
\draw[->] (m) -- (c4);
\draw[->] (c4) -- (s);

% Pointer positions when they meet
\draw[slow_pointer] (6, 4.5) -- (6, 4.2);
\node at (6, 4) {\tiny slow};
\draw[fast_pointer] (6, 4.5) -- (6, 4.2);
\node at (6.8, 4) {\tiny fast};

\node[step_box] at (11, 6) {
    \textbf{Meeting Point:}\\
    Both pointers meet\\
    at node 7\\[0.5em]
    Cycle detected!
};

% Phase 2: Find Start
\node at (6, 3) {\bfseries Phase 2: Find Cycle Start};

% Reset slow to head
\draw[slow_pointer] (0, 2.5) -- (0, 2.2);
\node at (0, 2) {\tiny slow (reset)};

% Keep fast at meeting point
\draw[fast_pointer] (6, 2.5) -- (6, 2.2);
\node at (6, 2) {\tiny fast (at meet)};

% Show movement until they meet at start
\node[step_box] at (11, 2.5) {
    \textbf{Move Both 1 Step:}\\
    slow: 1 → 2 → 3\\
    fast: 7 → 8 → 3\\[0.5em]
    Meet at cycle start!
};

% Final result
\node[step_box, fill=green!30] at (6, 0.5) {
    \textbf{Result: Node 3}\\
    Cycle starts at node 3\\
    Mathematical proof guarantees this!
};

\end{tikzpicture}
```

**Key Observations:**
1. **Phase 1**: Fast and slow pointers meet inside the cycle
2. **Phase 2**: Reset one pointer to head, move both at same speed
3. **Mathematical guarantee**: They will meet exactly at cycle start
4. **Efficiency**: Each node visited at most 3 times total

## 🧠 Key Takeaways & Problem-Solving Framework

### Why This Problem is Educational

```tikz
\begin{tikzpicture}[
    concept_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=1.8cm},
    arrow/.style={->, thick, green}
]

\node[concept_box] (math) at (0, 3) {
    \textbf{Mathematical Insight}\\[0.3em]
    Beautiful relationship\\
    between pointer speeds\\
    and cycle geometry
};

\node[concept_box] (algorithm) at (4.5, 3) {
    \textbf{Algorithm Design}\\[0.3em]
    Two-phase approach\\
    that leverages the\\
    mathematical property
};

\node[concept_box] (optimization) at (9, 3) {
    \textbf{Space Optimization}\\[0.3em]
    O(1) space solution\\
    instead of O(n) hash\\
    table approach
};

\draw[arrow] (math) -- (algorithm);
\draw[arrow] (algorithm) -- (optimization);

\node at (4.5, 1.5) {\bfseries Core Learning: Math → Algorithm → Optimization};

\end{tikzpicture}
```

### Problem-Solving Pattern: [[Floyd's Cycle Detection]]

This problem exemplifies **mathematical algorithm design**:

1. **Mathematical analysis**: Understand the geometric relationships
2. **Two-phase approach**: Separate detection from localization
3. **Pointer manipulation**: Use speed differences to create useful properties
4. **Space optimization**: Avoid auxiliary data structures

### Complexity Analysis
- **Time Complexity:** O(n) - each node visited at most 3 times
- **Space Complexity:** O(1) - only using pointer variables

### Related Concepts for Obsidian

This problem connects to several important algorithmic concepts:

- **[[Floyd's Cycle Detection]]**: Core algorithm being applied
- **[[Two Pointers]]**: Fast and slow pointer technique
- **[[Mathematical Algorithms]]**: Algorithm based on mathematical proof
- **[[Linked List]]**: Fundamental data structure
- **[[Cycle Detection]]**: General pattern for detecting cycles

### Related Problems
- **LC141. Linked List Cycle**: Basic cycle detection (prerequisite)
- **LC287. Find the Duplicate Number**: Array-based cycle detection
- **LC202. Happy Number**: Cycle detection in number sequences
- **LC457. Circular Array Loop**: Cycle detection with direction

### Implementation Tips

#### Template for Floyd's Cycle Detection
```python
def floyd_cycle_detection(head):
    # Phase 1: Detect cycle
    slow = fast = head
    while fast and fast.next:
        slow = slow.next
        fast = fast.next.next
        if slow == fast:
            break
    else:
        return None  # No cycle

    # Phase 2: Find cycle start
    slow = head
    while slow != fast:
        slow = slow.next
        fast = fast.next

    return slow  # Cycle start
```

#### Common Variations
- **Cycle length**: Count steps in phase 2 to get cycle length
- **All cycle nodes**: Continue from start to collect all cycle nodes
- **Multiple cycles**: Extend for graphs with multiple cycles
- **Cycle position**: Return index instead of node reference

#### Edge Cases to Consider
- **Empty list**: `head` is `None`
- **Single node**: `head.next` is `None`
- **No cycle**: Fast pointer reaches end
- **Self-loop**: Node points to itself
- **Entire list is cycle**: Head is part of the cycle

This problem beautifully demonstrates how **mathematical insight** can transform a seemingly complex problem into an elegant, space-efficient solution!
