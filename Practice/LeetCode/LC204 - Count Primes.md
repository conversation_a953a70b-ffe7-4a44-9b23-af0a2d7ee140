---
tags: [problem/leetcode, lc/medium, topic/math, topic/prime_numbers, algorithm/sieve_of_eratosthenes, course/labuladong]
aliases: [LC204, Count Primes, 计数质数]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 204. Count Primes
> Solution uses the [[Interview/Concept/Algorithms/Mathematical Techniques/00 - Efficient Prime Number Generation|Sieve of Eratosthenes]].

# LeetCode 204: Count Primes

## Problem Statement
Given an integer `n`, return *the number of prime numbers that are strictly less than `n`*.

**Official Link:** [LeetCode 204. Count Primes](https://leetcode.com/problems/count-primes/)

## Solution Approach: Sieve of Eratosthenes

This problem is a direct application of the Sieve of Eratosthenes algorithm.
The algorithm is detailed in [[Interview/Concept/Algorithms/Mathematical Techniques/00 - Efficient Prime Number Generation|Efficient Prime Number Generation]].

### Python Solution
```python
class Solution:
    def countPrimes(self, n: int) -> int:
        if n <= 2:
            return 0

        # is_prime[i] will be True if i is prime.
        # We are interested in numbers strictly less than n.
        is_prime = [True] * n 
        is_prime[0] = is_prime[1] = False # 0 and 1 are not prime

        # Iterate from p = 2 up to sqrt(n-1).
        # Multiples of p are marked starting from p*p.
        # The loop for p can go up to int(n**0.5) or p*p < n.
        for p in range(2, int(n**0.5) + 1):
            if is_prime[p]:
                # Mark all multiples of p as not prime.
                # Start from p*p, up to n-1, with step p.
                # Example: if p=2, mark 4, 6, 8...
                # Example: if p=3, mark 9, 12, 15...
                # Python slice assignment for efficiency:
                # is_prime[p*p : n : p] means slice from index p*p up to (but not including) n, with step p.
                # len(...) calculates how many False values are needed for this slice.
                is_prime[p*p : n : p] = [False] * len(is_prime[p*p : n : p])

        # Count the number of primes found
        count = 0
        for i in range(2, n): # Iterate from 2 up to n-1
            if is_prime[i]:
                count += 1
        return count

```

## Complexity Analysis
- **Time Complexity:** $O(N \log \log N)$, where $N$ is the input `n`.
- **Space Complexity:** $O(N)$ for the `is_prime` boolean array.

## 总结 (Summary)
LC204 asks to count primes less than `n`. The Sieve of Eratosthenes is the standard efficient algorithm for this, marking multiples of primes as composite and then counting the remaining unmarked numbers.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
