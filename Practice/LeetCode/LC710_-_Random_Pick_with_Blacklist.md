---
tags: [problem/leetcode, lc/hard, topic/array, topic/hash_table, pattern/randomized_algorithms, course/labuladong]
aliases: [LC710, Blacklist Random Pick]
---

> [!NOTE] Source Annotation
> Problem: LeetCode - Random Pick with Blacklist (LC710)
> Official Link: [https://leetcode.com/problems/random-pick-with-blacklist/](https://leetcode.com/problems/random-pick-with-blacklist/)
> Mentioned in [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/常数时间删除_查找数组中的任意元素.md|Labuladong's Constant Time Operations]].

# LC710: Random Pick with Blacklist

## Problem Statement
*(To be filled from LeetCode problem description)*

## Solution Approach
*(To be filled. Likely involves concepts from [[Interview/Concept/Data Structures/Hash Map/04 - ArrayHashMap - Random Key Access|ArrayHashMap (related remapping)]].)*

### Python Solution (Placeholder)
```python
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None): self.val = val; self.left = left; self.right = right
# class ListNode:
#     def __init__(self, val=0, next=None): self.val = val; self.next = next
class Solution:
    def solve(self, params_): # Signature will vary based on problem
        # TODO: Implement solution
        pass
```

## Complexity Analysis
*(To be filled)*

## 总结 (Summary)
*(To be filled)*

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Data Structures/Hash Map/04 - ArrayHashMap - Random Key Access|ArrayHashMap (related remapping)]]
