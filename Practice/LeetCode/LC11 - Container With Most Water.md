---
tags: [problem/leetcode, lc/medium, topic/array, pattern/two_pointers, course/labuladong, lc/lc11]
aliases: [LC11, Container With Most Water]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/如何高效解决接雨水问题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 11. Container With Most Water
> This problem is simpler than Trapping Rain Water but also uses a two-pointer approach. It's mentioned in the same Labuladong article as [[Interview/Practice/LeetCode/LC42 - Trapping Rain Water|LC42]].

# LeetCode 11: Container With Most Water

## Problem Statement
You are given an integer array `height` of length `n`. There are `n` vertical lines drawn such that the two endpoints of the `i`-th line are `(i, 0)` and `(i, height[i])`.
Find two lines that together with the x-axis form a container, such that the container contains the most water.
Return *the maximum amount of water a container can store*.
Notice that you cannot slant the container.

**Official Link:** [LeetCode 11. Container With Most Water](https://leetcode.com/problems/container-with-most-water/)

## Solution Approach: Two Pointers

The amount of water a container formed by lines at indices `i` and `j` (with `i < j`) can hold is `min(height[i], height[j]) * (j - i)`.
We want to maximize this value.

A brute-force approach would check all pairs, taking $O(N^2)$ time.
A more efficient two-pointer approach works in $O(N)$:
1.  Initialize `left = 0`, `right = len(height) - 1`.
2.  Initialize `max_area = 0`.
3.  Loop while `left < right`:
    a.  Calculate `current_width = right - left`.
    b.  Calculate `current_height = min(height[left], height[right])`.
    c.  `current_area = current_width * current_height`.
    d.  `max_area = max(max_area, current_area)`.
    e.  **Move the pointer pointing to the shorter line inward.** Why?
        - If we move the pointer of the taller line, the width decreases, and the height is still limited by the shorter line (or becomes even smaller if the new line is shorter). So, the area cannot increase.
        - If we move the pointer of the shorter line, the width decreases, but there's a *chance* that the new line encountered is taller, potentially leading to a larger height for the container, which might offset the decrease in width and result in a larger area.

### Python Solution
```python
class Solution:
    def maxArea(self, height: list[int]) -> int:
        left, right = 0, len(height) - 1
        max_area = 0

        while left < right:
            current_width = right - left
            # The height of the container is limited by the shorter line
            current_height = min(height[left], height[right])

            current_area = current_width * current_height
            max_area = max(max_area, current_area)

            # Move the pointer of the shorter line
            if height[left] < height[right]:
                left += 1
            else: # height[left] >= height[right]
                right -= 1

        return max_area
```

## Complexity Analysis
- **Time Complexity:** $O(N)$, as the `left` and `right` pointers each traverse the array at most once.
- **Space Complexity:** $O(1)$.

## 总结 (Summary)
LC11 is solved efficiently using a two-pointer approach. Start with the widest possible container (pointers at both ends). The area is limited by the shorter line. To potentially find a larger area, always move the pointer corresponding to the shorter line inward, as this is the only way the height of the container might increase.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]]
