---
tags: [problem/leetcode, lc/hard, topic/array, topic/sliding_window, pattern/monotonic_queue, course/labuladong]
aliases: [LC239, Sliding Window Maximum]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典队列_栈算法/单调队列结构解决滑动窗口问题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 239. Sliding Window Maximum
> Solution approach adapted from [[Interview/Concept/Data Structures/Queue/Applications/01 - Monotonic Queue Pattern|Monotonic Queue Pattern]].

# LeetCode 239: Sliding Window Maximum

## Problem Statement
You are given an array of integers `nums`, there is a sliding window of size `k` which is moving from the very left of the array to the very right. You can only see the `k` numbers in the window. Each time the sliding window moves right by one position.
Return *the max sliding window*.

**Official Link:** [LeetCode 239. Sliding Window Maximum](https://leetcode.com/problems/sliding-window-maximum/)

## Solution Approach: Monotonic Queue

This problem is a direct application of the [[Interview/Concept/Data Structures/Queue/Applications/01 - Monotonic Queue Pattern|Monotonic Queue pattern]]. We need a queue that can:
1. Add an element to the end (when window slides right).
2. Remove an element from the front (when window slides right and an element leaves from left).
3. Get the maximum element in the current window in $O(1)$ time.

A deque is used to implement the monotonic queue, storing elements (or their indices) in decreasing order.

### Python Solution (using a Monotonic Queue helper class)
```python
import collections

class MonotonicQueueMax:
    def __init__(self):
        self.q = collections.deque() # Stores elements in decreasing order

    def push(self, val: int):
        # Remove elements smaller than val from the rear
        while self.q and val > self.q[-1]:
            self.q.pop()
        self.q.append(val)

    def pop(self, val: int):
        # If val is the current max (front of queue), remove it
        # This check is crucial: only pop if 'val' is indeed the element
        # that was responsible for being the max.
        if self.q and val == self.q[0]:
            self.q.popleft()

    def max(self) -> int:
        if self.q:
            return self.q[0] # Front element is always the max
        # This case should ideally not be hit if window is non-empty
        # and contains numbers. Depending on constraints, might need error handling
        # or a default for truly empty window.
        # For LC239, window always has 'k' elements when we query max.
        return -float('inf') # Should not happen with valid k >= 1

class Solution:
    def maxSlidingWindow(self, nums: list[int], k: int) -> list[int]:
        if not nums or k == 0:
            return []
        if k == 1: # Each element is its own window max
            return nums

        window = MonotonicQueueMax()
        results = []

        # Initialize the first window
        for i in range(k -1): # Push first k-1 elements
            window.push(nums[i])

        # Slide the window
        for i in range(k - 1, len(nums)):
            # Add current element nums[i] to window
            window.push(nums[i])

            # Window has reached size k, record max
            results.append(window.max())

            # Remove element nums[i - k + 1] (which is now leaving window from left)
            element_leaving = nums[i - k + 1]
            window.pop(element_leaving)

        return results

# Example: nums = [1,3,-1,-3,5,3,6,7], k = 3
# i=0 (1): window.push(1). q=[1]
# i=1 (3): window.push(3). q=[3] (1 popped)
# i=2 (-1): window.push(-1). q=[3,-1]. results.append(window.max()=3). leaving=nums[0]=1. window.pop(1). q=[3,-1]
# i=3 (-3): window.push(-3). q=[3,-1,-3]. results.append(window.max()=3). leaving=nums[1]=3. window.pop(3). q=[-1,-3]
# ...
```

## Complexity Analysis
- **Time Complexity:** $O(N)$, where $N$ is the length of `nums`. Each element is pushed onto and popped from the monotonic queue at most once.
- **Space Complexity:** $O(K)$ for storing elements in the monotonic queue.

## 总结 (Summary)
- LC239 is a classic sliding window problem efficiently solved using a monotonic queue.
- The monotonic queue maintains elements within the current window in decreasing order, allowing $O(1)$ retrieval of the maximum.
- The overall algorithm processes the array in a single pass.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
