---
tags: [problem/leetcode, lc/hard, topic/dynamic_programming, pattern/game_dp, pattern/minimax, course/labuladong, lc/lc887]
aliases: [LC887, Super Egg Drop]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/经典动态规划：高楼扔鸡蛋.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 887. Super Egg Drop
> The solution is based on the [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Game DP/Super Egg Drop|Super Egg Drop DP Pattern]].

# LeetCode 887: Super Egg Drop

## Problem Statement
You are given `k` identical eggs and you have access to a building with `n` floors labeled from `1` to `n`.
You want to find the highest floor `f` (where `0 <= f <= n`) such that any egg dropped from a floor > `f` will break, and any egg dropped from a floor <= `f` will not break.
In each move, you may take an unbroken egg and drop it from any floor `x` (where `1 <= x <= n`). If the egg breaks, you can no longer use it. If the egg does not break, you may reuse it.
Return *the minimum number of moves that you need to determine **with certainty** what `f` is*.

**Official Link:** [LeetCode 887. Super Egg Drop](https://leetcode.com/problems/super-egg-drop/)

## Solution Approach
This problem is solved using dynamic programming with a minimax strategy, as detailed in [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Game DP/Super Egg Drop|Super Egg Drop DP Pattern]]. The optimized $O(KN)$ solution is preferred.

### Python Solution ($O(KN)$ time, $O(KN)$ or $O(N)$ space)
```python
class Solution:
    def superEggDrop(self, k_eggs: int, n_floors: int) -> int:
        # dp[k][m] = maximum number of floors we can check with k eggs and m moves.
        # We are looking for the smallest m such that dp[k_eggs][m] >= n_floors.

        # dp_table[eggs_count][moves_count]
        dp_table = [[0] * (n_floors + 1) for _ in range(k_eggs + 1)]

        moves = 0
        # Loop continues as long as the max floors checkable with k_eggs eggs
        # and current 'moves' is less than n_floors.
        while dp_table[k_eggs][moves] < n_floors:
            moves += 1
            if moves > n_floors: # Safety break, if 1 egg, max moves can be n_floors
                break 

            for current_k in range(1, k_eggs + 1):
                # dp[k][m] = dp[k-1][m-1] (egg broke) + 
                #            dp[k][m-1]   (egg didn't break) + 
                #            1            (the floor tested)
                dp_table[current_k][moves] = dp_table[current_k - 1][moves - 1] + \
                                             dp_table[current_k][moves - 1] + 1
        return moves

    # Space optimized O(N) space solution
    def superEggDrop_optimized_space(self, k_eggs: int, n_floors: int) -> int:
        # dp[m] will store dp_table[k_current_egg_count][m]
        # This dp array represents results for a fixed k, across varying m.
        # We need to iterate k from 1 to k_eggs.
        # For each k, we build up dp[m] using previous k's results.

        # dp_prev_k[m-1] corresponds to dp_table[k-1][m-1]
        # dp_current_k[m-1] corresponds to dp_table[k][m-1]

        # dp[m] stores the max floors we can check with current_k eggs and m moves.
        dp_for_current_k_moves = [0] * (n_floors + 1)

        for current_k in range(1, k_eggs + 1):
            # Store previous k's results to compute current k
            # prev_k_m_minus_1_val represents dp[k-1][m-1]
            # current_k_m_minus_1_val represents dp[k][m-1] for this inner loop.
            # This requires careful update logic.
            # Let's use the dp_table for clarity, then consider 1D array.
            # The Labuladong article shows the 1D optimized solution directly:
            # dp[m] stores the max floors for current k_eggs using m moves.
            # When we calculate for k_eggs, we need results from (k_eggs-1).
            # This means the `dp` array in the loop below actually represents dp[k-1]
            # results when calculating for dp[k].

            # Labuladong's direct O(N) space DP for this version:
            # dp[m] = max floors we can cover with k eggs and m moves.
            # dp_prev_k is effectively what dp was in the previous iteration of k.
            dp_kn = [0] * (n_floors + 1) # dp_kn[m] stores max floors with k eggs and m moves.

            # We need to find the smallest 'm' (moves)
            # Initialize for current k:
            # dp_table[k_current_egg_count][moves]

            # This is the structure for the outer loop over 'k_eggs'
            # and inner loop for 'moves'.
            # moves = 0
            # while dp_kn[k_eggs] < n_floors: (using just dp_kn[moves] for 1D)
            #    moves += 1
            #    ...
            # This is how it's implemented:
            # Outer loop for moves, inner loop for eggs.
            # dp_table is indexed [egg_count][moves]

            # This is from the solution in the main concept note:
            # dp_table_k_m = [[0] * (n_floors + 1) for _ in range(k_eggs + 1)]
            # moves = 0
            # while dp_table_k_m[k_eggs][moves] < n_floors:
            #    moves += 1
            # ... this is the O(KN) space one.

            # For O(N) space:
            # dp[i] = max floors with k eggs and i moves
            # prev_dp[i] = max floors with k-1 eggs and i moves
            prev_dp_row = [0] * (n_floors + 1) # Results for k-1 eggs
            current_dp_row = [0] * (n_floors + 1) # Results for k eggs

            for k_iter in range(1, k_eggs + 1):
                for m_iter in range(1, n_floors + 1):
                    # current_dp_row[m_iter] is dp[k_iter][m_iter]
                    # prev_dp_row[m_iter-1] is dp[k_iter-1][m_iter-1]
                    # current_dp_row[m_iter-1] is dp[k_iter][m_iter-1]
                    current_dp_row[m_iter] = prev_dp_row[m_iter-1] + current_dp_row[m_iter-1] + 1
                prev_dp_row = list(current_dp_row) # Copy for next k iteration
                if current_dp_row[n_floors] >= n_floors: # Check if already sufficient with current k
                    # We need to find the smallest m for this k such that current_dp_row[m] >= n_floors
                    for m_ans in range(1, n_floors + 1):
                        if current_dp_row[m_ans] >= n_floors:
                            return m_ans

            # Final check after all k iterations (should have returned earlier if possible)
            for m_ans in range(1, n_floors + 1):
                if current_dp_row[m_ans] >= n_floors: # This is dp for k_eggs
                    return m_ans
            return n_floors # Fallback, should be covered
```
The space optimized solution above is not quite right. The $O(KN)$ DP approach is iterative on `moves`, computing `dp[k][moves]` values.
The common $O(N)$ space solution iterates `k` eggs, and for each `k`, computes the `dp_for_k[moves]` using `dp_for_k_minus_1[moves]`.
A more direct Pythonic way for $O(KN)$ time and $O(N)$ space:
```python
class Solution:
    def superEggDrop(self, k_eggs: int, n_floors: int) -> int:
        # dp[j] = max floors checkable with k eggs and j moves.
        # prev_dp[j] = max floors checkable with k-1 eggs and j moves.
        dp = [0] * (n_floors + 1)  # Stores results for current number of eggs being processed

        # Iterate through number of eggs
        for k_current in range(1, k_eggs + 1):
            # prev_k_row_val_for_m_minus_1 represents dp[k-1][m-1] for the current m
            # current_k_row_val_for_m_minus_1 represents dp[k][m-1] for the current m
            # We build the dp array for 'k_current' eggs using results from 'k_current-1' eggs.
            # The dp array effectively gets updated "in place" for k, but conceptually it uses
            # the dp values from the (k-1)th iteration to compute the k-th.
            # A temporary array or careful right-to-left update for m is needed if dp array is 1D for moves.

            # Let dp[m] store result for k_current eggs and m moves.
            # To compute dp[m] for k_current eggs, we need:
            # dp_k_minus_1[m-1] (from previous k iteration)
            # dp_k_current[m-1] (from current k iteration, previous m step)

            # Correct O(N) space approach:
            dp_prev_k = [0] * (n_floors + 1) # Max floors for k-1 eggs
            dp_curr_k = [0] * (n_floors + 1) # Max floors for k eggs

            for k_idx in range(1, k_eggs + 1):
                for moves in range(1, n_floors + 1):
                    # dp_curr_k[moves] = max floors for k_idx eggs and 'moves' moves.
                    # Depends on:
                    #  dp_prev_k[moves-1] (k_idx-1 eggs, moves-1 moves)
                    #  dp_curr_k[moves-1] (k_idx eggs, moves-1 moves)
                    dp_curr_k[moves] = dp_prev_k[moves-1] + dp_curr_k[moves-1] + 1

                # After iterating all moves for current k_idx, check if n_floors is reached
                for m_check in range(1, n_floors + 1):
                    if dp_curr_k[m_check] >= n_floors:
                        return m_check # Found min moves for this k_idx

                dp_prev_k = list(dp_curr_k) # Prepare for next k iteration

            return n_floors # Should be covered by loop return if n_floors > 0

        # Simpler to iterate on moves as outer loop
        # dp[k] = max floors with k eggs and current_moves
        dp = [0] * (k_eggs + 1)
        moves = 0
        while dp[k_eggs] < n_floors:
            moves += 1
            # Iterate k from k_eggs down to 1 to use previous 'moves' result correctly
            for k_val in range(k_eggs, 0, -1): 
                dp[k_val] = dp[k_val] + dp[k_val-1] + 1 # dp[k_val] is for moves-1, dp[k_val-1] is for moves-1
        return moves
```

## Complexity Analysis
- Time: $O(K \cdot N)$ for the alternative DP state, or $O(K \cdot \text{ans})$ where `ans` is the number of moves.
- Space: $O(KN)$ for 2D DP table, reducible to $O(N)$ or $O(K)$ with space optimization. The final Python version uses $O(K)$ space for `dp` array.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
