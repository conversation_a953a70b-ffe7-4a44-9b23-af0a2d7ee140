---
tags: [problem/leetcode, lc/easy, topic/linked_list, topic/two_pointers, pattern/fast_slow_pointers]
aliases: [LC83, LeetCode 83. Remove Duplicates from Sorted List, 删除排序链表中的重复元素]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 83. Remove Duplicates from Sorted List
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道数组题目.md]] (though this is a list problem, the principle is analogous).

# LeetCode 83: Remove Duplicates from Sorted List

## Problem Statement

Given the `head` of a sorted linked list, *delete all duplicates such that each element appears only once*. Return *the linked list **sorted** as well*.

**Official Link:** [LeetCode 83. Remove Duplicates from Sorted List](https://leetcode.com/problems/remove-duplicates-from-sorted-list/)

## Solution Approach: Fast-Slow Pointers

This problem is very similar to [[Interview/Practice/LeetCode/LC26 - Remove Duplicates from Sorted Array|LC26 Remove Duplicates from Sorted Array]], but for a linked list. We use a fast and slow pointer.
- `slow` pointer: Points to the last unique node found so far in the modified list.
- `fast` pointer: Iterates through the original list to find the next unique node.

1. Initialize `slow = head` and `fast = head`.
2. While `fast` is not `None`:
   a. If `fast.val != slow.val`:
      i. This means `fast` has found a new unique element.
      ii. We need to connect `slow` to this new unique element: `slow.next = fast`.
      iii. Advance `slow` to this new unique element: `slow = slow.next`.
   b. Advance `fast`: `fast = fast.next`.
3. After the loop, the `slow` pointer is at the last unique node. Its `next` pointer might still be pointing to subsequent duplicate nodes from the original list. So, set `slow.next = None` to terminate the list correctly.
4. Return `head`.

### Python Solution (Labuladong's version)
```python
# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, val=0, next=None):
#         self.val = val
#         self.next = next

class Solution:
    def deleteDuplicates(self, head: [ListNode]) -> [ListNode]:
        if not head:
            return None

        slow, fast = head, head

        while fast is not None:
            if fast.val != slow.val:
                # nums[slow] = nums[fast]; (array analogy)
                slow.next = fast
                # slow++; (array analogy)
                slow = slow.next
            # fast++ (array analogy)
            fast = fast.next

        # Disconnect from further duplicate elements
        slow.next = None
        return head
```
Labuladong's visualizer `div_remove-duplicates-from-sorted-list` helps illustrate this.

## Complexity Analysis
- **Time Complexity:** $O(N)$, where $N$ is the number of nodes in the list. Both `slow` and `fast` traverse the list at most once.
- **Space Complexity:** $O(1)$, as we are modifying the list in-place using only a few pointers.

## Visualization

List: `1 -> 1 -> 2 -> 3 -> 3 -> None`

1.  `slow=Node(1)`, `fast=Node(1)` (first one)
2.  `fast` moves to `Node(1)` (second one). `fast.val == slow.val`. `fast` moves to `Node(2)`.
    - `slow=Node(1)`, `fast=Node(2)`
3.  `fast.val (2) != slow.val (1)`.
    - `slow.next = fast` (first `Node(1).next` now points to `Node(2)`).
    - `slow = slow.next` (`slow` becomes `Node(2)`).
    - `fast` moves to `Node(3)`.
    - `slow=Node(2)`, `fast=Node(3)`
4.  `fast.val (3) != slow.val (2)`.
    - `slow.next = fast` (`Node(2).next` points to `Node(3)`).
    - `slow = slow.next` (`slow` becomes `Node(3)`).
    - `fast` moves to `Node(3)` (second one).
    - `slow=Node(3)`, `fast=Node(3)` (second one)
5.  `fast.val (3) == slow.val (3)`. `fast` moves to `None`.
    - `slow=Node(3)`, `fast=None`. Loop terminates.
6.  `slow.next = None` (`Node(3).next` becomes `None`).

Result: `1 -> 2 -> 3 -> None`.

## 总结 (Summary)
- LC83 adapts the fast-slow pointer technique from arrays to sorted linked lists for duplicate removal.
- `slow` maintains the tail of the list of unique elements, while `fast` scouts for the next unique element.
- A final `slow.next = None` is crucial to correctly terminate the processed list.
- The solution is $O(N)$ time and $O(1)$ space.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]]
