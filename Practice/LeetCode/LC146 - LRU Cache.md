---
tags: [problem/leetcode, lc/medium, topic/data_structure_design, pattern/lru, course/labuladong]
aliases: [LC146, LRU Cache Problem]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 146. LRU Cache
> Discussed in [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/算法就像搭乐高：手撸 LRU 算法.md|算法就像搭乐高：手撸 LRU 算法 by Labuladong]].

# LeetCode 146: LRU Cache

## Problem Statement
Design a data structure that follows the constraints of a **Least Recently Used (LRU) cache**.

Implement the `LRUCache` class:
- `LRUCache(int capacity)` Initialize the LRU cache with positive size `capacity`.
- `int get(int key)` Return the value of the `key` if the key exists, otherwise return -1.
- `void put(int key, int value)` Update the value of the `key` if the `key` exists. Otherwise, add the `key-value` pair to the cache. If the number of keys exceeds the `capacity` from this operation, evict the least recently used key.

The functions `get` and `put` must each run in $O(1)$ average time complexity.

**Official Link:** [LeetCode 146. LRU Cache](https://leetcode.com/problems/lru-cache/)

## Solution Approach
This problem is a direct application of the [[Interview/Concept/Algorithms/Caching/LRU Cache|LRU Cache Algorithm]]. It requires combining a hash map for $O(1)$ key lookups and a doubly linked list to maintain the order of recency and allow $O(1)$ additions/removals from either end and of specific nodes.

### Python Solution
(Adapted from the concept note [[Interview/Concept/Algorithms/Caching/LRU Cache|LRU Cache Algorithm]])

```python
class Node:
    def __init__(self, key: int, val: int):
        self.key = key
        self.val = val
        self.next = None
        self.prev = None

class DoubleList:
    def __init__(self):
        self.head = Node(0, 0) # Dummy head
        self.tail = Node(0, 0) # Dummy tail
        self.head.next = self.tail
        self.tail.prev = self.head
        self._size = 0

    def addLast(self, x: Node):
        x.prev = self.tail.prev
        x.next = self.tail
        self.tail.prev.next = x
        self.tail.prev = x
        self._size += 1

    def remove(self, x: Node):
        x.prev.next = x.next
        x.next.prev = x.prev
        self._size -= 1

    def removeFirst(self) -> Node | None:
        if self.head.next == self.tail:
            return None
        first = self.head.next
        self.remove(first)
        return first

    def size(self) -> int:
        return self._size

class LRUCache:
    def __init__(self, capacity: int):
        self.cap = capacity
        self.map = {}  # key -> Node
        self.cache = DoubleList()

    def _make_recently(self, key: int):
        node = self.map[key]
        self.cache.remove(node)
        self.cache.addLast(node)

    def _add_recently(self, key: int, val: int):
        new_node = Node(key, val)
        self.cache.addLast(new_node)
        self.map[key] = new_node

    def _delete_key(self, key: int):
        node_to_delete = self.map[key]
        self.cache.remove(node_to_delete)
        del self.map[key]

    def _remove_least_recently(self):
        deleted_node = self.cache.removeFirst()
        if deleted_node:
            del self.map[deleted_node.key]

    def get(self, key: int) -> int:
        if key not in self.map:
            return -1
        self._make_recently(key)
        return self.map[key].val

    def put(self, key: int, value: int) -> None:
        if key in self.map:
            self._delete_key(key)
            self._add_recently(key, value)
            return

        if self.cache.size() >= self.cap:
            self._remove_least_recently()

        self._add_recently(key, value)

# Your LRUCache object will be instantiated and called as such:
# obj = LRUCache(capacity)
# param_1 = obj.get(key)
# obj.put(key,value)
```

## Complexity Analysis
- **`get(key)`:** $O(1)$ average.
- **`put(key, value)`:** $O(1)$ average.
- **Space Complexity:** $O(capacity)$.

## 总结 (Summary)
LC146 is a classic data structure design problem requiring an LRU cache. The optimal solution uses a hash map and a doubly linked list to achieve $O(1)$ average time complexity for `get` and `put` operations.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
