---
tags: [problem/leetcode, lc/medium, topic/data_structure_design, pattern/lru_cache, pattern/hybrid_data_structure]
aliases: [LC146, LeetCode 146, LRU Cache, 最近最少使用缓存]
---

# LeetCode 146: LRU Cache

## Problem Statement

Design a data structure that follows the constraints of a **Least Recently Used (LRU) cache**.

Implement the `LRUCache` class:
- `LRUCache(int capacity)` Initialize the LRU cache with positive size `capacity`.
- `int get(int key)` Return the value of the `key` if the key exists, otherwise return -1.
- `void put(int key, int value)` Update the value of the `key` if the `key` exists. Otherwise, add the `key-value` pair to the cache. If the number of keys exceeds the `capacity` from this operation, evict the least recently used key.

The functions `get` and `put` must each run in **O(1)** average time complexity.

**Official Link:** [LeetCode 146. LRU Cache](https://leetcode.com/problems/lru-cache/)

## 🧠 Understanding LRU Cache: From Real Life to Algorithm

### The Real-World Problem

Imagine your desk has limited space, but you need to keep important documents handy:

```tikz
\begin{tikzpicture}[
    desk_box/.style={rectangle, draw, minimum width=8cm, minimum height=2cm, fill=brown!20},
    doc_box/.style={rectangle, draw, minimum width=1.2cm, minimum height=1.5cm, font=\tiny, fill=blue!30},
    recent_doc/.style={doc_box, fill=green!40},
    old_doc/.style={doc_box, fill=red!30},
    arrow/.style={->, thick, red},
    insight_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=4cm, align=center}
]

% Desk representation
\node[desk_box] at (4, 2) {};
\node at (4, 3.2) {\bfseries Your Desk (Limited Space)};

% Documents on desk
\node[recent_doc] at (1, 2) {Doc A\\(just used)};
\node[doc_box] at (2.5, 2) {Doc B};
\node[doc_box] at (4, 2) {Doc C};
\node[old_doc] at (5.5, 2) {Doc D\\(oldest)};
\node[doc_box] at (7, 2) {Doc E};

% New document coming
\node[doc_box, fill=purple!40] at (9, 2) {Doc F\\(new!)};
\draw[arrow] (8.5, 2) -- (7.8, 2);

\node[insight_box] at (4, 0.5) {
    \textbf{Problem:}\\
    Desk is full!\\
    Which document\\
    should you remove?
};

\node[insight_box] at (9, 0.5) {
    \textbf{LRU Strategy:}\\
    Remove the document\\
    you haven't used\\
    for the longest time
};

\end{tikzpicture}
```

### The Core Challenge: Speed vs. Space

```tikz
\begin{tikzpicture}[
    challenge_box/.style={rectangle, draw, rounded corners, fill=red!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2cm},
    solution_box/.style={rectangle, draw, rounded corners, fill=green!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2cm},
    arrow/.style={->, thick, purple, line width=2pt}
]

\node[challenge_box] (challenge) at (0, 2) {
    \textbf{The Challenge:}\\[0.5em]
    "Need O(1) for both:\\
    • Finding items (get)\\
    • Updating recency (put)"\\[0.5em]
    Single data structure\\
    can't do both efficiently!
};

\node[solution_box] (solution) at (8, 2) {
    \textbf{The Insight:}\\[0.5em]
    "Combine two data structures:\\
    • Hash table for O(1) lookup\\
    • Linked list for O(1) ordering"\\[0.5em]
    Hybrid = Best of both!
};

\draw[arrow] (challenge) -- (solution);

\node at (4, 0.5) {\bfseries The constraint forces us to think creatively about data structure design!};

\end{tikzpicture}
```

### Why This Problem Matters

This isn't just about caching - it's about **hybrid data structure design**:
- **Performance Requirements**: Understanding when O(1) is truly necessary
- **Data Structure Composition**: Combining structures for complementary strengths
- **Real-World Applications**: CPU caches, web browsers, database buffers
- **Design Trade-offs**: Space complexity vs. time complexity decisions

## 🔍 Progressive Problem Analysis: Building the Solution

### Step 1: Understanding the Operations

Let's break down what each operation needs to do:

```tikz
\begin{tikzpicture}[
    operation_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2.5cm},
    requirement_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=3.5cm, align=center}
]

\node[operation_box] (get_op) at (0, 3) {
    \textbf{get(key) Operation:}\\[0.5em]
    1. Check if key exists\\
    2. If exists: return value\\
    3. Mark as "recently used"\\
    4. If not: return -1\\[0.5em]
    \textit{Must be O(1)!}
};

\node[operation_box] (put_op) at (8, 3) {
    \textbf{put(key, value) Operation:}\\[0.5em]
    1. If key exists: update value\\
    2. Mark as "recently used"\\
    3. If new key and cache full:\\
       evict least recently used\\
    4. Add new key-value\\[0.5em]
    \textit{Must be O(1)!}
};

\node[requirement_box] at (0, 0.5) {
    \textbf{Requirements:}\\
    • Fast lookup\\
    • Track recency\\
    • Update recency
};

\node[requirement_box] at (8, 0.5) {
    \textbf{Requirements:}\\
    • Fast lookup\\
    • Fast insertion\\
    • Fast eviction\\
    • Update recency
};

\end{tikzpicture}
```

### Step 2: Why Single Data Structures Fail

Let's see why common data structures can't handle all requirements:

```tikz
\begin{tikzpicture}[
    structure_box/.style={rectangle, draw, rounded corners, fill=red!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=2cm},
    problem_box/.style={rectangle, draw, fill=orange!20, font=\tiny, text width=3cm, align=center}
]

\node[structure_box] (array) at (0, 4) {
    \textbf{Array/List:}\\[0.5em]
    ✅ O(1) access by index\\
    ❌ O(n) search by key\\
    ❌ O(n) insertion/deletion\\
    ❌ O(n) reordering
};

\node[structure_box] (hash) at (4.5, 4) {
    \textbf{Hash Table:}\\[0.5em]
    ✅ O(1) lookup by key\\
    ✅ O(1) insertion\\
    ❌ No ordering concept\\
    ❌ Can't track recency
};

\node[structure_box] (linked) at (9, 4) {
    \textbf{Linked List:}\\[0.5em]
    ✅ O(1) insertion/deletion\\
    ✅ Natural ordering\\
    ❌ O(n) search by key\\
    ❌ O(n) random access
};

\node[problem_box] at (0, 2) {
    Can't find items\\
    quickly by key
};

\node[problem_box] at (4.5, 2) {
    Can't maintain\\
    order of usage
};

\node[problem_box] at (9, 2) {
    Can't find items\\
    quickly by key
};

\node at (4.5, 0.5) {\bfseries Each structure is missing something crucial!};

\end{tikzpicture}
```

### Step 3: The Hybrid Solution Insight

The breakthrough is combining data structures for complementary strengths:

```tikz
\begin{tikzpicture}[
    hybrid_box/.style={rectangle, draw, rounded corners, fill=green!20, font=\sffamily\small, text width=5cm, align=center, minimum height=3cm},
    component_box/.style={rectangle, draw, fill=blue!20, font=\sffamily\small, text width=3.5cm, align=center},
    arrow/.style={->, thick, purple, line width=2pt}
]

\node[component_box] (hash_component) at (0, 4) {
    \textbf{Hash Table Component:}\\[0.5em]
    • Maps key → list node\\
    • O(1) lookup by key\\
    • O(1) existence check
};

\node[component_box] (list_component) at (8, 4) {
    \textbf{Doubly Linked List:}\\[0.5em]
    • Maintains usage order\\
    • O(1) move to front\\
    • O(1) remove from anywhere\\
    • O(1) add/remove at ends
};

\node[hybrid_box] (solution) at (4, 1) {
    \textbf{Hybrid LRU Cache:}\\[0.5em]
    Hash table points to linked list nodes\\[0.3em]
    ✅ O(1) lookup (hash table)\\
    ✅ O(1) reordering (linked list)\\
    ✅ O(1) eviction (remove from tail)\\
    ✅ O(1) promotion (move to head)\\[0.3em]
    \textit{Best of both worlds!}
};

\draw[arrow] (hash_component) -- (solution);
\draw[arrow] (list_component) -- (solution);

\end{tikzpicture}
```

## 🏗️ Building the Solution: Step by Step

### Step 1: Design the Node Structure

First, we need nodes that can be efficiently linked and unlinked:

```python
class Node:
    """
    Doubly linked list node that stores key-value pairs.

    Why store both key and value?
    - Value: obvious (what we're caching)
    - Key: needed when we evict a node to remove it from hash table
    """
    def __init__(self, key: int, val: int):
        self.key = key
        self.val = val
        self.next = None  # Points to more recently used node
        self.prev = None  # Points to less recently used node
```

### Step 2: Create the Doubly Linked List

The linked list maintains the order of recency:

```python
class DoublyLinkedList:
    """
    Doubly linked list with dummy head and tail for easier operations.

    Structure: dummy_head ↔ [most recent] ↔ ... ↔ [least recent] ↔ dummy_tail

    Why dummy nodes?
    - Eliminates edge cases when adding/removing at ends
    - Makes code cleaner and less error-prone
    """
    def __init__(self):
        # Dummy nodes simplify edge case handling
        self.head = Node(0, 0)  # Dummy head (most recent side)
        self.tail = Node(0, 0)  # Dummy tail (least recent side)
        self.head.next = self.tail
        self.tail.prev = self.head
        self._size = 0

    def add_to_head(self, node: Node):
        """Add node right after dummy head (mark as most recently used)"""
        node.prev = self.head
        node.next = self.head.next
        self.head.next.prev = node
        self.head.next = node
        self._size += 1

    def remove_node(self, node: Node):
        """Remove node from anywhere in the list"""
        node.prev.next = node.next
        node.next.prev = node.prev
        self._size -= 1

    def remove_from_tail(self) -> Node:
        """Remove and return the least recently used node"""
        if self._size == 0:
            return None

        last_node = self.tail.prev
        self.remove_node(last_node)
        return last_node

    def move_to_head(self, node: Node):
        """Move existing node to head (mark as most recently used)"""
        self.remove_node(node)
        self.add_to_head(node)

    def size(self) -> int:
        return self._size
```

### Step 3: Implement the LRU Cache

Now we combine hash table and linked list:

```python
class LRUCache:
    """
    LRU Cache using hash table + doubly linked list hybrid approach.

    Key insight: Hash table provides O(1) lookup, linked list provides O(1) ordering.
    """

    def __init__(self, capacity: int):
        self.capacity = capacity
        self.cache = {}  # key -> Node (hash table for O(1) lookup)
        self.dll = DoublyLinkedList()  # maintains recency order

    def get(self, key: int) -> int:
        """
        Get value by key and mark as recently used.

        Algorithm:
        1. Check if key exists in hash table
        2. If exists: move to head (mark as recent) and return value
        3. If not exists: return -1
        """
        if key in self.cache:
            node = self.cache[key]
            # Mark as recently used by moving to head
            self.dll.move_to_head(node)
            return node.val
        return -1

    def put(self, key: int, value: int) -> None:
        """
        Put key-value pair and mark as recently used.

        Algorithm:
        1. If key exists: update value and move to head
        2. If key is new:
           a. If cache is full: evict least recently used
           b. Add new node to head and hash table
        """
        if key in self.cache:
            # Update existing key
            node = self.cache[key]
            node.val = value
            self.dll.move_to_head(node)
        else:
            # Add new key
            new_node = Node(key, value)

            if self.dll.size() >= self.capacity:
                # Cache is full, evict least recently used
                lru_node = self.dll.remove_from_tail()
                del self.cache[lru_node.key]  # Remove from hash table

            # Add new node
            self.dll.add_to_head(new_node)
            self.cache[key] = new_node
```

## 🔍 Visual Algorithm Trace

Let's trace through operations on an LRU cache with capacity 3:

```tikz
\begin{tikzpicture}[
    node_box/.style={rectangle, draw, minimum width=1.2cm, minimum height=0.8cm, font=\tiny, fill=blue!30},
    recent_node/.style={node_box, fill=green!40},
    old_node/.style={node_box, fill=red!30},
    hash_box/.style={rectangle, draw, fill=yellow!20, font=\tiny, text width=2cm, align=center},
    operation_box/.style={rectangle, draw, fill=purple!20, font=\tiny, text width=3cm, align=center},
    arrow/.style={->, thick, blue}
]

% Operation 1: put(1, "A")
\node at (6, 8) {\bfseries Operation Trace: LRU Cache (capacity=3)};

\node[operation_box] at (0, 7) {
    \textbf{put(1, "A")}\\
    Cache empty,\\
    add to head
};

\node at (3, 7.5) {\tiny Linked List:};
\node[recent_node] at (3, 7) {1:A};
\node at (4.5, 7) {\tiny (head)};

\node[hash_box] at (7, 7) {
    \textbf{Hash Table:}\\
    1 → node(1,A)
};

% Operation 2: put(2, "B")
\node[operation_box] at (0, 5.5) {
    \textbf{put(2, "B")}\\
    Add new node\\
    to head
};

\node at (3, 6) {\tiny Linked List:};
\node[recent_node] at (3, 5.5) {2:B};
\draw[arrow] (3.6, 5.5) -- (4.4, 5.5);
\node[node_box] at (5, 5.5) {1:A};
\node at (3, 5.2) {\tiny (head)};
\node at (5, 5.2) {\tiny (tail)};

\node[hash_box] at (7, 5.5) {
    \textbf{Hash Table:}\\
    1 → node(1,A)\\
    2 → node(2,B)
};

% Operation 3: get(1)
\node[operation_box] at (0, 4) {
    \textbf{get(1)}\\
    Move node 1\\
    to head\\
    Return "A"
};

\node at (3, 4.5) {\tiny Linked List:};
\node[recent_node] at (3, 4) {1:A};
\draw[arrow] (3.6, 4) -- (4.4, 4);
\node[node_box] at (5, 4) {2:B};
\node at (3, 3.7) {\tiny (head)};
\node at (5, 3.7) {\tiny (tail)};

\node[hash_box] at (7, 4) {
    \textbf{Hash Table:}\\
    1 → node(1,A)\\
    2 → node(2,B)\\[0.2em]
    \textit{Pointers updated!}
};

% Operation 4: put(3, "C") - cache full
\node[operation_box] at (0, 2.5) {
    \textbf{put(3, "C")}\\
    Cache full!\\
    Evict LRU (2:B)\\
    Add 3:C to head
};

\node at (3, 3) {\tiny Linked List:};
\node[recent_node] at (2.5, 2.5) {3:C};
\draw[arrow] (3.1, 2.5) -- (3.9, 2.5);
\node[node_box] at (4.5, 2.5) {1:A};
\node at (2.5, 2.2) {\tiny (head)};
\node at (4.5, 2.2) {\tiny (tail)};

\node[hash_box] at (7, 2.5) {
    \textbf{Hash Table:}\\
    1 → node(1,A)\\
    3 → node(3,C)\\[0.2em]
    \textit{Key 2 removed!}
};

% Final state
\node[operation_box, fill=green!30] at (3.5, 1) {
    \textbf{Final State:}\\
    Most recent: 3:C → 1:A (least recent)\\
    Hash table maps keys to list nodes for O(1) access
};

\end{tikzpicture}
```

**Key Observations:**
1. **Hash table** provides instant access to any node
2. **Linked list** maintains recency order automatically
3. **Move to head** operation marks items as recently used
4. **Remove from tail** evicts least recently used items
5. **Both structures** stay synchronized through careful pointer management

## 🧠 Key Takeaways & Design Principles

### Why This Problem is Educational

```tikz
\begin{tikzpicture}[
    principle_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=1.8cm},
    arrow/.style={->, thick, green}
]

\node[principle_box] (hybrid) at (0, 3) {
    \textbf{Hybrid Design}\\[0.3em]
    Combine data structures\\
    for complementary\\
    strengths and cover\\
    each other's weaknesses
};

\node[principle_box] (performance) at (4.5, 3) {
    \textbf{Performance Engineering}\\[0.3em]
    Understand when O(1)\\
    is truly necessary\\
    and design accordingly
};

\node[principle_box] (real_world) at (9, 3) {
    \textbf{Real-World Impact}\\[0.3em]
    Caching is everywhere:\\
    CPU, web browsers,\\
    databases, CDNs
};

\draw[arrow] (hybrid) -- (performance);
\draw[arrow] (performance) -- (real_world);

\node at (4.5, 1.5) {\bfseries Core Learning: Design → Performance → Application};

\end{tikzpicture}
```

### Design Principles: [[Hybrid Data Structures]]

This problem exemplifies **hybrid data structure design**:

1. **Identify limitations**: Single structures can't meet all requirements
2. **Combine strengths**: Use each structure for what it does best
3. **Maintain synchronization**: Keep both structures consistent
4. **Optimize operations**: Design for the most frequent use cases

### Complexity Analysis
- **Time Complexity:** O(1) for both `get` and `put` operations
- **Space Complexity:** O(capacity) for storing cache entries

### Related Concepts for Obsidian

This problem connects to several important concepts:

- **[[Hash Tables]]**: Fast key-based lookup
- **[[Doubly Linked Lists]]**: Efficient insertion/deletion anywhere
- **[[Caching Strategies]]**: LRU vs LFU vs other eviction policies
- **[[Data Structure Design]]**: Combining structures for hybrid solutions
- **[[System Design]]**: Caching in distributed systems

### Related Problems
- **LC460. LFU Cache**: Least Frequently Used eviction policy
- **LC432. All O(1) Data Structure**: Multiple operations in O(1)
- **LC380. Insert Delete GetRandom O(1)**: Hybrid structure for random access
- **LC381. Insert Delete GetRandom O(1) - Duplicates allowed**: Variant with duplicates

### Implementation Tips

#### Common Optimizations
- **Dummy nodes**: Eliminate edge cases in linked list operations
- **Direct node references**: Hash table points directly to list nodes
- **Bidirectional updates**: Always update both structures together
- **Size tracking**: Maintain size counter for efficient capacity checks

#### Edge Cases to Consider
- **Empty cache**: Handle gets on empty cache
- **Single capacity**: Cache with capacity 1
- **Duplicate puts**: Updating existing keys
- **Get after eviction**: Accessing evicted keys
- **Capacity changes**: Dynamic capacity adjustment (advanced)

#### Alternative Approaches
- **OrderedDict (Python)**: Built-in LRU-like behavior
- **LinkedHashMap (Java)**: Similar built-in functionality
- **Custom implementations**: When you need specific optimizations

### Real-World Applications

#### CPU Caches
- **L1/L2/L3 caches**: Hardware implements LRU-like policies
- **Page replacement**: Operating systems use LRU for virtual memory
- **Translation lookaside buffers**: CPU caches for address translation

#### Software Systems
- **Web browsers**: Cache web pages and resources
- **Database buffers**: Cache frequently accessed data pages
- **CDNs**: Cache content closer to users
- **Application caches**: Redis, Memcached use LRU eviction

This problem beautifully demonstrates how **thoughtful data structure design** can achieve seemingly impossible performance requirements through **creative combination** of existing structures!
