---
tags: [problem/leetcode, lc/medium, topic/dynamic_programming, lc/lc712, course/labuladong_mention]
aliases: [LC712, LeetCode 712]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/子序列类型问题/经典动态规划：最长公共子序列.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 712. Minimum ASCII Delete Sum for Two Strings
> Mentioned in Labuladong's notes, related to [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/01 - Longest Common Subsequence (LCS)|LCS Algorithm (Variation)]].

# LeetCode 712: Minimum ASCII Delete Sum for Two Strings

## Problem Statement
(To be filled from LeetCode - Visit [https://leetcode.com/problems/minimum-ascii-delete-sum-for-two-strings/](https://leetcode.com/problems/minimum-ascii-delete-sum-for-two-strings/))
*Note: Auto-generated URL might be incorrect if title doesn't match LC slug.*

## Solution Approach
(To be filled, based on [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/01 - Longest Common Subsequence (LCS)|LCS Algorithm (Variation)]].)

### Python Solution (Placeholder)
```python
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None): self.val = val; self.left = left; self.right = right
# class ListNode:
#     def __init__(self, val=0, next=None): self.val = val; self.next = next
class Solution:
    def solve(self, params): # Signature will vary based on problem
        # TODO: Implement solution
        pass
```

## Complexity Analysis
(To be filled)

## 总结 (Summary)
(To be filled)

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
