---
tags: [problem/leetcode, lc/medium, topic/dynamic_programming, topic/tree, pattern/house_robber, course/labuladong, lc/lc337]
aliases: [LC337, House Robber III]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/一个方法团灭 LeetCode 打家劫舍问题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 337. House Robber III
> This is part of the [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/House Robber Pattern|House Robber Pattern]].

# LeetCode 337: House Robber III

## Problem Statement
The thief has found himself a new place for his thievery again. There is only one entrance to this area, called `root`. Besides the `root`, each house has one and only one parent house. After a tour, the smart thief realized that "all houses in this place forms a binary tree". It will automatically contact the police if two directly-linked houses were broken into on the same night.
Given the `root` of the binary tree, return *the maximum amount of money the thief can rob **without alerting the police***.

**Official Link:** [LeetCode 337. House Robber III](https://leetcode.com/problems/house-robber-iii/)

## Solution Approach
This problem applies the House Robber logic to a binary tree structure. The solution uses recursive DP (post-order traversal) as detailed in [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/House Robber Pattern|House Robber Pattern]].

### Python Solution
```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class Solution:
    def rob(self, root: TreeNode) -> int:

        # Returns a pair: [money_if_rob_this_node, money_if_not_rob_this_node]
        def dp_rob(node: TreeNode) -> list[int]:
            if not node:
                return [0, 0] 

            left_res = dp_rob(node.left)
            right_res = dp_rob(node.right)

            # If we rob the current node `node`
            rob_current_node_val = node.val + left_res[1] + right_res[1] 
                                      # (node.val + not_rob_left_child + not_rob_right_child)

            # If we do NOT rob the current node `node`
            not_rob_current_node_val = max(left_res[0], left_res[1]) + \
                                       max(right_res[0], right_res[1])
                                      # (max gain from left child + max gain from right child)
            return [rob_current_node_val, not_rob_current_node_val]

        result_pair = dp_rob(root)
        return max(result_pair[0], result_pair[1])
```

## Complexity Analysis
- **Time Complexity:** $O(N)$, where $N$ is the number of nodes. Each node is visited once.
- **Space Complexity:** $O(H)$ for the recursion stack, where $H$ is the height of the tree. In the worst case (skewed tree), $H=N$.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
