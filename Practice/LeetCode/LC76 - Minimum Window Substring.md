---
tags: [problem/leetcode, lc/hard, topic/string, topic/hash_table, topic/two_pointers, pattern/sliding_window, course/labuladong]
aliases: [LC76, LeetCode 76. Minimum Window Substring, 最小覆盖子串]
summary: |
  This problem asks to find the minimum window substring of s that contains all characters of t, including duplicates. 
  It's a classic application of the sliding window technique using character frequency maps.
created: 2025-05-25T22:48:40.676-07:00
modified: 2025-05-26T18:00:00.000-07:00
---

> [!NOTE] Source Annotation
> Problem: LeetCode 76. Minimum Window Substring
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/滑动窗口算法核心代码模板.md|滑动窗口算法核心代码模板]].

# LeetCode 76: Minimum Window Substring

## Problem Statement

Given two strings `s` and `t` of lengths `m` and `n` respectively, return *the minimum window substring of `s` such that every character in `t` (including duplicates) is included in the window*. If there is no such substring, return the empty string `""`.

The testcases will be generated such that the answer is unique.

**Official Link:** [LeetCode 76. Minimum Window Substring](https://leetcode.com/problems/minimum-window-substring/)

**Example 1:**
Input: `s = "ADOBECODEBANC"`, `t = "ABC"`
Output: `"BANC"`
Explanation: The minimum window substring "BANC" includes 'A', 'B', and 'C' from string `t`.

## Solution Approach: Sliding Window

This problem asks for the *minimum* window, a classic indicator for the [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window technique]].

1.  **Character Counts for `t` (`needs`):** First, count the frequency of each character in string `t`. This map (`needs`) tells us what characters our window must contain, and how many of each.
2.  **Window Character Counts (`window_chars`):** Maintain a similar map for the characters currently within our sliding window `s[left:right)`.
3.  **`valid_chars_count`:** Keep track of how many character types from `t` have their required frequencies met within the current `window_chars`.
4.  **Pointers `left`, `right`:**
    - `right` expands the window. When `s[right]` (character `char_in`) enters the window, update `window_chars` and `valid_chars_count`.
    - `left` shrinks the window when the `window_needs_shrink` condition is met (i.e., `valid_chars_count == len(needs)`). When `s[left]` (character `char_out`) leaves the window, update `window_chars` and `valid_chars_count`.
5.  **Result Tracking:** When the window is valid (`valid_chars_count == len(needs)`), we are inside the inner "shrink" loop. This is where we update the minimum window found so far because any valid window is a candidate.

### Python Solution
```python
import collections

class Solution:
    def minWindow(self, s: str, t: str) -> str:
        needs = collections.Counter(t)
        window_chars = collections.defaultdict(int)

        left, right = 0, 0
        valid_chars_count = 0  # Number of character types in t that are fully satisfied in window

        # To store the result: start index and length of the minimum window
        min_len = float('inf')
        result_start_index = 0

        if not t: # Edge case: if t is empty, the answer is an empty string
            return ""

        while right < len(s):
            # Expand window by adding s[right] (char_in)
            char_in = s[right]
            right += 1 # Window is now s[left...right-1]

            if char_in in needs:
                window_chars[char_in] += 1
                if window_chars[char_in] == needs[char_in]:
                    valid_chars_count += 1

            # Shrink window while it's valid (contains all characters of t)
            # The condition to shrink for this problem is `valid_chars_count == len(needs)`
            while valid_chars_count == len(needs):
                # Current window s[left:right] is a valid candidate.
                # Update minimum window found so far.
                current_len = right - left
                if current_len < min_len:
                    min_len = current_len
                    result_start_index = left

                # Remove s[left] (char_out) from window to try for a smaller valid window
                char_out = s[left]
                left += 1 # Shrink window

                if char_out in needs:
                    # This check is important: only decrement valid_chars_count
                    # if char_out was making one of the needed char types valid.
                    if window_chars[char_out] == needs[char_out]:
                        valid_chars_count -= 1
                    window_chars[char_out] -= 1
        
        return "" if min_len == float('inf') else s[result_start_index : result_start_index + min_len]

```
Labuladong's article includes visual images `![](/algo/images/slidingwindow/1.png)` to `![](/algo/images/slidingwindow/4.png)` which depict this expansion and contraction.

## Complexity Analysis
-   **Time Complexity:** $O(M+N)$, where $M$ is the length of `s` and $N$ is the length of `t`.
    - Building `needs` map: $O(N)$.
    - The `left` and `right` pointers each traverse `s` at most once. So, the main `while` loop and inner `while` loop combined result in $O(M)$ operations. Hash map operations inside are average $O(1)$ (assuming character set is small, like ASCII or Unicode).
-   **Space Complexity:** $O(K)$, where $K$ is the size of the character set (e.g., 26 for lowercase English letters, or up to 52 for ASCII if case-sensitive). This is for `needs` and `window_chars` maps.

## Visualization of Key Steps
(Using `s = "ADOBECODEBANC", t = "ABC"`)

1.  `needs = {'A':1, 'B':1, 'C':1}`. `len(needs) = 3`.
2.  `right` pointer expands.
    - `s[0:6]` = `"ADOBEC"`. Window `left=0, right=6`.
    - `window_chars = {'A':1, 'D':1, 'O':1, 'B':1, 'E':1, 'C':1}`.
    - `valid_chars_count` becomes 3 (A, B, C are all satisfied).
3.  Inner `while (valid_chars_count == 3)` loop starts:
    -   Window `s[0:6]` is `"ADOBEC"`. Length `6`. `min_len = 6`, `result_start_index = 0`.
    -   Shrink: `char_out = s[0] = 'A'`. `left = 1`.
    -   `window_chars['A']` becomes 0. `valid_chars_count` becomes 2 (since `window_chars['A']` was `needs['A']` and now it's less).
    -   Inner loop condition `valid_chars_count == 3` is now false. Exit inner loop.
4.  Outer loop continues. `right` expands.
    - ... (window slides) ...
    - Eventually, `s[right-1]` might be `'B'` when window is `s[left...]="...BECODEB"`.
    - Later, `s[right-1]` might be `'A'` when window is `s[left...]="...ODEBANA"`.
    - Later, `s[right-1]` might be `'N'` then `'C'`. Consider window `s[left...]="BANC"`.
    - Say `left=9`, `right=13`. Window `s[9:13]` is `"BANC"`.
    - `window_chars = {'B':1, 'A':1, 'N':1, 'C':1}`. (`N` is not in `needs`).
    - `valid_chars_count` is 3 (for A, B, C).
    - Inner `while (valid_chars_count == 3)`:
        -   Window `"BANC"`. Length `4`. `4 < min_len (6)`. Update `min_len = 4`, `result_start_index = 9`.
        -   Shrink: `char_out = s[9] = 'B'`. `left = 10`.
        -   `window_chars['B']` becomes 0. `valid_chars_count` becomes 2.
        -   Inner loop condition `valid_chars_count == 3` is false. Exit inner loop.
5.  Outer loop continues until `right == len(s)`.
6.  Return `s[result_start_index : result_start_index + min_len]` which is `s[9:13] = "BANC"`.

## 总结 (Summary)
- LC76 is a classic "minimum window" problem solved effectively with the sliding window technique.
- Requires tracking character counts needed (`needs`) and character counts in the current window (`window_chars`).
- `valid_chars_count` efficiently checks if the window satisfies all character requirements from `t`.
- The process involves expanding the window with `right` and shrinking it with `left` while maintaining the validity condition and updating the minimum length found. The update for the minimum occurs *inside* the shrinking loop when the window is confirmed valid.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window Framework]]
