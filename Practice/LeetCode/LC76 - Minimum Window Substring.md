---
tags: [problem/leetcode, lc/hard, topic/string, topic/hash_table, topic/two_pointers, pattern/sliding_window]
aliases: [LC76, LeetCode 76. Minimum Window Substring, 最小覆盖子串]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 76. Minimum Window Substring
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/滑动窗口算法核心代码模板.md]].

# LeetCode 76: Minimum Window Substring

## Problem Statement

Given two strings `s` and `t` of lengths `m` and `n` respectively, return *the minimum window substring of `s` such that every character in `t` (including duplicates) is included in the window*. If there is no such substring, return the empty string `""`.

The testcases will be generated such that the answer is unique.

**Official Link:** [LeetCode 76. Minimum Window Substring](https://leetcode.com/problems/minimum-window-substring/)

**Example 1:**
Input: `s = "ADOBECODEBANC"`, `t = "ABC"`
Output: `"BANC"`
Explanation: The minimum window substring "BANC" includes 'A', 'B', and 'C' from string `t`.

## Solution Approach: Sliding Window

This problem asks for the *minimum* window, a classic indicator for the [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window technique]].

1.  **Character Counts for `t` (`needs`):** First, count the frequency of each character in string `t`. This map (`needs`) tells us what characters our window must contain, and how many of each.
2.  **Window Character Counts (`window_chars`):** Maintain a similar map for the characters currently within our sliding window `s[left:right]`.
3.  **`valid_chars_count`:** Keep track of how many character types from `t` have their required frequencies met within the current `window_chars`.
4.  **Pointers `left`, `right`:**
    - `right` expands the window by moving to the right. When `s[right]` enters the window, update `window_chars` and `valid_chars_count`.
    - `left` shrinks the window from the left when `valid_chars_count == len(needs)`. When `s[left]` leaves the window, update `window_chars` and `valid_chars_count`.
5.  **Result Tracking:** When `valid_chars_count == len(needs)`, the window is valid. At this point, try to shrink it by moving `left` to find the minimum possible length for this valid window. Record the start index and length of the shortest valid window found.

### Python Solution
```python
import collections

class Solution:
    def minWindow(self, s: str, t: str) -> str:
        needs = collections.Counter(t)
        window_chars = collections.defaultdict(int)

        left, right = 0, 0
        valid_chars_count = 0  # Number of character types in t that are fully satisfied in window

        # To store the result: start index and length of the minimum window
        min_len = float('inf')
        start_index_res = 0

        while right < len(s):
            # Expand window by adding s[right]
            char_in = s[right]
            right += 1

            if char_in in needs:
                window_chars[char_in] += 1
                if window_chars[char_in] == needs[char_in]:
                    valid_chars_count += 1

            # Shrink window when it's valid (contains all characters of t)
            while valid_chars_count == len(needs):
                # Update minimum window found so far
                current_len = right - left
                if current_len < min_len:
                    min_len = current_len
                    start_index_res = left

                # Remove s[left] from window
                char_out = s[left]
                left += 1

                if char_out in needs:
                    if window_chars[char_out] == needs[char_out]:
                        valid_chars_count -= 1
                    window_chars[char_out] -= 1

        return "" if min_len == float('inf') else s[start_index_res : start_index_res + min_len]

```
Labuladong's article includes visual images `![](/algo/images/slidingwindow/1.png)` to `![](/algo/images/slidingwindow/4.png)` which depict this expansion and contraction. The logic is:
1. `right` pointer expands the window `[left, right)`. `window_chars` is updated.
2. If `window_chars` now meets all character counts in `needs` (i.e., `valid_chars_count == len(needs)`), this window is a candidate. Record its length if it's the new minimum.
3. Now, try to shrink the window from the left by incrementing `left`. `window_chars` is updated for the character `s[left]` being removed.
4. If the window *still* meets the `needs` criteria after removing `s[left]`, it's an even shorter candidate. Record its length. Repeat shrinking.
5. If shrinking makes the window invalid ( `valid_chars_count < len(needs)` ), stop shrinking and go back to expanding `right`.

The provided Python code directly implements this logic.

## Complexity Analysis
-   **Time Complexity:** $O(M+N)$, where $M$ is the length of `s` and $N$ is the length of `t`.
    - Building `needs` map: $O(N)$.
    - The `left` and `right` pointers each traverse `s` at most once. So, the main `while` loop and inner `while` loop combined result in $O(M)$ operations. Hash map operations inside are average $O(1)$ (assuming character set is small, like ASCII or Unicode).
-   **Space Complexity:** $O(K)$, where $K$ is the number of unique characters in `t` (for `needs`) and `s` (for `window_chars`). If the character set is fixed (e.g., ASCII), this can be considered $O(1)$.

## Visualizing Key Steps
(Using `s = "ADOBECODEBANC", t = "ABC"`)

1.  `needs = {'A':1, 'B':1, 'C':1}`
2.  `right` expands:
    - `s[0..5] = "ADOBEC"`. `window_chars` contains A,B,C. `valid_chars_count = 3`. `min_len=6, start=0`.
3.  `left` shrinks (while `valid_chars_count == 3`):
    - `left=0 ('A')`. Remove 'A'. `window_chars` has 'A':0. `valid_chars_count` becomes 2 (if 'A' was critical). (Actually, `valid_chars_count` decreases when `window_chars[char_out]` *was equal to* `needs[char_out]` and now is less).
    - Let's trace carefully:
      - `ADOBEC` (len 6), `left=0, right=6`. `valid=3`. Update `min_len=6, start=0`.
      - Remove `s[0]='A'`. `window={'D':1,'O':1,'B':1,'E':1,'C':1}`. `valid` might become 2 if 'A' was the only 'A' needed. `left=1`. Current window `"DOBEC"`.
      - This detail is subtle: `valid_chars_count` only decreases if the count of a needed character in `window_chars` drops *below* its required count in `needs`.
      - Corrected logic for shrinking:
        - Current window `ADOBEC`, `left=0, right=6`. Valid. `min_len=6, start=0`.
        - `d = s[left] = 'A'`. `left++`. `window['A']--`. If `window['A'] < needs['A']`, then `valid--`.
        - Is `ADOBEC` the shortest? Yes, currently. `min_len=6, start=0`.
        - Shrink: `d = s[0]='A'`. `left=1`. `window_chars['A']` becomes 0. `valid_chars_count` becomes 2. Loop `while valid_chars_count == len(needs)` terminates.
    - This means the update to `min_len` and `start` must happen *inside* the shrinking loop, *before* `char_out` is processed in a way that might invalidate the window.

The key moments for LC76:
- `right` expands until `window_chars` satisfies `needs`.
- Once satisfied, `min_len` is updated.
- Then `left` tries to shrink. For every successful shrink that *still* satisfies `needs`, `min_len` is updated again.
- When shrinking `left` causes `window_chars` to no longer satisfy `needs`, the inner loop stops, and `right` expansion resumes.

The `while (valid_chars_count == need.size())` loop correctly handles this. When this condition is true:
1.  The current window `s[left:right]` is a valid candidate. Update `min_len` and `start_index_res` if `right - left` is smaller.
2.  Then, prepare to shrink: `d = s[left]`, `left++`.
3.  Update `window_chars` and `valid_chars_count` based on `d`.
4.  The loop condition `valid_chars_count == need.size()` is re-checked. If still true, it means `s[left:right]` (the newly shrunk window) is also valid, and we repeat step 1.

## 总结 (Summary)
- LC76 is a classic "minimum window" problem solved effectively with the sliding window technique.
- Requires tracking character counts needed (`needs`) and character counts in the current window (`window_chars`).
- `valid_chars_count` efficiently checks if the window satisfies all character requirements from `t`.
- The process involves expanding the window with `right` and shrinking it with `left` while maintaining the validity condition and updating the minimum length found.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window Framework]]
