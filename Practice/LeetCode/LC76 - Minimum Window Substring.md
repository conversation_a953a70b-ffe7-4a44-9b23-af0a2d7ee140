---
tags: [problem/leetcode, lc/hard, topic/sliding_window, topic/two_pointers, pattern/window_optimization, pattern/frequency_counting]
aliases: [LC76, LeetCode 76, Minimum Window Substring, 最小覆盖子串]
---

# LeetCode 76: Minimum Window Substring

## Problem Statement

Given two strings `s` and `t` of lengths `m` and `n` respectively, return the **minimum window substring** of `s` such that every character in `t` (including duplicates) is included in the window. If there is no such window, return the empty string `""`.

The testcases will be generated such that the answer is **unique**.

**Official Link:** [LeetCode 76. Minimum Window Substring](https://leetcode.com/problems/minimum-window-substring/)

## 🪟 Understanding the Window Problem

Think of this as finding the smallest "window" that contains all required characters:

```tikz
\begin{tikzpicture}[
    char_box/.style={rectangle, draw, minimum size=0.8cm, font=\sffamily\bfseries, fill=blue!30},
    target_box/.style={rectangle, draw, minimum size=0.8cm, font=\sffamily\bfseries, fill=red!30},
    window_box/.style={rectangle, draw, minimum size=0.8cm, font=\sffamily\bfseries, fill=green!40},
    window_frame/.style={rectangle, draw, thick, red, dashed},
    example_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=4cm, align=center}
]

% Example visualization
\node at (6, 6) {\bfseries Example: s = "ADOBECODEBANC", t = "ABC"};

% String s
\node at (0, 5) {\small s:};
\node[char_box] at (0.8, 5) {A};
\node[char_box] at (1.6, 5) {D};
\node[char_box] at (2.4, 5) {O};
\node[window_box] at (3.2, 5) {B};
\node[window_box] at (4, 5) {E};
\node[window_box] at (4.8, 5) {C};
\node[char_box] at (5.6, 5) {O};
\node[char_box] at (6.4, 5) {D};
\node[char_box] at (7.2, 5) {E};
\node[window_box] at (8, 5) {B};
\node[window_box] at (8.8, 5) {A};
\node[window_box] at (9.6, 5) {N};
\node[window_box] at (10.4, 5) {C};

% Target string t
\node at (0, 3.5) {\small t:};
\node[target_box] at (0.8, 3.5) {A};
\node[target_box] at (1.6, 3.5) {B};
\node[target_box] at (2.4, 3.5) {C};

% Window frames
\draw[window_frame] (2.9, 4.7) rectangle (5.1, 5.3);
\node at (4, 4.4) {\tiny Window 1: "BEC"};

\draw[window_frame] (7.7, 4.7) rectangle (10.7, 5.3);
\node at (9.2, 4.4) {\tiny Window 2: "BANC"};

\node[example_box] at (12, 4.5) {
    \textbf{Goal:}\\
    Find shortest substring\\
    containing all chars\\
    from t (with frequencies)\\[0.5em]
    \textbf{Answer: "BANC"}\\
    (length 4)
};

\end{tikzpicture}
```

**Key Insight:** This is a classic **[[Sliding Window]]** problem with **[[Frequency Counting]]** - we need to find the optimal window that satisfies our constraints.

## 🧠 The Breakthrough Insight: Two-Pointer Window

The key realization is to use a **flexible sliding window** that expands and contracts based on validity:

```tikz
\begin{tikzpicture}[
    insight_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4.5cm, align=center, minimum height=2.5cm},
    arrow/.style={->, thick, red, line width=2pt},
    strategy_box/.style={rectangle, draw, fill=orange!20, font=\sffamily\small, text width=6cm, align=center}
]

\node[insight_box] (naive) at (0, 3) {
    \textbf{Naive Approach}\\[0.5em]
    "Check every possible\\
    substring and find\\
    the shortest valid one"\\[0.5em]
    O(n³) complexity!\\
    Too slow!
};

\node[insight_box] (smart) at (8, 3) {
    \textbf{Sliding Window}\\[0.5em]
    "Use two pointers to\\
    maintain a dynamic\\
    window that expands\\
    and contracts"\\[0.5em]
    O(n) complexity!
};

\draw[arrow] (naive) -- (smart);

\node[strategy_box] at (4, 0.5) {
    \textbf{Window Strategy:}\\
    1. Expand window (move right) until valid\\
    2. Contract window (move left) while maintaining validity\\
    3. Track minimum valid window found\\[0.5em]
    Each character visited at most twice!
};

\end{tikzpicture}
```

**Why This Works:**
- **Expand**: Add characters until we have a valid window
- **Contract**: Remove characters while keeping window valid
- **Optimize**: Track the smallest valid window seen

## 🔍 Problem Decomposition: Window Validity

The core challenge is efficiently determining when a window is "valid":

```tikz
\begin{tikzpicture}[
    concept_box/.style={rectangle, draw, rounded corners, fill=green!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2cm},
    formula_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=3.5cm, align=center}
]

\node[concept_box] (validity) at (0, 3) {
    \textbf{Window Validity}\\[0.5em]
    A window is valid when\\
    it contains all characters\\
    from t with required\\
    frequencies
};

\node[concept_box] (tracking) at (5, 3) {
    \textbf{Efficient Tracking}\\[0.5em]
    Use frequency maps and\\
    a "formed" counter to\\
    check validity in O(1)
};

\node[formula_box] at (0, 0.5) {
    \textbf{Frequency Check:}\\
    For each char c in t:\\
    window\_count[c] >=\\
    required\_count[c]
};

\node[formula_box] at (5, 0.5) {
    \textbf{Formed Counter:}\\
    Track how many chars\\
    have sufficient frequency\\
    formed == unique\_chars
};

\end{tikzpicture}
```

## 💡 Clean, Elegant Algorithm

Here's the beautifully structured solution that maps directly to our intuition:

```python
class Solution:
    def minWindow(self, s: str, t: str) -> str:
        if not s or not t:
            return ""

        # Step 1: Build requirement map
        required = {}
        for char in t:
            required[char] = required.get(char, 0) + 1

        # Step 2: Initialize sliding window variables
        left = right = 0
        formed = 0  # Number of unique chars with desired frequency
        required_chars = len(required)

        # Step 3: Track current window
        window_counts = {}

        # Step 4: Track best window found
        min_len = float('inf')
        min_left = 0

        # Step 5: Sliding window algorithm
        while right < len(s):
            # Expand window: add character from right
            char = s[right]
            window_counts[char] = window_counts.get(char, 0) + 1

            # Check if this character's frequency matches requirement
            if char in required and window_counts[char] == required[char]:
                formed += 1

            # Contract window: try to shrink from left
            while left <= right and formed == required_chars:
                # Update minimum window if current is smaller
                if right - left + 1 < min_len:
                    min_len = right - left + 1
                    min_left = left

                # Remove character from left
                left_char = s[left]
                window_counts[left_char] -= 1
                if left_char in required and window_counts[left_char] < required[left_char]:
                    formed -= 1

                left += 1

            right += 1

        # Step 6: Return result
        return "" if min_len == float('inf') else s[min_left:min_left + min_len]
```

## 🔍 Visual Algorithm Trace

Let's trace through `s = "ADOBECODEBANC"`, `t = "ABC"`:

```tikz
\begin{tikzpicture}[
    char_box/.style={rectangle, draw, minimum size=0.6cm, font=\tiny, fill=blue!30},
    window_char/.style={char_box, fill=green!40},
    target_char/.style={char_box, fill=red!30},
    pointer/.style={->, thick, red},
    step_box/.style={rectangle, draw, fill=yellow!20, font=\tiny, text width=3cm, align=center},
    window_frame/.style={rectangle, draw, thick, blue, dashed}
]

% Step 1: Initial state
\node at (6, 8) {\bfseries Step 1: Initialize (required = \{A:1, B:1, C:1\})};

\node at (0, 7.5) {\tiny s:};
\foreach \i/\char in {0/A,1/D,2/O,3/B,4/E,5/C,6/O,7/D,8/E,9/B,10/A,11/N,12/C} {
    \node[char_box] at (\i*0.7+0.5, 7.5) {\char};
}

\draw[pointer] (0.5, 7.2) -- (0.5, 7) node[below] {\tiny L};
\draw[pointer] (0.5, 7.2) -- (0.5, 7) node[below] {\tiny R};

\node[step_box] at (10, 7.5) {
    left = 0, right = 0\\
    formed = 0\\
    window = \{\}\\
    min\_len = inf
};

% Step 2: Expand to find first valid window
\node at (6, 6) {\bfseries Step 2: Expand until valid window (ADOBEC)};

\foreach \i/\char in {0/A,1/D,2/O,3/B,4/E,5/C,6/O,7/D,8/E,9/B,10/A,11/N,12/C} {
    \ifnum\i<6
        \node[window_char] at (\i*0.7+0.5, 6) {\char};
    \else
        \node[char_box] at (\i*0.7+0.5, 6) {\char};
    \fi
}

\draw[window_frame] (0.2, 5.7) rectangle (4.5, 6.3);
\draw[pointer] (0.5, 5.5) -- (0.5, 5.7) node[below] {\tiny L};
\draw[pointer] (4.2, 5.5) -- (4.2, 5.7) node[below] {\tiny R};

\node[step_box] at (10, 6) {
    left = 0, right = 5\\
    formed = 3\\
    window = \{A:1,D:1,O:1,B:1,E:1,C:1\}\\
    min\_len = 6
};

% Step 3: Contract to find smaller window
\node at (6, 4.5) {\bfseries Step 3: Contract while valid (remove A,D,O)};

\foreach \i/\char in {0/A,1/D,2/O,3/B,4/E,5/C,6/O,7/D,8/E,9/B,10/A,11/N,12/C} {
    \ifnum\i>2
        \ifnum\i<6
            \node[window_char] at (\i*0.7+0.5, 4.5) {\char};
        \else
            \node[char_box] at (\i*0.7+0.5, 4.5) {\char};
        \fi
    \else
        \node[char_box] at (\i*0.7+0.5, 4.5) {\char};
    \fi
}

\draw[window_frame] (2.3, 4.2) rectangle (4.5, 4.8);
\draw[pointer] (2.6, 4) -- (2.6, 4.2) node[below] {\tiny L};
\draw[pointer] (4.2, 4) -- (4.2, 4.2) node[below] {\tiny R};

\node[step_box] at (10, 4.5) {
    left = 3, right = 5\\
    formed = 3\\
    window = \{B:1,E:1,C:1\}\\
    min\_len = 3
};

% Step 4: Continue to find optimal
\node at (6, 3) {\bfseries Step 4: Continue expansion to find "BANC"};

\foreach \i/\char in {0/A,1/D,2/O,3/B,4/E,5/C,6/O,7/D,8/E,9/B,10/A,11/N,12/C} {
    \ifnum\i>8
        \node[window_char] at (\i*0.7+0.5, 3) {\char};
    \else
        \node[char_box] at (\i*0.7+0.5, 3) {\char};
    \fi
}

\draw[window_frame] (6.6, 2.7) rectangle (9.4, 3.3);
\draw[pointer] (6.9, 2.5) -- (6.9, 2.7) node[below] {\tiny L};
\draw[pointer] (9.1, 2.5) -- (9.1, 2.7) node[below] {\tiny R};

\node[step_box] at (10, 3) {
    left = 9, right = 12\\
    formed = 3\\
    window = \{B:1,A:1,N:1,C:1\}\\
    min\_len = 4
};

% Final result
\node[step_box, fill=green!30] at (6, 1.5) {
    \textbf{Final Answer: "BANC"}\\
    Minimum window length = 4\\
    Found at indices 9-12
};

\end{tikzpicture}
```

**Key Observations:**
1. **Expand phase**: Right pointer moves until window becomes valid
2. **Contract phase**: Left pointer moves while window stays valid
3. **Optimization**: Track minimum window during contraction
4. **Efficiency**: Each character visited at most twice

## 🧠 Key Takeaways & Problem-Solving Framework

### Why This Problem is Educational

```tikz
\begin{tikzpicture}[
    concept_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=1.8cm},
    arrow/.style={->, thick, green}
]

\node[concept_box] (pattern) at (0, 3) {
    \textbf{Pattern Recognition}\\[0.3em]
    Identify sliding window\\
    from "minimum/maximum\\
    substring" keywords
};

\node[concept_box] (validity) at (4.5, 3) {
    \textbf{Validity Tracking}\\[0.3em]
    Efficient window validity\\
    using frequency counting\\
    and formed counter
};

\node[concept_box] (optimization) at (9, 3) {
    \textbf{Two-Pointer Magic}\\[0.3em]
    Expand and contract\\
    pointers independently\\
    for O(n) solution
};

\draw[arrow] (pattern) -- (validity);
\draw[arrow] (validity) -- (optimization);

\node at (4.5, 1.5) {\bfseries Core Learning: Recognize → Track → Optimize};

\end{tikzpicture}
```

### Problem-Solving Pattern: [[Sliding Window]]

This problem exemplifies the **sliding window** pattern:

1. **Window expansion**: Add elements until constraint satisfied
2. **Window contraction**: Remove elements while maintaining constraint
3. **Optimization tracking**: Record best solution during valid windows
4. **Efficiency**: Each element processed at most twice

### Complexity Analysis
- **Time Complexity:** O(|s| + |t|) - each character visited at most twice
- **Space Complexity:** O(|s| + |t|) - for frequency maps

### Related Concepts for Obsidian

This problem connects to several important algorithmic concepts:

- **[[Sliding Window]]**: Core technique being applied
- **[[Frequency Counting]]**: Efficient validity checking
- **[[Two Pointers]]**: Independent pointer movement
- **[[Hash Tables]]**: Fast frequency lookups
- **[[String Algorithms]]**: Substring optimization problems

### Related Problems
- **LC438. Find All Anagrams in a String**: Similar frequency-based sliding window
- **LC567. Permutation in String**: Exact frequency matching
- **LC3. Longest Substring Without Repeating Characters**: Different validity constraint
- **LC209. Minimum Size Subarray Sum**: Numeric sliding window variant

### Implementation Tips

#### Clean Code Structure
```python
# Template for sliding window problems
def sliding_window_template(s, constraint):
    # 1. Initialize window state
    left = right = 0
    window_state = {}

    # 2. Track best result
    best_result = None

    # 3. Sliding window loop
    while right < len(s):
        # Expand: add s[right] to window
        expand_window(window_state, s[right])

        # Contract: shrink window while valid
        while is_valid(window_state, constraint):
            update_best_result(best_result, left, right)
            contract_window(window_state, s[left])
            left += 1

        right += 1

    return best_result
```

#### Common Pitfalls
- **Off-by-one errors**: Careful with window boundaries
- **Frequency tracking**: Ensure correct increment/decrement
- **Edge cases**: Empty strings, no valid window
- **Result extraction**: Correct substring indices

This problem beautifully demonstrates how **sliding window** technique transforms an O(n³) brute force into an elegant O(n) solution through smart pointer management and frequency tracking!
