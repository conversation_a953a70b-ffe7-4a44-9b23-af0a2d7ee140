---
tags: [pattern/linked_list_reversal, topic/linked_list, topic/recursion, topic/iteration, concept/pointer_manipulation]
aliases: [Linked List Reversal, 链表反转, Reverse Linked List Pattern]
---

# Linked List Reversal: Complete Pattern Guide

## Pattern Overview

Linked List Reversal is a fundamental pattern that appears in many variations. This guide covers the complete spectrum from basic reversal to advanced k-group reversals.

**Core Problems Covered:**
- **LC206. Reverse Linked List** (Easy) - Basic reversal
- **LC92. Reverse Linked List II** (Medium) - Partial reversal
- **LC25. Reverse Nodes in k-Group** (Hard) - K-group reversal

## 🎯 Understanding the Linked List Reversal Pattern

### The Real-World Scenario

Imagine you have a chain of people holding hands, and you want to reverse their order:

```tikz
\begin{tikzpicture}[
    person/.style={circle, draw, minimum width=1cm, font=\tiny, fill=blue!30},
    arrow/.style={->, thick, blue},
    reverse_arrow/.style={->, thick, red, dashed},
    example_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=4cm, align=center}
]

% Original chain
\node at (6, 8) {\bfseries Linked List Reversal: Chain of People};

\node at (1, 7) {\small Original order:};
\node[person] (p1) at (1, 6) {1};
\node[person] (p2) at (2.5, 6) {2};
\node[person] (p3) at (4, 6) {3};
\node[person] (p4) at (5.5, 6) {4};
\node[person] (p5) at (7, 6) {5};

\draw[arrow] (p1) -- (p2);
\draw[arrow] (p2) -- (p3);
\draw[arrow] (p3) -- (p4);
\draw[arrow] (p4) -- (p5);

% Reversed chain
\node at (1, 4.5) {\small Reversed order:};
\node[person] (r5) at (1, 3.5) {5};
\node[person] (r4) at (2.5, 3.5) {4};
\node[person] (r3) at (4, 3.5) {3};
\node[person] (r2) at (5.5, 3.5) {2};
\node[person] (r1) at (7, 3.5) {1};

\draw[arrow] (r5) -- (r4);
\draw[arrow] (r4) -- (r3);
\draw[arrow] (r3) -- (r2);
\draw[arrow] (r2) -- (r1);

\node[example_box] at (9, 6) {
    \textbf{Goal:}\\
    Reverse the direction\\
    of all connections\\
    in the chain\\[0.5em]
    \textbf{Challenge:}\\
    Don't lose any\\
    connections during\\
    the process!
};

\node[example_box] at (9, 3.5) {
    \textbf{Key Insight:}\\
    We need to carefully\\
    manage pointers to\\
    avoid breaking\\
    the chain!
};

\end{tikzpicture}
```

**Critical Insight:** This is a **[[Pointer Manipulation]]** problem! We need to reverse the direction of all `next` pointers without losing any nodes.

## 🧠 The Breakthrough: Three-Pointer Strategy

### Step 1: Understanding the Pointer Challenge

The key realization is that we need to track multiple pointers to safely reverse connections:

```tikz
\begin{tikzpicture}[
    node/.style={circle, draw, minimum width=1cm, font=\tiny, fill=blue!30},
    pointer/.style={->, thick, red, line width=2pt},
    next_ptr/.style={->, thick, blue},
    insight_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4.5cm, align=center, minimum height=2.5cm},
    arrow/.style={->, thick, red, line width=2pt},
    strategy_box/.style={rectangle, draw, fill=orange!20, font=\sffamily\small, text width=6cm, align=center}
]

\node[insight_box] (problem) at (0, 3) {
    \textbf{The Problem:}\\[0.5em]
    "If we change cur.next\\
    to point backwards,\\
    we lose the rest\\
    of the list!"\\[0.5em]
    Need to save next\\
    before changing!
};

\node[insight_box] (solution) at (8, 3) {
    \textbf{The Solution:}\\[0.5em]
    "Use three pointers:\\
    prev, cur, next\\
    to safely navigate"\\[0.5em]
    Save → Reverse → Move\\
    in careful order!
};

\draw[arrow] (problem) -- (solution);

\node[strategy_box] at (4, 0.5) {
    \textbf{Three-Pointer Strategy:}\\
    prev: tracks the reversed portion\\
    cur: current node being processed\\
    next: saves the remaining list before we lose it
};

\end{tikzpicture}
```

### Step 2: The Three-Pointer Dance

Let's see how the three pointers work together in detail:

```tikz
\begin{tikzpicture}[
    node/.style={circle, draw, minimum width=0.8cm, font=\tiny, fill=blue!30},
    null_node/.style={rectangle, draw, minimum width=0.8cm, minimum height=0.8cm, font=\tiny, fill=gray!30},
    prev_ptr/.style={->, thick, green, line width=2pt},
    cur_ptr/.style={->, thick, red, line width=2pt},
    next_ptr/.style={->, thick, orange, line width=2pt},
    link/.style={->, thick, blue},
    reverse_link/.style={->, thick, red},
    step_box/.style={rectangle, draw, fill=yellow!20, font=\tiny, text width=3cm, align=center}
]

% Complete iteration trace for [1,2,3]
\node at (6, 9) {\bfseries Three-Pointer Dance: [1→2→3]};

% Iteration 0: Initial
\node at (1, 8) {\tiny Initial State};
\node[null_node] (null0) at (0.5, 7.5) {null};
\node[node] (n1_0) at (2, 7.5) {1};
\node[node] (n2_0) at (3.5, 7.5) {2};
\node[node] (n3_0) at (5, 7.5) {3};

\draw[link] (n1_0) -- (n2_0);
\draw[link] (n2_0) -- (n3_0);

\draw[prev_ptr] (0.5, 7) -- (null0);
\draw[cur_ptr] (2, 7) -- (n1_0);
\draw[next_ptr] (3.5, 7) -- (n2_0);

\node[step_box] at (7.5, 7.5) {
    prev = null\\
    cur = 1\\
    next = 2
};

% Iteration 1: After processing node 1
\node at (1, 6) {\tiny After Step 1};
\node[null_node] (null1) at (0.5, 5.5) {null};
\node[node] (n1_1) at (2, 5.5) {1};
\node[node] (n2_1) at (3.5, 5.5) {2};
\node[node] (n3_1) at (5, 5.5) {3};

\draw[reverse_link] (n1_1) -- (null1);
\draw[link] (n2_1) -- (n3_1);

\draw[prev_ptr] (2, 5) -- (n1_1);
\draw[cur_ptr] (3.5, 5) -- (n2_1);
\draw[next_ptr] (5, 5) -- (n3_1);

\node[step_box] at (7.5, 5.5) {
    1.next = null\\
    prev = 1\\
    cur = 2\\
    next = 3
};

% Iteration 2: After processing node 2
\node at (1, 4) {\tiny After Step 2};
\node[null_node] (null2) at (0.5, 3.5) {null};
\node[node] (n1_2) at (2, 3.5) {1};
\node[node] (n2_2) at (3.5, 3.5) {2};
\node[node] (n3_2) at (5, 3.5) {3};

\draw[reverse_link] (n1_2) -- (null2);
\draw[reverse_link] (n2_2) -- (n1_2);

\draw[prev_ptr] (3.5, 3) -- (n2_2);
\draw[cur_ptr] (5, 3) -- (n3_2);
\draw[next_ptr] (6, 3) to[bend left=30] (6.5, 3.5);
\node at (6.5, 3.5) {\tiny null};

\node[step_box] at (7.5, 3.5) {
    2.next = 1\\
    prev = 2\\
    cur = 3\\
    next = null
};

% Final result
\node at (1, 2) {\tiny Final: [3→2→1]};
\node[node] (f3) at (2, 1.5) {3};
\node[node] (f2) at (3.5, 1.5) {2};
\node[node] (f1) at (5, 1.5) {1};

\draw[reverse_link] (f3) -- (f2);
\draw[reverse_link] (f2) -- (f1);

\end{tikzpicture}
```

**Key Insight:** The three pointers move in lockstep, each serving a specific purpose in the reversal process.

## 💡 LC206: Basic Linked List Reversal

### Problem Statement

Given the `head` of a singly linked list, reverse the list and return the reversed list.

### Iterative Solution

```python
# Definition for singly-linked list
class ListNode:
    def __init__(self, val=0, next=None):
        self.val = val
        self.next = next

class Solution:
    def reverseList(self, head: ListNode) -> ListNode:
        """
        Iterative approach using three pointers.

        Time: O(n), Space: O(1)
        Classic pointer manipulation technique.
        """
        prev = None
        current = head

        while current:
            # Step 1: Save next node before we lose it
            next_temp = current.next

            # Step 2: Reverse the current connection
            current.next = prev

            # Step 3: Move pointers forward
            prev = current
            current = next_temp

        # prev now points to the new head
        return prev
```

### Recursive Solution

```python
class Solution:
    def reverseList(self, head: ListNode) -> ListNode:
        """
        Recursive approach with elegant decomposition.

        Time: O(n), Space: O(n) due to call stack
        Beautiful but less efficient than iterative.
        """
        # Base case: empty list or single node
        if not head or not head.next:
            return head

        # Recursively reverse the rest of the list
        new_head = self.reverseList(head.next)

        # Reverse the current connection
        head.next.next = head
        head.next = None

        return new_head
```

## 🔄 LC92: Reverse Linked List II (Partial Reversal)

### Problem Statement

Given the head of a singly linked list and two integers `left` and `right` where `left <= right`, reverse the nodes of the list from position `left` to position `right`, and return the reversed list.

### Visualization of Partial Reversal

```tikz
\begin{tikzpicture}[
    node/.style={circle, draw, minimum width=0.8cm, font=\tiny, fill=blue!30},
    reverse_node/.style={circle, draw, minimum width=0.8cm, font=\tiny, fill=red!30},
    link/.style={->, thick, blue},
    reverse_link/.style={->, thick, red},
    example_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=4cm, align=center}
]

% Original list: [1,2,3,4,5], reverse from position 2 to 4
\node at (6, 8) {\bfseries LC92: Reverse from position 2 to 4};

\node at (1, 7) {\small Original: [1,2,3,4,5]};
\node[node] (o1) at (1, 6.5) {1};
\node[reverse_node] (o2) at (2.5, 6.5) {2};
\node[reverse_node] (o3) at (4, 6.5) {3};
\node[reverse_node] (o4) at (5.5, 6.5) {4};
\node[node] (o5) at (7, 6.5) {5};

\draw[link] (o1) -- (o2);
\draw[link] (o2) -- (o3);
\draw[link] (o3) -- (o4);
\draw[link] (o4) -- (o5);

\node at (1, 5) {\small Result: [1,4,3,2,5]};
\node[node] (r1) at (1, 4.5) {1};
\node[reverse_node] (r4) at (2.5, 4.5) {4};
\node[reverse_node] (r3) at (4, 4.5) {3};
\node[reverse_node] (r2) at (5.5, 4.5) {2};
\node[node] (r5) at (7, 4.5) {5};

\draw[link] (r1) -- (r4);
\draw[reverse_link] (r4) -- (r3);
\draw[reverse_link] (r3) -- (r2);
\draw[link] (r2) -- (r5);

\node[example_box] at (9, 6) {
    \textbf{Strategy:}\\
    1. Find position left-1\\
    2. Reverse left to right\\
    3. Connect properly\\[0.5em]
    \textbf{Key:}\\
    Reuse reverseN function
};

\end{tikzpicture}
```

### Implementation

```python
class Solution:
    def reverseBetween(self, head: ListNode, left: int, right: int) -> ListNode:
        """
        Reverse linked list from position left to right.

        Strategy: Find the start position, then use reverseN helper.
        """
        if left == 1:
            return self.reverseN(head, right)

        # Find the node before the reversal start
        head.next = self.reverseBetween(head.next, left - 1, right - 1)
        return head

    def reverseN(self, head: ListNode, n: int) -> ListNode:
        """
        Reverse first n nodes of the linked list.
        """
        if n == 1:
            self.successor = head.next
            return head

        last = self.reverseN(head.next, n - 1)
        head.next.next = head
        head.next = self.successor
        return last
```

## 🔥 LC25: Reverse Nodes in k-Group (Advanced Pattern)

### Problem Statement

Given the head of a linked list, reverse the nodes of the list k at a time, and return the modified list. If the number of nodes is not a multiple of k, leave the remaining nodes as they are.

### Visualization of K-Group Reversal

```tikz
\begin{tikzpicture}[
    node/.style={circle, draw, minimum width=0.8cm, font=\tiny, fill=blue!30},
    group1/.style={circle, draw, minimum width=0.8cm, font=\tiny, fill=red!30},
    group2/.style={circle, draw, minimum width=0.8cm, font=\tiny, fill=green!30},
    remaining/.style={circle, draw, minimum width=0.8cm, font=\tiny, fill=gray!30},
    link/.style={->, thick, blue},
    reverse_link/.style={->, thick, red},
    group_box/.style={rectangle, draw, dashed, fill=yellow!10}
]

% Original list: [1,2,3,4,5], k=2
\node at (6, 8) {\bfseries LC25: Reverse in groups of k=2};

\node at (1, 7) {\small Original: [1,2,3,4,5]};
\node[group1] (o1) at (1, 6.5) {1};
\node[group1] (o2) at (2.5, 6.5) {2};
\node[group2] (o3) at (4, 6.5) {3};
\node[group2] (o4) at (5.5, 6.5) {4};
\node[remaining] (o5) at (7, 6.5) {5};

\draw[link] (o1) -- (o2);
\draw[link] (o2) -- (o3);
\draw[link] (o3) -- (o4);
\draw[link] (o4) -- (o5);

% Group boundaries
\draw[group_box] (0.5, 6) rectangle (3, 7);
\draw[group_box] (3.5, 6) rectangle (6, 7);
\node at (1.75, 6.2) {\tiny Group 1};
\node at (4.75, 6.2) {\tiny Group 2};

\node at (1, 5) {\small Result: [2,1,4,3,5]};
\node[group1] (r2) at (1, 4.5) {2};
\node[group1] (r1) at (2.5, 4.5) {1};
\node[group2] (r4) at (4, 4.5) {4};
\node[group2] (r3) at (5.5, 4.5) {3};
\node[remaining] (r5) at (7, 4.5) {5};

\draw[reverse_link] (r2) -- (r1);
\draw[link] (r1) -- (r4);
\draw[reverse_link] (r4) -- (r3);
\draw[link] (r3) -- (r5);

\end{tikzpicture}
```

### Recursive Strategy Visualization

```tikz
\begin{tikzpicture}[
    step_box/.style={rectangle, draw, rounded corners, fill=green!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=2cm},
    arrow/.style={->, thick, purple, line width=2pt}
]

\node[step_box] (identify) at (0, 3) {
    \textbf{Step 1: Identify}\\[0.5em]
    Find first k nodes\\
    Check if we have\\
    enough nodes
};

\node[step_box] (reverse) at (4.5, 3) {
    \textbf{Step 2: Reverse}\\[0.5em]
    Reverse the first\\
    k nodes using\\
    reverseN function
};

\node[step_box] (recurse) at (9, 3) {
    \textbf{Step 3: Recurse}\\[0.5em]
    Recursively process\\
    remaining nodes\\
    and connect
};

\draw[arrow] (identify) -- (reverse);
\draw[arrow] (reverse) -- (recurse);

\node at (4.5, 1) {\bfseries Divide and Conquer: Process k nodes at a time};

\end{tikzpicture}
```

### Implementation

```python
class Solution:
    def reverseKGroup(self, head: ListNode, k: int) -> ListNode:
        """
        Reverse linked list in groups of k nodes.

        Strategy: Use recursion to break down the problem.
        """
        if not head:
            return None

        # Check if we have k nodes to reverse
        a = b = head
        for _ in range(k):
            if not b:
                return head  # Not enough nodes, return as is
            b = b.next

        # Reverse first k nodes
        new_head = self.reverseN(a, k)

        # Recursively reverse remaining nodes and connect
        a.next = self.reverseKGroup(b, k)

        return new_head

    def reverseN(self, head: ListNode, n: int) -> ListNode:
        """
        Reverse first n nodes of the linked list.
        """
        prev = None
        current = head

        while n > 0:
            next_temp = current.next
            current.next = prev
            prev = current
            current = next_temp
            n -= 1

        # Connect to the remaining part
        head.next = current
        return prev
```

## 🐍 The Pythonic Way: Elegant and Expressive

### Understanding Python's Advantages for Linked Lists

```tikz
\begin{tikzpicture}[
    insight_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2cm},
    comparison_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=5cm, align=center}
]

\node[insight_box] (traditional) at (0, 3) {
    \textbf{Traditional Approach:}\\[0.5em]
    "Manual pointer management\\
    with explicit null checks\\
    and careful ordering"\\[0.3em]
    \textit{Verbose but educational}
};

\node[insight_box] (pythonic) at (8, 3) {
    \textbf{Pythonic Approach:}\\[0.5em]
    "Use tuple unpacking,\\
    walrus operator,\\
    and clean abstractions"\\[0.3em]
    \textit{Concise and readable}
};

\node[comparison_box] at (4, 0.5) {
    \textbf{Key Insight:}\\
    Python's syntax allows for more expressive\\
    pointer manipulation while maintaining clarity\\[0.3em]
    \textit{Focus on algorithm logic, not implementation details}
};

\end{tikzpicture}
```

### Pythonic Implementations

```python
class Solution:
    def reverseList(self, head: ListNode) -> ListNode:
        """
        Pythonic iterative approach with tuple unpacking.
        """
        prev, curr = None, head

        while curr:
            curr.next, prev, curr = prev, curr, curr.next

        return prev

    def reverseListRecursive(self, head: ListNode) -> ListNode:
        """
        Pythonic recursive with early return pattern.
        """
        if not head or not head.next:
            return head

        new_head = self.reverseListRecursive(head.next)
        head.next.next, head.next = head, None

        return new_head
```

## 📚 Educational Philosophy: Pattern Mastery

### When to Use Each Approach

```tikz
\begin{tikzpicture}[
    approach_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4cm, align=center, minimum height=3cm},
    context_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=5cm, align=center}
]

\node[approach_box] (iterative) at (0, 3) {
    \textbf{Iterative Approach}\\[0.5em]
    \textbf{When to use:}\\
    • Technical interviews\\
    • Memory-constrained systems\\
    • Performance-critical code\\
    • Large linked lists\\[0.5em]
    \textbf{Benefits:}\\
    • O(1) space complexity\\
    • No stack overflow risk\\
    • Faster execution
};

\node[approach_box] (recursive) at (8, 3) {
    \textbf{Recursive Approach}\\[0.5em]
    \textbf{When to use:}\\
    • Learning recursion\\
    • Code elegance priority\\
    • Small to medium lists\\
    • Functional programming\\[0.5em]
    \textbf{Benefits:}\\
    • More intuitive logic\\
    • Easier to understand\\
    • Natural decomposition
};

\node[context_box] at (4, 0.5) {
    \textbf{Educational Insight:}\\
    Understanding both approaches helps you recognize\\
    when to use iteration vs recursion in general\\[0.3em]
    \textit{Master the fundamentals, then choose the right tool}
};

\end{tikzpicture}
```

### The Learning Journey

```tikz
\begin{tikzpicture}[
    stage_box/.style={rectangle, draw, rounded corners, fill=green!20, font=\sffamily\small, text width=3cm, align=center, minimum height=2cm},
    arrow/.style={->, thick, purple, line width=2pt}
]

\node[stage_box] (basic) at (0, 3) {
    \textbf{Stage 1:}\\
    \textbf{Basic Reversal}\\[0.5em]
    Master three-pointer\\
    technique and\\
    recursive thinking
};

\node[stage_box] (partial) at (4, 3) {
    \textbf{Stage 2:}\\
    \textbf{Partial Reversal}\\[0.5em]
    Handle subrange\\
    reversals and\\
    connection management
};

\node[stage_box] (advanced) at (8, 3) {
    \textbf{Stage 3:}\\
    \textbf{K-Group Reversal}\\[0.5em]
    Apply divide-and-conquer\\
    with recursive\\
    decomposition
};

\draw[arrow] (basic) -- (partial);
\draw[arrow] (partial) -- (advanced);

\node at (4, 1) {\bfseries The journey from basic pointer manipulation to advanced patterns};

\end{tikzpicture}
```

## 🧠 Key Takeaways & Problem-Solving Framework

### Why This Pattern is Educational

```tikz
\begin{tikzpicture}[
    principle_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=1.8cm},
    arrow/.style={->, thick, green}
]

\node[principle_box] (pointers) at (0, 3) {
    \textbf{Pointer Mastery}\\[0.3em]
    Learn safe pointer\\
    manipulation without\\
    losing references
};

\node[principle_box] (recursion) at (4.5, 3) {
    \textbf{Recursive Thinking}\\[0.3em]
    Understand problem\\
    decomposition and\\
    base case design
};

\node[principle_box] (patterns) at (9, 3) {
    \textbf{Pattern Recognition}\\[0.3em]
    Identify when to apply\\
    reversal techniques\\
    in complex problems
};

\draw[arrow] (pointers) -- (recursion);
\draw[arrow] (recursion) -- (patterns);

\node at (4.5, 1.5) {\bfseries Core Learning: Pointers → Recursion → Patterns};

\end{tikzpicture}
```

### Problem-Solving Pattern: [[Linked List Reversal]]

This pattern exemplifies **pointer manipulation mastery**:

1. **Pattern recognition**: Identify reversal requirements
2. **Approach selection**: Choose iterative vs recursive based on constraints
3. **Pointer management**: Safely navigate without losing references
4. **Edge case handling**: Empty lists, single nodes, insufficient nodes

### Complexity Analysis
- **Iterative**: Time O(n), Space O(1)
- **Recursive**: Time O(n), Space O(n) due to call stack

### Related Concepts for Obsidian

This pattern connects to several important concepts:

- **[[Pointer Manipulation]]**: Safe reference management
- **[[Recursion Patterns]]**: Problem decomposition techniques
- **[[Linked List Fundamentals]]**: Core data structure operations
- **[[Divide and Conquer]]**: Breaking problems into subproblems
- **[[Memory Management]]**: Understanding space complexity trade-offs

### Related Problems
- **LC24. Swap Nodes in Pairs**: Special case of k=2 reversal
- **LC61. Rotate List**: Circular manipulation
- **LC143. Reorder List**: Complex pointer rearrangement
- **LC234. Palindrome Linked List**: Reversal for comparison

### Implementation Tips

#### Pointer Management
- **Save before modify**: Always save next pointer before changing it
- **Three-pointer pattern**: prev, curr, next for safe traversal
- **Edge case handling**: Empty lists and single nodes
- **Connection management**: Properly link reversed portions

#### Recursive Design
- **Clear base cases**: Single node or empty list
- **Trust the recursion**: Don't trace through all levels
- **Proper connections**: Link current node to recursive result
- **Space awareness**: Consider call stack limitations

#### Python-Specific Optimizations
- **Tuple unpacking**: Elegant multiple assignment
- **Walrus operator**: Inline assignment for cleaner code
- **Early returns**: Pythonic guard clauses
- **List comprehensions**: When converting to/from lists

### Edge Cases to Consider
- **Empty list**: Return None gracefully
- **Single node**: No reversal needed
- **Two nodes**: Simple swap case
- **Insufficient nodes**: For k-group, return unchanged
- **Memory constraints**: Choose iterative for large lists

This pattern beautifully demonstrates how **fundamental pointer manipulation** combined with **recursive thinking** can solve increasingly complex problems, showcasing the power of **building from simple to sophisticated** in algorithm design!
```
