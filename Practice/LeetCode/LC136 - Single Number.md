---
tags: [problem/leetcode, lc/easy, topic/array, topic/hash_table, topic/bit_manipulation, pattern/frequency_counting]
aliases: [LC136, LeetCode 136. Single Number]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 136. Single Number
> Hash map solution adapted from [[labuladong 的算法笔记/markdown_export_本站简介/入门：编程语言基础及练习/编程语言刷题实践.md]]. The problem also has a famous bit manipulation (XOR) solution.

# LeetCode 136: Single Number

## Problem Statement

Given a **non-empty** array of integers `nums`, every element appears *twice* except for one. Find that single one.

You must implement a solution with a linear runtime complexity and use only constant extra space. (The hash map solution below meets linear time but not constant space. The XOR solution meets both.)

**Official Link:** [LeetCode 136. Single Number](https://leetcode.com/problems/single-number/)

**Example 1:**
Input: `nums = [2,2,1]`
Output: `1`

**Example 2:**
Input: `nums = [4,1,2,1,2]`
Output: `4`

## Solutions (Python)

### 1. Hash Map (Dictionary) for Frequency Counting

This approach uses a hash map to count the occurrences of each number. Then, iterate through the map to find the number with a count of 1. This is an application of the [[Interview/Concept/Algorithms/Hashing/Frequency Counting with Hash Maps|Frequency Counting with Hash Maps]] pattern.

```python
from collections import Counter # For a more concise way to count

class Solution:
    def singleNumber_hashmap(self, nums: list[int]) -> int:
        counts = {} # Or use collections.Counter(nums)
        for num in nums:
            counts[num] = counts.get(num, 0) + 1
        
        for num, count in counts.items():
            if count == 1:
                return num
        return -1 # Should not be reached given problem constraints
    
    # Alternative using collections.Counter
    def singleNumber_counter(self, nums: list[int]) -> int:
        counts = Counter(nums) # Efficiently counts frequencies
        for num, count in counts.items():
            if count == 1:
                return num
        return -1 # Should not be reached
```

**Explanation (Hash Map):**
1.  Initialize an empty dictionary `counts`. This dictionary is an instance of a [[Interview/Concept/Programming Languages/Python/05 - Python Dict (Hash Map) for Interviews|Python Dict]].
2.  Iterate through the `nums` array. For each `num`:
    - Increment its count in the `counts` dictionary. `counts.get(num, 0)` retrieves the current count of `num`, or `0` if `num` is not yet in the dictionary.
3.  After populating the `counts` dictionary, iterate through its items (key-value pairs).
4.  If a `num` has a `count` of 1, that is the single number. Return it.

**Complexity Analysis (Hash Map):**
-   **Time Complexity:** $O(N)$, where $N$ is the number of elements in `nums`. The first loop to populate the hash map takes $O(N)$. The second loop to find the single number also takes $O(N)$ in the worst case (if the map contains up to $N/2 + 1$ unique elements).
-   **Space Complexity:** $O(N)$ in the worst case (e.g. if nums is `[1,2,3,4,5,1,2,3,4]`, map stores 5 unique keys). More precisely, $O(M)$ where $M$ is the number of unique elements. Given the problem, $M \approx N/2$.

### 2. Bit Manipulation (XOR) - Optimal Solution

This problem has a very elegant solution using the XOR bitwise operator.
Properties of XOR:
-   `x ^ x = 0` (any number XORed with itself is 0)
-   `x ^ 0 = x` (any number XORed with 0 is itself)
-   XOR is commutative and associative (`a ^ b ^ c = a ^ c ^ b`)

If we XOR all numbers in the array together:
-   Pairs of numbers that appear twice will cancel each other out (e.g., `a ^ b ^ a = (a ^ a) ^ b = 0 ^ b = b`).
-   The single number, appearing only once, will remain.

```python
class Solution:
    def singleNumber(self, nums: list[int]) -> int:
        single = 0
        for num in nums:
            single ^= num
        return single
```

*More Elegant Solution using `functools`*
```python
class Solution:

	def singleNumber(self, nums: List[int]) -> int:
	
		return functools.reduce(lambda x, y: x ^ y, nums)
```

**Explanation (XOR):**
1.  Initialize a variable `single` to 0.
2.  Iterate through each `num` in `nums`.
3.  Perform `single = single ^ num`.
4.  After iterating through all numbers, `single` will hold the value of the element that appears only once.

**Example Walkthrough (XOR):** `nums = [4,1,2,1,2]`
- `single = 0`
- `num = 4`: `single = 0 ^ 4 = 4`
- `num = 1`: `single = 4 ^ 1`
- `num = 2`: `single = 4 ^ 1 ^ 2`
- `num = 1`: `single = 4 ^ 1 ^ 2 ^ 1 = 4 ^ (1 ^ 1) ^ 2 = 4 ^ 0 ^ 2 = 4 ^ 2`
- `num = 2`: `single = 4 ^ 2 ^ 2 = 4 ^ (2 ^ 2) = 4 ^ 0 = 4`
- Result: `4`

**Complexity Analysis (XOR):**
-   **Time Complexity:** $O(N)$, as we iterate through the array once.
-   **Space Complexity:** $O(1)$, as we only use a single variable to store the XOR sum. This meets the problem's constraint.

## 总结 (Summary)
- The "Single Number" problem asks to find the unique element in an array where all other elements appear twice.
- A hash map approach can solve it in $O(N)$ time and $O(N)$ space by counting frequencies.
- The optimal solution uses the XOR bitwise operator, achieving $O(N)$ time and $O(1)$ space complexity, which perfectly fits the problem constraints.

---
Previous: [[Interview/Practice/LeetCode/LC217 - Contains Duplicate|LC217 - Contains Duplicate]]
Next: [[Interview/Practice/LeetCode/LC20 - Valid Parentheses|LC20 - Valid Parentheses]]
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Hashing/Frequency Counting with Hash Maps|Frequency Counting]], [[Interview/Concept/Algorithms/Bit Manipulation/XOR for Finding Unique Element|XOR for Finding Unique Element]], [[Interview/Concept/Programming Languages/Python/05 - Python Dict (Hash Map) for Interviews|Python Dict]]
