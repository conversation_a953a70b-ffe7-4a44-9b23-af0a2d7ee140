---
tags: [problem/leetcode, lc/hard, topic/heap, topic/data_structure_design, pattern/two_heaps]
aliases: [LC295, Find Median from Data Stream]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/拓展：两个二叉堆实现中位数算法.md
---

# LC295 - Find Median from Data Stream

## Problem Statement
(To be filled from LeetCode - Visit [https://leetcode.com/problems/find-median-from-data-stream/](https://leetcode.com/problems/find-median-from-data-stream/))

## Solution Approach
This problem is solved using the two-heap technique described in [[Interview/Concept/Data Structures/Heap/02 - Two Heaps for Median (MedianFinder)|Two Heaps for Median (MedianFinder)]].

### Python Solution (from Concept Note)
```python
import heapq

class MedianFinder:
    def __init__(self):
        self.small_half_max_heap = []  # stores smaller half, max-heap (negate values)
        self.large_half_min_heap = []  # stores larger half, min-heap

    def addNum(self, num: int) -> None:
        # Goal: small_half_max_heap.size() is either equal to or one greater than large_half_min_heap.size()

        if not self.small_half_max_heap or num <= -self.small_half_max_heap[0]:
            heapq.heappush(self.small_half_max_heap, -num)
        else:
            heapq.heappush(self.large_half_min_heap, num)
        
        # Balance the heaps
        if len(self.small_half_max_heap) < len(self.large_half_min_heap):
            heapq.heappush(self.small_half_max_heap, -heapq.heappop(self.large_half_min_heap))
        elif len(self.small_half_max_heap) > len(self.large_half_min_heap) + 1:
            heapq.heappush(self.large_half_min_heap, -heapq.heappop(self.small_half_max_heap))

    def findMedian(self) -> float:
        if len(self.small_half_max_heap) > len(self.large_half_min_heap):
            return float(-self.small_half_max_heap[0])
        else: # len(self.small_half_max_heap) == len(self.large_half_min_heap)
            return (-self.small_half_max_heap[0] + self.large_half_min_heap[0]) / 2.0

# Your MedianFinder object will be instantiated and called as such:
# obj = MedianFinder()
# obj.addNum(num)
# param_2 = obj.findMedian()
```

## Complexity Analysis
- `addNum`: $O(\log N)$
- `findMedian`: $O(1)$
- Space: $O(N)$

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
