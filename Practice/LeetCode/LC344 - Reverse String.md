---
tags: [problem/leetcode, lc/easy, topic/array, topic/string, topic/two_pointers, pattern/left_right_pointers]
aliases: [LC344, LeetCode 344. Reverse String, 反转字符串]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 344. Reverse String
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道数组题目.md]].

# LeetCode 344: Reverse String

## Problem Statement

Write a function that reverses a string. The input string is given as an array of characters `s`.
You must do this by modifying the input array **in-place** with $O(1)$ extra memory.

**Official Link:** [LeetCode 344. Reverse String](https://leetcode.com/problems/reverse-string/)

## Solution Approach: Left-Right Two Pointers

This problem is a straightforward application of the left-right two-pointer technique for in-place array modification.
- `left` pointer: Starts at the beginning of the array (index 0).
- `right` pointer: Starts at the end of the array (index `len(s) - 1`).

1. While `left < right`:
   a. Swap the characters `s[left]` and `s[right]`.
   b. Move `left` one step to the right (`left += 1`).
   c. Move `right` one step to the left (`right -= 1`).
2. When `left` meets or crosses `right`, the array is reversed.

### Python Solution (Labuladong's style)
```python
class Solution:
    def reverseString(self, s: list[str]) -> None:
        '''
        Do not return anything, modify s in-place instead.
        '''
        left, right = 0, len(s) - 1
        while left < right:
            # Swap s[left] and s[right]
            s[left], s[right] = s[right], s[left]
            left += 1
            right -= 1
```

## Complexity Analysis
- **Time Complexity:** $O(N)$, where $N$ is the length of the character array `s`. We iterate through roughly half of the array elements for swapping.
- **Space Complexity:** $O(1)$, as the reversal is done in-place.

## Visualization

`s = ["h","e","l","l","o"]`

1.  `left=0 ("h")`, `right=4 ("o")`. `left < right`.
    - Swap `s[0]` and `s[4]`. `s` becomes `["o","e","l","l","h"]`.
    - `left=1`, `right=3`.
    ```tikz
    \begin{tikzpicture}[
        char_cell/.style={draw, rectangle, minimum size=0.7cm, font=\sffamily\small},
        ptr_label/.style={font=\sffamily\bfseries\tiny, below=0.1cm}
    ]
        \node[char_cell] (c0) at (0*1,0) {o}; \node[ptr_label,red] at (c0.south) {L};
        \node[char_cell] (c1) at (1*1,0) {e};
        \node[char_cell] (c2) at (2*1,0) {l};
        \node[char_cell] (c3) at (3*1,0) {l};
        \node[char_cell] (c4) at (4*1,0) {h}; \node[ptr_label,blue] at (c4.south) {R};
        \node at (2, -1) {After 1st swap: L=0, R=4 $\rightarrow$ s=["o",_,_,_,"h"]. New L=1, R=3.};
    \end{tikzpicture}
    ```

2.  `left=1 ("e")`, `right=3 ("l")`. `left < right`.
    - Swap `s[1]` and `s[3]`. `s` becomes `["o","l","l","e","h"]`.
    - `left=2`, `right=2`.
    ```tikz
    \begin{tikzpicture}[
        char_cell/.style={draw, rectangle, minimum size=0.7cm, font=\sffamily\small},
        ptr_label/.style={font=\sffamily\bfseries\tiny, below=0.1cm}
    ]
        \node[char_cell] (c0) at (0*1,0) {o};
        \node[char_cell] (c1) at (1*1,0) {l}; \node[ptr_label,red] at (c1.south) {L};
        \node[char_cell] (c2) at (2*1,0) {l};
        \node[char_cell] (c3) at (3*1,0) {e}; \node[ptr_label,blue] at (c3.south) {R};
        \node[char_cell] (c4) at (4*1,0) {h};
        \node at (2, -1) {After 2nd swap: L=1, R=3 $\rightarrow$ s=["o","l",_,"e","h"]. New L=2, R=2.};
    \end{tikzpicture}
    ```
3.  `left=2 ("l")`, `right=2 ("l")`. `left < right` is false. Loop terminates.
    ```tikz
    \begin{tikzpicture}[
        char_cell/.style={draw, rectangle, minimum size=0.7cm, font=\sffamily\small},
        ptr_label/.style={font=\sffamily\bfseries\tiny, below=0.1cm}
    ]
        \node[char_cell] (c0) at (0*1,0) {o};
        \node[char_cell] (c1) at (1*1,0) {l};
        \node[char_cell] (c2) at (2*1,0) {l}; \node[ptr_label,red] at (c2.south) {L,R};
        \node[char_cell] (c3) at (3*1,0) {e};
        \node[char_cell] (c4) at (4*1,0) {h};
        \node at (2, -1) {L=2, R=2. Condition `L < R` fails. Loop ends.};
    \end{tikzpicture}
    ```
Final `s = ["o","l","l","e","h"]`.

## 总结 (Summary)
- LC344 is a basic application of the left-right two-pointer technique for in-place array reversal.
- Pointers start at opposite ends of the array and move towards each other, swapping elements.
- The solution achieves $O(N)$ time complexity and $O(1)$ space complexity as required.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]] (Left-Right Pointers)
