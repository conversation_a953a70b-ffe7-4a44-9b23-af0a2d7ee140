---
tags: [problem/leetcode, lc/easy, topic/data_structure_design, pattern/stack_from_queues, course/labuladong]
aliases: [LC225, Implement Stack using Queues]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典队列_栈算法/队列实现栈以及栈实现队列.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 225. Implement Stack using Queues
> Solution approach adapted from [[Interview/Concept/Data Structures/Stack/Applications/00 - Stack and Queue Inter-conversion|Stack and Queue Inter-conversion]].

# LeetCode 225: Implement Stack using Queues

## Problem Statement
Implement a last-in, first-out (LIFO) stack using only two queues. The implemented stack should support all the functions of a normal stack (`push`, `top`, `pop`, and `empty`).

Implement the `MyStack` class:
- `void push(int x)` Pushes element x to the top of the stack.
- `int pop()` Removes the element on the top of the stack and returns it.
- `int top()` Returns the element on the top of the stack.
- `boolean empty()` Returns true if the stack is empty, false otherwise.

**Official Link:** [LeetCode 225. Implement Stack using Queues](https://leetcode.com/problems/implement-stack-using-queues/)

## Solution Approach: Using One Queue

As discussed in [[Interview/Concept/Data Structures/Stack/Applications/00 - Stack and Queue Inter-conversion|Stack and Queue Inter-conversion]], a stack can be implemented using a single queue. The core idea is to re-arrange elements within the queue upon push such that the most recently pushed element is always at the front of the queue.

1.  **`push(x)`:**
    - Add `x` to the back of the queue.
    - Then, for `size_of_queue - 1` times, dequeue an element from the front and re-enqueue it at the back. This effectively moves all elements that were in the queue before `x` to be behind `x`.
2.  **`pop()`:** Dequeue from the front of the queue (`queue.popleft()`).
3.  **`top()`:** Peek at the front element of the queue (`queue[0]`).
4.  **`empty()`:** Check if the queue is empty.

### Python Solution (One Queue)
```python
from collections import deque

class MyStack:
    def __init__(self):
        self.q = deque()
        # Labuladong's MyStack in the article also tracks top_elem for O(1) top
        # self.top_elem = 0 

    def push(self, x: int) -> None:
        self.q.append(x)
        # self.top_elem = x # Keep track of the latest element for O(1) top

        # Rotate queue to make x the front element
        # This makes push O(N)
        for _ in range(len(self.q) - 1):
            self.q.append(self.q.popleft())

        # Alternative from article: Maintain top_elem for O(1) top,
        # then pop needs O(N) rotation to expose the new top_elem.
        # Let's follow the push-O(N) approach here for pop/top O(1).


    def pop(self) -> int:
        if not self.empty():
            return self.q.popleft()
        return -1 # Or raise error

    def top(self) -> int:
        if not self.empty():
            return self.q[0]
        return -1 # Or raise error

    def empty(self) -> bool:
        return len(self.q) == 0

# Your MyStack object will be instantiated and called as such:
# obj = MyStack()
# obj.push(x)
# param_2 = obj.pop()
# param_3 = obj.top()
# param_4 = obj.empty()
```
The Labuladong article presents a slightly different single-queue approach:
- **`push(x)`**: Simply `q.offer(x)` and update `top_elem = x`. This is $O(1)$.
- **`pop()`**: To get the LIFO element (which is `top_elem`), all other elements must be dequeued and re-enqueued. The second-to-last element becomes the new `top_elem`. This makes `pop()` $O(N)$.
- **`top()`**: Returns `top_elem`. $O(1)$.
- **`empty()`**: `q.isEmpty()`. $O(1)$.

This trade-off depends on which operation (push or pop) you want to be faster. The solution above makes `push` $O(N)$ and `pop/top` $O(1)$. Labuladong's article code makes `push/top` $O(1)$ and `pop` $O(N)$. Both are valid.

**Labuladong's Article's Single Queue Logic:**
```python
from collections import deque

class MyStackLabuladong:
    def __init__(self):
        self.q = deque()
        self.top_elem = 0

    def push(self, x: int) -> None:
        self.q.append(x)
        self.top_elem = x # x is at queue tail, considered stack top

    def pop(self) -> int: # O(N)
        size = len(self.q)
        # Move all but last two elements to back of queue
        while size > 2:
            self.q.append(self.q.popleft())
            size -= 1

        # The element before the original tail is the new top_elem
        if len(self.q) > 1: # If there are at least two elements
            self.top_elem = self.q[0] # The one that will be next after popping current head
            self.q.append(self.q.popleft()) # Move it to tail
            return self.q.popleft() # Pop the original tail (which was stack top)
        elif len(self.q) == 1: # Only one element left
            # self.top_elem will be undefined or 0 after this.
            # No new top_elem to set if stack becomes empty.
            return self.q.popleft()
        return -1 # Should not happen if pop called on non-empty stack

    def top(self) -> int: # O(1)
        return self.top_elem

    def empty(self) -> bool:
        return not self.q
```

## Complexity Analysis
**Using One Queue (Push $O(N)$, Pop/Top $O(1)$):**
- `push`: $O(N)$ due to rotation.
- `pop`: $O(1)$.
- `top`: $O(1)$.
- `empty`: $O(1)$.

**Using One Queue (Labuladong's code style - Push $O(1)$, Pop $O(N)$, Top $O(1)$):**
- `push`: $O(1)$.
- `pop`: $O(N)$ due to rotation.
- `top`: $O(1)$.
- `empty`: $O(1)$.

## 总结 (Summary)
- A stack can be implemented using one queue by reordering elements on push (making push $O(N)$) or on pop (making pop $O(N)$).
- The choice depends on desired performance characteristics for push vs. pop.
- This tests understanding of queue FIFO behavior and how to simulate LIFO using it.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
