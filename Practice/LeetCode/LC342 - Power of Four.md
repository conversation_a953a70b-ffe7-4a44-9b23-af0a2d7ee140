---
tags: [problem/leetcode, lc/easy, topic/bit_manipulation, topic/math, pattern/power_detection, pattern/bit_tricks]
aliases: [LC342, LeetCode 342, Power of Four, 4的幂]
---

# LeetCode 342: Power of Four

## Problem Statement

Given an integer `n`, return `true` if it is a power of four. Otherwise, return `false`.

An integer `n` is a power of four, if there exists an integer `x` such that `n == 4^x`.

**Examples:**
- Input: `n = 16`, Output: `true` (4² = 16)
- Input: `n = 5`, Output: `false`
- Input: `n = 1`, Output: `true` (4⁰ = 1)

**Constraints:** -2³¹ ≤ n ≤ 2³¹ - 1

**Follow up:** Could you solve it without loops/recursion?

**Official Link:** [LeetCode 342. Power of Four](https://leetcode.com/problems/power-of-four/)

## 🎯 Understanding the Power Detection Problem

### The Real-World Scenario

Imagine you're designing a computer graphics system that uses texture sizes:

```tikz
\begin{tikzpicture}[
    texture/.style={rectangle, draw, minimum width=1cm, minimum height=1cm, font=\tiny, fill=blue!30},
    valid/.style={rectangle, draw, minimum width=1cm, minimum height=1cm, font=\tiny, fill=green!40},
    invalid/.style={rectangle, draw, minimum width=1cm, minimum height=1cm, font=\tiny, fill=red!40},
    example_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=4cm, align=center}
]

% Example texture sizes
\node at (6, 8) {\bfseries Graphics Texture Validation: Powers of 4};

% Valid texture sizes (powers of 4)
\node at (1, 7) {\small Valid sizes:};
\node[valid] (t1) at (1, 6.5) {1×1};
\node[valid] (t2) at (2.5, 6.5) {2×2};
\node[valid] (t3) at (4, 6.5) {4×4};
\node[valid] (t4) at (5.5, 6.5) {8×8};
\node[valid] (t5) at (7, 6.5) {16×16};

% Powers shown
\node at (1, 6) {\tiny 4\^{}0=1};
\node at (2.5, 6) {\tiny 4\^{}1=4};
\node at (4, 6) {\tiny 4\^{}2=16};
\node at (5.5, 6) {\tiny 4\^{}3=64};
\node at (7, 6) {\tiny 4\^{}4=256};

% Invalid sizes
\node at (1, 5) {\small Invalid sizes:};
\node[invalid] (i1) at (1, 4.5) {3×3};
\node[invalid] (i2) at (2.5, 4.5) {5×5};
\node[invalid] (i3) at (4, 4.5) {6×6};
\node[invalid] (i4) at (5.5, 4.5) {7×7};
\node[invalid] (i5) at (7, 4.5) {12×12};

\node[example_box] at (9, 6) {
    \textbf{Goal:}\\
    Quickly validate if\\
    a texture size is\\
    a power of 4\\[0.5em]
    \textbf{Challenge:}\\
    Do it without loops\\
    or recursion!
};

\node[example_box] at (9, 3) {
    \textbf{Key Insight:}\\
    This is about\\
    bit manipulation\\
    and mathematical\\
    properties!
};

\end{tikzpicture}
```

**Critical Insight:** This is a **[[Bit Manipulation]]** problem! Powers of 4 have special binary patterns that we can detect using bitwise operations.

## 🧠 The Breakthrough: From Math to Bit Patterns

### Step 1: Understanding Powers of 4 in Binary

The key realization is to examine the binary representation of powers of 4:

```tikz
\begin{tikzpicture}[
    insight_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4.5cm, align=center, minimum height=2.5cm},
    arrow/.style={->, thick, red, line width=2pt},
    pattern_box/.style={rectangle, draw, fill=orange!20, font=\sffamily\small, text width=6cm, align=center}
]

\node[insight_box] (math) at (0, 3) {
    \textbf{Mathematical View:}\\[0.5em]
    "Check if n = 4\^{}x\\
    for some integer x"\\[0.5em]
    Requires division\\
    or logarithms!
};

\node[insight_box] (binary) at (8, 3) {
    \textbf{Binary Pattern View:}\\[0.5em]
    "Powers of 4 have\\
    specific bit patterns"\\[0.5em]
    Use bitwise operations!\\
    O(1) solution!
};

\draw[arrow] (math) -- (binary);

\node[pattern_box] at (4, 0.5) {
    \textbf{Binary Pattern Discovery:}\\
    4\^{}0 = 1 = 0001, 4\^{}1 = 4 = 0100, 4\^{}2 = 16 = 10000\\
    Pattern: Single bit at even positions (0, 2, 4, 6, ...)
};

\end{tikzpicture}
```

### Step 2: Analyzing the Binary Patterns

Let's examine the binary representation of powers of 4:

```tikz
\begin{tikzpicture}[
    binary_box/.style={rectangle, draw, rounded corners, fill=green!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=2cm},
    formula_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=5cm, align=center}
]

\node[binary_box] (pow0) at (0, 3) {
    \textbf{4\^{}0 = 1:}\\[0.5em]
    Binary: 0001\\
    Bit position: 0\\[0.3em]
    Even position: YES
};

\node[binary_box] (pow1) at (4.5, 3) {
    \textbf{4\^{}1 = 4:}\\[0.5em]
    Binary: 0100\\
    Bit position: 2\\[0.3em]
    Even position: YES
};

\node[binary_box] (pow2) at (9, 3) {
    \textbf{4\^{}2 = 16:}\\[0.5em]
    Binary: 10000\\
    Bit position: 4\\[0.3em]
    Even position: YES
};

\node[formula_box] at (4.5, 0.5) {
    \textbf{Key Pattern Discovery:}\\
    Powers of 4 have exactly one bit set,\\
    and that bit is at an even position (0, 2, 4, 6, ...)\\[0.3em]
    This gives us our O(1) detection algorithm!
};

\end{tikzpicture}
```

**Key Insight:** Powers of 4 are powers of 2 with the single bit at an even position. This leads to multiple elegant detection methods.

## 🔍 Progressive Algorithm Development

### Step 3: Multiple Detection Strategies

We can detect powers of 4 using different approaches:

```tikz
\begin{tikzpicture}[
    strategy_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=2cm},
    arrow/.style={->, thick, purple}
]

\node[strategy_box] (basic) at (0, 3) {
    \textbf{Basic Math}\\[0.5em]
    Use division or\\
    logarithms\\[0.3em]
    Simple but slower
};

\node[strategy_box] (bit_basic) at (4.5, 3) {
    \textbf{Bit Manipulation}\\[0.5em]
    Check power of 2\\
    + even bit position\\[0.3em]
    Fast and elegant
};

\node[strategy_box] (bit_advanced) at (9, 3) {
    \textbf{Bit Mask Trick}\\[0.5em]
    Single bitwise\\
    operation\\[0.3em]
    Ultra-efficient
};

\draw[arrow] (basic) -- (bit_basic);
\draw[arrow] (bit_basic) -- (bit_advanced);

\node at (4.5, 1) {\bfseries Evolution from simple to sophisticated bit manipulation};

\end{tikzpicture}
```

## 💡 Traditional Implementation: Step by Step

### Step 1: Basic Mathematical Approach

```python
import math

def is_power_of_four_math(n):
    """
    Basic mathematical approach using logarithms.

    Time: O(1), Space: O(1)
    Simple but involves floating point arithmetic.
    """
    if n <= 0:
        return False

    # Calculate log base 4 of n
    log_result = math.log(n) / math.log(4)

    # Check if it's an integer (within floating point precision)
    return abs(log_result - round(log_result)) < 1e-10
```

### Step 2: Iterative Division Approach

```python
def is_power_of_four_division(n):
    """
    Iterative approach using division.

    Time: O(log n), Space: O(1)
    Clear logic but not O(1) time.
    """
    if n <= 0:
        return False

    while n > 1:
        if n % 4 != 0:
            return False
        n //= 4

    return n == 1
```

### Step 3: Bit Manipulation Approach

```python
def is_power_of_four_bits(n):
    """
    Bit manipulation approach using power of 2 check + even position.

    Time: O(1), Space: O(1)
    Elegant and efficient.
    """
    if n <= 0:
        return False

    # Check if n is a power of 2
    if n & (n - 1) != 0:
        return False

    # Check if the single bit is at an even position
    # Count trailing zeros - should be even for powers of 4
    trailing_zeros = 0
    temp = n
    while temp & 1 == 0:
        trailing_zeros += 1
        temp >>= 1

    return trailing_zeros % 2 == 0
```

### Step 4: Complete Traditional Solution

```python
class Solution:
    def isPowerOfFour(self, n: int) -> bool:
        """
        Traditional bit manipulation approach.

        Algorithm:
        1. Check if n is positive
        2. Check if n is a power of 2: n & (n-1) == 0
        3. Check if the bit is at even position using bit mask
        """

        if n <= 0:
            return False

        # Check if n is a power of 2
        if n & (n - 1) != 0:
            return False

        # Check if the single bit is at an even position
        # Use mask 0x55555555 = 01010101010101010101010101010101
        # This mask has 1s at even positions (0, 2, 4, 6, ...)
        return (n & 0x55555555) != 0
```

## 🐍 The Pythonic Way: Elegant and Expressive

### Understanding Python's Mathematical Power

Python provides elegant tools that make power detection much cleaner:

```tikz
\begin{tikzpicture}[
    insight_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2cm},
    comparison_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=5cm, align=center}
]

\node[insight_box] (traditional) at (0, 3) {
    \textbf{Traditional Approach:}\\[0.5em]
    "Manual bit manipulation\\
    with explicit masks\\
    and position checking"\\[0.3em]
    \textit{Verbose but educational}
};

\node[insight_box] (pythonic) at (8, 3) {
    \textbf{Pythonic Approach:}\\[0.5em]
    "Use built-in functions,\\
    mathematical properties,\\
    and elegant expressions"\\[0.3em]
    \textit{Concise and readable}
};

\node[comparison_box] at (4, 0.5) {
    \textbf{Key Insight:}\\
    Python's expressiveness allows multiple elegant solutions\\
    while maintaining mathematical clarity\\[0.3em]
    \textit{Choose the approach that best fits your context}
};

\end{tikzpicture}
```

### Approach 1: Mathematical Elegance

```python
import math

class Solution:
    def isPowerOfFour(self, n: int) -> bool:
        """
        Pythonic approach using mathematical properties.

        Key improvements:
        - Uses math.log for clear mathematical intent
        - Handles edge cases elegantly
        - Readable and maintainable
        """

        # Guard clause for non-positive numbers
        if n <= 0:
            return False

        # Use logarithm base 4 to check if result is integer
        log_result = math.log(n, 4)
        return log_result == int(log_result)
```

### Approach 2: Bit Manipulation with Python Idioms

```python
class Solution:
    def isPowerOfFour(self, n: int) -> bool:
        """
        Pythonic bit manipulation using Python's integer methods.

        Features:
        - Uses bit_count() method (Python 3.10+)
        - Clean boolean expressions
        - Leverages Python's arbitrary precision integers
        """

        # One-liner with clear logic
        return (n > 0 and
                n.bit_count() == 1 and  # Exactly one bit set (power of 2)
                (n - 1) % 3 == 0)        # Mathematical property of powers of 4
```

### Approach 3: Pattern Matching with Walrus Operator

```python
class Solution:
    def isPowerOfFour(self, n: int) -> bool:
        """
        Modern Python approach using walrus operator and pattern matching.

        Features:
        - Walrus operator for concise variable assignment
        - Pattern-based thinking
        - Functional style
        """

        # Check if n is positive power of 2, then verify it's power of 4
        return (n > 0 and
                (n & (n - 1)) == 0 and  # Power of 2 check
                (trailing_zeros := (n - 1).bit_length()) % 2 == 0)
```

### Approach 4: One-Liner Approaches

```python
class Solution:
    def isPowerOfFour(self, n: int) -> bool:
        """
        Ultra-concise approaches showcasing Python's expressiveness.

        Multiple one-liner solutions with different insights.
        """

        # Approach 4a: Mathematical property
        return n > 0 and (n & (n - 1)) == 0 and (n - 1) % 3 == 0

        # Approach 4b: Bit mask approach
        return n > 0 and (n & (n - 1)) == 0 and (n & 0x55555555) != 0

        # Approach 4c: String pattern matching (creative but inefficient)
        return n > 0 and bin(n).count('1') == 1 and bin(n)[::-1].find('1') % 2 == 0
```

### Approach 5: Functional Programming Style

```python
from functools import reduce
from operator import and_

class Solution:
    def isPowerOfFour(self, n: int) -> bool:
        """
        Functional programming approach with composable predicates.

        Benefits:
        - Highly readable predicate composition
        - Easy to test individual conditions
        - Functional programming paradigm
        """

        def is_positive(x):
            return x > 0

        def is_power_of_two(x):
            return x & (x - 1) == 0

        def has_even_bit_position(x):
            return (x & 0x55555555) != 0

        # Compose all predicates
        predicates = [is_positive, is_power_of_two, has_even_bit_position]
        return reduce(and_, (pred(n) for pred in predicates), True)
```

## 🔍 Visual Algorithm Trace

Let's trace through the bit manipulation algorithm with concrete examples:

```tikz
\begin{tikzpicture}[
    number_box/.style={rectangle, draw, minimum width=1.5cm, minimum height=0.8cm, font=\sffamily\small, fill=blue!30},
    binary_box/.style={rectangle, draw, minimum width=3cm, minimum height=0.8cm, font=\tiny, fill=green!40},
    result_box/.style={rectangle, draw, minimum width=2cm, minimum height=0.8cm, font=\tiny, fill=yellow!30},
    step_box/.style={rectangle, draw, fill=orange!20, font=\tiny, text width=3cm, align=center}
]

% Algorithm trace for multiple examples
\node at (6, 9) {\bfseries Bit Manipulation Algorithm Trace};

% Example 1: n = 16 (power of 4)
\node at (1, 8) {\small Example 1: n = 16};
\node[number_box] (n1) at (1, 7.5) {16};
\node[binary_box] (b1) at (3, 7.5) {10000};
\node[result_box] (r1) at (6.5, 7.5) {TRUE};

% Example 2: n = 8 (power of 2, not 4)
\node at (1, 6.5) {\small Example 2: n = 8};
\node[number_box] (n2) at (1, 6) {8};
\node[binary_box] (b2) at (3, 6) {01000};
\node[result_box] (r2) at (6.5, 6) {FALSE};

% Example 3: n = 5 (not power of 2)
\node at (1, 5) {\small Example 3: n = 5};
\node[number_box] (n3) at (1, 4.5) {5};
\node[binary_box] (b3) at (3, 4.5) {00101};
\node[result_box] (r3) at (6.5, 4.5) {FALSE};

% Step-by-step breakdown
\node[step_box] at (9, 7.5) {
    \textbf{n = 16:}\\
    1. n > 0? YES\\
    2. n AND (n-1) = 0? YES\\
    3. Bit at even pos? YES\\
    Result: TRUE
};

\node[step_box] at (9, 6) {
    \textbf{n = 8:}\\
    1. n > 0? YES\\
    2. n AND (n-1) = 0? YES\\
    3. Bit at even pos? NO\\
    Result: FALSE
};

\node[step_box] at (9, 4.5) {
    \textbf{n = 5:}\\
    1. n > 0? YES\\
    2. n AND (n-1) = 0? NO\\
    3. Skip remaining\\
    Result: FALSE
};

% Bit position analysis
\node at (1, 3) {\small Bit Position Analysis:};
\node at (1, 2.5) {\tiny 16 = 10000 - bit at position 4 (even) YES};
\node at (1, 2) {\tiny 8 = 01000 - bit at position 3 (odd) NO};
\node at (1, 1.5) {\tiny 5 = 00101 - multiple bits set NO};

\end{tikzpicture}
```

**Key Observations:**
1. **Positive check**: Eliminates negative numbers and zero
2. **Power of 2 check**: `n & (n-1) == 0` ensures exactly one bit is set
3. **Even position check**: Bit mask `0x55555555` checks if bit is at even position
4. **Short-circuit evaluation**: Early termination for efficiency

## 📚 Educational Philosophy: Multiple Approaches, Deep Learning

### When to Use Each Approach

```tikz
\begin{tikzpicture}[
    approach_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4cm, align=center, minimum height=3cm},
    context_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=5cm, align=center}
]

\node[approach_box] (traditional) at (0, 3) {
    \textbf{Traditional Approach}\\[0.5em]
    \textbf{When to use:}\\
    • Technical interviews\\
    • Learning bit manipulation\\
    • Performance-critical code\\
    • Embedded systems\\[0.5em]
    \textbf{Benefits:}\\
    • Shows bit manipulation skills\\
    • O(1) time and space\\
    • No floating point issues
};

\node[approach_box] (pythonic) at (8, 3) {
    \textbf{Pythonic Approaches}\\[0.5em]
    \textbf{When to use:}\\
    • Production code\\
    • Code readability priority\\
    • Team collaboration\\
    • Rapid prototyping\\[0.5em]
    \textbf{Benefits:}\\
    • Self-documenting code\\
    • Multiple solution styles\\
    • Leverages Python features
};

\node[context_box] at (4, 0.5) {
    \textbf{Educational Insight:}\\
    Understanding bit patterns helps you recognize\\
    mathematical properties in binary representation\\[0.3em]
    \textit{Master the fundamentals, then choose the right tool}
};

\end{tikzpicture}
```

### The Learning Journey

```tikz
\begin{tikzpicture}[
    stage_box/.style={rectangle, draw, rounded corners, fill=green!20, font=\sffamily\small, text width=3cm, align=center, minimum height=2cm},
    arrow/.style={->, thick, purple, line width=2pt}
]

\node[stage_box] (understand) at (0, 3) {
    \textbf{Stage 1:}\\
    \textbf{Understand}\\[0.5em]
    Recognize binary patterns\\
    in powers of 4 and\\
    mathematical properties
};

\node[stage_box] (implement) at (4, 3) {
    \textbf{Stage 2:}\\
    \textbf{Implement}\\[0.5em]
    Master bit manipulation\\
    techniques and\\
    optimization strategies
};

\node[stage_box] (optimize) at (8, 3) {
    \textbf{Stage 3:}\\
    \textbf{Optimize}\\[0.5em]
    Use Python idioms\\
    for elegant, readable\\
    solutions
};

\draw[arrow] (understand) -- (implement);
\draw[arrow] (implement) -- (optimize);

\node at (4, 1) {\bfseries The journey from mathematical insight to elegant implementation};

\end{tikzpicture}
```

### Performance Comparison

| Aspect | Math Log | Division | Bit Basic | Bit Mask | Python Idioms |
|--------|----------|----------|-----------|----------|---------------|
| **Time Complexity** | O(1) | O(log n) | O(1) | O(1) | O(1) |
| **Space Complexity** | O(1) | O(1) | O(1) | O(1) | O(1) |
| **Code Length** | ~5 lines | ~8 lines | ~10 lines | ~3 lines | ~2 lines |
| **Readability** | High | High | Medium | Low | Very High |
| **Performance** | Good | Poor | Excellent | Excellent | Good |
| **Interview Suitability** | Good | Poor | Excellent | Excellent | Good |
| **Floating Point Issues** | Yes | No | No | No | Depends |
| **Learning Value** | Medium | Low | High | High | Medium |

## 🧠 Key Takeaways & Problem-Solving Framework

### Why This Problem is Educational

```tikz
\begin{tikzpicture}[
    principle_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=1.8cm},
    arrow/.style={->, thick, green}
]

\node[principle_box] (patterns) at (0, 3) {
    \textbf{Binary Patterns}\\[0.3em]
    Recognize mathematical\\
    properties in binary\\
    representation
};

\node[principle_box] (optimization) at (4.5, 3) {
    \textbf{Bit Manipulation}\\[0.3em]
    Master efficient\\
    bitwise operations\\
    and tricks
};

\node[principle_box] (elegance) at (9, 3) {
    \textbf{Code Elegance}\\[0.3em]
    Balance performance\\
    with readability\\
    and maintainability
};

\draw[arrow] (patterns) -- (optimization);
\draw[arrow] (optimization) -- (elegance);

\node at (4.5, 1.5) {\bfseries Core Learning: Pattern → Optimization → Elegance};

\end{tikzpicture}
```

### Problem-Solving Pattern: [[Power Detection]]

This problem exemplifies **mathematical property recognition**:

1. **Pattern analysis**: Examine binary representation of target numbers
2. **Property identification**: Find distinguishing characteristics
3. **Algorithm design**: Convert properties to efficient checks
4. **Implementation choice**: Select appropriate technique for context

### Complexity Analysis
- **Time Complexity:** O(1) - constant time bit operations
- **Space Complexity:** O(1) - no additional space needed

### Related Concepts for Obsidian

This problem connects to several important concepts:

- **[[Bit Manipulation]]**: Core bitwise operation techniques
- **[[Power Detection]]**: Algorithms for detecting mathematical powers
- **[[Binary Representation]]**: Understanding number systems
- **[[Mathematical Properties]]**: Leveraging number theory
- **[[Optimization Techniques]]**: Constant time algorithms

### Related Problems
- **LC231. Power of Two**: Simpler version with similar bit manipulation
- **LC326. Power of Three**: Different approach using mathematical properties
- **LC1009. Complement of Base 10 Integer**: Bit manipulation techniques
- **LC191. Number of 1 Bits**: Counting set bits in binary representation

### Implementation Tips

#### Bit Manipulation Mastery
- **Power of 2 check**: `n & (n-1) == 0` is the standard technique
- **Bit position masks**: `0x55555555` for even positions, `0xAAAAAAAA` for odd
- **Trailing zeros**: Use `(n-1).bit_length()` or manual counting

#### Mathematical Properties
- **Powers of 4 property**: `(n-1) % 3 == 0` for all powers of 4 > 1
- **Logarithm approach**: Useful but beware of floating point precision
- **Pattern recognition**: Binary patterns often reveal mathematical structure

#### Python-Specific Techniques
- **bit_count() method**: Python 3.10+ for counting set bits
- **Walrus operator**: Concise variable assignment in expressions
- **Guard clauses**: Early returns for edge cases
- **One-liners**: Balance conciseness with readability

### Edge Cases to Consider
- **Zero and negative numbers**: Always return false
- **n = 1**: Special case (4⁰ = 1)
- **Large numbers**: Ensure bit operations work correctly
- **Floating point precision**: Issues with logarithm approaches

This problem beautifully demonstrates how **mathematical insight** combined with **bit manipulation mastery** can lead to elegant O(1) solutions, showcasing the power of **pattern recognition** in algorithm design!
