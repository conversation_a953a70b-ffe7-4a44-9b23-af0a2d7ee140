---
tags: [problem/leetcode, lc/medium, topic/dynamic_programming, pattern/grid_dp, course/labuladong, lc/lc64]
aliases: [LC64, Minimum Path Sum]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/动态规划之最小路径和.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 64. Minimum Path Sum
> The solution is based on the [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Grid DP/Minimum Path Sum|Minimum Path Sum DP Pattern]].

# LeetCode 64: Minimum Path Sum

## Problem Statement
Given a `m x n` `grid` filled with non-negative numbers, find a path from top left to bottom right, which minimizes the sum of all numbers along its path.
You can only move either down or right at any point in time.

**Official Link:** [LeetCode 64. Minimum Path Sum](https://leetcode.com/problems/minimum-path-sum/)

## Solution Approach
This problem is solved using dynamic programming, as detailed in [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Grid DP/Minimum Path Sum|Minimum Path Sum DP Pattern]].

### Python Solution
```python
class Solution:
    def minPathSum(self, grid: list[list[int]]) -> int:
        m = len(grid)
        if m == 0: return 0
        n = len(grid[0])
        if n == 0: return 0

        # dp[j] will store the min path sum to reach cell (i, j) in the current row i
        # This is a space-optimized DP using only O(N) space for one row.
        dp = [0] * n

        for i in range(m): # Iterate through rows
            for j in range(n): # Iterate through columns
                if i == 0 and j == 0: # Top-left cell
                    dp[j] = grid[i][j]
                elif i == 0: # First row (can only come from left)
                    dp[j] = dp[j-1] + grid[i][j]
                elif j == 0: # First column (can only come from top)
                    dp[j] = dp[j] + grid[i][j] # dp[j] here is dp[i-1][j] from previous row
                else: # General cell
                    dp[j] = grid[i][j] + min(dp[j], dp[j-1])
                                           # dp[j] is dp[i-1][j] (from top)
                                           # dp[j-1] is dp[i][j-1] (from left, already updated for current row)
        return dp[n-1]
```

## Complexity Analysis
- **Time Complexity:** $O(M \cdot N)$.
- **Space Complexity:** $O(N)$ using the space-optimized DP (or $O(M)$ if optimized along columns). $O(MN)$ for the full DP table.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
