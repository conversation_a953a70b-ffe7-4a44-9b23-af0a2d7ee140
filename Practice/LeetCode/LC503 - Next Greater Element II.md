---
tags: [problem/leetcode, lc/medium, topic/array, topic/stack, pattern/monotonic_stack, pattern/circular_array, course/labuladong]
aliases: [LC503, Next Greater Element II]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典队列_栈算法/单调栈算法模板解决三道例题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 503. Next Greater Element II
> Solution approach adapted from [[Interview/Concept/Data Structures/Stack/Applications/01 - Monotonic Stack Pattern|Monotonic Stack Pattern]] for circular arrays.

# LeetCode 503: Next Greater Element II

## Problem Statement
Given a circular integer array `nums` (i.e., the next element of `nums[nums.length - 1]` is `nums[0]`), return *the next greater number for every element in `nums`*.
The **next greater number** of a number `x` is the first greater number to its traversing-order next in the array, which means you could search circularly to find its next greater number. If it doesn't exist, return `-1` for this number.

**Official Link:** [LeetCode 503. Next Greater Element II](https://leetcode.com/problems/next-greater-element-ii/)

## Solution Approach: Monotonic Stack with Circular Array Simulation

This problem extends the "Next Greater Element" concept to a circular array. The standard [[Interview/Concept/Data Structures/Stack/Applications/01 - Monotonic Stack Pattern|Monotonic Stack template]] can be adapted.

**Core Idea for Circularity:**
To handle the circular nature, we can conceptually "double" the array. For an array of length `n`, iterate from index `2n - 1` down to `0`. The actual element considered at loop index `i` is `nums[i % n]`. This ensures that when processing elements from the "first copy" of the array (original indices `n-1` down to `0`), the stack will contain elements from the "second copy" (which are elements from the end of the array, effectively simulating the wrap-around).

### Python Solution
```python
class Solution:
    def nextGreaterElements(self, nums: list[int]) -> list[int]:
        n = len(nums)
        res = [-1] * n # Initialize results with -1
        stack = []     # Monotonically decreasing stack (stores elements' values)

        # Iterate effectively twice through the array (from right to left)
        # Loop index `i` goes from 2*n - 1 down to 0.
        # Actual array index considered is `i % n`.
        for i in range(2 * n - 1, -1, -1):
            actual_idx = i % n
            current_num = nums[actual_idx]

            # Maintain monotonic stack
            while stack and current_num >= stack[-1]:
                stack.pop()

            # If i < n, we are in the "first pass" and should record result
            # For i >= n, we are just populating stack for future lookups
            if i < n:
                if stack:
                    res[actual_idx] = stack[-1]
                # else: res[actual_idx] remains -1 (default)

            stack.append(current_num)

        return res
```
Labuladong's article `![](/algo/images/monotonic-stack/2.jpeg)` shows the concept of effectively doubling the array.

## Complexity Analysis
- **Time Complexity:** $O(N)$, where $N$ is the length of `nums`. The loop runs $2N$ times. Each element is pushed and popped from the stack at most once across these $2N$ iterations.
- **Space Complexity:** $O(N)$ for the `stack` in the worst case (e.g., a decreasingly sorted array).

## 总结 (Summary)
- LC503 adapts the monotonic stack approach for circular arrays.
- Iterating conceptually through a "doubled" array (by using `i % n`) allows the stack to correctly find next greater elements that wrap around.
- Results are recorded only during the "first pass" (when loop index `i < n`).

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
