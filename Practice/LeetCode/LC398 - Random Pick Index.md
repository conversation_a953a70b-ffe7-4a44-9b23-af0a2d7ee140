---
tags: [problem/leetcode, lc/medium, topic/array, topic/hash_table, topic/randomized_algorithms, algorithm/reservoir_sampling, course/labuladong]
aliases: [LC398, Random Pick Index]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 398. Random Pick Index
> Solution approaches (HashMap, Reservoir Sampling) discussed in [[Interview/Concept/Algorithms/Randomized Algorithms/01 - Concepts in Game Random Algorithms|Concepts in Game Random Algorithms]].

# LeetCode 398: Random Pick Index

## Problem Statement
Given an integer array `nums` with possible duplicates, randomly output the index of a given `target` number. You can assume that the given target number must exist in the array.
Implement the `Solution` class:
- `Solution(int[] nums)` Initializes the object with the array `nums`.
- `int pick(int target)` Returns a random index `i` of `target` from `nums[i] == target`. If there are multiple valid i's, then each index should have an equal probability of returning.

**Official Link:** [LeetCode 398. Random Pick Index](https://leetcode.com/problems/random-pick-index/)

## Solution Approaches

### 1. Hash Map Precomputation
Store indices for each number in a hash map.
- **Constructor:** Iterate through `nums`, populate `map[number] -> list_of_indices`. Time $O(N)$, Space $O(N)$.
- **`pick(target)`:** Get `list_of_indices = map[target]`. Pick a random index from this list. Time $O(1)$ after lookup (if list of indices is small or random choice from list is $O(1)$).

```python
import random
import collections

class SolutionHashMap:
    def __init__(self, nums: list[int]):
        self.num_to_indices = collections.defaultdict(list)
        for i, num in enumerate(nums):
            self.num_to_indices[num].append(i)

    def pick(self, target: int) -> int:
        indices = self.num_to_indices[target]
        # random.choice picks an element uniformly from a non-empty sequence
        return random.choice(indices)
```

### 2. Reservoir Sampling (for `pick`)
If memory is a concern or we want to do it in one pass for `pick` without precomputation for all numbers.
- **Constructor:** Just store `nums`. $O(1)$ space if `nums` is a reference, or $O(N)$ if copied.
- **`pick(target)`:** Iterate through `nums`. Maintain a `count` of `target` occurrences seen so far. For the `k`-th occurrence of `target`, choose its index with probability `1/k`.
```python
import random

class SolutionReservoir:
    def __init__(self, nums: list[int]):
        self.nums = nums

    def pick(self, target: int) -> int:
        count = 0
        result_index = -1 # Placeholder

        for i, num in enumerate(self.nums):
            if num == target:
                count += 1
                # With probability 1/count, choose this index
                if random.randint(1, count) == 1:
                    result_index = i
        return result_index
```
This is Reservoir Sampling for $k=1$ item from the stream of indices where `nums[index] == target`.

## Complexity Analysis
**Hash Map Approach:**
- `__init__`: Time $O(N)$, Space $O(N)$.
- `pick`: Time $O(1)$ on average (assuming `random.choice` on the list of indices is $O(1)$ or list is small).

**Reservoir Sampling Approach:**
- `__init__`: Time $O(1)$ (if just storing reference) or $O(N)$ (if copying `nums`). Space $O(1)$ or $O(N)$.
- `pick`: Time $O(N)$ for each call. Space $O(1)$.

**Trade-off:**
- Hash map: Faster `pick` if called multiple times, more preprocessing and space.
- Reservoir sampling: Slower `pick`, less space/preprocessing. Suitable if `pick` is called infrequently or on very large arrays where $O(N)$ space for map is an issue.

The problem usually implies `pick` will be called multiple times, making the HashMap approach generally preferred for LeetCode.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
