---
tags: [problem/leetcode, lc/medium, topic/game_theory, topic/dynamic_programming, topic/brain_teaser, course/labuladong]
aliases: [LC877, Stone Game]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 877. Stone Game
> Discussed in [[Interview/Concept/Algorithms/Mathematical Techniques/04 - Brain Teaser Algorithm Problems|Brain Teaser Algorithm Problems]]. While solvable by DP, it has a simpler game theory insight.

# LeetCode 877: Stone Game

## Problem Statement
Alex and <PERSON> play a game with piles of stones. There are an even number of piles arranged in a row, and each pile has a positive integer number of stones `piles[i]`.
The objective of the game is to end with the most stones. The total number of stones is odd, so there are no ties.
<PERSON> and <PERSON> take turns, with <PERSON> starting first. Each turn, a player takes the entire pile of stones from either the beginning or the end of the row. This continues until there are no more piles left.
Assuming <PERSON> and <PERSON> play optimally, return `true` if <PERSON> wins the game, or `false` if <PERSON> wins.

**Official Link:** [LeetCode 877. Stone Game](https://leetcode.com/problems/stone-game/)

## Solution Approach: Game Theory Insight
Since the number of piles is even, <PERSON> (the first player) can always choose to take either all the odd-indexed piles or all the even-indexed piles.
1. Sum stones in odd-indexed piles: `sum_odd = piles[1] + piles[3] + ...`
2. Sum stones in even-indexed piles: `sum_even = piles[0] + piles[2] + ...`
Since the total number of stones is odd, `sum_odd` cannot equal `sum_even`. One sum must be greater.
Alex, playing optimally, will choose the strategy (starting with first pile or last pile) that allows them to secure the group of piles (all odd or all even) that has the larger sum. Therefore, Alex can always win.

### Python Solution
```python
class Solution:
    def stoneGame(self, piles: list[int]) -> bool:
        # Alex, as the first player, can always choose to take either
        # all odd-indexed piles or all even-indexed piles.
        # Since the total sum of stones is odd, the sum of odd-indexed piles
        # cannot be equal to the sum of even-indexed piles.
        # Alex will choose the set of piles (odd or even) that has a larger sum.
        # Therefore, Alex always wins if they play optimally.
        return True
```

## Dynamic Programming Approach (More General, Not Needed Here)
If the game had different rules (e.g., odd number of piles, or player cannot choose which "parity" of piles to take), a DP solution might be necessary.
`dp[i][j]` could represent the maximum score difference the current player can achieve from `piles[i...j]`.
This is more complex and not required due to the specific constraints of this problem.

## Complexity Analysis
- **Game Theory Insight Solution:**
    - Time Complexity: $O(1)$.
    - Space Complexity: $O(1)$.
- **DP Solution (if implemented):** Typically $O(N^2)$ time and space.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
