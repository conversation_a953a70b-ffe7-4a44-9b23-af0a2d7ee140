---
tags: [guide/leetcode, interview_prep/platform, type/guide]
aliases: [LeetCode Guide, How to Use LeetCode, Online Judge Tips, LeetCode Submission Errors]
---

> [!NOTE] Source Annotation
> Content primarily adapted from [[labuladong 的算法笔记/markdown_export_本站简介/入门：编程语言基础及练习/力扣_LeetCode 解题须知.md]].

# LeetCode Platform Guide and Tips

This guide provides an overview of how to effectively use platforms like LeetCode for practicing algorithm problems, focusing on understanding the testing mechanisms, common submission issues, and best practices.

## 1. Core Code Mode vs. ACM Mode

### Core Code Mode
Most online judges like LeetCode use the **Core Code Mode**.
- You are given a function signature (or a class with a method) and need to implement the logic within that function/method.
- **Example (Python - Two Sum):**
  ```python
  class Solution:
      def twoSum(self, nums: List[int], target: int) -> List[int]:
          # Your algorithm logic here
          pass
  ```
- The platform's backend compiles your code and runs it against a series of predefined test cases. It compares your function's return value with the expected answer for each test case.
- **Advantage:** You can focus solely on the algorithm logic without worrying about input parsing or output formatting.

### ACM Mode
Some platforms (e.g., NowCoder, some university contests) use **ACM Mode**.
- You need to write a complete program that handles input reading from standard input (stdin) and prints results to standard output (stdout).
- **Example (Conceptual Structure for ACM Mode - Python):**
  ```python
  def solve():
      # Read N, then N numbers for nums array
      N = int(input()) 
      nums_str = input().split()
      nums = [int(x) for x in nums_str]
      target = int(input())
      
      # --- Algorithm logic (similar to core code mode) ---
      result = two_sum_algorithm(nums, target) 
      # --- End of algorithm logic ---
      
      # Print result in specified format
      print(f"{result[0]} {result[1]}") 

  # def two_sum_algorithm(nums, target): ...

  if __name__ == "__main__":
      # Potentially multiple test cases in ACM mode
      # T = int(input()) 
      # for _ in range(T):
      solve()
  ```
- **Recommendation:** For general interview preparation, focus on Core Code Mode as it's more common in interviews. ACM mode can be practiced closer to specific contests or tests that require it.

## 2. How to Read Problems on LeetCode

- **Problem Statement:** Understand the core task, inputs, and expected outputs.
- **Examples:** These clarify the problem and illustrate edge cases or specific formats.
- **Constraints/Hints (Crucial!):**
    - **Data Types and Ranges:** E.g., `nums.length` will be between 1 and 10^4. `nums[i]` will be between -10^9 and 10^9. This informs choice of data types and potential for overflow.
    - **Assumptions:** E.g., "You can assume `nums` has no duplicate elements" or "Each input would have exactly one solution." These simplify your logic.
    - **Time/Space Complexity Requirements:** Sometimes explicitly stated ("Can you do this in O(N) time?") or implied by input size (e.g., N=10^5 often means O(N log N) or O(N) solution is needed, O(N^2) will likely TLE).

## 3. How to Solve and Test

1.  **Develop Algorithm:** Think through the logic, choose appropriate data structures.
2.  **Write Code:** Implement your algorithm within the provided function/class structure.
3.  **"Run Code" Button:** Use this to test your solution against the example test cases provided by LeetCode or your own custom test cases. This is a quick check before full submission.
4.  **"Submit" Button:** This runs your code against a comprehensive, hidden set of test cases.

## 4. Debugging Tips

- **Local IDE:** The most effective way is to set up your local environment to run LeetCode problems (e.g., using plugins for VSCode or JetBrains IDEs). This allows for full debugger capabilities.
- **Print Statements:** On the LeetCode website, you can add `print()` statements in your code. The output will appear in the "stdout" section of the test result.
> [!WARNING] Remove Prints Before Submission
> Always remove or comment out debug `print()` statements before final submission. Extensive printing is an I/O operation and can significantly slow down your code, potentially leading to a Time Limit Exceeded (TLE) error even if the algorithm itself is efficient.

## 5. Understanding Judging and Submission Results

- **Accepted (AC):** Your solution passed all test cases. Congratulations!
- **Compile Error:** Syntax errors in your code. The platform couldn't compile it. Usually, local IDEs catch these.
- **Runtime Error:** Your code compiled but crashed during execution. Common causes:
    - Array index out of bounds.
    - Null pointer dereference / accessing attributes of `None`.
    - Division by zero.
    - Unhandled exceptions.
> [!TIP] Check Edge Cases
> Runtime errors often occur due to unhandled edge cases (e.g., empty input arrays, null trees).
- **Wrong Answer (WA):** Your code produced an incorrect output for one or more test cases. The platform will usually show you the failing test case (input, your output, expected output).
- **Time Limit Exceeded (TLE):** Your algorithm is too slow for the given input constraints.
    - **Common Causes:**
        - Algorithm has too high time complexity (e.g., O(N^2) where O(N) is needed).
        - Infinite loops.
        - Excessive recursion without memoization leading to re-computation.
        - (As mentioned) Unremoved debug print statements.
    - **Solution:** Re-think your algorithm for a more efficient approach. Analyze constraints to deduce the required complexity.
- **Memory Limit Exceeded (MLE):** Your algorithm uses too much memory.
    - **Common Causes:**
        - Creating very large data structures unnecessarily.
        - Deep recursion leading to stack overflow (though sometimes reported as Runtime Error).
        - Storing too much data in global/class variables across test cases (see below).
    - **Solution:** Optimize space complexity. Check for memory leaks (less common in Python unless misusing external libraries or holding onto large objects).

## 6. Important Considerations for LeetCode Submissions

### Avoid File-Level Global Variables (Critical!)
LeetCode and similar platforms run multiple test cases using the same instance of your `Solution` class (for languages like Python, Java, C++) or by calling your function multiple times.
- **If you use true global variables (outside any class/function scope) or static class variables that are not reset, their state will persist between test cases.** This can lead to incorrect results for subsequent test cases.
- **Example Scenario:** You submit, it passes some initial tests, then fails on a later one. But if you run that failing test case *individually*, it passes. This is a classic sign of test case interference due to shared state.

**How to Manage State Correctly:**
- **Class Members (Python/Java/C++):** If you need to store state for a single test case's execution (e.g., a memoization table for recursion, a result list built during traversal), declare them as instance variables. They will be re-initialized or be fresh for each `Solution` object instantiation implicitly handled by LeetCode for each test run.
    ```python
    # Correct way for Python
    class Solution:
        def solveProblem(self, n: int) -> int:
            self.memo = {} # Instance variable, fresh for each conceptual test run
            # ... or ...
            res = [] # Local variable, fine
            self._helper(n, res)
            return len(res)

        def _helper(self, n, current_res_list):
            # Modify current_res_list
            pass
    ```
- **Pass as Parameters:** Pass necessary state (like result lists or memoization tables) as parameters to helper functions.
- **Local Variables:** Variables defined inside your main solution method are naturally reset for each call.
- **Closures (JavaScript/Go):** Define helper functions within the main solution function so they can capture variables from the outer scope, which are fresh for each call to the main function.

### Clear Standard Output (Print Statements)
Reiterating: remove all debugging `print()` statements before submission to avoid TLE or WA (if output format is strict).

## 总结 (Summary)
- Understand the difference between Core Code Mode (common for interviews) and ACM Mode.
- Read problem statements thoroughly, paying close attention to examples and constraints.
- Use the "Run Code" feature for initial testing and "Submit" for full evaluation.
- Be aware of common errors (Runtime Error, WA, TLE, MLE) and their causes.
- **Crucially, manage state correctly by avoiding persistent global/static variables that can cause interference between test cases.**
- Always remove debug prints before final submission.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Index]]
