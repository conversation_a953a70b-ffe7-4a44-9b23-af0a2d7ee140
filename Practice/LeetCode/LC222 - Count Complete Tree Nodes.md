---
tags: [problem/leetcode, lc/medium, topic/tree, topic/binary_tree, topic/dfs, topic/binary_search, pattern/divide_and_conquer]
aliases: [LC222, LeetCode 222. Count Complete Tree Nodes, 完全二叉树的节点个数]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 222. Count Complete Tree Nodes.
> Solution insights from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树的拓展延伸/拓展：如何计算完全二叉树的节点数.md]].

# LeetCode 222: Count Complete Tree Nodes

## Problem Statement

Given the `root` of a complete binary tree, return the number of the nodes in the tree.
According to LeetCode's definition, every level, except possibly the last, is completely filled in a complete binary tree, and all nodes in the last level are as far left as possible. It can have between 1 and $2^h$ nodes inclusive at the last level $h$.

Design an algorithm that runs in less than $O(n)$ time complexity.

**Official Link:** [LeetCode 222. Count Complete Tree Nodes](https://leetcode.com/problems/count-complete-tree-nodes/)

## Solution Approach

A naive solution would be to traverse all nodes (DFS or BFS), taking $O(N)$ time. However, the problem asks for something more efficient, leveraging the properties of a complete binary tree.

**Key Properties Utilized:**
1.  A **Full (Perfect) Binary Tree** of height $h$ has $2^h - 1$ nodes. (If root is height 1, or $2^{height\_val} - 1$ if height is number of levels, and root is level 1. If height is number of edges on longest path, and root is height 0, then $2^{h+1}-1$ nodes). Let's use $h$ as number of levels, root at level 1. Nodes = $2^h - 1$.
2.  In a **Complete Binary Tree**, if the height of the left subtree (measured by going down its leftmost path) is equal to the height of the right subtree (measured by going down its rightmost path, relative to current root), then the left subtree *must be a full binary tree*.
    - This isn't quite right. The crucial insight is comparing the height of the *entire tree* as found by repeatedly going left, versus the height found by repeatedly going right. More accurately:
    - Calculate `left_height`: Go left from `root` until null.
    - Calculate `right_height`: Go right from `root` until null.
    - If `left_height == right_height`, the tree rooted at `root` is a **full (perfect) binary tree**. Its node count is $2^{\text{left\_height}} - 1$.
    - If `left_height != right_height`, then it's a complete binary tree but not full. The number of nodes is `1 (for root) + countNodes(root.left) + countNodes(root.right)`. The optimization comes from the fact that one of the subtrees will be full and its height can be quickly determined.

**Labuladong's Approach (Optimized Recursive):**
The core idea is to compare the height of the tree if we only go left, versus the height if we only go right.

Let `h_left` be the height of the leftmost path from `root`.
Let `h_right` be the height of the rightmost path from `root`.
(Height here means number of nodes on the path).

1.  Calculate `h_left`: Start at `root`, keep going `node = node.left`, incrementing height counter, until `node` is `None`.
2.  Calculate `h_right`: Start at `root`, keep going `node = node.right`, incrementing height counter, until `node` is `None`.

3.  **If `h_left == h_right`:**
    This means the tree is a **full (perfect) binary tree**. The number of nodes is $2^{h_{left}} - 1$.
    ```tikz
    \begin{tikzpicture}[
        treenode/.style={circle, draw, minimum size=6mm, font=\sffamily\scriptsize},
        level 1/.style={sibling distance=2cm},
        level 2/.style={sibling distance=1cm}
    ]
    \node[treenode] (r) at (0,0) {R}
        child {node[treenode] (l) {L}
            child {node[treenode] (ll) {LL}}
            child {node[treenode] (lr) {LR}}
        }
        child {node[treenode] (r_child) {R_c} % Renamed to avoid conflict
            child {node[treenode] (rl) {RL}}
            child {node[treenode] (rr) {RR}}
        };
    \draw[red, thick, dashed] (r) -- (l) -- (ll) node[below left, font=\tiny]{$h_{left}=3$};
    \draw[blue, thick, dashed] (r) -- (r_child) -- (rr) node[below right, font=\tiny]{$h_{right}=3$};
    \node at (0,-3) [text width=5cm, align=center, draw, fill=yellow!10, rounded corners]
        {If $h_{left} == h_{right}$, tree is full. Nodes = $2^{h_{left}} - 1$. Here $2^3-1 = 7$.};
    \end{tikzpicture}
    ```

4.  **If `h_left != h_right`:**
    This means the tree is complete but not full. The last level is not completely filled.
    The number of nodes is `1 (for root) + countNodes(root.left) + countNodes(root.right)`.
    Crucially, one of the recursive calls (`countNodes(root.left)` or `countNodes(root.right)`) will hit the `h_left == h_right` case for its subtree and return quickly, while the other will recurse further. The heights `h_left` and `h_right` for subtrees are recomputed.
    ```tikz
    \begin{tikzpicture}[
        treenode/.style={circle, draw, minimum size=6mm, font=\sffamily\scriptsize},
        level 1/.style={sibling distance=2cm},
        level 2/.style={sibling distance=1cm}
    ]
    \node[treenode] (r) at (0,0) {R}
        child {node[treenode] (l) {L}
            child {node[treenode] (ll) {LL}}
            child {node[treenode] (lr) {LR}}
        }
        child {node[treenode] (r_child) {R_c}
            child {node[treenode] (rl) {RL}}
            % child {node[treenode] (rr) {RR}} % Missing RR to make it not full
        };
    \draw[red, thick, dashed] (r) -- (l) -- (ll) node[below left, font=\tiny]{$h_{left}=3$};
    \draw[blue, thick, dashed] (r) -- (r_child) -- (rl) node[right, midway, font=\tiny]{$h_{right}=2$ from R to RL leaf};
    \node at (0,-3) [text width=6cm, align=center, draw, fill=yellow!10, rounded corners]
        {If $h_{left} \neq h_{right}$ (e.g. $3 \neq 2$), recurse: $1 + \mathrm{count}(L) + \mathrm{count}(R_c)$.\\
        Subtree L is full ($h_L_left=2, h_L_right=2$), nodes $2^2-1=3$.\\
        Subtree $R_c$ (node RL) is full ($h_{R_c}_left=1, h_{R_c}_right=1$), nodes $2^1-1=1$. (Example if RL is leaf)
        };
    \end{tikzpicture}
    ```

The height calculation for $h_{left}$ and $h_{right}$ takes $O(\log N)$ time (height of the tree). In the worst case (a path-like skewed complete tree, which is not very skewed but still requires recursive calls), the recursion depth is $O(\log N)$. So, total complexity is $O((\log N)^2)$.

### Python Solution
```python
class Solution:
    def countNodes(self, root: TreeNode) -> int:
        if not root:
            return 0

        # Calculate height by going left all the way
        h_left_node = root
        h_left = 0
        while h_left_node:
            h_left_node = h_left_node.left
            h_left += 1

        # Calculate height by going right all the way
        h_right_node = root
        h_right = 0
        while h_right_node:
            h_right_node = h_right_node.right
            h_right += 1

        # If heights are equal, it's a full (perfect) binary tree
        if h_left == h_right:
            return (1 << h_left) - 1 # This is 2^h_left - 1
            # (1 << h_left) is a bitwise way to calculate 2 to the power of h_left

        # If not full, recursively count nodes in left and right subtrees
        # Add 1 for the current root node
        return 1 + self.countNodes(root.left) + self.countNodes(root.right)

```

## Complexity Analysis
-   **Time Complexity:** $O((\log N)^2)$.
    - In each call to `countNodes`, we calculate `h_left` and `h_right`, each taking $O(\log N)$ time (height of the current subtree).
    - If `h_left == h_right`, the call returns in $O(\log N)$.
    - If `h_left != h_right`, we make two recursive calls. However, it's guaranteed that in one of these recursive calls, the condition `h_left == h_right` will be met for its subtree (related to properties of complete binary trees: one child will be root of a full tree of height $h-1$ or $h-2$).
    - The recurrence relation is roughly $T(N) = T(N/2) + O(\log N)$. This does not directly solve to $O((\log N)^2)$.
    - A more precise analysis: The recursion path only goes down one branch where `h_left != h_right`. The depth of this recursion is $O(\log N)$. At each step of this recursion, we do $O(\log N)$ work for height calculations. Thus, $O(\log N \times \log N) = O((\log N)^2)$.
-   **Space Complexity:** $O(\log N)$ for the recursion stack.

## 总结 (Summary)
- To count nodes in a complete binary tree in less than $O(N)$ time, leverage its special properties.
- By comparing the height of the "all-left" path and "all-right" path from the current root:
    - If they are equal, the subtree is full (perfect), and its node count can be calculated directly using $2^h - 1$.
    - If they are not equal, recursively call on left and right children, adding 1 for the root. One of these recursive calls will quickly terminate on a full subtree.
- This optimized approach yields a time complexity of $O((\log N)^2)$.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Complete Binary Tree Properties]], [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer]]
