---
tags: [problem/leetcode, lc/easy, topic/string, topic/stack, pattern/matching_pairs]
aliases: [LC20, LeetCode 20. Valid Parentheses]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 20. Valid Parentheses
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/入门：编程语言基础及练习/编程语言刷题实践.md]].

# LeetCode 20: Valid Parentheses

## Problem Statement

Given a string `s` containing just the characters `'(', ')', '{', '}', '[' and ']'`, determine if the input string is valid.

An input string is valid if:
1.  Open brackets must be closed by the same type of brackets.
2.  Open brackets must be closed in the correct order.
3.  Every close bracket has a corresponding open bracket of the same type.

**Official Link:** [LeetCode 20. Valid Parentheses](https://leetcode.com/problems/valid-parentheses/)

**Example 1:**
Input: `s = "()"`
Output: `true`

**Example 2:**
Input: `s = "()[]{}"`
Output: `true`

**Example 3:**
Input: `s = "(]"`
Output: `false`

## Solution (Python)

This problem is a classic application of the [[Interview/Concept/Data Structures/Stack/Applications/Valid Parentheses Pattern|Valid Parentheses Pattern using a Stack]]. The idea is to use a stack to keep track of open brackets.

```python
class Solution:
    def isValid(self, s: str) -> bool:
        stack = []  # Use a list as a stack
        # Mapping of closing brackets to their corresponding opening brackets
        mapping = {")": "(", "}": "{", "]": "["}

        for char in s:
            if char in mapping:  # If the character is a closing bracket
                # Pop the top element from the stack if it's not empty,
                # otherwise assign a dummy value '#' if the stack is empty.
                top_element = stack.pop() if stack else '#'

                # If the mapping for the closing bracket doesn't match the top element,
                # or if the stack was empty (top_element is '#'), it's invalid.
                if mapping[char] != top_element:
                    return False
            else:  # If it's an opening bracket, push it onto the stack.
                stack.append(char)

        # If the stack is empty at the end, all brackets were matched correctly.
        # Otherwise, there are unmatched opening brackets.
        return not stack
```

**Explanation:**
1.  Initialize an empty `stack` (using a Python [[Interview/Concept/Programming Languages/Python/04 - Python Stack (using List or Deque) for Interviews|list as a stack]]).
2.  Create a `mapping` dictionary that stores the relationship between closing brackets and their corresponding opening brackets. This makes checking matches easier.
3.  Iterate through each `char` in the input string `s`:
    -   **If `char` is a closing bracket** (i.e., `char` is a key in `mapping`):
        -   Try to pop an element from the `stack`. This element should be the most recently opened, unmatched opening bracket.
        -   If the `stack` is empty before popping (meaning a closing bracket appeared without a preceding open bracket), or if the popped opening bracket does not match the type of the current closing bracket (e.g., `char` is `)` but `top_element` was `{`), then the string is invalid. Return `False`.
    -   **If `char` is an opening bracket** (i.e., `char` is not a key in `mapping`):
        -   Push `char` onto the `stack`.
4.  After iterating through the entire string:
    -   If the `stack` is empty, it means every opening bracket found a matching closing bracket. The string is valid. Return `True`.
    -   If the `stack` is not empty, it means there are some opening brackets that were never closed. The string is invalid. Return `False`. (The condition `not stack` conveniently handles this).

## Complexity Analysis
-   **Time Complexity:** $O(N)$, where $N$ is the length of the string `s`. We iterate through the string once, and each stack operation (push, pop) takes $O(1)$ time.
-   **Space Complexity:** $O(N)$ in the worst case. If the string consists of all opening brackets (e.g., `"((((("`), the stack will store all $N$ characters.

## Visualizing Stack Operations

Let `s = "([{}])"`

```tikz
\begin{tikzpicture}[
    char_node/.style={draw, rectangle, minimum size=0.7cm, font=\sffamily\bfseries},
    stack_cell/.style={draw, rectangle, minimum width=1cm, minimum height=0.6cm, fill=blue!10},
    stack_base/.style={draw, thick, minimum width=1.2cm},
    arrow/.style={->, >=stealth, thick, shorten >=1pt, shorten <=1pt},
    title/.style={font=\sffamily\bfseries\small}
]

\matrix[column sep=0.5cm, row sep=0.8cm] {
    % Iteration 1: char = '('
    \node[title] {Char: '('}; &
    \node[title] {Stack (Bottom -> Top)}; &
    \node[title] {Action}; \\

    \node[char_node, fill=green!20] {(}; &
    \node[stack_cell] (s1_1) {(}; \node[stack_base, below=0.05cm of s1_1] {}; &
    \node {Push '('}; \\

    % Iteration 2: char = '['
    \node[title] {Char: '['}; & & \\
    \node[char_node, fill=green!20] {[}; &
    \node[stack_cell] (s2_2) {[}; \node[stack_cell, below=0cm of s2_2] (s2_1) {(}; \node[stack_base, below=0.05cm of s2_1] {}; &
    \node {Push '['}; \\

    % Iteration 3: char = '{'
    \node[title] {Char: '\{'}; & & \\
    \node[char_node, fill=green!20] {\{}; &
    \node[stack_cell] (s3_3) {\{}; \node[stack_cell, below=0cm of s3_3] (s3_2) {[}; \node[stack_cell, below=0cm of s3_2] (s3_1) {(}; \node[stack_base, below=0.05cm of s3_1] {}; &
    \node {Push '\{'}; \\

    % Iteration 4: char = '}'
    \node[title] {Char: '\}'}; & & \\
    \node[char_node, fill=orange!20] {\}}; &
    \node[stack_cell] (s4_2) {[}; \node[stack_cell, below=0cm of s4_2] (s4_1) {(}; \node[stack_base, below=0.05cm of s4_1] {}; &
    \node {Pop '\{'. Match '\}'.}; \\

    % Iteration 5: char = ']'
    \node[title] {Char: ']'}; & & \\
    \node[char_node, fill=orange!20] {]}; &
    \node[stack_cell] (s5_1) {(}; \node[stack_base, below=0.05cm of s5_1] {}; &
    \node {Pop '['. Match ']'.}; \\

    % Iteration 6: char = ')'
    \node[title] {Char: ')'}; & & \\
    \node[char_node, fill=orange!20] {)}; &
    \node[stack_base] (s6_base) {}; \node at (s6_base.center) [above=0.1cm] {(empty)}; &
    \node {Pop '('. Match ')'.}; \\
};

\node at (1,-13) [title] {End of string. Stack is empty. Result: True};
\end{tikzpicture}
```

## 总结 (Summary)
- The Valid Parentheses problem is a classic stack application for checking balanced symbols.
- When an opening bracket is encountered, it's pushed onto the stack.
- When a closing bracket is encountered, the top of the stack is popped and checked for a match.
- If all brackets are matched and the stack is empty at the end, the string is valid.
- This approach provides an $O(N)$ time and $O(N)$ space solution.

---
Previous: [[Interview/Practice/LeetCode/LC136 - Single Number|LC136 - Single Number]]
Next: [[Interview/Practice/LeetCode/LC2073 - Time Needed to Buy Tickets|LC2073 - Time Needed to Buy Tickets]]
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Data Structures/Stack/Applications/Valid Parentheses Pattern|Valid Parentheses Pattern]], [[Interview/Concept/Programming Languages/Python/04 - Python Stack (using List or Deque) for Interviews|Python Stack]]
