---
tags: [problem/leetcode, lc/hard, topic/graph, topic/bfs, pattern/bfs_shortest_path, topic/matrix]
aliases: [LC773, LeetCode 773, Sliding Puzzle]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 773. Sliding Puzzle
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/BFS 算法解题套路框架.md]].

# LeetCode 773: Sliding Puzzle

## Problem Statement

On a 2x3 `board`, there are 5 tiles labeled `1` through `5`, and an empty square represented by `0`. A move consists of choosing `0` and a 4-directionally adjacent number and swapping it. The solved state is `[[1,2,3],[4,5,0]]`. Given an initial `board`, return the minimum number of moves to solve it, or -1 if unsolvable.

**Official Link:** [LeetCode 773. Sliding Puzzle](https://leetcode.com/problems/sliding-puzzle/)

**Example 1:**
Input: `board = [[1,2,3],[4,0,5]]`
Output: `1`
Explanation: Swap 0 and 5 in one move.

**Example 2:**
Input: `board = [[4,1,2],[5,0,3]]`
Output: `5`

## Solution Approach: BFS

This problem asks for the minimum number of moves, which signifies a shortest path problem on an unweighted graph. The states of the graph are the different configurations of the puzzle board. An edge exists between two board configurations if one can be transformed into the other by a single valid move (swapping '0' with an adjacent tile). This fits the [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Framework]].

### Problem Abstraction for BFS

1.  **Node/State:** A string representation of the 2x3 board. Serializing the 2D array into a 1D string (e.g., row by row) makes it hashable for the `visited` set and usable as keys in a queue if needed.
    - `[[r1c1, r1c2, r1c3], [r2c1, r2c2, r2c3]]` becomes `"r1c1r1c2r1c3r2c1r2c2r2c3"`.
2.  **`start_node`:** The string representation of the initial `board`.
3.  **`target_node`:** "123450".
4.  **`get_neighbors(board_str)`:**
    a. Find the index of '0' in `board_str`.
    b. Determine which 1D indices are adjacent to '0's 2D position. Labuladong uses a precomputed `neighbor_map` for this:
       ```
       # 2D board:
       # 0 1 2
       # 3 4 5
       # neighbor_map[i] = list of 1D indices adjacent to 1D index i
       neighbor_map = [
           [1, 3],    # Neighbors of index 0
           [0, 2, 4], # Neighbors of index 1
           [1, 5],    # Neighbors of index 2
           [0, 4],    # Neighbors of index 3
           [1, 3, 5], # Neighbors of index 4
           [2, 4]     # Neighbors of index 5
       ]
       ```
    c. For each valid adjacent index, create a new board string by swapping '0' with the tile at that adjacent index.
5.  **Invalid States:** None, other than those already visited.

### Standard BFS Solution (Python)

```python
from collections import deque

class Solution:
    def slidingPuzzle(self, board: list[list[int]]) -> int:
        # Target board string
        target_str = "123450"
        
        # Convert initial board to string
        start_str_list = []
        for r in range(2):
            for c in range(3):
                start_str_list.append(str(board[r][c]))
        start_str = "".join(start_str_list)

        if start_str == target_str:
            return 0

        # Precomputed neighbor mapping for 1D string indices
        # Index: 0  1  2
        #        3  4  5
        neighbor_map = [
            [1, 3],    # Neighbors of index 0
            [0, 2, 4], # Neighbors of index 1
            [1, 5],    # Neighbors of index 2
            [0, 4],    # Neighbors of index 3
            [1, 3, 5], # Neighbors of index 4
            [2, 4]     # Neighbors of index 5
        ]

        q = deque([(start_str, 0)]) # (board_string, steps)
        visited = {start_str}
        
        while q:
            current_board_str, steps = q.popleft()

            if current_board_str == target_str:
                return steps
            
            zero_idx = -1
            for i in range(len(current_board_str)):
                if current_board_str[i] == '0':
                    zero_idx = i
                    break
            
            # Generate neighbors by swapping '0'
            for neighbor_idx in neighbor_map[zero_idx]:
                new_board_list = list(current_board_str)
                new_board_list[zero_idx], new_board_list[neighbor_idx] = new_board_list[neighbor_idx], new_board_list[zero_idx]
                new_board_str = "".join(new_board_list)
                
                if new_board_str not in visited:
                    visited.add(new_board_str)
                    q.append((new_board_str, steps + 1))
                    
        return -1 # Target not reachable

# Example usage:
# sol = Solution()
# board1 = [[1,2,3],[4,0,5]]
# print(f"Board {board1}: Min moves = {sol.slidingPuzzle(board1)}") # Expected: 1

# board2 = [[4,1,2],[5,0,3]]
# print(f"Board {board2}: Min moves = {sol.slidingPuzzle(board2)}") # Expected: 5

# board3 = [[1,2,3],[5,4,0]] # Unsolvable based on parity, but BFS will explore until queue empty
# print(f"Board {board3}: Min moves = {sol.slidingPuzzle(board3)}") # Expected: -1
```
**Note on `steps` in BFS:** The provided solution `q.append((new_board_str, steps + 1))` directly associates the step count with the state in the queue. Labuladong's general BFS framework increments `steps` after processing each level. Both are valid ways to track steps. The general framework is:

```python
# General BFS framework step tracking
# q = deque([start_node])
# visited = {start_node}
# steps = 0
# while q:
#     level_size = len(q)
#     for _ in range(level_size):
#         curr = q.popleft()
#         if curr == target: return steps
#         for neighbor in get_neighbors(curr):
#             if neighbor not in visited:
#                 visited.add(neighbor)
#                 q.append(neighbor)
#     steps += 1
# return -1
```
The solution above integrates `steps` into the queue tuple `(state, steps_to_reach_state)`.

### Complexity
- **State Space:** The number of permutations of 6 items (0-5) is $6! = 720$. So, the number of states is relatively small.
- **Neighbors:** Max 4 neighbors (for '0' in a corner of 2x3, it's 2, edge is 3, center on longer side is 4).
- **Time:** $O(N \cdot k \cdot L)$, where $N$ is number of states (720), $k$ is max neighbors (4), $L$ is length of state string (6, for string ops like slicing/joining). Roughly constant time as state space is fixed and small. $O(6! \times \text{avg_degree} \times \text{string_len})$.
- **Space:** $O(N \cdot L)$ for `visited` set and `queue`. $O(6! \times \text{string_len})$.

## Bidirectional BFS Optimization

Since the target state ("123450") is known, bidirectional BFS can be applied.
The logic is similar to [[Interview/Practice/LeetCode/LC752 - Open the Lock|LC752 Open the Lock's bidirectional approach]]:
- Maintain two sets `q1` (frontier from start) and `q2` (frontier from target).
- In each step, expand the smaller frontier.
- If a state generated from `q1`'s expansion is found in `q2`, the path is found.

The Python solution would involve managing two sets and alternating expansions, similar to the `openLock` bidirectional solution.

## Visualization
The search explores states layer by layer:
- **Step 0:** `start_str`
- **Step 1:** Neighbors of `start_str`
- **Step 2:** Neighbors of Step 1 nodes, etc.
Until `target_str` is found.

```tikz
\begin{tikzpicture}[
    state/.style={draw, rectangle, rounded corners, font=\tiny\sffamily, minimum width=1.5cm, text centered},
    level_conn/.style={->, gray}
]
\node[state, fill=blue!20] (s0) at (0,0) {Initial Board Str};
\node[state] (s1_1) at (-3,-1.5) {Neighbor 1};
\node[state] (s1_2) at (0,-1.5) {Neighbor 2};
\node[state] (s1_3) at (3,-1.5) {Neighbor ...};

\draw[level_conn] (s0) -- (s1_1);
\draw[level_conn] (s0) -- (s1_2);
\draw[level_conn] (s0) -- (s1_3);

\node[state] (s2_1) at (-4,-3) {N of N1};
\node[state] (s2_2) at (-2,-3) {N of N1};
\node at (0,-3) {...};
\node[state,fill=green!30] (target) at (4,-4.5) {Target Board Str ("123450")};

\draw[level_conn] (s1_1) -- (s2_1);
\draw[level_conn] (s1_1) -- (s2_2);
\draw[level_conn, red, thick, dashed] (s1_3) -- (target); % Conceptual shortest path found

\node at (0,-5.5) [text width=8cm, align=center, draw, rounded corners, fill=yellow!10]
    {BFS explores states layer by layer. Path length = number of steps/levels. String representation of board state is key.};
\end{tikzpicture}
```

## 总结 (Summary)
- The Sliding Puzzle problem can be modeled as finding the shortest path in a state-space graph.
- BFS is suitable for finding this minimum number of moves.
- **Key steps in applying BFS:**
    1.  Represent board states uniquely (e.g., as strings).
    2.  Define how to generate neighboring states (valid moves of '0').
    3.  Use a `visited` set to avoid cycles and redundant work.
- The fixed small size of the board (2x3) keeps the state space manageable.
- Bidirectional BFS can offer a practical speedup.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Framework]], [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Graph BFS]]
Related Problems: [[Interview/Practice/LeetCode/LC752 - Open the Lock|LC752 - Open the Lock]]
