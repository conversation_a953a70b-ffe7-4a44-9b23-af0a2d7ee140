---
tags: [problem/leetcode, lc/hard, topic/graph, topic/bfs, pattern/bfs_shortest_path, topic/matrix, course/labuladong]
aliases: [LC773, LeetCode 773, Sliding Puzzle, 滑动谜题]
source_file_path: BFS 算法解题套路框架.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 773. Sliding Puzzle
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/BFS 算法解题套路框架.md|BFS 算法解题套路框架 by Labuladong]].

# LeetCode 773: Sliding Puzzle

## Problem Statement

On a 2x3 `board`, there are 5 tiles labeled `1` through `5`, and an empty square represented by `0`. A move consists of choosing `0` and a 4-directionally adjacent number and swapping it. The solved state is `[[1,2,3],[4,5,0]]`. Given an initial `board`, return the minimum number of moves to solve it, or -1 if unsolvable.

**Official Link:** [LeetCode 773. Sliding Puzzle](https://leetcode.com/problems/sliding-puzzle/)

## Solution Approach: BFS

This problem asks for the minimum number of moves to reach a target configuration, which is a shortest path problem in an unweighted state graph. BFS is the appropriate algorithm. See [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Framework]].

### Problem Abstraction for BFS

1.  **Node/State:** A string representation of the 2x3 board. For example, `[[4,1,2],[5,0,3]]` becomes `"412503"`.
2.  **`start_node`:** The stringified version of the initial `board`.
3.  **`target_node`:** The string `"123450"`.
4.  **`get_neighbors(board_str)`:**
    a.  Find the index of '0' in `board_str`.
    b.  Use a precomputed `neighbor_map` to determine which 1D indices are adjacent to the '0's current 1D index.
        - The 2x3 board indices `(row, col)` map to 1D string indices `idx = row * 3 + col`.
        - Example: `neighbor_map[idx_of_0]` gives list of `idx_to_swap_with`.
        - `![](/algo/images/sliding_puzzle/4.jpeg)` (Source: Labuladong) shows this 1D index mapping and adjacencies.
    c.  For each valid swap, generate a new board string. These are the neighbors.
5.  **Invalid States:** None explicitly, other than states already visited.

### Python Solution (Standard BFS)

```python
from collections import deque

class Solution:
    def _board_to_string(self, board: list[list[int]]) -> str:
        s = ""
        for r in range(2):
            for c in range(3):
                s += str(board[r][c])
        return s

    def _get_neighbors(self, board_str: str, neighbor_map: list[list[int]]) -> list[str]:
        neighbors = []
        zero_idx = -1
        for i, char_digit in enumerate(board_str):
            if char_digit == '0':
                zero_idx = i
                break

        for swap_idx in neighbor_map[zero_idx]:
            new_board_list = list(board_str)
            new_board_list[zero_idx], new_board_list[swap_idx] = new_board_list[swap_idx], new_board_list[zero_idx]
            neighbors.append("".join(new_board_list))
        return neighbors

    def slidingPuzzle(self, board: list[list[int]]) -> int:
        start_node_str = self._board_to_string(board)
        target_node_str = "123450"

        if start_node_str == target_node_str:
            return 0

        # Precomputed neighbor indices for 2x3 board (0-5)
        # 0 1 2
        # 3 4 5
        neighbor_map = [
            [1, 3],       # Neighbors of index 0
            [0, 2, 4],    # Neighbors of index 1
            [1, 5],       # Neighbors of index 2
            [0, 4],       # Neighbors of index 3
            [1, 3, 5],    # Neighbors of index 4
            [2, 4]        # Neighbors of index 5
        ]

        q = deque([start_node_str])
        visited = {start_node_str}
        steps = 0

        while q:
            level_size = len(q)
            for _ in range(level_size):
                current_board_str = q.popleft()

                if current_board_str == target_node_str:
                    return steps

                for neighbor_board_str in self._get_neighbors(current_board_str, neighbor_map):
                    if neighbor_board_str not in visited:
                        visited.add(neighbor_board_str)
                        q.append(neighbor_board_str)
            steps += 1

        return -1 # Target not reachable
```
Labuladong's visualization `![](/algo/images/sliding_puzzle/5.jpeg)` shows the process of solving `board = [[4,1,2],[5,0,3]]`.

**Complexity (Standard BFS):**
- Number of states: $6! = 720$ (permutations of 0-5).
- Each state has at most 4 neighbors.
- String operations (finding '0', creating new strings) take $O(L)$ where $L=6$ (board size).
- Time: $O(N_{states} \cdot K_{neighbors} \cdot L_{strlen})$. Since these are small constants, roughly $O(1)$ in terms of typical competitive programming scales, but more accurately $O(6! \cdot 4 \cdot 6)$.
- Space: $O(N_{states} \cdot L_{strlen})$ for `visited` set and `queue`.

### Bidirectional BFS
Since the target state is known, Bidirectional BFS can be applied for potential optimization, similar to [[Interview/Practice/LeetCode/LC752 - Open the Lock|LC752 Open the Lock]].

## Visualization
The state space is a graph where nodes are board configurations. BFS explores this graph layer by layer.
`![](/algo/images/sliding_puzzle/3.jpeg)` (Source: Labuladong - shows examples of neighbor states)

## 总结 (Summary)
- LC773 "Sliding Puzzle" is solved using BFS to find the minimum number of moves.
- States are represented as strings (flattened 2x3 board).
- Transitions involve swapping '0' with an adjacent tile. A precomputed `neighbor_map` for 1D indices helps.
- The state space is small ($6! = 720$), so BFS is efficient.
- Bidirectional BFS can also be applied.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Framework]]
Related Problems: [[Interview/Practice/LeetCode/LC752 - Open the Lock|LC752 - Open the Lock]]
