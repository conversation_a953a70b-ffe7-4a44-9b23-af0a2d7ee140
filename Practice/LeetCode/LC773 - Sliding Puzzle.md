---
tags: [problem/leetcode, lc/hard, topic/graph, topic/bfs, pattern/bfs_shortest_path, topic/matrix]
aliases: [LC773, LeetCode 773, Sliding Puzzle]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 773. Sliding Puzzle
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/BFS 算法解题套路框架.md]].

# LeetCode 773: Sliding Puzzle

## Problem Statement

On a 2x3 `board`, there are 5 tiles labeled `1` through `5`, and an empty square represented by `0`. A move consists of choosing `0` and a 4-directionally adjacent number and swapping it. The solved state is `[[1,2,3],[4,5,0]]`. Given an initial `board`, return the minimum number of moves to solve it, or -1 if unsolvable.

**Official Link:** [LeetCode 773. Sliding Puzzle](https://leetcode.com/problems/sliding-puzzle/)

## 🧩 Understanding the Sliding Puzzle

Think of this as a physical sliding puzzle where you slide numbered tiles into an empty space to arrange them in order.

```tikz
\begin{tikzpicture}[
    tile/.style={rectangle, draw, minimum size=1cm, font=\sffamily\bfseries, fill=blue!20},
    empty/.style={rectangle, draw, minimum size=1cm, font=\sffamily\bfseries, fill=gray!10},
    arrow/.style={->, thick, red},
    title/.style={font=\sffamily\bfseries, align=center}
]

% Target state (solved puzzle)
\node[title] at (1, 3.5) {Target State (Solved)};
\node[tile] at (0, 2.5) {1};
\node[tile] at (1, 2.5) {2};
\node[tile] at (2, 2.5) {3};
\node[tile] at (0, 1.5) {4};
\node[tile] at (1, 1.5) {5};
\node[empty] at (2, 1.5) {0};

% Example 1: One move to solve
\node[title] at (6, 3.5) {Example 1: One Move Away};
\node[tile] at (5, 2.5) {1};
\node[tile] at (6, 2.5) {2};
\node[tile] at (7, 2.5) {3};
\node[tile] at (5, 1.5) {4};
\node[empty] at (6, 1.5) {0};
\node[tile] at (7, 1.5) {5};

% Arrow showing the move
\draw[arrow] (6.3, 1.5) -- (6.7, 1.5);
\node at (6.5, 1) {\tiny Swap 0↔5};

% Example 2: Multiple moves needed
\node[title] at (11, 3.5) {Example 2: Multiple Moves};
\node[tile] at (10, 2.5) {4};
\node[tile] at (11, 2.5) {1};
\node[tile] at (12, 2.5) {2};
\node[tile] at (10, 1.5) {5};
\node[empty] at (11, 1.5) {0};
\node[tile] at (12, 1.5) {3};

\node at (11, 0.8) {\tiny Needs 5 moves};

% Movement rules
\node[title] at (6, 0) {Movement Rules};
\node at (6, -0.5) {\small • Only the empty space (0) can "move"};
\node at (6, -0.8) {\small • 0 swaps with adjacent tiles (up/down/left/right)};
\node at (6, -1.1) {\small • Goal: Arrange tiles as [1,2,3] on top, [4,5,0] on bottom};

\end{tikzpicture}
```

**Key Insights:**
- This is a **shortest path problem**: Find minimum moves to reach target
- Each board configuration is a **state** in our search space
- Each valid move creates an **edge** between states
- We need **BFS** to find the shortest path

## Solution Approach: BFS

This problem asks for the minimum number of moves, which signifies a shortest path problem on an unweighted graph. The states of the graph are the different configurations of the puzzle board. An edge exists between two board configurations if one can be transformed into the other by a single valid move (swapping '0' with an adjacent tile). This fits the [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Framework]].

## 🔄 Converting 2D Board to BFS Graph

The key insight is to treat each board configuration as a **node** in a graph, and each valid move as an **edge**.

### Step 1: State Representation

We convert the 2D board to a string for easy comparison and hashing:

```tikz
\begin{tikzpicture}[
    tile/.style={rectangle, draw, minimum size=0.8cm, font=\sffamily\small, fill=blue!20},
    empty/.style={rectangle, draw, minimum size=0.8cm, font=\sffamily\small, fill=gray!10},
    index/.style={font=\tiny, red},
    arrow/.style={->, thick, blue}
]

% 2D Board representation
\node at (1.5, 3) {\bfseries 2D Board};
\node[tile] at (0.5, 2) {1};
\node[tile] at (1.5, 2) {2};
\node[tile] at (2.5, 2) {3};
\node[tile] at (0.5, 1) {4};
\node[empty] at (1.5, 1) {0};
\node[tile] at (2.5, 1) {5};

% Index mapping
\node[index] at (0.5, 2.3) {0};
\node[index] at (1.5, 2.3) {1};
\node[index] at (2.5, 2.3) {2};
\node[index] at (0.5, 0.7) {3};
\node[index] at (1.5, 0.7) {4};
\node[index] at (2.5, 0.7) {5};

% Arrow
\draw[arrow] (3.5, 1.5) -- (5, 1.5);

% String representation
\node at (7, 3) {\bfseries String State};
\node at (7, 2) {\texttt{"123405"}};
\node at (7, 1.5) {\small Read row by row};
\node at (7, 1) {\small Index: 0→1, 1→2, 2→3, 3→4, 4→0, 5→5};

\end{tikzpicture}
```

### Step 2: Finding Valid Moves (Neighbors)

For each position, we precompute which positions the empty space (0) can move to:

```tikz
\begin{tikzpicture}[
    position/.style={circle, draw, minimum size=1cm, font=\sffamily\bfseries},
    highlight/.style={position, fill=yellow!30},
    neighbor/.style={position, fill=green!30},
    arrow/.style={<->, thick, red}
]

% Grid positions
\node at (2, 4) {\bfseries Position Adjacency Map};

% Position 0
\node[highlight] (p0) at (0, 2.5) {0};
\node[neighbor] (p1) at (1.5, 2.5) {1};
\node[neighbor] (p3) at (0, 1) {3};
\node[position] (p2) at (3, 2.5) {2};
\node[position] (p4) at (1.5, 1) {4};
\node[position] (p5) at (3, 1) {5};

\draw[arrow] (p0) -- (p1);
\draw[arrow] (p0) -- (p3);

\node at (5, 2.5) {\small Position 0 can move to: [1, 3]};

% Position 4 (center)
\node at (2, -0.5) {\bfseries Center Position (Most Connected)};
\node[position] (q0) at (0, -2) {0};
\node[neighbor] (q1) at (1.5, -2) {1};
\node[position] (q2) at (3, -2) {2};
\node[neighbor] (q3) at (0, -3.5) {3};
\node[highlight] (q4) at (1.5, -3.5) {4};
\node[neighbor] (q5) at (3, -3.5) {5};

\draw[arrow] (q4) -- (q1);
\draw[arrow] (q4) -- (q3);
\draw[arrow] (q4) -- (q5);

\node at (5, -2.5) {\small Position 4 can move to: [1, 3, 5]};

\end{tikzpicture}
```

### Step 3: BFS Components

1. **Start State:** String representation of initial board
2. **Target State:** `"123450"` (solved configuration)
3. **Neighbors:** All valid moves from current position of 0
4. **Goal:** Find shortest path from start to target

## 💡 Step-by-Step Solution

Let's build the solution piece by piece to understand each component:

### Step 1: Convert Board to String

```python
def board_to_string(board):
    """Convert 2D board to string representation"""
    result = []
    for r in range(2):
        for c in range(3):
            result.append(str(board[r][c]))
    return "".join(result)

# Example: [[1,2,3],[4,0,5]] → "123405"
```

### Step 2: Precompute Neighbor Map

```python
# For a 2x3 board, positions are indexed as:
# 0 1 2
# 3 4 5

neighbor_map = [
    [1, 3],    # Position 0 can move to positions 1, 3
    [0, 2, 4], # Position 1 can move to positions 0, 2, 4
    [1, 5],    # Position 2 can move to positions 1, 5
    [0, 4],    # Position 3 can move to positions 0, 4
    [1, 3, 5], # Position 4 can move to positions 1, 3, 5
    [2, 4]     # Position 5 can move to positions 2, 4
]
```

### Step 3: Generate Neighbors (Valid Moves)

```python
def get_neighbors(board_str, neighbor_map):
    """Generate all possible next states from current board state"""
    neighbors = []

    # Find position of empty space (0)
    zero_idx = board_str.index('0')

    # Try moving 0 to each adjacent position
    for neighbor_idx in neighbor_map[zero_idx]:
        # Create new board by swapping 0 with adjacent tile
        new_board = list(board_str)
        new_board[zero_idx], new_board[neighbor_idx] = new_board[neighbor_idx], new_board[zero_idx]
        neighbors.append("".join(new_board))

    return neighbors
```

### Step 4: Complete BFS Solution

```python
from collections import deque

class Solution:
    def slidingPuzzle(self, board: list[list[int]]) -> int:
        # Target state (solved puzzle)
        target = "123450"

        # Convert initial board to string
        start = self.board_to_string(board)

        # Early return if already solved
        if start == target:
            return 0

        # Precomputed adjacency map for 2x3 grid
        neighbor_map = [
            [1, 3],    [0, 2, 4], [1, 5],
            [0, 4],    [1, 3, 5], [2, 4]
        ]

        # BFS setup
        queue = deque([(start, 0)])  # (state, moves)
        visited = {start}

        while queue:
            current_state, moves = queue.popleft()

            # Generate all possible next states
            zero_pos = current_state.index('0')

            for next_pos in neighbor_map[zero_pos]:
                # Create new state by swapping 0 with adjacent tile
                new_state = list(current_state)
                new_state[zero_pos], new_state[next_pos] = new_state[next_pos], new_state[zero_pos]
                new_state_str = "".join(new_state)

                # Check if we reached the target
                if new_state_str == target:
                    return moves + 1

                # Add to queue if not visited
                if new_state_str not in visited:
                    visited.add(new_state_str)
                    queue.append((new_state_str, moves + 1))

        return -1  # No solution found

    def board_to_string(self, board):
        """Helper: Convert 2D board to string"""
        result = []
        for r in range(2):
            for c in range(3):
                result.append(str(board[r][c]))
        return "".join(result)
```
**Note on `steps` in BFS:** The provided solution `q.append((new_board_str, steps + 1))` directly associates the step count with the state in the queue. Labuladong's general BFS framework increments `steps` after processing each level. Both are valid ways to track steps. The general framework is:

```python
# General BFS framework step tracking
# q = deque([start_node])
# visited = {start_node}
# steps = 0
# while q:
#     level_size = len(q)
#     for _ in range(level_size):
#         curr = q.popleft()
#         if curr == target: return steps
#         for neighbor in get_neighbors(curr):
#             if neighbor not in visited:
#                 visited.add(neighbor)
#                 q.append(neighbor)
#     steps += 1
# return -1
```
The solution above integrates `steps` into the queue tuple `(state, steps_to_reach_state)`.

### Complexity
- **State Space:** The number of permutations of 6 items (0-5) is $6! = 720$. So, the number of states is relatively small.
- **Neighbors:** Max 4 neighbors (for '0' in a corner of 2x3, it's 2, edge is 3, center on longer side is 4).
- **Time:** $O(N \cdot k \cdot L)$, where $N$ is number of states (720), $k$ is max neighbors (4), $L$ is length of state string (6, for string ops like slicing/joining). Roughly constant time as state space is fixed and small. $O(6! \times \text{avg\_degree} \times \text{string\_len})$.
- **Space:** $O(N \cdot L)$ for `visited` set and `queue`. $O(6! \times \text{string\_len})$.

## Bidirectional BFS Optimization

Since the target state ("123450") is known, bidirectional BFS can be applied.
The logic is similar to [[Interview/Practice/LeetCode/LC752 - Open the Lock|LC752 Open the Lock's bidirectional approach]]:
- Maintain two sets `q1` (frontier from start) and `q2` (frontier from target).
- In each step, expand the smaller frontier.
- If a state generated from `q1`'s expansion is found in `q2`, the path is found.

The Python solution would involve managing two sets and alternating expansions, similar to the `openLock` bidirectional solution.

## 🔍 BFS Exploration Example

Let's trace through how BFS solves the puzzle `[[1,2,3],[4,0,5]]` → `[[1,2,3],[4,5,0]]`:

```tikz
\begin{tikzpicture}[
    board/.style={rectangle, draw, minimum size=0.4cm, font=\tiny},
    tile/.style={board, fill=blue!20},
    empty/.style={board, fill=gray!10},
    state_box/.style={draw, rounded corners, fill=yellow!10, font=\tiny, align=center},
    level_label/.style={font=\sffamily\bfseries, blue},
    arrow/.style={->, thick, red},
    solution_path/.style={->, very thick, green}
]

% Level 0 (Start)
\node[level_label] at (-1, 4) {Level 0\\(Start)};

% Initial state: "123405"
\node[state_box] (start) at (2, 4) {
    \begin{tikzpicture}[scale=0.3]
        \node[tile] at (0,1) {1}; \node[tile] at (1,1) {2}; \node[tile] at (2,1) {3};
        \node[tile] at (0,0) {4}; \node[empty] at (1,0) {0}; \node[tile] at (2,0) {5};
    \end{tikzpicture}\\
    "123405"\\
    0 moves
};

% Level 1 (First moves)
\node[level_label] at (-1, 2) {Level 1\\(1 move)};

% State 1: Move 0 up (swap with 2)
\node[state_box] (s1_1) at (0, 2) {
    \begin{tikzpicture}[scale=0.3]
        \node[tile] at (0,1) {1}; \node[empty] at (1,1) {0}; \node[tile] at (2,1) {3};
        \node[tile] at (0,0) {4}; \node[tile] at (1,0) {2}; \node[tile] at (2,0) {5};
    \end{tikzpicture}\\
    "103425"
};

% State 2: Move 0 left (swap with 4)
\node[state_box] (s1_2) at (2, 2) {
    \begin{tikzpicture}[scale=0.3]
        \node[tile] at (0,1) {1}; \node[tile] at (1,1) {2}; \node[tile] at (2,1) {3};
        \node[empty] at (0,0) {0}; \node[tile] at (1,0) {4}; \node[tile] at (2,0) {5};
    \end{tikzpicture}\\
    "123045"
};

% State 3: Move 0 right (swap with 5) - TARGET!
\node[state_box, fill=green!30] (target) at (4, 2) {
    \begin{tikzpicture}[scale=0.3]
        \node[tile] at (0,1) {1}; \node[tile] at (1,1) {2}; \node[tile] at (2,1) {3};
        \node[tile] at (0,0) {4}; \node[tile] at (1,0) {5}; \node[empty] at (2,0) {0};
    \end{tikzpicture}\\
    "123450"\\
    ✓ TARGET!
};

% Arrows showing BFS exploration
\draw[arrow] (start) -- (s1_1);
\draw[arrow] (start) -- (s1_2);
\draw[solution_path] (start) -- (target);

% BFS process explanation
\node[state_box, text width=6cm] at (8, 3) {
    \textbf{BFS Process:}\\
    1. Start with "123405"\\
    2. Find position of 0 (index 4)\\
    3. Generate neighbors: positions [1,3,5]\\
    4. Create new states by swapping\\
    5. Check if any equals "123450"\\
    6. Found target in 1 move!
};

% Queue visualization
\node[level_label] at (6, 0.5) {Queue After Level 0:};
\node[state_box] at (8, 0) {
    Queue: [("103425", 1), ("123045", 1), ("123450", 1)]\\
    When we dequeue "123450", we found the solution!
};

\end{tikzpicture}
```

**Key BFS Properties Demonstrated:**
- **Level-by-level exploration**: All 1-move states before any 2-move states
- **Shortest path guarantee**: First time we reach target = minimum moves
- **State representation**: String format enables easy comparison and hashing
- **Neighbor generation**: Systematic exploration of all valid moves

## 🎯 Key Takeaways & Problem-Solving Framework

### Why This Problem is Educational

This problem beautifully demonstrates several important algorithmic concepts:

1. **State Space Search**: Converting a physical puzzle into a graph problem
2. **BFS for Shortest Path**: Using BFS when all edges have equal weight (1 move)
3. **State Representation**: Choosing the right data structure (string) for states
4. **Neighbor Generation**: Systematically finding all valid next states

### Problem-Solving Framework

```tikz
\begin{tikzpicture}[
    step_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=3cm, align=center},
    arrow/.style={->, thick, blue}
]

\node[step_box] (s1) at (0, 4) {1. Identify Problem Type\\(Shortest path, unweighted)};
\node[step_box] (s2) at (4, 4) {2. Choose Algorithm\\(BFS for shortest path)};
\node[step_box] (s3) at (8, 4) {3. Design State\\(String representation)};

\node[step_box] (s4) at (0, 2) {4. Define Neighbors\\(Valid moves of 0)};
\node[step_box] (s5) at (4, 2) {5. Implement BFS\\(Queue + visited set)};
\node[step_box] (s6) at (8, 2) {6. Handle Edge Cases\\(Already solved, impossible)};

\draw[arrow] (s1) -- (s2);
\draw[arrow] (s2) -- (s3);
\draw[arrow] (s3) -- (s4);
\draw[arrow] (s4) -- (s5);
\draw[arrow] (s5) -- (s6);

\end{tikzpicture}
```

### Complexity Analysis
- **Time Complexity:** $O(6!)$ = $O(720)$ - At most 720 possible board states
- **Space Complexity:** $O(6!)$ = $O(720)$ - For visited set and queue
- **Practical Performance:** Very fast due to small, fixed state space

### When to Use This Pattern
- **Shortest path** in unweighted graphs
- **Minimum steps** to reach a goal state
- **State transformation** problems
- **Puzzle solving** with discrete moves

### Related Problems
- **8-Puzzle** (3x3 sliding puzzle)
- **Word Ladder** (transform one word to another)
- **Minimum Knight Moves** (chess knight shortest path)
- **Open the Lock** (combination lock states)

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Framework]], [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Graph BFS]]
Related Problems: [[Interview/Practice/LeetCode/LC752 - Open the Lock|LC752 - Open the Lock]]
