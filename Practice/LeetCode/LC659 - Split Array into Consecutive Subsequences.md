---
tags: [problem/leetcode, lc/medium, topic/array, topic/greedy, topic/heap, pattern/greedy_choice, lc/lc659, course/labuladong]
aliases: [LC659, Split Array into Consecutive Subsequences]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/谁能想到，斗地主也能玩出算法.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 659. Split Array into Consecutive Subsequences
> Discussed in [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/谁能想到，斗地主也能玩出算法.md|谁能想到，斗地主也能玩出算法 by Labuladong]].
> Solution uses pattern from [[Interview/Concept/Algorithms/Greedy Algorithms/Split Array into Consecutive Subsequences Pattern|Split Array into Consecutive Subsequences Pattern]].

# LeetCode 659: Split Array into Consecutive Subsequences

## Problem Statement
You are given an integer array `nums` that is sorted in non-decreasing order.
Determine if it is possible to split `nums` into one or more subsequences such that both of the following conditions are true:
- Each subsequence is a consecutive increasing sequence (i.e. each integer is exactly one more than the previous integer).
- All subsequences have a length of `3` or more.
Return `true` *if you can split `nums` according to the above conditions, or `false` otherwise*.

**Official Link:** [LeetCode 659. Split Array into Consecutive Subsequences](https://leetcode.com/problems/split-array-into-consecutive-subsequences/)

## Solution Approach: Greedy with Frequency and Need Maps

This problem is solved using a greedy strategy as detailed in [[Interview/Concept/Algorithms/Greedy Algorithms/Split Array into Consecutive Subsequences Pattern|Split Array into Consecutive Subsequences Pattern]].
We use two hash maps:
1. `freq`: To store the frequency of each number in `nums`.
2. `need`: To store how many subsequences are currently ending at `x-1` and thus "need" `x` to continue.

### Python Solution
```python
import collections

class Solution:
    def isPossible(self, nums: list[int]) -> bool:
        freq = collections.Counter(nums)
        # need[x] = count of subsequences ending at x-1, looking for x
        need = collections.defaultdict(int)

        for num in nums:
            if freq[num] == 0: # num already used up
                continue

            if need[num] > 0:
                # num can append to an existing subsequence
                freq[num] -= 1
                need[num] -= 1      # One less subsequence needs num
                need[num + 1] += 1  # This subsequence now needs num + 1
            elif freq[num] > 0 and freq.get(num + 1, 0) > 0 and freq.get(num + 2, 0) > 0:
                # num can start a new subsequence [num, num+1, num+2]
                freq[num] -= 1
                freq[num + 1] -= 1
                freq[num + 2] -= 1
                need[num + 3] += 1 # This new subsequence now needs num + 3
            else:
                # num cannot be placed
                return False

        return True
```

## Complexity Analysis
- **Time Complexity:** $O(N)$, where $N$ is the length of `nums`. We iterate through `nums` once, and hash map operations are $O(1)$ on average.
- **Space Complexity:** $O(N)$ for the `freq` and `need` hash maps in the worst case (if all numbers are distinct).

## 总结 (Summary)
LC659 asks if a sorted array can be partitioned into consecutive subsequences of length at least 3. A greedy approach works: for each number, try to append it to an existing valid subsequence. If not possible, try to start a new valid subsequence with it. If neither is possible, the partitioning fails. Frequency maps help manage available numbers and needed numbers efficiently.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
