---
tags: [problem/leetcode, lc/medium, topic/dynamic_programming, pattern/knapsack, pattern/memoization, pattern/tabulation]
aliases: [LC322, LeetCode 322, Coin Change, 零钱兑换]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 322. Coin Change
> Solution approaches (brute-force, memoization, tabulation) adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/动态规划解题套路框架.md]].

# LeetCode 322: Coin Change

## Problem Statement

You are given an integer array `coins` representing coins of different denominations and an integer `amount` representing a total amount of money.
Return *the fewest number of coins that you need to make up that amount*. If that amount of money cannot be made up by any combination of the coins, return `-1`.
You may assume that you have an infinite number of each kind of coin.

**Official Link:** [LeetCode 322. Coin Change](https://leetcode.com/problems/coin-change/)

## 🪙 Understanding the Coin Change Problem

Think of this as a real-world scenario: you're a cashier trying to give change using the fewest coins possible.

```tikz
\begin{tikzpicture}[
    coin/.style={circle, draw, minimum size=1cm, font=\sffamily\bfseries, fill=yellow!30},
    amount_box/.style={rectangle, draw, fill=green!20, font=\sffamily\bfseries, minimum width=2cm, minimum height=1cm},
    solution_box/.style={rectangle, draw, fill=blue!20, font=\sffamily\small, text width=4cm, align=center},
    arrow/.style={->, thick, red}
]

% Available coins
\node at (2, 4.5) {\bfseries Available Coins (Unlimited)};
\node[coin] at (0.5, 3.5) {1};
\node[coin] at (2, 3.5) {2};
\node[coin] at (3.5, 3.5) {5};

% Target amount
\node[amount_box] at (2, 2) {Amount: 11};

% Arrow pointing down
\draw[arrow] (2, 1.5) -- (2, 0.5);

% Possible solutions
\node at (2, 0) {\bfseries Find Minimum Coins};

% Solution 1: Greedy (not always optimal)
\node[solution_box] at (-1, -1.5) {
    Greedy Approach:\\
    5 + 5 + 1 = 11\\
    \textbf{3 coins} YES
};

% Solution 2: Suboptimal
\node[solution_box] at (2, -1.5) {
    Suboptimal:\\
    2 + 2 + 2 + 2 + 2 + 1 = 11\\
    \textbf{6 coins} NO
};

% Solution 3: Another suboptimal
\node[solution_box] at (5, -1.5) {
    Very Suboptimal:\\
    1 + 1 + 1 + ... + 1 = 11\\
    \textbf{11 coins} NO
};

% Key insight
\node[solution_box, fill=orange!20] at (2, -3.5) {
    \textbf{Key Insight:}\\
    We need to systematically explore\\
    all possibilities to find the\\
    true minimum!
};

\end{tikzpicture}
```

**Example Analysis:**
- **Input:** `coins = [1,2,5]`, `amount = 11`
- **Output:** `3` (using coins: 5 + 5 + 1)
- **Challenge:** Greedy doesn't always work! We need dynamic programming.

## Solution Approach: Dynamic Programming

This is a classic optimization problem ("fewest number") suitable for dynamic programming. We follow Labuladong's DP framework from [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|DP Introduction and Framework]].

## 🧠 DP Framework: Breaking Down the Problem

Let's systematically apply the dynamic programming framework to understand this problem:

```tikz
\begin{tikzpicture}[
    framework_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=5cm, align=left, minimum height=1.5cm},
    example_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\tiny, text width=3cm, align=center},
    arrow/.style={->, thick, blue}
]

% Framework steps
\node[framework_box] (step1) at (0, 4) {
    \textbf{1. Define State}\\
    State = amount remaining\\
    dp(n) = min coins for amount n
};

\node[framework_box] (step2) at (6, 4) {
    \textbf{2. Identify Choices}\\
    For amount n, try each coin c\\
    Choice: use coin c or not
};

\node[framework_box] (step3) at (0, 2) {
    \textbf{3. State Transition}\\
    dp(n) = min\{1 + dp(n-c)\}\\
    for all valid coins c
};

\node[framework_box] (step4) at (6, 2) {
    \textbf{4. Base Cases}\\
    dp(0) = 0 (no coins needed)\\
    dp(k) = INFINITY if k $<$ 0 (impossible)
};

% Arrows
\draw[arrow] (step1) -- (step2);
\draw[arrow] (step2) -- (step4);
\draw[arrow] (step4) -- (step3);
\draw[arrow] (step3) -- (step1);

% Example illustration
\node[example_box] at (3, 0) {
    \textbf{Example:}\\
    dp(11) with coins [1,2,5]\\
    = min\{\\
    \quad 1 + dp(10), \quad \textcolor{gray}{// use coin 1}\\
    \quad 1 + dp(9), \quad \textcolor{gray}{// use coin 2}\\
    \quad 1 + dp(6) \quad \textcolor{gray}{// use coin 5}\\
    \}
};

\end{tikzpicture}
```

### 🔍 Detailed Framework Application

1. **State Definition:** `dp(n)` = minimum coins needed to make amount `n`
2. **Choice Analysis:** For any amount, we can choose any coin that doesn't exceed the amount
3. **Recurrence Relation:** $dp(n) = \min_{c \in \text{coins, } c \leq n} \{1 + dp(n-c)\}$
4. **Base Cases:**
   - $dp(0) = 0$ (zero coins for zero amount)
   - $dp(k) = \infty$ for $k < 0$ (impossible state)

## 💡 Solution Evolution: From Naive to Optimal

Let's build our understanding by evolving from a simple recursive solution to an optimized DP solution.

### 🌳 Approach 1: Recursive Tree (Brute Force)

First, let's visualize how the recursive approach explores all possibilities:

```tikz
\begin{tikzpicture}[
    tree_node/.style={circle, draw, minimum size=1.2cm, font=\sffamily\small},
    leaf_node/.style={tree_node, fill=green!30},
    invalid_node/.style={tree_node, fill=red!30},
    level/.style={font=\sffamily\bfseries, blue}
]

% Root
\node[tree_node] (root) at (4, 6) {dp(6)};
\node[level] at (-1, 6) {Level 0};

% Level 1
\node[tree_node] (l1_1) at (1, 4.5) {dp(5)};
\node[tree_node] (l1_2) at (4, 4.5) {dp(4)};
\node[tree_node] (l1_3) at (7, 4.5) {dp(1)};
\node[level] at (-1, 4.5) {Level 1};

% Level 2 (partial)
\node[tree_node] (l2_1) at (0, 3) {dp(4)};
\node[tree_node] (l2_2) at (1.5, 3) {dp(3)};
\node[leaf_node] (l2_3) at (3, 3) {dp(0)};
\node[tree_node] (l2_4) at (5, 3) {dp(2)};
\node[leaf_node] (l2_5) at (6.5, 3) {dp(0)};
\node[invalid_node] (l2_6) at (8, 3) {dp(-1)};
\node[level] at (-1, 3) {Level 2};

% Edges with coin labels
\draw[->] (root) -- (l1_1) node[midway, above left, font=\tiny] {coin 1};
\draw[->] (root) -- (l1_2) node[midway, above, font=\tiny] {coin 2};
\draw[->] (root) -- (l1_3) node[midway, above right, font=\tiny] {coin 5};

\draw[->] (l1_1) -- (l2_1) node[midway, above left, font=\tiny] {1};
\draw[->] (l1_1) -- (l2_2) node[midway, above, font=\tiny] {2};
\draw[->] (l1_2) -- (l2_3) node[midway, above left, font=\tiny] {2};
\draw[->] (l1_2) -- (l2_4) node[midway, above right, font=\tiny] {2};
\draw[->] (l1_3) -- (l2_5) node[midway, above left, font=\tiny] {1};
\draw[->] (l1_3) -- (l2_6) node[midway, above right, font=\tiny] {2};

% Problem annotation
\node[rectangle, draw, fill=orange!20, text width=4cm, align=center] at (10, 5) {
    \textbf{Problem:}\\
    Many subproblems\\
    are computed\\
    multiple times!\\[0.5em]
    dp(4) appears twice\\
    dp(0) appears twice
};

\end{tikzpicture}
```

**Recursive Implementation:**
```python
def coinChange_recursive(self, coins: list[int], amount: int) -> int:
    def dp(n: int) -> int:
        # Base cases
        if n == 0: return 0
        if n < 0: return float('inf')

        min_coins = float('inf')
        for coin in coins:
            sub_result = dp(n - coin)
            min_coins = min(min_coins, 1 + sub_result)

        return min_coins

    result = dp(amount)
    return result if result != float('inf') else -1
```

**Problems:**
- **Time Complexity:** $O(C^A)$ - exponential due to overlapping subproblems
- **Space Complexity:** $O(A)$ - recursion stack depth
- **Performance:** Times out for large inputs

### 🧠 Approach 2: Memoization (Top-Down DP)

The key insight: **store results to avoid recomputation!**

```tikz
\begin{tikzpicture}[
    memo_box/.style={rectangle, draw, fill=blue!20, font=\sffamily\tiny, minimum width=1cm, minimum height=0.8cm},
    computed/.style={memo_box, fill=green!30},
    computing/.style={memo_box, fill=yellow!30},
    arrow/.style={->, thick, red}
]

% Memo table
\node at (3, 4.5) {\bfseries Memoization Table};
\node at (0, 4) {Amount:};
\node at (0, 3.5) {dp(n):};

% Table headers
\foreach \i in {0,1,2,3,4,5,6} {
    \node at (\i+1, 4) {\i};
}

% Initial state
\node[computed] at (1, 3.5) {0};
\foreach \i in {2,3,4,5,6,7} {
    \node[memo_box] at (\i, 3.5) {?};
}

% Computing dp(6)
\node[computing] at (7, 3.5) {?};
\node at (3, 2.5) {\small Computing dp(6)...};

% Recursive calls
\node at (3, 2) {\tiny dp(6) needs: dp(5), dp(4), dp(1)};
\node at (3, 1.5) {\tiny dp(5) needs: dp(4), dp(3), dp(0) YES};
\node at (3, 1) {\tiny dp(4) computed once, reused!};

% Final state
\node at (3, 0) {\bfseries After computation:};
\node[computed] at (1, -0.5) {0};
\node[computed] at (2, -0.5) {1};
\node[computed] at (3, -0.5) {1};
\node[computed] at (4, -0.5) {2};
\node[computed] at (5, -0.5) {2};
\node[computed] at (6, -0.5) {1};
\node[computed] at (7, -0.5) {2};

\end{tikzpicture}
```

**Memoized Implementation:**
```python
def coinChange_memoization(self, coins: list[int], amount: int) -> int:
    memo = {}  # Cache for computed results

    def dp(n: int) -> int:
        # Base cases
        if n == 0: return 0
        if n < 0: return float('inf')

        # Check cache first
        if n in memo:
            return memo[n]

        # Compute minimum coins
        min_coins = float('inf')
        for coin in coins:
            sub_result = dp(n - coin)
            min_coins = min(min_coins, 1 + sub_result)

        # Store in cache
        memo[n] = min_coins
        return min_coins

    result = dp(amount)
    return result if result != float('inf') else -1
```

**Improvements:**
- **Time Complexity:** $O(A \times C)$ - each subproblem computed once
- **Space Complexity:** $O(A)$ - memo table + recursion stack
- **Performance:** Much faster! No redundant calculations

### 🏗️ Approach 3: Tabulation (Bottom-Up DP)

The most intuitive approach: **build the solution from the ground up!**

Let's trace through building the DP table for `coins = [1,2,5]`, `amount = 11`:

```tikz
\begin{tikzpicture}[
    dp_cell/.style={rectangle, draw, minimum width=1cm, minimum height=0.8cm, font=\sffamily\small},
    base_case/.style={dp_cell, fill=green!30},
    computing/.style={dp_cell, fill=yellow!30},
    computed/.style={dp_cell, fill=blue!20},
    impossible/.style={dp_cell, fill=red!20}
]

% Step 1: Initialize
\node at (6, 5.5) {\bfseries Step 1: Initialize DP Table};
\node at (0, 5) {Amount:};
\foreach \i in {0,1,2,3,4,5,6,7,8,9,10,11} {
    \node at (\i+1, 5) {\tiny \i};
}

\node at (0, 4.5) {dp[i]:};
\node[base_case] at (1, 4.5) {0};
\foreach \i in {2,3,4,5,6,7,8,9,10,11,12} {
    \node[dp_cell] at (\i, 4.5) {\tiny INFINITY};
}

% Step 2: Computing dp[1]
\node at (6, 3.5) {\bfseries Step 2: Computing dp[1]};
\node at (0, 3) {dp[i]:};
\node[base_case] at (1, 3) {0};
\node[computing] at (2, 3) {?};
\foreach \i in {3,4,5,6,7,8,9,10,11,12} {
    \node[dp_cell] at (\i, 3) {\tiny INFINITY};
}
\node at (6, 2.5) {\tiny Try coins [1,2,5]: min(INFINITY, 1+dp[0]) = 1};

% Step 3: Computing dp[2]
\node at (6, 1.5) {\bfseries Step 3: Computing dp[2]};
\node at (0, 1) {dp[i]:};
\node[base_case] at (1, 1) {0};
\node[computed] at (2, 1) {1};
\node[computing] at (3, 1) {?};
\foreach \i in {4,5,6,7,8,9,10,11,12} {
    \node[dp_cell] at (\i, 1) {\tiny INFINITY};
}
\node at (6, 0.5) {\tiny Try coins: min(INFINITY, 1+dp[1], 1+dp[0]) = 1};

% Final result
\node at (6, -0.5) {\bfseries Final DP Table};
\node at (0, -1) {dp[i]:};
\node[base_case] at (1, -1) {0};
\node[computed] at (2, -1) {1};
\node[computed] at (3, -1) {1};
\node[computed] at (4, -1) {2};
\node[computed] at (5, -1) {2};
\node[computed] at (6, -1) {1};
\node[computed] at (7, -1) {2};
\node[computed] at (8, -1) {2};
\node[computed] at (9, -1) {3};
\node[computed] at (10, -1) {2};
\node[computed] at (11, -1) {3};
\node[computed] at (12, -1) {3};

\end{tikzpicture}
```

**Tabulation Implementation:**
```python
def coinChange(self, coins: list[int], amount: int) -> int:
    # Initialize DP table
    # Use amount + 1 as "infinity" (impossible value)
    dp = [amount + 1] * (amount + 1)

    # Base case: 0 coins needed for amount 0
    dp[0] = 0

    # Fill table bottom-up
    for i in range(1, amount + 1):
        # Try each coin
        for coin in coins:
            if i >= coin:  # Can use this coin
                dp[i] = min(dp[i], 1 + dp[i - coin])

    # Return result or -1 if impossible
    return dp[amount] if dp[amount] != amount + 1 else -1
```

**Why This Works:**
- **Bottom-up construction:** Build solutions for smaller amounts first
- **Optimal substructure:** dp[i] uses optimal solutions for smaller amounts
- **No recursion:** Iterative approach avoids stack overflow
- **Clear logic:** Easy to trace and debug

**Complexity:**
- **Time:** $O(A \times C)$ - two nested loops
- **Space:** $O(A)$ - DP table only

## 🎯 Key Takeaways & Problem-Solving Framework

### Why This Problem is Educational

This problem beautifully demonstrates the evolution of algorithmic thinking:

```tikz
\begin{tikzpicture}[
    approach_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=2cm},
    arrow/.style={->, thick, green},
    complexity/.style={font=\tiny, red}
]

\node[approach_box] (naive) at (0, 3) {
    \textbf{Naive Recursion}\\[0.5em]
    • Natural thinking\\
    • Easy to understand\\
    • Exponential time\\
    • Times out
};

\node[approach_box] (memo) at (4.5, 3) {
    \textbf{Memoization}\\[0.5em]
    • Add caching\\
    • Same logic\\
    • Polynomial time\\
    • Top-down
};

\node[approach_box] (tab) at (9, 3) {
    \textbf{Tabulation}\\[0.5em]
    • Bottom-up\\
    • No recursion\\
    • Most efficient\\
    • Easiest to debug
};

\draw[arrow] (naive) -- (memo);
\draw[arrow] (memo) -- (tab);

\node[complexity] at (0, 1.5) {$O(C^A)$ time};
\node[complexity] at (4.5, 1.5) {$O(A \times C)$ time};
\node[complexity] at (9, 1.5) {$O(A \times C)$ time};

\node at (4.5, 0.5) {\bfseries Progressive Optimization};

\end{tikzpicture}
```

### Problem-Solving Pattern

**When to Use This Approach:**
- **Optimization problems** (minimum/maximum)
- **Overlapping subproblems** exist
- **Optimal substructure** property holds
- **Choices** at each step affect the outcome

### Complexity Comparison

| Approach | Time | Space | Pros | Cons |
|----------|------|-------|------|------|
| Recursive | $O(C^A)$ | $O(A)$ | Simple logic | Too slow |
| Memoization | $O(A \times C)$ | $O(A)$ | Natural thinking | Recursion overhead |
| Tabulation | $O(A \times C)$ | $O(A)$ | Most efficient | Requires planning |

### Related Problems
- **Unbounded Knapsack** (same pattern, different context)
- **Perfect Squares** (minimum squares to sum to n)
- **Minimum Path Sum** (grid-based DP)
- **Word Break** (string-based DP)

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|DP Introduction and Framework]], [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Knapsack/Unbounded Knapsack|Unbounded Knapsack]] (as this is a form of it)
Related Problems: [[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 - Fibonacci Number]]
