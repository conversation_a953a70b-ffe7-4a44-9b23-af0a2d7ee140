---
tags: [problem/leetcode, lc/medium, topic/dynamic_programming, pattern/knapsack, pattern/memoization, pattern/tabulation]
aliases: [LC322, LeetCode 322, Coin Change, 零钱兑换]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 322. Coin Change
> Solution approaches (brute-force, memoization, tabulation) adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/动态规划解题套路框架.md]].

# LeetCode 322: Coin Change

## Problem Statement

You are given an integer array `coins` representing coins of different denominations and an integer `amount` representing a total amount of money.
Return *the fewest number of coins that you need to make up that amount*. If that amount of money cannot be made up by any combination of the coins, return `-1`.
You may assume that you have an infinite number of each kind of coin.

**Official Link:** [LeetCode 322. Coin Change](https://leetcode.com/problems/coin-change/)

**Example 1:**
Input: `coins = [1,2,5]`, `amount = 11`
Output: `3`
Explanation: `11 = 5 + 5 + 1`

## Solution Approach: Dynamic Programming

This is a classic optimization problem ("fewest number") suitable for dynamic programming. We follow Labuladong's DP framework from [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|DP Introduction and Framework]].

### DP Framework Application

1.  **Define States:** The primary state that changes is the `amount` we are trying to make change for. So, `dp(n)` will represent something about amount `n`.
2.  **Identify Choices:** For a given `amount`, the choices are which coin `c` from the `coins` array to use. If we use coin `c`, the remaining amount to make change for becomes `amount - c`.
3.  **Define `dp` function/array meaning:** `dp(n)` = the fewest number of coins needed to make up amount `n`.
4.  **Formulate State Transition Equation:**
    To compute `dp(n)`, we consider each coin `c` in `coins`:
    If we use coin `c`, we need `1 + dp(n-c)` coins in total.
    We want the minimum among all choices of `c`.
    So, $dp(n) = \min_{c \in \text{coins, } n \ge c} \{1 + dp(n-c)\}$.
5.  **Base Cases:**
    - $dp(0) = 0$ (0 coins needed to make amount 0).
    - $dp(k) = -1$ or $\infty$ (if $k < 0$, representing an invalid state or impossibility). We use $\infty$ for `min` calculations and convert to -1 for final output if still $\infty$.

### 1. Brute-Force Recursion (Top-Down)
This directly implements the recurrence.
```python
class Solution:
    def coinChange_recursive(self, coins: list[int], amount: int) -> int:
        
        def dp(n: int) -> int:
            # Base cases
            if n == 0: return 0
            if n < 0: return float('inf') # Use infinity for min() to work, effectively -1

            min_coins = float('inf')
            for coin in coins:
                sub_problem_result = dp(n - coin)
                # If sub_problem_result is inf, it means n-coin is not makeable
                # Adding 1 to inf is still inf.
                min_coins = min(min_coins, 1 + sub_problem_result)
            
            return min_coins

        result = dp(amount)
        return result if result != float('inf') else -1

# This will TLE (Time Limit Exceeded) due to recomputing overlapping subproblems.
# Example: coins = [1,2,5], amount = 11.
# dp(11) depends on dp(10), dp(9), dp(6)
# dp(10) depends on dp(9), dp(8), dp(5)
# dp(9) is computed multiple times.
```
- **Complexity:** Exponential, $O(C^A)$ where $C$ is number of coins, $A$ is amount, due to overlapping subproblems.
- Labuladong's visualizer `div_coin-change-brute-force` would show this large tree.

### 2. Top-Down with Memoization (Recursive DP)
Store results of `dp(n)` to avoid recomputation.
```python
class Solution:
    def coinChange_memoization(self, coins: list[int], amount: int) -> int:
        memo = {} # Using a dictionary for memoization

        def dp(n: int) -> int:
            if n == 0: return 0
            if n < 0: return float('inf') # Represents impossibility for min calculation
            
            if n in memo:
                return memo[n]

            min_coins = float('inf')
            for coin in coins:
                sub_problem_result = dp(n - coin)
                # No need to check if sub_problem_result is inf before adding 1,
                # 1 + inf is still inf. min() will handle it.
                min_coins = min(min_coins, 1 + sub_problem_result)
            
            memo[n] = min_coins
            return min_coins

        result = dp(amount)
        return result if result != float('inf') else -1
```
- **Time Complexity:** $O(A \cdot C)$, where $A$ is `amount` and $C$ is `len(coins)`. Each state `dp(i)` from `0` to `A` is computed once. Each computation involves a loop of $C$ coins.
- **Space Complexity:** $O(A)$ for the `memo` and $O(A)$ for recursion stack depth in worst case (e.g., using only coin '1').
- Labuladong's visualizer `div_coin-change` shows the effect of memoization.

### 3. Bottom-Up with Tabulation (Iterative DP)
Build a `dp_table` from base cases up to `amount`.
`dp_table[i]` = fewest coins to make amount `i`.
```python
class Solution:
    def coinChange(self, coins: list[int], amount: int) -> int: # Final DP solution
        # dp_table[i] will store the minimum coins to make amount i
        # Initialize with a value larger than any possible coin count (e.g., amount + 1)
        # amount + 1 is a good "infinity" because max coins for amount A is A (using all 1s)
        dp_table = [amount + 1] * (amount + 1)
        
        # Base case
        dp_table[0] = 0
        
        # Iterate for each amount from 1 to `amount`
        for i in range(1, amount + 1):
            # For each coin, see if it can improve dp_table[i]
            for coin in coins:
                if i - coin >= 0: # If current amount `i` can accommodate `coin`
                    # dp_table[i - coin] is min coins for amount `i-coin`
                    # Add 1 for the current `coin`
                    dp_table[i] = min(dp_table[i], 1 + dp_table[i - coin])
        
        # If dp_table[amount] is still amount + 1, it means amount cannot be made
        return dp_table[amount] if dp_table[amount] != amount + 1 else -1

```
- **Time Complexity:** $O(A \cdot C)$. Outer loop runs $A$ times, inner loop $C$ times.
- **Space Complexity:** $O(A)$ for `dp_table`.

## Visualization
Labuladong's image for the DP table calculation process:
![](/algo/images/dynamic-programming/6.jpg)
This shows how `dp[i]` is derived from `dp[i-coin]`. For `dp_table[i]`, you're essentially trying each `coin` as the *last coin* used to reach `i`, and taking the best option.

## 总结 (Summary)
- The Coin Change problem asks for the minimum number of coins to make a target amount, a classic DP optimization problem.
- **State:** `amount` to be made.
- **Choices:** Which `coin` to use next.
- **DP Definition:** `dp(n)` = min coins for amount `n`.
- **State Transition:** $dp(n) = \min(1 + dp(n-c))$ for all $c \in coins$.
- Solvable with brute-force recursion (inefficient), memoized recursion ($O(AC)$ time, $O(A)$ space), or tabulation ($O(AC)$ time, $O(A)$ space).
- The tabulation approach is often preferred for its explicitness and avoidance of recursion depth issues.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|DP Introduction and Framework]], [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Knapsack/Unbounded Knapsack|Unbounded Knapsack]] (as this is a form of it)
Related Problems: [[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 - Fibonacci Number]]
