---
tags: [problem/leetcode, lc/medium, topic/data_structure_design, topic/hash_table, topic/heap, topic/linked_list, pattern/oop_design]
aliases: [LC355, Design Twitter]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/设计朋友圈时间线功能.md
---

# LC355 - Design Twitter

## Problem Statement
(To be filled from LeetCode - Visit [https://leetcode.com/problems/design-twitter/](https://leetcode.com/problems/design-twitter/))

## Solution Approach
This problem is solved using Object-Oriented Design principles combined with heaps for merging feeds, as detailed in [[Interview/Concept/Data Structures/Custom Design/01 - Design Twitter Timeline (OOP with Heap)|Design Twitter Timeline Algorithm]].

### Python Solution (from Concept Note)
```python
import heapq
import collections

# Global timestamp for tweets
_global_time = 0

class Tweet:
    def __init__(self, id: int, time: int):
        self.id = id
        self.time = time
        self.next = None # Points to the previous tweet by this user

    # For heapq to work as a max-heap based on time
    def __lt__(self, other: 'Tweet'):
        return self.time > other.time # Max-heap stores largest time first

class User:
    def __init__(self, userId: int):
        self.id = userId
        self.followed = {userId} # User follows self by default
        self.head_tweet = None # Head of user's tweet linked list (most recent)

    def follow(self, followeeId: int):
        self.followed.add(followeeId)

    def unfollow(self, followeeId: int):
        if followeeId != self.id: # Cannot unfollow self
            self.followed.discard(followeeId)

    def post(self, tweetId: int):
        global _global_time
        _global_time += 1
        twt = Tweet(tweetId, _global_time)
        twt.next = self.head_tweet 
        self.head_tweet = twt

class Twitter:
    def __init__(self):
        self.user_map = collections.defaultdict(User) # userId -> User object
        # Reset global time for each Twitter instance (if tests run this way)
        # Or, ensure _global_time is managed per instance if needed.
        # LeetCode usually re-initializes the Solution class for each test suite.
        global _global_time
        _global_time = 0


    def _ensure_user(self, userId: int) -> User:
        # This function is slightly problematic with defaultdict(User) as it might create 
        # user by just accessing. A direct check and create is safer.
        # However, defaultdict(User) will call User(userId) if key missing, BUT User.__init__ needs userId.
        # So, defaultdict(User) won't work correctly without userId.
        # Let's adjust:
        if userId not in self.user_map:
            self.user_map[userId] = User(userId)
        return self.user_map[userId]


    def postTweet(self, userId: int, tweetId: int) -> None:
        user = self._ensure_user(userId)
        user.post(tweetId)
        

    def getNewsFeed(self, userId: int) -> list[int]:
        # Ensure user exists, though if they don't, they follow no one (not even self if not created)
        # So, it might be okay to just check. If user doesn't exist, they have no followed set.
        if userId not in self.user_map: # User has no activity, no follows (not even self)
            return []

        user = self.user_map[userId]
        followed_ids = user.followed
        
        pq = [] # Min-heap, but we store Tweet objects with custom __lt__ for max-heap by time

        for uid in followed_ids:
            if uid in self.user_map: # Check if the followed user actually exists / has posts
                followed_user_obj = self.user_map[uid]
                if followed_user_obj.head_tweet:
                    heapq.heappush(pq, followed_user_obj.head_tweet)
        
        news_feed = []
        while pq and len(news_feed) < 10:
            tweet = heapq.heappop(pq)
            news_feed.append(tweet.id)
            if tweet.next:
                heapq.heappush(pq, tweet.next)
                
        return news_feed

    def follow(self, followerId: int, followeeId: int) -> None:
        follower = self._ensure_user(followerId)
        # Ensure followee exists so their ID is valid, though User obj itself isn't strictly needed here
        self._ensure_user(followeeId) 
        follower.follow(followeeId)

    def unfollow(self, followerId: int, followeeId: int) -> None:
        # If followerId doesn't exist, no action needed or error
        if followerId in self.user_map:
            # ensure_user for followeeId is not strictly needed for unfollow logic.
            self.user_map[followerId].unfollow(followeeId)

# Your Twitter object will be instantiated and called as such:
# obj = Twitter()
# obj.postTweet(userId,tweetId)
# param_2 = obj.getNewsFeed(userId)
# obj.follow(followerId,followeeId)
# obj.unfollow(followerId,followeeId)
```

## Complexity Analysis
- `postTweet`: $O(1)$
- `getNewsFeed`: $O(K \log K + 10 \log K)$ where $K$ is number of followed users. Roughly $O(K \log K)$.
- `follow`/`unfollow`: $O(1)$ average for set operations.
- Space: $O(NumUsers + TotalTweets)$

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
