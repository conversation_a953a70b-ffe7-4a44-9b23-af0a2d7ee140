---
tags: [problem/leetcode, lc/medium, topic/graph, topic/bfs, pattern/bfs_shortest_path, pattern/bidirectional_bfs, course/labuladong]
aliases: [LC752, LeetCode 752, Open the Lock, 打开转盘锁]
source_file_path: BFS 算法解题套路框架.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 752. Open the Lock
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/BFS 算法解题套路框架.md|BFS 算法解题套路框架 by Labuladong]].

# LeetCode 752: Open the Lock

## Problem Statement

You have a lock with four circular wheels, each with digits '0'-'9'. The lock starts at "0000". You are given a list of `deadends` (strings representing locked states) and a `target` string. Each move consists of turning one wheel one digit. Find the minimum number of turns required to reach the `target` from "0000" without passing through any `deadends`. If it's impossible, return -1.

**Official Link:** [LeetCode 752. Open the Lock](https://leetcode.com/problems/open-the-lock/)

**Example 1:**
Input: `deadends = ["0201","0101","0102","1212","2002"]`, `target = "0202"`
Output: `6`
Explanation: A possible sequence: "0000" -> "1000" -> "1100" -> "1200" -> "1201" -> "1202" -> "0202".

## Solution Approach: BFS

This problem asks for the minimum number of turns, which is a classic shortest path problem on an unweighted graph. The states of the graph are all possible 4-digit combinations ("0000" to "9999"), and an edge exists between two combinations if one can be transformed into the other by a single wheel turn. This is a perfect fit for the [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Framework]].

### Problem Abstraction for BFS

1.  **Node/State:** A 4-digit string representing the lock's current combination.
2.  **`start_node`:** "0000".
3.  **`target_node`:** The input `target` string.
4.  **`get_neighbors(combo_str)`:** For a given `combo_str`, generate its 8 potential neighbors:
    - For each of the 4 wheels (indices 0, 1, 2, 3):
        - Turn wheel up by 1 (e.g., '0' -> '1', '9' -> '0').
        - Turn wheel down by 1 (e.g., '1' -> '0', '0' -> '9').
5.  **Invalid States (`deadends`):** Any combination listed in `deadends` cannot be visited. The `is_valid_node_func` in the BFS framework will check against this.

### Standard BFS Solution (Python)

```python
from collections import deque

class Solution:
    def _plus_one(self, s: str, j: int) -> str:
        chars = list(s)
        if chars[j] == '9':
            chars[j] = '0'
        else:
            # Corrected: Directly increment character code for Python
            chars[j] = chr(ord(chars[j]) + 1)
        return "".join(chars)

    def _minus_one(self, s: str, j: int) -> str:
        chars = list(s)
        if chars[j] == '0':
            chars[j] = '9'
        else:
            # Corrected: Directly decrement character code
            chars[j] = chr(ord(chars[j]) - 1)
        return "".join(chars)

    def _get_neighbors(self, combo_str: str) -> list[str]:
        neighbors = []
        for i in range(4): # For each of the 4 dials
            neighbors.append(self._plus_one(combo_str, i))
            neighbors.append(self._minus_one(combo_str, i))
        return neighbors

    def openLock(self, deadends: list[str], target: str) -> int:
        dead_set = set(deadends)

        start_node = "0000"
        if start_node in dead_set:
            return -1
        if target == start_node: # Already at target
            return 0

        q = deque([start_node])
        visited = {start_node}
        steps = 0

        while q:
            level_size = len(q)
            for _ in range(level_size):
                current_combo = q.popleft()

                if current_combo == target:
                    return steps

                for neighbor in self._get_neighbors(current_combo):
                    if neighbor not in visited and neighbor not in dead_set:
                        visited.add(neighbor)
                        q.append(neighbor)
            steps += 1

        return -1 # Target not reachable
```
*Self-correction in _plus_one/_minus_one: The Labuladong example used Java/C++ char arithmetic. Python needs `chr(ord(c)+1)`. The original LeetCode problem notes (from general BFS Framework) used `str(int(c)+1)` which is fine for digits but `chr/ord` is more general for char manipulations if letters were involved.*

**Complexity (Standard BFS):**
- State space size: $10^4$ possible combinations.
- Each state has 8 neighbors.
- Time: $O(N_{states} \cdot K_{neighbors} \cdot L_{strlen} + D_{deadends})$, where $N_{states}=10^4$, $K_{neighbors}=8$, $L_{strlen}=4$ (for string operations), $D_{deadends}$ is length of `deadends` list (for set conversion). Roughly $O(10^4 \cdot 4)$ because string ops for length 4 are small constants.
- Space: $O(10^4 \cdot 4)$ for `visited` set and `queue` storing strings.

### Bidirectional BFS Solution (Python)
Bidirectional BFS can be more efficient by exploring from both start and target simultaneously.

```python
from collections import deque

class Solution: # (Using same helper methods _plus_one, _minus_one, _get_neighbors as above)
    def _plus_one(self, s: str, j: int) -> str:
        chars = list(s)
        if chars[j] == '9': chars[j] = '0'
        else: chars[j] = chr(ord(chars[j]) + 1)
        return "".join(chars)

    def _minus_one(self, s: str, j: int) -> str:
        chars = list(s)
        if chars[j] == '0': chars[j] = '9'
        else: chars[j] = chr(ord(chars[j]) - 1)
        return "".join(chars)

    def _get_neighbors(self, combo_str: str) -> list[str]:
        neighbors = []
        for i in range(4):
            neighbors.append(self._plus_one(combo_str, i))
            neighbors.append(self._minus_one(combo_str, i))
        return neighbors

    def openLock(self, deadends: list[str], target: str) -> int: # Renamed to match LeetCode
        dead_set = set(deadends)
        start_node = "0000"

        if start_node in dead_set: return -1
        if target == start_node: return 0
        # If target itself is a deadend, it's unreachable directly by this logic.
        # The problem implies target isn't a deadend. If it could be, add:
        # if target in dead_set: return -1 

        q1 = {start_node} # Frontier from start
        q2 = {target}   # Frontier from target
        visited = {start_node, target}
        steps = 0

        while q1 and q2:
            # Optimization: always expand the smaller frontier
            if len(q1) > len(q2):
                q1, q2 = q2, q1 # Swap frontiers

            temp_q_next_level = set() # To store next level for the currently expanding frontier (q1)

            for current_combo in q1:
                # Neighbors of current_combo are at distance `steps + 1` from q1's origin
                for neighbor in self._get_neighbors(current_combo):
                    if neighbor in q2: # Intersection! Path found.
                                       # `q1` expanded `steps` times. `q2` also implicitly expanded some steps.
                                       # Path from `start` to `current_combo` is `steps`.
                                       # Path from `neighbor` to `target` is what `q2` represents.
                                       # If `q2` also took `steps` to reach `neighbor` from `target`, total is `2*steps + 1`.
                                       # If `q2` took `steps-1` (if q1 started expansion in this iteration), total is `2*steps`.
                                       # Labuladong's pattern for path length in BiBFS for this type of problem:
                                       # If 'steps' counts how many "layers" one side has expanded,
                                       # and an element from current_q1 (at layer 'steps') has a neighbor
                                       # that is in q2 (which is at layer 'steps' from ITS origin if q1 and q2
                                       # were swapped, or layer 'steps-1' if q2 was smaller and wasn't expanded this turn)
                                       # it's steps_from_q1_origin + steps_from_q2_origin.
                                       # A simpler way: The variable `steps` here will count full "paired" expansions.
                                       # If steps is incremented after each set of expansions (q1 and q2),
                                       # then when q1's neighbor hits q2's current frontier, path is 2*steps+1.
                                       # If steps is incremented once per main loop (expanding smaller set), path is steps.
                                       # The common BiBFS pattern like in Labuladong's framework:
                                       # `steps` is incremented. `q1` is expanded. Its neighbors are checked against `q2`.
                                       # If `neighbor` in `q2`, total path length is `steps`.
                        return steps + 1 # Path is (steps from q1's origin to current_combo) + 1 (to neighbor)
                                         # + (steps from q2's origin to neighbor, which is what q2 tracks implicitly)
                                         # The steps here are asymmetric, one side grows then other.
                                         # If steps = 0, q1 nodes are at dist 0. Neighbors at dist 1. If neighbor in q2 (dist 0 from target), path = 1.
                                         # This matches the example in the framework note.

                    if neighbor not in visited and neighbor not in dead_set:
                        visited.add(neighbor)
                        temp_q_next_level.add(neighbor)

            steps += 1
            q1 = temp_q_next_level # q1 now represents nodes for the next layer of expansion

        return -1 # Target not reachable
```

**Complexity (Bidirectional BFS):**
- Roughly $O(2 \cdot K^{D/2})$ where $K=8$ (branching factor), $D$ is shortest path length.
- Max states $10^4$. So, $O(10^4 \cdot 4)$ for string operations in worst case.
- Space: $O(10^4 \cdot 4)$ for visited sets and frontiers.

## Visualization
The problem can be visualized as finding a path in a state graph where nodes are combinations and edges are single-digit turns.
- **Standard BFS:** `![](/algo/images/bfs/1.jpeg)` (Source: Labuladong - shows expanding search radius)
- **Bidirectional BFS:** `![](/algo/images/bfs/2.jpeg)` (Source: Labuladong - shows two search radii meeting)

## 总结 (Summary)
- LC752 "Open the Lock" is a classic shortest path problem solvable with BFS.
- The state space is all $10^4$ lock combinations.
- Transitions involve turning one of the four wheels up or down.
- `deadends` act as forbidden nodes.
- Standard BFS finds the minimum number of turns (steps).
- Bidirectional BFS can offer a performance improvement by searching from both "0000" and the `target` simultaneously.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Framework]]
Related Problems: [[Interview/Practice/LeetCode/LC773 - Sliding Puzzle|LC773 - Sliding Puzzle]]
