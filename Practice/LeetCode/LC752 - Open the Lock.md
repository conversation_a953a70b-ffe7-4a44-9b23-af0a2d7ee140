---
tags: [problem/leetcode, lc/medium, topic/graph, topic/bfs, pattern/bfs_shortest_path, pattern/bidirectional_bfs]
aliases: [LC752, LeetCode 752, Open the Lock, 打开转盘锁]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 752. Open the Lock
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/BFS 算法解题套路框架.md]].

# LeetCode 752: Open the Lock

## Problem Statement

You have a lock with four circular wheels, each with digits '0'-'9'. The lock starts at "0000". You are given a list of `deadends` (strings representing locked states) and a `target` string. Each move consists of turning one wheel one digit. Find the minimum number of turns required to reach the `target` from "0000" without passing through any `deadends`. If it's impossible, return -1.

**Official Link:** [LeetCode 752. Open the Lock](https://leetcode.com/problems/open-the-lock/)

**Example 1:**
Input: `deadends = ["0201","0101","0102","1212","2002"]`, `target = "0202"`
Output: `6`
Explanation: A possible sequence: "0000" -> "1000" -> "1100" -> "1200" -> "1201" -> "1202" -> "0202".

## Solution Approach: BFS

This problem asks for the minimum number of turns, which is a classic shortest path problem on an unweighted graph. The states of the graph are all possible 4-digit combinations ("0000" to "9999"), and an edge exists between two combinations if one can be transformed into the other by a single wheel turn. This is a perfect fit for the [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Framework]].

### Problem Abstraction for BFS

1.  **Node/State:** A 4-digit string representing the lock's current combination.
2.  **`start_node`:** "0000".
3.  **`target_node`:** The input `target` string.
4.  **`get_neighbors(combo_str)`:** For a given `combo_str`, generate its 8 potential neighbors:
    - For each of the 4 wheels (indices 0, 1, 2, 3):
        - Turn wheel up by 1 (e.g., '0' -> '1', '9' -> '0').
        - Turn wheel down by 1 (e.g., '1' -> '0', '0' -> '9').
5.  **Invalid States (`deadends`):** Any combination listed in `deadends` cannot be visited.

### Standard BFS Solution (Python)

```python
from collections import deque

class Solution:
    def _plus_one(self, s: str, j: int) -> str:
        chars = list(s)
        if chars[j] == '9':
            chars[j] = '0'
        else:
            chars[j] = str(int(chars[j]) + 1)
        return "".join(chars)

    def _minus_one(self, s: str, j: int) -> str:
        chars = list(s)
        if chars[j] == '0':
            chars[j] = '9'
        else:
            chars[j] = str(int(chars[j]) - 1)
        return "".join(chars)

    def _get_neighbors(self, combo_str: str) -> list[str]:
        neighbors = []
        for i in range(4): # For each of the 4 dials
            neighbors.append(self._plus_one(combo_str, i))
            neighbors.append(self._minus_one(combo_str, i))
        return neighbors

    def openLock(self, deadends: list[str], target: str) -> int:
        dead_set = set(deadends)
        
        if "0000" in dead_set:
            return -1
        if target == "0000": # Already at target
            return 0

        q = deque(["0000"])
        visited = {"0000"}
        steps = 0

        while q:
            level_size = len(q)
            for _ in range(level_size):
                current_combo = q.popleft()
                
                if current_combo == target:
                    return steps

                for neighbor in self._get_neighbors(current_combo):
                    if neighbor not in visited and neighbor not in dead_set:
                        visited.add(neighbor)
                        q.append(neighbor)
            steps += 1
        
        return -1 # Target not reachable
```

**Complexity (Standard BFS):**
- State space size: $10^4$ possible combinations.
- Each state has 8 neighbors.
- Time: $O(N \cdot k + D)$, where $N=10^4$ is number of states, $k=8$ is neighbors, $D$ is length of `deadends` list (for set conversion). Roughly $O(10^4)$.
- Space: $O(10^4)$ for `visited` set and `queue`.

### Bidirectional BFS Solution (Python)

Bidirectional BFS can be more efficient by exploring from both start and target simultaneously.

```python
from collections import deque

class Solution: # (Using same helper methods _plus_one, _minus_one, _get_neighbors as above)
    def _plus_one(self, s: str, j: int) -> str:
        chars = list(s)
        if chars[j] == '9': chars[j] = '0'
        else: chars[j] = str(int(chars[j]) + 1)
        return "".join(chars)

    def _minus_one(self, s: str, j: int) -> str:
        chars = list(s)
        if chars[j] == '0': chars[j] = '9'
        else: chars[j] = str(int(chars[j]) - 1)
        return "".join(chars)

    def _get_neighbors(self, combo_str: str) -> list[str]:
        neighbors = []
        for i in range(4):
            neighbors.append(self._plus_one(combo_str, i))
            neighbors.append(self._minus_one(combo_str, i))
        return neighbors

    def openLock(self, deadends: list[str], target: str) -> int:
        dead_set = set(deadends)
        if "0000" in dead_set: return -1
        if target == "0000": return 0
        if target in dead_set: return -1 # Added check: if target itself is a deadend

        # Use sets for frontiers for O(1) intersection check
        q1 = {"0000"}
        q2 = {target}
        visited = {"0000", target} # Add both start and target to visited initially
        steps = 0

        while q1 and q2:
            # Optimization: always expand the smaller frontier
            if len(q1) > len(q2):
                q1, q2 = q2, q1 # Swap frontiers

            next_level_nodes = set()
            for current_combo in q1:
                if current_combo == target : # Should be caught by intersection check, but safe
                    return steps

                for neighbor in self._get_neighbors(current_combo):
                    if neighbor in q2: # Intersection found!
                        return steps + 1 # Current steps for q1 + 1 step for neighbor to reach q2's frontier
                    
                    if neighbor not in visited and neighbor not in dead_set:
                        visited.add(neighbor)
                        next_level_nodes.add(neighbor)
            
            q1 = next_level_nodes
            steps += 1
            # Note on steps: If intersection found, q1 expanded 'steps' times,
            # and q2 (other frontier) expanded 'steps' or 'steps-1' times.
            # The path connects after one more step from q1's current nodes.
            # Labuladong's implementation increments step at start of loop AFTER first check.
            # This version increments step after expanding one frontier.
            # The critical point is how `steps` relates to path length upon intersection.
            # If q1 expands for 's1' steps and q2 for 's2' steps, total is s1+s2.
            # Here, steps for q1. `neighbor in q2` means neighbor is on frontier of q2.
            # The total steps is current `steps` (for q1) + 1 (for neighbor to bridge).

        return -1
```
**Correction on step count for BiBFS (Labuladong's style):**
Labuladong's version correctly increments `step` at the start of each expansion cycle for `q1`.
If `q1` has taken `s` steps, its current nodes are at distance `s`. If a neighbor (distance `s+1`) is in `q2`, it means `q2` has *also* reached that neighbor. The number of steps `q2` took to reach that common neighbor isn't explicitly tracked as `s2` in the same loop variable. Instead, `visited` ensures that `q2` also doesn't explore beyond `q1`. The sum of steps is handled by the single `steps` variable.

Revised BiBFS `steps` logic to align with common pattern:
```python
# ... (inside openLock method, after initial checks and q1, q2, visited setup)
        steps = 0
        while q1 and q2:
            # Optimization: always expand the smaller frontier
            if len(q1) > len(q2):
                q1, q2 = q2, q1 

            temp_q = set() # To store next level for the currently expanding frontier (q1)
            for combo in q1:
                # Check for intersection with the *other* frontier
                # This check might be redundant if done after neighbor generation
                # if combo in q2: return steps 

                for neighbor in self._get_neighbors(combo):
                    if neighbor in q2: # Intersection!
                        return steps + 1
                    if neighbor not in visited and neighbor not in dead_set:
                        visited.add(neighbor)
                        temp_q.add(neighbor)
            
            steps += 1
            q1 = temp_q # q1 now represents nodes at 'steps' distance from its original start

        return -1
```
**Complexity (Bidirectional BFS):**
- Roughly $O(2 \cdot b^{d/2})$ where $b=8$ (branching factor), $d$ is shortest path.
- Still $O(10^4)$ in worst case, but practically faster for many cases. Space is similar.

## Visualization
Imagine the state space as a large graph.
- **Standard BFS:** Ripples expanding from "0000".
    ```tikz
    \begin{tikzpicture}[tn/.style={circle, draw, font=\sffamily\tiny, minimum size=4mm, inner sep=0.5pt}]
        \node[tn, fill=blue!20] (s) at (0,0) {0000};
        \node[tn, fill=red!20] (t) at (5,0) {Target};
        \draw[dashed, blue!50] (s) circle (0.5cm);
        \draw[dashed, blue!50] (s) circle (1cm);
        \draw[dashed, blue!50] (s) circle (1.5cm);
        \draw[dashed, blue!50] (s) circle (2cm); \node at (2.3,0) {...};
        \node at (2.5, -1) {Standard BFS expands from start};
    \end{tikzpicture}
    ```
- **Bidirectional BFS:** Ripples expanding from "0000" and `target` simultaneously. They meet earlier.
    ```tikz
    \begin{tikzpicture}[tn/.style={circle, draw, font=\sffamily\tiny, minimum size=4mm, inner sep=0.5pt}]
        \node[tn, fill=blue!20] (s) at (0,0) {0000};
        \node[tn, fill=red!20] (t) at (5,0) {Target};
        \draw[dashed, blue!50] (s) circle (0.5cm);
        \draw[dashed, blue!50] (s) circle (1cm);
        \draw[dashed, red!50] (t) circle (0.5cm);
        \draw[dashed, red!50] (t) circle (1cm);
        \draw[dashed, red!50] (t) circle (1.5cm);
        \node[tn,fill=green!30] (meet) at (2.5,0) {Meet!};
        \draw[<->, thick, green!70!black] (1.2,0) -- (meet);
        \draw[<->, thick, green!70!black] (3.8,0) -- (meet);
        \node at (2.5, -1) {Bidirectional BFS expands from both ends};
    \end{tikzpicture}
    ```

## 总结 (Summary)
- "Open the Lock" is a shortest path problem on an implicit graph of lock combinations.
- Standard BFS is a natural fit:
    - States are 4-digit strings.
    - Transitions involve turning one wheel up/down.
    - `visited` set handles cycles, `deadends` set prunes invalid states.
- Bidirectional BFS can optimize by reducing the search radius, effective when the target state is known.
- The core BFS framework remains the same; the main work is in defining states and transitions.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Framework]], [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Graph BFS]]
Related Problems: [[Interview/Practice/LeetCode/LC773 - Sliding Puzzle|LC773 - Sliding Puzzle]]
