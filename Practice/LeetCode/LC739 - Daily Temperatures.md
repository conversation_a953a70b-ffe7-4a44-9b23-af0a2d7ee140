---
tags: [problem/leetcode, lc/medium, topic/array, topic/stack, pattern/monotonic_stack, course/labuladong]
aliases: [LC739, Daily Temperatures]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典队列_栈算法/单调栈算法模板解决三道例题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 739. Daily Temperatures
> Solution approach adapted from [[Interview/Concept/Data Structures/Stack/Applications/01 - Monotonic Stack Pattern|Monotonic Stack Pattern]].

# LeetCode 739: Daily Temperatures

## Problem Statement
Given an array of integers `temperatures` representing the daily temperatures, return an array `answer` such that `answer[i]` is the number of days you have to wait after the `ith` day to get a warmer temperature. If there is no future day for which this is possible, keep `answer[i] == 0` instead.

**Official Link:** [LeetCode 739. Daily Temperatures](https://leetcode.com/problems/daily-temperatures/)

## Solution Approach: Monotonic Stack (Storing Indices)

This problem is a variation of "Next Greater Element". Instead of the value of the next greater element, we need the *distance (number of days)* to it.
The [[Interview/Concept/Data Structures/Stack/Applications/01 - Monotonic Stack Pattern|Monotonic Stack template]] applies, but the stack should store *indices* of the temperatures, not the temperatures themselves.

1.  Initialize `res` array with zeros and an empty `stack`.
2.  Iterate through `temperatures` from right to left (index `i` from `n-1` down to `0`).
3.  For each `temperatures[i]`:
    -   While `stack` is not empty and `temperatures[i] >= temperatures[stack[-1]]` (current temp is warmer than or equal to stack top's temp):
        - Pop from `stack`. These popped indices have `temperatures[i]` as their (or an even earlier) next warmer day, but we are processing from right to left, so `temperatures[i]` blocks them for elements further left.
    -   If `stack` is empty, no warmer day found to the right: `res[i]` remains `0`.
    -   Else, the next warmer day is at index `stack[-1]`. The wait is `stack[-1] - i`. So, `res[i] = stack[-1] - i`.
    -   Push current index `i` onto the stack. The stack will maintain indices of days with decreasing temperatures from bottom to top.

### Python Solution
```python
class Solution:
    def dailyTemperatures(self, temperatures: list[int]) -> list[int]:
        n = len(temperatures)
        res = [0] * n  # Initialize results with 0 (default for no warmer day)
        stack = []     # Monotonically decreasing stack (stores indices)

        # Iterate from right to left
        for i in range(n - 1, -1, -1):
            # While stack is not empty and current temperature is greater than or
            # equal to the temperature at the index stored at stack top
            while stack and temperatures[i] >= temperatures[stack[-1]]:
                stack.pop()

            # If stack is not empty, the top is the index of the next warmer day
            if stack:
                res[i] = stack[-1] - i # Difference in indices is days to wait
            # Else, res[i] remains 0 (no warmer day)

            # Push current index onto stack
            stack.append(i)

        return res
```

## Complexity Analysis
- **Time Complexity:** $O(N)$, where $N$ is the length of `temperatures`. Each index is pushed and popped from the stack at most once.
- **Space Complexity:** $O(N)$ in the worst case for the `stack` (e.g., if temperatures are strictly decreasing).

## 总结 (Summary)
- LC739 is a "next greater element" type problem where the goal is to find the distance to the next greater element.
- A monotonic stack storing *indices* is used.
- Iterating from right to left allows efficiently finding the next warmer day for each day.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
