---
tags: [problem/leetcode, lc/medium, topic/string, topic/dynamic_programming, pattern/two_pointers, pattern/expand_around_center]
aliases: [LC5, LeetCode 5. Longest Palindromic Substring, 最长回文子串]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 5. Longest Palindromic Substring
> Two-pointer solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道数组题目.md]].

# LeetCode 5: Longest Palindromic Substring

## Problem Statement

Given a string `s`, return *the longest palindromic substring* in `s`.

**Official Link:** [LeetCode 5. Longest Palindromic Substring](https://leetcode.com/problems/longest-palindromic-substring/)

## Solution Approach: Expand Around Center (Two Pointers)

A palindrome reads the same forwards and backward. A key observation is that a palindrome is symmetric around its center. The center can be a single character (for odd-length palindromes like "aba") or a pair of characters (for even-length palindromes like "abba").

The "expand around center" strategy iterates through all possible centers of a palindrome and expands outwards to find the longest palindrome centered at that position.

1.  Iterate through each character index `i` from `0` to `len(s)-1`.
2.  For each `i`, consider two cases for the center:
    a.  **Odd length palindrome:** Center is `s[i]`. Expand from `(i, i)`.
    b.  **Even length palindrome:** Center is between `s[i]` and `s[i+1]`. Expand from `(i, i+1)`.
3.  A helper function `palindrome(s, l, r)` takes the string `s` and two pointers `l` and `r` (initially defining the center). It expands `l` to the left and `r` to the right as long as `l >= 0`, `r < len(s)`, and `s[l] == s[r]`. It returns the palindromic substring found.
4.  Keep track of the longest palindrome found so far.

### Python Solution (Labuladong's version)
```python
class Solution:
    def longestPalindrome(self, s: str) -> str:
        res = ""
        for i in range(len(s)):
            # Odd length palindrome (center is s[i])
            s1 = self._palindrome(s, i, i)
            # Even length palindrome (center is between s[i] and s[i+1])
            s2 = self._palindrome(s, i, i + 1)

            # Update result if s1 or s2 is longer
            if len(s1) > len(res):
                res = s1
            if len(s2) > len(res):
                res = s2
        return res

    def _palindrome(self, s: str, l: int, r: int) -> str:
        # Expand from center (l, r) while characters match and bounds are valid
        while l >= 0 and r < len(s) and s[l] == s[r]:
            l -= 1
            r += 1
        # The palindrome is s[l+1 ... r-1]
        # s.substring(l + 1, r) in Java notation
        return s[l+1 : r]
```
Labuladong's visualizer `div_longest-palindromic-substring` demonstrates the `l, r` pointers expanding.

## Complexity Analysis
- **Time Complexity:** $O(N^2)$, where $N$ is the length of `s`.
    - There are $2N-1$ possible centers (N single character centers, N-1 double character centers).
    - For each center, the `_palindrome` expansion can take up to $O(N)$ time in the worst case (e.g., if the entire string is a palindrome).
- **Space Complexity:** $O(1)$ if we don't count the space for the result string. The substring slicing might take $O(N)$ space in some Python implementations if not optimized, but conceptually, it's often considered $O(1)$ auxiliary space if we only store start/end indices of the longest palindrome. If slicing creates new strings repeatedly for comparison, space could be $O(N)$. Labuladong's Python implementation returning slices implies $O(N)$ for result string, but the computation itself is $O(1)$ auxiliary if we just track indices.

## Visualization

`s = "babad"`

1.  `i=0`:
    - `_palindrome(s,0,0)` ("a" center): `l=-1, r=1`. Returns `s[0:1]="b"`. `res="b"`.
    - `_palindrome(s,0,1)` ("ba" center): `s[0]!=s[1]`. `l=0, r=0`. Returns `s[1:0]=""`.
2.  `i=1`:
    - `_palindrome(s,1,1)` ("b" center): `l=0, r=2` -> `s[0](b)==s[2](b)`. `l=-1, r=3`. Returns `s[0:3]="bab"`. `res="bab"`.
    - `_palindrome(s,1,2)` ("ab" center): `s[1]!=s[2]`. `l=1, r=1`. Returns `s[2:1]=""`.
3.  `i=2`:
    - `_palindrome(s,2,2)` ("a" center): `s[1](b)==s[3](a)` is false. Wait. Original string `babad`.
      `s[1](a)==s[3](d)` is false. (Expanding from `s[2]='b'`).
      `s = "babad"`
      `_palindrome(s, 2, 2)` (center is `s[2]='b'`)
        - `l=2, r=2`. `s[2]==s[2]`. `l=1, r=3`.
        - `s[1](a)==s[3](a)`. `l=0, r=4`.
        - `s[0](b)==s[4](d)`. False.
        - Loop ends. `l=0, r=4`. Returns `s[1:4]="aba"`. `res` (was "bab") remains "bab" (same length).
    - `_palindrome(s,2,3)` ("ba" center): `s[2](b)!=s[3](a)`. Returns `s[3:3]=""`.
... and so on.

If `s = "cbbd"`
1. `i=1`:
   - `_palindrome(s,1,1)` (center `s[1]='b'`). Returns "b". `res="b"`.
   - `_palindrome(s,1,2)` (center `s[1]='b', s[2]='b'`).
     - `l=1,r=2`. `s[1]==s[2]`. `l=0, r=3`.
     - `s[0](c)==s[3](d)`. False.
     - Loop ends. `l=0, r=3`. Returns `s[1:3]="bb"`. `res="bb"`.

## Other Approaches
- **Dynamic Programming:** An $O(N^2)$ time and $O(N^2)$ space solution exists. `dp[i][j]` is true if `s[i...j]` is a palindrome.
  `dp[i][j] = (s[i] == s[j]) and dp[i+1][j-1]`. This can be optimized to $O(N)$ space.
- **Manacher's Algorithm:** An $O(N)$ time complexity algorithm, but it's more complex to implement.

The expand-around-center approach is often preferred in interviews for its relative simplicity and good enough $O(N^2)$ performance for typical constraints.

## 总结 (Summary)
- LC5 finds the longest palindromic substring.
- The "expand around center" two-pointer strategy is an intuitive and common way to solve this in $O(N^2)$ time and $O(1)$ auxiliary space.
- It considers every possible center (single character or pair of characters) and expands outwards.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]] (Expand Around Center)
