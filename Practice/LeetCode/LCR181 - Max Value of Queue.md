---
tags: [problem/leetcode, lc/medium, topic/data_structure_design, pattern/monotonic_queue_like, course/labuladong]
aliases: [LCR181, Max Value of Queue, 队列的最大值, 面试题59 - II]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典队列_栈算法/单调队列结构解决滑动窗口问题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode LCR 181. 队列的最大值 (剑指 Offer 59 - II. 队列的最大值)
> This problem is similar to designing a queue that can return its current maximum element in O(1).
> Related concepts from [[Interview/Concept/Data Structures/Queue/Applications/01 - Monotonic Queue Pattern|Monotonic Queue Pattern]].

# LeetCode LCR 181: 队列的最大值 (Max Value of Queue)

## Problem Statement
Please define a queue and implement the function `max_value` to get the maximum value in the queue. The time complexity of `max_value`, `push_back`, and `pop_front` should all be $O(1)$ (amortized for push/pop if using a typical monotonic queue setup with deque).

If the queue is empty, `pop_front` and `max_value` should return -1.

**Official Link:** (e.g., [LeetCode LCR 181](https://leetcode.cn/problems/dui-lie-de-zui-da-zhi-lcof/))

## Solution Approach: Main Queue + Monotonic Helper Deque

This problem requires implementing a standard queue that also supports finding the maximum element efficiently. We can use:
1.  A standard queue (e.g., `collections.deque`) to store the actual elements in FIFO order. Let's call this `data_q`.
2.  A helper deque (`max_q`) that stores elements from `data_q` in decreasing order (a monotonic decreasing queue). This `max_q` will help us find the maximum element in $O(1)$.

**Operations:**
-   **`push_back(value)`:**
    -   Enqueue `value` into `data_q`.
    -   While `max_q` is not empty and `value > max_q.back()`: `max_q.pop_back()`. (Maintain decreasing order in `max_q`).
    -   Enqueue `value` into `max_q`.
-   **`pop_front()`:**
    -   If `data_q` is empty, return -1.
    -   Dequeue `val_popped` from `data_q`.
    -   If `val_popped == max_q.front()`: `max_q.pop_front()`. (If the popped element was the current max, remove it from `max_q` too).
    -   Return `val_popped`.
-   **`max_value()`:**
    -   If `max_q` (or `data_q`) is empty, return -1.
    -   Return `max_q.front()`.

### Python Solution
```python
import collections

class MaxQueue:
    def __init__(self):
        self.data_q = collections.deque()
        self.max_q = collections.deque() # Monotonically decreasing helper queue

    def max_value(self) -> int:
        if not self.max_q: # Or if not self.data_q
            return -1
        return self.max_q[0] # Front of max_q is the current max

    def push_back(self, value: int) -> None:
        self.data_q.append(value)

        # Maintain decreasing order in max_q
        while self.max_q and value > self.max_q[-1]:
            self.max_q.pop()
        self.max_q.append(value)

    def pop_front(self) -> int:
        if not self.data_q:
            return -1

        val_popped = self.data_q.popleft()

        # If the popped element was the current maximum, remove it from max_q
        if self.max_q and val_popped == self.max_q[0]:
            self.max_q.popleft()

        return val_popped

# Your MaxQueue object will be instantiated and called as such:
# obj = MaxQueue()
# param_1 = obj.max_value()
# obj.push_back(value)
# param_3 = obj.pop_front()
```

## Complexity Analysis
- **`max_value`**: $O(1)$.
- **`push_back`**: Amortized $O(1)$. The `while` loop for `max_q` might run multiple times, but each element is added to and removed from `max_q` at most once.
- **`pop_front`**: $O(1)$.
- **Space Complexity:** $O(N)$ in the worst case for storing elements in both `data_q` and `max_q` (e.g., if elements are pushed in decreasing order, `max_q` will store all of them).

## 总结 (Summary)
- To implement a queue with $O(1)$ `max_value` retrieval, use a primary data queue and a helper monotonic (decreasing) deque.
- The helper deque `max_q` always has the current maximum element of the `data_q` at its front.
- `push_back` maintains the monotonic property of `max_q`.
- `pop_front` updates `max_q` if the popped element was the maximum.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
