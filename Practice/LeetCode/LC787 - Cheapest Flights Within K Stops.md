---
tags: [problem/leetcode, lc/medium, topic/graph, topic/dynamic_programming, pattern/shortest_path_dp, course/labuladong, lc/lc787]
aliases: [LC787, Cheapest Flights Within K Stops]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/旅游省钱大法：加权最短路径.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 787. Cheapest Flights Within K Stops
> The solution is based on the [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Game DP/Travel Min Cost K Stops|Cheapest Flights K Stops DP Pattern]].

# LeetCode 787: Cheapest Flights Within K Stops

## Problem Statement
There are `n` cities connected by some number of flights. You are given an array `flights` where `flights[i] = [fromi, toi, pricei]` indicates that there is a flight from city `fromi` to city `toi` with cost `pricei`.
You are also given three integers `src`, `dst`, and `k`, return *the cheapest price from `src` to `dst` with at most `k` stops*. If there is no such route, return `-1`.

**Official Link:** [LeetCode 787. Cheapest Flights Within K Stops](https://leetcode.com/problems/cheapest-flights-within-k-stops/)

## Solution Approach
This problem is solved using a dynamic programming approach similar to Bellman-Ford, as detailed in [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Game DP/Travel Min Cost K Stops|Cheapest Flights K Stops DP Pattern]].

### Python Solution
```python
class Solution:
    def findCheapestPrice(self, n: int, flights: list[list[int]], src: int, dst: int, k_stops: int) -> int:
        # dp[i][j] = min cost to reach city j using exactly i flights.
        # We want min cost to reach dst using at most k_stops + 1 flights.

        # Initialize costs to infinity, cost to reach src with 0 flights is 0.
        # dp_prev[j] = min cost to reach city j using prev_num_flights flights
        # dp_curr[j] = min cost to reach city j using current_num_flights flights

        # Using k_stops + 2 because we can take up to k_stops + 1 flights.
        # dp[flights_count][destination_city]
        # Let dp[v] be the min cost to reach city v with current number of flights.
        # This will be updated iteratively.

        # costs[j] will store the minimum cost to reach city j using up to
        # the current number of flights considered in the outer loop.
        costs = [float('inf')] * n
        costs[src] = 0

        # Iterate for the number of flights allowed.
        # num_flights = 1, 2, ..., k_stops + 1
        for num_flights_taken in range(1, k_stops + 2):
            new_costs = list(costs) # To store costs calculated in this iteration

            # Relax all edges
            for u, v, price in flights:
                if costs[u] != float('inf'): # If city u is reachable with previous number of flights
                    # Update cost to reach v using one more flight (from u to v)
                    new_costs[v] = min(new_costs[v], costs[u] + price)

            costs = new_costs # Update costs for the next iteration

        return costs[dst] if costs[dst] != float('inf') else -1
```

## Complexity Analysis
- **Time Complexity:** $O((K+1) \cdot E)$, where $K$ is `k_stops` and $E$ is `len(flights)`.
- **Space Complexity:** $O(N)$ for the `costs` array.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
