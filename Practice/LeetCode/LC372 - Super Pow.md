---
tags: [problem/leetcode, lc/medium, topic/math, topic/number_theory, algorithm/modular_exponentiation, course/labuladong]
aliases: [LC372, Super Pow]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 372. Super Pow
> Solution uses [[Interview/Concept/Algorithms/Mathematical Techniques/02 - Modular Exponentiation|Modular Exponentiation]].

# LeetCode 372: Super Pow

## Problem Statement
Your task is to calculate $a^b \pmod{1337}$ where `a` is an integer and `b` is an extremely large positive integer given in the form of an array.

**Official Link:** [LeetCode 372. Super Pow](https://leetcode.com/problems/super-pow/)

## Solution Approach: Modular Exponentiation with Iterative Exponent Processing

The core problem is computing $a^b \pmod m$ where $m=1337$.
The exponent $b$ is given as an array of digits, e.g., if `b = [1, 2, 3]`, it represents $123$.
We use the property: $x^{yz} = (x^y)^z$.
More specifically, if $b = d_k d_{k-1} ... d_1 d_0$ (digits), then $b = (...((d_k \cdot 10 + d_{k-1}) \cdot 10 + d_{k-2})...)\cdot 10 + d_0$.
So, $a^b = a^{(...((d_k \cdot 10 + d_{k-1}) \cdot 10 + d_{k-2})...)\cdot 10 + d_0}$
$= ((( (a^{d_k})^{10} \cdot a^{d_{k-1}} )^{10} \cdot a^{d_{k-2}} ) ... )^{10} \cdot a^{d_0}$.
All operations are done modulo 1337.

**Algorithm:**
1.  Define a helper function `mypow(base, exp, mod)` that computes `(base^exp) % mod` using [[Interview/Concept/Algorithms/Mathematical Techniques/02 - Modular Exponentiation|modular exponentiation by squaring]] ($O(\log \text{exp})$ time).
2.  Iterate through the digits of `b` from left to right (most significant to least significant).
    - Maintain a running result `res`. Initialize `res = 1`.
    - For each digit `d` in `b`:
        - `res = mypow(res, 10, 1337)` (Effectively `res = res^10`)
        - `res = (res * mypow(a, d, 1337)) % 1337` (Effectively `res = res * (a^d)`)
3.  Return `res`.

The modulus $1337 = 7 \times 191$. It's not prime. Euler's totient theorem could be used if `a` and 1337 are coprime, to reduce `b` modulo $\phi(1337)$.
$\phi(1337) = \phi(7 \times 191) = \phi(7) \times \phi(191) = (7-1)(191-1) = 6 \times 190 = 1140$.
So, $a^b \equiv a^{b \pmod{1140}} \pmod{1337}$ if $\gcd(a, 1337)=1$.
This can simplify the exponent `b` if `b` is processed as a large number modulo 1140.
However, Labuladong's solution uses the direct iterative approach, which is more general and avoids coprimality issues.

### Python Solution (Labuladong's iterative style)
```python
class Solution:
    MOD = 1337

    def _mypow(self, base: int, exp: int) -> int:
        ''' Computes (base^exp) % MOD using exponentiation by squaring '''
        res = 1
        base %= self.MOD # Ensure base is within mod range

        while exp > 0:
            if exp % 2 == 1: # If exp is odd
                res = (res * base) % self.MOD
            base = (base * base) % self.MOD # Square the base
            exp //= 2 # Halve the exponent
        return res

    def superPow(self, a: int, b: list[int]) -> int:
        if a == 0: return 0
        if a == 1: return 1 # 1 to any power is 1
        if not b: return 1 # a^0 = 1

        # Iteratively process digits of b
        # Example: b = [1,5,6,4] -> number 1564
        # a^1564 = a^(156*10 + 4) = (a^156)^10 * a^4
        #          = ( (a^15)^10 * a^6 )^10 * a^4
        #          = ( ( (a^1)^10 * a^5 )^10 * a^6 )^10 * a^4

        current_power_val = 1 # Represents a^0
        for digit in b:
            # For each digit, current_power_val becomes (current_power_val)^10 * (a^digit)
            term1 = self._mypow(current_power_val, 10) # (current_power_val)^10
            term2 = self._mypow(a, digit)              # a^digit
            current_power_val = (term1 * term2) % self.MOD

        return current_power_val
```

## Complexity Analysis
- `_mypow(base, exp)`: $O(\log \text{exp})$. Max `exp` here is 10 (for `res^10`) or max digit `d` (which is 9). So, this is effectively constant time.
- `superPow`: Iterates through `len(b)` digits. Each iteration calls `_mypow` twice.
- **Time Complexity:** $O(L \cdot \log K)$, where $L$ is length of `b` and $K$ is max value in `_mypow` (10 or 9). So, roughly $O(L)$.
- **Space Complexity:** $O(1)$ for iterative `_mypow`. If recursive `_mypow` used, $O(\log K)$ for stack.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
