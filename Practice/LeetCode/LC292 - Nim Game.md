---
tags: [problem/leetcode, lc/easy, topic/game_theory, topic/brain_teaser, course/labuladong]
aliases: [LC292, <PERSON><PERSON>]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 292. Nim Game
> Discussed in [[Interview/Concept/Algorithms/Mathematical Techniques/04 - Brain Teaser Algorithm Problems|Brain Teaser Algorithm Problems]].

# LeetCode 292: Nim Game

## Problem Statement
You are playing the following Nim Game with your friend:
- Initially, there is a pile of stones on the table.
- You and your friend will alternate taking turns, and **you go first**.
- On each turn, the person whose turn it is will remove 1 to 3 stones from the pile.
- The one who removes the last stone is the winner.
Given `n`, the number of stones in the pile, return `true` if you can win the game assuming both players play optimally, and `false` otherwise.

**Official Link:** [LeetCode 292. Nim Game](https://leetcode.com/problems/nim-game/)

## Solution Approach: Pattern Recognition
The key insight is that if the number of stones `n` is a multiple of 4, you will lose (assuming optimal play from your opponent). Otherwise, you can always win.
- If `n % 4 == 0`: Whatever stones you take (1, 2, or 3), you leave `n' = 4k + r` where `r` is 3, 2, or 1. Your opponent can then take `r` stones, leaving you with `4k` stones again. This continues until you face 4 stones, and you lose.
- If `n % 4 != 0`: You can take `n % 4` stones (1, 2, or 3 stones), leaving a multiple of 4 for your opponent. Now your opponent is in the losing position described above.

### Python Solution
```python
class Solution:
    def canWinNim(self, n: int) -> bool:
        # You lose if n is a multiple of 4.
        # You win if n is not a multiple of 4.
        return n % 4 != 0
```

## Complexity Analysis
- **Time Complexity:** $O(1)$.
- **Space Complexity:** $O(1)$.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
