---
tags: [problem/leetcode, lc/easy, topic/string, pattern/string_matching, algorithm/rabin_karp, algorithm/kmp, course/labuladong]
aliases: [LC28, LeetCode 28, Implement strStr(), Find First Occurrence]
summary: |
  Implement strStr() to find the first occurrence of a needle string in a haystack string.
  Commonly solved with built-in functions, KMP, or Rabin-Karp.
created: 2025-05-26T18:00:00.000-07:00
modified: 2025-05-26T18:00:00.000-07:00
---

> [!NOTE] Source Annotation
> Problem: LeetCode 28. Find the Index of the First Occurrence in a String
> Mentioned in [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷数组算法/滑动窗口延伸：Rabin Karp 字符匹配算法.md|滑动窗口延伸：Rabin Karp 字符匹配算法]] as an application.

# LeetCode 28: Find the Index of the First Occurrence in a String

## Problem Statement

Given two strings `haystack` and `needle`, return the index of the first occurrence of `needle` in `haystack`, or `-1` if `needle` is not part of `haystack`.

**Official Link:** [LeetCode 28. Find the Index of the First Occurrence in a String](https://leetcode.com/problems/find-the-index-of-the-first-occurrence-in-a-string/)

**Example 1:**
Input: `haystack = "sadbutsad"`, `needle = "sad"`
Output: `0`
Explanation: `"sad"` occurs at index 0 and 6. The first occurrence is at index 0, so we return 0.

**Example 2:**
Input: `haystack = "leetcode"`, `needle = "leeto"`
Output: `-1`
Explanation: `"leeto"` did not occur in `"leetcode"`, so we return -1.

## Solution Approaches

### 1. Built-in String Method (Pythonic)
Most languages provide a built-in method for this.
```python
class Solution:
    def strStr_builtin(self, haystack: str, needle: str) -> int:
        if not needle: # An empty needle is usually considered to be at index 0
            return 0
        return haystack.find(needle) # Returns -1 if not found
```
This is often the most practical solution in a timed contest if allowed, but interviewers might ask for a manual implementation.

### 2. Brute-Force / Naive Sliding Window
Iterate through `haystack`, and for each starting position `i`, check if the substring `haystack[i : i+len(needle)]` is equal to `needle`.
```python
class Solution:
    def strStr_brute_force(self, haystack: str, needle: str) -> int:
        N, M = len(haystack), len(needle)
        if M == 0: return 0
        if N < M: return -1

        for i in range(N - M + 1): # Possible start indices in haystack
            # Check if haystack[i : i+M] == needle
            match = True
            for j in range(M):
                if haystack[i+j] != needle[j]:
                    match = False
                    break
            if match:
                return i
        return -1
```
- **Time Complexity:** $O((N-M+1) \cdot M) = O(NM)$ in the worst case.
- **Space Complexity:** $O(1)$ (or $O(M)$ if slicing creates copies, but direct char comparison is $O(1)$).

### 3. Rabin-Karp Algorithm
This uses hashing and a rolling hash. A detailed explanation and conceptual code for Rabin-Karp applied to this problem is in [[Interview/Concept/Algorithms/String Matching/00 - Rabin-Karp Algorithm|Rabin-Karp Algorithm]].
- **Average Time Complexity:** $O(N+M)$.
- **Worst-Case Time Complexity:** $O(NM)$.
- **Space Complexity:** $O(1)$ for hash values (or $O(M)$ if storing pattern string).

### 4. Knuth-Morris-Pratt (KMP) Algorithm
KMP is an optimized string matching algorithm that preprocesses the `needle` to build an LPS (Longest Proper Prefix which is also Suffix) array. This allows skipping redundant comparisons when a mismatch occurs.
- **Time Complexity:** $O(N+M)$ (preprocessing `needle` is $O(M)$, searching is $O(N)$).
- **Space Complexity:** $O(M)$ for the LPS array.
(KMP is a more advanced topic, usually covered separately, e.g., `[[Interview/Concept/Algorithms/String Matching/01 - KMP Algorithm|KMP Algorithm]]` - placeholder).

## Recommended Solution for Interviews (if not using built-in)

If asked to implement manually, the brute-force approach is simple to code. If better performance is required and KMP is known, it's optimal. Rabin-Karp is also a valid approach. The choice might depend on interviewer expectations. For LC28, brute-force usually passes.

Python Solution (using the brute-force approach, as it's simplest to implement manually if `find` is disallowed):
```python
class Solution:
    def strStr(self, haystack: str, needle: str) -> int:
        N, M = len(haystack), len(needle)
        
        if M == 0: # According to LeetCode, an empty needle is found at index 0.
            return 0
        if N < M: # Needle cannot be in haystack if it's longer.
            return -1

        for i in range(N - M + 1):
            # Check if the substring of haystack starting at i and of length M matches needle
            if haystack[i : i+M] == needle:
                return i
        
        return -1
```
This Python version leverages string slicing, which is efficient in Python. The slice `haystack[i : i+M]` takes $O(M)$ time to create and compare. So the loop runs $N-M+1$ times, giving $O((N-M)M)$ complexity.

## 总结 (Summary)
- LC28 (Implement `strStr()`) asks for the first index of `needle` in `haystack`.
- **Built-in:** `haystack.find(needle)` is simplest.
- **Brute-Force:** Iterate and compare substrings, $O(NM)$.
- **Rabin-Karp:** Hashing with rolling hash, average $O(N+M)$.
- **KMP:** Optimal $O(N+M)$ using precomputed LPS array for `needle`.
- For typical interview constraints on this problem, brute-force or Python's slicing comparison often suffices if built-ins are disallowed.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/String Matching/00 - Rabin-Karp Algorithm|Rabin-Karp Algorithm]], `[[Interview/Concept/Algorithms/String Matching/01 - KMP Algorithm|KMP Algorithm]]`
