---
tags: [problem/leetcode, lc/easy, topic/array, topic/searching, pattern/binary_search]
aliases: [LC704, LeetCode 704. Binary Search, 二分查找]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 704. Binary Search
> Solution based on [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary Search Core Template]] from Labuladong's notes.

# LeetCode 704: Binary Search

## Problem Statement

Given an array of integers `nums` which is sorted in ascending order, and an integer `target`, write a function to search `target` in `nums`. If `target` exists, then return its index. Otherwise, return -1.

You must write an algorithm with $O(\log n)$ runtime complexity.

**Official Link:** [LeetCode 704. Binary Search](https://leetcode.com/problems/binary-search/)

## Solution Approach: Basic Binary Search

This problem is a direct application of the standard binary search algorithm.
We use two pointers, `left` and `right`, to define the current search interval `[left, right]`.
1. Initialize `left = 0`, `right = len(nums) - 1`.
2. While `left <= right`:
   a. Calculate `mid = left + (right - left) // 2`.
   b. If `nums[mid] == target`, return `mid`.
   c. If `nums[mid] < target`, the target must be in the right half: `left = mid + 1`.
   d. If `nums[mid] > target`, the target must be in the left half: `right = mid - 1`.
3. If the loop finishes, the target was not found. Return -1.

### Python Solution (Standard Template)
```python
class Solution:
    def search(self, nums: list[int], target: int) -> int:
        left, right = 0, len(nums) - 1

        while left <= right:
            mid = left + (right - left) // 2 # Prevent potential overflow

            if nums[mid] == target:
                return mid
            elif nums[mid] < target:
                left = mid + 1
            else: # nums[mid] > target
                right = mid - 1

        return -1 # Target not found
```
Labuladong's visualizer `div_binary-search` in the "二分搜索算法核心代码模板.md" article illustrates this exact process.

## Complexity Analysis
- **Time Complexity:** $O(\log N)$, where $N$ is the number of elements in `nums`. Each step halves the search space.
- **Space Complexity:** $O(1)$, as only a few variables are used.

## 总结 (Summary)
- LC704 is a fundamental binary search problem.
- The standard template with a closed search interval `[left, right]` and `while left <= right` condition works effectively.
- Key is to correctly adjust `left` and `right` pointers based on the comparison of `nums[mid]` with `target`.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary Search Core Template]]
