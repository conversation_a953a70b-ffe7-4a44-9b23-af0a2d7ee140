---
tags: [problem/leetcode, lc/medium, topic/trie, pattern/data_structure_implementation, course/labuladong]
aliases: [LC208, Implement Trie, Prefix Tree]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 208. Implement Trie (Prefix Tree)
> This problem is discussed in [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/【练习】Trie 树算法习题.md|【练习】Trie 树算法习题 by Labuladong]].
> Solution based on standard TrieSet implementation.

# LeetCode 208: Implement Trie (Prefix Tree)

## Problem Statement
A [Trie](https://en.wikipedia.org/wiki/Trie) (pronounced "try") or **prefix tree** is a tree data structure used to efficiently store and retrieve keys in a dataset of strings. There are various applications of this data structure, such as autocomplete and spellchecker.

Implement the Trie class:
- `Trie()` Initializes the trie object.
- `void insert(String word)` Inserts the string `word` into the trie.
- `boolean search(String word)` Returns `true` if the string `word` is in the trie (i.e., was inserted before), and `false` otherwise.
- `boolean startsWith(String prefix)` Returns `true` if there is a previously inserted string `word` that has the prefix `prefix`, and `false` otherwise.

**Official Link:** [LeetCode 208. Implement Trie (Prefix Tree)](https://leetcode.com/problems/implement-trie-prefix-tree/)

## Solution Approach: Standard TrieSet

This problem requires implementing a basic Trie structure, often referred to as a `TrieSet` because it primarily deals with the existence of words rather than associating them with values. The core components are:
1.  **`TrieNode` Class:** Each node will typically contain:
    *   `children`: A way to store child nodes, commonly a dictionary mapping characters to child `TrieNode` objects, or an array if the character set is fixed and small (e.g., 26 lowercase English letters).
    *   `is_end_of_word`: A boolean flag indicating if the path from the root to this node forms a complete word inserted into the Trie.
2.  **`Trie` Class:**
    *   `root`: The root `TrieNode` of the Trie.
    *   `insert(word)`: Traverses the Trie, creating nodes as necessary for each character in `word`. Marks the final node as `is_end_of_word`.
    *   `_find_node(prefix)`: A helper method to traverse the Trie for a given string (word or prefix) and return the node corresponding to the end of that string, or `None` if the path doesn't exist.
    *   `search(word)`: Uses `_find_node(word)`. Returns `True` if the node exists and its `is_end_of_word` is `True`.
    *   `startsWith(prefix)`: Uses `_find_node(prefix)`. Returns `True` if the node exists (meaning the prefix path exists).

Labuladong emphasizes that with a standard `TrieSet` (or `TrieMap`) implementation like the one from [[Interview/Concept/Data Structures/Trie/01 - TrieMap and TrieSet Implementation|TrieMap and TrieSet Implementation]], this problem becomes a direct application.

### Python Solution

```python
class TrieNode:
    def __init__(self):
        # Using a dictionary for children for general character support.
        # For 'a'-'z' only, an array of size 26 could be used: [None] * 26
        self.children = {} 
        self.is_end_of_word = False

class Trie:
    def __init__(self):
        self.root = TrieNode()

    def insert(self, word: str) -> None:
        curr = self.root
        for char_code in word:
            if char_code not in curr.children:
                curr.children[char_code] = TrieNode()
            curr = curr.children[char_code]
        curr.is_end_of_word = True

    def _find_node(self, text: str) -> TrieNode | None:
        '''Helper to find node corresponding to end of text (word or prefix).'''
        curr = self.root
        for char_code in text:
            if char_code not in curr.children:
                return None
            curr = curr.children[char_code]
        return curr

    def search(self, word: str) -> bool:
        node = self._find_node(word)
        return node is not None and node.is_end_of_word

    def startsWith(self, prefix: str) -> bool:
        node = self._find_node(prefix)
        return node is not None

# Your Trie object will be instantiated and called as such:
# obj = Trie()
# obj.insert(word)
# param_2 = obj.search(word)
# param_3 = obj.startsWith(prefix)
```

**Optimization for Character Set:**
As mentioned in [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/【练习】Trie 树算法习题.md|the exercise source]], if words only contain lowercase English letters, `TrieNode.children` can be an array:
```python
# class TrieNodeOptimized:
#     def __init__(self):
#         self.children = [None] * 26 # For 'a' through 'z'
#         self.is_end_of_word = False

# # In methods:
# char_idx = ord(char_code) - ord('a')
# if curr.children[char_idx] is None:
#     curr.children[char_idx] = TrieNodeOptimized()
# curr = curr.children[char_idx]
```
This can offer minor performance benefits by avoiding dictionary overhead but reduces generality.

## Complexity Analysis
Let $L$ be the length of the word/prefix being operated on. Let $N$ be the total number of words and $L_{avg}$ be their average length.
- **`insert(word)`:**
    - Time: $O(L)$, where $L$ is the length of `word`. We iterate through each character.
    - Space: $O(L)$ in the worst case if `word` introduces $L$ new nodes.
- **`search(word)`:**
    - Time: $O(L)$, where $L$ is the length of `word`.
    - Space: $O(1)$.
- **`startsWith(prefix)`:**
    - Time: $O(L)$, where $L$ is the length of `prefix`.
    - Space: $O(1)$.
- **Space Complexity of Trie:** $O(\sum L_i)$ in the worst case (no shared prefixes), or $O(Number\_of\_Nodes \times AlphabetSize)$ if using array-based children. More typically, proportional to the total number of characters in all unique words if prefixes are shared.

## 总结 (Summary)
- LC208 requires implementing a basic Trie for `insert`, `search` (exact word), and `startsWith` (prefix) operations.
- The solution involves a `TrieNode` class and a `Trie` class that manages these nodes.
- Key operations rely on traversing the Trie character by character from the root.
- `is_end_of_word` flag in `TrieNode` is crucial to distinguish between prefixes and actual inserted words.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Data Structures/Trie/00 - Trie - Introduction and Principles|Trie Principles]], [[Interview/Concept/Data Structures/Trie/01 - TrieMap and TrieSet Implementation|Trie Implementation]]
Related Problems: `[[Interview/Practice/LeetCode/LC1804 - Implement Trie II (Prefix Tree)|LC1804]]`, `[[Interview/Practice/LeetCode/LC211 - Design Add and Search Words Data Structure|LC211]]`
