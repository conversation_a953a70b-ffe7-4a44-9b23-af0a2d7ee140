---
tags: [problem/leetcode, lc/easy, topic/linked_list, pattern/list_reversal, course/labuladong]
aliases: [LC206, LeetCode 206. Reverse Linked List, 反转链表]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 206. Reverse Linked List
> Solutions and explanations adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷链表算法/单链表的花式反转方法汇总.md]].

# LeetCode 206: Reverse Linked List

## Problem Statement

Given the `head` of a singly linked list, reverse the list, and return *the reversed list*.

**Official Link:** [LeetCode 206. Reverse Linked List](https://leetcode.com/problems/reverse-linked-list/)

**Example 1:**
Input: `head = [1,2,3,4,5]`
Output: `[5,4,3,2,1]`

## Solution Approaches

This problem is a fundamental linked list operation and can be solved iteratively or recursively. The conceptual framework is from [[Interview/Concept/Algorithms/Linked List/01 - Linked List Reversal Techniques|Linked List Reversal Techniques]].

### 1. Iterative Approach

Uses three pointers: `prev`, `curr`, and `next_temp`.
- `prev` stores the previous node (initially `None`).
- `curr` is the current node being processed (initially `head`).
- `next_temp` temporarily stores `curr.next` before `curr.next` is reassigned.

**Steps:**
1. Initialize `prev = None`, `curr = head`.
2. While `curr` is not `None`:
   a. `next_temp = curr.next` (Store next node).
   b. `curr.next = prev` (Reverse pointer of current node).
   c. `prev = curr` (Move `prev` one step forward).
   d. `curr = next_temp` (Move `curr` one step forward).
3. After the loop, `prev` will be the new head of the reversed list.

**Python Solution (Iterative):**
```python
# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, val=0, next=None):
#         self.val = val
#         self.next = next

class Solution:
    def reverseList_iterative(self, head: [ListNode]) -> [ListNode]:
        prev = None
        curr = head

        while curr:
            next_temp = curr.next # Store the next node
            curr.next = prev     # Reverse the current node's pointer
            prev = curr          # Move prev to current node
            curr = next_temp     # Move to the next node in original list

        return prev # prev is the new head
```
Labuladong's visualization panel `div_reverse-linked-list-iter` shows this process.

### 2. Recursive Approach

Define a recursive function `reverse(head)` that reverses the list starting at `head` and returns the new head of this reversed sublist.
1.  **Base Case:** If `head` is `None` or `head.next` is `None` (empty or single-node list), it's already reversed. Return `head`.
2.  **Recursive Step:**
    a.  `new_head = reverse(head.next)`: Recursively reverse the rest of the list. `new_head` is the head of the reversed "tail" part (which is the original last node of the list).
    b.  `head.next.next = head`: The original `head.next` node is now the *last* node of the reversed tail. Make its `next` pointer point back to `head`.
    c.  `head.next = None`: `head` becomes the new tail of the overall reversed list, so its `next` should be `None`.
    d.  Return `new_head` (this is the head of the fully reversed list).

**Python Solution (Recursive):**
```python
class Solution:
    def reverseList(self, head: [ListNode]) -> [ListNode]: # Main function, can choose method
        return self.reverseList_recursive(head)

    def reverseList_recursive(self, head: [ListNode]) -> [ListNode]:
        if not head or not head.next:
            return head

        # Recursively reverse the sublist starting from head.next
        last_node_of_reversed_sublist = self.reverseList_recursive(head.next)

        # head.next is the original second node, which is now the tail of the reversed sublist.
        # Make its 'next' pointer point to head.
        head.next.next = head

        # head is now the new tail of the overall reversed list.
        head.next = None

        # The 'last_node_of_reversed_sublist' is the original tail of the list,
        # which is the new head of the fully reversed list.
        return last_node_of_reversed_sublist
```
Labuladong's visualization panel `div_reverse-linked-list` and accompanying images (`![](/algo/images/reverse-linked-list/3.jpg)` etc.) explain this recursive process.

## Complexity Analysis

**Iterative Approach:**
-   **Time Complexity:** $O(N)$, where $N$ is the number of nodes. Each node is visited once.
-   **Space Complexity:** $O(1)$, as only a few pointers are used.

**Recursive Approach:**
-   **Time Complexity:** $O(N)$. Each node is visited once during the recursion unwind.
-   **Space Complexity:** $O(N)$ due to the recursion call stack. In the worst case (a long list), the stack depth can be $N$.

## 总结 (Summary)
- Reversing a singly linked list is a fundamental operation.
- The iterative solution uses three pointers (`prev`, `curr`, `next_temp`) and is $O(N)$ time, $O(1)$ space.
- The recursive solution breaks the problem into reversing the "rest" of the list and then attaching the current head to the end of that reversed "rest". It's $O(N)$ time and $O(N)$ space (for recursion stack).
- The iterative approach is generally preferred for space efficiency.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Linked List/01 - Linked List Reversal Techniques|Linked List Reversal Techniques]]
