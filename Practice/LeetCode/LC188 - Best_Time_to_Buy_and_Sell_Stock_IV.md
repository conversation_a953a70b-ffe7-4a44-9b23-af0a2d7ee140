---
tags: [problem/leetcode, lc/hard, topic/dynamic_programming, pattern/stock_trading, course/labuladong, lc/lc188]
aliases: [LC188, Best Time to Buy and Sell Stock IV]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/一个方法团灭 LeetCode 股票买卖问题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 188. Best Time to Buy and Sell Stock IV
> This problem is part of the [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Stock Trading Pattern|Stock Trading Pattern]].

# LeetCode 188: Best Time to Buy and Sell Stock IV

## Problem Statement
(To be filled from LeetCode - Visit [https://leetcode.com/problems/best-time-to-buy-and-sell-stock-iv/](https://leetcode.com/problems/best-time-to-buy-and-sell-stock-iv/))
*Note: Auto-generated URL might be incorrect if title doesn't match LC slug.*

## Solution Approach
This problem can be solved using the [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Stock Trading Pattern|Stock Trading DP Pattern]].
The specific state transitions depend on the constraints (k transactions, cooldown, fee).

### Python Solution
```python
class Solution:
    def maxProfit(self, prices: list[int]) -> int: # Or (self, k, prices) etc.
        # Solution will be adapted from the general Stock Trading Pattern
        # For LC188, specific constraints apply:
        # (e.g., k=1, k=infinity, k=2, k=variable, +cooldown, +fee)

        # Example for k=1 (LC121)
        if "188" == "121":
            if not prices:
                return 0
            dp_i0 = 0
            dp_i1 = float('-inf')
            for price in prices:
                dp_i0 = max(dp_i0, dp_i1 + price)
                dp_i1 = max(dp_i1, -price) # Buy action means dp_i_0_prev_k0 = 0
            return dp_i0

        # Example for k=infinity (LC122)
        elif "188" == "122":
            if not prices:
                return 0
            dp_i0 = 0
            dp_i1 = float('-inf')
            for price in prices:
                temp = dp_i0
                dp_i0 = max(dp_i0, dp_i1 + price)
                dp_i1 = max(dp_i1, temp - price)
            return dp_i0

        # Example for k=infinity with cooldown (LC309)
        elif "188" == "309":
            if not prices:
                return 0
            dp_i0 = 0
            dp_i1 = float('-inf')
            dp_pre_0 = 0 # Represents dp[i-2][0]
            for price in prices:
                temp = dp_i0
                dp_i0 = max(dp_i0, dp_i1 + price)
                dp_i1 = max(dp_i1, dp_pre_0 - price)
                dp_pre_0 = temp
            return dp_i0

        # Example for k=infinity with fee (LC714)
        elif "188" == "714": # Assuming 'fee' is passed, e.g. maxProfit(self, prices, fee)
            # This placeholder needs 'fee' parameter
            # For now, just a structure
            # fee = ... # get fee from actual method signature
            if not prices: return 0
            dp_i0 = 0
            dp_i1 = float('-inf')
            # fee = self.fee # if fee is a class member for a test runner
            # Let's assume fee is a parameter for the actual method
            # def maxProfit(self, prices: list[int], fee: int) -> int:
            # for price in prices:
            #     temp = dp_i0
            #     dp_i0 = max(dp_i0, dp_i1 + price)
            #     dp_i1 = max(dp_i1, temp - price - fee) # Assume fee paid on buy
            # return dp_i0
            return -1 # Placeholder, needs fee

        # Example for k=2 (LC123)
        elif "188" == "123":
            if not prices: return 0
            dp_i10, dp_i11 = 0, float('-inf') # Max profit with 1 transaction, 0/1 holding
            dp_i20, dp_i21 = 0, float('-inf') # Max profit with 2 transactions, 0/1 holding
            for price in prices:
                dp_i20 = max(dp_i20, dp_i21 + price)
                dp_i21 = max(dp_i21, dp_i10 - price) # Buy for 2nd transaction uses profit from 1st
                dp_i10 = max(dp_i10, dp_i11 + price)
                dp_i11 = max(dp_i11, -price)       # Buy for 1st transaction from 0 profit
            return dp_i20

        # Example for general k (LC188)
        elif "188" == "188": # Assuming 'k_transactions' is passed
            # This placeholder needs 'k_transactions' parameter
            # def maxProfit(self, k_transactions: int, prices: list[int]) -> int:
            # n = len(prices)
            # if n == 0 or k_transactions == 0: return 0
            # if k_transactions > n // 2: # Use infinity case
            #     dp_i0 = 0
            #     dp_i1 = float('-inf')
            #     for price in prices:
            #         temp = dp_i0
            #         dp_i0 = max(dp_i0, dp_i1 + price)
            #         dp_i1 = max(dp_i1, temp - price)
            #     return dp_i0
            # dp = [[[0] * 2 for _ in range(k_transactions + 1)] for _ in range(n)]
            # for k_val in range(k_transactions + 1):
            #     dp[0][k_val][0] = 0
            #     dp[0][k_val][1] = -prices[0]
            # for i in range(n):
            #     dp[i][0][0] = 0
            #     dp[i][0][1] = float('-inf')
            # for i in range(1, n):
            #     for k_val in range(1, k_transactions + 1):
            #         dp[i][k_val][0] = max(dp[i-1][k_val][0], dp[i-1][k_val][1] + prices[i])
            #         dp[i][k_val][1] = max(dp[i-1][k_val][1], dp[i-1][k_val-1][0] - prices[i])
            # return dp[n-1][k_transactions][0]
            return -1 # Placeholder

        else: # Default placeholder
            return -1 # Needs specific implementation
```

## Complexity Analysis
(Depends on specific problem, generally $O(N)$ or $O(N \cdot K)$ time, $O(1)$ or $O(K)$ or $O(N \cdot K)$ space before optimization)

## 总结 (Summary)
(To be filled based on specific problem adaptation)

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
