---
tags: [problem/leetcode, lc/medium, topic/dynamic_programming, pattern/house_robber, course/labuladong, lc/lc198]
aliases: [LC198, House Robber I]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/一个方法团灭 LeetCode 打家劫舍问题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 198. House Robber
> This is part of the [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/House Robber Pattern|House Robber Pattern]].

# LeetCode 198: House Robber

## Problem Statement
You are a professional robber planning to rob houses along a street. Each house has a certain amount of money stashed, the only constraint stopping you from robbing each of them is that adjacent houses have security systems connected and **it will automatically contact the police if two adjacent houses were broken into on the same night**.
Given an integer array `nums` representing the amount of money of each house, return *the maximum amount of money you can rob tonight **without alerting the police***.

**Official Link:** [LeetCode 198. House Robber](https://leetcode.com/problems/house-robber/)

## Solution Approach
This is the base problem for the House Robber series. The DP solution is detailed in [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/House Robber Pattern|House Robber Pattern]].

### Python Solution
```python
class Solution:
    def rob(self, nums: list[int]) -> int:
        n = len(nums)
        if n == 0:
            return 0
        if n == 1:
            return nums[0]

        # dp[i] = max money robbed considering houses up to index i
        # Using space optimized variables:
        # prev2 stores dp[i-2], prev1 stores dp[i-1]

        prev2 = nums[0]  # Represents dp[0]
        prev1 = max(nums[0], nums[1]) # Represents dp[1]

        if n == 2: # Base case for 2 houses
            return prev1

        current_max = prev1 # Initialize with dp[1]
        for i in range(2, n):
            # Rob house i: nums[i] + money from house i-2 (prev2)
            # Don't rob house i: money from house i-1 (prev1)
            current_max = max(nums[i] + prev2, prev1)
            prev2 = prev1
            prev1 = current_max

        return current_max
```

## Complexity Analysis
- **Time Complexity:** $O(N)$
- **Space Complexity:** $O(1)$ (using optimized variables). If using a DP array, $O(N)$.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
