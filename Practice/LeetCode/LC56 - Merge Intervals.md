---
tags: [problem/leetcode, lc/medium, topic/array, pattern/interval_problems, pattern/sorting, pattern/merging_intervals, course/labuladong, lc/lc56]
aliases: [LC56, Merge Intervals]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/一个方法解决三道区间问题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 56. Merge Intervals
> Solution idea from [[Interview/Concept/Algorithms/Interval Problems/00 - General Interval Problem Solving Strategy|General Interval Problem Solving Strategy]].

# LeetCode 56: Merge Intervals

## Problem Statement
Given an array of `intervals` where `intervals[i] = [start_i, end_i]`, merge all overlapping intervals, and return *an array of the non-overlapping intervals that cover all the intervals in the input*.

**Official Link:** [LeetCode 56. Merge Intervals](https://leetcode.com/problems/merge-intervals/)

## Solution Approach: Sort + Merge
1.  **Sort intervals by start points.** This is crucial.
2.  Initialize `merged = []`. If `intervals` is empty, return `merged`.
3.  Add the first interval from sorted `intervals` to `merged`.
4.  Iterate through the rest of the sorted `intervals` (from the second interval onwards):
    - Let `last_merged_interval = merged[-1]`.
    - Let `current_interval = intervals[i]`.
    - **If `current_interval[0] <= last_merged_interval[1]` (Overlap Condition):**
        - There's an overlap. Merge `current_interval` into `last_merged_interval`.
        - `last_merged_interval[1] = max(last_merged_interval[1], current_interval[1])`.
    - **Else (No Overlap):**
        - Add `current_interval` to `merged`.
5.  Return `merged`.

### Python Solution
```python
class Solution:
    def merge(self, intervals: list[list[int]]) -> list[list[int]]:
        if not intervals:
            return []

        # Sort intervals by their start times
        intervals.sort(key=lambda x: x[0])

        merged_intervals = []
        merged_intervals.append(intervals[0]) # Add the first interval

        for i in range(1, len(intervals)):
            current_start, current_end = intervals[i]
            last_merged_start, last_merged_end = merged_intervals[-1]

            if current_start <= last_merged_end: # Overlap
                # Merge: update the end of the last interval in merged_intervals
                merged_intervals[-1][1] = max(last_merged_end, current_end)
            else: # No overlap
                merged_intervals.append([current_start, current_end])

        return merged_intervals
```

## Complexity Analysis
- **Time Complexity:** $O(N \log N)$ for sorting. The merging pass is $O(N)$.
- **Space Complexity:** $O(N)$ for storing the `merged_intervals` list (in the worst case, no intervals merge). $O(N)$ or $O(\log N)$ for sorting, depending on implementation.

## 总结 (Summary)
LC56 asks to merge overlapping intervals. The standard approach is to sort intervals by start times, then iterate through them, merging with the last interval in the result list if an overlap exists, or adding as a new interval otherwise.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
