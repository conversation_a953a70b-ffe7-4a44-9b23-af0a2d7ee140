---
tags: [problem/leetcode, lc/medium, topic/array, topic/matrix, pattern/matrix_rotation]
aliases: [LC48, LeetCode 48. Rotate Image, 旋转图像]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 48. Rotate Image
> Solution based on [[Interview/Concept/Algorithms/Array Manipulation/00 - 2D Array Fancy Traversal|2D Array Fancy Traversal techniques]] from Labuladong.

# LeetCode 48: Rotate Image

## Problem Statement
You are given an `n x n` 2D `matrix` representing an image. Rotate the image by **90 degrees (clockwise)**.
You have to rotate the image **in-place**, which means you have to modify the input 2D matrix directly. **DO NOT** allocate another 2D matrix and do the rotation.

**Official Link:** [LeetCode 48. Rotate Image](https://leetcode.com/problems/rotate-image/)

## Solution Approach: Transpose + Reverse Rows

As described in [[Interview/Concept/Algorithms/Array Manipulation/00 - 2D Array Fancy Traversal|2D Array Fancy Traversal]], a 90-degree clockwise rotation can be achieved by:
1.  **Transposing the matrix:** For an element `matrix[row][col]`, swap it with `matrix[col][row]`. This effectively flips the matrix over its main diagonal.
2.  **Reversing each row:** After transposing, iterate through each row and reverse its elements.

### Python Solution
```python
class Solution:
    def rotate(self, matrix: list[list[int]]) -> None:
        '''
        Do not return anything, modify matrix in-place instead.
        '''
        n = len(matrix)

        # Step 1: Transpose the matrix
        # Only need to iterate through the upper triangle (or lower)
        for r in range(n):
            for c in range(r + 1, n): # c starts from r+1 to avoid double swaps and diagonal
                matrix[r][c], matrix[c][r] = matrix[c][r], matrix[r][c]

        # Step 2: Reverse each row
        for r in range(n):
            # matrix[r].reverse() # Python's built-in list reverse
            # Or manual reverse:
            left, right = 0, n - 1
            while left < right:
                matrix[r][left], matrix[r][right] = matrix[r][right], matrix[r][left]
                left += 1
                right -= 1
```
Labuladong's article provides GIFs: `![](/algo/images/2d-array/2.gif)` (transpose) and `![](/algo/images/2d-array/3.gif)` (row reversal).

## Complexity Analysis
- **Time Complexity:** $O(N^2)$, where $N$ is the dimension of the square matrix (i.e., `matrix` has $N \times N$ elements).
    - Transposition involves iterating through roughly half the elements ($N^2/2$).
    - Reversing each of $N$ rows takes $O(N)$ per row, so $N \times O(N) = O(N^2)$ for all rows.
- **Space Complexity:** $O(1)$, as the operations are done in-place.

## 总结 (Summary)
- LC48 requires in-place 90-degree clockwise rotation of an $N \times N$ matrix.
- The two-step process (transpose, then reverse each row) provides an elegant solution.
- The time complexity is $O(N^2)$ and space complexity is $O(1)$.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Array Manipulation/00 - 2D Array Fancy Traversal|2D Array Fancy Traversal]]
