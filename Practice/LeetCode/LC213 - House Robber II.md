---
tags: [problem/leetcode, lc/medium, topic/dynamic_programming, pattern/house_robber, course/labuladong, lc/lc213]
aliases: [LC213, House Robber II]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/一个方法团灭 LeetCode 打家劫舍问题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 213. House Robber II
> This is part of the [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/House Robber Pattern|House Robber Pattern]].

# LeetCode 213: House Robber II

## Problem Statement
You are a professional robber planning to rob houses along a street. Each house has a certain amount of money stashed. All houses at this place are **arranged in a circle**. That means the first house is the neighbor of the last one. Meanwhile, adjacent houses have security systems connected and **it will automatically contact the police if two adjacent houses were broken into on the same night**.
Given an integer array `nums` representing the amount of money of each house, return *the maximum amount of money you can rob tonight **without alerting the police***.

**Official Link:** [LeetCode 213. House Robber II](https://leetcode.com/problems/house-robber-ii/)

## Solution Approach
This problem extends House Robber I by making the arrangement circular. The solution is detailed in [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/House Robber Pattern|House Robber Pattern]]. We break it into two linear subproblems.

### Python Solution
```python
class Solution:
    def _rob_linear(self, nums_segment: list[int]) -> int:
        n = len(nums_segment)
        if n == 0:
            return 0
        if n == 1:
            return nums_segment[0]

        prev2 = nums_segment[0]
        prev1 = max(nums_segment[0], nums_segment[1])

        if n == 2:
            return prev1

        current_max = prev1
        for i in range(2, n):
            current_max = max(nums_segment[i] + prev2, prev1)
            prev2 = prev1
            prev1 = current_max
        return current_max

    def rob(self, nums: list[int]) -> int:
        n = len(nums)
        if n == 0:
            return 0
        if n == 1: # Special case for single house (cannot be handled by slicing below)
            return nums[0]

        # Case 1: Rob houses [0...n-2] (exclude last house)
        max1 = self._rob_linear(nums[:-1])
        # Case 2: Rob houses [1...n-1] (exclude first house)
        max2 = self._rob_linear(nums[1:])

        return max(max1, max2)
```

## Complexity Analysis
- **Time Complexity:** $O(N)$ because `_rob_linear` is called twice on arrays of size $N-1$.
- **Space Complexity:** $O(1)$ (if `_rob_linear` is space-optimized). Slicing `nums[:-1]` and `nums[1:]` creates copies, so technically $O(N)$ space for slices. To achieve true $O(1)$, `_rob_linear` would need to accept `start` and `end` indices.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
