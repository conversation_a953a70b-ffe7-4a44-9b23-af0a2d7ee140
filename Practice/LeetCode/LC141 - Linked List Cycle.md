---
tags: [problem/leetcode, lc/easy, topic/linked_list, topic/two_pointers, pattern/fast_slow_pointers, course/labuladong]
aliases: [LC141, LeetCode 141. Linked List Cycle]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 141. Linked List Cycle
> Solution from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道链表题目.md]].

# LeetCode 141: Linked List Cycle

## Problem Statement

Given `head`, the head of a linked list, determine if the linked list has a cycle in it.

There is a cycle in a linked list if there is some node in the list that can be reached again by continuously following the `next` pointer. Internally, `pos` is used to denote the index of the node that tail's `next` pointer is connected to. **Note that `pos` is not passed as a parameter.**

Return `true` *if there is a cycle in the linked list*. Otherwise, return `false`.

**Official Link:** [LeetCode 141. Linked List Cycle](https://leetcode.com/problems/linked-list-cycle/)

**Example 1:**
Input: `head = [3,2,0,-4]`, `pos = 1`
Output: `true`
Explanation: There is a cycle in the linked list, where the tail connects to the 1st node (0-indexed).

Visual from Labuladong's article (similar to `LC142` but general cycle):
`![](/algo/images/linked-list-two-pointer/circularlinkedlist.png)`

## Solution Approach: Fast and Slow Pointers

This is a classic application of the [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Fast and Slow Pointers technique for linked lists]].

1.  Initialize two pointers, `slow` and `fast`, both starting at `head`.
2.  Move `slow` one step at a time (`slow = slow.next`).
3.  Move `fast` two steps at a time (`fast = fast.next.next`).
4.  If `fast` encounters `None` or `fast.next` is `None`, it means `fast` has reached the end of the list, so there is no cycle. Return `False`.
5.  If `slow` and `fast` meet at some point (`slow == fast`), it means there is a cycle in the list. Return `True`.

### Python Solution (from Labuladong's article)
```python
# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, x):
#         self.val = x
#         self.next = None

class Solution:
    def hasCycle(self, head: [ListNode]) -> bool:
        # Initialize slow and fast pointers to the head of the list
        slow, fast = head, head

        # Loop until fast pointer reaches the end or detects a cycle
        while fast is not None and fast.next is not None:
            # Move slow pointer one step
            slow = slow.next
            # Move fast pointer two steps
            fast = fast.next.next

            # If slow and fast pointers meet, there's a cycle
            if slow == fast:
                return True

        # If fast pointer reaches the end (None), there's no cycle
        return False
```
Labuladong's visualization `div_linked-list-cycle` for this problem illustrates the pointer movements.

## Complexity Analysis
-   **Time Complexity:** $O(N)$, where $N$ is the number of nodes in the linked list.
    - If there's no cycle, `fast` reaches the end in $N/2$ steps.
    - If there is a cycle, `fast` enters the cycle and will eventually lap `slow`. The number of steps is bounded by $N$.
-   **Space Complexity:** $O(1)$, as we only use two pointers.

## 总结 (Summary)
- Detecting a cycle in a linked list is a canonical problem solved using the fast and slow pointer technique.
- If the `fast` pointer (moving two steps) meets the `slow` pointer (moving one step), a cycle exists.
- If `fast` reaches the end of the list (`None`), no cycle exists.
- The solution is efficient with $O(N)$ time and $O(1)$ space complexity.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]]
Next Problem (related): [[Interview/Practice/LeetCode/LC142 - Linked List Cycle II|LC142 - Linked List Cycle II]]
