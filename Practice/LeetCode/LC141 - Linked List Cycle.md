---
tags: [problem/leetcode, lc/easy, topic/linked_list, topic/two_pointers, pattern/fast_slow_pointers]
aliases: [LC141, LeetCode 141. Linked List Cycle]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 141. Linked List Cycle
> Solution from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道链表题目.md]].

# LeetCode 141: Linked List Cycle

## Problem Statement

Given `head`, the head of a linked list, determine if the linked list has a cycle in it.

There is a cycle in a linked list if there is some node in the list that can be reached again by continuously following the `next` pointer. Internally, `pos` is used to denote the index of the node that tail's `next` pointer is connected to. **Note that `pos` is not passed as a parameter.**

Return `true` *if there is a cycle in the linked list*. Otherwise, return `false`.

**Official Link:** [LeetCode 141. Linked List Cycle](https://leetcode.com/problems/linked-list-cycle/)

**Example 1:**
Input: `head = [3,2,0,-4]`, `pos = 1` (2 is connected to -4)
Output: `true`
Explanation: There is a cycle in the linked list, where the tail connects to the 1st node (0-indexed).

![](/algo/images/linked-list-two-pointer/circularlinkedlist.png)
*(Example visual from a similar problem, LC142, but illustrates a cycle)*

## Solution Approach: Fast and Slow Pointers

This is a classic application of the [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Fast and Slow Pointers technique for linked lists]].

1.  Initialize two pointers, `slow` and `fast`, both starting at `head`.
2.  Move `slow` one step at a time (`slow = slow.next`).
3.  Move `fast` two steps at a time (`fast = fast.next.next`).
4.  If `fast` encounters `None` or `fast.next` is `None`, it means `fast` has reached the end of the list, so there is no cycle. Return `False`.
5.  If `slow` and `fast` meet at some point (`slow == fast`), it means there is a cycle in the list. Return `True`.

### Python Solution

```python
# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, x):
#         self.val = x
#         self.next = None

class Solution:
    def hasCycle(self, head: [ListNode]) -> bool:
        slow, fast = head, head

        while fast is not None and fast.next is not None:
            slow = slow.next
            fast = fast.next.next
            
            # If slow and fast pointers meet, there's a cycle
            if slow == fast:
                return True
        
        # If fast reaches the end (None), there's no cycle
        return False

```

## Complexity Analysis
-   **Time Complexity:** $O(N)$, where $N$ is the number of nodes in the linked list.
    - If there's no cycle, `fast` reaches the end in $N/2$ steps.
    - If there is a cycle, `fast` enters the cycle and will eventually lap `slow`. The number of steps is bounded by $N$.
-   **Space Complexity:** $O(1)$, as we only use two pointers.

## Visualization

**Scenario 1: No Cycle** `head = [1,2,3]`
- Init: `slow=1, fast=1`
- Step 1: `slow=2, fast=3`
- Step 2: `slow=3, fast=None` (since `fast.next.next` is `None`). Loop terminates. Return `False`.

**Scenario 2: Cycle** `head = [3,2,0,-4]`, `pos = 1` (tail `-4` connects to node `2`)

```tikz
\begin{tikzpicture}[
    node_style/.style={draw, rectangle, minimum size=0.8cm, font=\sffamily\small},
    ptr_label/.style={font=\sffamily\bfseries\tiny, above=0.1cm},
    cycle_arrow/.style={->, thick, red, dashed, bend left=30},
    move_arrow/.style={->, thick, blue, shorten >=1pt}
]
    \node[node_style] (n3) at (0,0) {3};
    \node[node_style] (n2) at (2,0) {2};
    \node[node_style] (n0) at (4,0) {0};
    \node[node_style] (n_4) at (6,0) {-4};

    \draw[move_arrow] (n3) -- (n2);
    \draw[move_arrow] (n2) -- (n0);
    \draw[move_arrow] (n0) -- (n_4);
    \draw[cycle_arrow] (n_4.east) to[bend left=40] (n2.east); % Cycle: -4 points to 2

    % Initial
    \node[ptr_label, color=orange!80!black] at (n3.north) {S,F};
    \node at (3,-1) {Initial: `slow` at 3, `fast` at 3};

    % Step 1
    \begin{scope}[yshift=-2cm]
        \node[node_style] (s1_n3) at (0,0) {3};
        \node[node_style] (s1_n2) at (2,0) {2}; \node[ptr_label, color=orange!80!black] at (s1_n2.north) {S};
        \node[node_style] (s1_n0) at (4,0) {0}; \node[ptr_label, color=purple!80!black] at (s1_n0.north) {F};
        \node[node_style] (s1_n_4) at (6,0) {-4};
        \draw[move_arrow] (s1_n3) -- (s1_n2); \draw[move_arrow] (s1_n2) -- (s1_n0); \draw[move_arrow] (s1_n0) -- (s1_n_4);
        \draw[cycle_arrow] (s1_n_4.east) to[bend left=40] (s1_n2.east);
        \node at (3,-1) {Step 1: `slow` at 2, `fast` at 0};
    \end{scope}

    % Step 2
    \begin{scope}[yshift=-4cm]
        \node[node_style] (s2_n3) at (0,0) {3};
        \node[node_style] (s2_n2) at (2,0) {2};
        \node[node_style] (s2_n0) at (4,0) {0}; \node[ptr_label, color=orange!80!black] at (s2_n0.north) {S};
        \node[node_style] (s2_n_4) at (6,0) {-4}; \node[ptr_label, color=purple!80!black] at (s2_n_4.north) {F};
        \draw[move_arrow] (s2_n3) -- (s2_n2); \draw[move_arrow] (s2_n2) -- (s2_n0); \draw[move_arrow] (s2_n0) -- (s2_n_4);
        \draw[cycle_arrow] (s2_n_4.east) to[bend left=40] (s2_n2.east);
        \node at (3,-1) {Step 2: `slow` at 0, `fast` at -4};
    \end{scope}
    
    % Step 3 (fast moves to 0, then -4; slow moves to -4)
    \begin{scope}[yshift=-6cm]
        \node[node_style] (s3_n3) at (0,0) {3};
        \node[node_style] (s3_n2) at (2,0) {2}; % Fast was at -4, next is 2, then 0
        \node[node_style] (s3_n0) at (4,0) {0}; \node[ptr_label, color=purple!80!black] at (s3_n0.north) {F}; % Fast moves -4 -> 2 -> 0
        \node[node_style] (s3_n_4) at (6,0) {-4}; \node[ptr_label, color=orange!80!black] at (s3_n_4.north) {S}; % Slow moves 0 -> -4
        \draw[move_arrow] (s3_n3) -- (s3_n2); \draw[move_arrow] (s3_n2) -- (s3_n0); \draw[move_arrow] (s3_n0) -- (s3_n_4);
        \draw[cycle_arrow] (s3_n_4.east) to[bend left=40] (s3_n2.east);
        \node at (3,-1) {Step 3: `slow` at -4, `fast` at 0 (after -4 -> 2 -> 0)};
    \end{scope}
    
    % Step 4 (fast moves to -4, then 2; slow moves to 2) - THEY MEET!
    \begin{scope}[yshift=-8cm, node_style/.append style={fill=yellow!30}]
        \node[node_style] (s4_n3) at (0,0) {3};
        \node[node_style] (s4_n2) at (2,0) {2}; \node[ptr_label, color=orange!80!black] at (s4_n2.north) {S,F MEET!};
        \node[node_style] (s4_n0) at (4,0) {0};
        \node[node_style] (s4_n_4) at (6,0) {-4};
        \draw[move_arrow] (s4_n3) -- (s4_n2); \draw[move_arrow] (s4_n2) -- (s4_n0); \draw[move_arrow] (s4_n0) -- (s4_n_4);
        \draw[cycle_arrow] (s4_n_4.east) to[bend left=40] (s4_n2.east);
        \node at (3,-1) {Step 4: `slow` at 2, `fast` at 2. Meet! Return `True`.};
    \end{scope}
\end{tikzpicture}
```

## 总结 (Summary)
- Detecting a cycle in a linked list is a canonical problem solved using the fast and slow pointer technique.
- If the `fast` pointer (moving two steps) meets the `slow` pointer (moving one step), a cycle exists.
- If `fast` reaches the end of the list (`None`), no cycle exists.
- The solution is efficient with $O(N)$ time and $O(1)$ space complexity.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Previous: [[Interview/Practice/LeetCode/LC876 - Middle of the Linked List|LC876 - Middle of the Linked List]] (Example - order may vary)
Next: [[Interview/Practice/LeetCode/LC142 - Linked List Cycle II|LC142 - Linked List Cycle II]]
Concepts: [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]]
