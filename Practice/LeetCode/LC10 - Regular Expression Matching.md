---
tags: [problem/leetcode, lc/hard, topic/dynamic_programming, topic/string, pattern/regex, course/labuladong, lc/lc10]
aliases: [LC10, Regular Expression Matching]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/经典动态规划：正则表达式.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 10. Regular Expression Matching
> The solution is based on the [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Game DP/Regular Expression Matching|Regex Matching DP Pattern]].

# LeetCode 10: Regular Expression Matching

## Problem Statement
Given an input string `s` and a pattern `p`, implement regular expression matching with support for `'.'` and `'*'` where:
- `'.'` Matches any single character.
- `'*'` Matches zero or more of the preceding element.
The matching should cover the **entire** input string (not partial).

**Official Link:** [LeetCode 10. Regular Expression Matching](https://leetcode.com/problems/regular-expression-matching/)

## Solution Approach
This problem is solved using dynamic programming, as detailed in [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Game DP/Regular Expression Matching|Regex Matching DP Pattern]].

### Python Solution
```python
class Solution:
    def isMatch(self, s: str, p: str) -> bool:
        m, n = len(s), len(p)

        # dp[i][j] will be true if s[0..i-1] matches p[0..j-1]
        dp = [[False] * (n + 1) for _ in range(m + 1)]

        # Base cases
        dp[0][0] = True # Empty string matches empty pattern

        # Initialize dp[0][j] for patterns like "a*b*c*" matching empty string s
        for j_pat_idx in range(1, n + 1):
            if p[j_pat_idx-1] == '*':
                # '*' can eliminate itself and the preceding character
                # This depends on dp[0][j-2] (match up to p[j-3])
                if j_pat_idx >= 2: # p[j-2] must exist
                     dp[0][j_pat_idx] = dp[0][j_pat_idx-2]

        # Fill the dp table
        for i_s_idx in range(1, m + 1): # Iterate through s (1 to m)
            for j_pat_idx in range(1, n + 1): # Iterate through p (1 to n)
                s_char = s[i_s_idx-1]
                p_char = p[j_pat_idx-1]

                if p_char == '.' or p_char == s_char:
                    # If pattern char is '.' or matches current s_char
                    # Match depends on previous subproblem
                    dp[i_s_idx][j_pat_idx] = dp[i_s_idx-1][j_pat_idx-1]
                elif p_char == '*':
                    # '*' case: p[j_pat_idx-1] is '*'
                    # p_char_before_star is p[j_pat_idx-2]
                    p_char_before_star = p[j_pat_idx-2] # Must exist due to valid regex constraint

                    # Option 1: '*' matches zero occurrences of p_char_before_star
                    # Then, s[0..i_s_idx-1] must match p[0...j_pat_idx-3] (effectively p up to char before p_char_before_star)
                    match_zero = dp[i_s_idx][j_pat_idx-2]

                    # Option 2: '*' matches one or more occurrences of p_char_before_star
                    # This requires s_char to match p_char_before_star
                    match_one_or_more = False
                    if s_char == p_char_before_star or p_char_before_star == '.':
                        # If s_char matches, then '*' consumes s_char.
                        # We need s[0..i_s_idx-2] to match p[0...j_pat_idx-1]
                        # (p still includes p_char_before_star* part)
                        match_one_or_more = dp[i_s_idx-1][j_pat_idx]

                    dp[i_s_idx][j_pat_idx] = match_zero or match_one_or_more
                # else: (p_char is a normal char and doesn't match s_char)
                # dp[i_s_idx][j_pat_idx] remains False (default)

        return dp[m][n]
```

## Complexity Analysis
- **Time Complexity:** $O(M \cdot N)$, where $M = \text{len}(s)$ and $N = \text{len}(p)$.
- **Space Complexity:** $O(M \cdot N)$ for the DP table.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
