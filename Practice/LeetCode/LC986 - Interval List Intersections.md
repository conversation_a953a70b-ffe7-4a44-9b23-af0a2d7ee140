---
tags: [problem/leetcode, lc/medium, topic/array, pattern/interval_problems, pattern/two_pointers, course/labuladong, lc/lc986]
aliases: [LC986, Interval List Intersections]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/一个方法解决三道区间问题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 986. Interval List Intersections
> Solution idea from [[Interview/Concept/Algorithms/Interval Problems/00 - General Interval Problem Solving Strategy|General Interval Problem Solving Strategy]].

# LeetCode 986: Interval List Intersections

## Problem Statement
You are given two lists of closed intervals, `firstList` and `secondList`, where `firstList[i] = [start_i, end_i]` and `secondList[j] = [start_j, end_j]`. Each list of intervals is pairwise disjoint and in sorted order.
Return *the intersection of these two interval lists*.
A closed interval `[a, b]` (with `a <= b`) denotes the set of real numbers `x` with `a <= x <= b`.
The intersection of two closed intervals is a set of real numbers that are either empty or represented as a closed interval. For example, the intersection of `[1, 3]` and `[2, 4]` is `[2, 3]`.

**Official Link:** [LeetCode 986. Interval List Intersections](https://leetcode.com/problems/interval-list-intersections/)

## Solution Approach: Two Pointers
Since both input lists are sorted and disjoint, we can use a two-pointer approach.
1.  Initialize two pointers, `i = 0` for `firstList` and `j = 0` for `secondList`.
2.  Initialize an empty list `intersections`.
3.  While `i < len(firstList)` and `j < len(secondList)`:
    a.  Let `interval1 = firstList[i]` and `interval2 = secondList[j]`.
    b.  Calculate the potential intersection:
        `start_intersect = max(interval1[0], interval2[0])`
        `end_intersect = min(interval1[1], interval2[1])`
    c.  If `start_intersect <= end_intersect` (a valid intersection exists):
        Add `[start_intersect, end_intersect]` to `intersections`.
    d.  Advance the pointer of the interval that finishes earlier:
        If `interval1[1] < interval2[1]`, increment `i`.
        Else (if `interval2[1] < interval1[1]`), increment `j`.
        Else (if `interval1[1] == interval2[1]`), increment both `i` and `j`.
4.  Return `intersections`.

### Python Solution
```python
class Solution:
    def intervalIntersection(self, firstList: list[list[int]], secondList: list[list[int]]) -> list[list[int]]:
        intersections = []
        i, j = 0, 0

        while i < len(firstList) and j < len(secondList):
            start1, end1 = firstList[i]
            start2, end2 = secondList[j]

            # Calculate intersection
            intersect_start = max(start1, start2)
            intersect_end = min(end1, end2)

            if intersect_start <= intersect_end:
                intersections.append([intersect_start, intersect_end])

            # Move pointer of the interval that ends earlier
            if end1 < end2:
                i += 1
            elif end2 < end1:
                j += 1
            else: # Both end at the same time
                i += 1
                j += 1

        return intersections
```

## Complexity Analysis
- **Time Complexity:** $O(M + N)$, where $M$ is the length of `firstList` and $N$ is the length of `secondList`. Each pointer traverses its list once.
- **Space Complexity:** $O(P)$, where $P$ is the number of intersecting intervals. In the worst case, this can be $O(M+N)$.

## 总结 (Summary)
LC986 finds intersections of two sorted lists of disjoint intervals. A two-pointer approach efficiently identifies overlapping segments by comparing current intervals from each list and advancing the pointer of the interval that finishes sooner.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
