---
tags: [problem/leetcode, lc/hard, topic/linked_list, topic/heap, topic/divide_and_conquer, pattern/merge_k_sorted]
aliases: [<PERSON><PERSON>23, Merge K Sorted Lists, 合并K个升序链表]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 23. Merge K Sorted Lists.
> The Divide and Conquer solution is discussed in [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/分治算法解题套路框架.md]].
> A common alternative uses a Min-Heap.

# LeetCode 23: Merge K Sorted Lists

## Problem Statement

You are given an array of `k` linked-lists `lists`, each linked-list is sorted in ascending order.
Merge all the linked-lists into one sorted linked-list and return it.

**Official Link:** [LeetCode 23. Merge K Sorted Lists](https://leetcode.com/problems/merge-k-sorted-lists/)

**Example 1:**
Input: `lists = [[1,4,5],[1,3,4],[2,6]]`
Output: `[1,1,2,3,4,4,5,6]`

## Solution Approaches

### 1. Brute Force: Collect All and Sort
1. Iterate through all `k` lists, collect all node values into a single list.
2. Sort this list.
3. Create a new sorted linked list from the sorted values.
- **Time Complexity:** $O(N \log N)$, where $N$ is the total number of nodes across all lists. Dominated by sorting.
- **Space Complexity:** $O(N)$ for storing all values and for the new list.

### 2. Iterative Merging (One by One)
1. Take the first list as the initial merged list.
2. Iterate from the second list to the $k$-th list. In each step, merge the current merged list with the current list from the input array.
   - Merging two sorted lists of length $L_1$ and $L_2$ takes $O(L_1+L_2)$ time.
- **Time Complexity:** If average list length is $L_{avg}$, total nodes $N = k \cdot L_{avg}$.
  - Merge 1st and 2nd: $O(L_{avg} + L_{avg})$
  - Merge result (2 $L_{avg}$) with 3rd ($L_{avg}$): $O(2L_{avg} + L_{avg})$
  - ...
  - Sum is $O(L_{avg} \sum_{i=1}^{k-1} (i+1)) = O(L_{avg} \cdot k^2) = O(N \cdot k)$.
- **Space Complexity:** $O(1)$ if merging in-place (modifying pointers), or $O(N)$ for new list if not.

### 3. Using a Min-Heap (Priority Queue)
This is a very common and efficient approach.
1. Initialize a min-heap.
2. Add the head node of each of the `k` lists to the min-heap. The heap stores `(node.val, list_index, node_reference)` to handle value ties and access the node. Python's `heapq` can store `(value, tie_breaker_id, node)` where `tie_breaker_id` can be an increasing counter if node objects are not directly comparable for tie-breaking.
3. While the heap is not empty:
   a. Extract the node with the minimum value from the heap (let it be `min_node`).
   b. Add `min_node` to the result linked list.
   c. If `min_node.next` is not null, add `min_node.next` (from the same original list) to the heap.
- **Time Complexity:**
    - Adding $k$ initial heads to heap: $O(k \log k)$.
    - Total $N$ nodes. Each `pop` from heap is $O(\log k)$. Each `push` to heap is $O(\log k)$.
    - Total: $O(N \log k)$.
- **Space Complexity:** $O(k)$ for the heap (at most $k$ nodes in heap). $O(N)$ for the new list.

```python
import heapq

class ListNode:
    def __init__(self, val=0, next=None):
        self.val = val
        self.next = next
    # Need to add __lt__ for heapq if ListNode objects are directly stored and Python version < 3
    # or if values can be equal and nodes are not otherwise comparable.
    # A common trick is to store (value, id, node) where id is a unique counter.
    def __lt__(self, other): # Necessary for heapq with custom objects if values are equal
        return self.val < other.val


class SolutionHeap:
    def mergeKLists(self, lists: list[ListNode]) -> ListNode:
        min_heap = []
        # To handle cases where node values are equal and ListNode objects are not comparable
        # by default for tie-breaking in heapq, we can add a unique counter.
        unique_id = 0 
        for i, l_head in enumerate(lists):
            if l_head:
                heapq.heappush(min_heap, (l_head.val, unique_id, l_head))
                unique_id += 1
        
        dummy = ListNode(-1)
        current = dummy
        
        while min_heap:
            val, _, node = heapq.heappop(min_heap)
            current.next = node
            current = current.next
            
            if node.next:
                heapq.heappush(min_heap, (node.next.val, unique_id, node.next))
                unique_id += 1
                
        return dummy.next
```

### 4. Divide and Conquer
This approach recursively merges pairs of lists, then pairs of merged lists, and so on, similar to the merge step in Merge Sort. This is the method Labuladong refers to in the "分治算法解题套路框架" context.

1.  **Base Cases:**
    - If `lists` is empty, return `None`.
    - If `lists` contains one list, return that list.
2.  **Divide:** Split `lists` into two halves.
3.  **Conquer:** Recursively call `mergeKLists` on the left half and the right half. This will result in two sorted linked lists (`merged_left`, `merged_right`).
4.  **Combine:** Merge `merged_left` and `merged_right` using a standard two-list merge utility.

```python
# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, val=0, next=None):
#         self.val = val
#         self.next = next

class SolutionDivideConquer:
    def _merge_two_lists(self, l1: ListNode, l2: ListNode) -> ListNode:
        dummy = ListNode(-1)
        curr = dummy
        while l1 and l2:
            if l1.val < l2.val:
                curr.next = l1
                l1 = l1.next
            else:
                curr.next = l2
                l2 = l2.next
            curr = curr.next
        curr.next = l1 if l1 else l2
        return dummy.next

    def mergeKLists(self, lists: list[ListNode]) -> ListNode:
        if not lists:
            return None
        if len(lists) == 1:
            return lists[0]

        # Iterative Divide and Conquer (Pairwise merging)
        # More common in practice than pure recursion for this to avoid deep recursion stack
        # Amount is the number of lists remaining to be merged
        amount = len(lists)
        interval = 1
        while interval < amount:
            for i in range(0, amount - interval, interval * 2):
                lists[i] = self._merge_two_lists(lists[i], lists[i + interval])
            interval *= 2
        return lists[0] if amount > 0 else None

    # Pure Recursive Divide and Conquer (as described by Labuladong)
    def _merge_k_lists_recursive_helper(self, lists: list[ListNode], low: int, high: int) -> ListNode:
        if low > high:
            return None
        if low == high: # Base case: single list
            return lists[low]
        
        mid = low + (high - low) // 2
        
        left_merged = self._merge_k_lists_recursive_helper(lists, low, mid)
        right_merged = self._merge_k_lists_recursive_helper(lists, mid + 1, high)
        
        return self._merge_two_lists(left_merged, right_merged)

    def mergeKLists_recursive_entry(self, lists: list[ListNode]) -> ListNode:
        if not lists:
            return None
        return self._merge_k_lists_recursive_helper(lists, 0, len(lists) - 1)

```
The iterative pairwise merging (`while interval < amount`) is often preferred for `mergeKLists` to avoid deep Python recursion limits for large `k`. It still embodies the divide and conquer strategy by repeatedly merging pairs.

**Time Complexity (Divide and Conquer):**
- Merging two lists of total $N'$ nodes takes $O(N')$.
- There are $\log k$ levels of merging. At each level, a total of $N$ (total nodes in all lists) comparisons/operations are done.
- Total: $O(N \log k)$.
- Example: 8 lists.
  - Level 1: 4 merges (L1+L2, L3+L4, L5+L6, L7+L8)
  - Level 2: 2 merges
  - Level 3: 1 merge
- Each node participates in $\log k$ merges.
**Space Complexity (Divide and Conquer):**
- $O(\log k)$ for recursion stack if using pure recursion.
- $O(1)$ for iterative pairwise merging (modifies input list array, pointers are rearranged).
- $O(N)$ if creating new nodes for the merged list during `_merge_two_lists` instead of rewiring. If rewiring, it's $O(1)$ extra space for pointers beyond recursion.

## Visualization (Divide and Conquer for Merging Lists)

Imagine `lists = [L1, L2, L3, L4, L5, L6, L7, L8]`
```mermaid
graph TD
    subgraph "Level 0 (Initial Lists)"
        L1["L1"] -- merge --> M12["M(L1,L2)"]
        L2["L2"] -- merge --> M12
        L3["L3"] -- merge --> M34["M(L3,L4)"]
        L4["L4"] -- merge --> M34
        L5["L5"] -- merge --> M56["M(L5,L6)"]
        L6["L6"] -- merge --> M56
        L7["L7"] -- merge --> M78["M(L7,L8)"]
        L8["L8"] -- merge --> M78
    end

    subgraph "Level 1 Merges"
        M12 -- merge --> M1234["M(M12,M34)"]
        M34 -- merge --> M1234
        M56 -- merge --> M5678["M(M56,M78)"]
        M78 -- merge --> M5678
    end
    
    subgraph "Level 2 Merges"
        M1234 -- merge --> Final["M(M1234,M5678)"]
        M5678 -- merge --> Final
    end
    
    Final --> Result["Final Merged List"]

    classDef list fill:#cde4ff,stroke:#5a9ae5;
    class L1,L2,L3,L4,L5,L6,L7,L8 list;
    classDef merged fill:#e6ffcc,stroke:#66cc00;
    class M12,M34,M56,M78,M1234,M5678,Final,Result merged;
```
This shows $\log_2 k$ levels of merging.

## 总结 (Summary)
- **Merge K Sorted Lists** can be solved in multiple ways.
- **Brute Force (Collect & Sort):** $O(N \log N)$.
- **Iterative One-by-One Merge:** $O(Nk)$.
- **Min-Heap (Priority Queue):** $O(N \log k)$. Efficient and common. Requires $O(k)$ extra space for heap.
- **Divide and Conquer:** $O(N \log k)$. Similar time complexity to heap, can be $O(1)$ or $O(\log k)$ space for iterative/recursive versions (if merging by rewiring pointers).
- The choice between Min-Heap and Divide and Conquer often comes down to implementation preference or specific constraints (e.g., strict space limits might favor iterative D&C if in-place merging is done carefully, though Python's list of lists means $O(k)$ for list pointers anyway).

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework and Principles|Divide and Conquer]], [[Interview/Concept/Data Structures/Heap/index|Min-Heap]], [[Interview/Concept/Data Structures/Linked List/index|Linked Lists]]
