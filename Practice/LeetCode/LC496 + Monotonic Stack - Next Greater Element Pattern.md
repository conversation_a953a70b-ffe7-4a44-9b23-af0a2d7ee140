---
tags: [pattern/monotonic_stack, topic/stack, topic/array, pattern/next_greater_element, concept/data_structure]
aliases: [Monotonic Stack, 单调栈, Next Greater Element, 下一个更大元素]
---

# Monotonic Stack: Next Greater Element Pattern

## Pattern Overview

The **Monotonic Stack** is a specialized stack data structure that maintains elements in a specific order (either increasing or decreasing). It's particularly powerful for solving "Next Greater Element" type problems efficiently.

**Core Problems Covered:**
- **LC496. Next Greater Element I** (Easy)
- **LC739. Daily Temperatures** (Medium)
- **LC503. Next Greater Element II** (Medium) - Circular Array

## 🎯 Understanding the Monotonic Stack Pattern

### The Real-World Analogy

Imagine people of different heights standing in a line, all facing the same direction:

```tikz
\begin{tikzpicture}[
    person/.style={rectangle, draw, minimum width=0.8cm, font=\tiny, fill=blue!30},
    tall_person/.style={rectangle, draw, minimum width=0.8cm, font=\tiny, fill=green!40},
    blocked/.style={rectangle, draw, minimum width=0.8cm, font=\tiny, fill=red!20, dashed},
    example_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=4cm, align=center}
]

% People standing in line with heights
\node at (6, 8) {\bfseries Height Visibility: Next Taller Person};

% People with different heights
\node[person] (p1) at (1, 6) {2};
\node[person] (p2) at (2, 5.5) {1};
\node[person] (p3) at (3, 6) {2};
\node[tall_person] (p4) at (4, 7) {4};
\node[person] (p5) at (5, 6.5) {3};

% Height indicators
\node at (1, 5.5) {\tiny h=2};
\node at (2, 5) {\tiny h=1};
\node at (3, 5.5) {\tiny h=2};
\node at (4, 6.5) {\tiny h=4};
\node at (5, 6) {\tiny h=3};

% Visibility lines
\draw[->, thick, green] (p1) to[bend left=20] (p4);
\draw[->, thick, green] (p2) to[bend left=30] (p3);
\draw[->, thick, green] (p3) to[bend left=20] (p4);
\draw[->, thick, red] (p4) to[bend left=20] (5.5, 7);
\draw[->, thick, red] (p5) to[bend left=20] (5.5, 6.5);

\node[example_box] at (8, 6) {
    \textbf{Question:}\\
    For each person, who is\\
    the next taller person\\
    they can see?\\[0.5em]
    \textbf{Key Insight:}\\
    Shorter people get\\
    "blocked" by taller ones
};

\node[example_box] at (8, 3.5) {
    \textbf{Answer:}\\
    Person 2 → Person 4\\
    Person 1 → Person 2\\
    Person 2 → Person 4\\
    Person 4 → None\\
    Person 3 → None
};

\end{tikzpicture}
```

**Critical Insight:** This is exactly how monotonic stack works! We maintain a stack of "visible" elements, removing shorter ones when a taller one appears.

## 🧠 The Breakthrough: Stack-Based Visibility

### Step 1: Understanding the Core Algorithm

The key realization is that we can use a stack to efficiently track potential "next greater elements":

```tikz
\begin{tikzpicture}[
    insight_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4.5cm, align=center, minimum height=2.5cm},
    arrow/.style={->, thick, red, line width=2pt},
    strategy_box/.style={rectangle, draw, fill=orange!20, font=\sffamily\small, text width=6cm, align=center}
]

\node[insight_box] (brute) at (0, 3) {
    \textbf{Brute Force:}\\[0.5em]
    "For each element, scan\\
    all elements to the right\\
    to find next greater"\\[0.5em]
    O(n²) time complexity!\\
    Too slow for large inputs!
};

\node[insight_box] (stack) at (8, 3) {
    \textbf{Monotonic Stack:}\\[0.5em]
    "Maintain stack of elements\\
    in decreasing order, pop\\
    when greater element found"\\[0.5em]
    O(n) time complexity!\\
    Each element pushed/popped once!
};

\draw[arrow] (brute) -- (stack);

\node[strategy_box] at (4, 0.5) {
    \textbf{Stack Invariant:}\\
    Stack contains elements in decreasing order from bottom to top\\
    When new element arrives: pop all smaller elements, they found their answer!
};

\end{tikzpicture}
```

### Step 2: The Monotonic Stack Template

The universal template for next greater element problems:

```tikz
\begin{tikzpicture}[
    step_box/.style={rectangle, draw, rounded corners, fill=green!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=2cm},
    formula_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=5cm, align=center}
]

\node[step_box] (scan) at (0, 3) {
    \textbf{Step 1: Scan}\\[0.5em]
    Iterate through array\\
    (usually right to left\\
    for next greater)
};

\node[step_box] (pop) at (4.5, 3) {
    \textbf{Step 2: Pop}\\[0.5em]
    Remove elements from\\
    stack that are smaller\\
    than current element
};

\node[step_box] (answer) at (9, 3) {
    \textbf{Step 3: Answer}\\[0.5em]
    Stack top is the next\\
    greater element\\
    (or empty if none)
};

\node[formula_box] at (4.5, 0.5) {
    \textbf{Template Pattern:}\\
    while stack not empty AND stack.top <= current:\\
    \quad stack.pop()\\
    result[i] = stack.top if stack else -1\\
    stack.push(current)
};

\end{tikzpicture}
```

**Key Insight:** The stack maintains potential candidates for "next greater element" in decreasing order.

## 💡 Core Template Implementation

### The Universal Next Greater Element Template

```python
def next_greater_element_template(nums):
    """
    Universal template for next greater element problems.

    Time: O(n), Space: O(n)
    Each element is pushed and popped at most once.
    """
    n = len(nums)
    result = [0] * n
    stack = []

    # Scan from right to left
    for i in range(n - 1, -1, -1):
        # Pop elements smaller than or equal to current
        while stack and stack[-1] <= nums[i]:
            stack.pop()

        # Stack top is the next greater element
        result[i] = stack[-1] if stack else -1

        # Push current element
        stack.append(nums[i])

    return result
```

### Why Scan Right to Left?

```tikz
\begin{tikzpicture}[
    array_elem/.style={rectangle, draw, minimum width=0.8cm, minimum height=0.8cm, font=\sffamily\small, fill=blue!30},
    stack_elem/.style={rectangle, draw, minimum width=0.8cm, minimum height=0.8cm, font=\sffamily\small, fill=green!40},
    step_box/.style={rectangle, draw, fill=yellow!20, font=\tiny, text width=3cm, align=center}
]

% Array visualization: [2,1,2,4,3]
\node at (6, 8) {\bfseries Why Right-to-Left: Array [2,1,2,4,3]};

% Step 1: Process 3 (rightmost)
\node at (1, 7) {\tiny Step 1: i=4, nums[4]=3};
\node[array_elem] at (1, 6.5) {2};
\node[array_elem] at (2, 6.5) {1};
\node[array_elem] at (3, 6.5) {2};
\node[array_elem] at (4, 6.5) {4};
\node[array_elem, fill=red!30] at (5, 6.5) {3};

\node at (7, 6.5) {\tiny Stack: []};
\node[step_box] at (9, 6.5) {
    Stack empty\\
    result[4] = -1\\
    Push 3
};

% Step 2: Process 4
\node at (1, 5.5) {\tiny Step 2: i=3, nums[3]=4};
\node[array_elem] at (1, 5) {2};
\node[array_elem] at (2, 5) {1};
\node[array_elem] at (3, 5) {2};
\node[array_elem, fill=red!30] at (4, 5) {4};
\node[array_elem] at (5, 5) {3};

\node at (7, 5) {\tiny Stack: [3]};
\node[step_box] at (9, 5) {
    4 > 3, pop 3\\
    result[3] = -1\\
    Push 4
};

% Final insight
\node[step_box, fill=green!30] at (4.5, 3) {
    \textbf{Key Insight:}\\
    Right-to-left ensures we always have\\
    "future" elements in stack when\\
    processing current element
};

\end{tikzpicture}
```

## 🔍 Problem-Specific Implementations

### LC496: Next Greater Element I

```python
class Solution:
    def nextGreaterElement(self, nums1: List[int], nums2: List[int]) -> List[int]:
        """
        Find next greater elements for nums1 elements in nums2.

        Strategy:
        1. Compute next greater for all elements in nums2
        2. Map results for nums1 elements
        """

        # Compute next greater elements for nums2
        def next_greater(nums):
            n = len(nums)
            result = {}
            stack = []

            for i in range(n - 1, -1, -1):
                while stack and stack[-1] <= nums[i]:
                    stack.pop()
                result[nums[i]] = stack[-1] if stack else -1
                stack.append(nums[i])

            return result

        # Get mapping for nums2
        greater_map = next_greater(nums2)

        # Return results for nums1
        return [greater_map[num] for num in nums1]
```

### LC739: Daily Temperatures

```python
class Solution:
    def dailyTemperatures(self, temperatures: List[int]) -> List[int]:
        """
        Find how many days until warmer temperature.

        Key difference: Store indices in stack, return index differences.
        """
        n = len(temperatures)
        result = [0] * n
        stack = []  # Store indices, not values

        for i in range(n - 1, -1, -1):
            # Pop indices with smaller or equal temperatures
            while stack and temperatures[stack[-1]] <= temperatures[i]:
                stack.pop()

            # Calculate days difference
            result[i] = stack[-1] - i if stack else 0

            # Push current index
            stack.append(i)

        return result
```

### LC503: Next Greater Element II (Circular Array)

```python
class Solution:
    def nextGreaterElements(self, nums: List[int]) -> List[int]:
        """
        Handle circular array by simulating double-length array.

        Key insight: Use modulo operator to simulate circular behavior.
        """
        n = len(nums)
        result = [0] * n
        stack = []

        # Process 2*n elements to handle circular nature
        for i in range(2 * n - 1, -1, -1):
            # Use modulo to get actual index
            while stack and stack[-1] <= nums[i % n]:
                stack.pop()

            result[i % n] = stack[-1] if stack else -1
            stack.append(nums[i % n])

        return result
```

## 🐍 The Pythonic Way: Elegant and Expressive

### Understanding Python's Stack Advantages

Python provides elegant tools that make monotonic stack implementations cleaner:

```tikz
\begin{tikzpicture}[
    insight_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2cm},
    comparison_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=5cm, align=center}
]

\node[insight_box] (traditional) at (0, 3) {
    \textbf{Traditional Approach:}\\[0.5em]
    "Manual stack management\\
    with explicit bounds\\
    checking and indexing"\\[0.3em]
    \textit{Verbose but educational}
};

\node[insight_box] (pythonic) at (8, 3) {
    \textbf{Pythonic Approach:}\\[0.5em]
    "Use list as stack,\\
    enumerate for indexing,\\
    and comprehensions"\\[0.3em]
    \textit{Concise and readable}
};

\node[comparison_box] at (4, 0.5) {
    \textbf{Key Insight:}\\
    Python's list operations and built-in functions\\
    make stack manipulation natural and efficient\\[0.3em]
    \textit{Focus on algorithm logic, not implementation details}
};

\end{tikzpicture}
```

### Approach 1: Functional Style with Enumerate

```python
class Solution:
    def dailyTemperatures(self, temperatures: List[int]) -> List[int]:
        """
        Pythonic approach using enumerate and list comprehensions.

        Key improvements:
        - enumerate() for clean index handling
        - List as natural stack
        - Clear variable names
        """
        result = [0] * len(temperatures)
        stack = []

        for i, temp in enumerate(reversed(temperatures)):
            actual_i = len(temperatures) - 1 - i

            # Clean stack maintenance
            while stack and temperatures[stack[-1]] <= temp:
                stack.pop()

            # Calculate result
            result[actual_i] = (stack[-1] - actual_i) if stack else 0
            stack.append(actual_i)

        return result
```

### Approach 2: Object-Oriented Design

```python
class MonotonicStack:
    """Reusable monotonic stack for next greater element problems"""

    def __init__(self, decreasing=True):
        self.stack = []
        self.decreasing = decreasing

    def should_pop(self, current, stack_top):
        """Determine if stack top should be popped"""
        if self.decreasing:
            return stack_top <= current
        else:
            return stack_top >= current

    def process_element(self, element):
        """Process a single element and return next greater"""
        while self.stack and self.should_pop(element, self.stack[-1]):
            self.stack.pop()

        result = self.stack[-1] if self.stack else -1
        self.stack.append(element)
        return result

class Solution:
    def nextGreaterElements(self, nums: List[int]) -> List[int]:
        """
        Object-oriented approach with reusable components.

        Benefits:
        - Reusable monotonic stack class
        - Clear separation of concerns
        - Easy to extend for variants
        """
        stack_processor = MonotonicStack(decreasing=True)
        n = len(nums)
        result = [0] * n

        # Handle circular array
        for i in range(2 * n - 1, -1, -1):
            idx = i % n
            result[idx] = stack_processor.process_element(nums[idx])

        return result
```

## 📚 Educational Philosophy: Pattern Mastery

### When to Use Each Approach

```tikz
\begin{tikzpicture}[
    approach_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4cm, align=center, minimum height=3cm},
    context_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=5cm, align=center}
]

\node[approach_box] (traditional) at (0, 3) {
    \textbf{Traditional Template}\\[0.5em]
    \textbf{When to use:}\\
    • Technical interviews\\
    • Learning the pattern\\
    • Performance-critical code\\
    • Teaching fundamentals\\[0.5em]
    \textbf{Benefits:}\\
    • Shows algorithm mastery\\
    • O(n) time, O(n) space\\
    • Universal applicability
};

\node[approach_box] (pythonic) at (8, 3) {
    \textbf{Pythonic Approaches}\\[0.5em]
    \textbf{When to use:}\\
    • Production code\\
    • Complex variations\\
    • Team collaboration\\
    • Maintainable systems\\[0.5em]
    \textbf{Benefits:}\\
    • Leverages Python features\\
    • More readable\\
    • Easier to extend
};

\node[context_box] at (4, 0.5) {
    \textbf{Educational Insight:}\\
    Understanding monotonic stack pattern helps you recognize\\
    optimization opportunities in many array problems\\[0.3em]
    \textit{Master the pattern, then choose the right implementation}
};

\end{tikzpicture}
```

### The Learning Journey

```tikz
\begin{tikzpicture}[
    stage_box/.style={rectangle, draw, rounded corners, fill=green!20, font=\sffamily\small, text width=3cm, align=center, minimum height=2cm},
    arrow/.style={->, thick, purple, line width=2pt}
]

\node[stage_box] (understand) at (0, 3) {
    \textbf{Stage 1:}\\
    \textbf{Understand}\\[0.5em]
    Recognize "next greater"\\
    pattern and stack\\
    invariant properties
};

\node[stage_box] (implement) at (4, 3) {
    \textbf{Stage 2:}\\
    \textbf{Implement}\\[0.5em]
    Master the template\\
    and handle variations\\
    (indices, circular arrays)
};

\node[stage_box] (optimize) at (8, 3) {
    \textbf{Stage 3:}\\
    \textbf{Optimize}\\[0.5em]
    Use Python idioms\\
    for elegant, maintainable\\
    solutions
};

\draw[arrow] (understand) -- (implement);
\draw[arrow] (implement) -- (optimize);

\node at (4, 1) {\bfseries The journey from pattern recognition to elegant implementation};

\end{tikzpicture}
```

## 🧠 Key Takeaways & Problem-Solving Framework

### Why This Pattern is Educational

```tikz
\begin{tikzpicture}[
    principle_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=1.8cm},
    arrow/.style={->, thick, green}
]

\node[principle_box] (recognition) at (0, 3) {
    \textbf{Pattern Recognition}\\[0.3em]
    Identify "next greater"\\
    type problems in\\
    various contexts
};

\node[principle_box] (optimization) at (4.5, 3) {
    \textbf{Stack Optimization}\\[0.3em]
    Transform O(n²) brute\\
    force to O(n) using\\
    monotonic stack
};

\node[principle_box] (variations) at (9, 3) {
    \textbf{Handle Variations}\\[0.3em]
    Adapt template for\\
    indices, circular arrays,\\
    and different comparisons
};

\draw[arrow] (recognition) -- (optimization);
\draw[arrow] (optimization) -- (variations);

\node at (4.5, 1.5) {\bfseries Core Learning: Recognize → Optimize → Adapt};

\end{tikzpicture}
```

### Problem-Solving Pattern: [[Monotonic Stack]]

This pattern exemplifies **stack-based optimization**:

1. **Pattern recognition**: Identify "next greater/smaller" requirements
2. **Stack invariant**: Maintain monotonic order for efficient lookups
3. **Template application**: Apply universal template with problem-specific modifications
4. **Variation handling**: Adapt for indices, circular arrays, or different comparisons

### Complexity Analysis
- **Time Complexity:** O(n) - each element pushed and popped at most once
- **Space Complexity:** O(n) - stack space in worst case

### Related Concepts for Obsidian

This pattern connects to several important concepts:

- **[[Stack Data Structure]]**: Core data structure for LIFO operations
- **[[Monotonic Sequences]]**: Maintaining order invariants
- **[[Array Optimization]]**: Converting O(n²) to O(n) solutions
- **[[Template Patterns]]**: Reusable algorithmic templates
- **[[Circular Arrays]]**: Handling wraparound with modulo arithmetic

### Related Problems
- **LC84. Largest Rectangle in Histogram**: Monotonic stack for area calculation
- **LC42. Trapping Rain Water**: Stack-based water trapping
- **LC85. Maximal Rectangle**: 2D extension of histogram problem
- **LC316. Remove Duplicate Letters**: Monotonic stack with constraints

### Implementation Tips

#### Pattern Recognition
- **"Next greater/smaller"**: Direct monotonic stack application
- **"Nearest larger/smaller"**: Variation with different scanning direction
- **"Count of elements"**: Store indices instead of values
- **"Until condition met"**: Calculate distances or counts

#### Template Variations
- **Decreasing stack**: For next greater elements (most common)
- **Increasing stack**: For next smaller elements
- **Index storage**: When you need positions, not just values
- **Circular handling**: Use modulo for wraparound behavior

#### Python-Specific Optimizations
- **List as stack**: Natural and efficient in Python
- **enumerate()**: Clean index handling
- **Comprehensions**: Concise result building
- **Object-oriented**: Reusable components for complex variations

### Edge Cases to Consider
- **Empty arrays**: Handle gracefully
- **Single element**: Trivial case
- **All increasing/decreasing**: Stack behavior at extremes
- **Duplicate elements**: Ensure correct <= vs < comparisons
- **Circular arrays**: Proper modulo arithmetic

This pattern beautifully demonstrates how **stack data structures** can transform seemingly complex problems into elegant O(n) solutions, showcasing the power of **maintaining invariants** and **template-based thinking** in algorithm design!