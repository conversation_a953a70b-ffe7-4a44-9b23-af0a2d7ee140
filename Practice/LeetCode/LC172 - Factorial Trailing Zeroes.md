---
tags: [problem/leetcode, lc/medium, topic/math, topic/number_theory, topic/factorial, course/labuladong]
aliases: [LC172, Factorial Trailing Zeroes]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 172. Factorial Trailing Zeroes
> Solution detailed in [[Interview/Concept/Algorithms/Mathematical Techniques/03 - Factorial Algorithm Problems|Factorial Algorithm Problems]].

# LeetCode 172: Factorial Trailing Zeroes

## Problem Statement
Given an integer `n`, return *the number of trailing zeroes in `n!`*.
Note that `n! = n * (n - 1) * (n - 2) * ... * 3 * 2 * 1`.

**Official Link:** [LeetCode 172. Factorial Trailing Zeroes](https://leetcode.com/problems/factorial-trailing-zeroes/)

## Solution Approach
The number of trailing zeros in $n!$ is determined by the number of factors of 5 in its prime factorization. This is because factors of 10 (which create zeros) require pairs of 2 and 5, and factors of 2 are always more abundant than factors of 5.
The number of factors of 5 is $\lfloor n/5 \rfloor + \lfloor n/25 \rfloor + \lfloor n/125 \rfloor + \dots$.

### Python Solution
```python
class Solution:
    def trailingZeroes(self, n: int) -> int:
        if n < 0: # Factorial not defined for negative numbers in this context
            return 0 # Or raise error based on constraints (LeetCode n >= 0)

        count = 0
        # Python integers have arbitrary precision, so power_of_5 won't overflow
        # easily, but n // power_of_5 handles large n naturally.
        power_of_5 = 5
        while n >= power_of_5:
            count += n // power_of_5
            # Safe multiplication check for fixed-size integers (less critical in Python):
            # if power_of_5 > n // 5: # power_of_5 * 5 would exceed n or overflow
            #     break
            power_of_5 *= 5
            if power_of_5 <= 0 : # Overflow for fixed int, or if power_of_5 became 0 somehow
                break
        return count

    # More concise Pythonic version often seen:
    def trailingZeroes_concise(self, n: int) -> int:
        count = 0
        while n > 0:
            n //= 5 # Integer division
            count += n
        return count
```

## Complexity Analysis
- **Time Complexity:** $O(\log_5 N)$. The loop runs as many times as `n` can be divided by 5.
- **Space Complexity:** $O(1)$.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
