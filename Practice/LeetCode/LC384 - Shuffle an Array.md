---
tags: [problem/leetcode, lc/medium, topic/array, topic/randomized_algorithms, algorithm/fisher_yates, course/labuladong]
aliases: [LC384, Shuffle an Array]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 384. Shuffle an Array
> Solution uses <PERSON><PERSON><PERSON> Shuffle, discussed in [[Interview/Concept/Algorithms/Randomized Algorithms/01 - Concepts in Game Random Algorithms|Concepts in Game Random Algorithms]].

# LeetCode 384: Shuffle an Array

## Problem Statement
Given an integer array `nums`, design an algorithm to randomly shuffle the array. All permutations of the array should be **equally likely** as a result of the shuffling.

Implement the `Solution` class:
- `Solution(int[] nums)` Initializes the object with the integer array `nums`.
- `int[] reset()` Resets the array to its original configuration and returns it.
- `int[] shuffle()` Returns a randomly shuffled Vof the array.

**Official Link:** [LeetCode 384. Shuffle an Array](https://leetcode.com/problems/shuffle-an-array/)

## Solution Approach: <PERSON><PERSON><PERSON> Shuffle

The <PERSON>-Yates shuffle algorithm is the standard way to generate a random permutation of a finite sequence.

**Algorithm for `shuffle()`:**
1.  Create a copy of the original array to avoid modifying it (as `reset()` needs the original).
2.  Iterate from the last element of the copy down to the second element (index `i` from `n-1` down to `1`):
    a.  Generate a random integer `j` such that `0 <= j <= i`.
    b.  Swap the element at index `i` with the element at index `j` in the copied array.
3.  Return the shuffled copy.

### Python Solution
```python
import random

class Solution:
    def __init__(self, nums: list[int]):
        self.original_nums = list(nums) # Store a copy of the original array for reset
        self.current_nums = list(nums)  # Array to be shuffled

    def reset(self) -> list[int]:
        # Reset current_nums to the original configuration
        self.current_nums = list(self.original_nums)
        return self.current_nums

    def shuffle(self) -> list[int]:
        n = len(self.current_nums)
        # Create a new list or shuffle self.current_nums in-place for this call
        # LeetCode problem expects shuffle to return a *new* shuffled array *each time*
        # and not necessarily modify the internal state used by reset,
        # or rather, `shuffle` can modify `self.current_nums` then return it.
        # Let's shuffle a copy to be safe or shuffle `self.current_nums` (which is already a copy)

        shuffled_arr = list(self.current_nums) # Work on a copy if we want self.current_nums to represent last shuffle
                                            # or just use self.current_nums which is already a copy of original.
                                            # The problem implies current_nums is the array we are shuffling for output.

        for i in range(n - 1, 0, -1): # Iterate from n-1 down to 1
            # Pick a random index j from 0 to i (inclusive)
            j = random.randint(0, i)
            # Swap element at i with element at j
            shuffled_arr[i], shuffled_arr[j] = shuffled_arr[j], shuffled_arr[i]

        # If shuffle is meant to modify internal state for next shuffle:
        # self.current_nums = shuffled_arr 
        return shuffled_arr # Return the shuffled array

# Your Solution object will be instantiated and called as such:
# obj = Solution(nums)
# param_1 = obj.reset()
# param_2 = obj.shuffle()
```

## Complexity Analysis
- **Constructor (`__init__`)**: $O(N)$ to copy the array.
- **`reset()`**: $O(N)$ to copy the original array back.
- **`shuffle()`**: $O(N)$ for the Fisher-Yates shuffle algorithm.
- **Space Complexity**: $O(N)$ to store `original_nums` and `current_nums`.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
