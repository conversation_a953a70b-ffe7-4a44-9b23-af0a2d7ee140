---
tags: [problem/leetcode, lc/medium, topic/array, topic/backtracking, pattern/permutations]
aliases: [LC46, LeetCode 46. Permutations]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 46. Permutations
> Solution based on the backtracking framework discussed in [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking - Core Framework]] and [[Interview/Concept/Algorithms/Backtracking/Patterns/01 - Backtracking for Permutations, Combinations, Subsets|Backtracking for Permutations, Combinations, Subsets]].

# LeetCode 46: Permutations

## Problem Statement

Given an array `nums` of distinct integers, return all the possible permutations. You can return the answer in any order.

**Official Link:** [LeetCode 46. Permutations](https://leetcode.com/problems/permutations/)

**Example 1:**
Input: `nums = [1,2,3]`
Output: `[[1,2,3],[1,3,2],[2,1,3],[2,3,1],[3,1,2],[3,2,1]]`

## Solution Approach: Backtracking

This is a classic permutation problem solvable using backtracking. The core idea is to build permutations step-by-step. At each step, we select an unused number from `nums` and add it to the current permutation path.

### Decision Tree for `nums = [1,2,3]`

![](/algo/images/backtracking/1.jpg)
*(Source: Labuladong. Each path from root to leaf represents a complete permutation.)*

- **Path (`track`):** The current sequence of numbers chosen for the permutation.
- **Choices List:** Numbers in `nums` that are not yet in `track` (managed by a `used` boolean array).
- **End Condition:** When `len(track) == len(nums)`, a full permutation is formed.

### Python Solution

```python
class Solution:
    def permute(self, nums: list[int]) -> list[list[int]]:
        results = []
        current_permutation = []
        # 'used' array tracks if nums[i] is already in current_permutation
        used = [False] * len(nums)

        def backtrack():
            # Base case: if the current permutation is complete
            if len(current_permutation) == len(nums):
                results.append(current_permutation.copy()) # Add a copy
                return

            # Iterate through all numbers in nums to make a choice
            for i in range(len(nums)):
                # If the number is already used in the current permutation, skip it
                if used[i]:
                    continue
                
                # Make choice: add nums[i] to current_permutation
                current_permutation.append(nums[i])
                used[i] = True
                
                # Recurse to build the rest of the permutation
                backtrack()
                
                # Undo choice: remove nums[i] to explore other possibilities
                used[i] = False
                current_permutation.pop()
        
        backtrack()
        return results

```
This solution directly applies the [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|backtracking framework]].

## Complexity Analysis
-   **Time Complexity:** $O(N \times N!)$.
    - There are $N!$ permutations.
    - For each permutation, it takes $O(N)$ to copy `current_permutation` to `results`.
    - The decision tree has roughly $N!$ leaf nodes, and the work to reach them involves $N$ levels of choices. The total number of nodes in the permutation tree is about $N \times N!$ or $\sum_{k=0}^{N} P(N,k)$, which is $O(N \cdot N!)$.
-   **Space Complexity:** $O(N)$.
    - The recursion stack can go up to depth $N$.
    - `current_permutation` stores up to $N$ elements.
    - `used` array takes $O(N)$ space.
    - `results` stores $N!$ permutations each of length $N$, so $O(N \cdot N!)$ for the output, but this is often excluded from auxiliary space complexity analysis. If considering auxiliary space for computation, it's $O(N)$.

## Visualization of Backtracking Steps (Conceptual)

For `nums = [1,2]`:
1.  `backtrack()`:
    - `i = 0` (choose `nums[0]=1`):
        - `current_permutation = [1]`, `used = [T,F]`
        - `backtrack()`:
            - `i = 0` (skip, `used[0]` is True)
            - `i = 1` (choose `nums[1]=2`):
                - `current_permutation = [1,2]`, `used = [T,T]`
                - `backtrack()`:
                    - `len(current_permutation) == len(nums)` is True. `results.append([1,2])`. Return.
                - `current_permutation.pop()` (becomes `[1]`), `used[1]=F`
        - `current_permutation.pop()` (becomes `[]`), `used[0]=F`
    - `i = 1` (choose `nums[1]=2`):
        - `current_permutation = [2]`, `used = [F,T]`
        - `backtrack()`:
            - `i = 0` (choose `nums[0]=1`):
                - `current_permutation = [2,1]`, `used = [T,T]`
                - `backtrack()`:
                    - `len(current_permutation) == len(nums)` is True. `results.append([2,1])`. Return.
                - `current_permutation.pop()` (becomes `[2]`), `used[0]=F`
            - `i = 1` (skip, `used[1]` is True)
        - `current_permutation.pop()` (becomes `[]`), `used[1]=F`

Final `results = [[1,2], [2,1]]`.

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=8mm, font=\sffamily\small},
    leafnode/.style={treenode, fill=green!20, double},
    edge_label/.style={font=\sffamily\tiny, sloped, midway, above},
    level 1/.style={sibling distance=3cm, level distance=1.5cm},
    level 2/.style={sibling distance=2cm, level distance=1.5cm}
]

\node[treenode] (root) {$\emptyset$};
\node at (root.north) [above=1mm] {Path: []};

\node[treenode] (n1) at (-2,-2) {1}
    child {node[leafnode] (n12) {2} edge from parent node[edge_label] {choose 2}}
    edge from parent node[edge_label] {choose 1};
\node at (n1.north) [above=1mm] {Path: [1]};
\node at (n12.north) [above=1mm] {Path: [1,2]};


\node[treenode] (n2) at (2,-2) {2}
    child {node[leafnode] (n21) {1} edge from parent node[edge_label] {choose 1}}
    edge from parent node[edge_label] {choose 2};
\node at (n2.north) [above=1mm] {Path: [2]};
\node at (n21.north) [above=1mm] {Path: [2,1]};

\path[->] (root) edge (n1);
\path[->] (root) edge (n2);

\end{tikzpicture}
```

## 总结 (Summary)
- LC46 Permutations is a standard backtracking problem.
- The solution involves exploring all possible orderings of distinct elements.
- A `used` array is essential to ensure each element is picked only once per permutation.
- The time complexity is factorial due to the nature of permutations.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Previous: [[Interview/Practice/LeetCode/LC2073 - Time Needed to Buy Tickets|LC2073 - Time Needed to Buy Tickets]] (Example - order may vary)
Next: [[Interview/Practice/LeetCode/LC47 - Permutations II|LC47 - Permutations II]] (Placeholder - logical next problem)
Concepts: [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking Framework]], [[Interview/Concept/Algorithms/Backtracking/Patterns/01 - Backtracking for Permutations, Combinations, Subsets|Permutations Pattern]]
