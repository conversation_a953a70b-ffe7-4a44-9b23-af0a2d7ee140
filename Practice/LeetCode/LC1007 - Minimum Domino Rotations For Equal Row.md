---
tags: [problem/leetcode, lc/medium, topic/greedy, topic/array, pattern/case_analysis, pattern/constraint_satisfaction]
aliases: [LC1007, LeetCode 1007, Minimum Domino Rotations For Equal Row, 行相等的最少多米诺旋转]
---

# LeetCode 1007: Minimum Domino Rotations For Equal Row

## Problem Statement

In a row of dominoes, `tops[i]` and `bottoms[i]` represent the top and bottom halves of the ith domino. (A domino is a tile with two numbers from 1 to 6 - one on each half of the tile.)

We may rotate the ith domino, so that `tops[i]` and `bottoms[i]` swap values.

Return the minimum number of rotations so that all the values in `tops` are the same, or all the values in `bottoms` are the same.

If it cannot be done, return `-1`.

**Official Link:** [LeetCode 1007. Minimum Domino Rotations For Equal Row](https://leetcode.com/problems/minimum-domino-rotations-for-equal-row/)

## 🎲 Understanding the Domino Problem

Think of this as arranging physical dominoes: each domino has two faces, and you can flip any domino to show either face on top.

```tikz
\begin{tikzpicture}[
    domino/.style={rectangle, draw, minimum width=1cm, minimum height=2cm, font=\sffamily\bfseries},
    top_half/.style={fill=blue!30},
    bottom_half/.style={fill=red!30},
    arrow/.style={->, thick, green},
    goal/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=4cm, align=center}
]

% Original configuration
\node at (3, 5) {\bfseries Original Configuration};
\node at (0, 4.5) {\small tops:};
\node at (0, 3.5) {\small bottoms:};

% Dominoes
\foreach \i/\t/\b in {0/2/1, 1/2/3, 2/3/2, 3/1/2, 4/2/4, 5/2/2} {
    \node[domino, top_half] at (\i*1.2+1, 4.5) {\t};
    \node[domino, bottom_half] at (\i*1.2+1, 3.5) {\b};
}

% Show rotation example
\draw[arrow] (2.2, 3) -- (2.2, 2.5);
\node at (2.2, 2.2) {\tiny Rotate};

% After rotation
\node at (3, 1.5) {\bfseries After Rotating Domino 2};
\foreach \i/\t/\b in {0/2/1, 1/2/3, 2/2/3, 3/1/2, 4/2/4, 5/2/2} {
    \node[domino, top_half] at (\i*1.2+1, 1) {\t};
    \node[domino, bottom_half] at (\i*1.2+1, 0) {\b};
}

% Goal explanation
\node[goal] at (8.5, 2.5) {
    \textbf{Goal:}\\
    Make all tops the same\\
    OR\\
    Make all bottoms the same\\
    \\
    Find minimum rotations!
};

\end{tikzpicture}
```

**Example Analysis:**
- **Input:** `tops = [2,1,2,4,2,2]`, `bottoms = [5,2,6,2,3,2]`
- **Goal:** Make one row uniform (all same number)
- **Key Insight:** Only certain numbers can possibly work!

## 🧠 The Critical Insight: Candidate Analysis

The breakthrough insight is that **only a few numbers can possibly work**:

```tikz
\begin{tikzpicture}[
    insight_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2cm},
    arrow/.style={->, thick, red, line width=2pt},
    candidate_box/.style={rectangle, draw, fill=orange!20, font=\sffamily\small, text width=5cm, align=center}
]

\node[insight_box] (observation) at (0, 2) {
    \textbf{Key Observation}\\[0.5em]
    If we can make all tops\\
    value X, then X must\\
    appear in EVERY domino\\
    (either top or bottom)
};

\node[insight_box] (candidates) at (8, 2) {
    \textbf{Limited Candidates}\\[0.5em]
    Only tops[0] and bottoms[0]\\
    can possibly work!\\
    \\
    Why? They must appear\\
    in position 0!
};

\draw[arrow] (observation) -- (candidates);

\node[candidate_box] at (4, -0.5) {
    \textbf{Algorithm Strategy:}\\
    1. Try making all tops = tops[0]\\
    2. Try making all tops = bottoms[0]\\
    3. Try making all bottoms = tops[0]\\
    4. Try making all bottoms = bottoms[0]\\
    5. Return minimum rotations needed
};

\end{tikzpicture}
```

**Why This Works:**
- If target value X can make a uniform row, X must exist in every domino
- Since X must exist in domino 0, X ∈ {tops[0], bottoms[0]}
- This reduces our search space from 6 possibilities to just 2!

## 🔍 Case Analysis Framework

Let's systematically analyze what rotations are needed:

```tikz
\begin{tikzpicture}[
    case_box/.style={rectangle, draw, rounded corners, fill=green!20, font=\sffamily\small, text width=3cm, align=center, minimum height=2.5cm},
    formula_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\tiny, text width=2.5cm, align=center}
]

% Case 1: Make tops uniform
\node[case_box] (case1) at (0, 3) {
    \textbf{Case 1:}\\
    Make all tops = X\\[0.5em]
    For each domino i:\\
    • If tops[i] = X: no rotation\\
    • If bottoms[i] = X: rotate\\
    • If neither = X: impossible
};

% Case 2: Make bottoms uniform
\node[case_box] (case2) at (4, 3) {
    \textbf{Case 2:}\\
    Make all bottoms = X\\[0.5em]
    For each domino i:\\
    • If bottoms[i] = X: no rotation\\
    • If tops[i] = X: rotate\\
    • If neither = X: impossible
};

% Formulas
\node[formula_box] at (0, 0.5) {
    Rotations for tops = X:\\
    Count where\\
    tops[i] ≠ X and\\
    bottoms[i] = X
};

\node[formula_box] at (4, 0.5) {
    Rotations for bottoms = X:\\
    Count where\\
    bottoms[i] ≠ X and\\
    tops[i] = X
};

\end{tikzpicture}
```

## 💡 Step-by-Step Algorithm

```python
def minDominoRotations(self, tops: List[int], bottoms: List[int]) -> int:
    def check_rotations(target, target_row, source_row):
        """Count rotations needed to make target_row all equal to target"""
        rotations = 0
        for i in range(len(tops)):
            if target_row[i] != target:
                if source_row[i] == target:
                    rotations += 1  # Need to rotate
                else:
                    return -1  # Impossible
        return rotations

    # Try all 4 possibilities
    candidates = [
        check_rotations(tops[0], tops, bottoms),    # Make tops = tops[0]
        check_rotations(bottoms[0], tops, bottoms), # Make tops = bottoms[0]
        check_rotations(tops[0], bottoms, tops),    # Make bottoms = tops[0]
        check_rotations(bottoms[0], bottoms, tops)  # Make bottoms = bottoms[0]
    ]

    # Filter out impossible cases and return minimum
    valid_candidates = [c for c in candidates if c != -1]
    return min(valid_candidates) if valid_candidates else -1
```

## 🔍 Visual Algorithm Trace

Let's trace through `tops = [2,1,2,4,2,2]`, `bottoms = [5,2,6,2,3,2]`:

```tikz
\begin{tikzpicture}[
    domino/.style={rectangle, draw, minimum width=0.8cm, minimum height=0.6cm, font=\tiny},
    top_val/.style={domino, fill=blue!30},
    bottom_val/.style={domino, fill=red!30},
    highlight/.style={domino, fill=yellow!50},
    impossible/.style={domino, fill=gray!30},
    case_label/.style={font=\sffamily\bfseries\small},
    result_box/.style={rectangle, draw, fill=green!20, font=\tiny, text width=2.5cm, align=center}
]

% Original arrays
\node[case_label] at (3, 6) {Original Configuration};
\node at (-0.5, 5.5) {\tiny tops:};
\node at (-0.5, 5) {\tiny bottoms:};

\foreach \i/\t/\b in {0/2/5, 1/1/2, 2/2/6, 3/4/2, 4/2/3, 5/2/2} {
    \node[top_val] at (\i*1+0.5, 5.5) {\t};
    \node[bottom_val] at (\i*1+0.5, 5) {\b};
}

% Candidates
\node[case_label] at (3, 4) {Candidates: tops[0]=2, bottoms[0]=5};

% Case 1: Make tops = 2
\node[case_label] at (3, 3) {Case 1: Make all tops = 2};
\node at (-0.5, 2.5) {\tiny tops:};
\node at (-0.5, 2) {\tiny bottoms:};

\foreach \i/\t/\b/\action in {0/2/5/ok, 1/1/2/rotate, 2/2/6/ok, 3/4/2/rotate, 4/2/3/ok, 5/2/2/ok} {
    \ifnum\i=1
        \node[highlight] at (\i*1+0.5, 2.5) {\t};
        \node[highlight] at (\i*1+0.5, 2) {\b};
    \else\ifnum\i=3
        \node[highlight] at (\i*1+0.5, 2.5) {\t};
        \node[highlight] at (\i*1+0.5, 2) {\b};
    \else
        \node[top_val] at (\i*1+0.5, 2.5) {\t};
        \node[bottom_val] at (\i*1+0.5, 2) {\b};
    \fi
}

\node[result_box] at (7.5, 2.25) {
    Rotations needed: 2\\
    (positions 1 and 3)\\
    ✓ Possible
};

% Case 2: Make tops = 5
\node[case_label] at (3, 1) {Case 2: Make all tops = 5};
\node at (-0.5, 0.5) {\tiny tops:};
\node at (-0.5, 0) {\tiny bottoms:};

\foreach \i/\t/\b in {0/2/5, 1/1/2, 2/2/6, 3/4/2, 4/2/3, 5/2/2} {
    \ifnum\i=1
        \node[impossible] at (\i*1+0.5, 0.5) {\t};
        \node[impossible] at (\i*1+0.5, 0) {\b};
    \else\ifnum\i=2
        \node[impossible] at (\i*1+0.5, 0.5) {\t};
        \node[impossible] at (\i*1+0.5, 0) {\b};
    \else\ifnum\i=4
        \node[impossible] at (\i*1+0.5, 0.5) {\t};
        \node[impossible] at (\i*1+0.5, 0) {\b};
    \else\ifnum\i=5
        \node[impossible] at (\i*1+0.5, 0.5) {\t};
        \node[impossible] at (\i*1+0.5, 0) {\b};
    \else
        \node[top_val] at (\i*1+0.5, 0.5) {\t};
        \node[bottom_val] at (\i*1+0.5, 0) {\b};
    \fi
}

\node[result_box, fill=red!20] at (7.5, 0.25) {
    5 not in positions\\
    1, 2, 4, 5\\
    ✗ Impossible
};

% Case 3: Make bottoms = 2
\node[case_label] at (3, -1) {Case 3: Make all bottoms = 2};
\node at (-0.5, -1.5) {\tiny tops:};
\node at (-0.5, -2) {\tiny bottoms:};

\foreach \i/\t/\b in {0/2/5, 1/1/2, 2/2/6, 3/4/2, 4/2/3, 5/2/2} {
    \ifnum\i=0
        \node[highlight] at (\i*1+0.5, -1.5) {\t};
        \node[highlight] at (\i*1+0.5, -2) {\b};
    \else\ifnum\i=2
        \node[highlight] at (\i*1+0.5, -1.5) {\t};
        \node[highlight] at (\i*1+0.5, -2) {\b};
    \else\ifnum\i=4
        \node[highlight] at (\i*1+0.5, -1.5) {\t};
        \node[highlight] at (\i*1+0.5, -2) {\b};
    \else
        \node[top_val] at (\i*1+0.5, -1.5) {\t};
        \node[bottom_val] at (\i*1+0.5, -2) {\b};
    \fi
}

\node[result_box] at (7.5, -1.75) {
    Rotations needed: 3\\
    (positions 0, 2, 4)\\
    ✓ Possible
};

% Final result
\node[result_box, fill=yellow!30] at (3, -3.5) {
    \textbf{Final Answer: 2}\\
    (minimum of valid cases)
};

\end{tikzpicture}
```

**Trace Summary:**
1. **Candidates:** Only 2 and 5 (from position 0) can work
2. **Case 1 (tops=2):** Need 2 rotations ✓
3. **Case 2 (tops=5):** Impossible (5 missing in several positions) ✗
4. **Case 3 (bottoms=2):** Need 3 rotations ✓
5. **Case 4 (bottoms=5):** Impossible ✗
6. **Result:** min(2, 3) = 2

## 🚀 Complete Solution

```python
class Solution:
    def minDominoRotations(self, tops: List[int], bottoms: List[int]) -> int:
        def count_rotations(target, target_row, source_row):
            """Count rotations needed to make target_row uniform with target value"""
            rotations = 0
            for i in range(len(tops)):
                if target_row[i] != target:
                    if source_row[i] == target:
                        rotations += 1  # Need to rotate this domino
                    else:
                        return -1  # Impossible - target not available
            return rotations

        # Only tops[0] and bottoms[0] can be valid targets
        candidates = [
            count_rotations(tops[0], tops, bottoms),      # Make all tops = tops[0]
            count_rotations(bottoms[0], tops, bottoms),   # Make all tops = bottoms[0]
            count_rotations(tops[0], bottoms, tops),      # Make all bottoms = tops[0]
            count_rotations(bottoms[0], bottoms, tops)    # Make all bottoms = bottoms[0]
        ]

        # Return minimum valid rotation count
        valid = [c for c in candidates if c != -1]
        return min(valid) if valid else -1
```

## ⚡ Optimized Solution

We can optimize by recognizing that we only need to check 2 candidates:

```python
class Solution:
    def minDominoRotations(self, tops: List[int], bottoms: List[int]) -> int:
        def min_rotations_for_target(target):
            """Find minimum rotations to make either row uniform with target"""
            top_rotations = bottom_rotations = 0

            for i in range(len(tops)):
                if tops[i] != target and bottoms[i] != target:
                    return -1  # Impossible
                elif tops[i] != target:
                    top_rotations += 1     # Need rotation to fix tops
                elif bottoms[i] != target:
                    bottom_rotations += 1  # Need rotation to fix bottoms

            return min(top_rotations, bottom_rotations)

        # Try both possible targets
        for target in [tops[0], bottoms[0]]:
            result = min_rotations_for_target(target)
            if result != -1:
                return result

        return -1
```

## 🧠 Key Takeaways & Problem-Solving Framework

### Why This Problem is Educational

```tikz
\begin{tikzpicture}[
    concept_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=1.8cm},
    arrow/.style={->, thick, green}
]

\node[concept_box] (insight) at (0, 3) {
    \textbf{Constraint Analysis}\\[0.3em]
    Identify what values\\
    can possibly work\\
    (candidate reduction)
};

\node[concept_box] (cases) at (4.5, 3) {
    \textbf{Case Enumeration}\\[0.3em]
    Systematically check\\
    all valid scenarios\\
    (exhaustive search)
};

\node[concept_box] (optimization) at (9, 3) {
    \textbf{Greedy Choice}\\[0.3em]
    For each target, choose\\
    the better of two options\\
    (local optimization)
};

\draw[arrow] (insight) -- (cases);
\draw[arrow] (cases) -- (optimization);

\node at (4.5, 1.5) {\bfseries Core Learning: Constrain → Enumerate → Optimize};

\end{tikzpicture}
```

### Problem-Solving Pattern: [[Constraint Satisfaction]]

This problem exemplifies the **constraint satisfaction** pattern:

1. **Identify constraints**: Target must appear in every domino
2. **Reduce search space**: Only 2 possible targets
3. **Enumerate cases**: Check all valid configurations
4. **Optimize locally**: Choose best option for each target

### Complexity Analysis
- **Time Complexity:** O(n) - single pass through arrays
- **Space Complexity:** O(1) - only using constant extra space

### Related Concepts for Obsidian

This problem connects to several important algorithmic concepts:

- **[[Greedy Algorithms]]**: Choose locally optimal rotations
- **[[Case Analysis]]**: Systematic enumeration of possibilities
- **[[Constraint Satisfaction]]**: Working within problem constraints
- **[[Array Manipulation]]**: Processing paired array elements
- **[[Optimization Problems]]**: Finding minimum cost solution

### Related Problems
- **LC1460. Make Two Arrays Equal by Reversing Sub-arrays**
- **LC1888. Minimum Number of Flips to Make Binary String Alternating**
- **LC1769. Minimum Number of Operations to Move All Balls to Each Box**

This problem beautifully demonstrates how **constraint analysis** can dramatically reduce the search space and lead to elegant solutions!
