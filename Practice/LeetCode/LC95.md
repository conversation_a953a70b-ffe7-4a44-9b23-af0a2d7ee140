---
tags: [problem/leetcode, lc/placeholder, topic/unknown, pattern/unknown, course/labuladong_mention]
aliases: [LC95, LeetC<PERSON> 95]
---
> [!NOTE] Source Annotation
> Problem: LC95 - Unique Binary Search Trees II
> This is a placeholder note. Solution details to be added.
> Mentioned in Labuladong's notes related to [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Tree DP/00 - Unique Binary Search Trees (LC95 LC96)|Unique BSTs DP]].

# LC95 - Unique Binary Search Trees II

## Problem Statement
(To be filled from LeetCode - Visit [https://leetcode.com/problems/unique-binary-search-trees-ii/](https://leetcode.com/problems/unique-binary-search-trees-ii/))

## Solution Approach
(To be filled)

### Python Solution (Placeholder)
```python
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None): self.val = val; self.left = left; self.right = right
# class ListNode:
#     def __init__(self, val=0, next=None): self.val = val; self.next = next
class Solution:
    def solve(self, params): # Signature will vary based on problem
        # TODO: Implement solution
        pass
```

## Complexity Analysis
(To be filled)

## 总结 (Summary)
(To be filled)

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Tree DP/00 - Unique Binary Search Trees (LC95 LC96)|Unique BSTs DP]]
