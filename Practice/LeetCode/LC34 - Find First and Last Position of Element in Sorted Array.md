---
tags: [problem/leetcode, lc/medium, topic/array, topic/searching, pattern/binary_search, pattern/binary_search_bounds]
aliases: [LC34, LeetCode 34. Find First and Last Position, 在排序数组中查找元素的第一个和最后一个位置]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 34. Find First and Last Position of Element in Sorted Array
> Solution uses left-bound and right-bound binary search variations from [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary Search Core Template]] (Labuladong's notes).

# LeetCode 34: Find First and Last Position of Element in Sorted Array

## Problem Statement

Given an array of integers `nums` sorted in non-decreasing order, find the starting and ending position of a given `target` value.
If `target` is not found in the array, return `[-1, -1]`.
You must write an algorithm with $O(\log n)$ runtime complexity.

**Official Link:** [LeetCode 34. Find First and Last Position of Element in Sorted Array](https://leetcode.com/problems/find-first-and-last-position-of-element-in-sorted-array/)

## Solution Approach: Left-Bound and Right-Bound Binary Search

To find the first and last positions of `target`, we can perform two separate binary searches:
1.  **Find Left Bound:** Use a modified binary search to find the first occurrence of `target`. This is the "left_bound" search.
2.  **Find Right Bound:** Use another modified binary search to find the last occurrence of `target`. This is the "right_bound" search.

If the left bound search doesn't find `target` (e.g., returns -1 or an index where `nums[index] != target`), then `target` is not in the array, and we can return `[-1, -1]`.

### Python Solution (Using Labuladong's Unified Templates)
```python
class Solution:
    def searchRange(self, nums: list[int], target: int) -> list[int]:
        left_idx = self._find_left_bound(nums, target)
        right_idx = self._find_right_bound(nums, target)

        return [left_idx, right_idx]

    def _find_left_bound(self, nums: list[int], target: int) -> int:
        left, right = 0, len(nums) - 1
        # Target for left_bound search is the index of the first element == target
        # Or, if target not present, -1.

        while left <= right:
            mid = left + (right - left) // 2
            if nums[mid] < target:
                left = mid + 1
            elif nums[mid] > target:
                right = mid - 1
            elif nums[mid] == target:
                # Found target, but it might not be the leftmost.
                # Continue searching in the left part.
                right = mid - 1 

        # After loop, `left` is the insertion point or the first occurrence.
        # Check if `left` is a valid index and if nums[left] is actually the target.
        if left < len(nums) and nums[left] == target:
            return left
        return -1

    def _find_right_bound(self, nums: list[int], target: int) -> int:
        left, right = 0, len(nums) - 1
        # Target for right_bound search is the index of the last element == target

        while left <= right:
            mid = left + (right - left) // 2
            if nums[mid] < target:
                left = mid + 1
            elif nums[mid] > target:
                right = mid - 1
            elif nums[mid] == target:
                # Found target, but it might not be the rightmost.
                # Continue searching in the right part.
                left = mid + 1

        # After loop, `right` is the candidate for the rightmost occurrence.
        # (Because when nums[mid]==target, `left` was pushed past `mid`. So `right` is `left-1`.)
        # Check if `right` is a valid index and if nums[right] is actually the target.
        if right >= 0 and nums[right] == target:
            return right
        return -1

```

## Complexity Analysis
- **Time Complexity:** $O(\log N)$ for each bound search. Total $O(\log N) + O(\log N) = O(\log N)$.
- **Space Complexity:** $O(1)$, as only a few variables are used.

## 总结 (Summary)
- LC34 combines two variations of binary search: one to find the leftmost occurrence (left bound) and one to find the rightmost occurrence (right bound) of the target.
- Each bound search adheres to the $O(\log N)$ complexity, making the overall solution efficient.
- Careful handling of loop termination conditions and final checks for bounds is crucial.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary Search Core Template]] (Left-bound and Right-bound variations)
