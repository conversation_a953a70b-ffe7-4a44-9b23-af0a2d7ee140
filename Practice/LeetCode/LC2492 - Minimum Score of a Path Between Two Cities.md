---
tags: [problem/leetcode, lc/medium, topic/graph, topic/union_find, pattern/connected_components, pattern/graph_traversal]
aliases: [LC2492, LeetCode 2492, Minimum Score of a Path Between Two Cities, 两个城市间路径的最小分数]
---

# LeetCode 2492: Minimum Score of a Path Between Two Cities

## Problem Statement

You are given a positive integer `n` representing `n` cities numbered from 1 to `n`. You are also given a 2D array `roads` where `roads[i] = [ai, bi, distancei]` indicates that there is a bidirectional road between cities `ai` and `bi` with a distance equal to `distancei`.

The **score** of a path between two cities is defined as the **minimum distance** of a road in this path.

Return the **minimum possible score** of a path between cities 1 and `n`.

**Official Link:** [LeetCode 2492. Minimum Score of a Path Between Two Cities](https://leetcode.com/problems/minimum-score-of-a-path-between-two-cities/)

## 🌆 Understanding the Problem

Think of this as finding the "weakest link" in any possible route between two cities:

```tikz
\begin{tikzpicture}[
    city/.style={circle, draw, minimum size=1cm, font=\sffamily\bfseries, fill=blue!30},
    road/.style={thick, font=\sffamily\small},
    highlight/.style={very thick, red},
    score_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=4cm, align=center}
]

% Example 1 graph
\node at (4, 5) {\bfseries Example 1: Finding the Minimum Score};

\node[city] (c1) at (0, 3) {1};
\node[city] (c2) at (3, 4) {2};
\node[city] (c3) at (6, 4) {3};
\node[city] (c4) at (3, 2) {4};

% Roads with distances
\draw[road] (c1) -- (c2) node[midway, above] {9};
\draw[road] (c2) -- (c3) node[midway, above] {6};
\draw[road] (c2) -- (c4) node[midway, right] {5};
\draw[road] (c1) -- (c4) node[midway, below] {7};

% Highlight optimal path
\draw[highlight] (c1) -- (c2);
\draw[highlight] (c2) -- (c4);

\node[score_box] at (8.5, 3) {
    \textbf{Path 1→2→4:}\\
    Roads: [9, 5]\\
    Score = min(9, 5) = 5\\[0.5em]
    \textbf{Key Insight:}\\
    Score = weakest road\\
    in the path!
};

\end{tikzpicture}
```

**Critical Insight:** The score is the **minimum edge weight** in any path, not the sum!

## 🧠 The Breakthrough Insight: Connected Components

The key realization transforms this from a complex path problem to a simple graph connectivity problem:

```tikz
\begin{tikzpicture}[
    insight_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4.5cm, align=center, minimum height=2.5cm},
    arrow/.style={->, thick, red, line width=2pt},
    conclusion_box/.style={rectangle, draw, fill=orange!20, font=\sffamily\small, text width=6cm, align=center}
]

\node[insight_box] (problem) at (0, 3) {
    \textbf{Original Thinking}\\[0.5em]
    "Find the best path\\
    from 1 to n that\\
    maximizes the minimum\\
    edge weight"\\[0.5em]
    Complex path search!
};

\node[insight_box] (insight) at (8, 3) {
    \textbf{Key Insight}\\[0.5em]
    "If 1 and n are connected,\\
    we can use ANY edge\\
    in their component\\
    by revisiting cities"\\[0.5em]
    Simple connectivity!
};

\draw[arrow] (problem) -- (insight);

\node[conclusion_box] at (4, 0.5) {
    \textbf{Algorithm Strategy:}\\
    1. Find all cities connected to city 1\\
    2. Among all edges in this connected component\\
    3. Return the minimum edge weight\\
    \\
    No path finding needed!
};

\end{tikzpicture}
```

**Why This Works:**
- We can revisit cities and reuse roads multiple times
- If cities 1 and n are connected, we can reach any edge in their component
- The minimum edge in the component gives us the best possible score

## 🔍 Problem Transformation

Let's see how the problem transforms with this insight:

```tikz
\begin{tikzpicture}[
    approach_box/.style={rectangle, draw, rounded corners, fill=green!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2cm},
    vs_label/.style={font=\sffamily\bfseries\large, red}
]

\node[approach_box] (complex) at (0, 2) {
    \textbf{Complex Approach}\\[0.5em]
    • Find all paths 1→n\\
    • Calculate score for each\\
    • Return minimum score\\[0.5em]
    Time: Exponential\\
    Space: Exponential
};

\node[vs_label] at (4.5, 2) {VS};

\node[approach_box] (simple) at (9, 2) {
    \textbf{Simple Approach}\\[0.5em]
    • Find connected component\\
    • Scan all edges in component\\
    • Return minimum weight\\[0.5em]
    Time: O(E)\\
    Space: O(V)
};

\end{tikzpicture}
```

This transformation is a perfect example of **[[Problem Reframing]]** - changing how we think about the problem to find a simpler solution.

## 💡 Solution Approaches

### Approach 1: Union-Find ([[Disjoint Set Union]])

```python
class Solution:
    def minScore(self, n: int, roads: List[List[int]]) -> int:
        # Union-Find to group connected cities
        parent = list(range(n + 1))

        def find(x):
            if parent[x] != x:
                parent[x] = find(parent[x])
            return parent[x]

        def union(x, y):
            px, py = find(x), find(y)
            if px != py:
                parent[px] = py

        # Build connected components
        for a, b, dist in roads:
            union(a, b)

        # Find minimum edge in component containing city 1
        target_component = find(1)
        min_score = float('inf')

        for a, b, dist in roads:
            if find(a) == target_component:
                min_score = min(min_score, dist)

        return min_score
```

### Approach 2: DFS/BFS ([[Graph Traversal]])

```python
class Solution:
    def minScore(self, n: int, roads: List[List[int]]) -> int:
        # Build adjacency list
        graph = defaultdict(list)
        for a, b, dist in roads:
            graph[a].append((b, dist))
            graph[b].append((a, dist))

        # DFS to find all reachable cities from city 1
        visited = set()
        min_score = float('inf')

        def dfs(city):
            nonlocal min_score
            if city in visited:
                return
            visited.add(city)

            for neighbor, dist in graph[city]:
                min_score = min(min_score, dist)
                dfs(neighbor)

        dfs(1)
        return min_score
```

## 🔍 Visual Algorithm Trace

Let's trace through Example 2: `n = 4, roads = [[1,2,2],[1,3,4],[3,4,7]]`

```tikz
\begin{tikzpicture}[
    city/.style={circle, draw, minimum size=0.8cm, font=\sffamily\small, fill=blue!30},
    visited/.style={city, fill=green!40},
    road/.style={thick, font=\tiny},
    step_box/.style={rectangle, draw, fill=yellow!20, font=\tiny, text width=3cm, align=center},
    component/.style={rectangle, draw, dashed, thick, red}
]

% Step 1: Initial graph
\node at (4, 6) {\bfseries Step 1: Build Graph and Start DFS from City 1};

\node[city] (c1) at (1, 4.5) {1};
\node[city] (c2) at (3, 5.5) {2};
\node[city] (c3) at (3, 3.5) {3};
\node[city] (c4) at (5, 4.5) {4};

\draw[road] (c1) -- (c2) node[midway, above] {2};
\draw[road] (c1) -- (c3) node[midway, below] {4};
\draw[road] (c3) -- (c4) node[midway, above] {7};

\node[step_box] at (7, 4.5) {
    Start DFS from 1\\
    min\_score = ∞\\
    visited = \{\}
};

% Step 2: Visit city 1
\node at (4, 3) {\bfseries Step 2: Visit City 1, Explore Edges};

\node[visited] (v1) at (1, 1.5) {1};
\node[city] (v2) at (3, 2.5) {2};
\node[city] (v3) at (3, 0.5) {3};
\node[city] (v4) at (5, 1.5) {4};

\draw[road] (v1) -- (v2) node[midway, above] {2};
\draw[road] (v1) -- (v3) node[midway, below] {4};
\draw[road] (v3) -- (v4) node[midway, above] {7};

\node[step_box] at (7, 1.5) {
    Visit city 1\\
    Check edges: 2, 4\\
    min\_score = 2\\
    visited = \{1\}
};

% Step 3: Continue DFS
\node at (4, -1) {\bfseries Step 3: Continue DFS to Cities 2 and 3};

\node[visited] (f1) at (1, -2.5) {1};
\node[visited] (f2) at (3, -1.5) {2};
\node[visited] (f3) at (3, -3.5) {3};
\node[visited] (f4) at (5, -2.5) {4};

\draw[road] (f1) -- (f2) node[midway, above] {2};
\draw[road] (f1) -- (f3) node[midway, below] {4};
\draw[road] (f3) -- (f4) node[midway, above] {7};

% Draw component boundary
\draw[component] (0.5, -4) rectangle (5.5, -1);

\node[step_box] at (7, -2.5) {
    All cities visited\\
    All edges checked\\
    min\_score = 2\\
    Answer: 2
};

\end{tikzpicture}
```

**Key Observations:**
1. **Connected Component**: All cities 1,2,3,4 are in the same component
2. **Edge Scanning**: We check every edge in the component: [2,4,7]
3. **Minimum Edge**: The answer is min(2,4,7) = 2
4. **Path Construction**: We can construct path 1→2→1→3→4 with score 2

## 🚀 Complete Optimized Solution

```python
class Solution:
    def minScore(self, n: int, roads: List[List[int]]) -> int:
        # Build adjacency list with edge weights
        graph = defaultdict(list)
        for a, b, dist in roads:
            graph[a].append((b, dist))
            graph[b].append((a, dist))

        # BFS to find all cities in component containing city 1
        visited = set()
        queue = deque([1])
        visited.add(1)
        min_score = float('inf')

        while queue:
            city = queue.popleft()

            # Check all edges from this city
            for neighbor, dist in graph[city]:
                min_score = min(min_score, dist)

                # Add unvisited neighbors to queue
                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append(neighbor)

        return min_score
```

## 🧠 Key Takeaways & Problem-Solving Framework

### Why This Problem is Educational

```tikz
\begin{tikzpicture}[
    concept_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=1.8cm},
    arrow/.style={->, thick, green}
]

\node[concept_box] (reframe) at (0, 3) {
    \textbf{Problem Reframing}\\[0.3em]
    Transform complex path\\
    problem into simple\\
    connectivity problem
};

\node[concept_box] (insight) at (4.5, 3) {
    \textbf{Key Insight}\\[0.3em]
    Revisiting allowed means\\
    any edge in component\\
    is reachable
};

\node[concept_box] (algorithms) at (9, 3) {
    \textbf{Multiple Approaches}\\[0.3em]
    Union-Find, DFS, BFS\\
    all solve the same\\
    core problem
};

\draw[arrow] (reframe) -- (insight);
\draw[arrow] (insight) -- (algorithms);

\node at (4.5, 1.5) {\bfseries Core Learning: Reframe → Insight → Algorithm Choice};

\end{tikzpicture}
```

### Problem-Solving Pattern: [[Connected Components Analysis]]

This problem exemplifies the **connected components** pattern:

1. **Identify connectivity**: What cities can reach each other?
2. **Component properties**: What's true for the entire component?
3. **Global optimization**: Find optimal value across the component
4. **Algorithm selection**: Choose appropriate traversal method

### Complexity Analysis
- **Time Complexity:** O(V + E) - visit each vertex and edge once
- **Space Complexity:** O(V + E) - adjacency list and visited set

### Related Concepts for Obsidian

This problem connects to several important algorithmic concepts:

- **[[Connected Components]]**: Finding groups of connected vertices
- **[[Union-Find (Disjoint Set Union)]]**: Efficient connectivity queries
- **[[Graph Traversal]]**: DFS and BFS for exploring graphs
- **[[Problem Reframing]]**: Transforming problem perspective
- **[[Greedy Algorithms]]**: Taking the minimum edge weight

### Related Problems
- **LC547. Number of Provinces**: Basic connected components
- **LC200. Number of Islands**: 2D connected components
- **LC1319. Number of Operations to Make Network Connected**: Union-Find application
- **LC684. Redundant Connection**: Cycle detection with Union-Find

This problem beautifully demonstrates how **problem reframing** can transform an apparently complex pathfinding problem into a straightforward graph connectivity problem!
