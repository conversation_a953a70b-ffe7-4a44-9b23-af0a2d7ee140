---
tags: [problem/leetcode, lc/easy, topic/bit_manipulation, topic/math, pattern/power_of_two_check, course/labuladong]
aliases: [LC231, Power of Two]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 231. Power of Two
> Discussed as an application of `n & (n-1)` in [[Interview/Concept/Algorithms/Bit Manipulation/00 - Common Bitwise Operations|Common Bitwise Operations]].

# LeetCode 231: Power of Two

## Problem Statement
Given an integer `n`, return `true` if it is a power of two. Otherwise, return `false`.
An integer `n` is a power of two, if there exists an integer `x` such that `n == 2^x`.

**Official Link:** [LeetCode 231. Power of Two](https://leetcode.com/problems/power-of-two/)

## Solution Approach: Using `n & (n-1)`

A positive integer `n` is a power of two if and only if it has exactly one bit set to '1' in its binary representation.
- Examples: `1 (0b1)`, `2 (0b10)`, `4 (0b100)`, `8 (0b1000)`.
The operation `n & (n-1)` clears the least significant '1' bit. If `n` is a power of two, it has only one '1' bit. So, `n & (n-1)` will result in `0`.
We also need to handle the case `n <= 0`, as powers of two are positive.

### Python Solution
```python
class Solution:
    def isPowerOfTwo(self, n: int) -> bool:
        if n <= 0:
            return False
        # A power of two has exactly one bit set.
        # n & (n-1) clears the least significant set bit.
        # If n is a power of two, n & (n-1) will be 0.
        return (n & (n - 1)) == 0
```

## Complexity Analysis
- **Time Complexity:** $O(1)$. Bitwise operations are constant time.
- **Space Complexity:** $O(1)$.

## Other Approaches
- **Looping/Division:** Repeatedly divide `n` by 2. If it's a power of two, it will eventually become 1. If any odd number (other than 1) is encountered, it's not. Handle `n <= 0`.
- **Counting Set Bits:** Use the [[Interview/Practice/LeetCode/LC191 - Number of 1 Bits|Hamming Weight]] method. If `count == 1`, then it's a power of two (ensure `n>0`).

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
