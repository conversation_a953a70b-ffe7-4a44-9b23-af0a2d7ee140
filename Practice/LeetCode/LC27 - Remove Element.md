---
tags: [problem/leetcode, lc/easy, topic/array, topic/two_pointers, pattern/fast_slow_pointers]
aliases: [LC27, LeetCode 27. Remove Element, 移除元素]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 27. Remove Element
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道数组题目.md]].

# LeetCode 27: Remove Element

## Problem Statement

Given an integer array `nums` and an integer `val`, remove all occurrences of `val` in `nums` **in-place**. The order of the elements may be changed. Then return *the number of elements in `nums` which are not equal to `val`*.

Consider the number of elements in `nums` which are not equal to `val` be `k`, to get accepted, you need to do the following things:
- Change the array `nums` such that the first `k` elements of `nums` contain the elements which are not equal to `val`.
- The remaining elements of `nums` beyond the first `k` elements, as well as the size of `nums`, do not matter.
- Return `k`.

**Official Link:** [LeetCode 27. Remove Element](https://leetcode.com/problems/remove-element/)

## Solution Approach: Fast-Slow Pointers

This problem is another application of the fast-slow pointer technique for in-place array modification.
- `slow` pointer: Marks the position where the next element *not equal to `val`* should be placed. `nums[0...slow-1]` will contain elements not equal to `val`.
- `fast` pointer: Iterates through the array to examine each element.

1. Initialize `slow = 0` and `fast = 0`.
2. Iterate with `fast` through the array (`while fast < len(nums)`).
3. If `nums[fast]` is **not equal** to `val`:
   - It's an element we want to keep.
   - Copy `nums[fast]` to `nums[slow]`.
   - Increment `slow`.
4. Increment `fast` in every iteration.
5. After the loop, `slow` will be the count of elements not equal to `val`, and these elements will occupy `nums[0...slow-1]`. Return `slow`.

### Python Solution (Labuladong's version)
```python
class Solution:
    def removeElement(self, nums: list[int], val: int) -> int:
        slow = 0
        fast = 0
        while fast < len(nums):
            if nums[fast] != val:
                nums[slow] = nums[fast]
                slow += 1
            fast += 1
        return slow
```
Labuladong's visualizer `div_remove-element` helps illustrate this process.

## Complexity Analysis
- **Time Complexity:** $O(N)$, where $N$ is the length of `nums`. Both `slow` and `fast` pointers traverse the array at most once.
- **Space Complexity:** $O(1)$, as the modification is done in-place.

## Visualization

`nums = [3,2,2,3]`, `val = 3`

1.  `slow=0`, `fast=0`. `nums[0](3) == val(3)`. `fast` becomes 1.
    - `nums = [3,2,2,3]`, `slow=0`, `fast=1`
2.  `nums[1](2) != val(3)`.
    - `nums[slow] = nums[fast]`  => `nums[0] = 2`.
    - `slow` becomes 1.
    - `nums = [2,2,2,3]`, `slow=1`, `fast=1`. Then `fast` becomes 2.
3.  `nums[2](2) != val(3)`.
    - `nums[slow] = nums[fast]` => `nums[1] = 2`.
    - `slow` becomes 2.
    - `nums = [2,2,2,3]`, `slow=2`, `fast=2`. Then `fast` becomes 3.
4.  `nums[3](3) == val(3)`. `fast` becomes 4.
    - `nums = [2,2,2,3]`, `slow=2`, `fast=4`. Loop ends.

Return `slow = 2`. `nums` becomes `[2,2,_,_]`.

## 总结 (Summary)
- LC27 uses the fast-slow pointer technique to remove all occurrences of a specific value in-place.
- The `slow` pointer effectively builds the new array (without `val`) at the beginning of `nums`.
- The `fast` pointer scans the array for elements to keep.
- The solution is $O(N)$ time and $O(1)$ space.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]] (Fast-Slow Pointers)
