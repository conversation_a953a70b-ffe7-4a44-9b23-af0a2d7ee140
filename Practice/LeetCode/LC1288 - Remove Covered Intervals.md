---
tags: [problem/leetcode, lc/medium, topic/array, pattern/interval_problems, pattern/sorting, course/labuladong, lc/lc1288]
aliases: [LC1288, Remove Covered Intervals]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/一个方法解决三道区间问题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 1288. Remove Covered Intervals
> Solution idea from [[Interview/Concept/Algorithms/Interval Problems/00 - General Interval Problem Solving Strategy|General Interval Problem Solving Strategy]].

# LeetCode 1288: Remove Covered Intervals

## Problem Statement
Given an array `intervals` where `intervals[i] = [li, ri]` represent the interval `[li, ri)`, return *the number of remaining intervals after removing all intervals that are covered by another interval in the list*.
An interval `[a, b)` is covered by an interval `[c, d)` if and only if `c <= a` and `b <= d`.

**Official Link:** [LeetCode 1288. Remove Covered Intervals](https://leetcode.com/problems/remove-covered-intervals/)

## Solution Approach: Sorting
1.  **Sort intervals:** Sort by start point ascending. If start points are equal, sort by end point descending.
    This ensures that if `[c,d)` potentially covers `[a,b)` and `c=a`, the longer interval `[c,d)` comes first.
2.  **Iterate and Count:**
    - Initialize `count = 0` (non-covered intervals).
    - Initialize `prev_end = -1` (or `float('-inf')`).
    - For each interval `[start, end]` in the sorted list:
        - If `end > prev_end` (current interval is not covered by the "effective" previous non-covered interval):
            - Increment `count`.
            - Update `prev_end = end`.
        - (If `end <= prev_end`, it means the current interval is covered by some previous interval that extended at least as far as `prev_end`. Because we sorted by start points, and for equal start points by descending end points, any interval that *could* have covered this one and started at the same time would have been processed earlier and set a `prev_end` that covers the current interval.)
3.  Return `count`.

The logic is subtly about merging or extending coverage.
A more robust way to think:
- Keep track of `merged_start` and `merged_end` of the current "dominant" interval.
- If the current interval `[s, e]` is covered by `[merged_start, merged_end]`, it's removed.
- If it extends `merged_end`, update `merged_end`.
- If it's a new, disjoint interval, increment count and update `merged_start, merged_end`.

Let's follow Labuladong's typical logic for interval problems more closely if available, or a standard greedy approach.
The initial sort (start asc, end desc) is key.
`count_removed = 0`
`prev_start, prev_end = intervals[0]`
For interval `curr = intervals[i]` (from 1 to N-1):
  If `curr.start >= prev_start` and `curr.end <= prev_end`: `count_removed++`
  Else (not covered by prev):
    `prev_start = curr.start`
    `prev_end = curr.end`
Return `N - count_removed`.

### Python Solution
```python
class Solution:
    def removeCoveredIntervals(self, intervals: list[list[int]]) -> int:
        # Sort by start time ascending. If start times are equal,
        # sort by end time descending (longer interval comes first).
        intervals.sort(key=lambda x: (x[0], -x[1]))

        count = 0 # Count of non-covered intervals
        # prev_end tracks the maximum end point of the intervals considered so far
        # that form the current "covering" extent.
        prev_end = -1 # Or float('-inf')

        for start, end in intervals:
            # If the current interval's end extends beyond the previous max end,
            # it means this interval is not covered by the previous ones that started
            # at or before it (due to sorting).
            if end > prev_end:
                count += 1
                prev_end = end # Update the max reach
            # If end <= prev_end, this interval is covered by a previous one
            # that started at the same point (and was longer) or started earlier
            # and extended at least as far as prev_end.

        return count
```

## Complexity Analysis
- **Time Complexity:** $O(N \log N)$ for sorting. The iteration is $O(N)$.
- **Space Complexity:** $O(1)$ or $O(N)$ depending on sort.

## 总结 (Summary)
LC1288 requires finding the number of non-covered intervals. Sorting by start point (asc) and then by end point (desc for ties) is crucial. Then, iterate through the sorted intervals, keeping track of the maximum end point reached by a non-covered interval. If a new interval extends this maximum end point, it's also non-covered.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
