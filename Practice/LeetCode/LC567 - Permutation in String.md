---
tags: [problem/leetcode, lc/medium, topic/string, topic/hash_table, topic/two_pointers, pattern/sliding_window, pattern/permutation_substring]
aliases: [LC567, LeetCode 567. Permutation in String, 字符串的排列]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 567. Permutation in String
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/滑动窗口算法核心代码模板.md]].

# LeetCode 567: Permutation in String

## Problem Statement

Given two strings `s1` and `s2`, return `true` if `s2` contains a permutation of `s1`, or `false` otherwise.
In other words, return `true` if one of `s1`'s permutations is the substring of `s2`.

**Official Link:** [LeetCode 567. Permutation in String](https://leetcode.com/problems/permutation-in-string/)

**Example 1:**
Input: `s1 = "ab"`, `s2 = "eidbaooo"`
Output: `true`
Explanation: `s2` contains one permutation of `s1` ("ba").

## Solution Approach: Sliding Window

This problem asks if any permutation of `s1` exists as a substring within `s2`. A key insight is that any permutation of `s1` will:
1.  Have the same length as `s1`.
2.  Have the same character counts as `s1`.

This makes it suitable for a [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window technique]] where the window size is fixed to `len(s1)`.

1.  **Character Counts for `s1` (`needs`):** Count character frequencies in `s1`.
2.  **Window Character Counts (`window_chars`):** Maintain character frequencies for the current window in `s2`.
3.  **`valid_chars_count`:** Track how many character types from `s1` have their frequencies matched in the current window.
4.  **Fixed-Size Window:**
    - Expand `right` pointer. Add `s2[right]` to `window_chars`. Update `valid_chars_count`.
    - If `window_size (right - left) == len(s1)`:
        - Check if `valid_chars_count == len(needs)`. If so, a permutation is found, return `True`.
        - Shrink window: Remove `s2[left]` from `window_chars`. Update `valid_chars_count`. Increment `left`.

### Python Solution
```python
import collections

class Solution:
    def checkInclusion(self, s1: str, s2: str) -> bool:
        needs = collections.Counter(s1)
        window_chars = collections.defaultdict(int)

        left, right = 0, 0
        valid_chars_count = 0

        target_len_s1 = len(s1)

        while right < len(s2):
            char_in = s2[right]
            right += 1

            if char_in in needs:
                window_chars[char_in] += 1
                if window_chars[char_in] == needs[char_in]:
                    valid_chars_count += 1

            # Check and shrink when window size reaches len(s1)
            while (right - left) >= target_len_s1:
                # If all character counts match, we found a permutation
                if valid_chars_count == len(needs):
                    return True

                char_out = s2[left]
                left += 1

                if char_out in needs:
                    if window_chars[char_out] == needs[char_out]:
                        valid_chars_count -= 1
                    window_chars[char_out] -= 1

        return False
```

Labuladong's visualization `div_permutation-in-string` would show a fixed-size window sliding through `s2`.

## Complexity Analysis
-   **Time Complexity:** $O(N_2 + N_1)$, where $N_1$ is length of `s1` and $N_2$ is length of `s2`.
    - Building `needs` map: $O(N_1)$.
    - The sliding window part: `left` and `right` pointers each traverse `s2` once, $O(N_2)$. Hash map operations are $O(1)$ on average (for fixed alphabet size like ASCII).
-   **Space Complexity:** $O(K)$, where $K$ is the size of the character set (e.g., 26 for lowercase English letters). This is for `needs` and `window_chars` maps.

## Visualization
Let `s1 = "ab"`, `s2 = "eidbaooo"`
`needs = {'a':1, 'b':1}`. `len(needs)=2`.
`target_len_s1 = 2`.

1.  `right` moves: `e`, `i`, `d`
    - `s2[0:1]="e"`. Window `{'e':1}`. `valid=0`. `right-left=1 < 2`.
    - `s2[0:2]="ei"`. Window `{'e':1,'i':1}`. `valid=0`. `right-left=2`.
        - Inner `while (right-left >= 2)`:
            - `valid != len(needs)`.
            - `char_out = s2[0]='e'`. `left=1`. `window={'i':1}`.
    - `s2[1:3]="id"`. Window `{'i':1,'d':1}`. `valid=0`. `right-left=2`.
        - Inner `while`:
            - `valid != len(needs)`.
            - `char_out = s2[1]='i'`. `left=2`. `window={'d':1}`.
2.  ...
3.  `right` at index 4 (`char_in = 'b'`). `s2 = "eidbaooo"`.
    - `right` becomes 5. Window `s2[left:right]` becomes relevant.
    - Previous state: `left=2, right=4`. Window `s2[2:4]="db"`. `window_chars={'d':1,'b':1}`. `valid=1` (only 'b' matches).
    - `char_in = s2[4]='a'`. `right=5`.
    - `window_chars={'d':1,'b':1,'a':1}`. `valid=2` ('b' and 'a' match counts in `needs`).
    - `right-left = 5-2 = 3`. `3 >= target_len_s1 (2)`. Enter inner `while`.
        - `valid (2) == len(needs) (2)`. **Return `True`**. (This is a conceptual walkthrough, actual `left` would be different if `target_len_s1` is strictly maintained. Let's re-trace with strict window size.)

**Corrected Trace for Fixed Size Logic:**
`s1 = "ab"`, `s2 = "eidbaooo"`. `needs = {'a':1, 'b':1}`. `target_len = 2`.

| `right` | `char_in` | `window_chars` Before Add | `window_chars` After Add | `valid` | `left` | `right-left` | Action in Inner Loop           | Result      |
|---------|-----------|-------------------------|--------------------------|---------|--------|--------------|--------------------------------|-------------|
| 0       | -         | {}                      | {}                       | 0       | 0      | 0            |                                |             |
| 1       | `e`       | {}                      | `{'e':1}`                | 0       | 0      | 1            | `1 < 2`, skip inner            |             |
| 2       | `i`       | `{'e':1}`               | `{'e':1,'i':1}`          | 0       | 0      | 2            | `right-left=2`. `valid!=2`. `d='e'`, `left=1`. `window={'i':1}`. |             |
| 3       | `d`       | `{'i':1}`               | `{'i':1,'d':1}`          | 0       | 1      | 2            | `right-left=2`. `valid!=2`. `d='i'`, `left=2`. `window={'d':1}`. |             |
| 4       | `b`       | `{'d':1}`               | `{'d':1,'b':1}`          | 1       | 2      | 2            | `right-left=2`. `valid(1)!=2`. `d='d'`, `left=3`. `window={'b':1}`. |             |
| 5       | `a`       | `{'b':1}`               | `{'b':1,'a':1}`          | **2**   | 3      | 2            | `right-left=2`. `valid(2)==2`. **Return `True`**. | **True**    |

The window `s2[3:5]` is "ba", which is a permutation of "ab".

## 总结 (Summary)
- LC567 checks for permutations of `s1` as substrings in `s2`.
- This is a fixed-size sliding window problem. The window size is `len(s1)`.
- Character counts (`needs` for `s1`, `window_chars` for current window) are maintained.
- When the window has the correct size and character counts match, a permutation is found.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window Framework]]
