---
tags: [problem/leetcode, lc/medium, topic/string, topic/hash_table, topic/two_pointers, pattern/sliding_window, pattern/permutation_substring, course/labuladong]
aliases: [LC567, LeetCode 567. Permutation in String, 字符串的排列]
summary: |
  This problem asks to determine if s2 contains a permutation of s1. 
  It's solved using a fixed-size sliding window based on the length of s1, 
  tracking character frequencies to identify anagrams.
created: 2025-05-25T22:48:40.676-07:00
modified: 2025-05-26T18:00:00.000-07:00
---

> [!NOTE] Source Annotation
> Problem: LeetCode 567. Permutation in String
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/滑动窗口算法核心代码模板.md|滑动窗口算法核心代码模板]].

# LeetCode 567: Permutation in String

## Problem Statement

Given two strings `s1` and `s2`, return `true` if `s2` contains a permutation of `s1`, or `false` otherwise.
In other words, return `true` if one of `s1`'s permutations is the substring of `s2`.

**Official Link:** [LeetCode 567. Permutation in String](https://leetcode.com/problems/permutation-in-string/)

**Example 1:**
Input: `s1 = "ab"`, `s2 = "eidbaooo"`
Output: `true`
Explanation: `s2` contains one permutation of `s1` ("ba").

## Solution Approach: Sliding Window (Fixed Size)

This problem asks if any permutation of `s1` exists as a substring within `s2`. A key insight is that any permutation of `s1` will:
1.  Have the same length as `s1`.
2.  Have the same character counts as `s1`.

This makes it suitable for a [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window technique]] where the window size is fixed to `len(s1)`.

1.  **Character Counts for `s1` (`needs`):** Count character frequencies in `s1`.
2.  **Window Character Counts (`window_chars`):** Maintain character frequencies for the current window in `s2`.
3.  **`valid_chars_count`:** Track how many character types from `s1` have their frequencies matched in the current `window_chars`.
4.  **Fixed-Size Window Logic:**
    - Expand `right` pointer. Add `s2[right]` to `window_chars`. Update `valid_chars_count`.
    - The shrinking condition `window_needs_shrink` is when `(right - left) >= len(s1)`.
    - Inside the shrinking loop:
        - First, check if the current window `s[left:right]` (which has length `len(s1)`) is a permutation: `if valid_chars_count == len(needs): return True`.
        - Then, proceed to shrink by removing `s2[left]` from `window_chars`, update `valid_chars_count`, and increment `left`.

### Python Solution
```python
import collections

class Solution:
    def checkInclusion(self, s1: str, s2: str) -> bool:
        needs = collections.Counter(s1)
        window_chars = collections.defaultdict(int)

        left, right = 0, 0
        valid_chars_count = 0
        target_s1_len = len(s1)

        if target_s1_len == 0: # Edge case: empty s1 is a permutation of any s2 window of length 0
            return True
        if target_s1_len > len(s2): # s1 cannot be a substring of s2
            return False

        while right < len(s2):
            char_in = s2[right]
            right += 1 # Window is s[left...right-1]

            if char_in in needs:
                window_chars[char_in] += 1
                if window_chars[char_in] == needs[char_in]:
                    valid_chars_count += 1
            
            # Shrink window if its size has reached len(s1)
            # The condition for shrinking is `(right - left) >= target_s1_len`
            # because we first check for solution, then shrink.
            while (right - left) >= target_s1_len:
                # Check if current window is a permutation BEFORE shrinking
                # Window s[left:right] currently has length `right - left`.
                # If `right - left == target_s1_len`, this is a candidate.
                if (right - left) == target_s1_len and valid_chars_count == len(needs):
                    return True

                # Now, prepare to shrink by removing s[left]
                char_out = s2[left]
                left += 1

                if char_out in needs:
                    if window_chars[char_out] == needs[char_out]:
                        valid_chars_count -= 1
                    window_chars[char_out] -= 1
        
        return False
```
Labuladong's visualization `div_permutation-in-string` would show a fixed-size window sliding through `s2`.

## Complexity Analysis
-   **Time Complexity:** $O(N_2 + N_1)$, where $N_1$ is length of `s1` and $N_2$ is length of `s2`.
    - Building `needs` map: $O(N_1)$.
    - The sliding window part: `left` and `right` pointers each traverse `s2` once, $O(N_2)$. Hash map operations are $O(1)$ on average (for fixed alphabet size like ASCII).
-   **Space Complexity:** $O(K)$, where $K$ is the size of the character set (e.g., 26 for lowercase English letters). This is for `needs` and `window_chars` maps.

## Visualization
Let `s1 = "ab"`, `s2 = "eidbaooo"`.
`needs = {'a':1, 'b':1}`, `len(needs)=2`. `target_s1_len = 2`.

| `right` | `char_in` | `window_chars` (after add) | `valid` | `left` | `len=r-l` | Inner Loop Action (`len >= 2`?)  | Output Update |
|:-------:|:---------:|:--------------------------:|:-------:|:------:|:---------:|:-------------------------------:|:-------------:|
| 0       | -         | {}                         | 0       | 0      | 0         |                                 |               |
| 1       | `e`       | `{'e':1}`                  | 0       | 0      | 1         | `1 < 2`, No shrink              |               |
| 2       | `i`       | `{'e':1, 'i':1}`           | 0       | 0      | 2         | `len=2`. `valid(0)!=2`. Shrink: `char_out='e'`, `left=1`. `window={'i':1}`. |               |
| 3       | `d`       | `{'i':1, 'd':1}`           | 0       | 1      | 2         | `len=2`. `valid(0)!=2`. Shrink: `char_out='i'`, `left=2`. `window={'d':1}`. |               |
| 4       | `b`       | `{'d':1, 'b':1}`           | 1       | 2      | 2         | `len=2`. `valid(1)!=2`. Shrink: `char_out='d'`, `left=3`. `window={'b':1}`. |               |
| 5       | `a`       | `{'b':1, 'a':1}`           | **2**   | 3      | 2         | `len=2`. `valid(2)==2`. **Return True.** | **True**      |

The window `s2[3:5]` is "ba", which is a permutation of "ab".

## 总结 (Summary)
- LC567 checks for permutations of `s1` as substrings in `s2`.
- This is effectively a fixed-size sliding window problem. The window size is `len(s1)`.
- Character counts (`needs` for `s1`, `window_chars` for current window) are maintained.
- When the window has the correct size and character counts match `needs` (checked by `valid_chars_count == len(needs)`), a permutation is found.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window Framework]]
