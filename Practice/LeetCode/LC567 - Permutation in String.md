---
tags: [problem/leetcode, lc/medium, topic/sliding_window, topic/two_pointers, pattern/fixed_window, pattern/frequency_counting]
aliases: [LC567, LeetCode 567, Permutation in String, 字符串的排列]
---

# LeetCode 567: Permutation in String

## Problem Statement

Given two strings `s1` and `s2`, return `true` if `s2` contains a **permutation** of `s1`. In other words, return `true` if one of `s1`'s permutations is the substring of `s2`.

**Official Link:** [LeetCode 567. Permutation in String](https://leetcode.com/problems/permutation-in-string/)

## 🔄 Understanding Permutations in Substrings

Think of this as finding any rearrangement of `s1` as a contiguous substring in `s2`:

```tikz
\begin{tikzpicture}[
    char_box/.style={rectangle, draw, minimum size=0.8cm, font=\sffamily\bfseries, fill=blue!30},
    pattern_box/.style={rectangle, draw, minimum size=0.8cm, font=\sffamily\bfseries, fill=red!30},
    match_box/.style={rectangle, draw, minimum size=0.8cm, font=\sffamily\bfseries, fill=green!40},
    window_frame/.style={rectangle, draw, thick, red, dashed},
    example_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=4cm, align=center}
]

% Example visualization
\node at (6, 6) {\bfseries Example: s1 = "ab", s2 = "eidbaooo"};

% Pattern s1
\node at (0, 5) {\small s1:};
\node[pattern_box] at (0.8, 5) {a};
\node[pattern_box] at (1.6, 5) {b};

% String s2 with windows
\node at (0, 3.5) {\small s2:};
\node[char_box] at (0.8, 3.5) {e};
\node[char_box] at (1.6, 3.5) {i};
\node[char_box] at (2.4, 3.5) {d};
\node[match_box] at (3.2, 3.5) {b};
\node[match_box] at (4, 3.5) {a};
\node[char_box] at (4.8, 3.5) {o};
\node[char_box] at (5.6, 3.5) {o};
\node[char_box] at (6.4, 3.5) {o};

% Window frames showing different attempts
\draw[window_frame] (0.5, 3.2) rectangle (2.1, 3.8);
\node at (1.3, 2.9) {\tiny "ei" NO};

\draw[window_frame] (1.3, 3.2) rectangle (2.9, 3.8);
\node at (2.1, 2.9) {\tiny "id" NO};

\draw[window_frame] (2.9, 3.2) rectangle (4.3, 3.8);
\node at (3.6, 2.9) {\tiny "ba" YES};

\node[example_box] at (9, 4) {
    \textbf{Goal:}\\
    Find any permutation\\
    of s1 as substring\\
    in s2\\[0.5em]
    \textbf{Key Insight:}\\
    Permutations have\\
    same character\\
    frequencies!
};

\end{tikzpicture}
```

**Critical Insight:** Two strings are permutations of each other if and only if they have **identical character frequencies**. This transforms the problem from checking all permutations to **[[Frequency Counting]]**.

## 🧠 The Breakthrough Insight: Fixed-Size Sliding Window

The key realization is to use a **fixed-size sliding window** equal to the length of `s1`:

```tikz
\begin{tikzpicture}[
    insight_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4.5cm, align=center, minimum height=2.5cm},
    arrow/.style={->, thick, red, line width=2pt},
    strategy_box/.style={rectangle, draw, fill=orange!20, font=\sffamily\small, text width=6cm, align=center}
]

\node[insight_box] (naive) at (0, 3) {
    \textbf{Naive Approach}\\[0.5em]
    "Generate all permutations\\
    of s1 and check if any\\
    appears in s2"\\[0.5em]
    O(n! × m) complexity!\\
    Exponential disaster!
};

\node[insight_box] (smart) at (8, 3) {
    \textbf{Fixed Sliding Window}\\[0.5em]
    "Use window of size |s1|\\
    and compare frequency\\
    counts at each position"\\[0.5em]
    O(m) complexity!
};

\draw[arrow] (naive) -- (smart);

\node[strategy_box] at (4, 0.5) {
    \textbf{Window Strategy:}\\
    1. Create frequency map for s1\\
    2. Slide fixed-size window through s2\\
    3. Compare window frequency with s1 frequency\\[0.5em]
    Each position checked in constant time!
};

\end{tikzpicture}
```

**Why This Works:**
- **Fixed size**: Window always has same length as `s1`
- **Frequency comparison**: O(1) check if frequencies match
- **Sliding efficiency**: Add one character, remove one character per step

## 🔍 Problem Analysis: Window Frequency Matching

The core challenge is efficiently comparing frequency maps as the window slides:

```tikz
\begin{tikzpicture}[
    concept_box/.style={rectangle, draw, rounded corners, fill=green!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2cm},
    formula_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=3.5cm, align=center}
]

\node[concept_box] (frequency) at (0, 3) {
    \textbf{Frequency Matching}\\[0.5em]
    Two strings are\\
    permutations iff they\\
    have identical character\\
    frequency counts
};

\node[concept_box] (efficiency) at (5, 3) {
    \textbf{Efficient Updates}\\[0.5em]
    Maintain window frequency\\
    with O(1) updates:\\
    add new, remove old
};

\node[formula_box] at (0, 0.5) {
    \textbf{Permutation Check:}\\
    For all characters c:\\
    window\_freq[c] ==\\
    pattern\_freq[c]
};

\node[formula_box] at (5, 0.5) {
    \textbf{Window Update:}\\
    window\_freq[new\_char]++\\
    window\_freq[old\_char]--\\
    Compare frequencies
};

\end{tikzpicture}
```

## 💡 Clean, Elegant Algorithm

Here's the beautifully structured solution that maps directly to our intuition:

```python
class Solution:
    def checkInclusion(self, s1: str, s2: str) -> bool:
        if len(s1) > len(s2):
            return False

        # Step 1: Build frequency map for s1 (pattern)
        s1_freq = {}
        for char in s1:
            s1_freq[char] = s1_freq.get(char, 0) + 1

        # Step 2: Initialize sliding window
        window_size = len(s1)
        window_freq = {}

        # Step 3: Process first window
        for i in range(window_size):
            char = s2[i]
            window_freq[char] = window_freq.get(char, 0) + 1

        # Step 4: Check if first window matches
        if window_freq == s1_freq:
            return True

        # Step 5: Slide window through rest of s2
        for i in range(window_size, len(s2)):
            # Add new character (expand right)
            new_char = s2[i]
            window_freq[new_char] = window_freq.get(new_char, 0) + 1

            # Remove old character (contract left)
            old_char = s2[i - window_size]
            window_freq[old_char] -= 1
            if window_freq[old_char] == 0:
                del window_freq[old_char]  # Clean up zero counts

            # Check if current window matches
            if window_freq == s1_freq:
                return True

        return False
```

## 🔍 Visual Algorithm Trace

Let's trace through `s1 = "ab"`, `s2 = "eidbaooo"`:

```tikz
\begin{tikzpicture}[
    char_box/.style={rectangle, draw, minimum size=0.6cm, font=\tiny, fill=blue!30},
    window_char/.style={char_box, fill=green!40},
    match_char/.style={char_box, fill=red!40},
    window_frame/.style={rectangle, draw, thick, blue, dashed},
    step_box/.style={rectangle, draw, fill=yellow!20, font=\tiny, text width=3cm, align=center}
]

% Step 1: Initial window "ei"
\node at (6, 8) {\bfseries Step 1: First window "ei" (indices 0-1)};

\node at (0, 7.5) {\tiny s2:};
\node[window_char] at (0.8, 7.5) {e};
\node[window_char] at (1.6, 7.5) {i};
\node[char_box] at (2.4, 7.5) {d};
\node[char_box] at (3.2, 7.5) {b};
\node[char_box] at (4, 7.5) {a};
\node[char_box] at (4.8, 7.5) {o};
\node[char_box] at (5.6, 7.5) {o};
\node[char_box] at (6.4, 7.5) {o};

\draw[window_frame] (0.5, 7.2) rectangle (1.9, 7.8);

\node[step_box] at (9, 7.5) {
    window\_freq = \{e:1, i:1\}\\
    s1\_freq = \{a:1, b:1\}\\
    Match? NO\\
    Continue sliding...
};

% Step 2: Window "id"
\node at (6, 6) {\bfseries Step 2: Slide to "id" (indices 1-2)};

\node at (0, 5.5) {\tiny s2:};
\node[char_box] at (0.8, 5.5) {e};
\node[window_char] at (1.6, 5.5) {i};
\node[window_char] at (2.4, 5.5) {d};
\node[char_box] at (3.2, 5.5) {b};
\node[char_box] at (4, 5.5) {a};
\node[char_box] at (4.8, 5.5) {o};
\node[char_box] at (5.6, 5.5) {o};
\node[char_box] at (6.4, 5.5) {o};

\draw[window_frame] (1.3, 5.2) rectangle (2.7, 5.8);

\node[step_box] at (9, 5.5) {
    Remove: e, Add: d\\
    window\_freq = \{i:1, d:1\}\\
    s1\_freq = \{a:1, b:1\}\\
    Match? NO
};

% Step 3: Window "db"
\node at (6, 4) {\bfseries Step 3: Slide to "db" (indices 2-3)};

\node at (0, 3.5) {\tiny s2:};
\node[char_box] at (0.8, 3.5) {e};
\node[char_box] at (1.6, 3.5) {i};
\node[window_char] at (2.4, 3.5) {d};
\node[window_char] at (3.2, 3.5) {b};
\node[char_box] at (4, 3.5) {a};
\node[char_box] at (4.8, 3.5) {o};
\node[char_box] at (5.6, 3.5) {o};
\node[char_box] at (6.4, 3.5) {o};

\draw[window_frame] (2.1, 3.2) rectangle (3.5, 3.8);

\node[step_box] at (9, 3.5) {
    Remove: i, Add: b\\
    window\_freq = \{d:1, b:1\}\\
    s1\_freq = \{a:1, b:1\}\\
    Match? NO (missing a)
};

% Step 4: Window "ba" - MATCH!
\node at (6, 2) {\bfseries Step 4: Slide to "ba" (indices 3-4) - MATCH!};

\node at (0, 1.5) {\tiny s2:};
\node[char_box] at (0.8, 1.5) {e};
\node[char_box] at (1.6, 1.5) {i};
\node[char_box] at (2.4, 1.5) {d};
\node[match_char] at (3.2, 1.5) {b};
\node[match_char] at (4, 1.5) {a};
\node[char_box] at (4.8, 1.5) {o};
\node[char_box] at (5.6, 1.5) {o};
\node[char_box] at (6.4, 1.5) {o};

\draw[window_frame] (2.9, 1.2) rectangle (4.3, 1.8);

\node[step_box] at (9, 1.5) {
    Remove: d, Add: a\\
    window\_freq = \{b:1, a:1\}\\
    s1\_freq = \{a:1, b:1\}\\
    Match? YES!
};

% Final result
\node[step_box, fill=green!30] at (6, 0) {
    \textbf{Result: True}\\
    Found permutation "ba" at indices 3-4\\
    "ba" is a permutation of "ab"
};

\end{tikzpicture}
```

**Key Observations:**
1. **Fixed window size**: Always equals length of `s1` (2 in this case)
2. **Efficient sliding**: Add one character, remove one character per step
3. **Frequency comparison**: Direct dictionary comparison for permutation check
4. **Early termination**: Return `True` as soon as match found

## 🧠 Key Takeaways & Problem-Solving Framework

### Why This Problem is Educational

```tikz
\begin{tikzpicture}[
    concept_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=1.8cm},
    arrow/.style={->, thick, green}
]

\node[concept_box] (insight) at (0, 3) {
    \textbf{Key Insight}\\[0.3em]
    Transform permutation\\
    checking into frequency\\
    comparison problem
};

\node[concept_box] (window) at (4.5, 3) {
    \textbf{Fixed Window}\\[0.3em]
    Use fixed-size sliding\\
    window for efficient\\
    substring checking
};

\node[concept_box] (optimization) at (9, 3) {
    \textbf{Optimization}\\[0.3em]
    O(1) frequency updates\\
    make algorithm linear\\
    in string length
};

\draw[arrow] (insight) -- (window);
\draw[arrow] (window) -- (optimization);

\node at (4.5, 1.5) {\bfseries Core Learning: Transform → Window → Optimize};

\end{tikzpicture}
```

### Problem-Solving Pattern: [[Fixed Sliding Window]]

This problem exemplifies the **fixed sliding window** pattern:

1. **Fixed size**: Window size determined by constraint (length of pattern)
2. **Frequency tracking**: Use frequency maps for efficient comparison
3. **Sliding mechanism**: Add new element, remove old element
4. **Comparison check**: Compare current window with target pattern

### Complexity Analysis
- **Time Complexity:** O(|s2|) - each character processed once
- **Space Complexity:** O(|s1|) - for frequency maps

### Related Concepts for Obsidian

This problem connects to several important algorithmic concepts:

- **[[Sliding Window]]**: Core technique with fixed window size
- **[[Frequency Counting]]**: Efficient permutation checking
- **[[Hash Tables]]**: Fast frequency lookups and comparisons
- **[[String Algorithms]]**: Substring pattern matching
- **[[Anagram Detection]]**: Permutation checking techniques

### Related Problems
- **LC76. Minimum Window Substring**: Variable-size sliding window
- **LC438. Find All Anagrams in a String**: Multiple permutation positions
- **LC242. Valid Anagram**: Basic permutation checking
- **LC49. Group Anagrams**: Grouping by permutation equivalence

### Implementation Tips

#### Template for Fixed Sliding Window
```python
def fixed_sliding_window(s, pattern):
    if len(pattern) > len(s):
        return False

    # Build pattern frequency
    pattern_freq = build_frequency(pattern)
    window_size = len(pattern)

    # Process first window
    window_freq = build_frequency(s[:window_size])
    if window_freq == pattern_freq:
        return True

    # Slide window
    for i in range(window_size, len(s)):
        # Update window frequency
        add_char(window_freq, s[i])
        remove_char(window_freq, s[i - window_size])

        # Check match
        if window_freq == pattern_freq:
            return True

    return False
```

#### Common Optimizations
- **Array vs Dictionary**: Use arrays for known character sets (a-z)
- **Early termination**: Return immediately when match found
- **Clean zero counts**: Remove zero entries to keep dictionaries clean
- **Counter class**: Use Python's Counter for cleaner code

This problem beautifully demonstrates how **frequency analysis** transforms a complex permutation problem into an elegant sliding window solution!
