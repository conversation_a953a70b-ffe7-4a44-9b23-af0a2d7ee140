---
tags: [problem/leetcode, lc/easy, topic/array, topic/stack, pattern/monotonic_stack, course/labuladong]
aliases: [LC496, Next Greater Element I]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典队列_栈算法/单调栈算法模板解决三道例题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 496. Next Greater Element I
> Solution approach adapted from [[Interview/Concept/Data Structures/Stack/Applications/01 - Monotonic Stack Pattern|Monotonic Stack Pattern]].

# LeetCode 496: Next Greater Element I

## Problem Statement
The **next greater element** of some element `x` in an array is the **first greater** element that is **to the right** of `x` in the same array.
You are given two distinct **0-indexed** integer arrays `nums1` and `nums2`, where `nums1` is a subset of `nums2`.
For each `0 <= i < nums1.length`, find the index `j` such that `nums1[i] == nums2[j]` and determine the **next greater element** of `nums2[j]` in `nums2`. If there is no next greater element, then the answer for this query is `-1`.
Return *an array `ans` of length `nums1.length` such that `ans[i]` is the **next greater element** as described above.*

**Official Link:** [LeetCode 496. Next Greater Element I](https://leetcode.com/problems/next-greater-element-i/)

## Solution Approach: Monotonic Stack + HashMap

1.  **Precompute Next Greater Elements for `nums2`:**
    Use the [[Interview/Concept/Data Structures/Stack/Applications/01 - Monotonic Stack Pattern|Monotonic Stack template]] to calculate the next greater element for every number in `nums2`. Store these mappings (e.g., `number_in_nums2 -> its_next_greater_element`) in a hash map.
2.  **Query for `nums1` Elements:**
    Iterate through `nums1`. For each `num` in `nums1`, look up its precomputed next greater element from the hash map.

### Python Solution
```python
class Solution:
    def nextGreaterElement(self, nums1: list[int], nums2: list[int]) -> list[int]:
        # Step 1: Precompute NGE for nums2 using monotonic stack
        nge_map = {} # Stores element -> next_greater_element
        stack = []   # Monotonically decreasing stack

        for num in reversed(nums2): # Iterate from right to left
            while stack and num >= stack[-1]:
                stack.pop()

            if stack:
                nge_map[num] = stack[-1]
            else:
                nge_map[num] = -1

            stack.append(num)

        # Step 2: Build result for nums1 using the map
        result = [0] * len(nums1)
        for i, num in enumerate(nums1):
            result[i] = nge_map.get(num, -1) # Should always find num in map as nums1 is subset

        return result
```

## Complexity Analysis
- **Time Complexity:** $O(N_1 + N_2)$, where $N_1 = \text{len(nums1)}$ and $N_2 = \text{len(nums2)}$.
    - Monotonic stack part for `nums2`: $O(N_2)$.
    - Building result for `nums1`: $O(N_1)$.
- **Space Complexity:** $O(N_2)$ for `nge_map` and the `stack`.

## 总结 (Summary)
- LC496 combines the monotonic stack pattern with a hash map for efficient querying.
- First, find the next greater element for all elements in the larger array (`nums2`).
- Then, use these precomputed results to quickly find answers for elements in `nums1`.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
