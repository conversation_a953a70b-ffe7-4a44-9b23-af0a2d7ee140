---
tags: [problem/leetcode, lc/hard, topic/dynamic_programming, pattern/game_dp, pattern/interval_dp, course/labuladong, lc/lc312]
aliases: [LC312, <PERSON>urst Balloons]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/经典动态规划：戳气球.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 312. Burst Balloons
> The solution is based on the [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Game DP/Burst Balloons|Burst Balloons DP Pattern]].

# LeetCode 312: Burst Balloons

## Problem Statement
You are given `n` balloons, indexed from `0` to `n - 1`. Each balloon has a number painted on it represented by an array `nums`. You are asked to burst all the balloons.
If you burst the `i`-th balloon, you will get `nums[i - 1] * nums[i] * nums[i + 1]` coins. If `i - 1` or `i + 1` goes out of bounds of the array, then treat it as if there is a balloon with a `1` painted on it.
Return *the maximum coins you can collect by bursting the balloons wisely*.

**Official Link:** [LeetCode 312. Burst Balloons](https://leetcode.com/problems/burst-balloons/)

## Solution Approach
This problem is solved using dynamic programming with the "last balloon to burst" strategy, as detailed in [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Game DP/Burst Balloons|Burst Balloons DP Pattern]].

### Python Solution
```python
class Solution:
    def maxCoins(self, nums: list[int]) -> int:
        n_orig = len(nums)
        if n_orig == 0:
            return 0

        # Add 1 at both ends for boundary conditions
        points = [1] * (n_orig + 2)
        for i in range(n_orig):
            points[i+1] = nums[i]

        n_padded = len(points)

        # dp[i][j] = max coins from bursting balloons in open interval (i, j)
        # i.e., balloons points[i+1]...points[j-1]
        dp = [[0] * n_padded for _ in range(n_padded)]

        # Iterate by length of the segment [i...j] considered for its inner balloons (i,j)
        # Smallest meaningful segment to consider is length 3 (e.g., points[i, k, j])
        # where k is the only balloon in (i,j).
        for length_segment in range(3, n_padded + 1):
            for i in range(n_padded - length_segment + 1):
                j = i + length_segment - 1 # End of the segment [i...j]

                # Iterate through k as the last balloon to burst in (i, j)
                for k in range(i + 1, j): # i < k < j
                    # Coins from bursting k last in (i,j)
                    coins_from_k_burst = points[i] * points[k] * points[j]

                    # Total coins = coins from bursting sub-interval (i,k) +
                    #               coins from bursting sub-interval (k,j) +
                    #               coins_from_k_burst
                    current_config_coins = dp[i][k] + dp[k][j] + coins_from_k_burst
                    dp[i][j] = max(dp[i][j], current_config_coins)

        # The answer is for bursting all original balloons, which are in interval (0, n_padded-1)
        return dp[0][n_padded - 1]

```

## Complexity Analysis
- **Time Complexity:** $O(N^3)$, where $N$ is the original number of balloons.
- **Space Complexity:** $O(N^2)$ for the DP table.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
