---
tags: [problem/leetcode, lc/medium, topic/linked_list, topic/randomized_algorithms, algorithm/reservoir_sampling, course/labuladong]
aliases: [LC382, Linked List Random Node]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 382. Linked List Random Node
> Solution uses Reservoir Sampling, discussed in [[Interview/Concept/Algorithms/Randomized Algorithms/01 - Concepts in Game Random Algorithms|Concepts in Game Random Algorithms]].

# LeetCode 382: Linked List Random Node

## Problem Statement
Given a singly linked list, return a random node's value from the linked list. Each node must have the **same probability** of being chosen.

Implement the `Solution` class:
- `Solution(ListNode head)` Initializes the object with the head of the singly-linked list `head`.
- `int getRandom()` Chooses a node randomly from the list and returns its value. All nodes should be chosen with equal probability.

**Official Link:** [LeetCode 382. Linked List Random Node](https://leetcode.com/problems/linked-list-random-node/)

## Solution Approach: Reservoir Sampling (for k=1)

This problem can be solved efficiently in one pass using Reservoir Sampling for selecting a single element ($k=1$) from a stream of unknown length (or a list where length is not pre-calculated).

**Algorithm for `getRandom()`:**
1. Initialize `count = 0` and `result_value = None`.
2. Iterate through the linked list with a pointer `current_node`, starting from `head`.
3. For each `current_node` encountered:
   a. Increment `count`.
   b. Generate a random integer `rand_choice` between `1` and `count` (inclusive).
   c. If `rand_choice == 1` (this happens with probability `1/count`):
      - Set `result_value = current_node.val`.
4. After iterating through the entire list, `result_value` will hold the value of the randomly chosen node. Return `result_value`.

**Why this ensures equal probability ($1/N$ for each node, where N is list length):**
Consider the `i`-th node (1-indexed) in the list.
- It's chosen as `result_value` at step `i` with probability `1/i`.
- For it to remain the `result_value` until the end, for all subsequent nodes `j` (from `i+1` to `N`):
    - At step `j`, the `j`-th node must *not* be chosen. This happens with probability `1 - (1/j) = (j-1)/j`.
- So, the probability that `i`-th node is the final choice is:
  `(1/i) * (i/(i+1)) * ((i+1)/(i+2)) * ... * ((N-1)/N)`
  $= (1/i) * (i/N)$  (telescoping product)
  $= 1/N$.
This holds for all nodes.

### Python Solution
```python
import random

# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, val=0, next=None):
#         self.val = val
#         self.next = next

class Solution:
    def __init__(self, head: [ListNode]):
        self.head = head
        # For an alternative approach if length can be pre-calculated:
        # self.length = 0
        # curr = head
        # while curr:
        #     self.length += 1
        #     curr = curr.next

    def getRandom(self) -> int:
        # Reservoir Sampling for k=1
        count = 0
        result_val = 0 # Placeholder, will be overwritten
        curr = self.head

        while curr:
            count += 1
            # Generate random number from 1 to count (inclusive)
            if random.randint(1, count) == 1:
                result_val = curr.val
            curr = curr.next

        return result_val

    # Alternative: Pre-calculate length (if allowed or efficient for multiple calls)
    # def getRandom_with_length(self) -> int:
    #     if self.length == 0: return -1 # Or raise error
    #     rand_idx = random.randint(0, self.length - 1)
    #     curr = self.head
    #     for _ in range(rand_idx):
    #         curr = curr.next
    #     return curr.val

# Your Solution object will be instantiated and called as such:
# obj = Solution(head)
# param_1 = obj.getRandom()
```

## Complexity Analysis
- **Constructor (`__init__`)**:
    - Reservoir Sampling version: $O(1)$ (just stores head).
    - Length pre-calculation version: $O(N)$ to find length.
- **`getRandom()`**:
    - Reservoir Sampling version: $O(N)$ for each call, as it traverses the list. $O(1)$ space.
    - Length pre-calculation version: $O(N)$ for each call to traverse to k-th node. $O(1)$ space.

If `getRandom` is called many times, and the list doesn't change, converting the list to an array in constructor ($O(N)$ time and space) allows $O(1)$ `getRandom`. However, the problem often implies an online/streaming context where one pass is preferred per query.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
