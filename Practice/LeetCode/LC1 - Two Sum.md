---
tags: [problem/leetcode, lc/easy, topic/array, topic/hash_table, pattern/two_sum]
aliases: [LC1, LeetCode 1. Two Sum]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 1. Two Sum
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/入门：编程语言基础及练习/编程语言刷题实践.md]].

# LeetCode 1: Two Sum

## Problem Statement

Given an array of integers `nums` and an integer `target`, return *indices of the two numbers such that they add up to `target`*.

You may assume that each input would have **exactly one solution**, and you may not use the *same* element twice.

You can return the answer in any order.

**Official Link:** [LeetCode 1. Two Sum](https://leetcode.com/problems/two-sum/)

**Example 1:**
Input: `nums = [2,7,11,15]`, `target = 9`
Output: `[0,1]`
Explanation: Because `nums[0] + nums[1] == 9`, we return `[0, 1]`.

## Solution (Python)

The problem can be solved using a brute-force approach with nested loops, or more efficiently using a hash map.

### 1. Brute-Force Approach

Iterate through each element `x` and then iterate through the rest of the array to find an element `y` such that `x + y = target`.

```python
class Solution:
    def twoSum_brute_force(self, nums: list[int], target: int) -> list[int]:
        n = len(nums)
        for i in range(n):
            for j in range(i + 1, n): # j starts from i + 1 to avoid using the same element twice
                                      # and avoid redundant pairs
                if nums[i] + nums[j] == target:
                    return [i, j]
        return [] # Should not be reached based on problem constraints (exactly one solution)
```

**Explanation:**
- The outer loop picks the first number, `nums[i]`.
- The inner loop picks the second number, `nums[j]`.
    - It starts from `i + 1` to ensure that `i` and `j` are different indices and to avoid checking the same pair twice (e.g., `(nums[0], nums[1])` is the same as `(nums[1], nums[0])` for the sum).
- If `nums[i] + nums[j]` equals the `target`, their indices `[i, j]` are returned.

**Complexity Analysis (Brute-Force):**
-   **Time Complexity:** $O(N^2)$, where $N$ is the length of `nums`. The nested loops iterate through roughly $N^2/2$ pairs.
-   **Space Complexity:** $O(1)$, as we are only using a few variables.

### 2. Hash Map (Dictionary) Approach

A more efficient approach uses a hash map to store numbers and their indices as we iterate through the array. For each number `num`, we check if `target - num` (the complement) is already in the hash map.

This approach is a common application of the [[Interview/Concept/Algorithms/Hashing/Two Sum Pattern|Two Sum Pattern]].

```python
class Solution:
    def twoSum(self, nums: list[int], target: int) -> list[int]:
        num_to_index = {}  # Use a dictionary to store number -> index
        for index, num in enumerate(nums):
            complement = target - num
            if complement in num_to_index:
                # Found the complement in the dictionary
                return [num_to_index[complement], index]
            # Store the current number and its index
            num_to_index[num] = index
        return [] # Should not be reached
```

**Explanation:**
1.  Initialize an empty dictionary `num_to_index` to store numbers encountered so far and their corresponding indices. This dictionary is an instance of a [[Interview/Concept/Programming Languages/Python/05 - Python Dict (Hash Map) for Interviews|Python Dict]].
2.  Iterate through the `nums` array using `enumerate` to get both the `index` and the `num` (value).
3.  For each `num`, calculate the `complement` needed to reach the `target` (i.e., `complement = target - num`).
4.  Check if this `complement` already exists as a key in `num_to_index`.
    -   If it exists, it means we have found the two numbers that sum up to the target. The first number's index is `num_to_index[complement]`, and the second number's index is the current `index`. Return `[num_to_index[complement], index]`.
    -   If the `complement` does not exist in the dictionary, it means we haven't encountered the other half of the pair yet. Add the current `num` and its `index` to the `num_to_index` dictionary for future reference.

**Why does this work?**
As we iterate, if `num` is the second number of the pair `(complement, num)`, and `complement` was encountered earlier, it would already be in `num_to_index`. If `num` is the first number of a pair `(num, complement)`, then `complement` is not yet in the map. We add `num` to the map so that when we later encounter `complement`, we can find `num`.

**Complexity Analysis (Hash Map):**
-   **Time Complexity:** $O(N)$, where $N$ is the length of `nums`. We iterate through the list once. Dictionary lookups and insertions take average $O(1)$ time.
-   **Space Complexity:** $O(N)$ in the worst case, as the hash map might store up to $N-1$ elements if the solution pair is found at the very end or if no solution exists (though the problem guarantees one).

## Visualizing Hash Map Approach

Let `nums = [2, 7, 11, 15]`, `target = 9`.

```tikz
\begin{tikzpicture}[
    node/.style={draw, rectangle, minimum height=0.8cm, minimum width=1cm},
    dict_entry/.style={draw, rectangle, fill=blue!10, minimum height=0.6cm, text width=2.5cm, align=center},
    arrow/.style={->, >=stealth, thick},
    highlight/.style={fill=green!20}
]
    % Array
    \node[node] (n0) at (0,0) {2}; \node at (0,-0.7) {idx 0};
    \node[node] (n1) at (1.5,0) {7}; \node at (1.5,-0.7) {idx 1};
    \node[node] (n2) at (3,0) {11}; \node at (3,-0.7) {idx 2};
    \node[node] (n3) at (4.5,0) {15}; \node at (4.5,-0.7) {idx 3};
    \node at (6,0) {target=9};

    % Dictionary state
    \node at (2.25, -1.5) {num\_to\_index (Dictionary)};

    % Step 1: index=0, num=2
    \node at (-2, -3) {Step 1:};
    \node at (0, -2.5) {num=2, idx=0};
    \node at (0, -3) {complement = 9-2 = 7};
    \node at (0, -3.5) {7 not in dict};
    \node[dict_entry] (d1) at (2.25, -3.5) {\{2: 0\}};
    \draw[arrow, red!70] (n0) -- (0, -2);

    % Step 2: index=1, num=7
    \node at (-2, -5) {Step 2:};
    \node at (0, -4.5) {num=7, idx=1};
    \node at (0, -5) {complement = 9-7 = 2};
    \node[highlight] at (0, -5.5) {2 in dict! (value: 0)};
    \node[dict_entry, fill=yellow!30] (d2) at (2.25, -5) {\{2: 0\} (current state)};
    \node at (2.25, -6) {Return [dict[2], 1] $\Rightarrow$ [0, 1]};
    \draw[arrow, red!70] (n1) -- (0, -4);
    \draw[arrow, dashed, blue] (d2.west) .. controls (1,-5.5) and (1,-5.5) .. (0.2, -5.5); % Link complement check to dict

\end{tikzpicture}
```

## 总结 (Summary)
- The Two Sum problem is a classic introductory problem often solved efficiently using a hash map.
- The brute-force $O(N^2)$ solution involves nested loops.
- The hash map approach achieves $O(N)$ time complexity by storing seen numbers and their indices, allowing for quick lookups of the required complement. This trades space complexity ($O(N)$ for the hash map) for improved time complexity.

---
Next: [[Interview/Practice/LeetCode/LC217 - Contains Duplicate|LC217 - Contains Duplicate]]
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Hashing/Two Sum Pattern|Two Sum Pattern]], [[Interview/Concept/Programming Languages/Python/05 - Python Dict (Hash Map) for Interviews|Python Dict]]
