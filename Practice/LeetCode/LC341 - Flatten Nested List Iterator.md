---
tags: [problem/leetcode, lc/medium, topic/tree, topic/n_ary_tree, topic/iterator, topic/design, pattern/dfs, pattern/lazy_evaluation]
aliases: [LC341, LeetCode 341. Flatten Nested List Iterator, 扁平化嵌套列表迭代器, 惰性展开多叉树]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 341. Flatten Nested List Iterator.
> Solution insights from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树的拓展延伸/拓展：惰性展开多叉树.md]]. The problem involves designing an iterator for a nested list structure, which can be viewed as an N-ary tree. Labuladong discusses a "lazy expansion" approach.

# LeetCode 341: Flatten Nested List Iterator

## Problem Statement

You are given a nested list of integers `nestedList`. Each element is either an integer or a list whose elements may also be integers or other lists. Implement an iterator to flatten it.

Implement the `NestedIterator` class:
- `NestedIterator(List<NestedInteger> nestedList)` Initializes the iterator with the nested list.
- `int next()` Returns the next integer in the flattened list.
- `boolean hasNext()` Returns `true` if there are still some integers in the flattened list, otherwise returns `false`.

Your code will be tested with the following pseudocode:
```
initialize iterator with nestedList
res = []
while iterator.hasNext()
    append iterator.next() to the end of res
return res
```

**Official Link:** [LeetCode 341. Flatten Nested List Iterator](https://leetcode.com/problems/flatten-nested-list-iterator/)

## Understanding the `NestedInteger` Interface (Conceptual)
The problem provides a `NestedInteger` interface:
```python
# This is the interface that NestedInteger objects will implement.
# You should not implement it, or speculate about its implementation
# class NestedInteger:
#    def isInteger(self) -> bool:
#        '''
#        @return True if this NestedInteger holds a single integer, rather than a nested list.
#        '''
#
#    def getInteger(self) -> int:
#        '''
#        @return the single integer that this NestedInteger holds, if it holds a single integer
#        Return None if this NestedInteger holds a nested list
#        '''
#
#    def getList(self) -> list['NestedInteger']:
#        '''
#        @return the nested list that this NestedInteger holds, if it holds a nested list
#        Return None if this NestedInteger holds a single integer
#        '''
```

## Solution Approach: Lazy Expansion with a Stack (DFS Simulation)

The core challenge is to provide the next integer on demand (`next()`) and correctly report if one exists (`hasNext()`). A "lazy" approach is often best: don't flatten the entire list upfront, as it might be very large or deep. Instead, find the next integer only when `hasNext()` or `next()` is called.

This can be achieved by simulating a Depth-First Search (DFS) iteratively using a stack. The stack will hold iterators (or simply lists/elements) of the nested lists.

**Labuladong's "惰性展开" (Lazy Expansion) Idea:**
Instead of pre-processing the entire `nestedList` into a flat list in the constructor (which is one valid approach but not lazy), we can use `hasNext()` to ensure the "next" element to be returned is an integer and is ready.

**Algorithm using a Stack of Lists (Iterators):**
1.  **Constructor (`__init__`)**:
    - Initialize a stack.
    - Push the initial `nestedList` onto the stack *in reverse order*. This is because we'll be `pop`ping from the stack, and we want to process elements from the original list's beginning. If we push `[item1, item2, item3]` as is, `item3` is popped first. Pushing `item3, item2, item1` ensures `item1` is processed first.
    Alternatively, push list iterators or use a deque and add to the front. Let's use a simple list for stack and push elements directly.

2.  **`hasNext()` (The Core Logic - Ensure Next Element is Integer):**
    - Loop while the stack is not empty:
        a. Get the `top` element from the stack.
        b. If `top.isInteger()` is `True`: We've found the next integer. Return `True`. (Do *not* pop it yet; `next()` will do that).
        c. If `top.isInteger()` is `False` (it's a list):
            i. Pop `top` (the list itself) from the stack because we are processing it now.
            ii. Get its `sub_list = top.getList()`.
            iii. Push elements of `sub_list` onto the stack *in reverse order* so they are processed in their original sequence.
    - If the stack becomes empty and no integer was found at the top, return `False`.

3.  **`next()`:**
    - Call `hasNext()` to ensure an integer is at the top of the stack and ready (this call is often implicit as per problem spec: `hasNext` is called before `next`).
    - If `hasNext()` was true, the top of the stack currently holds a `NestedInteger` that `isInteger()`.
    - Pop this `NestedInteger` from the stack.
    - Return its `getInteger()` value.

### Python Solution
```python
# '''
# This is the interface that allows for creating nested lists.
# You should not implement it, or speculate about its implementation
# '''
#class NestedInteger:
#    def isInteger(self) -> bool:
#        '''
#        @return True if this NestedInteger holds a single integer, rather than a nested list.
#        '''
#
#    def getInteger(self) -> int:
#        '''
#        @return the single integer that this NestedInteger holds, if it holds a single integer
#        Return None if this NestedInteger holds a nested list
#        '''
#
#    def getList(self) -> list['NestedInteger']:
#        '''
#        @return the nested list that this NestedInteger holds, if it holds a nested list
#        Return None if this NestedInteger holds a single integer
#        '''

class NestedIterator:
    def __init__(self, nestedList: list['NestedInteger']):
        # The stack will store NestedInteger objects.
        # We push lists (or their elements) in reverse to process them in forward order.
        self.stack = []
        # Initialize stack with the elements of the top-level list in reverse order.
        for i in range(len(nestedList) - 1, -1, -1):
            self.stack.append(nestedList[i])

    def next(self) -> int:
        # hasNext() should have already ensured the top is an integer.
        # If problem guarantees hasNext() is called before next(), we don't need to check again.
        # However, a robust iterator might call self.ensure_top_is_integer() or similar.
        # For this problem, LeetCode's test harness calls hasNext() first.

        # The top of the stack is guaranteed to be an integer by a preceding hasNext() call.
        current_integer_ni = self.stack.pop()
        return current_integer_ni.getInteger()

    def hasNext(self) -> bool:
        # This method does the "lazy expansion".
        # It ensures that if there's a next element, the top of the stack is an integer.
        while self.stack:
            top_element_ni = self.stack[-1] # Peek at the top
            if top_element_ni.isInteger():
                return True # Found an integer, ready for next()

            # It's a list, so expand it.
            # Pop the list itself, and push its elements (in reverse order) onto the stack.
            nested_list_to_expand = self.stack.pop().getList()
            for i in range(len(nested_list_to_expand) - 1, -1, -1):
                self.stack.append(nested_list_to_expand[i])

        return False # Stack is empty, no more elements
```

## Complexity Analysis
Let $N$ be the total number of integers in the `nestedList` and $L$ be the total number of lists (including nested ones).
-   **Constructor (`__init__`)**: $O(L_0)$, where $L_0$ is the number of elements in the initial `nestedList`.
-   **`hasNext()`**: In the worst case, this method might have to traverse deep into nested lists to find the next integer or determine that none exists. Over the entire course of iteration, each `NestedInteger` (both integers and lists) is pushed onto and popped from the stack at most once. Expanding a list involves pushing its elements. So, amortized cost for `hasNext()` when called repeatedly until the iterator is exhausted is complex to state per call, but the total work done by all `hasNext()` and `next()` calls together is proportional to the total number of integers and list objects.
    - Total time for all `hasNext` and `next` calls: $O(N+L)$. Each integer is returned once. Each list is expanded once.
-   **`next()`**: $O(1)$ on average, assuming `hasNext()` has already prepared an integer at the top of the stack. The `pop` operation is $O(1)$.
-   **Space Complexity:** $O(D_{max} + W_{max})$, where $D_{max}$ is the maximum nesting depth of the lists and $W_{max}$ is the maximum number of items in any single list. The stack can hold elements up to the deepest nesting level.

If we consider the "amortized" cost per element retrieved, it's efficient.

## Visualization (Conceptual Stack Operations)
Input: `[[1,1], 2, [3,[4]]]`

1.  `__init__`:
    - `stack = [[3,[4]], 2, [1,1]]` (pushed in reverse: `[1,1]` then `2` then `[3,[4]]`)

2.  `hasNext()` called:
    - Stack top is `[1,1]` (a list). Pop it.
    - Push its elements (1, 1) in reverse: `stack = [[3,[4]], 2, 1, 1]`
    - Stack top is `1` (an integer). Return `True`.

3.  `next()` called:
    - Pop `1`. Return `1`.
    - `stack = [[3,[4]], 2, 1]`

4.  `hasNext()` called:
    - Stack top is `1` (an integer). Return `True`.

5.  `next()` called:
    - Pop `1`. Return `1`.
    - `stack = [[3,[4]], 2]`

6.  `hasNext()` called:
    - Stack top is `2` (an integer). Return `True`.

7.  `next()` called:
    - Pop `2`. Return `2`.
    - `stack = [[3,[4]]]`

8.  `hasNext()` called:
    - Stack top is `[3,[4]]` (a list). Pop it.
    - Push its elements (`[4]`, then `3`) in reverse: `stack = [3, [4]]`
    - Stack top is `3` (an integer). Return `True`.

9.  `next()` called:
    - Pop `3`. Return `3`.
    - `stack = [[4]]`

10. `hasNext()` called:
    - Stack top is `[4]` (a list). Pop it.
    - Push its elements (`4`) in reverse: `stack = [4]`
    - Stack top is `4` (an integer). Return `True`.

11. `next()` called:
    - Pop `4`. Return `4`.
    - `stack = []`

12. `hasNext()` called:
    - Stack is empty. Return `False`.

Sequence returned: `1, 1, 2, 3, 4`.

## 总结 (Summary)
- The Flatten Nested List Iterator problem requires designing an iterator that can "flatten" a deeply nested list structure containing integers and other lists.
- A common and efficient approach is "lazy expansion" using a stack to simulate DFS.
- The `hasNext()` method is responsible for ensuring the top of the stack holds the next integer to be returned, by expanding any lists encountered at the top.
- `next()` simply pops and returns the integer prepared by `hasNext()`.
- This approach processes elements on demand, making it efficient for large or deeply nested structures.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Data Structures/Stack/index|Stacks]], [[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|N-ary Trees]] (as nested lists form a tree-like structure), [[Interview/Concept/Algorithms/Tree Traversal/02 - N-ary Tree Recursive Traversal (DFS)|Iterative DFS]]
