---
tags: [problem/leetcode, lc/medium, topic/dynamic_programming, lc/lc516, course/labuladong_mention]
aliases: [LC516, LeetCode 516]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/子序列类型问题/动态规划之子序列问题解题模板.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 516. Longest Palindromic Subsequence
> Mentioned in Labuladong's notes, related to [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/02 - Longest Palindromic Subsequence (LPS)|LPS Algorithm]].

# LeetCode 516: Longest Palindromic Subsequence

## Problem Statement
(To be filled from LeetCode - Visit [https://leetcode.com/problems/longest-palindromic-subsequence/](https://leetcode.com/problems/longest-palindromic-subsequence/))
*Note: Auto-generated URL might be incorrect if title doesn't match <PERSON><PERSON> slug.*

## Solution Approach
(To be filled, based on [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/02 - Longest Palindromic Subsequence (LPS)|LPS Algorithm]].)

### Python Solution (Placeholder)
```python
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None): self.val = val; self.left = left; self.right = right
# class ListNode:
#     def __init__(self, val=0, next=None): self.val = val; self.next = next
class Solution:
    def solve(self, params): # Signature will vary based on problem
        # TODO: Implement solution
        pass
```

## Complexity Analysis
(To be filled)

## 总结 (Summary)
(To be filled)

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
