---
tags: [problem/leetcode, lc/medium, topic/string, topic/hash_table, pattern/sliding_window, pattern/hashing, algorithm/rabin_karp, course/labuladong]
aliases: [LC187, LeetCode 187, Repeated DNA Sequences]
summary: |
  Find all 10-letter-long DNA sequences that occur more than once. 
  Can be solved using a sliding window and hashing substrings (either Python's built-in or custom like Rabin-Karp).
created: 2025-05-26T18:00:00.000-07:00
modified: 2025-05-26T18:00:00.000-07:00
---

> [!NOTE] Source Annotation
> Problem: LeetCode 187. Repeated DNA Sequences
> Mentioned in [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷数组算法/滑动窗口延伸：Rabin Karp 字符匹配算法.md|滑动窗口延伸：<PERSON><PERSON> Karp 字符匹配算法]].

# LeetCode 187: Repeated DNA Sequences

## Problem Statement

The DNA sequence is composed of a series of nucleotides abbreviated as `'A'`, `'C'`, `'G'`, and `'T'`.
For example, `"ACGAATTCCG"` is a DNA sequence.
When studying DNA, it is useful to identify repeated sequences within the DNA.
Given a string `s` that represents a DNA sequence, return all the **10-letter-long** sequences (substrings) that occur more than once in the DNA molecule. You may return the answer in **any order**.

**Official Link:** [LeetCode 187. Repeated DNA Sequences](https://leetcode.com/problems/repeated-dna-sequences/)

**Example 1:**
Input: `s = "AAAAACCCCCAAAAACCCCCCAAAAAGGGTTT"`
Output: `["AAAAACCCCC","CCCCCAAAAA"]`

## Solution Approach: Sliding Window + Hashing

This problem asks us to find all fixed-length (10) substrings that are repeated. This is a good candidate for a sliding window of size 10, combined with hashing to keep track of substrings encountered.

1.  **Sliding Window:** Iterate through `s` with a window of length 10.
2.  **Hashing Substrings:** For each substring in the window:
    -   Use a hash set `seen_substrings` to store unique substrings encountered so far.
    -   Use another hash set `repeated_substrings_result` to store the substrings that have been identified as repeats (to avoid adding the same repeated substring multiple times to the final result list).
3.  **Logic:**
    -   For each window (substring `sub`):
        -   If `sub` is in `seen_substrings`, it means we've seen it before, so it's a repeat. Add `sub` to `repeated_substrings_result`.
        -   If `sub` is not in `seen_substrings`, add it to `seen_substrings`.

### Python Solution (Using Python's string slicing and set hashing)
```python
import collections

class Solution:
    def findRepeatedDnaSequences(self, s: str) -> list[str]:
        L = 10  # Length of the DNA sequence to find
        n = len(s)

        if n <= L:
            return []

        seen_substrings = set()
        # Using a set for results ensures each repeated sequence is added only once
        repeated_substrings_result = set() 

        # Iterate with a sliding window of size L
        for i in range(n - L + 1):
            current_substring = s[i : i + L]
            
            if current_substring in seen_substrings:
                repeated_substrings_result.add(current_substring)
            else:
                seen_substrings.add(current_substring)
                
        return list(repeated_substrings_result)
```

### Rabin-Karp Based Optimization (Conceptual)
While the above solution is simple and often passes due to optimized string hashing in Python, a true [[Interview/Concept/Algorithms/String Matching/00 - Rabin-Karp Algorithm|Rabin-Karp]] approach would implement a rolling hash for the 10-letter window.
- Map 'A', 'C', 'G', 'T' to integers (e.g., 0, 1, 2, 3). Use base `R=4`.
- Calculate hash for the first 10-letter window.
- Slide the window, using rolling hash to update the hash value in $O(1)$.
- Store hash values in a set (`seen_hashes`) and identify duplicates for a secondary set (`repeated_hashes`).
- If a hash collision occurs or a hash is identified as repeated, then verify the actual substring to avoid false positives from hash collisions (though with good hashing, this is rare, and for this problem, one might even risk not verifying if problem constraints allow, or if the hash space is large enough). For LeetCode, usually, substring verification or storing actual substrings after hash match is safer.

```python
# Conceptual Rabin-Karp logic addition for LC187
# class SolutionRabinKarp:
#     def findRepeatedDnaSequences(self, s: str) -> list[str]:
#         L = 10
#         N = len(s)
#         if N <= L: return []

#         # Map chars to int
#         mapping = {'A': 0, 'C': 1, 'G': 2, 'T': 3}
#         R = 4 # Base for hash

#         # Modulus for hash (large prime)
#         Q = (1 << 31) - 1 # Example large prime (Mersenne prime 2^31 - 1)
        
#         RL = pow(R, L - 1, Q) # R^(L-1) % Q

#         hashes = set()
#         result_set = set()

#         current_hash = 0
#         # Calculate hash for the first window s[0...L-1]
#         for i in range(L):
#             current_hash = (current_hash * R + mapping[s[i]]) % Q
        
#         hashes.add(current_hash)

#         # Slide window from L to N-1
#         for i in range(L, N):
#             # Rolling hash: subtract s[i-L], add s[i]
#             val_out = mapping[s[i-L]]
#             val_in = mapping[s[i]]
            
#             current_hash = (current_hash - val_out * RL) % Q
#             current_hash = (current_hash + Q) % Q # Ensure positive
#             current_hash = (current_hash * R + val_in) % Q
            
#             if current_hash in hashes:
#                 # Potential repeat. For this problem, string itself is key.
#                 # A simple hash set might lead to collision if Q is not large enough
#                 # or hash function is not perfect.
#                 # Usually, one might store the string in a secondary check or use hash -> list of start_indices.
#                 # For LC, often direct string comparison is done upon hash match or simply
#                 # add the string s[i-L+1 : i+1] to result_set if its hash is already seen.
#                 # The simpler Pythonic solution above using set of strings is often sufficient.
#                 # If we trust the hash enough (or problem allows for it):
#                 result_set.add(s[i-L+1 : i+1]) # Add the actual substring
#             else:
#                 hashes.add(current_hash)
        
#         return list(result_set)
```
The simpler Python string-set solution is generally preferred for its clarity unless rolling hash is explicitly required for performance on extremely long strings or custom alphabet constraints.

## Complexity Analysis
**Simple String Hashing Solution:**
-   **Time Complexity:** $O((N-L+1) \cdot L)$.
    -   Iterating $N-L+1$ times for the window.
    -   Each substring slicing `s[i : i+L]` takes $O(L)$.
    -   Set insertion and lookup of strings of length $L$ take $O(L)$ on average (due to hashing and comparison of strings).
    -   So, total is $O((N-L) \cdot L)$. Given $L=10$, this is effectively $O(N)$.
-   **Space Complexity:** $O((N-L) \cdot L)$ in the worst case if all substrings are unique and stored in `seen_substrings`.

**Rabin-Karp Rolling Hash Solution:**
-   **Time Complexity:** $O(N-L+1)$ on average, i.e., $O(N)$.
    -   Initial hash: $O(L)$.
    -   Each rolling hash update: $O(1)$.
    -   Set operations on integer hashes: $O(1)$ average.
    -   If string verification is needed on hash collision, it adds $O(L)$ per collision. With good hashing, collisions are rare.
-   **Space Complexity:** $O(N-L)$ for storing integer hashes or $O((N-L) \cdot L)$ if storing actual repeated substrings.

## 总结 (Summary)
- LC187 asks for repeated 10-letter DNA sequences.
- A sliding window of size 10 is used to examine all such substrings.
- Hashing (either Python's built-in for strings or a custom rolling hash like Rabin-Karp) is used to efficiently track seen substrings and identify duplicates.
- The straightforward solution using a set of strings is often sufficient and clear for typical LeetCode constraints for this problem.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/String Matching/00 - Rabin-Karp Algorithm|Rabin-Karp Algorithm]], [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window]]
