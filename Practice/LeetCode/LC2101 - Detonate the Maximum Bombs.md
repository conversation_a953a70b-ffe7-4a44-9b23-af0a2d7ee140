---
tags: [problem/leetcode, lc/medium, topic/graph, topic/dfs, pattern/graph_traversal, pattern/connected_components]
aliases: [LC2101, LeetCode 2101, Detonate the Maximum Bombs, 引爆最多的炸弹]
---

# LeetCode 2101: Detonate the Maximum Bombs

## Problem Statement

You are given a list of bombs. The **range** of a bomb is defined as the area where its effect can be felt. The range is circular, and the radius of this circle is given.

When a bomb is detonated, it will detonate **all bombs** that lie in its range. These newly detonated bombs will further detonate the bombs that lie in their ranges.

Given the list of bombs, return *the maximum number of bombs that can be detonated if you are allowed to detonate **only one** bomb manually*.

**Official Link:** [LeetCode 2101. Detonate the Maximum Bombs](https://leetcode.com/problems/detonate-the-maximum-bombs/)

## 💥 Understanding the Chain Reaction Problem

### The Real-World Scenario

Imagine you're a demolition expert planning the most efficient way to clear a field of bombs:

```tikz
\begin{tikzpicture}[
    bomb/.style={circle, draw, minimum size=0.8cm, font=\tiny, fill=red!40},
    blast_radius/.style={circle, draw, dashed, thick, blue},
    chain_arrow/.style={->, thick, red, line width=2pt},
    example_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=4cm, align=center}
]

% Example bomb field
\node at (6, 7) {\bfseries Bomb Field: Chain Reaction Scenario};

% Bombs with their blast radii
\node[bomb] (b1) at (2, 5) {B1};
\node[blast_radius] at (2, 5) [minimum size=2.5cm] {};

\node[bomb] (b2) at (4, 4.5) {B2};
\node[blast_radius] at (4, 4.5) [minimum size=1.8cm] {};

\node[bomb] (b3) at (6, 5.5) {B3};
\node[blast_radius] at (6, 5.5) [minimum size=2cm] {};

\node[bomb] (b4) at (8, 4) {B4};
\node[blast_radius] at (8, 4) [minimum size=1.5cm] {};

\node[bomb] (b5) at (3, 2.5) {B5};
\node[blast_radius] at (3, 2.5) [minimum size=1.2cm] {};

% Chain reaction arrows
\draw[chain_arrow] (b1) -- (b2);
\draw[chain_arrow] (b2) -- (b3);
\draw[chain_arrow] (b3) -- (b4);

\node[example_box] at (10, 5) {
    \textbf{Goal:}\\
    Find which bomb to\\
    detonate manually to\\
    trigger the maximum\\
    chain reaction
};

\node[example_box] at (2, 1) {
    \textbf{Key Insight:}\\
    This is a graph problem!\\
    Bombs = nodes\\
    Blast reach = edges
};

\end{tikzpicture}
```

**Critical Insight:** This is a **[[Graph Traversal]]** problem disguised as a geometry problem. Each bomb can trigger others within its blast radius, forming a directed graph.

## 🧠 The Breakthrough: From Geometry to Graph Theory

### Step 1: Problem Transformation

The key realization is to model this as a graph connectivity problem:

```tikz
\begin{tikzpicture}[
    insight_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4.5cm, align=center, minimum height=2.5cm},
    arrow/.style={->, thick, red, line width=2pt},
    strategy_box/.style={rectangle, draw, fill=orange!20, font=\sffamily\small, text width=6cm, align=center}
]

\node[insight_box] (geometry) at (0, 3) {
    \textbf{Geometry View:}\\[0.5em]
    "Calculate distances\\
    between bomb positions\\
    and check blast radii"\\[0.5em]
    Complex spatial reasoning!
};

\node[insight_box] (graph) at (8, 3) {
    \textbf{Graph View:}\\[0.5em]
    "Build directed graph\\
    where edges represent\\
    'can trigger' relationships"\\[0.5em]
    Standard graph traversal!
};

\draw[arrow] (geometry) -- (graph);

\node[strategy_box] at (4, 0.5) {
    \textbf{Transformation Strategy:}\\
    1. Build graph: bomb A → bomb B if A can detonate B\\
    2. For each bomb: count reachable bombs via DFS/BFS\\
    3. Return maximum count across all starting bombs
};

\end{tikzpicture}
```

### Step 2: Graph Construction Logic

Understanding how to build the directed graph:

```tikz
\begin{tikzpicture}[
    bomb_node/.style={circle, draw, minimum size=1cm, font=\sffamily\small, fill=red!30},
    edge_arrow/.style={->, thick, blue},
    formula_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=4cm, align=center}
]

% Graph representation
\node at (6, 6) {\bfseries Graph Construction: Blast Relationships};

% Bombs as graph nodes
\node[bomb_node] (a) at (1, 4) {A};
\node[bomb_node] (b) at (4, 4) {B};
\node[bomb_node] (c) at (7, 4) {C};
\node[bomb_node] (d) at (4, 2) {D};

% Directed edges (who can trigger whom)
\draw[edge_arrow] (a) -- (b);
\draw[edge_arrow] (b) -- (c);
\draw[edge_arrow] (b) -- (d);
\draw[edge_arrow] (c) to[bend left] (a);

% Distance formula
\node[formula_box] at (1, 1) {
    \textbf{Edge Condition:}\\
    distance(A, B) $<=$ radius\_A\\[0.3em]
    Then A → B exists\\
    (A can detonate B)
};

\node[formula_box] at (7, 1) {
    \textbf{Distance Formula:}\\
    sqrt((x2-x1)² + (y2-y1)²)\\[0.3em]
    Standard Euclidean\\
    distance
};

\end{tikzpicture}
```

**Key Insight:** The graph is **directed** because bomb A might detonate bomb B, but B might not be able to detonate A (different blast radii).

## 🔍 Progressive Problem Analysis

### Step 3: Algorithm Strategy

Once we have the graph, the problem becomes a standard traversal:

```tikz
\begin{tikzpicture}[
    step_box/.style={rectangle, draw, rounded corners, fill=green!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=2cm},
    arrow/.style={->, thick, purple}
]

\node[step_box] (build) at (0, 3) {
    \textbf{Step 1: Build Graph}\\[0.5em]
    For each pair of bombs,\\
    check if one can\\
    detonate the other\\
    based on distance
};

\node[step_box] (traverse) at (4.5, 3) {
    \textbf{Step 2: Traverse}\\[0.5em]
    For each bomb as\\
    starting point, use\\
    DFS/BFS to count\\
    all reachable bombs
};

\node[step_box] (maximize) at (9, 3) {
    \textbf{Step 3: Maximize}\\[0.5em]
    Return the maximum\\
    count found across\\
    all possible starting\\
    bombs
};

\draw[arrow] (build) -- (traverse);
\draw[arrow] (traverse) -- (maximize);

\node at (4.5, 1) {\bfseries Transform → Traverse → Optimize};

\end{tikzpicture}
```

## 💡 Traditional Implementation: Step by Step

### Step 1: Graph Construction

```python
def build_graph(bombs):
    """
    Build directed graph where bombs[i] -> bombs[j]
    if bomb i can detonate bomb j.

    Returns adjacency list representation.
    """
    n = len(bombs)
    graph = [[] for _ in range(n)]

    for i in range(n):
        x1, y1, r1 = bombs[i]
        for j in range(n):
            if i == j:
                continue

            x2, y2, r2 = bombs[j]
            # Check if bomb i can detonate bomb j
            distance_squared = (x2 - x1) ** 2 + (y2 - y1) ** 2

            # Use squared distance to avoid floating point precision issues
            if distance_squared <= r1 ** 2:
                graph[i].append(j)

    return graph
```

### Step 2: DFS Traversal

```python
def count_detonated_bombs(graph, start):
    """
    Count total bombs that can be detonated starting from 'start' bomb.
    Uses DFS to explore all reachable bombs.
    """
    visited = set()

    def dfs(bomb_idx):
        if bomb_idx in visited:
            return

        visited.add(bomb_idx)

        # Explore all bombs this one can detonate
        for neighbor in graph[bomb_idx]:
            dfs(neighbor)

    dfs(start)
    return len(visited)
```

### Step 3: Complete Solution

```python
class Solution:
    def maximumDetonation(self, bombs: List[List[int]]) -> int:
        """
        Traditional approach: Build graph + DFS traversal

        Time: O(n² + n²) = O(n²) for graph building + traversal
        Space: O(n²) for adjacency list
        """
        n = len(bombs)

        # Step 1: Build directed graph
        graph = [[] for _ in range(n)]

        for i in range(n):
            x1, y1, r1 = bombs[i]
            for j in range(n):
                if i == j:
                    continue

                x2, y2, r2 = bombs[j]
                distance_squared = (x2 - x1) ** 2 + (y2 - y1) ** 2

                if distance_squared <= r1 ** 2:
                    graph[i].append(j)

        # Step 2: Try each bomb as starting point
        max_detonated = 0

        for start in range(n):
            # Count bombs reachable from this starting bomb
            visited = set()

            def dfs(bomb_idx):
                if bomb_idx in visited:
                    return
                visited.add(bomb_idx)
                for neighbor in graph[bomb_idx]:
                    dfs(neighbor)

            dfs(start)
            max_detonated = max(max_detonated, len(visited))

        return max_detonated
```

## 🐍 The Pythonic Way: Elegant and Concise

### Understanding Python's Graph Tools

Python provides elegant tools that make graph problems much cleaner:

```tikz
\begin{tikzpicture}[
    insight_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2cm},
    comparison_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=5cm, align=center}
]

\node[insight_box] (traditional) at (0, 3) {
    \textbf{Traditional Approach:}\\[0.5em]
    "Manual graph building\\
    with nested loops and\\
    explicit DFS recursion"\\[0.3em]
    \textit{Verbose but educational}
};

\node[insight_box] (pythonic) at (8, 3) {
    \textbf{Pythonic Approach:}\\[0.5em]
    "Use comprehensions,\\
    itertools, and built-in\\
    graph libraries"\\[0.3em]
    \textit{Concise and readable}
};

\node[comparison_box] at (4, 0.5) {
    \textbf{Key Insight:}\\
    Python's expressive syntax and standard library\\
    can dramatically simplify graph algorithms\\[0.3em]
    \textit{Focus on logic, not implementation details}
};

\end{tikzpicture}
```

### Approach 1: Comprehension-Heavy Style

```python
from itertools import combinations

class Solution:
    def maximumDetonation(self, bombs: List[List[int]]) -> int:
        """
        Pythonic approach using comprehensions and modern Python features.

        Key improvements:
        - Graph building with comprehensions
        - Cleaner distance calculation
        - More readable DFS
        """
        n = len(bombs)

        # Build graph using comprehension - more Pythonic
        def can_detonate(i, j):
            x1, y1, r1 = bombs[i]
            x2, y2, r2 = bombs[j]
            return (x2 - x1) ** 2 + (y2 - y1) ** 2 <= r1 ** 2

        # Adjacency list using comprehension
        graph = {
            i: [j for j in range(n) if i != j and can_detonate(i, j)]
            for i in range(n)
        }

        def count_reachable(start):
            """Count reachable bombs using iterative DFS with set operations"""
            visited = set()
            stack = [start]

            while stack:
                current = stack.pop()
                if current not in visited:
                    visited.add(current)
                    # Extend stack with unvisited neighbors
                    stack.extend(neighbor for neighbor in graph[current]
                               if neighbor not in visited)

            return len(visited)

        # Find maximum using generator expression
        return max(count_reachable(i) for i in range(n))
```

### Approach 2: Functional Style with NetworkX

```python
import networkx as nx
from math import sqrt

class Solution:
    def maximumDetonation(self, bombs: List[List[int]]) -> int:
        """
        Ultra-Pythonic approach using NetworkX library.

        Benefits:
        - Leverages specialized graph library
        - Built-in traversal algorithms
        - Very readable and maintainable
        """
        # Create directed graph
        G = nx.DiGraph()

        # Add nodes (bombs)
        G.add_nodes_from(range(len(bombs)))

        # Add edges (detonation relationships)
        for i, (x1, y1, r1) in enumerate(bombs):
            for j, (x2, y2, r2) in enumerate(bombs):
                if i != j:
                    distance = sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
                    if distance <= r1:
                        G.add_edge(i, j)

        # Find maximum reachable nodes from any starting point
        return max(
            len(nx.descendants(G, start)) + 1  # +1 for the starting bomb itself
            for start in range(len(bombs))
        )
```

### Approach 3: Modern Python with Type Hints

```python
from typing import List, Set, Dict
from collections import deque

class Solution:
    def maximumDetonation(self, bombs: List[List[int]]) -> int:
        """
        Modern Python approach with type hints and clean structure.

        Features:
        - Type hints for better code documentation
        - BFS instead of DFS (often clearer for counting)
        - Clean separation of concerns
        """

        def build_detonation_graph(bombs: List[List[int]]) -> Dict[int, List[int]]:
            """Build directed graph of bomb detonation relationships."""
            n = len(bombs)
            graph = {i: [] for i in range(n)}

            for i, (x1, y1, r1) in enumerate(bombs):
                for j, (x2, y2, r2) in enumerate(bombs):
                    if i != j and (x2 - x1) ** 2 + (y2 - y1) ** 2 <= r1 ** 2:
                        graph[i].append(j)

            return graph

        def count_chain_reaction(graph: Dict[int, List[int]], start: int) -> int:
            """Count total bombs in chain reaction starting from given bomb."""
            visited: Set[int] = set()
            queue = deque([start])

            while queue:
                current = queue.popleft()
                if current not in visited:
                    visited.add(current)
                    queue.extend(
                        neighbor for neighbor in graph[current]
                        if neighbor not in visited
                    )

            return len(visited)

        # Main algorithm
        graph = build_detonation_graph(bombs)
        return max(
            count_chain_reaction(graph, start)
            for start in range(len(bombs))
        )
```

## 🔍 Visual Algorithm Trace

Let's trace through a concrete example with bombs = [[2,1,3],[6,1,4]]:

```tikz
\begin{tikzpicture}[
    bomb_node/.style={circle, draw, minimum size=1.2cm, font=\sffamily\small, fill=red!30},
    blast_circle/.style={circle, draw, dashed, thick, blue},
    step_box/.style={rectangle, draw, fill=yellow!20, font=\tiny, text width=3cm, align=center},
    arrow/.style={->, thick, green}
]

% Step 1: Visualize bombs and blast radii
\node at (6, 8) {\bfseries Algorithm Trace: bombs = [[2,1,3],[6,1,4]]};

\node at (2, 7) {\small Step 1: Visualize Bombs};
\node[bomb_node] (b0) at (2, 5.5) {B0\\(2,1)};
\node[blast_circle] at (2, 5.5) [minimum size=3cm] {};
\node at (2, 4) {\tiny radius = 3};

\node[bomb_node] (b1) at (6, 5.5) {B1\\(6,1)};
\node[blast_circle] at (6, 5.5) [minimum size=4cm] {};
\node at (6, 4) {\tiny radius = 4};

% Step 2: Check distances
\node at (2, 3) {\small Step 2: Check Distances};
\node[step_box] at (1, 2) {
    \textbf{B0 → B1:}\\
    dist = sqrt((6-2)² + (1-1)²)\\
    = sqrt(16) = 4\\
    4 > 3? NO\\
    No edge B0 → B1
};

\node[step_box] at (5, 2) {
    \textbf{B1 → B0:}\\
    dist = sqrt((2-6)² + (1-1)²)\\
    = sqrt(16) = 4\\
    4 $<=$ 4? YES\\
    Edge B1 → B0 exists
};

% Step 3: Graph representation
\node at (8, 3) {\small Step 3: Graph};
\node[bomb_node] (g0) at (8, 2) {B0};
\node[bomb_node] (g1) at (10, 2) {B1};
\draw[arrow] (g1) -- (g0);

% Step 4: DFS results
\node at (2, 0.5) {\small Step 4: DFS Results};
\node[step_box] at (1, -0.5) {
    \textbf{Start B0:}\\
    Reachable: {B0}\\
    Count: 1
};

\node[step_box] at (5, -0.5) {
    \textbf{Start B1:}\\
    Reachable: {B1, B0}\\
    Count: 2
};

\node[step_box, fill=green!30] at (8, -0.5) {
    \textbf{Result:}\\
    Maximum = 2\\
    (Start from B1)
};

\end{tikzpicture}
```

**Key Observations:**
1. **Distance calculation** determines graph edges
2. **Direction matters** - B1 can detonate B0, but not vice versa
3. **DFS/BFS** counts all reachable bombs in chain reaction
4. **Try all starting points** to find maximum

## 📚 Educational Philosophy: Multiple Approaches, Deep Learning

### When to Use Each Approach

```tikz
\begin{tikzpicture}[
    approach_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4cm, align=center, minimum height=3cm},
    context_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=5cm, align=center}
]

\node[approach_box] (traditional) at (0, 3) {
    \textbf{Traditional Approach}\\[0.5em]
    \textbf{When to use:}\\
    • Technical interviews\\
    • Learning graph algorithms\\
    • Performance-critical code\\
    • Custom requirements\\[0.5em]
    \textbf{Benefits:}\\
    • Shows algorithm knowledge\\
    • Full control over implementation\\
    • Educational value
};

\node[approach_box] (pythonic) at (8, 3) {
    \textbf{Pythonic Approaches}\\[0.5em]
    \textbf{When to use:}\\
    • Production code\\
    • Rapid prototyping\\
    • Team collaboration\\
    • Maintainable systems\\[0.5em]
    \textbf{Benefits:}\\
    • Readable and concise\\
    • Leverages libraries\\
    • Less error-prone
};

\node[context_box] at (4, 0.5) {
    \textbf{Educational Insight:}\\
    Understanding the traditional approach helps you appreciate\\
    the power of Python's expressive syntax and libraries\\[0.3em]
    \textit{Master the fundamentals, then leverage the tools}
};

\end{tikzpicture}
```

### The Learning Journey

```tikz
\begin{tikzpicture}[
    stage_box/.style={rectangle, draw, rounded corners, fill=green!20, font=\sffamily\small, text width=3cm, align=center, minimum height=2cm},
    arrow/.style={->, thick, purple, line width=2pt}
]

\node[stage_box] (understand) at (0, 3) {
    \textbf{Stage 1:}\\
    \textbf{Understand}\\[0.5em]
    Recognize this as\\
    a graph problem\\
    disguised as geometry
};

\node[stage_box] (implement) at (4, 3) {
    \textbf{Stage 2:}\\
    \textbf{Implement}\\[0.5em]
    Build graph manually\\
    and implement DFS\\
    from scratch
};

\node[stage_box] (optimize) at (8, 3) {
    \textbf{Stage 3:}\\
    \textbf{Optimize}\\[0.5em]
    Use Python idioms\\
    and libraries for\\
    cleaner solutions
};

\draw[arrow] (understand) -- (implement);
\draw[arrow] (implement) -- (optimize);

\node at (4, 1) {\bfseries The journey from problem recognition to elegant implementation};

\end{tikzpicture}
```

### Performance Comparison

| Aspect | Traditional | Comprehensions | NetworkX | Modern Python |
|--------|-------------|----------------|----------|---------------|
| **Time Complexity** | O(n²) | O(n²) | O(n²) | O(n²) |
| **Space Complexity** | O(n²) | O(n²) | O(n²) | O(n²) |
| **Code Length** | ~40 lines | ~25 lines | ~15 lines | ~30 lines |
| **Readability** | Medium | High | Very High | High |
| **Dependencies** | None | None | NetworkX | None |
| **Interview Suitability** | Excellent | Good | Poor | Good |
| **Production Use** | Good | Excellent | Good | Excellent |
| **Learning Value** | High | Medium | Low | Medium |

## 🧠 Key Takeaways & Problem-Solving Framework

### Why This Problem is Educational

```tikz
\begin{tikzpicture}[
    principle_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=3.5cm, align=center, minimum height=1.8cm},
    arrow/.style={->, thick, green}
]

\node[principle_box] (recognition) at (0, 3) {
    \textbf{Problem Recognition}\\[0.3em]
    Transform geometric\\
    problem into graph\\
    traversal problem
};

\node[principle_box] (modeling) at (4.5, 3) {
    \textbf{Graph Modeling}\\[0.3em]
    Build directed graph\\
    based on geometric\\
    relationships
};

\node[principle_box] (optimization) at (9, 3) {
    \textbf{Algorithm Choice}\\[0.3em]
    Use DFS/BFS to find\\
    connected components\\
    and maximize count
};

\draw[arrow] (recognition) -- (modeling);
\draw[arrow] (modeling) -- (optimization);

\node at (4.5, 1.5) {\bfseries Core Learning: Transform → Model → Traverse};

\end{tikzpicture}
```

### Problem-Solving Pattern: [[Graph Traversal]]

This problem exemplifies **geometric-to-graph transformation**:

1. **Problem recognition**: Identify graph structure in geometric problem
2. **Graph construction**: Build adjacency relationships based on constraints
3. **Traversal strategy**: Use DFS/BFS to explore connected components
4. **Optimization**: Try all starting points to find maximum

### Complexity Analysis
- **Time Complexity:** O(n²) - building graph + traversal for each starting point
- **Space Complexity:** O(n²) - adjacency list storage

### Related Concepts for Obsidian

This problem connects to several important concepts:

- **[[Graph Traversal]]**: Core DFS/BFS algorithms
- **[[Connected Components]]**: Finding reachable nodes
- **[[Geometric Algorithms]]**: Distance calculations and spatial relationships
- **[[Problem Transformation]]**: Converting between problem domains
- **[[Directed Graphs]]**: Understanding asymmetric relationships

### Related Problems
- **LC200. Number of Islands**: Connected components in grid
- **LC547. Number of Provinces**: Undirected graph connected components
- **LC1020. Number of Enclaves**: Boundary-connected components
- **LC1319. Number of Operations to Make Network Connected**: Graph connectivity

This problem beautifully demonstrates how **problem transformation** can reveal the underlying algorithmic structure, making complex geometric problems solvable with standard graph techniques!
```
