---
tags: [index, practice/linked_list, pattern/two_pointers, course/labuladong]
aliases: [Linked List Two Pointer Exercises]
---

> [!NOTE] Source Annotation
> This page is inspired by [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷链表算法/【练习】链表双指针经典习题.md]].
> It serves as a collection of exercises focusing on two-pointer techniques for linked lists.

# Linked List: Two Pointer Classic Exercises

This section aggregates classic LeetCode problems that are effectively solved using two-pointer techniques on linked lists. Refer to [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]] for core concepts.

## Problems

- [[Interview/Practice/LeetCode/LC21 - Merge Two Sorted Lists|LC21 - Merge Two Sorted Lists]]
- [[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Sorted Lists]] (Uses list merging as a sub-problem)
- [[Interview/Practice/LeetCode/LC25 - Reverse Nodes in k-Group|LC25 - Reverse Nodes in k-Group]]
- [[Interview/Practice/LeetCode/LC86 - Partition List|LC86 - Partition List]]
- [[Interview/Practice/LeetCode/LC92 - Reverse Linked List II|LC92 - Reverse Linked List II]]
- [[Interview/Practice/LeetCode/LC141 - Linked List Cycle|LC141 - Linked List Cycle]]
- [[Interview/Practice/LeetCode/LC142 - Linked List Cycle II|LC142 - Linked List Cycle II]]
- [[Interview/Practice/LeetCode/LC160 - Intersection of Two Linked Lists|LC160 - Intersection of Two Linked Lists]]
- [[Interview/Practice/LeetCode/LC206 - Reverse Linked List|LC206 - Reverse Linked List]]
- [[Interview/Practice/LeetCode/LC234 - Palindrome Linked List|LC234 - Palindrome Linked List]]
- [[Interview/Practice/LeetCode/LC876 - Middle of the Linked List|LC876 - Middle of the Linked List]]
- [[Interview/Practice/LeetCode/LC19 - Remove Nth Node From End of List|LC19 - Remove Nth Node From End of List]]
- *(Sword Offer 22 is similar to LC19)*

---
Parent: [[Interview/Practice/index|Practice Problems]]
