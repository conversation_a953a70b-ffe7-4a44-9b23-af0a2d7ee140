---
tags: [index, practice/topic_exercises, topic/bfs, course/labuladong]
aliases: [BFS Exercises I, BFS 经典习题 I]
source_file_path: 【练习】BFS 经典习题 I.md
---

# BFS Classic Exercises I

> [!NOTE] Source Annotation
> This page is inspired by Labuladong's "【练习】BFS 经典习题 I.md".
> It serves as a collection point for exercises focusing on Breadth-First Search.

This page lists exercises related to [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Algorithm Framework]].

Labuladong's guide for this section (`loading...`) would typically list specific LeetCode problems or other exercises that are good examples of BFS applications.

## Potential Problem Categories / Examples (Illustrative)

-   **Shortest Path in Unweighted Graphs:**
    -   Finding min steps in a maze.
    -   Minimum transformations (e.g., Word Ladder).
    -   [[Interview/Practice/LeetCode/LC752 - Open the Lock|LC752 - Open the Lock]]
    -   [[Interview/Practice/LeetCode/LC773 - Sliding Puzzle|LC773 - Sliding Puzzle]]
-   **Level-Order Traversal and Variants:**
    -   Binary tree level order, zigzag, right side view.
    -   See [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Binary Tree Level-Order Traversal (BFS)]].
-   **Connectivity / Reachability:**
    -   Determining if a path exists (though DFS is also common).

*(Specific problem links and detailed solutions would be added based on Labuladong's actual content for this exercise page.)*

---
Parent: [[Interview/Practice/Topic Exercises/index|Topic Exercises Index]]
