---
tags: [index, practice/topic_exercises]
aliases: [Topic Exercises Index, Algorithm Practice by Topic]
---

# Algorithm Topic Exercises

This section organizes LeetCode problems and other exercises by specific algorithmic topics or patterns.

- [[Interview/Practice/Topic Exercises/BFS_Exercises_Index_I|BFS Exercises I]]
- [[Interview/Practice/Topic Exercises/BFS_Exercises_Index_II|BFS Exercises II]]
- [[Interview/Practice/Topic Exercises/Monotonic_Stack_Exercises_Index|Monotonic Stack Exercises]]
- [[Interview/Practice/Topic Exercises/Valid_Parentheses_Exercises_Index|Valid Parentheses Exercises]]
- [[Interview/Practice/Topic Exercises/Monotonic_Queue_Exercises_Index|Monotonic Queue Exercises]]
- [[Interview/Practice/Topic Exercises/Queue_Classic_Exercises_Index|Queue Classic Exercises]]
- [[Interview/Practice/Topic Exercises/Stack_Classic_Exercises_Index|Stack Classic Exercises]]
- [[Interview/Practice/Topic Exercises/Union-Find_Exercises_Index|Union-Find Exercises]]
- [[Interview/Practice/Topic Exercises/Dijkstra_Algorithm_Exercises_Index|Dijkstra Algorithm Exercises]]
- [[Interview/Practice/Topic Exercises/Classic_Design_Exercises_Index|Classic Design Exercises]]
- [[Interview/Practice/Topic Exercises/Hash_Table_Exercises_Index|Hash Table Exercises]]
- [[Interview/Practice/Topic Exercises/Trie_Tree_Exercises_Index|Trie Tree Exercises]]
- [[Interview/Practice/Topic Exercises/Segment_Tree_Exercises_Index|Segment Tree Exercises]]
- [[Interview/Practice/Topic Exercises/Backtracking/index|Backtracking Exercises Collection]]


---
Parent: [[Interview/Practice/index|Practice Problems]]
