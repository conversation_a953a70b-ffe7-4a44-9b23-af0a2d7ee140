---
tags: [index, practice/topic_exercises, topic/bfs, course/labuladong]
aliases: [BFS Exercises II, BFS 经典习题 II]
source_file_path: 【练习】BFS 经典习题 II.md
---

# BFS Classic Exercises II

> [!NOTE] Source Annotation
> This page is inspired by Labuladong's "【练习】BFS 经典习题 II.md".
> It serves as a collection point for further exercises focusing on Breadth-First Search.

This page lists additional exercises related to the [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Algorithm Framework]].

Labuladong's guide for this section (`loading...`) would typically continue with more advanced or varied BFS problems.

## Potential Problem Categories / Examples (Illustrative)

-   **State-Space Search with Complex States:**
    -   Problems where the "state" in BFS is more than just coordinates (e.g., includes current resources, time, or other constraints).
    -   [[Interview/Practice/Games/华容道游戏|华容道游戏 (Huarong Road/Klotski)]] (if not covered elsewhere)
    -   [[Interview/Practice/Games/连连看游戏|连连看游戏 (Link Game)]]
-   **Bidirectional BFS Applications:**
    -   Problems where bidirectional search offers significant speedup.
-   **BFS on Grids with Multiple Sources/Targets:**
    -   E.g., finding shortest distance from multiple '1's to all '0's in a grid (01 Matrix type problems).

*(Specific problem links and detailed solutions would be added based on Labuladong's actual content for this exercise page.)*

---
Parent: [[Interview/Practice/Topic Exercises/index|Topic Exercises Index]]
