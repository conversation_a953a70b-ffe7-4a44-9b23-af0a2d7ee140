---
tags: [index, practice/topic_exercises, topic/hash, course/labuladong]
aliases: [Hash Table Problems, HashMap HashSet Exercises]
---

> [!NOTE] Source Annotation
> This index page is based on [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/【练习】哈希表更多习题.md|【练习】哈希表更多习题 by Labuladong]].

# Hash Table Exercises Collection

This page lists exercises related to Hash Tables (HashMaps and HashSets).
Refer to [[Interview/Concept/Data Structures/Hash Table/index|Hash Table Concepts]] for principles.
This section is currently a placeholder in Labuladong's guide (`loading...`).
Consider problems like:
- [[Interview/Practice/LeetCode/LC380 - Insert Delete GetRandom O(1)|LC380 - Insert Delete GetRandom O(1)]] (uses HashMap + Array)
- [[Interview/Practice/LeetCode/LC710 - Random Pick with Blacklist|LC710 - Random Pick with Blacklist]] (uses HashMap for remapping)
- Standard frequency counting, two-sum, duplicate detection problems.


---
Parent: [[Interview/Practice/index|Practice Problems]]
