---
tags: [index, practice/topic_exercises, topic/priority, course/labuladong]
aliases: [Heap Exercises, Priority Queue Problems]
---

> [!NOTE] Source Annotation
> This index page is based on [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/【练习】优先级队列经典习题.md|【练习】优先级队列经典习题 by Labuladong]].

# Priority Queue Classic Exercises

This page lists exercises related to Priority Queues (often implemented with Heaps).
Refer to [[Interview/Concept/Data Structures/Heap/index|Heap and Priority Queue Concepts]].
Labuladong's guide (`loading...`) mentions that PQs are for "dynamic sorting" and categorizes problems:
- Type 1: Merging multiple ordered sequences.
  - Example: [[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Sorted Lists]]
- Other types might include Top K problems, event simulation, <PERSON><PERSON><PERSON>'s algorithm, etc.


---
Parent: [[Interview/Practice/index|Practice Problems]]
