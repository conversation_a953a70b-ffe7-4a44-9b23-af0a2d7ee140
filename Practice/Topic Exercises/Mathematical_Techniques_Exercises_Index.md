---
tags: [index, practice/topic_exercises, topic/mathematical_techniques, course/labuladong]
aliases: [Mathematical Techniques Exercises, Math Algorithm Problems]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/数学运算技巧/【练习】数学技巧相关习题.md
---

> [!NOTE] Source Annotation
> This index page is inspired by Labuladong's "【练习】数学技巧相关习题.md", which is marked "loading...".
> It serves as a collection point for exercises leveraging mathematical insights, number theory, probability, etc.

# Mathematical Techniques: Exercises Collection

This page lists exercises related to applying mathematical techniques and insights to solve algorithmic problems. Refer to [[Interview/Concept/Algorithms/Mathematical Techniques/index|Mathematical Techniques Concepts]] for principles.

Labuladong's guide for this section (`loading...`) would typically list problems where a mathematical observation simplifies the solution or is core to it.

## Potential Problem Categories / Examples (Illustrative)

-   **Number Theory:**
    -   Primes: [[Interview/Practice/LeetCode/LC204 - Count Primes|LC204]]
    -   GCD/LCM problems.
    -   Modular Arithmetic: [[Interview/Practice/LeetCode/LC372 - Super Pow|LC372]]
-   **Combinatorics & Factorials:**
    -   [[Interview/Practice/LeetCode/LC172 - Factorial Trailing Zeroes|LC172]]
    -   [[Interview/Practice/LeetCode/LC793 - Preimage Size of Factorial Zeroes Function|LC793]]
-   **Probability & Randomized Algorithms:**
    -   (Conceptual Problems: [[Interview/Concept/Algorithms/Mathematical Techniques/01 - Counterintuitive Probability Problems|Probability Puzzles]])
    -   Weighted Random Selection: [[Interview/Practice/LeetCode/LC528 - Random Pick with Weight|LC528]] (Uses prefix sums + binary search, math insight)
-   **Bit Manipulation with Math Properties:**
    -   [[Interview/Practice/LeetCode/LC191 - Number of 1 Bits|LC191]], [[Interview/Practice/LeetCode/LC231 - Power of Two|LC231]]
-   **Geometry / Coordinate Math:**
    -   (Problems involving distances, areas, intersections)
-   **Brain Teasers / Pattern Recognition:**
    -   [[Interview/Practice/LeetCode/LC292 - Nim Game|LC292]], [[Interview/Practice/LeetCode/LC319 - Bulb Switcher|LC319]], [[Interview/Practice/LeetCode/LC877 - Stone Game|LC877]]
-   **Specific Equations / Properties:**
    -   [[Interview/Practice/LeetCode/LC645 - Set Mismatch|LC645]] (Can use sum/sum of squares)

*(Specific problem links and detailed solutions for these variations would be added as individual notes based on Labuladong's content for this exercise page.)*

---
Parent: [[Interview/Practice/Topic Exercises/index|Topic Exercises Index]]
