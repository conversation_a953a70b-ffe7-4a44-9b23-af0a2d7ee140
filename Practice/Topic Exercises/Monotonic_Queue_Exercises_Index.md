---
tags: [index, practice/topic_exercises, topic/queue, pattern/monotonic_queue, course/labuladong]
aliases: [Monotonic Queue Exercises, 单调队列习题]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典队列_栈算法/【练习】单调队列的通用实现及经典习题.md
---

> [!NOTE] Source Annotation
> This page is inspired by Labuladong's "【练习】单调队列的通用实现及经典习题".
> It lists exercises related to the [[Interview/Concept/Data Structures/Queue/Applications/01 - Monotonic Queue Pattern|Monotonic Queue Pattern]].

# Monotonic Queue: Universal Implementation and Classic Exercises

This page is a collection point for exercises and concepts related to Monotonic Queues, including their universal implementation and applications.

## Core Concept Refresher
- [[Interview/Concept/Data Structures/Queue/Applications/01 - Monotonic Queue Pattern|Monotonic Queue Pattern]]

## Universal Implementation (Conceptual - Placeholder)
Labuladong's title "通用实现" (Universal Implementation) suggests that this section would detail a more robust `MonotonicQueue` class that might handle:
- Both min and max tracking.
- A standard queue API (`push`, `pop`, `size`) independent of the element being popped.
- This typically involves storing elements along with their counts or timestamps in the underlying actual queue, and the monotonic deque stores only values (or values and their counts if duplicates matter distinctly for max/min).
- A note like `[[Interview/Concept/Data Structures/Queue/Applications/02 - Monotonic Queue Universal Implementation|Monotonic Queue Universal Implementation]]` would cover this.

## Classic LeetCode Problems
Problems that can be solved or are related to Monotonic Queues (often for sliding window extremes):
-   [[Interview/Practice/LeetCode/LC239 - Sliding Window Maximum|LC239 - Sliding Window Maximum]]
-   [[Interview/Practice/LeetCode/LCR181 - Max Value of Queue|LCR181 - Max Value of Queue]] (面试题59 - II. 队列的最大值)
-   `[[Interview/Practice/LeetCode/LC1425 - Constrained Subsequence Sum|LC1425 - Constrained Subsequence Sum]]` (Uses monotonic queue for DP optimization)
-   `[[Interview/Practice/LeetCode/LC862 - Shortest Subarray with Sum at Least K|LC862 - Shortest Subarray with Sum at Least K]]` (Involves monotonic queue for finding optimal window ends with prefix sums)

*(Specific problem links and detailed solutions for these variations would be added as individual notes based on Labuladong's content for this exercise page.)*

---
Parent: [[Interview/Practice/Topic Exercises/index|Topic Exercises Index]]
