---
tags: [index, practice/topic_exercises, topic/segment, course/labuladong]
aliases: [Segment Tree Problems]
---

> [!NOTE] Source Annotation
> This index page is based on [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/【练习】线段树经典习题.md|【练习】线段树经典习题 by Labula<PERSON>]].

# Segment Tree Classic Exercises

This page lists exercises related to Segment Trees.
Refer to:
- [[Interview/Concept/Data Structures/Tree/Segment Tree/00 - Segment Tree - Principles and Visualization|Segment Tree Principles]]
- [[Interview/Concept/Data Structures/Tree/Segment Tree/01 - Segment Tree Implementation - Basic|Basic Segment Tree Implementation]]
- [[Interview/Concept/Data Structures/Tree/Segment Tree/02 - Segment Tree Implementation - Dynamic|Dynamic Segment Tree]]
- [[Interview/Concept/Data Structures/Tree/Segment Tree/03 - Segment Tree Implementation - Lazy Propagation|Lazy Propagation Segment Tree]]

Labuladong's guide (`loading...`) suggests that these problems are often "hard" but become manageable with the right templates.
Consider problems involving range queries and/or range updates:
- [[Interview/Practice/LeetCode/LC307 - Range Sum Query - Mutable|LC307 - Range Sum Query - Mutable]] (Point update, range query)
- Problems requiring range min/max query, range add/set update.


---
Parent: [[Interview/Practice/index|Practice Problems]]
