---
tags: [index, practice/topic_exercises, topic/stack, course/labuladong]
aliases: [Stack Classic Exercises, 栈经典习题]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典队列_栈算法/【练习】栈的经典习题.md
---

> [!NOTE] Source Annotation
> This page is inspired by Labuladong's "【练习】栈的经典习题".
> It lists exercises focusing on the LIFO property of stacks, such as expression evaluation and parentheses matching.

# Stack: Classic Exercises

This page collects exercises that primarily leverage the Last-In, First-Out (LIFO) property of stacks.

## Core Concept Refresher
- [[Interview/Concept/Data Structures/Stack/index|Stack (Data Structure)]]
- [[Interview/Concept/Data Structures/Stack/Applications/index|Stack Applications]]

## Problem Types (Based on "考察先进后出性质", "表达式运算", "括号合法性检测")
Labuladong's guide (`loading...`) points towards problems where LIFO nature is key:

1.  **Parentheses Matching / Symbol Balancing:**
    -   Classic use of stack to ensure opening and closing symbols are correctly paired and nested.
    -   See [[Interview/Concept/Data Structures/Stack/Applications/Valid Parentheses Pattern|Valid Parentheses Pattern]] and its exercise list [[Interview/Practice/Topic Exercises/Valid_Parentheses_Exercises_Index|Valid Parentheses Exercises]].
    -   Example: [[Interview/Practice/LeetCode/LC20 - Valid Parentheses|LC20 - Valid Parentheses]]

2.  **Expression Evaluation:**
    -   Evaluating infix, postfix, or prefix arithmetic expressions. Stacks are used to manage operands and operators according to precedence.
    -   Problems in the "Calculator" series (e.g., [[Interview/Practice/LeetCode/LC224 - Basic Calculator|LC224]], [[Interview/Practice/LeetCode/LC227 - Basic Calculator II|LC227]], [[Interview/Practice/LeetCode/LC772 - Basic Calculator III|LC772]]) heavily use stacks.
    -   See [[Interview/Concept/Algorithms/Stack Applications/01 - Basic Calculator Implementation|Calculator Implementation Framework]].

3.  **Monotonic Stack Problems:**
    -   Finding next/previous greater/smaller elements, etc.
    -   See [[Interview/Concept/Data Structures/Stack/Applications/01 - Monotonic Stack Pattern|Monotonic Stack Pattern]] and its exercise list [[Interview/Practice/Topic Exercises/Monotonic_Stack_Exercises_Index|Monotonic Stack Exercises]].

4.  **Simulating Recursion / Backtracking:**
    -   An explicit stack can be used to convert a recursive DFS into an iterative one.

*(Specific problem links and detailed solutions for non-covered categories would be added based on Labuladong's content for this exercise page.)*

---
Parent: [[Interview/Practice/Topic Exercises/index|Topic Exercises Index]]
