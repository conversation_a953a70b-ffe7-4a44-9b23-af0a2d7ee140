---
tags: [index, practice/topic_exercises, topic/trie, course/labuladong]
aliases: [Trie Tree Exercise Collection, Trie Problems]
---

> [!NOTE] Source Annotation
> This index page is based on the problems listed in [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/【练习】Trie 树算法习题.md|【练习】Trie 树算法习题 by Labuladong]].

# Trie Tree Algorithm Exercises

This page serves as an index for LeetCode exercises and related problems that can be solved using Trie data structures. Understanding [[Interview/Concept/Data Structures/Trie/00 - Trie - Introduction and Principles|Trie principles]] and [[Interview/Concept/Data Structures/Trie/01 - TrieMap and TrieSet Implementation|Trie implementations]] is key.

Labuladong notes that with `TrieMap` and `TrieSet` implementations, many Trie-related LeetCode problems become straightforward applications. Optimizations, such as using a fixed-size array for children when the character set is small (e.g., 'a-z'), can be important for performance.

## LeetCode Problems

-   **[[Interview/Practice/LeetCode/LC208 - Implement Trie (Prefix Tree)|LC208 - Implement Trie (Prefix Tree)]]** (🟠 Medium)
    -   Core problem to implement basic Trie (`TrieSet`) functionality.
-   `[[Interview/Practice/LeetCode/LC1804 - Implement Trie II (Prefix Tree)|LC1804 - Implement Trie II (Prefix Tree)]]` (🟠 Medium, 🔒 Premium)
    -   Extends Trie with methods to count words equal to prefix and words starting with prefix. Requires storing counts in TrieNodes.
-   `[[Interview/Practice/LeetCode/LC211 - Design Add and Search Words Data Structure|LC211 - Design Add and Search Words Data Structure]]` (🟠 Medium)
    -   Involves adding words and searching, where search can include `.` (wildcard) characters. Requires DFS/backtracking within the Trie search.
-   `[[Interview/Practice/LeetCode/LC648 - Replace Words|LC648 - Replace Words]]` (🟠 Medium)
    -   Given a dictionary of "roots" and a sentence, replace words in the sentence with their shortest root from the dictionary. Build a Trie from roots for efficient prefix checking.
-   `[[Interview/Practice/LeetCode/LC677 - Map Sum Pairs|LC677 - Map Sum Pairs]]` (🟠 Medium)
    -   Implement a `MapSum` class to insert key-value pairs (string keys, int values) and sum values of keys with a given prefix. Store sums in TrieNodes or compute during prefix traversal.

## 剑指 Offer II (Sword Finger Offer II) Equivalents

-   `[剑指 Offer II 062. 实现前缀树]` - Equivalent to [[Interview/Practice/LeetCode/LC208 - Implement Trie (Prefix Tree)|LC208]].
-   `[剑指 Offer II 063. 替换单词]` - Equivalent to `[[Interview/Practice/LeetCode/LC648 - Replace Words|LC648]]`.
-   `[剑指 Offer II 066. 单词之和]` - Equivalent to `[[Interview/Practice/LeetCode/LC677 - Map Sum Pairs|LC677]]`.


```mermaid
graph TD
    Root["Trie Exercises"]

    subgraph "Core Implementation"
        LC208["[[Interview/Practice/LeetCode/LC208 - Implement Trie (Prefix Tree)|LC208. Implement Trie]]"]
        LC1804["[[Interview/Practice/LeetCode/LC1804 - Implement Trie II (Prefix Tree)|LC1804. Implement Trie II]] (🔒)"]
    end
    
    subgraph "Applications"
        LC211["[[Interview/Practice/LeetCode/LC211 - Design Add and Search Words Data Structure|LC211. Add/Search Words (Wildcard)]]"]
        LC648["[[Interview/Practice/LeetCode/LC648 - Replace Words|LC648. Replace Words]]"]
        LC677["[[Interview/Practice/LeetCode/LC677 - Map Sum Pairs|LC677. Map Sum Pairs]]"]
    end

    Root --> LC208
    Root --> LC1804
    Root --> LC211
    Root --> LC648
    Root --> LC677
    
    classDef problem fill:#e6ffe6,stroke:#006400,stroke-width:2px;
    class LC208, LC1804, LC211, LC648, LC677 problem;
```

---
Parent: [[Interview/Practice/index|Practice Problems]]
Related Concept: [[Interview/Concept/Data Structures/Trie/00 - Trie - Introduction and Principles|Trie Principles]], [[Interview/Concept/Data Structures/Trie/01 - TrieMap and TrieSet Implementation|Trie Implementation]]
