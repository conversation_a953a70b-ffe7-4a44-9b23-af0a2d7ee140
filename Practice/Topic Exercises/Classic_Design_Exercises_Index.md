---
tags: [index, practice/topic_exercises, topic/classic, course/labuladong]
aliases: [Data Structure Design Problems]
---

> [!NOTE] Source Annotation
> This index page is based on [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/【练习】更多经典设计习题.md|【练习】更多经典设计习题 by Labuladong]].

# Classic Data Structure Design Exercises

This page lists exercises related to designing data structures, often by combining basic structures.
Labuladong's guide for this section (`loading...`) mentions prerequisites:
- Using [[Interview/Concept/Data Structures/Hash Map/03 - LinkedHashMap - Maintaining Insertion Order|Linked Lists to enhance HashMaps (LinkedHashMap)]]
- Using [[Interview/Concept/Data Structures/Hash Map/04 - ArrayHashMap - Random Key Access|Arrays to enhance HashMaps (ArrayHashMap)]]

Consider problems like:
- [[Interview/Practice/LeetCode/LC380 - Insert Delete GetRandom O(1)|LC380 - Insert Delete GetRandom O(1)]]
- LR<PERSON> Cache, LFU Cache (see [[Interview/Concept/Algorithms/Caching/index|Caching Algorithms]])


---
Parent: [[Interview/Practice/index|Practice Problems]]
