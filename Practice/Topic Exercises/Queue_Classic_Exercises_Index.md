---
tags: [index, practice/topic_exercises, topic/queue, course/labuladong]
aliases: [Queue Classic Exercises, 队列经典习题]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典队列_栈算法/【练习】队列的经典习题.md
---

> [!NOTE] Source Annotation
> This page is inspired by Labuladong's "【练习】队列的经典习题".
> It lists exercises focusing on the FIFO property of queues, often in sliding window or simulation contexts.

# Queue: Classic Exercises

This page collects exercises that primarily leverage the First-In, First-Out (FIFO) property of queues.

## Core Concept Refresher
- [[Interview/Concept/Data Structures/Queue/index|Queue (Data Structure)]]
- [[Interview/Concept/Data Structures/Queue/Applications/index|Queue Applications]]

## Problem Types (Based on "考察先进先出性质" and "滑动窗口")
Labuladong's guide (`loading...`) points towards problems where the FIFO nature is key:

1.  **Simulations:**
    -   Problems modeling real-world queues, task processing, or step-by-step state changes where order of arrival/processing matters.
    -   Example: [[Interview/Practice/LeetCode/LC2073 - Time Needed to Buy Tickets|LC2073 - Time Needed to Buy Tickets]] (though this one explicitly uses a queue for simulation rather than just its FIFO property in an abstract sense).

2.  **Breadth-First Search (BFS):**
    -   The queue is fundamental to BFS for exploring trees/graphs level by level.
    -   See [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Graph BFS]] and [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Tree Level-Order Traversal]].

3.  **Sliding Window (Maintaining Window Elements):**
    -   While [[Interview/Concept/Data Structures/Queue/Applications/01 - Monotonic Queue Pattern|Monotonic Queues]] are specialized for finding extremes in a window, a standard queue (often a deque) can be used in sliding window problems to simply maintain the elements currently in the window if their order of entry/exit matters.
    -   Example: If a problem needs to track the exact sequence of elements in a fixed-size window as it slides.

*(Specific problem links and detailed solutions would be added based on Labuladong's content for this exercise page.)*

---
Parent: [[Interview/Practice/Topic Exercises/index|Topic Exercises Index]]
