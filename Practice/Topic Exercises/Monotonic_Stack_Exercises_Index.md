---
tags: [index, practice/topic_exercises, topic/stack, pattern/monotonic_stack, course/labuladong]
aliases: [Monotonic Stack Exercises, 单调栈习题]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典队列_栈算法/【练习】单调栈的几种变体及经典习题.md
---

> [!NOTE] Source Annotation
> This page is inspired by Labuladong's "【练习】单调栈的几种变体及经典习题".
> It lists exercises related to the [[Interview/Concept/Data Structures/Stack/Applications/01 - Monotonic Stack Pattern|Monotonic Stack Pattern]].

# Monotonic Stack: Classic Exercises and Variations

This page serves as a collection point for exercises that utilize the Monotonic Stack pattern. The core idea is to maintain a stack whose elements are always in a specific order (increasing or decreasing).

## Core Concept Refresher
- [[Interview/Concept/Data Structures/Stack/Applications/01 - Monotonic Stack Pattern|Monotonic Stack Pattern]]

## Problem Variations (Conceptual - Based on Title)
Labuladong's "【练习】单调栈的几种变体及经典习题" (loading...) likely covers variations such as:

-   **Next Greater Element:** (Covered in the main pattern note)
    -   [[Interview/Practice/LeetCode/LC496 - Next Greater Element I|LC496]]
    -   [[Interview/Practice/LeetCode/LC503 - Next Greater Element II|LC503]]
    -   [[Interview/Practice/LeetCode/LC739 - Daily Temperatures|LC739]] (distance to next greater)
-   **Previous Greater Element:**
    -   Modify iteration to be from left-to-right.
    -   Stack stores elements in increasing order (if finding previous *greater*). Or decreasing if finding previous *smaller*.
-   **Next Smaller Element:**
    -   Iterate right-to-left.
    -   Stack maintains elements in increasing order.
-   **Previous Smaller Element:**
    -   Iterate left-to-right.
    -   Stack maintains elements in increasing order.

### General Template Adaptation Logic
-   **"Next" vs. "Previous":** Determines iteration direction.
    -   "Next" (element to the right): Iterate from right-to-left.
    -   "Previous" (element to the left): Iterate from left-to-right.
-   **"Greater" vs. "Smaller":** Determines stack's monotonic property and comparison in `while` loop.
    -   Finding "Greater": Stack usually keeps smaller elements (top is smallest of potential candidates, or largest if stack is decreasing). When `current_num` is processed, pop elements `<= current_num` (if stack is decreasing for NGE) or pop elements `>= current_num` (if stack is increasing for NSE).
    -   Finding "Smaller": Reverse logic.

## Classic LeetCode Problems (Beyond Basic NGE)
-   `[[Interview/Practice/LeetCode/LC84 - Largest Rectangle in Histogram|LC84 - Largest Rectangle in Histogram]]` (Uses prev/next smaller elements)
-   `[[Interview/Practice/LeetCode/LC42 - Trapping Rain Water|LC42 - Trapping Rain Water]]` (Can be solved with monotonic stack)
-   `[[Interview/Practice/LeetCode/LC402 - Remove K Digits|LC402 - Remove K Digits]]` (Build smallest number by selectively keeping digits using a monotonic stack-like idea)

*(Specific problem links and detailed solutions for these variations would be added as individual notes based on Labuladong's content for this exercise page.)*

---
Parent: [[Interview/Practice/Topic Exercises/index|Topic Exercises Index]]
