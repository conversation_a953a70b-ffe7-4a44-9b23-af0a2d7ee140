---
tags: [index, practice/games]
aliases: [Game Problems Index, Algorithmic Games]
---

# Game Problems (Algorithmic Implementations)

This section lists algorithmic problems framed as games, often requiring graph traversal, state-space search, or dynamic programming.

- [[Interview/Practice/Games/连连看游戏|连连看游戏 (Link Game Pathfinding)]]
- [[Interview/Practice/Games/华容道游戏|华容道游戏 (Huarong Road / Klotski Puzzle)]]
- [[Interview/Practice/Games/Sudoku Solver (Backtracking)|Sudoku Solver (Backtracking)]]
- [[Interview/Practice/Games/Minesweeper II (Expand Click)|Minesweeper II (Expand Click)]] (Placeholder)


---
Parent: [[Interview/Practice/index|Practice Problems]]
