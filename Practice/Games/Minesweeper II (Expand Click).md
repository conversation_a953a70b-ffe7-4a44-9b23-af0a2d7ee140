---
tags: [problem/game, algorithm/dfs, algorithm/bfs, topic/grid_traversal, pattern/flood_fill, course/labuladong]
aliases: [Minesweeper Click Expand, 扫雷展开逻辑]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第二章、经典暴力搜索算法/DFS_回溯算法/【游戏】扫雷 II.md
---

> [!NOTE] Source Annotation
> Content conceptualized from Labuladong's article "【游戏】扫雷 II" (which is marked as "loading..." in the provided source files).
> This note outlines the expected logic for implementing the "expand click" functionality in a Minesweeper game.

# Game: Minesweeper II - Expand Click Logic

This problem involves implementing the logic for what happens when a user clicks on a cell in a Minesweeper game.
- If the clicked cell `(r, c)` is a mine, the game might end.
- If the cell is not a mine, it reveals the number of adjacent mines.
- If the cell is not a mine AND has 0 adjacent mines, it should recursively (or iteratively using BFS) expand to reveal all adjacent 0-mine cells, and also reveal the first layer of numbered cells bordering these 0-mine areas. This is a [[Interview/Concept/Algorithms/Graph Traversal/Island Problems Framework (DFS BFS)|Flood Fill]] type of operation.

## 🎯 Core Idea: DFS/BFS for Expansion

1.  **Input:** `board` (representing mine locations and current revealed state), clicked `(r, c)`.
2.  **Pre-computation (Implied):** The game board usually has an underlying layer storing mine locations and another layer storing pre-calculated adjacent mine counts for non-mine cells. The `boardHandler` in the game likely abstracts this.
3.  **`expandClick(board, r, c)` Logic:**
    a.  If `board[r][c]` is already revealed or flagged, do nothing.
    b.  If `board[r][c]` is a mine: Handle game over (reveal mine).
    c.  If `board[r][c]` is not a mine:
        i.  Reveal `board[r][c]` to show its adjacent mine count (e.g., `count = count_adjacent_mines(r,c)`).
        ii. If `count == 0`:
            -   This is an empty cell. Recursively call `expandClick` (or add to BFS queue) for all 8 neighbors of `(r, c)` that are not yet revealed.

## 🐍 Python Sketch (Conceptual)

```python
# Assume board_handler provides:
# board_handler.is_mine(r,c) -> bool
# board_handler.get_adjacent_mine_count(r,c) -> int (0-8)
# board_handler.reveal_cell(r,c, count_or_mine_symbol)
# board_handler.is_revealed(r,c) -> bool
# board_handler.get_dims() -> (rows, cols)

class MinesweeperExpand:
    def __init__(self, board_handler):
        self.board_handler = board_handler
        self.rows, self.cols = self.board_handler.get_dims()

    def expand_click_dfs(self, r, c):
        # Base cases for DFS
        if not (0 <= r < self.rows and 0 <= c < self.cols): # Out of bounds
            return
        if self.board_handler.is_revealed(r, c): # Already revealed
            return
        # (Add check for flagged cells if applicable)

        if self.board_handler.is_mine(r, c):
            self.board_handler.reveal_cell(r, c, 'M') # 'M' for mine
            # Handle game over logic here
            return

        adjacent_mines = self.board_handler.get_adjacent_mine_count(r, c)
        self.board_handler.reveal_cell(r, c, str(adjacent_mines)) # Reveal with number

        if adjacent_mines == 0:
            # Recursively expand neighbors
            for dr in [-1, 0, 1]:
                for dc in [-1, 0, 1]:
                    if dr == 0 and dc == 0:
                        continue # Skip self
                    self.expand_click_dfs(r + dr, c + dc)

    # BFS version is also common
    def expand_click_bfs(self, r_start, c_start):
        from collections import deque
        if not (0 <= r_start < self.rows and 0 <= c_start < self.cols) or \
           self.board_handler.is_revealed(r_start, c_start):
            return

        if self.board_handler.is_mine(r_start, c_start):
            self.board_handler.reveal_cell(r_start, c_start, 'M')
            return

        q = deque([(r_start, c_start)])
        # For BFS, you might need a separate 'visited_for_bfs' set if reveal doesn't
        # immediately mark it in a way that stops re-queuing.
        # If board_handler.is_revealed is robust, it can serve as 'visited'.
        
        # Initial click: reveal the clicked cell first
        initial_mines = self.board_handler.get_adjacent_mine_count(r_start, c_start)
        self.board_handler.reveal_cell(r_start, c_start, str(initial_mines))

        if initial_mines != 0: # If clicked on a number, don't expand further
            return

        # If clicked on a '0', proceed with BFS expansion
        # (q already has (r_start, c_start), which is a '0' cell)

        while q:
            r, c = q.popleft()
            
            # Expand neighbors only if current cell (r,c) was a '0' cell.
            # The logic here is: if cell (r,c) has 0 adjacent mines, its neighbors are revealed.
            # If a neighbor also has 0 adjacent mines, it is added to queue for further expansion.
            # If a neighbor has >0 mines, it's revealed but not added to queue.
            
            for dr_offset in [-1, 0, 1]:
                for dc_offset in [-1, 0, 1]:
                    if dr_offset == 0 and dc_offset == 0:
                        continue
                    
                    nr, nc = r + dr_offset, c + dc_offset
                    
                    if 0 <= nr < self.rows and 0 <= nc < self.cols and \
                       not self.board_handler.is_revealed(nr, nc) and \
                       not self.board_handler.is_mine(nr, nc): # Don't auto-reveal mines
                        
                        neighbor_mines = self.board_handler.get_adjacent_mine_count(nr, nc)
                        self.board_handler.reveal_cell(nr, nc, str(neighbor_mines))
                        
                        if neighbor_mines == 0: # Only expand from other '0' cells
                            q.append((nr, nc))
```

## Complexity
- **Worst Case:** $O(R \times C)$ if the entire board is empty and needs to be revealed. Each cell is visited/processed once.
- **Space Complexity:** $O(R \times C)$ for recursion stack (DFS) or queue (BFS) in the worst case.

## 总结 (Summary)
- Minesweeper's "expand click" involves revealing a cell.
- If the cell is a mine, game over.
- If it's a number (>0 adjacent mines), reveal that number.
- If it's empty (0 adjacent mines), reveal it and recursively/iteratively expand to all adjacent empty cells and their numbered borders. This is a flood-fill algorithm.
- DFS or BFS can be used for the expansion.

---
Parent: [[Interview/Practice/Games/index|Game Problems]]
Related: [[Interview/Concept/Algorithms/Graph Traversal/Island Problems Framework (DFS BFS)|Island Problems Framework]] (similar grid traversal)
