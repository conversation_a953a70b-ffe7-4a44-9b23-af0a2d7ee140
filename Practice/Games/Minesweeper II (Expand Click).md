---
tags: [problem/game, algorithm/dfs, algorithm/bfs, topic/grid_traversal, pattern/flood_fill, course/labuladong]
aliases: [Minesweeper Click Expand, 扫雷展开逻辑]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第二章、经典暴力搜索算法/DFS_回溯算法/【游戏】扫雷 II.md
---

> [!NOTE] Source Annotation
> Content conceptualized from Labuladong's article "【游戏】扫雷 II" ([[Interview/labuladong 的算法笔记/markdown_export_本站简介/第二章、经典暴力搜索算法/DFS_回溯算法/【游戏】扫雷 II.md|Source]]) (which is marked as "loading..." in the provided source files).
> This note outlines the expected logic for implementing the "expand click" functionality in a Minesweeper game.

# Game: Minesweeper II - Expand Click Logic

This problem involves implementing the logic for what happens when a user clicks on a cell in a Minesweeper game.
- If the clicked cell `(r, c)` is a mine, the game might end.
- If the cell is not a mine, it reveals the number of adjacent mines.
- If the cell is not a mine AND has 0 adjacent mines, it should recursively (or iteratively using BFS) expand to reveal all adjacent 0-mine cells, and also reveal the first layer of numbered cells bordering these 0-mine areas. This is a [[Interview/Concept/Algorithms/Graph Traversal/Island Problems Framework (DFS BFS)|Flood Fill]] type of operation.

## 🎯 Core Idea: DFS/BFS for Expansion

1.  **Input:** `board` (representing mine locations and current revealed state), clicked `(r, c)`.
2.  **Pre-computation (Implied):** The game board usually has an underlying layer storing mine locations and another layer storing pre-calculated adjacent mine counts for non-mine cells. A `boardHandler` in the game likely abstracts this.
3.  **`expandClick(board, r, c)` Logic:**
    a.  If `board[r][c]` is already revealed or flagged, do nothing.
    b.  If `board[r][c]` is a mine: Handle game over (reveal mine).
    c.  If `board[r][c]` is not a mine:
        i.  Reveal `board[r][c]` to show its adjacent mine count (e.g., `count = count_adjacent_mines(r,c)`).
        ii. If `count == 0`:
            -   This is an empty cell. Recursively call `expandClick` (or add to BFS queue) for all 8 neighbors of `(r, c)` that are not yet revealed.

## 🐍 Python Sketch (Conceptual)

This sketch assumes a `board_handler` object encapsulates the game logic like checking for mines, getting adjacent counts, and updating the revealed state of the board.

```python
# Assuming board_handler provides:
# board_handler.is_mine(r, c) -> bool
# board_handler.get_adjacent_mine_count(r, c) -> int (0-8)
# board_handler.reveal_cell(r, c, display_value) -> None (updates internal game board)
# board_handler.is_revealed(r, c) -> bool
# board_handler.get_dims() -> (rows, cols)
# board_handler.is_flagged(r,c) -> bool (optional, if flags prevent expansion)
# board_handler.game_over() -> None (handles game over state)

from collections import deque

class MinesweeperExpandLogic:
    def __init__(self, board_handler_instance):
        self.board_handler = board_handler_instance
        self.rows, self.cols = self.board_handler.get_dims()

    def click_cell(self, r_click: int, c_click: int):
        '''Main function called when a cell is clicked.'''
        if not (0 <= r_click < self.rows and 0 <= c_click < self.cols):
            return # Click out of bounds

        if self.board_handler.is_revealed(r_click, c_click): # or self.board_handler.is_flagged(r_click, c_click):
            return # Already revealed or flagged, do nothing

        if self.board_handler.is_mine(r_click, c_click):
            self.board_handler.reveal_cell(r_click, c_click, 'M') # 'M' for mine
            self.board_handler.game_over() 
            return

        # If not a mine, use BFS for expansion if it's a '0' cell
        self._bfs_expand(r_click, c_click)

    def _bfs_expand(self, r_start: int, c_start: int):
        q = deque([(r_start, c_start)])

        # Initial cell clicked (guaranteed not a mine at this point)
        # Reveal it first. If it's a number > 0, BFS won't expand further from it.
        # If it's a 0, BFS will continue from it.

        # We need a visited set specifically for the BFS process to avoid re-adding to queue,
        # even if is_revealed takes time to update or for cells currently in queue.
        bfs_visited_during_this_click = set()
        bfs_visited_during_this_click.add((r_start,c_start))


        while q:
            r, c = q.popleft()

            # If already revealed by another path in this same BFS expansion, skip.
            # (This check is mainly if is_revealed is not immediate or for complex propagation)
            # If board_handler.is_revealed() is robust and quick, this explicit bfs_visited check on deque might be redundant.
            # However, for adding to queue, it's good.

            # Action: Reveal the cell (r,c) if not already revealed by board_handler itself
            # This ensures this cell is processed once per click expansion.
            if self.board_handler.is_revealed(r,c): # Could have been revealed by a previous step in this BFS
                continue

            adjacent_mines = self.board_handler.get_adjacent_mine_count(r, c)
            self.board_handler.reveal_cell(r, c, str(adjacent_mines))

            # If this cell has 0 adjacent mines, add its neighbors to the queue for expansion
            if adjacent_mines == 0:
                for dr_offset in [-1, 0, 1]:
                    for dc_offset in [-1, 0, 1]:
                        if dr_offset == 0 and dc_offset == 0:
                            continue # Skip self

                        nr, nc = r + dr_offset, c + dc_offset

                        if 0 <= nr < self.rows and 0 <= nc < self.cols and \
                           not self.board_handler.is_revealed(nr, nc) and \
                           (nr, nc) not in bfs_visited_during_this_click:
                           # and not self.board_handler.is_flagged(nr,nc): # Check for flags

                            # Add to queue for potential expansion.
                            # No need to check if neighbor is mine here; BFS will handle it when it's popped.
                            bfs_visited_during_this_click.add((nr,nc))
                            q.append((nr, nc))
```

## Complexity
- **Worst Case Time:** $O(R \times C)$ where $R, C$ are dimensions of the board. Each cell is visited and processed (revealed or added to queue) at most once during a single click expansion.
- **Worst Case Space:** $O(R \times C)$ for the queue and `bfs_visited_during_this_click` set in the case where the entire board is empty and needs to be revealed.

## 总结 (Summary)
- Minesweeper's "expand click" functionality handles revealing cells.
- If a mine is clicked, the game ends.
- If a non-mine cell is clicked:
    - If it has adjacent mines (count > 0), reveal its number.
    - If it has zero adjacent mines (an empty square), reveal it (as '0' or blank) and then automatically expand to reveal all its adjacent unrevealed non-mine neighbors. This expansion continues recursively (or iteratively with BFS) from any newly revealed '0' cells.
- This expansion is a form of flood fill, typically implemented with DFS or BFS.

---
Parent: [[Interview/Practice/Games/index|Game Problems]]
Related: [[Interview/Concept/Algorithms/Graph Traversal/Island Problems Framework (DFS BFS)|Island Problems Framework]] (similar grid traversal with DFS/BFS)
