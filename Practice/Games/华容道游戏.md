---
tags: [problem/game, algorithm/bfs, pattern/pathfinding, topic/state_space_search, course/labuladong]
aliases: [华容道, <PERSON><PERSON><PERSON>]
source_file_path: 【游戏】华容道游戏.md
---

# 【游戏】华容道游戏 (Huarong Road / Klotski Puzzle)

> [!NOTE] Source Annotation
> This problem is based on the description in "【游戏】华容道游戏.md" by Labuladong.
> It's an advanced version of sliding puzzle problems, solvable with BFS, requiring careful state representation and transition logic.

## Problem Statement (Summary)

The Huarong Road (Klotski) puzzle involves moving various sized blocks on a board to free a specific block (typically "曹操") to an exit position. This problem requires finding a sequence of moves to solve the puzzle. Unlike typical LeetCode problems asking for minimum steps, this version (as framed by <PERSON><PERSON><PERSON>) often requires returning the *sequence of moves*.

The game typically provides a `gameHandler.move(piece_id, direction)` function to interact with the puzzle.

## 🧠 Core Idea: BFS for State-Space Search

This is a state-space search problem.
-   **State:** A representation of the current configuration of all pieces on the board. This needs to be hashable to be used in a `visited` set. A tuple of tuples (for the board) or a canonical string representation can work.
-   **Start State:** The initial board configuration.
-   **Target State:** A configuration where the main piece (e.g., "曹操") is at the exit.
-   **Transitions:** Moving a piece one step in a valid direction (up, down, left, right) into an empty space.
-   **Goal:** Find *a* sequence of moves. BFS will find a shortest sequence in terms of number of moves.

## 🛠️ Challenges and Differences from Simple Puzzles

1.  **Piece Shapes and Sizes:** Unlike [[Interview/Practice/LeetCode/LC773 - Sliding Puzzle|LC773 Sliding Puzzle]] where all tiles are 1x1 (or '0' is 1x1), Huarong Road has pieces of different dimensions (e.g., 1x1, 1x2, 2x1, 2x2). This makes `get_neighbors` (valid moves) more complex.
    - When moving a piece, all cells it occupies must move to valid empty cells.
2.  **Path/Move Recording:** Since the goal is often the sequence of moves, the BFS queue needs to store not just the state, but the path (sequence of `(piece_id, direction)` tuples) taken to reach that state.
    - State in queue: `(board_config, path_of_moves)`
3.  **State Representation:** Needs to be carefully chosen for efficient hashing and copying. A tuple of strings (each string a row) or a single string for the whole board.

## ⚙️ BFS Algorithm Sketch

1.  **Initialize:**
    -   Queue `q = deque([(initial_board_state, [])])`  (state, path_to_state)
    -   `visited = {initial_board_state}`
2.  **Loop:** While `q` is not empty:
    a.  `current_board, current_path = q.popleft()`
    b.  If `current_board` is a goal state (e.g., "曹操" at exit):
        -   Return `current_path`. This path can then be used to call `gameHandler.move()` sequentially.
    c.  For each piece `p` on `current_board`:
        i.  For each possible direction `d` (Up, Down, Left, Right):
            1.  Check if moving piece `p` in direction `d` is valid (i.e., target cells are empty and within bounds).
            2.  If valid, generate `next_board_state`.
            3.  If `next_board_state` not in `visited`:
                -   `visited.add(next_board_state)`
                -   `new_path = current_path + [(piece_id_of_p, d)]`
                -   `q.append((next_board_state, new_path))`
3.  If queue becomes empty, no solution found.

## 💡 Labuladong's Tip for Game Panel Submission

The game panel in the source typically runs JavaScript. If you implement the BFS in Python/Java/C++:
1.  Solve the puzzle using your algorithm.
2.  Your algorithm should output the sequence of moves, e.g.:
    ```
    gameHandler.move(10, 'left')
    gameHandler.move(9, 'right')
    ...
    ```
3.  Copy this generated sequence of `gameHandler.move` calls and paste it into the game panel's function (e.g., `solveHuarongRoad`).

This approach decouples the algorithm solving from the game panel's execution environment.

## Complexity
-   State space can be very large.
-   Complexity of `get_neighbors` (checking valid moves for all pieces) can be high for each state.
-   This is generally a hard problem if the board is large or has many pieces.

## 总结 (Summary)
-   Huarong Road is a complex state-space search problem solvable with BFS.
-   Key challenges include handling pieces of different sizes and recording the sequence of moves.
-   A canonical, hashable representation of the board state is crucial for the `visited` set.
-   The solution involves generating valid next states by moving one piece at a time.
-   For practical submission in some game environments, the algorithm can output a script of moves to be executed by the game handler.

---
Parent: [[Interview/Practice/Games/index|Game Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Framework]], [[Interview/Practice/LeetCode/LC773 - Sliding Puzzle|LC773 Sliding Puzzle]] (simpler version)
