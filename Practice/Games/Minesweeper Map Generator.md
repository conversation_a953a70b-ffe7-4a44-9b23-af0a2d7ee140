---
tags: [problem/game, algorithm/randomized_algorithms, topic/array, pattern/fisher_yates, course/labuladong]
aliases: [Minesweeper Generator, Random Mine Placement]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/数学运算技巧/【游戏】扫雷游戏地图生成器.md
---

> [!NOTE] Source Annotation
> Problem idea from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/数学运算技巧/【游戏】扫雷游戏地图生成器.md|【游戏】扫雷游戏地图生成器 by Labuladong]].
> Focus is on generating a random mine map.

# Game: Minesweeper Map Generator

This problem asks to generate an initial Minesweeper game map of `width` x `height` with `mineCount` mines placed randomly and uniformly.

## Core Idea: Random Selection of Mine Locations

1.  **Dimensionality Reduction:** Treat the `width` x `height` grid as a 1D array of `total_cells = width * height`.
2.  **Represent Cells:** Create a list of all possible cell indices `[0, 1, ..., total_cells - 1]`.
3.  **Random Shuffle and Pick:**
    - Shuffle this list of indices using the [[Interview/Concept/Algorithms/Randomized Algorithms/01 - Concepts in Game Random Algorithms|Fisher-Yates algorithm]].
    - Select the first `mineCount` indices from the shuffled list. These will be the locations of the mines.
4.  **Populate Board:** Create the 2D `board`. For each chosen mine index, convert it back to `(row, col)` and mark it as a mine.

This ensures uniform random probability for each cell being a mine.

## Python Implementation
```python
import random

class MinesweeperGenerator:
    def generate_map(self, width: int, height: int, mine_count: int) -> list[list[bool]]: # True for mine
        total_cells = width * height
        if mine_count > total_cells:
            raise ValueError("Mine count cannot exceed total number of cells.")

        # Create a list of all possible 1D cell indices
        all_cell_indices = list(range(total_cells))

        # Shuffle the indices (Fisher-Yates)
        for i in range(total_cells - 1, 0, -1):
            j = random.randint(0, i)
            all_cell_indices[i], all_cell_indices[j] = all_cell_indices[j], all_cell_indices[i]

        # Select the first mine_count indices for mines
        mine_indices_1d = set(all_cell_indices[:mine_count])

        # Create and populate the board
        board = [[False for _ in range(width)] for _ in range(height)] # Or height rows, width cols
                                                                    # Labuladong example implies width=rows, height=cols
                                                                    # Let's assume standard matrix: height=rows, width=cols

        for flat_idx in mine_indices_1d:
            row = flat_idx // width # Assuming width is number of columns
            col = flat_idx % width
            board[row][col] = True

        return board

# Example: generator = MinesweeperGenerator()
# game_board = generator.generate_map(width=10, height=8, mine_count=10)
# for row_data in game_board: print(row_data)
```

## Advanced Considerations (from Labuladong's problem description)
- **Time/Space Complexity:**
    - Time: $O(W \cdot H)$ for shuffling.
    - Space: $O(W \cdot H)$ for `all_cell_indices` and the board.
- **Optimize Space to $O(\text{mineCount})$?**
    - If `mineCount` is much smaller than `W*H`.
    - **Algorithm:**
        1. Initialize an empty set `mine_locations`.
        2. While `len(mine_locations) < mine_count`:
           - Pick a random 1D index `rand_idx` from `0` to `total_cells - 1`.
           - Add `rand_idx` to `mine_locations`.
        3. Convert these `mineCount` 1D indices to 2D and populate board (still needs $O(W \cdot H)$ for board itself).
        - The part for *selecting* mine locations is $O(\text{mineCount})$ space on average if collisions are not too frequent.
        - Time for selection can be higher than `mineCount` due to collisions, but average is okay if `mineCount` is small.
        - This is good for "select K from N" when N is huge.

## 总结 (Summary)
- Generating a Minesweeper map involves uniformly randomly selecting `mineCount` cells out of `width * height` total cells.
- A common approach is to flatten the grid, shuffle all cell indices, and pick the first `mineCount`.
- If `mineCount` is small, iteratively picking random unique indices into a set until `mineCount` is reached can be more space-efficient for the selection part (though the final board is still large).

---
Parent: [[Interview/Practice/Games/index|Game Problems]]
Related: [[Interview/Concept/Algorithms/Randomized Algorithms/01 - Concepts in Game Random Algorithms|Random Algorithms]]
