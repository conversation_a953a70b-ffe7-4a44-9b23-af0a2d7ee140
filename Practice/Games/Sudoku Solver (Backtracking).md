---
tags: [problem/game, problem/leetcode_equivalent, algorithm/backtracking, topic/matrix_traversal, course/labuladong]
aliases: [Sudoku Solver Game, Backtracking Sudoku, 数独作弊器]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第二章、经典暴力搜索算法/DFS_回溯算法/【游戏】实现数独作弊器.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第二章、经典暴力搜索算法/DFS_回溯算法/【游戏】实现数独作弊器.md|【游戏】实现数独作弊器 by Labuladong]].
> This problem is equivalent to [[Interview/Practice/LeetCode/LC37 - Sudoku Solver|LC37 - Sudoku Solver]].

# Game: Sudoku Solver (Backtracking)

This problem requires implementing a `solveSudoku` function that takes a Sudoku board handler (which provides `get(r,c)`, `set(r,c,val)`, `isEditable(r,c)`) and fills in the board to produce a valid solution. The core technique is [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking]].

## 🎯 Core Idea: Recursive Backtracking

The Sudoku solver explores possibilities for each empty cell.
1.  **Iterate through cells:** Find an empty cell `(r, c)`.
2.  **Try numbers:** For each number `num` from 1 to 9:
    a.  If `num` is valid to place at `(r, c)` (i.e., not already in row `r`, column `c`, or the 3x3 subgrid):
        i.  Place `num` at `board[r][c]`.
        ii. Recursively call `backtrack` to solve for the next empty cell.
        iii. If the recursive call returns `true` (solution found), then propagate `true` upwards.
        iv. If the recursive call returns `false` (this `num` choice didn't lead to a solution), **undo the choice** by resetting `board[r][c]` to empty and try the next `num`.
3.  **Base Case:** If all cells are filled (or no more empty cells to process), a solution is found. Return `true`.
4.  If all numbers 1-9 have been tried for cell `(r, c)` and none lead to a solution, return `false`.

## 🐍 Python Adaptation of Labuladong's JavaScript Solution

The provided source code is in JavaScript. Here's a Python adaptation.
The `boardHandler` in the original game panel is simulated here by directly modifying a `board` (list of lists of strings/integers).

```python
class SudokuSolverGame:
    def __init__(self, board_handler=None): # board_handler for game, or direct board
        self.board_handler = board_handler # Or use a direct board representation
        self.found_solution = False

    def solveSudoku(self, board: list[list[str]]): # board is list of lists of chars '1'-'9' or '.'
        self.board = board # Store board if not using handler
        self.found_solution = False
        self._backtrack(0, 0) # Start backtracking from cell (0,0)
        # Board is modified in-place. If self.found_solution is True, board contains solution.

    def _backtrack(self, r: int, c: int) -> None:
        if self.found_solution:
            return

        rows, cols = 9, 9
        
        # If current row `r` is past the end, all cells are filled
        if r == rows:
            self.found_solution = True
            return

        # Calculate next cell coordinates
        next_r, next_c = (r, c + 1) if c < cols - 1 else (r + 1, 0)

        # If current cell board[r][c] is already filled, move to next
        if self.board[r][c] != '.': # Assuming '.' for empty, or use handler.isEditable
            self._backtrack(next_r, next_c)
            return

        # Try numbers 1-9 for current empty cell
        for num_char in "123456789":
            if self._is_valid(r, c, num_char):
                # Make choice
                self.board[r][c] = num_char
                # self.board_handler.set(r,c, int(num_char)) # If using handler

                self._backtrack(next_r, next_c) # Recurse

                if self.found_solution: # If a solution was found by recursive call
                    return
                
                # Undo choice (backtrack)
                self.board[r][c] = '.'
                # self.board_handler.set(r,c, None) # If using handler

    def _is_valid(self, r: int, c: int, num_char: str) -> bool:
        # Check row
        for col_idx in range(9):
            if self.board[r][col_idx] == num_char:
                return False
        
        # Check column
        for row_idx in range(9):
            if self.board[row_idx][c] == num_char:
                return False
                
        # Check 3x3 subgrid
        box_row_start = (r // 3) * 3
        box_col_start = (c // 3) * 3
        for i in range(3):
            for j in range(3):
                if self.board[box_row_start + i][box_col_start + j] == num_char:
                    return False
        return True

# Example Usage:
# board = [
#   ["5","3",".",".","7",".",".",".","."],
#   ["6",".",".","1","9","5",".",".","."],
#   [".","9","8",".",".",".",".","6","."],
#   ["8",".",".",".","6",".",".",".","3"],
#   ["4",".",".","8",".","3",".",".","1"],
#   ["7",".",".",".","2",".",".",".","6"],
#   [".","6",".",".",".",".","2","8","."],
#   [".",".",".","4","1","9",".",".","5"],
#   [".",".",".",".","8",".",".","7","9"]
# ]
# solver = SudokuSolverGame()
# solver.solveSudoku(board)
# if solver.found_solution:
#   for row in board:
#     print(row)
# else:
#   print("No solution found.")
```
Labuladong's provided JS code in the article iterates through cells using a single `index` from `0` to `m*n - 1`, converting `index` to `(i,j)` using `Math.floor(index / n)` and `index % n`. The Python adaptation uses `(r,c)` directly. The core backtracking logic is the same.

## Complexity
-   **Time Complexity:** Difficult to analyze precisely, but it's exponential in the number of empty cells in the worst case (e.g., $9^M$ where $M$ is number of empty cells, before pruning). For a fixed $9 \times 9$ board, it's considered constant time in a very loose sense, but the constant is huge. For N-Queens on an $N \times N$ board, it's roughly $O(N!)$. Sudoku's constraints are tighter.
-   **Space Complexity:** $O(M)$ for recursion stack depth, where $M$ is number of empty cells (at most 81).

## 总结 (Summary)
- Solving Sudoku is a classic backtracking problem.
- The algorithm tries to fill empty cells one by one, checking validity (row, column, 3x3 subgrid) at each step.
- If a choice leads to a dead end, it backtracks and tries a different number.
- The process continues until a full valid solution is found or all possibilities are exhausted.

---
Parent: [[Interview/Practice/Games/index|Game Problems]]
Related: [[Interview/Practice/LeetCode/LC37 - Sudoku Solver|LC37 - Sudoku Solver]], [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking Framework]]
