---
tags: [problem/game, algorithm/bfs, pattern/pathfinding, topic/grid_traversal]
aliases: [连连看, Link Game, Connect Two]
source_file_path: 【游戏】连连看游戏.md
---

# 【游戏】连连看游戏 (Link Game Pathfinding)

> [!NOTE] Source Annotation
> This problem and solution approach are derived from the description in "【游戏】连连看游戏.md".
> It involves finding a path between two pieces on a board with at most two turns, a classic application of Breadth-First Search for shortest paths in terms of turns.

## Problem Statement

连连看游戏可以消除成对儿的相同棋子，但是仅当两个棋子之间连线不超过两次拐弯时才能消除，如果超过两次拐弯则不能消除。

You need to implement a `connect` function. Given a game `board`, and the coordinates of two pieces `(row1, col1)` and `(row2, col2)`, determine if these two pieces can be connected by a path with at most two turns. The path can only pass through empty cells. If they can be connected, return the list of coordinates representing the path; otherwise, return an empty list.

A special rule: "棋盘边缘棋子的连接线可能超出棋盘边界" (Lines connecting pieces near the board edge can extend beyond the board boundary). This implies that the path can utilize "virtual" empty cells one step outside the board's perimeter.

## 🧠 Core Idea: BFS for Shortest Path in Turns

This problem asks for a path with a maximum of 2 turns. This can be modeled as a shortest path problem on an implicit graph where:
-   **Nodes/States:** Represent `(row, col, direction_of_arrival, turns_made)`.
-   **Edges:** Represent extending a path in a straight line from the current state to a new cell. A turn occurs if the new direction is different from `direction_of_arrival`.
-   **Goal:** Reach `(row2, col2)` with `turns_made <= 2`.

We use [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|Breadth-First Search (BFS)]] because it naturally finds the path with the fewest "segments" or, in this case, fewest turns if we define states carefully.

## 🛠️ BFS State and Transitions

-   **State in Queue:** `(r, c, turns, last_direction_idx, current_path_points)`
    -   `(r, c)`: Current cell coordinates.
    -   `turns`: Number of turns made to reach this cell (0, 1, or 2).
    -   `last_direction_idx`: The direction of the segment that *arrived* at `(r, c)`. (0:Up, 1:Down, 2:Left, 3:Right, -1 for start).
    -   `current_path_points`: A list of all `(row, col)` coordinates making up the path from `(row1, col1)` to `(r, c)`.
-   **Visited Set:** To avoid cycles and redundant exploration, we can use a set `visited` storing tuples `(r, c, turns, last_direction_idx)`.

### "Out of Bounds" Rule Interpretation
The rule "棋盘边缘棋子的连接线可能超出棋盘边界" means we can think of the effective grid as being padded by one layer of empty cells around the actual board.
-   Board dimensions: `R` rows, `C` columns (indices `0..R-1`, `0..C-1`).
-   Extended grid for pathfinding: `row_idx \in [-1, R]`, `col_idx \in [-1, C]`.
-   A cell `(nr, nc)` is "walkable" if:
    1.  It is the target `(row2, col2)`.
    2.  It is on the actual board (`0 <= nr < R` and `0 <= nc < C`) AND `board[nr][nc] == 0` (assuming `0` means empty).
    3.  It is an "extended boundary cell" (e.g., `nr = -1` or `nr = R` or `nc = -1` or `nc = C`). These act as temporary empty cells for routing.

## ⚙️ Algorithm Steps for `connect` function

1.  Initialize a queue. Add the starting state: `((row1, col1), 0, -1, [(row1, col1)])`.
    `(current_coords, num_turns, arrival_direction_idx, path_list)`
2.  Initialize `visited = set()`. Add `(row1, col1, 0, -1)` to `visited`.
3.  Define movement directions: `dr = [-1, 1, 0, 0]` (Up, Down, Left, Right for row changes), `dc = [0, 0, -1, 1]` (for col changes). Direction indices: 0:Up, 1:Down, 2:Left, 3:Right.
4.  While the queue is not empty:
    a.  Dequeue `((curr_r, curr_c), turns, last_arr_dir, path)`
    b.  For each of the 4 possible `new_direction_idx` (0 to 3):
        i.  Calculate `current_segment_turns = turns`.
        ii. If `last_arr_dir != -1` (not the start) AND `new_direction_idx != last_arr_dir` (a turn is made):
            `current_segment_turns += 1`.
        iii. If `current_segment_turns > 2`, this path is invalid; continue to next direction.
        iv. Explore in `new_direction_idx`:
            -   Initialize `next_r, next_c = curr_r, curr_c`.
            -   `segment_points = []`
            -   Loop indefinitely (extend straight line):
                1.  `next_r += dr[new_direction_idx]`, `next_c += dc[new_direction_idx]`.
                2.  If `(next_r, next_c) == (row2, col2)`:
                    -   Path found! Return `path + segment_points + [(row2, col2)]`.
                3.  If `not is_walkable(next_r, next_c, board, row2, col2)` (hit obstacle or too far out of bounds):
                    -   Break from this inner loop (cannot extend further in this direction).
                4.  If `(next_r, next_c, current_segment_turns, new_direction_idx)` is in `visited`:
                    -   Skip this specific state to avoid cycles *with the same turn count and arrival direction*. (Note: could revisit a cell with fewer turns or different arrival direction).
                5.  Add `(next_r, next_c, current_segment_turns, new_direction_idx)` to `visited`.
                6.  `segment_points.append((next_r, next_c))`.
                7.  Enqueue `( (next_r, next_c), current_segment_turns, new_direction_idx, path + segment_points )`.
5.  If queue becomes empty, no path found. Return `[]`.

### Helper `is_walkable(r, c, board, target_r, target_c)`
```python
def is_walkable(r, c, R, C, board_data, target_r_val, target_c_val):
    # Check if (r, c) is the target (target is always walkable)
    if r == target_r_val and c == target_c_val:
        return True

    # Check if within extended bounds [-1, R] and [-1, C]
    if not (-1 <= r <= R and -1 <= c <= C):
        return False # Too far out of bounds

    # If on the actual board
    if 0 <= r < R and 0 <= c < C:
        return board_data[r][c] == 0 # Must be an empty cell (0)

    # Otherwise, it's an "extended boundary cell" (e.g., r=-1), considered walkable
    return True
```

## 🐍 Python Implementation (Conceptual)

```python
import collections

def connect(board: list[list[int]], r1: int, c1: int, r2: int, c2: int) -> list[tuple[int, int]]:
    R = len(board)
    C = len(board[0])

    # Directions: 0:Up, 1:Down, 2:Left, 3:Right
    # (dr, dc) pairs for Up, Down, Left, Right
    dr = [-1, 1, 0, 0] 
    dc = [0, 0, -1, 1]

    # Helper for walkability, incorporating the "out of bounds path" rule
    def is_valid_and_walkable(curr_r, curr_c):
        if curr_r == r2 and curr_c == c2: # Target is always "walkable" to end path
            return True

        # Check extended bounds: cells just outside are considered empty pathways
        if not (-1 <= curr_r <= R and -1 <= curr_c <= C):
            return False # Too far out, beyond the allowed "virtual" layer

        # If on the actual board, must be empty (0)
        if 0 <= curr_r < R and 0 <= curr_c < C:
            return board[curr_r][curr_c] == 0

        # If it's one of the "virtual" cells outside the board, it's walkable
        return True

    # Queue stores: ( (row, col), num_turns, last_arrival_direction_idx, path_list )
    # last_arrival_direction_idx: -1 for start, 0-3 for U,D,L,R
    # path_list: list of (r,c) tuples forming the path
    queue = collections.deque([((r1, c1), 0, -1, [(r1, c1)])])

    # Visited set stores: (row, col, num_turns, last_arrival_direction_idx)
    # This prevents cycles and redundant exploration of states
    visited = set()
    visited.add((r1, c1, 0, -1))

    while queue:
        (curr_pos, turns_so_far, last_dir_idx, current_path) = queue.popleft()
        curr_r, curr_c = curr_pos

        # Try to extend path in all 4 directions
        for new_dir_idx in range(4): # 0:U, 1:D, 2:L, 3:R

            # Calculate turns if this move involves a turn
            current_segment_turns = turns_so_far
            if last_dir_idx != -1 and new_dir_idx != last_dir_idx: # A turn is made
                current_segment_turns += 1

            if current_segment_turns > 2: # Exceeded max turns for this segment
                continue

            # Explore along the new_dir_idx as far as possible
            # Path segment for this straight line move

            # Start from the current position, extend one step
            next_r, next_c = curr_r, curr_c

            # Temporarily stores points in the current straight segment being explored
            # Does not include the starting point of the segment (curr_r, curr_c)
            # as that is already in `current_path`.
            path_extension_for_this_segment = []

            while True: # Keep moving in new_dir_idx
                next_r_candidate = next_r + dr[new_dir_idx]
                next_c_candidate = next_c + dc[new_dir_idx]

                if not is_valid_and_walkable(next_r_candidate, next_c_candidate):
                    break # Hit a wall or an occupied cell on board, or too far out of bounds

                # Update current position for this segment
                next_r, next_c = next_r_candidate, next_c_candidate
                path_extension_for_this_segment.append((next_r, next_c))

                # Check if target reached
                if next_r == r2 and next_c == c2:
                    return current_path + path_extension_for_this_segment

                # If this new state (end of segment part) hasn't been visited with this config
                state_key = (next_r, next_c, current_segment_turns, new_dir_idx)
                if state_key not in visited:
                    visited.add(state_key)
                    # The full path to this new point (next_r, next_c)
                    full_path_to_next_node = current_path + path_extension_for_this_segment
                    queue.append( ((next_r, next_c), current_segment_turns, new_dir_idx, full_path_to_next_node) )
                # else: this specific intermediate point in this state (turns, dir) was already reached optimally.

    return [] # No path found
```
```tikz
\begin{tikzpicture}[
    node/.style={draw, rectangle, minimum size=0.8cm, font=\sffamily\small, fill=blue!10},
    path_node/.style={node, fill=green!30},
    turn_node/.style={node, fill=yellow!40, thick},
    board_cell/.style={draw, rectangle, minimum size=0.8cm, font=\sffamily\small},
    empty_cell/.style={board_cell, fill=gray!10},
    piece_cell/.style={board_cell, fill=red!30},
    arrow/.style={->, >=stealth, thick, red, line width=1.5pt},
    grid_style/.style={help lines, color=gray, very thin, step=0.8cm}
]

% Title
\node at (2.8, 5) {\bfseries Example: Connect (0,0) to (0,3) with 1 turn};

% Board representation
\node[piece_cell] (s) at (0*0.8, 4*0.8) {S}; % (0,0)
\node[empty_cell] at (1*0.8, 4*0.8) {0};
\node[piece_cell] at (2*0.8, 4*0.8) {X}; % Obstacle at (0,2)
\node[piece_cell] (t) at (3*0.8, 4*0.8) {T}; % (0,3)

\node[empty_cell] at (0*0.8, 3*0.8) {0};
\node[empty_cell] at (1*0.8, 3*0.8) {0};
\node[empty_cell] at (2*0.8, 3*0.8) {0};
\node[empty_cell] at (3*0.8, 3*0.8) {0};

% Virtual "out of bounds" cells conceptually
\node[empty_cell, dashed] (v_neg1_0) at (0*0.8, 5*0.8) {0};
\node[empty_cell, dashed] (v_neg1_1) at (1*0.8, 5*0.8) {0};
\node[empty_cell, dashed] (v_neg1_2) at (2*0.8, 5*0.8) {0};
\node[empty_cell, dashed] (v_neg1_3) at (3*0.8, 5*0.8) {0};

% Path
\node[path_node] (p00) at (0*0.8, 4*0.8) {S}; % Start
\node[path_node] (p_10) at (0*0.8, 5*0.8) {}; % Virtual out
\node[turn_node] (p_13) at (3*0.8, 5*0.8) {}; % Virtual out (turn point)
\node[path_node] (p03) at (3*0.8, 4*0.8) {T}; % Target

\draw[arrow] (p00.north) -- (p_10.south);
\draw[arrow] (p_10.east) -- (p_13.west);
\draw[arrow] (p_13.south) -- (p03.north);

\node at (1.2, 1.5) [draw, fill=orange!10, rounded corners, text width=6cm, align=center] {
    Path: (0,0) $\rightarrow$ (-1,0) [Segment 1, 0 turns]\\
    (-1,0) $\rightarrow$ (-1,1) $\rightarrow$ (-1,2) $\rightarrow$ (-1,3) [Segment 2, 1 turn at (-1,0)]\\
    (-1,3) $\rightarrow$ (0,3) [Segment 3, 1 turn at (-1,3)]\\
    Example returned path (simplified for diagram): `[(0,0), (-1,0), (-1,3), (0,3)]` (just critical points)\\
    Full path points: `[(0,0), (-1,0), (-1,1), (-1,2), (-1,3), (0,3)]`
};

\end{tikzpicture}
