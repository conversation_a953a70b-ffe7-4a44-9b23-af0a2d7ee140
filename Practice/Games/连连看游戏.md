---
tags: [problem/game, algorithm/bfs, pattern/pathfinding, topic/grid_traversal, course/labuladong]
aliases: [连连看, Link Game, Connect Two]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第二章、经典暴力搜索算法/BFS 算法/【游戏】连连看游戏.md
---

# 【游戏】连连看游戏 (Link Game Pathfinding)

> [!NOTE] Source Annotation
> This problem and solution approach are derived from the description in "【游戏】连连看游戏.md" by Labuladong: [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第二章、经典暴力搜索算法/BFS 算法/【游戏】连连看游戏.md|Source]].
> It involves finding a path between two pieces on a board with at most two turns, a classic application of Breadth-First Search for shortest paths in terms of turns.

## Problem Statement

连连看游戏可以消除成对儿的相同棋子，但是仅当两个棋子之间连线不超过两次拐弯时才能消除，如果超过两次拐弯则不能消除。

You need to implement a `connect` function. Given a game `board`, and the coordinates of two pieces `(row1, col1)` and `(row2, col2)`, determine if these two pieces can be connected by a path with at most two turns. The path can only pass through empty cells. If they can be connected, return the list of coordinates representing the path; otherwise, return an empty list.

A special rule: "棋盘边缘棋子的连接线可能超出棋盘边界" (Lines connecting pieces near the board edge can extend beyond the board boundary). This implies that the path can utilize "virtual" empty cells one step outside the board's perimeter.

## 🧠 Core Idea: BFS for Shortest Path in Turns

This problem asks for a path with a maximum of 2 turns. This can be modeled as a shortest path problem on an implicit graph where:
-   **Nodes/States:** Represent `(row, col, direction_of_arrival, turns_made)`.
-   **Edges:** Represent extending a path in a straight line from the current state to a new cell. A turn occurs if the new direction is different from `direction_of_arrival`.
-   **Goal:** Reach `(row2, col2)` with `turns_made <= 2`.

We use [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|Breadth-First Search (BFS)]] because it naturally finds the path with the fewest "segments" or, in this case, fewest turns if we define states carefully.

## 🛠️ BFS State and Transitions

-   **State in Queue:** `(r, c, turns, last_direction_idx, current_path_points)`
    -   `(r, c)`: Current cell coordinates.
    -   `turns`: Number of turns made to reach this cell (0, 1, or 2).
    -   `last_direction_idx`: The direction of the segment that *arrived* at `(r, c)`. (0:Up, 1:Down, 2:Left, 3:Right, -1 for start).
    -   `current_path_points`: A list of all `(row, col)` coordinates making up the path from `(row1, col1)` to `(r, c)`.
-   **Visited Set:** To avoid cycles and redundant exploration, we can use a set `visited` storing tuples `(r, c, turns, last_direction_idx)`.

### "Out of Bounds" Rule Interpretation
The rule "棋盘边缘棋子的连接线可能超出棋盘边界" means we can think of the effective grid as being padded by one layer of empty cells around the actual board.
-   Board dimensions: `R` rows, `C` columns (indices `0..R-1`, `0..C-1`).
-   Extended grid for pathfinding: `row_idx \in [-1, R]`, `col_idx \in [-1, C]`.
-   A cell `(nr, nc)` is "walkable" if:
    1.  It is the target `(row2, col2)`.
    2.  It is on the actual board (`0 <= nr < R` and `0 <= nc < C`) AND `board[nr][nc] == 0` (assuming `0` means empty).
    3.  It is an "extended boundary cell" (e.g., `nr = -1` or `nr = R` or `nc = -1` or `nc = C`). These act as temporary empty cells for routing.

## ⚙️ Algorithm Steps for `connect` function

1.  Initialize a queue. Add the starting state: `((row1, col1), 0, -1, [(row1, col1)])`.
    `(current_coords, num_turns, arrival_direction_idx, path_list)`
2.  Initialize `visited = set()`. Add `(r1, c1, 0, -1)` to `visited`. (Note: the state should include turns and direction to allow revisiting a cell with a different path configuration if it leads to fewer turns).
3.  Define movement directions: `dr = [-1, 1, 0, 0]` (Up, Down, Left, Right for row changes), `dc = [0, 0, -1, 1]` (for col changes). Direction indices: 0:Up, 1:Down, 2:Left, 3:Right.
4.  While the queue is not empty:
    a.  Dequeue `((curr_r, curr_c), turns_so_far, last_dir_idx, current_path)`
    b.  For each of the 4 possible `new_dir_idx` (0 to 3):
        i.  Calculate `path_turns_if_this_segment_is_taken = turns_so_far`.
        ii. If `last_dir_idx != -1` (not the start) AND `new_dir_idx != last_dir_idx` (a turn is made):
            `path_turns_if_this_segment_is_taken += 1`.
        iii. If `path_turns_if_this_segment_is_taken > 2`, this path is invalid for this segment; continue to next direction.
        iv. Explore in `new_dir_idx` (extend current segment or start new one):
            -   Initialize `next_r_in_segment, next_c_in_segment = curr_r, curr_c`.
            -   `points_in_this_segment = []`
            -   Loop (extend straight line):
                1.  `next_r_in_segment += dr[new_dir_idx]`, `next_c_in_segment += dc[new_dir_idx]`.
                2.  If `not is_walkable(next_r_in_segment, next_c_in_segment, board, r2, c2)`: Break inner loop.
                3.  `points_in_this_segment.append((next_r_in_segment, next_c_in_segment))`
                4.  If `(next_r_in_segment, next_c_in_segment) == (r2, c2)`: Path found! Return `current_path + points_in_this_segment`.
                5.  `state_key = (next_r_in_segment, next_c_in_segment, path_turns_if_this_segment_is_taken, new_dir_idx)`
                6.  If `state_key` not in `visited`:
                    -   `visited.add(state_key)`
                    -   `queue.append( ((next_r_in_segment, next_c_in_segment), path_turns_if_this_segment_is_taken, new_dir_idx, current_path + points_in_this_segment) )`
5.  If queue becomes empty, no path found. Return `[]`.

### Helper `is_walkable(r, c, R_rows, C_cols, board_data, target_r, target_c)`
```python
def is_walkable(r, c, R_rows, C_cols, board_data, target_r, target_c):
    if r == target_r and c == target_c: return True
    if not (-1 <= r <= R_rows and -1 <= c <= C_cols): return False
    if 0 <= r < R_rows and 0 <= c < C_cols:
        return board_data[r][c] == 0 # Assuming 0 is empty
    return True # Extended boundary cell
```

### Python Implementation (More detailed sketch based on the logic above)
```python
import collections

def connect_link_game(board_grid: list[list[int]], r_start: int, c_start: int, r_target: int, c_target: int) -> list[tuple[int, int]]:
    R_count = len(board_grid)
    C_count = len(board_grid[0])

    # dr, dc for Up, Down, Left, Right
    directions_map = [(-1, 0), (1, 0), (0, -1), (0, 1)] 

    # Queue: ((row, col), turns, last_direction_index, path_list)
    q = collections.deque([((r_start, c_start), 0, -1, [(r_start, c_start)])])

    # Visited: (row, col, turns, last_direction_index)
    # Key change: The state must be (row, col, direction_OF_THIS_SEGMENT, turns_IN_THIS_SEGMENT)
    # This is because you can arrive at a cell (r,c) multiple times via different routes/turns.
    # The key is: how many turns did it take to *reach the start of the current straight segment*.
    # Let's use BFS on cells, where each cell stores (min_turns_to_reach_cell, direction_arrived_from)
    # This is getting complex. The simpler approach is:
    # BFS queue stores (r, c, direction_of_segment_ENDING_at_rc, num_turns_to_reach_rc_this_way, path)

    # Labuladong's game panel likely has a simpler interpretation or internal state management.
    # Let's use the state (r, c, turns_taken, last_dir_idx, current_path_to_rc)
    # The problem is that simply visiting (r,c) is not enough; how we got there (direction, turns) matters.
    # The `visited` set needs to capture this: (r, c, direction_of_arrival, turns_so_far)
    visited_states = set()
    visited_states.add((r_start, c_start, -1, 0)) # (r,c, prev_dir, turns)

    while q:
        (current_coords, turns_so_far, prev_dir_idx, path_coords) = q.popleft()
        curr_r, curr_c = current_coords

        # Try extending path in 4 new directions (or continuing current)
        for new_dir_idx in range(4):
            # Calculate turns if this move is a turn
            turns_for_new_segment = turns_so_far
            if prev_dir_idx != -1 and new_dir_idx != prev_dir_idx:
                turns_for_new_segment += 1

            if turns_for_new_segment > 2:
                continue

            # Explore in new_dir_idx
            temp_r, temp_c = curr_r, curr_c
            segment_extension_points = [] # Points added in this straight segment after (curr_r, curr_c)

            while True: # Move straight in new_dir_idx
                next_r = temp_r + directions_map[new_dir_idx][0]
                next_c = temp_c + directions_map[new_dir_idx][1]

                if not is_walkable(next_r, next_c, R_count, C_count, board_grid, r_target, c_target):
                    break # Obstacle or too far out

                # Valid next step in this segment
                temp_r, temp_c = next_r, next_c
                segment_extension_points.append((temp_r, temp_c))

                current_full_path = path_coords + segment_extension_points

                if temp_r == r_target and temp_c == c_target:
                    return current_full_path # Target reached

                # Check if this new state is worth exploring
                new_state_key = (temp_r, temp_c, new_dir_idx, turns_for_new_segment)
                if new_state_key not in visited_states:
                    visited_states.add(new_state_key)
                    q.append(((temp_r, temp_c), turns_for_new_segment, new_dir_idx, current_full_path))
                # else: This cell via this direction and turns has been reached/queued already.
    return []
```

## Complexity
- **State Space:** $R \times C \times 4 \times 3$ (row, col, arrival_direction, turns).
- **Time:** Each state processed once. Transitions involve iterating straight lines (at most $R+C$ steps). Total time $O(R \cdot C \cdot \text{Turns} \cdot \text{Directions} \cdot (R+C))$. Given Turns=3, Directions=4, it's $O(R \cdot C \cdot (R+C))$.
- **Space:** $O(R \cdot C \cdot \text{Turns} \cdot \text{Directions})$ for `visited` and `queue` (path lists can make it larger if not careful, depends on how many partial paths are stored).

## 总结 (Summary)
- 连连看 pathfinding with turn limits is a BFS problem on an expanded state space.
- State includes current position, turns taken, and arrival direction.
- Path can go one step outside board boundaries.
- BFS explores paths, pruning those exceeding 2 turns.
- Need to store the actual path points if the problem requires returning the path.

---
Parent: [[Interview/Practice/Games/index|Game Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Framework]]
