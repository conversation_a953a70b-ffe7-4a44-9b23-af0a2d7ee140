
import os
import textwrap

# Helper function to create directories if they don't exist
def ensure_dir(directory_path):
    os.makedirs(directory_path, exist_ok=True)

# --- Content Generation Functions ---

def create_linked_list_two_pointers_overview(path):
    content = r"""---
tags: [concept/algorithms, pattern/two_pointers, topic/linked_list, type/framework, course/labuladong]
aliases: [Linked List Two Pointers, 双指针链表技巧, Two Pointer Techniques for Linked Lists]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道链表题目.md]].
> This note summarizes common two-pointer techniques for solving linked list problems.

# Linked List: Two Pointer Techniques

The [[Interview/Concept/Algorithms/Two Pointers/00 - Two Pointers - Introduction|Two Pointers technique]] is highly effective for solving a variety of linked list problems. This note summarizes several common patterns and their applications, primarily focusing on Fast-Slow Pointers and general iterative two-pointer strategies.

## 🔗 Core Patterns for Linked Lists

### 1. Merging Two Sorted Lists
- **Problem:** [[Interview/Practice/LeetCode/LC21 - Merge Two Sorted Lists|LC21 - Merge Two Sorted Lists]]
- **Technique:** Use three pointers: `p1` for list1, `p2` for list2, and `p_current` to build the new merged list. Compare `p1.val` and `p2.val`, append the smaller node to `p_current`, and advance the corresponding pointer.
- **Key Idea:** Similar to the merge step in [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]]. Often utilizes a [[Interview/Concept/Algorithms/Linked List/00 - Linked List - Virtual Head Node|Virtual Head Node]] to simplify list construction.
- **Visualization (Labuladong's "拉拉链" - Zipping Analogy):**
  Imagine two zipper tracks (`l1`, `l2`) and a zipper pull (`p`) that interleaves them based on value.
  ```tikz
  \begin{tikzpicture}[
      lnode/.style={draw, rectangle, minimum height=0.7cm, minimum width=1cm, font=\sffamily\small},
      ptr/.style={font=\sffamily\bfseries\tiny, above=1pt},
      merged_link/.style={->, thick, green!60!black},
      list_link/.style={->, thick}
  ]
  \node[lnode] (l1_1) at (0,2) {1}; \node[lnode] (l1_2) at (1.5,2) {4}; \node[lnode] (l1_3) at (3,2) {5};
  \draw[list_link] (l1_1) -- (l1_2); \draw[list_link] (l1_2) -- (l1_3);
  \node[ptr, red] at (l1_1.north) {p1};
  \node at (-1,2) {L1:};

  \node[lnode] (l2_1) at (0,1) {1}; \node[lnode] (l2_2) at (1.5,1) {3}; \node[lnode] (l2_3) at (3,1) {4};
  \draw[list_link] (l2_1) -- (l2_2); \draw[list_link] (l2_2) -- (l2_3);
  \node[ptr, blue] at (l2_1.north) {p2};
  \node at (-1,1) {L2:};

  \node[lnode, fill=gray!20] (dummy) at (0,0) {dummy}; \node[ptr, green!60!black] at (dummy.north) {p};
  \node[lnode, fill=green!10] (m1) at (1.5,0) {};
  \node[lnode, fill=green!10] (m2) at (3,0) {};
  \node[lnode, fill=green!10] (m3) at (4.5,0) {};
  \node at (-1,0) {Merged:};
  
  \draw[merged_link, dashed] (dummy) -- (m1); % Conceptual start
  % Example after a few steps: Merged: dummy -> 1 (from l1) -> 1 (from l2) -> 3 (from l2)...
  % p1 points to l1_2(4), p2 points to l2_3(4)
  % p points to the last added node in merged list.
  \end{tikzpicture}
  ```

### 2. Partitioning a List
- **Problem:** [[Interview/Practice/LeetCode/LC86 - Partition List|LC86 - Partition List]]
- **Technique:** Use two dummy head nodes and two current pointers (`p1`, `p2`) to build two separate lists: one for elements less than a pivot `x`, and another for elements greater than or equal to `x`. Finally, connect these two lists.
- **Key Idea:** Decomposes the original list into two, then reassembles. Essential to null-terminate the second list before connecting to avoid cycles if the last node of the original list was part of the "less than x" partition.
- Also see: [[Interview/Concept/Algorithms/Linked List/00 - Linked List - Virtual Head Node|Virtual Head Node]].

### 3. Merging k Sorted Lists
- **Problem:** [[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Sorted Lists]]
- **Technique (Primary):** Use a Min-Heap ([[Interview/Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization|Priority Queue]]) to efficiently find the smallest among the current heads of all `k` lists.
- **Connection to Two Pointers:** The heap helps manage `k` pointers. The underlying "merge" logic if done pairwise would use the two-pointer merging. A divide-and-conquer approach for merging `k` lists also boils down to repeatedly merging two lists.

### 4. Finding Nth Node From End / Middle Node
These use **Fast and Slow Pointers**.
- **Middle Node ([[Interview/Practice/LeetCode/LC876 - Middle of the Linked List|LC876]]):** `slow` moves 1 step, `fast` moves 2 steps. When `fast` reaches end, `slow` is at middle.
- **Nth Node From End ([[Interview/Practice/LeetCode/LC19 - Remove Nth Node From End of List|LC19]]):** `p1` moves `N` steps ahead. Then `p1` and `p2` move together. When `p1` reaches end, `p2` is at Nth from end (or N+1th if deleting).
  ```tikz
  \begin{tikzpicture}[
      node_style/.style={draw, rectangle, minimum size=0.7cm, font=\sffamily\small},
      ptr_label/.style={font=\sffamily\bfseries\tiny, above=0.1cm},
      arrow/.style={->, thick}
  ]
      \node[node_style] (n1) at (0,0) {1};
      \node[node_style] (n2) at (1.5,0) {2};
      \node[node_style] (n3) at (3,0) {3};
      \node[node_style] (n4) at (4.5,0) {4};
      \node[node_style] (n5) at (6,0) {5};
      \node (null) at (7.5,0) {null};
      \draw[arrow] (n1) -- (n2); \draw[arrow] (n2) -- (n3); \draw[arrow] (n3) -- (n4); \draw[arrow] (n4) -- (n5); \draw[arrow] (n5) -- (null);

      \node at (3,-0.8) {Find 2nd from end (N=2):};
      % After p1 moves N steps
      \node[ptr_label, red] at (n3.north) {p1};
      \node[ptr_label, blue] at (n1.north) {p2};
      \node at (3,-1.5) {1. p1 moves N steps.};
      
      % Both move until p1 hits null
      \begin{scope}[yshift=-2.5cm]
          \node[node_style] (nn1) at (0,0) {1}; \node[node_style] (nn2) at (1.5,0) {2}; \node[node_style] (nn3) at (3,0) {3};
          \node[node_style, fill=yellow!30] (nn4) at (4.5,0) {4}; \node[ptr_label, blue] at (nn4.north) {p2 (Target)};
          \node[node_style] (nn5) at (6,0) {5}; 
          \node (nnull) at (7.5,0) {null}; \node[ptr_label, red] at (nnull.north east) {p1};
          \draw[arrow] (nn1) -- (nn2); \draw[arrow] (nn2) -- (nn3); \draw[arrow] (nn3) -- (nn4); \draw[arrow] (nn4) -- (nn5); \draw[arrow] (nn5) -- (nnull);
          \node at (3,-0.8) {2. p1, p2 move together. When p1 is null, p2 is at Nth from end.};
      \end{scope}
  \end{tikzpicture}
  ```

### 5. Detecting Cycles & Finding Cycle Start
- **Detect Cycle ([[Interview/Practice/LeetCode/LC141 - Linked List Cycle|LC141]]):** `slow` moves 1 step, `fast` moves 2 steps. If they meet, cycle exists.
- **Find Cycle Start ([[Interview/Practice/LeetCode/LC142 - Linked List Cycle II|LC142]]):** After `slow` and `fast` meet, reset `slow` to `head`. Move both `slow` and `fast` one step at a time. Their next meeting point is the cycle start.
  - The mathematical proof relies on distances: `X + Y = kL` where `X` is head-to-cycle-start, `Y` is cycle-start-to-meeting-point, `L` is cycle length.

### 6. Finding Intersection of Two Lists
- **Problem:** [[Interview/Practice/LeetCode/LC160 - Intersection of Two Linked Lists|LC160 - Intersection of Two Linked Lists]]
- **Technique 1 (Length Alignment):** Calculate lengths of `listA` and `listB`. Advance the pointer of the longer list by the difference in lengths. Then, move both pointers one step at a time until they meet at the intersection or reach `null`.
- **Technique 2 (Concatenation Logic):**
  - `p1` traverses `A` then `B`. `p2` traverses `B` then `A`.
  - `p1 = headA, p2 = headB`
  - Loop `while p1 != p2`:
    - `p1 = p1.next if p1 else headB`
    - `p2 = p2.next if p2 else headA`
  - If they meet, it's the intersection. If no intersection, they both become `null` simultaneously.
  - This ensures both pointers travel `len(A) + len(B)` total distance before meeting (if an intersection exists) or both becoming `null`.
  ```tikz
  \begin{tikzpicture}[
      lnode/.style={draw, rectangle, minimum size=0.6cm, font=\sffamily\tiny},
      ptr/.style={font=\sffamily\bfseries\tiny, above=1pt, red},
      path_arrow/.style={->, thick, blue, dashed, shorten >=1pt, shorten <=1pt}
  ]
  % List A: a1 -> a2 -> c1 -> c2 -> c3
  \node[lnode] (a1) at (0,1) {a1}; \node[lnode] (a2) at (1,1) {a2};
  % List B: b1 -> b2 -> b3 -> c1 -> c2 -> c3
  \node[lnode] (b1) at (0,0) {b1}; \node[lnode] (b2) at (1,0) {b2}; \node[lnode] (b3) at (2,0) {b3};
  % Common part
  \node[lnode, fill=green!20] (c1) at (2,1) {c1};
  \node[lnode, fill=green!20] (c2) at (3,1) {c2};
  \node[lnode, fill=green!20] (c3) at (4,1) {c3};

  \draw[->] (a1) -- (a2); \draw[->] (a2) -- (c1);
  \draw[->] (b1) -- (b2); \draw[->] (b2) -- (b3); \draw[->] (b3) -- (c1);
  \draw[->] (c1) -- (c2); \draw[->] (c2) -- (c3);

  \node[ptr] at (a1.north) {p1 starts};
  \node[ptr, blue] at (b1.north) {p2 starts};
  
  \node[text width=5cm,align=center] at (2.5,-1) {
  Path of p1: a1-a2-c1-c2-c3-null $\rightarrow$ (switches to headB) b1-b2-b3-c1...\\
  Path of p2: b1-b2-b3-c1-c2-c3-null $\rightarrow$ (switches to headA) a1-a2-c1...\\
  Both meet at c1.
  };
  \end{tikzpicture}
  ```

## 总结 (Summary)
- Two-pointer techniques are versatile for linked list problems, offering efficient solutions.
- **Fast-Slow Pointers:** Ideal for finding middle, Nth from end, detecting cycles, and finding cycle start points.
- **General Two/Three Pointers:** Useful for merging, partitioning, and finding intersections.
- Understanding the specific movement logic (e.g., fixed gap, different speeds, list-switching) is key to applying these patterns correctly.
- [[Interview/Concept/Algorithms/Linked List/00 - Linked List - Virtual Head Node|Virtual head nodes]] are often helpful for simplifying edge cases in list modification problems.

---
Parent: [[Interview/Concept/Algorithms/Two Pointers/00 - Two Pointers - Introduction|Two Pointers - Introduction]]
Next: (Could be other Two Pointer applications or specific linked list problem notes)
Related Problems: (See list in "Core Patterns" section above)
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Two Pointers"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists.md"), 'w', encoding='utf-8') as f:
        f.write(textwrap.dedent(content))
    print("Created/Updated: Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists.md")

def create_virtual_head_node_concept(path):
    content = r"""---
tags: [concept/data_structures, topic/linked_list, type/technique, pattern/dummy_node, course/labuladong]
aliases: [Dummy Head Node, Sentinel Node Linked List, 虚拟头节点]
---

> [!NOTE] Source Annotation
> This technique is frequently mentioned by Labuladong, for example, in [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道链表题目.md]] for problems like "Merge Two Sorted Lists" and "Remove Nth Node From End of List".

# Linked List: Virtual Head Node Technique

The Virtual Head Node (also known as a Dummy Head Node or Sentinel Node) is a common technique used in linked list problems to simplify edge cases, particularly those involving modifications at the beginning of the list or constructing a new list.

## 🎯 Core Idea
A virtual head node is an auxiliary node placed at the very beginning of a linked list. It does not store any meaningful data related to the problem but acts as a placeholder.

- **Original List:** `head -> node1 -> node2 -> ...`
- **With Virtual Head:** `dummy_head -> head -> node1 -> node2 -> ...`

```tikz
\begin{tikzpicture}[
    lnode/.style={draw, rectangle, minimum height=0.7cm, minimum width=1cm, font=\sffamily\small},
    dnode/.style={lnode, fill=gray!30, dashed},
    arrow/.style={->, thick}
]
    % Original
    \node[lnode] (h1) at (0,1) {H}; \node[lnode] (n1_1) at (1.5,1) {N1}; \node[lnode] (n1_2) at (3,1) {N2};
    \draw[arrow] (h1) -- (n1_1); \draw[arrow] (n1_1) -- (n1_2); \node at (4,1) {...};
    \node at (-1,1) {Original:};

    % With Dummy
    \node[dnode] (d0) at (0,0) {Dummy};
    \node[lnode] (h0) at (1.5,0) {H}; \node[lnode] (n0_1) at (3,0) {N1}; \node[lnode] (n0_2) at (4.5,0) {N2};
    \draw[arrow] (d0) -- (h0); \draw[arrow] (h0) -- (n0_1); \draw[arrow] (n0_1) -- (n0_2); \node at (5.5,0) {...};
    \node at (-1,0) {With Dummy:};
    
\end{tikzpicture}
```

## ✨ Advantages

1.  **Simplifies Head Operations:**
    - When inserting or deleting the actual head node of a list, the `head` pointer itself needs to change. This often requires special conditional logic.
    - With a dummy head, the actual head node is `dummy_head.next`. Operations on the "first real node" become operations on `dummy_head.next`, which are handled like operations on any other node (modifying `prev.next`).
    - Example: Deleting the first node. Without dummy: `head = head.next`. With dummy: `dummy.next = dummy.next.next`. The `dummy` pointer itself never changes.

2.  **Uniform Node Handling:**
    - All nodes (including the first actual node) have a preceding node (the dummy head for the first real node, or a real node for subsequent ones). This can eliminate special `if (prev == null)` checks in loops.

3.  **Simplified List Construction:**
    - When building a new list (e.g., in merging two sorted lists), start with a dummy head. Use a `current` pointer initialized to this dummy head. Append new nodes to `current.next` and advance `current = current.next`.
    - The final constructed list is `dummy_head.next`. This avoids needing to handle the "first node insertion" as a special case.

## 🚀 Common Use Cases

-   **[[Interview/Practice/LeetCode/LC21 - Merge Two Sorted Lists|LC21 - Merge Two Sorted Lists]]:** A dummy head is used to start the merged list. `p_merged = dummy_head; while(l1 && l2) { ... p_merged.next = ...; p_merged = p_merged.next; } return dummy_head.next;`
-   **[[Interview/Practice/LeetCode/LC19 - Remove Nth Node From End of List|LC19 - Remove Nth Node From End of List]]:** A dummy head makes it easier to remove the actual head of the list if it's the Nth node from the end. The pointer `p2` (which finds the node *before* the one to delete) can start at `dummy`.
-   **[[Interview/Practice/LeetCode/LC206 - Reverse Linked List|LC206 - Reverse Linked List]] (Iterative):** While not strictly necessary, a dummy node could be imagined as the initial `prev = null`.
-   **[[Interview/Practice/LeetCode/LC86 - Partition List|LC86 - Partition List]]:** Two dummy heads are used to build two separate lists.

## 🛠️ Implementation Detail
Typically, initialize `dummy = ListNode(-1)` (or any arbitrary value, as it's not part of the data). `dummy.next = original_head`. After operations, the new list starts at `dummy.next`.

```python
# class ListNode:
#     def __init__(self, val=0, next=None):
#         self.val = val
#         self.next = next

# Example: Removing head if value is X
def remove_head_if_val_is_x(head, x_val):
    dummy = ListNode(-1, head)
    prev = dummy
    curr = head

    # This loop now handles removing the actual head uniformly
    if curr and curr.val == x_val:
        prev.next = curr.next # 'prev' is dummy, so dummy.next is updated
        # No need to reassign 'head' variable explicitly.
    
    return dummy.next # The new head of the list
```

## 总结 (Summary)
- A virtual head node (dummy node) is a placeholder node added to the beginning of a linked list.
- It simplifies list manipulations, especially at the head of the list, by ensuring every actual node has a predecessor.
- Useful for constructing new lists iteratively and for deletion operations near the start.
- The final result is typically `dummy_head.next`.

---
Parent: [[Interview/Concept/Data Structures/Linked List/index|Linked List Concepts]]
Related: [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Linked List"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Linked List/00 - Linked List - Virtual Head Node.md"), 'w', encoding='utf-8') as f:
        f.write(textwrap.dedent(content))
    print("Created/Updated: Interview/Concept/Algorithms/Linked List/00 - Linked List - Virtual Head Node.md")

def create_linked_list_reversal_techniques(path):
    content = r"""---
tags: [concept/algorithms, topic/linked_list, type/technique, pattern/list_reversal, course/labuladong]
aliases: [Linked List Reversal, Reverse Linked List Methods, 翻转链表技巧]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷链表算法/单链表的花式反转方法汇总.md]].
> This note covers common techniques for reversing linked lists: full reversal, partial reversal.

# Linked List: Reversal Techniques

Reversing a linked list or parts of it is a fundamental linked list manipulation skill. Labuladong's article covers several variations, including iterative and recursive approaches.

## 1. Reverse Entire Single Linked List ([[Interview/Practice/LeetCode/LC206 - Reverse Linked List|LC206]])

### a. Iterative Approach
This is the most common way. It involves three pointers: `prev`, `curr`, and `next_temp`.
- `prev`: Points to the previous node in the reversed part (initially `None`).
- `curr`: Points to the current node being processed (initially `head`).
- `next_temp`: Temporarily stores `curr.next` before `curr.next` is changed.

**Steps:**
1. Initialize `prev = None`, `curr = head`.
2. Loop while `curr` is not `None`:
   a. `next_temp = curr.next` (Save next node).
   b. `curr.next = prev` (Reverse current node's pointer).
   c. `prev = curr` (Move `prev` one step forward).
   d. `curr = next_temp` (Move `curr` one step forward).
3. After the loop, `prev` points to the new head of the reversed list. Return `prev`.

Labuladong's visualization panel `div_reverse-linked-list-iter` shows this.

### b. Recursive Approach
Define `reverseList(head)` to return the new head of the reversed list starting at `head`.
1.  **Base Case:** If `head` is `None` or `head.next` is `None` (list is empty or has one node), it's already "reversed". Return `head`.
2.  **Recursive Step:**
    a.  `last_node_of_reversed_sublist = reverseList(head.next)`: Recursively reverse the rest of the list (`head.next` onwards). `last_node_of_reversed_sublist` will be the new head of this reversed sublist (which is the original tail of the list).
    b.  Now, `head.next` (which was the original second node) is the *tail* of the reversed sublist. Make it point back to `head`: `head.next.next = head`.
    c.  Set `head.next = None` because `head` is now the new tail of the fully reversed list.
    d.  Return `last_node_of_reversed_sublist` (this is the overall new head).

Labuladong's visualization panel `div_reverse-linked-list` explains this with diagrams `![](/algo/images/reverse-linked-list/3.jpg)` to `![](/algo/images/reverse-linked-list/5.jpg)`.

```tikz
\begin{tikzpicture}[
    lnode/.style={draw, rectangle, minimum height=0.7cm, minimum width=1cm, font=\sffamily\small},
    arrow/.style={->, thick},
    rev_arrow/.style={->, thick, red, dashed}
]
    \node[lnode] (n1) at (0,0) {1}; \node[lnode] (n2) at (1.5,0) {2};
    \node[lnode] (n3) at (3,0) {3}; \node[lnode] (n4) at (4.5,0) {null};
    \draw[arrow] (n1) -- (n2); \draw[arrow] (n2) -- (n3); \draw[arrow] (n3) -- (n4);
    \node at (1.5, -0.7) {Original: 1 -> 2 -> 3};

    % After reverseList(2) returns 3 (new head of 3->2)
    % head is 1, head.next is 2.
    % head.next.next = head means 2.next = 1
    \node at (1.5, -1.5) {Recursive call `reverseList(1)`:};
    \node at (1.5, -2) {`last = reverseList(2)` returns Node(3) where `3 -> 2 -> null`};
    \node[lnode] (r_n3) at (0,-3) {3}; \node[lnode] (r_n2) at (1.5,-3) {2};
    \node[lnode] (r_n1) at (3,-3) {1}; \node[lnode] (r_nN) at (4.5,-3) {null};
    \draw[arrow] (r_n3) -- (r_n2);
    \node[anchor=south] at (r_n1.north) {head=1};
    \node[anchor=south] at (r_n2.north) {head.next=2};
    \node at (1.5, -3.7) {State before `head.next.next = head` in `reverseList(1)` call};
    \node at (1.5, -4.2) {Reversed sublist: `3 -> 2`. `head` is `1`, `head.next` is `2` (tail of sublist).};

    % Make 2 point to 1
    \draw[rev_arrow] (r_n2.east) to[bend left=40] node[midway,below] {`2.next = 1`} (r_n1.west);
    % Make 1 point to null
    \draw[rev_arrow] (r_n1) -- (r_nN);

    \node at (1.5, -5) {Final: 3 -> 2 -> 1 -> null};
\end{tikzpicture}
```

## 2. Reverse First N Nodes of a List
A helper function often used for more complex reversals.
`reverseN(head, n)`: Reverses the first `n` nodes of the list starting at `head` and returns the new head.
- **Base Case:** If `n == 1`, no reversal needed for the first node, return `head`. (The (n+1)th node becomes the successor).
- **Recursive Step:**
    - `last = reverseN(head.next, n - 1)`: Reverse first `n-1` nodes of sublist.
    - `successor_node = head.next.next` (store before modification if needed, tricky part not explicitly in Labuladong's simplified `reverseN`).
    - `head.next.next = head`.
    - `head.next = successor_node_from_original_nth_plus_1` (this needs to be passed back or handled carefully).
Labuladong's `reverseN` implementation simplifies this by returning the `successor` as well, or by connecting `head.next` to the $(N+1)^{th}$ node *after* the recursive call returns. The key is to link the original `head` (which becomes the $N^{th}$ node after partial reversal) to the original $(N+1)^{th}$ node.

## 3. Reverse List Between `m` and `n` (LC92)
[[Interview/Practice/LeetCode/LC92 - Reverse Linked List II|LC92 - Reverse Linked List II]]
- Use recursion. If `m == 1`, it's `reverseN(head, n)`.
- If `m > 1`, `head.next = reverseBetween(head.next, m - 1, n - 1)`. This reduces `m` until the subproblem starts at the first node to be reversed.

## 4. Reverse Nodes in k-Group (LC25)
[[Interview/Practice/LeetCode/LC25 - Reverse Nodes in k-Group|LC25 - Reverse Nodes in k-Group]]
- Iteratively or recursively reverse groups of `k` nodes.
- **Iterative:** Find `k` nodes, reverse them using standard iterative reversal, connect to previous group's tail and next group's head.
- **Recursive `reverseKGroup(head, k)`:**
    - Check if there are at least `k` nodes. If not, return `head`.
    - Find the `k`-th node (`b`). The `(k+1)`-th node is `b.next`.
    - Reverse the `k` nodes from `head` to `b`. `new_head = reverse_first_k_nodes(head, b)`.
    - The original `head` is now the tail of this reversed group.
    - `head.next = reverseKGroup(original_b_next, k)` (Recursively reverse rest).
    - Return `new_head`.

## 总结 (Summary)
- Linked list reversal is a core skill. Iterative approach uses `prev, curr, next_temp`. Recursive approach uses post-order logic.
- Partial reversals (first N, or between m and n) build upon the full reversal logic, often by carefully managing connections to the un-reversed parts of the list.
- k-Group reversal combines these ideas, typically processing the list in chunks of `k`.
- Recursive solutions for list reversal often benefit from helper functions that define clear contracts for what sub-problem they solve (e.g., "reverse first N nodes and return new head AND (N+1)th node").

---
Parent: [[Interview/Concept/Algorithms/Linked List/index|Linked List Concepts]]
Related: [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Linked List"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Linked List/01 - Linked List Reversal Techniques.md"), 'w', encoding='utf-8') as f:
        f.write(textwrap.dedent(content))
    print("Created/Updated: Interview/Concept/Algorithms/Linked List/01 - Linked List Reversal Techniques.md")

def create_palindrome_linked_list_detection(path):
    content = r"""---
tags: [concept/algorithms, topic/linked_list, type/technique, pattern/palindrome_check, pattern/fast_slow_pointers, pattern/list_reversal, course/labuladong]
aliases: [Palindrome Linked List, 回文链表判断]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷链表算法/如何判断回文链表.md]].
> This note explains the common optimized approach to check if a linked list is a palindrome.

# Linked List: Palindrome Detection

Determining if a singly linked list is a palindrome (reads the same forwards and backwards) requires comparing the first half of the list with the reversed second half.

## 🎯 Core Idea & Algorithm
The standard $O(N)$ time and $O(1)$ space (excluding recursion stack for reversal if recursive) approach involves:
1.  **Find the Middle Node:** Use the [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|fast and slow pointer technique]] to find the middle of the linked list.
    - If the list has an odd number of nodes, `slow` will point to the exact middle.
    - If even, `slow` points to the start of the second half (or the first of two middle nodes, depending on specific implementation).
2.  **Reverse the Second Half:** Reverse the portion of the list starting from `slow.next` (or `slow` itself if adjusting for odd/even length). See [[Interview/Concept/Algorithms/Linked List/01 - Linked List Reversal Techniques|Linked List Reversal Techniques]].
3.  **Compare Halves:** Iterate with one pointer from `head` (start of first half) and another from the new head of the reversed second half. Compare their values node by node.
    - If any mismatch, it's not a palindrome.
    - If they match until one half is exhausted, it's a palindrome.
4.  **(Optional) Restore List:** If the original list structure must be preserved, reverse the second half again to link it back. This step is crucial if modifications are not allowed permanently.

**Labuladong's Visualization Analogy for Palindrome Strings:**
`![](/algo/images/palindrome-linkedlist/1.png)` (Shows left/right pointers for string palindrome)
`![](/algo/images/palindrome-linkedlist/2.png)` (Extends this to linked list idea)

## 🛠️ Step-by-Step Implementation Details

```python
# class ListNode:
#     def __init__(self, val=0, next=None): self.val = val; self.next = next

class SolutionPalindromeLL:
    def isPalindrome(self, head: ListNode) -> bool:
        if not head or not head.next:
            return True

        # 1. Find the middle of the list (slow will point to middle or first of two middles)
        slow, fast = head, head
        while fast.next and fast.next.next: # Ensures fast stops correctly for odd/even
            slow = slow.next
            fast = fast.next.next
        
        # `slow` is now at the end of the first half.
        # `slow.next` is the start of the second half.
        
        # 2. Reverse the second half of the list
        head_second_half_reversed = self._reverse_list(slow.next)
        slow.next = None # Disconnect first half from second for comparison (optional but clean)

        # 3. Compare the first half with the reversed second half
        p1 = head
        p2 = head_second_half_reversed
        is_palindrome_result = True
        while p1 and p2: # Or just while p2, as reversed second half might be shorter for odd length
            if p1.val != p2.val:
                is_palindrome_result = False
                break
            p1 = p1.next
            p2 = p2.next
        
        # 4. (Optional) Restore the list by reversing the second half again
        # and connecting slow.next back to it.
        # slow.next = self._reverse_list(head_second_half_reversed) # Restore
            
        return is_palindrome_result

    def _reverse_list(self, node: ListNode) -> ListNode:
        prev = None
        curr = node
        while curr:
            next_temp = curr.next
            curr.next = prev
            prev = curr
            curr = next_temp
        return prev # New head of the reversed list
```

### Detailed Breakdown from Labuladong's article:
- **`middleNode(head)`:** Finds middle (implementation might vary slightly if middle of first half or second half is desired).
  `![](/algo/images/palindrome-linkedlist/3.gif)`
- **`reverse(head)`:** Reverses a list.
- **Main logic:**
  `slow = middleNode(head)`
  `head2 = reverse(slow.next)` (if middle means end of first half)
  `slow.next = None`
  Compare `head` with `head2`.
  `slow.next = reverse(head2)` (Restore)

## Complexity Analysis
- **Time Complexity:** $O(N)$.
    - Finding middle: $O(N)$.
    - Reversing second half: $O(N/2) = O(N)$.
    - Comparing halves: $O(N/2) = O(N)$.
    - (Optional) Restoring list: $O(N/2) = O(N)$.
- **Space Complexity:** $O(1)$ if reversal is iterative. If recursive reversal is used for the second half, then $O(N)$ for recursion stack in worst case (skewed list, though list length here is N/2). The provided solution uses iterative reversal.

## 总结 (Summary)
- Palindrome linked list detection typically involves:
    1. Finding the middle node using fast/slow pointers.
    2. Reversing the second half of the list.
    3. Comparing the first half with the reversed second half.
    4. (Optionally) Restoring the list by re-reversing the second half.
- This achieves $O(N)$ time complexity and $O(1)$ auxiliary space (for iterative reversal).
- This technique combines several fundamental linked list operations.

---
Parent: [[Interview/Concept/Algorithms/Linked List/index|Linked List Concepts]]
Related: [[Interview/Practice/LeetCode/LC234 - Palindrome Linked List|LC234 - Palindrome Linked List]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Linked List"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Linked List/02 - Palindrome Linked List Detection.md"), 'w', encoding='utf-8') as f:
        f.write(textwrap.dedent(content))
    print("Created/Updated: Interview/Concept/Algorithms/Linked List/02 - Palindrome Linked List Detection.md")

def create_lc141(path):
    content = r"""---
tags: [problem/leetcode, lc/easy, topic/linked_list, topic/two_pointers, pattern/fast_slow_pointers, course/labuladong]
aliases: [LC141, LeetCode 141. Linked List Cycle]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 141. Linked List Cycle
> Solution from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道链表题目.md]].

# LeetCode 141: Linked List Cycle

## Problem Statement

Given `head`, the head of a linked list, determine if the linked list has a cycle in it.

There is a cycle in a linked list if there is some node in the list that can be reached again by continuously following the `next` pointer. Internally, `pos` is used to denote the index of the node that tail's `next` pointer is connected to. **Note that `pos` is not passed as a parameter.**

Return `true` *if there is a cycle in the linked list*. Otherwise, return `false`.

**Official Link:** [LeetCode 141. Linked List Cycle](https://leetcode.com/problems/linked-list-cycle/)

**Example 1:**
Input: `head = [3,2,0,-4]`, `pos = 1`
Output: `true`
Explanation: There is a cycle in the linked list, where the tail connects to the 1st node (0-indexed).

Visual from Labuladong's article (similar to `LC142` but general cycle):
`![](/algo/images/linked-list-two-pointer/circularlinkedlist.png)`

## Solution Approach: Fast and Slow Pointers

This is a classic application of the [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Fast and Slow Pointers technique for linked lists]].

1.  Initialize two pointers, `slow` and `fast`, both starting at `head`.
2.  Move `slow` one step at a time (`slow = slow.next`).
3.  Move `fast` two steps at a time (`fast = fast.next.next`).
4.  If `fast` encounters `None` or `fast.next` is `None`, it means `fast` has reached the end of the list, so there is no cycle. Return `False`.
5.  If `slow` and `fast` meet at some point (`slow == fast`), it means there is a cycle in the list. Return `True`.

### Python Solution (from Labuladong's article)
```python
# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, x):
#         self.val = x
#         self.next = None

class Solution:
    def hasCycle(self, head: [ListNode]) -> bool:
        # Initialize slow and fast pointers to the head of the list
        slow, fast = head, head

        # Loop until fast pointer reaches the end or detects a cycle
        while fast is not None and fast.next is not None:
            # Move slow pointer one step
            slow = slow.next
            # Move fast pointer two steps
            fast = fast.next.next
            
            # If slow and fast pointers meet, there's a cycle
            if slow == fast:
                return True
        
        # If fast pointer reaches the end (None), there's no cycle
        return False
```
Labuladong's visualization `div_linked-list-cycle` for this problem illustrates the pointer movements.

## Complexity Analysis
-   **Time Complexity:** $O(N)$, where $N$ is the number of nodes in the linked list.
    - If there's no cycle, `fast` reaches the end in $N/2$ steps.
    - If there is a cycle, `fast` enters the cycle and will eventually lap `slow`. The number of steps is bounded by $N$.
-   **Space Complexity:** $O(1)$, as we only use two pointers.

## 总结 (Summary)
- Detecting a cycle in a linked list is a canonical problem solved using the fast and slow pointer technique.
- If the `fast` pointer (moving two steps) meets the `slow` pointer (moving one step), a cycle exists.
- If `fast` reaches the end of the list (`None`), no cycle exists.
- The solution is efficient with $O(N)$ time and $O(1)$ space complexity.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]]
Next Problem (related): [[Interview/Practice/LeetCode/LC142 - Linked List Cycle II|LC142 - Linked List Cycle II]]
"""
    ensure_dir(os.path.join(path, "Interview/Practice/LeetCode"))
    with open(os.path.join(path, "Interview/Practice/LeetCode/LC141 - Linked List Cycle.md"), 'w', encoding='utf-8') as f:
        f.write(textwrap.dedent(content))
    print("Created/Updated: Interview/Practice/LeetCode/LC141 - Linked List Cycle.md")

def create_lc142(path):
    content = r"""---
tags: [problem/leetcode, lc/medium, topic/linked_list, topic/two_pointers, pattern/fast_slow_pointers, course/labuladong]
aliases: [LC142, LeetCode 142. Linked List Cycle II, 环形链表 II]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 142. Linked List Cycle II
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道链表题目.md]].

# LeetCode 142: Linked List Cycle II

## Problem Statement

Given the `head` of a linked list, return *the node where the cycle begins. If there is no cycle, return `null`*.

There is a cycle in a linked list if there is some node in the list that can be reached again by continuously following the `next` pointer. Internally, `pos` is used to denote the index of the node that tail's `next` pointer is connected to (**0-indexed**). It is `-1` if there is no cycle. **Note that `pos` is not passed as a parameter.**

**Do not modify** the linked list.

**Official Link:** [LeetCode 142. Linked List Cycle II](https://leetcode.com/problems/linked-list-cycle-ii/)

**Example 1:**
Input: `head = [3,2,0,-4]`, `pos = 1`
Output: `Node with value 2` (where cycle begins)

![](/algo/images/linked-list-two-pointer/circularlinkedlist.png)
*(Source: Labuladong, example visual for a cycle)*

## Solution Approach: Fast and Slow Pointers (Two Phases)

This problem builds upon [[Interview/Practice/LeetCode/LC141 - Linked List Cycle|LC141 - Linked List Cycle]].
1.  **Phase 1: Detect Cycle and Find Meeting Point**
    - Use fast and slow pointers (`slow` moves 1 step, `fast` moves 2 steps).
    - If they meet, a cycle exists. Let the meeting point be `M`.
    - If `fast` reaches `null`, no cycle exists; return `null`.

2.  **Phase 2: Find Cycle Start**
    - After `slow` and `fast` meet at point `M`, reset one pointer (say, `slow`) to the `head` of the list.
    - Keep the other pointer (`fast`) at the meeting point `M`.
    - Now, move both `slow` (from `head`) and `fast` (from `M`) one step at a time.
    - The node where they meet again is the start of the cycle.

**Mathematical Intuition for Phase 2:**
Let distance from `head` to cycle start `S` be $X$.
Let distance from `S` to meeting point `M` be $Y$.
Let length of cycle be $L$.
When `slow` and `fast` meet at `M`:
- `slow` traveled: $X + Y$
- `fast` traveled: $X + Y + kL$ (for some integer $k \ge 1$, `fast` lapped `slow` $k$ times within the cycle)
Since `fast` moves twice as fast as `slow`: $2 \times \text{dist(slow)} = \text{dist(fast)}$
$2(X+Y) = X+Y+kL \implies X+Y = kL$
This means $X = kL - Y$.
Now, if we move one pointer from `head` (distance $X$ to `S`) and another from `M` (distance $L-Y$ to `S` along the cycle, or $kL-Y$ if it laps more), they will meet at `S`.
- Distance from `head` to `S` is $X$.
- Distance from `M` to `S` (going forward in the cycle) is $L-Y$.
Since $X = kL - Y = (k-1)L + (L-Y)$, if pointer 1 moves $X$ steps from `head` and pointer 2 moves $X$ steps from `M`, pointer 2 will have effectively moved $L-Y$ steps from `M` plus some full cycle laps, landing at `S`.
Thus, moving them one step at a time will make them meet at `S`.

Labuladong's article uses images `![](/algo/images/linked-two-pointer/3.jpeg)` (fast/slow meet) and `![](/algo/images/linked-two-pointer/2.jpeg)` (second phase to find start) to visualize this.

### Python Solution (from Labuladong's article)
```python
# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, x):
#         self.val = x
#         self.next = None

class Solution:
    def detectCycle(self, head: [ListNode]) -> [ListNode]:
        slow, fast = head, head

        # Phase 1: Detect cycle and find meeting point
        while fast is not None and fast.next is not None:
            slow = slow.next
            fast = fast.next.next
            if slow == fast: # Cycle detected, break to find start
                break
        
        # If no cycle (fast reached end)
        if fast is None or fast.next is None:
            return None

        # Phase 2: Find cycle start
        # Reset slow to head, keep fast at meeting point
        slow = head
        while slow != fast:
            slow = slow.next
            fast = fast.next
        
        # Both pointers now meet at the start of the cycle
        return slow
```
Labuladong's visualization panel `div_linked-list-cycle-ii` demonstrates both phases.

## Complexity Analysis
-   **Time Complexity:** $O(N)$, where $N$ is the number of nodes. Both phases involve traversing the list at most a constant number of times.
-   **Space Complexity:** $O(1)$, using only a few pointers.

## 总结 (Summary)
- Finding the start of a cycle in a linked list is a two-phase process using fast and slow pointers.
- Phase 1 detects the cycle and the meeting point of `slow` and `fast`.
- Phase 2 resets one pointer to `head` and moves both one step at a time from `head` and the meeting point, respectively. Their next meeting point is the cycle's start.
- This solution is $O(N)$ time and $O(1)$ space, and does not modify the list.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Previous: [[Interview/Practice/LeetCode/LC141 - Linked List Cycle|LC141 - Linked List Cycle]]
Related Concepts: [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]]
"""
    ensure_dir(os.path.join(path, "Interview/Practice/LeetCode"))
    with open(os.path.join(path, "Interview/Practice/LeetCode/LC142 - Linked List Cycle II.md"), 'w', encoding='utf-8') as f:
        f.write(textwrap.dedent(content))
    print("Created/Updated: Interview/Practice/LeetCode/LC142 - Linked List Cycle II.md")

def create_lc206(path):
    content = r"""---
tags: [problem/leetcode, lc/easy, topic/linked_list, pattern/list_reversal, course/labuladong]
aliases: [LC206, LeetCode 206. Reverse Linked List, 反转链表]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 206. Reverse Linked List
> Solutions and explanations adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷链表算法/单链表的花式反转方法汇总.md]].

# LeetCode 206: Reverse Linked List

## Problem Statement

Given the `head` of a singly linked list, reverse the list, and return *the reversed list*.

**Official Link:** [LeetCode 206. Reverse Linked List](https://leetcode.com/problems/reverse-linked-list/)

**Example 1:**
Input: `head = [1,2,3,4,5]`
Output: `[5,4,3,2,1]`

## Solution Approaches

This problem is a fundamental linked list operation and can be solved iteratively or recursively. The conceptual framework is from [[Interview/Concept/Algorithms/Linked List/01 - Linked List Reversal Techniques|Linked List Reversal Techniques]].

### 1. Iterative Approach

Uses three pointers: `prev`, `curr`, and `next_temp`.
- `prev` stores the previous node (initially `None`).
- `curr` is the current node being processed (initially `head`).
- `next_temp` temporarily stores `curr.next` before `curr.next` is reassigned.

**Steps:**
1. Initialize `prev = None`, `curr = head`.
2. While `curr` is not `None`:
   a. `next_temp = curr.next` (Store next node).
   b. `curr.next = prev` (Reverse pointer of current node).
   c. `prev = curr` (Move `prev` one step forward).
   d. `curr = next_temp` (Move `curr` one step forward).
3. After the loop, `prev` will be the new head of the reversed list.

**Python Solution (Iterative):**
```python
# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, val=0, next=None):
#         self.val = val
#         self.next = next

class Solution:
    def reverseList_iterative(self, head: [ListNode]) -> [ListNode]:
        prev = None
        curr = head
        
        while curr:
            next_temp = curr.next # Store the next node
            curr.next = prev     # Reverse the current node's pointer
            prev = curr          # Move prev to current node
            curr = next_temp     # Move to the next node in original list
            
        return prev # prev is the new head
```
Labuladong's visualization panel `div_reverse-linked-list-iter` shows this process.

### 2. Recursive Approach

Define a recursive function `reverse(head)` that reverses the list starting at `head` and returns the new head of this reversed sublist.
1.  **Base Case:** If `head` is `None` or `head.next` is `None` (empty or single-node list), it's already reversed. Return `head`.
2.  **Recursive Step:**
    a.  `new_head = reverse(head.next)`: Recursively reverse the rest of the list. `new_head` is the head of the reversed "tail" part (which is the original last node of the list).
    b.  `head.next.next = head`: The original `head.next` node is now the *last* node of the reversed tail. Make its `next` pointer point back to `head`.
    c.  `head.next = None`: `head` becomes the new tail of the overall reversed list, so its `next` should be `None`.
    d.  Return `new_head` (this is the head of the fully reversed list).

**Python Solution (Recursive):**
```python
class Solution:
    def reverseList(self, head: [ListNode]) -> [ListNode]: # Main function, can choose method
        return self.reverseList_recursive(head)

    def reverseList_recursive(self, head: [ListNode]) -> [ListNode]:
        if not head or not head.next:
            return head
        
        # Recursively reverse the sublist starting from head.next
        last_node_of_reversed_sublist = self.reverseList_recursive(head.next)
        
        # head.next is the original second node, which is now the tail of the reversed sublist.
        # Make its 'next' pointer point to head.
        head.next.next = head
        
        # head is now the new tail of the overall reversed list.
        head.next = None
        
        # The 'last_node_of_reversed_sublist' is the original tail of the list,
        # which is the new head of the fully reversed list.
        return last_node_of_reversed_sublist
```
Labuladong's visualization panel `div_reverse-linked-list` and accompanying images (`![](/algo/images/reverse-linked-list/3.jpg)` etc.) explain this recursive process.

## Complexity Analysis

**Iterative Approach:**
-   **Time Complexity:** $O(N)$, where $N$ is the number of nodes. Each node is visited once.
-   **Space Complexity:** $O(1)$, as only a few pointers are used.

**Recursive Approach:**
-   **Time Complexity:** $O(N)$. Each node is visited once during the recursion unwind.
-   **Space Complexity:** $O(N)$ due to the recursion call stack. In the worst case (a long list), the stack depth can be $N$.

## 总结 (Summary)
- Reversing a singly linked list is a fundamental operation.
- The iterative solution uses three pointers (`prev`, `curr`, `next_temp`) and is $O(N)$ time, $O(1)$ space.
- The recursive solution breaks the problem into reversing the "rest" of the list and then attaching the current head to the end of that reversed "rest". It's $O(N)$ time and $O(N)$ space (for recursion stack).
- The iterative approach is generally preferred for space efficiency.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Linked List/01 - Linked List Reversal Techniques|Linked List Reversal Techniques]]
"""
    ensure_dir(os.path.join(path, "Interview/Practice/LeetCode"))
    with open(os.path.join(path, "Interview/Practice/LeetCode/LC206 - Reverse Linked List.md"), 'w', encoding='utf-8') as f:
        f.write(textwrap.dedent(content))
    print("Created/Updated: Interview/Practice/LeetCode/LC206 - Reverse Linked List.md")

def create_lc234(path):
    content = r"""---
tags: [problem/leetcode, lc/easy, topic/linked_list, pattern/palindrome_check, pattern/fast_slow_pointers, pattern/list_reversal, course/labuladong]
aliases: [LC234, LeetCode 234. Palindrome Linked List, 回文链表]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 234. Palindrome Linked List
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷链表算法/如何判断回文链表.md]].

# LeetCode 234: Palindrome Linked List

## Problem Statement

Given the `head` of a singly linked list, return `true` if it is a palindrome or `false` otherwise.

**Official Link:** [LeetCode 234. Palindrome Linked List](https://leetcode.com/problems/palindrome-linked-list/)

**Example 1:**
Input: `head = [1,2,2,1]`
Output: `true`

## Solution Approach: Find Middle + Reverse Second Half + Compare

The optimized approach to solve this in $O(N)$ time and $O(1)$ space involves these steps (detailed in [[Interview/Concept/Algorithms/Linked List/02 - Palindrome Linked List Detection|Palindrome Linked List Detection]]):
1.  **Find Middle:** Use fast and slow pointers to find the middle of the list.
2.  **Reverse Second Half:** Reverse the linked list starting from the node after the middle.
3.  **Compare:** Compare the first half with the reversed second half.
4.  **(Optional but good practice) Restore List:** Reverse the second half again to restore the original list structure.

### Python Solution
```python
# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, val=0, next=None):
#         self.val = val
#         self.next = next

class Solution:
    def isPalindrome(self, head: [ListNode]) -> bool:
        if not head or not head.next:
            return True

        # 1. Find the middle of the list
        # slow will point to the end of the first half (or the middle node in odd length list)
        slow, fast = head, head
        while fast.next and fast.next.next:
            slow = slow.next
            fast = fast.next.next
        
        # head_second_half is the start of the second half
        head_second_half = slow.next
        
        # 2. Reverse the second half
        reversed_second_half_head = self._reverse_list(head_second_half)
        
        # (Optional but clean) Disconnect the first half from the (original) second half
        # slow.next = None 
        # For comparison, we only need p1 to go up to slow, and p2 through reversed_second_half

        # 3. Compare the first half with the reversed second half
        p1 = head
        p2 = reversed_second_half_head
        is_palindrome_result = True
        while p2: # Iterate while the reversed second half has nodes
            if p1.val != p2.val:
                is_palindrome_result = False
                break
            p1 = p1.next
            p2 = p2.next
            
        # 4. (Optional) Restore the list: reverse the second half again and connect
        # slow.next = self._reverse_list(reversed_second_half_head)
            
        return is_palindrome_result

    def _reverse_list(self, node: ListNode) -> ListNode:
        prev = None
        curr = node
        while curr:
            next_node = curr.next
            curr.next = prev
            prev = curr
            curr = next_node
        return prev
```
Labuladong's article uses visualizations like `![](/algo/images/palindrome-linkedlist/3.gif)` for finding the middle.

## Complexity Analysis
-   **Time Complexity:** $O(N)$.
    - Finding middle: $O(N)$.
    - Reversing second half: $O(N/2) = O(N)$.
    - Comparing halves: $O(N/2) = O(N)$.
    - Restoring (optional): $O(N/2) = O(N)$.
-   **Space Complexity:** $O(1)$ (if iterative reversal is used). The pointers take constant space.

## Alternative Approach: Using a List (O(N) space)
A simpler but less space-efficient approach:
1. Traverse the linked list and store all node values in an array/list.
2. Use two pointers (start and end) on the array to check if it's a palindrome.
- Time: $O(N)$ (for traversal and for checking array palindrome).
- Space: $O(N)$ (to store node values in the array).
This does not meet the "follow-up" often implied for $O(1)$ space.

## 总结 (Summary)
- Checking if a linked list is a palindrome with $O(1)$ space involves finding the middle, reversing the second half, and then comparing the two halves.
- This method cleverly combines fast-slow pointers and list reversal techniques.
- Restoring the list by re-reversing the second half is good practice if the original list structure needs to be preserved.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Linked List/02 - Palindrome Linked List Detection|Palindrome Linked List Detection]], [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Fast-Slow Pointers]], [[Interview/Concept/Algorithms/Linked List/01 - Linked List Reversal Techniques|List Reversal]]
"""
    ensure_dir(os.path.join(path, "Interview/Practice/LeetCode"))
    with open(os.path.join(path, "Interview/Practice/LeetCode/LC234 - Palindrome Linked List.md"), 'w', encoding='utf-8') as f:
        f.write(textwrap.dedent(content))
    print("Created/Updated: Interview/Practice/LeetCode/LC234 - Palindrome Linked List.md")

def create_placeholder_lc_problem(path, lc_id, title, concept_link, source_article_link):
    content = f"""---
tags: [problem/leetcode, lc/{lc_id}, topic/linked_list, pattern/two_pointers, course/labuladong]
aliases: [LC{lc_id}, LeetCode {lc_id}. {title}]
---

> [!NOTE] Source Annotation
> Problem: LeetCode {lc_id}. {title}
> Mentioned in [[{source_article_link}]].
> This is a placeholder note. Detailed solution and explanation to be added based on the source.

# LeetCode {lc_id}: {title}

## Problem Statement
(To be filled from LeetCode - Visit [https://leetcode.com/problems/{title.lower().replace(' ', '-')}/](https://leetcode.com/problems/{title.lower().replace(' ', '-')}/))
*Note: Auto-generated URL might be incorrect.*


## Solution Approach
(To be filled, likely using concepts from [[{concept_link}]])

### Python Solution (Placeholder)
```python
# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, val=0, next=None):
#         self.val = val
#         self.next = next

class Solution:
    def solve(self, head): # Signature will vary
        # TODO: Implement solution
        pass
```

## Complexity Analysis
(To be filled)

## 总结 (Summary)
(To be filled)

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[{concept_link}]]
"""
    ensure_dir(os.path.join(path, "Interview/Practice/LeetCode"))
    file_name = f"LC{lc_id} - {title.replace(' ', '_')}.md" # Basic filename sanitization
    with open(os.path.join(path, "Interview/Practice/LeetCode", file_name), 'w', encoding='utf-8') as f:
        f.write(textwrap.dedent(content))
    print(f"Created Placeholder: Interview/Practice/LeetCode/{file_name}")

def update_two_pointers_index(path):
    index_path = os.path.join(path, "Interview/Concept/Algorithms/Two Pointers/index.md")
    ensure_dir(os.path.dirname(index_path))

    # Check if file exists, if not, create a basic one.
    if not os.path.exists(index_path):
        base_content = r"""---
tags: [index, concept/algorithms, pattern/two_pointers]
aliases: [Two Pointers Index, Two Pointer Algorithms]
---

# Two Pointer Techniques

This section covers the "Two Pointers" algorithmic pattern, a versatile technique for solving problems involving arrays and linked lists by efficiently managing and moving two pointers (or indices) through the data structure.

## Core Concepts & Types:
- [[Interview/Concept/Algorithms/Two Pointers/00 - Two Pointers - Introduction|Two Pointers - Introduction and Core Ideas]]
  - Fast and Slow Pointers
  - Left and Right Pointers

## Applications by Data Structure:
- **Arrays:**
  - [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]]
- **Linked Lists:**
  - [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]]

## Visualization
```mermaid
graph TD
    TPConcept["Two Pointers"] --> Intro["[[Interview/Concept/Algorithms/Two Pointers/00 - Two Pointers - Introduction|Introduction]]"]
    Intro --> FastSlow["Fast & Slow Pointers"]
    Intro --> LeftRight["Left & Right Pointers"]

    TPConcept --> Applications["Applications"]
    Applications --> ForArrays["[[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|For Arrays]]"]
    
    Applications --> ForLists["[[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|For Linked Lists]]"]

    classDef main fill:#e6fff2,stroke:#00994d,stroke-width:2px;
    class TPConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
"""
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write(textwrap.dedent(base_content))
        print(f"Created base index: {index_path}")

    # Read existing content
    with open(index_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Add link to "02 - Two Pointers for Linked Lists.md" if not already present
    link_to_add = "- [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]]"
    if link_to_add not in content:
        # Find the insertion point (e.g., after the "For Arrays" link)
        insertion_marker = "- [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]]"
        if insertion_marker in content:
            content = content.replace(insertion_marker, f"{insertion_marker}\n    {link_to_add.lstrip('- ')}") # Add under Arrays, or adjust as needed
        else: # Fallback: add to end of applications list or just append to Core Concepts
            if "## Applications by Data Structure:" in content:
                 content = content.replace("## Applications by Data Structure:", f"## Applications by Data Structure:\n{link_to_add}")
            else: # Simple append if structure is very basic
                 content += f"\n{link_to_add}\n"
        
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Updated: {index_path} with linked list two pointers link.")

def update_linked_list_concept_index(path):
    index_dir = os.path.join(path, "Interview/Concept/Algorithms/Linked List")
    ensure_dir(index_dir)
    index_path = os.path.join(index_dir, "index.md")

    content = r"""---
tags: [index, concept/algorithms, topic/linked_list]
aliases: [Linked List Algorithm Concepts, Linked List Techniques]
---

# Linked List Algorithmic Concepts

This section covers core algorithmic techniques and patterns related to linked lists. For basic linked list data structure implementation, see [[Interview/Concept/Data Structures/Linked List/index|Linked List Data Structure]].

## Core Techniques & Patterns:
- [[Interview/Concept/Algorithms/Linked List/00 - Linked List - Virtual Head Node|Virtual Head Node Technique]]
- [[Interview/Concept/Algorithms/Linked List/01 - Linked List Reversal Techniques|Linked List Reversal Techniques]]
  - Iterative Reversal
  - Recursive Reversal
  - Partial Reversal (N nodes, range m-n)
  - K-Group Reversal
- [[Interview/Concept/Algorithms/Linked List/02 - Palindrome Linked List Detection|Palindrome Linked List Detection]]

## Related General Patterns:
- [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]] (Fast/Slow, etc.)

## Visualization
```mermaid
graph TD
    LLAlgo["Linked List Algorithms"] --> VirtualNode["[[Interview/Concept/Algorithms/Linked List/00 - Linked List - Virtual Head Node|Virtual Head Node]]"]
    LLAlgo --> Reversal["[[Interview/Concept/Algorithms/Linked List/01 - Linked List Reversal Techniques|Reversal Techniques]]"]
    LLAlgo --> Palindrome["[[Interview/Concept/Algorithms/Linked List/02 - Palindrome Linked List Detection|Palindrome Detection]]"]
    
    Reversal --> IterRev["(Iterative Full Reverse)"]
    Reversal --> RecRev["(Recursive Full Reverse)"]
    Reversal --> PartialRev["(Partial Reverse - N, M-N)"]
    Reversal --> KGroupRev["(K-Group Reverse)"]

    LLAlgo --> TwoPtrsLink["(See [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for LL]])"]

    classDef main fill:#e0ffff,stroke:#008b8b,stroke-width:2px;
    class LLAlgo main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
"""
    with open(index_path, 'w', encoding='utf-8') as f:
        f.write(textwrap.dedent(content))
    print(f"Created/Updated: {index_path}")

def update_leetcode_practice_index(path, new_entries):
    index_path = os.path.join(path, "Interview/Practice/LeetCode/index.md")
    ensure_dir(os.path.dirname(index_path))

    existing_content = ""
    if os.path.exists(index_path):
        with open(index_path, 'r', encoding='utf-8') as f:
            existing_content = f.read()
    else:
        existing_content = r"""---
tags: [index, practice/leetcode, interview_prep]
aliases: [LeetCode Problem Solving, LC Practice]
---

# LeetCode Practice Problems

This section contains solutions, explanations, and conceptual links for various LeetCode problems, aimed at building practical problem-solving skills for interviews.

## Getting Started
- [[Interview/Practice/LeetCode/00 - LeetCode Platform Guide and Tips|LeetCode Platform Guide and Tips]]

## Problems by Topic / Difficulty (Example Structure)

### Easy

### Medium

### Hard

## Focus Areas

## Visualization of Practice Areas
```mermaid
graph TD
    Practice["LeetCode Practice"] --> Guide["[[Interview/Practice/LeetCode/00 - LeetCode Platform Guide and Tips|Platform Guide]]"]
    Practice --> EasyCat["Easy Problems"]
    Practice --> MediumCat["(Medium Problems)"]
    Practice --> HardCat["(Hard Problems)"]

    subgraph "Problem Categories"
        CatArrays["Arrays/Hashing"]
        CatStack["Stack"]
        CatQueue["Queue/Simulation"]
        CatBitMan["Bit Manipulation"]
        CatGraphBFS["Graph/BFS"]
        CatDP["Dynamic Programming"]
        CatLinkedList["Linked List"]
        CatTwoPointers["Two Pointers"]
        CatBacktracking["Backtracking"]
    end
    
    classDef category fill:#fffacd,stroke:#ffd700,stroke-width:2px;
    class EasyCat, MediumCat, HardCat category;
```

This index will be updated as more problems are added and categorized.
"""
    
    # Find where to insert new problem links (e.g., under a difficulty heading)
    # For simplicity, just append to a section. Example: Easy problems
    insertion_point_str = "### Easy"
    if insertion_point_str not in existing_content and "## Problems by Topic" in existing_content:
        insertion_point_str = "## Problems by Topic" # fallback
    
    updated_content = existing_content
    for entry in new_entries:
        if entry not in updated_content:
            if insertion_point_str in updated_content:
                updated_content = updated_content.replace(insertion_point_str, f"{insertion_point_str}\n- {entry}", 1)
            else: # If insertion point not found, just append
                updated_content += f"\n- {entry}"

    with open(index_path, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    print(f"Updated: {index_path} with new LeetCode problem links.")


# --- Main Script ---
if __name__ == "__main__":
    kb_root = "../"  # Assuming script is in a subfolder, and "Interview" is in parent. Adjust if needed.
    
    print(f"Knowledge base operations will be relative to: {kb_root}")

    # Create/Update Conceptual Notes
    create_linked_list_two_pointers_overview(kb_root)
    create_virtual_head_node_concept(kb_root)
    create_linked_list_reversal_techniques(kb_root)
    create_palindrome_linked_list_detection(kb_root)

    # Create/Update LeetCode Problem Notes (Detailed for some, Placeholders for others)
    create_lc141(kb_root)
    create_lc142(kb_root)
    create_lc206(kb_root)
    create_lc234(kb_root)

    # Placeholder LeetCode problem notes creation
    # From 双指针技巧秒杀七道链表题目.md
    placeholder_problems_two_pointers = [
        (21, "Merge Two Sorted Lists", "Interview/Concept/Algorithms/Linked List/Merging Sorted Lists", "labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道链表题目.md"),
        (86, "Partition List", "Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists", "labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道链表题目.md"),
        (23, "Merge K Sorted Lists", "Interview/Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization", "labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道链表题目.md"),
        (19, "Remove Nth Node From End of List", "Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists", "labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道链表题目.md"),
        (876, "Middle of the Linked List", "Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists", "labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道链表题目.md"),
        (160, "Intersection of Two Linked Lists", "Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists", "labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道链表题目.md"),
    ]
    # From 单链表的花式反转方法汇总.md
    placeholder_problems_reversal = [
        (92, "Reverse Linked List II", "Interview/Concept/Algorithms/Linked List/01 - Linked List Reversal Techniques", "labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷链表算法/单链表的花式反转方法汇总.md"),
        (25, "Reverse Nodes in k-Group", "Interview/Concept/Algorithms/Linked List/01 - Linked List Reversal Techniques", "labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷链表算法/单链表的花式反转方法汇总.md"),
    ]

    all_placeholder_problems = placeholder_problems_two_pointers + placeholder_problems_reversal
    new_leetcode_index_entries = []

    for lc_id, title, concept_link, source_link in all_placeholder_problems:
        create_placeholder_lc_problem(kb_root, lc_id, title, concept_link, source_link)
        # Prepare link for LeetCode index
        file_name_part = title.replace(' ', '_') # Basic filename sanitization used in placeholder func
        new_leetcode_index_entries.append(f"[[Interview/Practice/LeetCode/LC{lc_id} - {file_name_part}|LC{lc_id} - {title}]]")
    
    # Add the detailed ones too
    new_leetcode_index_entries.extend([
        "[[Interview/Practice/LeetCode/LC141 - Linked List Cycle|LC141 - Linked List Cycle]]",
        "[[Interview/Practice/LeetCode/LC142 - Linked List Cycle II|LC142 - Linked List Cycle II]]",
        "[[Interview/Practice/LeetCode/LC206 - Reverse Linked List|LC206 - Reverse Linked List]]",
        "[[Interview/Practice/LeetCode/LC234 - Palindrome Linked List|LC234 - Palindrome Linked List]]"
    ])
    
    # Update Index Files
    update_two_pointers_index(kb_root)
    update_linked_list_concept_index(kb_root) # Create if not exists
    update_leetcode_practice_index(kb_root, new_leetcode_index_entries) # Update with all new problems

    # Placeholder for 【练习】链表双指针经典习题.md - this file itself is an index/exercise page
    exercise_index_path = os.path.join(kb_root, "Interview/Practice/Linked List Exercises")
    ensure_dir(exercise_index_path)
    exercise_index_content = r"""---
tags: [index, practice/linked_list, pattern/two_pointers, course/labuladong]
aliases: [Linked List Two Pointer Exercises]
---

> [!NOTE] Source Annotation
> This page is inspired by [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷链表算法/【练习】链表双指针经典习题.md]].
> It serves as a collection of exercises focusing on two-pointer techniques for linked lists.

# Linked List: Two Pointer Classic Exercises

This section aggregates classic LeetCode problems that are effectively solved using two-pointer techniques on linked lists. Refer to [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]] for core concepts.

## Problems

- [[Interview/Practice/LeetCode/LC21 - Merge Two Sorted Lists|LC21 - Merge Two Sorted Lists]]
- [[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Sorted Lists]] (Uses list merging as a sub-problem)
- [[Interview/Practice/LeetCode/LC25 - Reverse Nodes in k-Group|LC25 - Reverse Nodes in k-Group]]
- [[Interview/Practice/LeetCode/LC86 - Partition List|LC86 - Partition List]]
- [[Interview/Practice/LeetCode/LC92 - Reverse Linked List II|LC92 - Reverse Linked List II]]
- [[Interview/Practice/LeetCode/LC141 - Linked List Cycle|LC141 - Linked List Cycle]]
- [[Interview/Practice/LeetCode/LC142 - Linked List Cycle II|LC142 - Linked List Cycle II]]
- [[Interview/Practice/LeetCode/LC160 - Intersection of Two Linked Lists|LC160 - Intersection of Two Linked Lists]]
- [[Interview/Practice/LeetCode/LC206 - Reverse Linked List|LC206 - Reverse Linked List]]
- [[Interview/Practice/LeetCode/LC234 - Palindrome Linked List|LC234 - Palindrome Linked List]]
- [[Interview/Practice/LeetCode/LC876 - Middle of the Linked List|LC876 - Middle of the Linked List]]
- [[Interview/Practice/LeetCode/LC19 - Remove Nth Node From End of List|LC19 - Remove Nth Node From End of List]]
- *(Sword Offer 22 is similar to LC19)*

---
Parent: [[Interview/Practice/index|Practice Problems]]
"""
    with open(os.path.join(exercise_index_path, "00 - Two Pointer Classic Exercises Index.md"), 'w', encoding='utf-8') as f:
        f.write(textwrap.dedent(exercise_index_content))
    print(f"Created: {os.path.join(exercise_index_path, '00 - Two Pointer Classic Exercises Index.md')}")

    print("Script finished. Specified files for Linked List Two Pointers created/updated.")

