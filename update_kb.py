
import os
import textwrap

# Helper function to create directories if they don't exist
def ensure_dir(directory):
    os.makedirs(directory, exist_ok=True)

# --- Content Generation Functions ---

def create_sliding_window_framework(path):
    content = r"""---
tags: [concept/algorithms, pattern/two_pointers, pattern/sliding_window, type/framework, course/labuladong]
aliases: [Sliding Window Algorithm, 滑动窗口算法, 滑动窗口框架]
summary: |
  This note outlines the general framework for solving problems using the Sliding Window technique, 
  which is efficient for sub-array/sub-string problems. It typically achieves O(N) time complexity.
  Key aspects include managing window boundaries (left, right), updating window state, 
  and defining conditions for window expansion and contraction.
created: 2025-05-25T22:48:40.673-07:00
modified: 2025-05-26T18:00:00.000-07:00
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/滑动窗口算法核心代码模板.md|滑动窗口算法核心代码模板]].
> This note outlines the general framework for solving problems using the Sliding Window technique.

# Sliding Window: Core Framework

The Sliding Window technique is an algorithmic pattern often used for problems involving finding a sub-array or sub-string that satisfies certain conditions (e.g., longest, shortest, contains specific elements). It typically uses two pointers, `left` and `right`, to define a "window" `s[left...right)` (inclusive `left`, exclusive `right`) over the sequence. This approach can often reduce time complexity from $O(N^2)$ for brute-force approaches to $O(N)$.

## 🎯 Core Idea

The window `[left, right)` slides through the data:
1.  The `right` pointer expands the window by moving to the right, incorporating new elements.
2.  The `left` pointer shrinks the window from the left when a certain condition is met or violated, removing elements.
This process efficiently explores contiguous sub-sequences.

> [!TIP] Why $O(N)$ Complexity?
> Although the typical implementation involves a `while` loop for `right` and an inner `while` loop for `left`, each element of the input sequence `s` is processed by `right` at most once (when it enters the window) and by `left` at most once (when it leaves the window). Thus, the total number of pointer movements is at most $2N$, leading to $O(N)$ time complexity for the pointer operations. Operations on data structures inside the loops (like hash map updates) are usually average $O(1)$ per element.

## 🛠️ Generic Sliding Window Algorithm Pseudocode (Python)

```python
import collections

def sliding_window_template(s: str, t: str = None): # t is optional, for problems like minWindow
    # 1. Initialization of window data structures
    # For problems involving character counts (e.g., Min Window Substring, Permutation in String)
    needs = collections.Counter(t) if t else collections.defaultdict(int) # Characters and counts needed from t
    window_chars = collections.defaultdict(int) # Characters and counts in the current window s[left:right)

    left, right = 0, 0 # Window boundaries [left, right)

    # `valid_chars_count` tracks how many character types from `needs` 
    # have their counts fully satisfied in `window_chars`.
    # This is crucial for problems where specific character frequencies must be met.
    valid_chars_count = 0 

    # Result variables (example for min window substring)
    min_len = float('inf')
    result_start_index = 0
    # For other problems, this might be max_length, a list of indices, etc.
    # e.g. res_list = [] for Find All Anagrams
    # e.g. max_substring_len = 0 for Longest Substring Without Repeats

    # 2. Main Loop: Expand window with `right` pointer
    while right < len(s):
        char_in = s[right] # Character entering the window
        right += 1         # Expand window: right pointer moves, window becomes [left, right)
                           # The new character s[right-1] (which is char_in) is now in the window.

        # --- Part A: Update window data based on char_in ---
        # This logic is specific to the problem.
        # Example for problems with 'needs' map (like Min Window, Permutation in String):
        if t and char_in in needs:
            window_chars[char_in] += 1
            if window_chars[char_in] == needs[char_in]:
                valid_chars_count += 1
        # Example for problems like Longest Substring Without Repeating Chars:
        # window_chars[char_in] += 1 (if window_chars just counts all chars in window)
        # ---------------------------------------------------

        # Debug print (optional, remove for submission)
        # print(f"Window s[{left}:{right}]='{s[left:right]}', window_chars={dict(window_chars)}, valid={valid_chars_count if t else 'N/A'}")

        # 3. Shrink Window: While window meets the condition to shrink
        # The condition `window_needs_shrink` is problem-specific.
        while window_needs_shrink(s, t, left, right, window_chars, needs, valid_chars_count):
            # --- Part B: Update result (if applicable) BEFORE shrinking ---
            # For "minimum" type problems (e.g., Min Window Substring LC76), 
            # the window s[left:right] is currently valid and a candidate for the minimum.
            # Update result if this window is better than current best.
            # Example for Min Window Substring:
            # if t and (right - left) < min_len:
            #    min_len = (right - left)
            #    result_start_index = left
            # For "existence" problems (e.g., Permutation in String LC567), 
            # if condition is met here, might return True.
            # Example for Permutation in String:
            # if t and valid_chars_count == len(needs) and (right - left) == len(t): return True
            # For "all occurrences" (e.g. Find All Anagrams LC438):
            # if t and valid_chars_count == len(needs) and (right - left) == len(t): res_list.append(left)
            # -----------------------------------------------------------------

            char_out = s[left] # Character leaving the window
            left += 1          # Shrink window: left pointer moves

            # --- Part C: Update window data based on char_out ---
            # This logic is also specific to the problem and often mirrors Part A.
            # Example for problems with 'needs' map:
            if t and char_out in needs:
                if window_chars[char_out] == needs[char_out]:
                    valid_chars_count -= 1
                window_chars[char_out] -= 1
            # Example for problems like Longest Substring Without Repeating Chars:
            # window_chars[char_out] -= 1
            # if window_chars[char_out] == 0: del window_chars[char_out]
            # ----------------------------------------------------
            
            # Debug print (optional)
            # print(f"Shrunk to s[{left}:{right}]='{s[left:right]}', window_chars={dict(window_chars)}, valid={valid_chars_count if t else 'N/A'}")
        
        # --- Part D: Update result (if applicable) AFTER shrinking (common for "maximum" problems) ---
        # For "maximum" type problems (e.g. Longest Substring Without Repeats LC3),
        # the window s[left:right] is now guaranteed to be valid *after* any necessary shrinking.
        # So, update max_length here.
        # Example for Longest Substring Without Repeats:
        # max_substring_len = max(max_substring_len, right - left)
        # --------------------------------------------------------------------------------------


    # 4. Return final result
    # Example for Min Window Substring:
    if t:
        return "" if min_len == float('inf') else s[result_start_index : result_start_index + min_len]
    # Example for Longest Substring Without Repeats:
    # return max_substring_len
    # Example for Permutation in String (if loop finishes without returning True):
    # return False
    # Example for Find All Anagrams:
    # return res_list
    return "Define return based on problem" # Placeholder


def window_needs_shrink(s, t, left, right, window_chars, needs, valid_chars_count):
    # This function must be defined based on the specific problem
    
    # Example for Min Window Substring (LC76): Shrink if window is valid (contains all of t)
    if t: # Only if t is relevant
        return valid_chars_count == len(needs)

    # Example for Permutation in String (LC567) / Find All Anagrams (LC438):
    # Shrink when window size is >= length of t (or > length of t if we check for solution before shrink)
    # The template above suggests checking `right - left >= len(t)`
    # If checking for fixed size window of len(t):
    # if t:
    #    return (right - left) >= len(t)

    # Example for Longest Substring Without Repeating Chars (LC3):
    # Shrink if `window_chars[s[right-1]] > 1` (the char just added caused a repeat)
    # Or, more generally, if any char count in `window_chars` > 1.
    # This is specific and often the condition is tied to `char_in` from the expansion step.
    # For a generic check on window_chars:
    # for char_count in window_chars.values():
    #    if char_count > 1: return True # Found a repeating character
    # return False

    return False # Default: don't shrink, needs problem-specific logic
```

**Key Questions to Adapt the Framework (Labuladong's Method):**

1.  **When to expand `right`?** (Outer loop condition: `right < len(s)`).
    -   **What data to update (Part A)?** When `s[right]` (the character `char_in`) enters the window, how do `window_chars`, `valid_chars_count`, or other problem-specific state variables change?

2.  **When to shrink `left` (`window_needs_shrink` condition)?**
    -   This is the condition for the inner `while` loop. It's problem-specific.
        -   *Minimum Window (LC76):* `valid_chars_count == len(needs)` (window contains all required characters).
        -   *Permutation in String (LC567)/Find All Anagrams (LC438):* `(right - left) >= len(t)` (window has reached target pattern's length).
        -   *Longest Substring Without Repeats (LC3):* A character count in `window_chars` becomes `> 1` (a repeat occurred).
    -   **What data to update (Part C)?** When `s[left]` (the character `char_out`) leaves the window, how do `window_chars`, `valid_chars_count`, etc., change? This often mirrors the updates in Part A.

3.  **When/Where to update the result (Part B or Part D)?**
    -   **Minimum-seeking problems (e.g., LC76):** Update the result *inside* the shrinking loop (Part B), just after confirming the window is valid and before actually shrinking it in a way that might make it invalid or less optimal for the current `left`.
    -   **Maximum-seeking problems (e.g., LC3):** Update the result *after* the shrinking loop (Part D), ensuring the window `s[left:right]` is valid according to the problem's constraints before its length is considered for the maximum.
    -   **Existence/Count problems (e.g., LC567, LC438):** If the condition for a valid match is met (often inside the shrinking loop, e.g., window of correct size AND `valid_chars_count` matches), then update the result (return `True`, or add `left` to a list of start indices).

## Visualizing Window Movement (Example: LC76)
Imagine `s = "ADOBECODEBANC"`, `t = "ABC"`. Target: find minimum window in `s` containing all chars of `t`.
(For detailed step-by-step, see [[Interview/Practice/LeetCode/LC76 - Minimum Window Substring|LC76 Problem Note]])

The core dynamic:
1. `right` expands the window: `[A]`, `[A,D]`, `[A,D,O]`, `[A,D,O,B]`, `[A,D,O,B,E]`, `[A,D,O,B,E,C]`
   - Window: `"ADOBEC"` (`left=0, right=6`). `window_chars` contains A,B,C. `valid_chars_count` equals `len(needs)`.
   - `window_needs_shrink` condition (`valid_chars_count == len(needs)`) is met.

2. Inner `while` loop (shrinking `left`):
   - **Iteration 1 of inner loop:**
     - Current window `s[0:6]` ("ADOBEC") is valid. Update `min_len=6, result_start_index=0`.
     - `char_out = s[0] = 'A'`. `left` becomes 1.
     - Update `window_chars['A']--`. If this makes `window_chars['A'] < needs['A']`, then `valid_chars_count--`.
     - Now `window_chars` might not satisfy `needs`. Re-check `window_needs_shrink`. If false, inner loop exits.
   - (If window `s[1:6]` ("DOBEC") was still valid, repeat: update `min_len`, shrink `left` again.)

3. If inner loop exits (window no longer satisfies `needs`), outer loop continues expanding `right`.

Labuladong's article uses images `![](/algo/images/slidingwindow/1.png)` through `![](/algo/images/slidingwindow/4.png)` to effectively illustrate this expansion and contraction for LC76.

## 总结 (Summary)
- Sliding window is an $O(N)$ technique for sub-array/sub-string problems, significantly more efficient than $O(N^2)$ brute-force.
- It uses `left` and `right` pointers to define a dynamic window `[left, right)`.
- **Framework Steps:**
    1. Initialize `left`, `right`, `window_data_structures` (e.g., `needs`, `window_chars`, `valid_chars_count`), and `result_variables`.
    2. Loop with `right` to expand the window: Add `s[right]` to window, update `window_data` (Part A).
    3. Inner loop: While `window_needs_shrink` is true:
        a. Update `result_variables` if current window is a candidate (Part B - often for min-type problems).
        b. Remove `s[left]` from window, update `window_data` (Part C). Increment `left`.
    4. After inner loop (if applicable), update `result_variables` if current window is valid (Part D - often for max-type problems).
- This framework is highly adaptable by changing the `window_data` structure, the `window_needs_shrink` condition, and the logic for updating results.

---
Parent: [[Interview/Concept/Algorithms/Sliding Window/index|Sliding Window Index]]
Related Concepts: [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]] (as it's a form of fast/slow pointers)
Related Problems:
- [[Interview/Practice/LeetCode/LC76 - Minimum Window Substring|LC76 - Minimum Window Substring]]
- [[Interview/Practice/LeetCode/LC567 - Permutation in String|LC567 - Permutation in String]]
- [[Interview/Practice/LeetCode/LC438 - Find All Anagrams in a String|LC438 - Find All Anagrams in a String]]
- [[Interview/Practice/LeetCode/LC3 - Longest Substring Without Repeating Characters|LC3 - Longest Substring Without Repeating Characters]]
- [[Interview/Practice/LeetCode/LC1004 - Max Consecutive Ones III|LC1004 - Max Consecutive Ones III]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Sliding Window"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Generated/Updated: Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework.md")

def create_lc76_minimum_window_substring(path):
    content = r"""---
tags: [problem/leetcode, lc/hard, topic/string, topic/hash_table, topic/two_pointers, pattern/sliding_window, course/labuladong]
aliases: [LC76, LeetCode 76. Minimum Window Substring, 最小覆盖子串]
summary: |
  This problem asks to find the minimum window substring of s that contains all characters of t, including duplicates. 
  It's a classic application of the sliding window technique using character frequency maps.
created: 2025-05-25T22:48:40.676-07:00
modified: 2025-05-26T18:00:00.000-07:00
---

> [!NOTE] Source Annotation
> Problem: LeetCode 76. Minimum Window Substring
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/滑动窗口算法核心代码模板.md|滑动窗口算法核心代码模板]].

# LeetCode 76: Minimum Window Substring

## Problem Statement

Given two strings `s` and `t` of lengths `m` and `n` respectively, return *the minimum window substring of `s` such that every character in `t` (including duplicates) is included in the window*. If there is no such substring, return the empty string `""`.

The testcases will be generated such that the answer is unique.

**Official Link:** [LeetCode 76. Minimum Window Substring](https://leetcode.com/problems/minimum-window-substring/)

**Example 1:**
Input: `s = "ADOBECODEBANC"`, `t = "ABC"`
Output: `"BANC"`
Explanation: The minimum window substring "BANC" includes 'A', 'B', and 'C' from string `t`.

## Solution Approach: Sliding Window

This problem asks for the *minimum* window, a classic indicator for the [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window technique]].

1.  **Character Counts for `t` (`needs`):** First, count the frequency of each character in string `t`. This map (`needs`) tells us what characters our window must contain, and how many of each.
2.  **Window Character Counts (`window_chars`):** Maintain a similar map for the characters currently within our sliding window `s[left:right)`.
3.  **`valid_chars_count`:** Keep track of how many character types from `t` have their required frequencies met within the current `window_chars`.
4.  **Pointers `left`, `right`:**
    - `right` expands the window. When `s[right]` (character `char_in`) enters the window, update `window_chars` and `valid_chars_count`.
    - `left` shrinks the window when the `window_needs_shrink` condition is met (i.e., `valid_chars_count == len(needs)`). When `s[left]` (character `char_out`) leaves the window, update `window_chars` and `valid_chars_count`.
5.  **Result Tracking:** When the window is valid (`valid_chars_count == len(needs)`), we are inside the inner "shrink" loop. This is where we update the minimum window found so far because any valid window is a candidate.

### Python Solution
```python
import collections

class Solution:
    def minWindow(self, s: str, t: str) -> str:
        needs = collections.Counter(t)
        window_chars = collections.defaultdict(int)

        left, right = 0, 0
        valid_chars_count = 0  # Number of character types in t that are fully satisfied in window

        # To store the result: start index and length of the minimum window
        min_len = float('inf')
        result_start_index = 0

        if not t: # Edge case: if t is empty, the answer is an empty string
            return ""

        while right < len(s):
            # Expand window by adding s[right] (char_in)
            char_in = s[right]
            right += 1 # Window is now s[left...right-1]

            if char_in in needs:
                window_chars[char_in] += 1
                if window_chars[char_in] == needs[char_in]:
                    valid_chars_count += 1

            # Shrink window while it's valid (contains all characters of t)
            # The condition to shrink for this problem is `valid_chars_count == len(needs)`
            while valid_chars_count == len(needs):
                # Current window s[left:right] is a valid candidate.
                # Update minimum window found so far.
                current_len = right - left
                if current_len < min_len:
                    min_len = current_len
                    result_start_index = left

                # Remove s[left] (char_out) from window to try for a smaller valid window
                char_out = s[left]
                left += 1 # Shrink window

                if char_out in needs:
                    # This check is important: only decrement valid_chars_count
                    # if char_out was making one of the needed char types valid.
                    if window_chars[char_out] == needs[char_out]:
                        valid_chars_count -= 1
                    window_chars[char_out] -= 1
        
        return "" if min_len == float('inf') else s[result_start_index : result_start_index + min_len]

```
Labuladong's article includes visual images `![](/algo/images/slidingwindow/1.png)` to `![](/algo/images/slidingwindow/4.png)` which depict this expansion and contraction.

## Complexity Analysis
-   **Time Complexity:** $O(M+N)$, where $M$ is the length of `s` and $N$ is the length of `t`.
    - Building `needs` map: $O(N)$.
    - The `left` and `right` pointers each traverse `s` at most once. So, the main `while` loop and inner `while` loop combined result in $O(M)$ operations. Hash map operations inside are average $O(1)$ (assuming character set is small, like ASCII or Unicode).
-   **Space Complexity:** $O(K)$, where $K$ is the size of the character set (e.g., 26 for lowercase English letters, or up to 52 for ASCII if case-sensitive). This is for `needs` and `window_chars` maps.

## Visualization of Key Steps
(Using `s = "ADOBECODEBANC", t = "ABC"`)

1.  `needs = {'A':1, 'B':1, 'C':1}`. `len(needs) = 3`.
2.  `right` pointer expands.
    - `s[0:6]` = `"ADOBEC"`. Window `left=0, right=6`.
    - `window_chars = {'A':1, 'D':1, 'O':1, 'B':1, 'E':1, 'C':1}`.
    - `valid_chars_count` becomes 3 (A, B, C are all satisfied).
3.  Inner `while (valid_chars_count == 3)` loop starts:
    -   Window `s[0:6]` is `"ADOBEC"`. Length `6`. `min_len = 6`, `result_start_index = 0`.
    -   Shrink: `char_out = s[0] = 'A'`. `left = 1`.
    -   `window_chars['A']` becomes 0. `valid_chars_count` becomes 2 (since `window_chars['A']` was `needs['A']` and now it's less).
    -   Inner loop condition `valid_chars_count == 3` is now false. Exit inner loop.
4.  Outer loop continues. `right` expands.
    - ... (window slides) ...
    - Eventually, `s[right-1]` might be `'B'` when window is `s[left...]="...BECODEB"`.
    - Later, `s[right-1]` might be `'A'` when window is `s[left...]="...ODEBANA"`.
    - Later, `s[right-1]` might be `'N'` then `'C'`. Consider window `s[left...]="BANC"`.
    - Say `left=9`, `right=13`. Window `s[9:13]` is `"BANC"`.
    - `window_chars = {'B':1, 'A':1, 'N':1, 'C':1}`. (`N` is not in `needs`).
    - `valid_chars_count` is 3 (for A, B, C).
    - Inner `while (valid_chars_count == 3)`:
        -   Window `"BANC"`. Length `4`. `4 < min_len (6)`. Update `min_len = 4`, `result_start_index = 9`.
        -   Shrink: `char_out = s[9] = 'B'`. `left = 10`.
        -   `window_chars['B']` becomes 0. `valid_chars_count` becomes 2.
        -   Inner loop condition `valid_chars_count == 3` is false. Exit inner loop.
5.  Outer loop continues until `right == len(s)`.
6.  Return `s[result_start_index : result_start_index + min_len]` which is `s[9:13] = "BANC"`.

## 总结 (Summary)
- LC76 is a classic "minimum window" problem solved effectively with the sliding window technique.
- Requires tracking character counts needed (`needs`) and character counts in the current window (`window_chars`).
- `valid_chars_count` efficiently checks if the window satisfies all character requirements from `t`.
- The process involves expanding the window with `right` and shrinking it with `left` while maintaining the validity condition and updating the minimum length found. The update for the minimum occurs *inside* the shrinking loop when the window is confirmed valid.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window Framework]]
"""
    ensure_dir(os.path.join(path, "Interview/Practice/LeetCode"))
    with open(os.path.join(path, "Interview/Practice/LeetCode/LC76 - Minimum Window Substring.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Generated/Updated: Interview/Practice/LeetCode/LC76 - Minimum Window Substring.md")


def create_lc567_permutation_in_string(path):
    content = r"""---
tags: [problem/leetcode, lc/medium, topic/string, topic/hash_table, topic/two_pointers, pattern/sliding_window, pattern/permutation_substring, course/labuladong]
aliases: [LC567, LeetCode 567. Permutation in String, 字符串的排列]
summary: |
  This problem asks to determine if s2 contains a permutation of s1. 
  It's solved using a fixed-size sliding window based on the length of s1, 
  tracking character frequencies to identify anagrams.
created: 2025-05-25T22:48:40.676-07:00
modified: 2025-05-26T18:00:00.000-07:00
---

> [!NOTE] Source Annotation
> Problem: LeetCode 567. Permutation in String
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/滑动窗口算法核心代码模板.md|滑动窗口算法核心代码模板]].

# LeetCode 567: Permutation in String

## Problem Statement

Given two strings `s1` and `s2`, return `true` if `s2` contains a permutation of `s1`, or `false` otherwise.
In other words, return `true` if one of `s1`'s permutations is the substring of `s2`.

**Official Link:** [LeetCode 567. Permutation in String](https://leetcode.com/problems/permutation-in-string/)

**Example 1:**
Input: `s1 = "ab"`, `s2 = "eidbaooo"`
Output: `true`
Explanation: `s2` contains one permutation of `s1` ("ba").

## Solution Approach: Sliding Window (Fixed Size)

This problem asks if any permutation of `s1` exists as a substring within `s2`. A key insight is that any permutation of `s1` will:
1.  Have the same length as `s1`.
2.  Have the same character counts as `s1`.

This makes it suitable for a [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window technique]] where the window size is fixed to `len(s1)`.

1.  **Character Counts for `s1` (`needs`):** Count character frequencies in `s1`.
2.  **Window Character Counts (`window_chars`):** Maintain character frequencies for the current window in `s2`.
3.  **`valid_chars_count`:** Track how many character types from `s1` have their frequencies matched in the current `window_chars`.
4.  **Fixed-Size Window Logic:**
    - Expand `right` pointer. Add `s2[right]` to `window_chars`. Update `valid_chars_count`.
    - The shrinking condition `window_needs_shrink` is when `(right - left) >= len(s1)`.
    - Inside the shrinking loop:
        - First, check if the current window `s[left:right]` (which has length `len(s1)`) is a permutation: `if valid_chars_count == len(needs): return True`.
        - Then, proceed to shrink by removing `s2[left]` from `window_chars`, update `valid_chars_count`, and increment `left`.

### Python Solution
```python
import collections

class Solution:
    def checkInclusion(self, s1: str, s2: str) -> bool:
        needs = collections.Counter(s1)
        window_chars = collections.defaultdict(int)

        left, right = 0, 0
        valid_chars_count = 0
        target_s1_len = len(s1)

        if target_s1_len == 0: # Edge case: empty s1 is a permutation of any s2 window of length 0
            return True
        if target_s1_len > len(s2): # s1 cannot be a substring of s2
            return False

        while right < len(s2):
            char_in = s2[right]
            right += 1 # Window is s[left...right-1]

            if char_in in needs:
                window_chars[char_in] += 1
                if window_chars[char_in] == needs[char_in]:
                    valid_chars_count += 1
            
            # Shrink window if its size has reached len(s1)
            # The condition for shrinking is `(right - left) >= target_s1_len`
            # because we first check for solution, then shrink.
            while (right - left) >= target_s1_len:
                # Check if current window is a permutation BEFORE shrinking
                # Window s[left:right] currently has length `right - left`.
                # If `right - left == target_s1_len`, this is a candidate.
                if (right - left) == target_s1_len and valid_chars_count == len(needs):
                    return True

                # Now, prepare to shrink by removing s[left]
                char_out = s2[left]
                left += 1

                if char_out in needs:
                    if window_chars[char_out] == needs[char_out]:
                        valid_chars_count -= 1
                    window_chars[char_out] -= 1
        
        return False
```
Labuladong's visualization `div_permutation-in-string` would show a fixed-size window sliding through `s2`.

## Complexity Analysis
-   **Time Complexity:** $O(N_2 + N_1)$, where $N_1$ is length of `s1` and $N_2$ is length of `s2`.
    - Building `needs` map: $O(N_1)$.
    - The sliding window part: `left` and `right` pointers each traverse `s2` once, $O(N_2)$. Hash map operations are $O(1)$ on average (for fixed alphabet size like ASCII).
-   **Space Complexity:** $O(K)$, where $K$ is the size of the character set (e.g., 26 for lowercase English letters). This is for `needs` and `window_chars` maps.

## Visualization
Let `s1 = "ab"`, `s2 = "eidbaooo"`.
`needs = {'a':1, 'b':1}`, `len(needs)=2`. `target_s1_len = 2`.

| `right` | `char_in` | `window_chars` (after add) | `valid` | `left` | `len=r-l` | Inner Loop Action (`len >= 2`?)  | Output Update |
|:-------:|:---------:|:--------------------------:|:-------:|:------:|:---------:|:-------------------------------:|:-------------:|
| 0       | -         | {}                         | 0       | 0      | 0         |                                 |               |
| 1       | `e`       | `{'e':1}`                  | 0       | 0      | 1         | `1 < 2`, No shrink              |               |
| 2       | `i`       | `{'e':1, 'i':1}`           | 0       | 0      | 2         | `len=2`. `valid(0)!=2`. Shrink: `char_out='e'`, `left=1`. `window={'i':1}`. |               |
| 3       | `d`       | `{'i':1, 'd':1}`           | 0       | 1      | 2         | `len=2`. `valid(0)!=2`. Shrink: `char_out='i'`, `left=2`. `window={'d':1}`. |               |
| 4       | `b`       | `{'d':1, 'b':1}`           | 1       | 2      | 2         | `len=2`. `valid(1)!=2`. Shrink: `char_out='d'`, `left=3`. `window={'b':1}`. |               |
| 5       | `a`       | `{'b':1, 'a':1}`           | **2**   | 3      | 2         | `len=2`. `valid(2)==2`. **Return True.** | **True**      |

The window `s2[3:5]` is "ba", which is a permutation of "ab".

## 总结 (Summary)
- LC567 checks for permutations of `s1` as substrings in `s2`.
- This is effectively a fixed-size sliding window problem. The window size is `len(s1)`.
- Character counts (`needs` for `s1`, `window_chars` for current window) are maintained.
- When the window has the correct size and character counts match `needs` (checked by `valid_chars_count == len(needs)`), a permutation is found.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window Framework]]
"""
    ensure_dir(os.path.join(path, "Interview/Practice/LeetCode"))
    with open(os.path.join(path, "Interview/Practice/LeetCode/LC567 - Permutation in String.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Generated/Updated: Interview/Practice/LeetCode/LC567 - Permutation in String.md")

def create_rabin_karp_concept(path):
    content = r"""---
tags: [concept/algorithms, concept/string_matching, pattern/hashing, pattern/sliding_window, course/labuladong]
aliases: [Rabin-Karp Algorithm, Rolling Hash, 字符串匹配算法, RK算法]
summary: |
  The Rabin-Karp algorithm is a string-searching algorithm that uses hashing to find occurrences of a pattern string within a main text string. 
  It employs a "rolling hash" to efficiently calculate hash values for substrings, often in conjunction with a sliding window approach.
created: 2025-05-26T18:00:00.000-07:00
modified: 2025-05-26T18:00:00.000-07:00
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷数组算法/滑动窗口延伸：Rabin Karp 字符匹配算法.md|滑动窗口延伸：Rabin Karp 字符匹配算法]].
> Labuladong explains Rabin-Karp as an extension of sliding window ideas, using hashing for efficient string comparison.

# Rabin-Karp Algorithm for String Matching

The Rabin-Karp algorithm is a string-searching algorithm that uses hashing to find occurrences of a pattern string within a main text string. It's particularly known for its use of a "rolling hash" technique, which allows for efficient calculation of hash values for substrings as a window slides across the text.

## 🎯 Core Idea: Hashing Substrings

Instead of directly comparing substrings (which can be $O(M)$ for a pattern of length $M$), Rabin-Karp compares hash values.
1.  Calculate the hash of the `pattern` string.
2.  Slide a window of the same length as `pattern` across the `text` string.
3.  For each window in `text`, calculate its hash.
4.  If `hash(window) == hash(pattern)`, then it's a *potential* match. A direct character-by-character comparison is then performed to confirm (to handle hash collisions).

## 🔢 String to Number Conversion (Foundation)

A string can be treated as a number in a base-`R` system, where `R` is the size of the alphabet (e.g., 26 for lowercase English letters, 256 for ASCII).
For a string `s = s_1 s_2 ... s_M`:
`hash(s) = (s_1 \cdot R^{M-1} + s_2 \cdot R^{M-2} + ... + s_M \cdot R^0) \pmod Q`
- `s_i`: Numeric value of the character (e.g., `ord(char) - ord('a')`).
- `R`: Base, often a prime larger than alphabet size.
- `M`: Length of the string.
- `Q`: A large prime modulus to keep hash values manageable and reduce collisions.

**Example: "abc" with R=26, a=0, b=1, c=2**
`hash("abc") = (0 * 26^2 + 1 * 26^1 + 2 * 26^0) = 0 + 26 + 2 = 28` (modulo Q if needed)

This conversion is similar to converting a string representation of a number (e.g., "8264") to an integer, as shown by Labuladong:
```python
# s = "8264" (base 10)
# number = 0
# for char_digit in s:
#     number = 10 * number + (ord(char_digit) - ord('0'))
# Iterations:
# num = 0*10 + 8 = 8
# num = 8*10 + 2 = 82
# num = 82*10 + 6 = 826
# num = 826*10 + 4 = 8264
```

## 🔄 Rolling Hash Technique

The power of Rabin-Karp comes from efficiently updating the hash of the window as it slides.
If `hash(s[i..i+M-1])` is known, `hash(s[i+1..i+M])` can be calculated in $O(1)$ (amortized, considering modulo arithmetic).

Let `h_window = hash(s[i..i+M-1])`.
To get `hash(s[i+1..i+M])`:
1.  **Subtract** the contribution of the leftmost character `s[i]`: `h_window = (h_window - s_i \cdot R^{M-1}) \pmod Q`.
2.  **Shift** the hash value: `h_window = (h_window \cdot R) \pmod Q`.
3.  **Add** the contribution of the new rightmost character `s[i+M]`: `h_window = (h_window + s_{i+M}) \pmod Q`.

Care must be taken with modulo operations, especially subtraction (ensure result is non-negative before modulo). `R^{M-1} \pmod Q` (often called `RM` or `H`) needs to be precomputed.

**Formula for Rolling Hash:**
`hash_next = ((hash_current - val(char_out) * R^{M-1}) * R + val(char_in)) \pmod Q`
Make sure subtractions `(X - Y) \pmod Q` are handled as `(X - Y + Q) \pmod Q` to stay positive.

## 🛠️ Algorithm Steps (Conceptual)
Assume `txt` (length `N`) and `pat` (length `M`).

1.  Choose `R` (base) and `Q` (modulus). Precompute `RM = R^{M-1} \pmod Q`.
2.  Calculate `hash_pat = hash(pat[0..M-1])`.
3.  Calculate `hash_txt_window = hash(txt[0..M-1])`.
4.  Loop `i` from `0` to `N-M`:
    a.  If `hash_txt_window == hash_pat`:
        i.  Perform a character-by-character comparison of `pat` and `txt[i..i+M-1]`.
        ii. If they match, `i` is an occurrence. Add to results.
    b.  If `i < N-M` (i.e., window can slide further):
        i.  Update `hash_txt_window` using the rolling hash formula to represent `txt[i+1..i+M]`.

## Example: LC187 - Repeated DNA Sequences
[[Interview/Practice/LeetCode/LC187 - Repeated DNA Sequences|LC187 - Repeated DNA Sequences]] asks to find all 10-letter-long sequences (substrings) that occur more than once in a DNA molecule.
- Here, the "pattern" length is fixed (10).
- We can slide a window of length 10, calculate the hash of each window.
- Store hashes (or actual substrings if collision) in a set or map to detect repeats.
- Labuladong suggests mapping 'A'->0, 'C'->1, 'G'->2, 'T'->3, so `R=4`.

**Simplified approach for LC187 (without explicit rolling hash, but uses hashing on substrings):**
```python
# Conceptual for LC187
# def findRepeatedDnaSequences(s: str) -> list[str]:
#     L = 10
#     n = len(s)
#     if n <= L:
#         return []
#
#     seen_substrings = set()
#     repeated_substrings = set()
#
#     for i in range(n - L + 1):
#         substring = s[i : i+L]
#         if substring in seen_substrings:
#             repeated_substrings.add(substring)
#         else:
#             seen_substrings.add(substring)
#     return list(repeated_substrings)
```
This uses Python's string hashing implicitly. A full Rabin-Karp would implement custom rolling hash for these substrings.

## Example: LC28 - Find the Index of the First Occurrence in a String
[[Interview/Practice/LeetCode/LC28 - Find the Index of the First Occurrence in a String|LC28 - Find Index]] (implement `strStr()`). This is a direct application of string matching.
- `pattern` is `needle`. `text` is `haystack`.

**Python's `str.find()` or KMP are often faster for this specific problem due to highly optimized C implementations, but Rabin-Karp is a valid approach.**

```python
# Conceptual Rabin-Karp for LC28 (Python-like, actual implementation needs care with modulo)
# class Solution:
#     def strStr(self, haystack: str, needle: str) -> int:
#         N, M = len(haystack), len(needle)
#         if M == 0: return 0
#         if N < M: return -1

#         R = 256 # Alphabet size (ASCII)
#         Q = 101 # A small prime for example, usually larger (e.g., 10^9 + 7)
        
#         # Precompute R^(M-1) % Q
#         RM = 1
#         for _ in range(M - 1):
#             RM = (RM * R) % Q

#         hash_needle = 0
#         hash_haystack_window = 0
#         for i in range(M):
#             hash_needle = (R * hash_needle + ord(needle[i])) % Q
#             hash_haystack_window = (R * hash_haystack_window + ord(haystack[i])) % Q
        
#         if hash_needle == hash_haystack_window:
#             if haystack[0:M] == needle: # Verify
#                 return 0
        
#         for i in range(1, N - M + 1):
#             # Rolling hash for haystack window
#             # hash_haystack_window = (hash_haystack_window - ord(haystack[i-1]) * RM) % Q # Subtract outgoing
#             # hash_haystack_window = (hash_haystack_window + Q) % Q # Ensure positive
#             # hash_haystack_window = (hash_haystack_window * R) % Q # Shift
#             # hash_haystack_window = (hash_haystack_window + ord(haystack[i+M-1])) % Q # Add incoming

#             # Corrected rolling hash for base R polynomial
#             val_out = ord(haystack[i-1])
#             val_in = ord(haystack[i+M-1])
#             hash_haystack_window = ( (hash_haystack_window - val_out * RM) * R + val_in ) % Q
#             hash_haystack_window = (hash_haystack_window + Q) % Q # Ensure positive
            
#             if hash_needle == hash_haystack_window:
#                 if haystack[i : i+M] == needle: # Verify
#                     return i
        
#         return -1
```
*Note: The modulo arithmetic for rolling hash, especially subtraction, needs to be handled carefully to keep results positive and correct.*

## Complexity
- **Preprocessing (hash of pattern, first window, RM):** $O(M)$.
- **Main Loop (Rolling Hash & Comparison):**
    - Each roll: $O(1)$ (amortized for large numbers, or if arithmetic is on fixed-size integers).
    - Number of windows: $N-M+1$. Total $O(N-M)$.
    - If hash matches, character verification: $O(M)$.
- **Worst Case (e.g., many hash collisions, "aaaaa" in "aaaaaaaaaa"):** $O((N-M+1) \cdot M) = O(NM)$.
- **Average Case (good hash function, few collisions):** $O(N+M)$.

## 🚀 Advantages
- Conceptually extends sliding window.
- Can be very fast on average.
- Used in practice for multiple pattern matching (Aho-Corasick variant).

## 👎 Disadvantages
- Worst-case performance can be poor ($O(NM)$).
- Relies on good hash function and prime modulus `Q` to minimize collisions.
- Arithmetic overflow can be an issue if `Q` and `R` are not chosen carefully relative to integer sizes.

## 总结 (Summary)
- Rabin-Karp is a string matching algorithm using hashing and a rolling hash technique.
- It slides a window over the text, comparing hash of window with hash of pattern.
- Rolling hash allows $O(1)$ update of window hash.
- Average time complexity $O(N+M)$, worst-case $O(NM)$.
- Proper choice of base `R` and modulus `Q`, and careful handling of modulo arithmetic (especially for subtraction in rolling hash) are crucial for correctness and performance.

---
Parent: [[Interview/Concept/Algorithms/String Matching/index|String Matching Algorithms]] (Create this index if it doesn't exist)
Related Concepts: [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window]], [[Interview/Concept/Algorithms/Hashing/index|Hashing]]
Related Problems: [[Interview/Practice/LeetCode/LC187 - Repeated DNA Sequences|LC187]], [[Interview/Practice/LeetCode/LC28 - Find the Index of the First Occurrence in a String|LC28]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/String Matching"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/String Matching/00 - Rabin-Karp Algorithm.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Generated/Updated: Interview/Concept/Algorithms/String Matching/00 - Rabin-Karp Algorithm.md")

def create_lc187_repeated_dna_sequences(path):
    content = r"""---
tags: [problem/leetcode, lc/medium, topic/string, topic/hash_table, pattern/sliding_window, pattern/hashing, algorithm/rabin_karp, course/labuladong]
aliases: [LC187, LeetCode 187, Repeated DNA Sequences]
summary: |
  Find all 10-letter-long DNA sequences that occur more than once. 
  Can be solved using a sliding window and hashing substrings (either Python's built-in or custom like Rabin-Karp).
created: 2025-05-26T18:00:00.000-07:00
modified: 2025-05-26T18:00:00.000-07:00
---

> [!NOTE] Source Annotation
> Problem: LeetCode 187. Repeated DNA Sequences
> Mentioned in [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷数组算法/滑动窗口延伸：Rabin Karp 字符匹配算法.md|滑动窗口延伸：Rabin Karp 字符匹配算法]].

# LeetCode 187: Repeated DNA Sequences

## Problem Statement

The DNA sequence is composed of a series of nucleotides abbreviated as `'A'`, `'C'`, `'G'`, and `'T'`.
For example, `"ACGAATTCCG"` is a DNA sequence.
When studying DNA, it is useful to identify repeated sequences within the DNA.
Given a string `s` that represents a DNA sequence, return all the **10-letter-long** sequences (substrings) that occur more than once in the DNA molecule. You may return the answer in **any order**.

**Official Link:** [LeetCode 187. Repeated DNA Sequences](https://leetcode.com/problems/repeated-dna-sequences/)

**Example 1:**
Input: `s = "AAAAACCCCCAAAAACCCCCCAAAAAGGGTTT"`
Output: `["AAAAACCCCC","CCCCCAAAAA"]`

## Solution Approach: Sliding Window + Hashing

This problem asks us to find all fixed-length (10) substrings that are repeated. This is a good candidate for a sliding window of size 10, combined with hashing to keep track of substrings encountered.

1.  **Sliding Window:** Iterate through `s` with a window of length 10.
2.  **Hashing Substrings:** For each substring in the window:
    -   Use a hash set `seen_substrings` to store unique substrings encountered so far.
    -   Use another hash set `repeated_substrings_result` to store the substrings that have been identified as repeats (to avoid adding the same repeated substring multiple times to the final result list).
3.  **Logic:**
    -   For each window (substring `sub`):
        -   If `sub` is in `seen_substrings`, it means we've seen it before, so it's a repeat. Add `sub` to `repeated_substrings_result`.
        -   If `sub` is not in `seen_substrings`, add it to `seen_substrings`.

### Python Solution (Using Python's string slicing and set hashing)
```python
import collections

class Solution:
    def findRepeatedDnaSequences(self, s: str) -> list[str]:
        L = 10  # Length of the DNA sequence to find
        n = len(s)

        if n <= L:
            return []

        seen_substrings = set()
        # Using a set for results ensures each repeated sequence is added only once
        repeated_substrings_result = set() 

        # Iterate with a sliding window of size L
        for i in range(n - L + 1):
            current_substring = s[i : i + L]
            
            if current_substring in seen_substrings:
                repeated_substrings_result.add(current_substring)
            else:
                seen_substrings.add(current_substring)
                
        return list(repeated_substrings_result)
```

### Rabin-Karp Based Optimization (Conceptual)
While the above solution is simple and often passes due to optimized string hashing in Python, a true [[Interview/Concept/Algorithms/String Matching/00 - Rabin-Karp Algorithm|Rabin-Karp]] approach would implement a rolling hash for the 10-letter window.
- Map 'A', 'C', 'G', 'T' to integers (e.g., 0, 1, 2, 3). Use base `R=4`.
- Calculate hash for the first 10-letter window.
- Slide the window, using rolling hash to update the hash value in $O(1)$.
- Store hash values in a set (`seen_hashes`) and identify duplicates for a secondary set (`repeated_hashes`).
- If a hash collision occurs or a hash is identified as repeated, then verify the actual substring to avoid false positives from hash collisions (though with good hashing, this is rare, and for this problem, one might even risk not verifying if problem constraints allow, or if the hash space is large enough). For LeetCode, usually, substring verification or storing actual substrings after hash match is safer.

```python
# Conceptual Rabin-Karp logic addition for LC187
# class SolutionRabinKarp:
#     def findRepeatedDnaSequences(self, s: str) -> list[str]:
#         L = 10
#         N = len(s)
#         if N <= L: return []

#         # Map chars to int
#         mapping = {'A': 0, 'C': 1, 'G': 2, 'T': 3}
#         R = 4 # Base for hash

#         # Modulus for hash (large prime)
#         Q = (1 << 31) - 1 # Example large prime (Mersenne prime 2^31 - 1)
        
#         RL = pow(R, L - 1, Q) # R^(L-1) % Q

#         hashes = set()
#         result_set = set()

#         current_hash = 0
#         # Calculate hash for the first window s[0...L-1]
#         for i in range(L):
#             current_hash = (current_hash * R + mapping[s[i]]) % Q
        
#         hashes.add(current_hash)

#         # Slide window from L to N-1
#         for i in range(L, N):
#             # Rolling hash: subtract s[i-L], add s[i]
#             val_out = mapping[s[i-L]]
#             val_in = mapping[s[i]]
            
#             current_hash = (current_hash - val_out * RL) % Q
#             current_hash = (current_hash + Q) % Q # Ensure positive
#             current_hash = (current_hash * R + val_in) % Q
            
#             if current_hash in hashes:
#                 # Potential repeat. For this problem, string itself is key.
#                 # A simple hash set might lead to collision if Q is not large enough
#                 # or hash function is not perfect.
#                 # Usually, one might store the string in a secondary check or use hash -> list of start_indices.
#                 # For LC, often direct string comparison is done upon hash match or simply
#                 # add the string s[i-L+1 : i+1] to result_set if its hash is already seen.
#                 # The simpler Pythonic solution above using set of strings is often sufficient.
#                 # If we trust the hash enough (or problem allows for it):
#                 result_set.add(s[i-L+1 : i+1]) # Add the actual substring
#             else:
#                 hashes.add(current_hash)
        
#         return list(result_set)
```
The simpler Python string-set solution is generally preferred for its clarity unless rolling hash is explicitly required for performance on extremely long strings or custom alphabet constraints.

## Complexity Analysis
**Simple String Hashing Solution:**
-   **Time Complexity:** $O((N-L+1) \cdot L)$.
    -   Iterating $N-L+1$ times for the window.
    -   Each substring slicing `s[i : i+L]` takes $O(L)$.
    -   Set insertion and lookup of strings of length $L$ take $O(L)$ on average (due to hashing and comparison of strings).
    -   So, total is $O((N-L) \cdot L)$. Given $L=10$, this is effectively $O(N)$.
-   **Space Complexity:** $O((N-L) \cdot L)$ in the worst case if all substrings are unique and stored in `seen_substrings`.

**Rabin-Karp Rolling Hash Solution:**
-   **Time Complexity:** $O(N-L+1)$ on average, i.e., $O(N)$.
    -   Initial hash: $O(L)$.
    -   Each rolling hash update: $O(1)$.
    -   Set operations on integer hashes: $O(1)$ average.
    -   If string verification is needed on hash collision, it adds $O(L)$ per collision. With good hashing, collisions are rare.
-   **Space Complexity:** $O(N-L)$ for storing integer hashes or $O((N-L) \cdot L)$ if storing actual repeated substrings.

## 总结 (Summary)
- LC187 asks for repeated 10-letter DNA sequences.
- A sliding window of size 10 is used to examine all such substrings.
- Hashing (either Python's built-in for strings or a custom rolling hash like Rabin-Karp) is used to efficiently track seen substrings and identify duplicates.
- The straightforward solution using a set of strings is often sufficient and clear for typical LeetCode constraints for this problem.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/String Matching/00 - Rabin-Karp Algorithm|Rabin-Karp Algorithm]], [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window]]
"""
    ensure_dir(os.path.join(path, "Interview/Practice/LeetCode"))
    with open(os.path.join(path, "Interview/Practice/LeetCode/LC187 - Repeated DNA Sequences.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Generated/Updated: Interview/Practice/LeetCode/LC187 - Repeated DNA Sequences.md")

def create_lc28_find_index(path):
    content = r"""---
tags: [problem/leetcode, lc/easy, topic/string, pattern/string_matching, algorithm/rabin_karp, algorithm/kmp, course/labuladong]
aliases: [LC28, LeetCode 28, Implement strStr(), Find First Occurrence]
summary: |
  Implement strStr() to find the first occurrence of a needle string in a haystack string.
  Commonly solved with built-in functions, KMP, or Rabin-Karp.
created: 2025-05-26T18:00:00.000-07:00
modified: 2025-05-26T18:00:00.000-07:00
---

> [!NOTE] Source Annotation
> Problem: LeetCode 28. Find the Index of the First Occurrence in a String
> Mentioned in [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷数组算法/滑动窗口延伸：Rabin Karp 字符匹配算法.md|滑动窗口延伸：Rabin Karp 字符匹配算法]] as an application.

# LeetCode 28: Find the Index of the First Occurrence in a String

## Problem Statement

Given two strings `haystack` and `needle`, return the index of the first occurrence of `needle` in `haystack`, or `-1` if `needle` is not part of `haystack`.

**Official Link:** [LeetCode 28. Find the Index of the First Occurrence in a String](https://leetcode.com/problems/find-the-index-of-the-first-occurrence-in-a-string/)

**Example 1:**
Input: `haystack = "sadbutsad"`, `needle = "sad"`
Output: `0`
Explanation: `"sad"` occurs at index 0 and 6. The first occurrence is at index 0, so we return 0.

**Example 2:**
Input: `haystack = "leetcode"`, `needle = "leeto"`
Output: `-1`
Explanation: `"leeto"` did not occur in `"leetcode"`, so we return -1.

## Solution Approaches

### 1. Built-in String Method (Pythonic)
Most languages provide a built-in method for this.
```python
class Solution:
    def strStr_builtin(self, haystack: str, needle: str) -> int:
        if not needle: # An empty needle is usually considered to be at index 0
            return 0
        return haystack.find(needle) # Returns -1 if not found
```
This is often the most practical solution in a timed contest if allowed, but interviewers might ask for a manual implementation.

### 2. Brute-Force / Naive Sliding Window
Iterate through `haystack`, and for each starting position `i`, check if the substring `haystack[i : i+len(needle)]` is equal to `needle`.
```python
class Solution:
    def strStr_brute_force(self, haystack: str, needle: str) -> int:
        N, M = len(haystack), len(needle)
        if M == 0: return 0
        if N < M: return -1

        for i in range(N - M + 1): # Possible start indices in haystack
            # Check if haystack[i : i+M] == needle
            match = True
            for j in range(M):
                if haystack[i+j] != needle[j]:
                    match = False
                    break
            if match:
                return i
        return -1
```
- **Time Complexity:** $O((N-M+1) \cdot M) = O(NM)$ in the worst case.
- **Space Complexity:** $O(1)$ (or $O(M)$ if slicing creates copies, but direct char comparison is $O(1)$).

### 3. Rabin-Karp Algorithm
This uses hashing and a rolling hash. A detailed explanation and conceptual code for Rabin-Karp applied to this problem is in [[Interview/Concept/Algorithms/String Matching/00 - Rabin-Karp Algorithm|Rabin-Karp Algorithm]].
- **Average Time Complexity:** $O(N+M)$.
- **Worst-Case Time Complexity:** $O(NM)$.
- **Space Complexity:** $O(1)$ for hash values (or $O(M)$ if storing pattern string).

### 4. Knuth-Morris-Pratt (KMP) Algorithm
KMP is an optimized string matching algorithm that preprocesses the `needle` to build an LPS (Longest Proper Prefix which is also Suffix) array. This allows skipping redundant comparisons when a mismatch occurs.
- **Time Complexity:** $O(N+M)$ (preprocessing `needle` is $O(M)$, searching is $O(N)$).
- **Space Complexity:** $O(M)$ for the LPS array.
(KMP is a more advanced topic, usually covered separately, e.g., `[[Interview/Concept/Algorithms/String Matching/01 - KMP Algorithm|KMP Algorithm]]` - placeholder).

## Recommended Solution for Interviews (if not using built-in)

If asked to implement manually, the brute-force approach is simple to code. If better performance is required and KMP is known, it's optimal. Rabin-Karp is also a valid approach. The choice might depend on interviewer expectations. For LC28, brute-force usually passes.

Python Solution (using the brute-force approach, as it's simplest to implement manually if `find` is disallowed):
```python
class Solution:
    def strStr(self, haystack: str, needle: str) -> int:
        N, M = len(haystack), len(needle)
        
        if M == 0: # According to LeetCode, an empty needle is found at index 0.
            return 0
        if N < M: # Needle cannot be in haystack if it's longer.
            return -1

        for i in range(N - M + 1):
            # Check if the substring of haystack starting at i and of length M matches needle
            if haystack[i : i+M] == needle:
                return i
        
        return -1
```
This Python version leverages string slicing, which is efficient in Python. The slice `haystack[i : i+M]` takes $O(M)$ time to create and compare. So the loop runs $N-M+1$ times, giving $O((N-M)M)$ complexity.

## 总结 (Summary)
- LC28 (Implement `strStr()`) asks for the first index of `needle` in `haystack`.
- **Built-in:** `haystack.find(needle)` is simplest.
- **Brute-Force:** Iterate and compare substrings, $O(NM)$.
- **Rabin-Karp:** Hashing with rolling hash, average $O(N+M)$.
- **KMP:** Optimal $O(N+M)$ using precomputed LPS array for `needle`.
- For typical interview constraints on this problem, brute-force or Python's slicing comparison often suffices if built-ins are disallowed.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/String Matching/00 - Rabin-Karp Algorithm|Rabin-Karp Algorithm]], `[[Interview/Concept/Algorithms/String Matching/01 - KMP Algorithm|KMP Algorithm]]`
"""
    ensure_dir(os.path.join(path, "Interview/Practice/LeetCode"))
    with open(os.path.join(path, "Interview/Practice/LeetCode/LC28 - Find the Index of the First Occurrence in a String.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Generated/Updated: Interview/Practice/LeetCode/LC28 - Find the Index of the First Occurrence in a String.md")

def create_binary_search_2d_matrix_concept(path):
    content = r"""---
tags: [concept/algorithms, concept/searching, pattern/binary_search, topic/matrix, course/labuladong]
aliases: [Binary Search in 2D Array, Search a 2D Matrix, 二维数组二分搜索]
summary: |
  Explains how to apply binary search techniques to 2D matrices, typically by treating the matrix as a flattened 1D sorted array or by performing binary search on rows then columns (or vice-versa) if rows/columns are sorted.
created: 2025-05-26T18:00:00.000-07:00
modified: 2025-05-26T18:00:00.000-07:00
---

> [!NOTE] Source Annotation
> Content conceptualized from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷数组算法/【练习】二分搜索算法经典习题.md|【练习】二分搜索算法经典习题.md]] which mentions "二维数组中的二分搜索".
> This note details common strategies for applying binary search to 2D matrices.

# Binary Search in 2D Matrices

Applying binary search to a 2D matrix requires adapting the 1D binary search logic to the matrix's structure. The specific approach depends on the properties of the matrix (e.g., how it's sorted).

## Strategy 1: Treat Matrix as a Flattened 1D Sorted Array

This strategy is applicable if the matrix has the property that:
- Each row is sorted from left to right.
- The first integer of each row is greater than the last integer of the previous row.
(Example: LeetCode 74. Search a 2D Matrix)

If these properties hold, the entire matrix can be conceptually viewed as a single, large, sorted 1D array.

**Algorithm:**
1.  Get matrix dimensions: `rows`, `cols`.
2.  Perform a standard binary search on the index range `0` to `rows * cols - 1`.
3.  For a `mid_1d_index` obtained from the 1D binary search:
    -   Convert it back to 2D matrix coordinates:
        -   `row = mid_1d_index // cols`
        -   `col = mid_1d_index % cols`
    -   Compare `matrix[row][col]` with the `target`.
    -   Adjust `left_1d` and `right_1d` pointers of the 1D binary search accordingly.

```python
# Conceptual for LC74-like matrix
# class Solution:
#     def searchMatrix_flattened(self, matrix: list[list[int]], target: int) -> bool:
#         if not matrix or not matrix[0]:
#             return False
        
#         rows, cols = len(matrix), len(matrix[0])
#         left, right = 0, rows * cols - 1

#         while left <= right:
#             mid_1d = left + (right - left) // 2
#             mid_row = mid_1d // cols
#             mid_col = mid_1d % cols
#             val_at_mid = matrix[mid_row][mid_col]

#             if val_at_mid == target:
#                 return True
#             elif val_at_mid < target:
#                 left = mid_1d + 1
#             else:
#                 right = mid_1d - 1
#         return False
```
- **Time Complexity:** $O(\log(R \cdot C))$, where R is rows, C is columns.
- **Space Complexity:** $O(1)$.

## Strategy 2: Binary Search on Rows, then Binary Search on Chosen Row

Applicable if:
- Each row is sorted.
- (Optional, but helps for row selection) Rows are sorted with respect to their first/last elements.
  (Example: LeetCode 74 can also use this. Find the row where `target` might be, then BS in that row).

**Algorithm (for LC74-like matrix):**
1.  **Find Target Row:** Perform binary search on the first (or last) elements of each row to identify which row *could* contain the `target`.
    - E.g., find row `r` such that `matrix[r][0] <= target <= matrix[r][cols-1]`.
2.  **Search in Row:** Once a candidate row `r` is found, perform a standard 1D binary search on `matrix[r]`.

```python
# Conceptual for LC74-like matrix (alternative)
# class Solution:
#     def searchMatrix_row_then_col(self, matrix: list[list[int]], target: int) -> bool:
#         if not matrix or not matrix[0]: return False
#         rows, cols = len(matrix), len(matrix[0])

#         # Step 1: Find the target row using BS on first elements
#         top, bottom = 0, rows - 1
#         target_row_idx = -1
#         while top <= bottom:
#             row_mid = top + (bottom - top) // 2
#             if matrix[row_mid][0] <= target <= matrix[row_mid][cols-1]:
#                 target_row_idx = row_mid
#                 break
#             elif matrix[row_mid][0] > target:
#                 bottom = row_mid - 1
#             else: # matrix[row_mid][cols-1] < target
#                 top = row_mid + 1
        
#         if target_row_idx == -1: return False # Target cannot be in any row based on range

#         # Step 2: Binary search in the identified row
#         left, right = 0, cols - 1
#         while left <= right:
#             col_mid = left + (right - left) // 2
#             if matrix[target_row_idx][col_mid] == target:
#                 return True
#             elif matrix[target_row_idx][col_mid] < target:
#                 left = col_mid + 1
#             else:
#                 right = col_mid - 1
#         return False
```
- **Time Complexity:** $O(\log R + \log C)$.
- **Space Complexity:** $O(1)$.

## Strategy 3: Start from a Corner, Eliminate Rows/Columns

Applicable if:
- Rows are sorted left-to-right.
- Columns are sorted top-to-bottom.
(Example: LeetCode 240. Search a 2D Matrix II)

**Algorithm:**
1. Start at a specific corner, e.g., top-right `(row=0, col=cols-1)` or bottom-left `(row=rows-1, col=0)`.
2. Let current element be `matrix[row][col]`.
   - If `matrix[row][col] == target`, found.
   - If `matrix[row][col] > target`: The target cannot be in the current column (if starting top-right, because elements below are larger) or current row (if starting bottom-left, because elements to right are larger). So, eliminate current column (`col--` if top-right) or row (`row--` if bottom-left).
   - If `matrix[row][col] < target`: The target cannot be in the current row (if starting top-right) or current column (if starting bottom-left). So, eliminate current row (`row++` if top-right) or column (`col++` if bottom-left).
3. Repeat until target found or pointers go out of bounds.

```python
# Conceptual for LC240-like matrix
# class Solution:
#     def searchMatrix_corner_elimination(self, matrix: list[list[int]], target: int) -> bool:
#         if not matrix or not matrix[0]: return False
#         rows, cols = len(matrix), len(matrix[0])
#         row, col = 0, cols - 1 # Start at top-right corner

#         while row < rows and col >= 0:
#             current_val = matrix[row][col]
#             if current_val == target:
#                 return True
#             elif current_val > target:
#                 col -= 1 # Target must be to the left
#             else: # current_val < target
#                 row += 1 # Target must be downwards
#         return False
```
- **Time Complexity:** $O(R+C)$. In each step, either `row` increases or `col` decreases.
- **Space Complexity:** $O(1)$.

## 总结 (Summary)
- Binary search in 2D matrices depends on the matrix's sorting properties.
- **Flattened 1D Search ($O(\log(RC))$):** If rows are sorted and each row's start > previous row's end. (e.g., LC74)
- **Row BS then Column BS ($O(\log R + \log C)$):** If rows are sorted, and first/last elements of rows are also sorted. (e.g., LC74)
- **Corner Elimination ($O(R+C)$):** If rows sorted L-R and columns sorted T-B. This is not strictly binary search but a similar search space reduction technique. (e.g., LC240)
- Understanding the specific sorted structure of the matrix is key to choosing the right approach.

---
Parent: [[Interview/Concept/Algorithms/Searching/Binary Search/index|Binary Search Algorithms Index]]
Related Problems: [[Interview/Practice/LeetCode/LC74 - Search a 2D Matrix|LC74]], [[Interview/Practice/LeetCode/LC240 - Search a 2D Matrix II|LC240]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Searching/Binary Search"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Searching/Binary Search/02 - Binary Search in 2D Matrix.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Generated/Updated: Interview/Concept/Algorithms/Searching/Binary Search/02 - Binary Search in 2D Matrix.md")


def create_string_matching_index(path):
    content = r"""---
tags: [index, concept/algorithms, concept/string_matching]
aliases: [String Matching Index, String Search Algorithms]
---

# String Matching Algorithms

This section covers algorithms designed for finding occurrences of a pattern string within a larger text string.

## Core Algorithms:
- [[Interview/Concept/Algorithms/String Matching/00 - Rabin-Karp Algorithm|Rabin-Karp Algorithm]] (Hashing-based)
- `[[Interview/Concept/Algorithms/String Matching/01 - KMP Algorithm|Knuth-Morris-Pratt (KMP) Algorithm]]` (Placeholder)
- `[[Interview/Concept/Algorithms/String Matching/02 - Boyer-Moore Algorithm|Boyer-Moore Algorithm]]` (Placeholder)

## Visualization
```mermaid
graph TD
    SM["String Matching"] --> RK["[[Interview/Concept/Algorithms/String Matching/00 - Rabin-Karp Algorithm|Rabin-Karp]]"]
    SM --> KMP["(KMP Algorithm)"]
    SM --> BM["(Boyer-Moore)"]

    classDef main fill:#e6ffe6,stroke:#006400,stroke-width:2px;
    class SM main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/String Matching"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/String Matching/index.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Generated/Updated: Interview/Concept/Algorithms/String Matching/index.md")


def update_algo_index(path):
    index_path = os.path.join(path, "Interview/Concept/Algorithms/index.md")
    
    # Ensure the directory and file exist, creating basic structure if not
    ensure_dir(os.path.dirname(index_path))
    if not os.path.exists(index_path):
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write(textwrap.dedent(r"""---
tags: [index, concept/algorithms]
aliases: [Algorithms Index, Algorithm Concepts]
---

# Algorithm Concepts

This index covers various algorithm design paradigms, specific algorithms, and analytical techniques relevant to coding interviews.

## Algorithm Categories / Patterns
- [[Interview/Concept/Algorithms/Sliding Window/index|Sliding Window]]
- [[Interview/Concept/Algorithms/String Matching/index|String Matching]]
## Visualization of Algorithm Areas
```mermaid
graph TD
    Algo["Algorithms"] --> SlidingWindow["[[Interview/Concept/Algorithms/Sliding Window/index|Sliding Window]]"]
    Algo --> StrMatch["[[Interview/Concept/Algorithms/String Matching/index|String Matching]]"]
    
    classDef category fill:#e0ffff,stroke:#008b8b,stroke-width:2px;
    class Algo, SlidingWindow, StrMatch category;
```
"""))
        print(f"Created basic: {index_path}")
        return

    with open(index_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Add to list of categories if not present
    categories_section_header = "## Algorithm Categories / Patterns"
    sliding_window_link = "- [[Interview/Concept/Algorithms/Sliding Window/index|Sliding Window]]"
    string_matching_link = "- [[Interview/Concept/Algorithms/String Matching/index|String Matching]]"
    
    if categories_section_header in content:
        if sliding_window_link not in content:
            content = content.replace(categories_section_header, f"{categories_section_header}\n{sliding_window_link}")
        if string_matching_link not in content:
            current_links_str = content.split(categories_section_header)[1].split("##")[0] # get links under the section
            if string_matching_link not in current_links_str:
                 content = content.replace(categories_section_header, f"{categories_section_header}\n{string_matching_link}", 1)


    # Update Mermaid diagram
    mermaid_start_tag = "## Visualization of Algorithm Areas\n```mermaid"
    mermaid_end_tag = "```"
    
    sliding_window_node = '    Algo --> SlidingWindow["[[Interview/Concept/Algorithms/Sliding Window/index|Sliding Window]]"]'
    string_matching_node = '    Algo --> StrMatch["[[Interview/Concept/Algorithms/String Matching/index|String Matching]]"]'
    
    sliding_window_class = '    class Algo, SlidingWindow category;' # Assuming Algo and category exist
    string_matching_class_update = '    class Algo, SlidingWindow, StrMatch category;'


    if mermaid_start_tag in content:
        start_idx = content.find(mermaid_start_tag) + len(mermaid_start_tag)
        end_idx = content.find(mermaid_end_tag, start_idx)
        mermaid_content = content[start_idx:end_idx]

        if sliding_window_node not in mermaid_content:
            mermaid_content += f"\n{sliding_window_node}"
        if string_matching_node not in mermaid_content:
            mermaid_content += f"\n{string_matching_node}"
        
        # Update class definition
        if "StrMatch" not in mermaid_content: # Check if StrMatch needs to be added to class def
            if sliding_window_class in mermaid_content:
                 mermaid_content = mermaid_content.replace(sliding_window_class, string_matching_class_update)
            elif "class Algo" in mermaid_content and "category;" in mermaid_content: # More general update
                # find the line with "class Algo" and "category;"
                class_lines = [line for line in mermaid_content.splitlines() if "class Algo" in line and "category;" in line]
                if class_lines:
                    old_class_line = class_lines[0]
                    # simplistic update, might need more robust parsing for complex class defs
                    if "SlidingWindow" in old_class_line and "StrMatch" not in old_class_line:
                        new_class_line = old_class_line.replace("category;", "SlidingWindow, StrMatch category;")
                        mermaid_content = mermaid_content.replace(old_class_line, new_class_line)
                    elif "StrMatch" not in old_class_line: # if SlidingWindow not there but Algo is
                        new_class_line = old_class_line.replace("category;", "StrMatch category;")
                        mermaid_content = mermaid_content.replace(old_class_line, new_class_line)


        content = content[:start_idx] + mermaid_content + content[end_idx:]
    
    with open(index_path, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Updated: {index_path}")


# --- Main Script ---
if __name__ == "__main__":
    kb_root = ".." # Assuming script is run from the root of the Obsidian vault or "Interview" folder's parent

    # File 1: Sliding Window Framework and its LeetCode problems
    create_sliding_window_framework(kb_root) # Updates existing
    create_lc76_minimum_window_substring(kb_root) # Creates/Updates LC76
    create_lc567_permutation_in_string(kb_root) # Creates/Updates LC567
    
    # Create placeholders for other LC problems mentioned in sliding window framework if they don't exist
    # LC3, LC438 (Placeholders already exist in the provided context for these)
    # LC1004 (New placeholder to create)
    ensure_dir(os.path.join(kb_root, "Interview/Practice/LeetCode"))
    lc1004_path = os.path.join(kb_root, "Interview/Practice/LeetCode/LC1004 - Max Consecutive Ones III.md")
    if not os.path.exists(lc1004_path):
        with open(lc1004_path, 'w', encoding='utf-8') as f:
            f.write(textwrap.dedent(r"""---
tags: [problem/leetcode, lc/medium, topic/sliding_window, pattern/two_pointers, pattern/window_optimization, course/labuladong_mention]
aliases: [LC1004, LeetCode 1004, Max Consecutive Ones III]
summary: Find the maximum number of consecutive 1's in a binary array if you can flip at most k 0's. Solved with sliding window.
created: 2025-05-26T18:00:00.000-07:00
modified: 2025-05-26T18:00:00.000-07:00
---

> [!NOTE] Source Annotation
> Problem: LeetCode 1004. Max Consecutive Ones III
> Mentioned in [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/滑动窗口算法核心代码模板.md|滑动窗口算法核心代码模板]].
> Full solution and explanation adapted from [[Interview/Practice/LeetCode/LC1004 - Max Consecutive Ones III.md]].

# LeetCode 1004: Max Consecutive Ones III
(Content will be in the main LC1004 note, this is just linking to its future self for now or if it was created separately)

See detailed explanation and solution at [[Interview/Practice/LeetCode/LC1004 - Max Consecutive Ones III.md]].

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window Framework]]
"""))
        print(f"Created placeholder: {lc1004_path}")

    # File 2: Rabin-Karp
    create_rabin_karp_concept(kb_root)
    create_lc187_repeated_dna_sequences(kb_root)
    create_lc28_find_index(kb_root)

    # File 4: Binary Search in 2D Matrix
    create_binary_search_2d_matrix_concept(kb_root)
    # Create placeholder LC notes for 2D BS if not existing
    lc74_path = os.path.join(kb_root, "Interview/Practice/LeetCode/LC74 - Search a 2D Matrix.md")
    if not os.path.exists(lc74_path):
        with open(lc74_path, 'w', encoding='utf-8') as f:
            f.write(textwrap.dedent(r"""---
tags: [problem/leetcode, lc/medium, topic/array, topic/matrix, pattern/binary_search]
aliases: [LC74, Search a 2D Matrix]
---
# LeetCode 74: Search a 2D Matrix
Placeholder for solution.
Related: [[Interview/Concept/Algorithms/Searching/Binary Search/02 - Binary Search in 2D Matrix.md]]
"""))
        print(f"Created placeholder: {lc74_path}")
        
    lc240_path = os.path.join(kb_root, "Interview/Practice/LeetCode/LC240 - Search a 2D Matrix II.md")
    if not os.path.exists(lc240_path):
        with open(lc240_path, 'w', encoding='utf-8') as f:
            f.write(textwrap.dedent(r"""---
tags: [problem/leetcode, lc/medium, topic/array, topic/matrix, pattern/divide_and_conquer]
aliases: [LC240, Search a 2D Matrix II]
---
# LeetCode 240: Search a 2D Matrix II
Placeholder for solution.
Related: [[Interview/Concept/Algorithms/Searching/Binary Search/02 - Binary Search in 2D Matrix.md]]
"""))
        print(f"Created placeholder: {lc240_path}")


    # Update Index Files
    # Ensuring these index files exist or are created with basic structure
    ensure_dir(os.path.join(kb_root, "Interview/Concept/Algorithms/Sliding Window"))
    sliding_window_index_path = os.path.join(kb_root, "Interview/Concept/Algorithms/Sliding Window/index.md")
    if not os.path.exists(sliding_window_index_path):
        with open(sliding_window_index_path, 'w', encoding='utf-8') as f:
            f.write(textwrap.dedent(r"""---
tags: [index, concept/algorithms, pattern/sliding_window, pattern/two_pointers]
aliases: [Sliding Window Index]
---
# Sliding Window Algorithm
- [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window - Core Framework]]
---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
"""))
        print(f"Created basic: {sliding_window_index_path}")
    
    create_string_matching_index(kb_root) # This will create if not exists, or overwrite with this content
    
    ensure_dir(os.path.join(kb_root, "Interview/Concept/Algorithms/Searching/Binary Search"))
    binary_search_index_path = os.path.join(kb_root, "Interview/Concept/Algorithms/Searching/Binary Search/index.md")
    if not os.path.exists(binary_search_index_path):
        with open(binary_search_index_path, 'w', encoding='utf-8') as f:
            f.write(textwrap.dedent(r"""---
tags: [index, concept/algorithms, concept/searching, concept/binary_search]
aliases: [Binary Search Index]
---
# Binary Search Algorithms
- [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary Search - Core Template]]
- [[Interview/Concept/Algorithms/Searching/Binary Search/01 - Binary Search - Advanced Application Framework|Binary Search - Advanced Application Framework]]
- [[Interview/Concept/Algorithms/Searching/Binary Search/02 - Binary Search in 2D Matrix|Binary Search in 2D Matrix]]
---
Parent: [[Interview/Concept/Algorithms/Searching/index|Searching Algorithms Index]]
"""))
        print(f"Created basic: {binary_search_index_path}")
    else: # If it exists, try to add the new 2D matrix link
        with open(binary_search_index_path, 'r', encoding='utf-8') as f_read:
            bs_idx_content = f_read.read()
        new_link = "- [[Interview/Concept/Algorithms/Searching/Binary Search/02 - Binary Search in 2D Matrix|Binary Search in 2D Matrix]]"
        if new_link not in bs_idx_content:
            # Append after existing list items or before --- Parent:
            parent_marker = "---"
            parts = bs_idx_content.split(parent_marker, 1)
            if len(parts) > 1:
                bs_idx_content = parts[0].strip() + "\n" + new_link + "\n" + parent_marker + parts[1]
            else: # No parent marker, just append
                bs_idx_content = bs_idx_content.strip() + "\n" + new_link
            with open(binary_search_index_path, 'w', encoding='utf-8') as f_write:
                f_write.write(bs_idx_content)
            print(f"Updated: {binary_search_index_path} with 2D Matrix link")


    update_algo_index(kb_root) # Update main algo index

    print("Script finished.")

