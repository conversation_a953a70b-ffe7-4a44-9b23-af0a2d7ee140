
import os
import textwrap

# Helper function to create directories if they don't exist
def ensure_dir(directory):
    os.makedirs(directory, exist_ok=True)

# --- Content Generation Functions ---

def create_tree_problem_patterns_index(path):
    # Based on "二叉树算法习题汇总/本章导读.md"
    # Source file path for annotation
    source_file_path = "labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树算法习题汇总/本章导读.md"

    content = r"""---
tags: [index, concept/algorithms, concept/tree_traversal, concept/problem_solving_pattern, course/labuladong]
aliases: [Binary Tree Problem Patterns Index, 二叉树算法习题模式索引, Labuladong Tree Problem Frameworks]
---

> [!NOTE] Source Annotation
> This index is based on the structure and intent of Labuladong's "二叉树算法习题汇总" chapter introduction.
> Original source: [[{source_file_path}|本章导读 - 二叉树算法习题汇总]]

# Binary Tree Problem-Solving Patterns (Labuladong Inspired)

This section aggregates various problem-solving patterns and thinking modes for tackling binary tree algorithm questions, drawing heavily from Labuladong's "二叉树算法习题汇总" collection. The focus is on understanding how to apply core traversal and decomposition strategies to a wide range of tree problems.

Mastering these patterns, particularly the distinction between "traversal" thinking and "decomposition" (divide and conquer) thinking as applied to binary trees, can significantly enhance one's ability to solve tree-based algorithmic challenges efficiently.

## Core Thinking Modes & Traversal Positions

These notes explore how to leverage different parts of the standard tree traversal framework ([[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|recursive DFS]]) and specific thinking modes ([[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Core Algorithmic Principles]]) to solve problems.

-   **Traversal Thinking Patterns (遍历思维模式):**
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/01 - Tree Problems - Traversal Thinking Part 1|Traversal Thinking Part 1: Path-based Problems]]
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/02 - Tree Problems - Traversal Thinking Part 2|Traversal Thinking Part 2: Node-based Operations]]
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/03 - Tree Problems - Traversal Thinking Part 3|Traversal Thinking Part 3: Advanced Traversal Applications]]

-   **Decomposition Thinking Patterns (分解问题思维模式 / Divide & Conquer):**
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/04 - Tree Problems - Decomposition Thinking Part 1|Decomposition Thinking Part 1: Tree Construction]]
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/05 - Tree Problems - Decomposition Thinking Part 2|Decomposition Thinking Part 2: Symmetric & Structural Problems]]

-   **Leveraging Post-Order Traversal (后序位置的妙用):**
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/06 - Tree Problems - Post-Order Solutions Part 1|Post-Order Solutions Part 1: Basic Aggregations & Properties]]
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/07 - Tree Problems - Post-Order Solutions Part 2|Post-Order Solutions Part 2: Complex Dependencies]]
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/08 - Tree Problems - Post-Order Solutions Part 3|Post-Order Solutions Part 3: Advanced Applications]]

-   **Binary Search Tree (BST) Specific Patterns (二叉搜索树特性应用):**
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/09 - BST Problems - Properties and Iteration Part 1|BST Problems Part 1: Leveraging In-order Traversal & Properties]]
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/10 - BST Problems - Modification and Construction Part 2|BST Problems Part 2: Modification & Construction]]

-   **Level-Order Traversal (BFS) Patterns (层序遍历应用):**
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/11 - Tree Problems - Level-Order (BFS) Part 1|Level-Order (BFS) Solutions Part 1: Level-based Views]]
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/12 - Tree Problems - Level-Order (BFS) Part 2|Level-Order (BFS) Solutions Part 2: Width & Connectivity]]

-   **Combining Thinking Modes (综合运用):**
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/13 - Tree Problems - Combining Traversal and Decomposition|Combining Traversal and Decomposition]]

## Visualization of Pattern Categories

```mermaid
graph TD
    Root["Binary Tree Problem Patterns"]

    subgraph "Traversal (DFS-like)"
        direction LR
        T1["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/01 - Tree Problems - Traversal Thinking Part 1|Paths]]"]
        T2["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/02 - Tree Problems - Traversal Thinking Part 2|Node Ops]]"]
        T3["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/03 - Tree Problems - Traversal Thinking Part 3|Advanced Apps]]"]
    end

    subgraph "Decomposition (D&C)"
        direction LR
        D1["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/04 - Tree Problems - Decomposition Thinking Part 1|Construction]]"]
        D2["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/05 - Tree Problems - Decomposition Thinking Part 2|Structure]]"]
    end
    
    subgraph "Post-Order Magic"
        direction LR
        P1["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/06 - Tree Problems - Post-Order Solutions Part 1|Aggregation]]"]
        P2["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/07 - Tree Problems - Post-Order Solutions Part 2|Dependencies]]"]
        P3["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/08 - Tree Problems - Post-Order Solutions Part 3|Advanced]]"]
    end

    subgraph "BST Specifics"
        direction LR
        BST1["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/09 - BST Problems - Properties and Iteration Part 1|Properties]]"]
        BST2["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/10 - BST Problems - Modification and Construction Part 2|Modification]]"]
    end

    subgraph "Level-Order (BFS)"
        direction LR
        L1["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/11 - Tree Problems - Level-Order (BFS) Part 1|Level Views]]"]
        L2["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/12 - Tree Problems - Level-Order (BFS) Part 2|Width/Connect]]"]
    end
    
    Root --> TravThinking["Traversal Thinking"]
    TravThinking --> T1
    TravThinking --> T2
    TravThinking --> T3

    Root --> DecompThinking["Decomposition Thinking"]
    DecompThinking --> D1
    DecompThinking --> D2

    Root --> PostOrder["Post-Order Applications"]
    PostOrder --> P1
    PostOrder --> P2
    PostOrder --> P3
    
    Root --> BSTSpecifics["BST Patterns"]
    BSTSpecifics --> BST1
    BSTSpecifics --> BST2

    Root --> LevelOrderBFS["Level-Order (BFS) Patterns"]
    LevelOrderBFS --> L1
    LevelOrderBFS --> L2

    Root --> Combined["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/13 - Tree Problems - Combining Traversal and Decomposition|Combining Modes]]"]

    classDef main fill:#cde4ff,stroke:#5a9ae5;
    classDef category fill:#e6ffe6,stroke:#006400;
    class Root main;
    class TravThinking, DecompThinking, PostOrder, BSTSpecifics, LevelOrderBFS, Combined category;
```

By studying these patterns, one can develop a more systematic approach to solving a wide variety of binary tree problems.

---
Parent: [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal Algorithms]]
"""
    folder_path = os.path.join(path, "Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns")
    ensure_dir(folder_path)
    with open(os.path.join(folder_path, "index.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Created: {os.path.join(folder_path, 'index.md')}")

# --- Traversal Thinking Patterns ---
def create_traversal_thinking_part1_note(path):
    source_file_path = "labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树算法习题汇总/【练习】用「遍历」思维解题 I.md"
    content = r"""---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, pattern/tree_problem, course/labuladong]
aliases: [Tree Path Problems DFS, DFS Traversal for Paths, 二叉树遍历思维应用一]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's framework for solving tree problems using "traversal thinking", particularly for path-related tasks.
> Original source context: [[{source_file_path}|【练习】用「遍历」思维解题 I]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/02 - Tree Problems - Traversal Thinking Part 2|Next: Traversal Thinking Part 2]] |

# Tree Problems: Traversal Thinking Part 1 - Path-based Problems

The "traversal" thinking mode, as outlined in [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]], involves using a standard tree traversal (usually DFS) and injecting logic at pre-order, in-order, or post-order positions. This part focuses on applying this to problems involving paths in a binary tree, such as finding specific paths or path properties.

## 核心概念 (Core Concept)
Many path-related problems require accumulating information along a path from the root to a node (often a leaf). This is naturally handled by passing a "current path" data structure (e.g., a list) down the recursion or by maintaining it as an instance variable that is updated during the DFS traversal. [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking]] is often essential: when a choice (going down a branch) is made, the path is updated; after the recursive call for that branch returns, the choice is undone to explore other branches.

## 通用模板 (General Template - Path Sum Example)
Let's consider a common problem: finding all root-to-leaf paths that sum to a target value (similar to LeetCode 113. Path Sum II).

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionPathSum:
    def __init__(self):
        self.result_paths = []
        self.current_path = []

    def find_paths(self, root: TreeNode, target_sum: int) -> list[list[int]]:
        self.result_paths = []
        self.current_path = []
        self._traverse(root, target_sum)
        return self.result_paths

    def _traverse(self, node: TreeNode, remaining_sum: int):
        if not node:
            return

        # Pre-order action: Add current node to path
        self.current_path.append(node.val)
        new_remaining_sum = remaining_sum - node.val

        # Check if it's a leaf node and if the path sum matches
        if not node.left and not node.right: # Leaf node
            if new_remaining_sum == 0:
                # Found a valid path, add a copy to results
                self.result_paths.append(list(self.current_path))
            # Backtrack will happen after this block
        else:
            # Continue traversal if not a leaf
            if node.left:
                self._traverse(node.left, new_remaining_sum)
            if node.right:
                self._traverse(node.right, new_remaining_sum)

        # Post-order action (Backtrack): Remove current node from path
        self.current_path.pop()

# Example usage:
# root = TreeNode(5, TreeNode(4, TreeNode(11, TreeNode(7), TreeNode(2))), TreeNode(8, TreeNode(13), TreeNode(4, TreeNode(5), TreeNode(1))))
# solver = SolutionPathSum()
# paths = solver.find_paths(root, 22)
# print(paths) # Expected: [[5, 4, 11, 2], [5, 8, 4, 5]]
```

### 模板解析 (Template Explanation)
1.  **State Variables:**
    *   `result_paths`: Stores all valid paths found.
    *   `current_path`: Stores the nodes in the path currently being explored.
    *   `remaining_sum`: The sum still needed to reach the target.
2.  **Base Case (`_traverse`):** If `node` is `None`, stop this branch.
3.  **Pre-order Logic:**
    *   Add `node.val` to `current_path`.
    *   Update `remaining_sum`.
4.  **Leaf Node Check:**
    *   If `node` is a leaf and `new_remaining_sum == 0`, a valid path is found. Add a *copy* of `current_path` to `result_paths`.
5.  **Recursive Calls:**
    *   Recursively call `_traverse` for left and right children.
6.  **Post-order Logic (Backtracking):**
    *   After returning from recursive calls for children (i.e., after exploring subtrees rooted at `node.left` and `node.right`), remove `node.val` from `current_path` using `pop()`. This "undoes" the choice of including `node` in the path, allowing exploration of other paths correctly.

## 示例图示 (Diagram Example: Path Sum)
Consider finding paths summing to 8 in a simple tree:
```
    5
   / \
  4   3
 /   /
1   2
 \
  -1
```
Target Sum = 8

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\small},
    pathtracker/.style={rectangle, draw, fill=yellow!20, font=\sffamily\tiny, align=left, text width=3.5cm},
    foundpath/.style={treenode, fill=green!30, double},
    level 1/.style={sibling distance=2.5cm},
    level 2/.style={sibling distance=1.5cm},
    level 3/.style={sibling distance=1cm}
]
\node[treenode] (n5) at (0,0) {5};
\node[treenode] (n4) at (-1.5,-1.5) {4};
\node[treenode] (n3) at (1.5,-1.5) {3};
\node[treenode] (n1) at (-2.25,-3) {1};
\node[treenode] (n2) at (0.75,-3) {2};
\node[treenode] (n_1) at (-2.25,-4.5) {-1};

\draw (n5) -- (n4); \draw (n5) -- (n3);
\draw (n4) -- (n1); \draw (n3) -- (n2);
\draw (n1) -- (n_1);

% Path tracking (conceptual)
\node[pathtracker] at (4,0) {
    Path: [5], Sum: 5, Rem: 3\\
    $\rightarrow$ Path: [5,4], Sum: 9, Rem: -1 (Too much)\\
    $\rightarrow$ Path: [5,4,1], Sum: 10, Rem: -2\\
    $\rightarrow$ Path: [5,4,1,-1], Sum: 9, Rem: -1 (Leaf, Sum!=8)
};
\node[pathtracker, fill=green!20] at (4,-3) {
    Backtrack to 5, try right child 3\\
    Path: [5,3], Sum: 8, Rem: 0\\
    $\rightarrow$ Path: [5,3,2], Sum: 10, Rem: -2 (Leaf, Sum!=8)
};
\node at (4,-4.5) {Actually, the sum is checked at leaf. For path [5,3], sum is 8. Is (3) a leaf? No.
Recurse for (2). Path [5,3,2], leaf. Sum is 10, not 8.
This example highlights that path sum is checked at leaves. If [5,3] was a path where node 3 was a leaf and sum = 8, then it would be a solution.
};

\end{tikzpicture}
```
The key is the "add to path -> recurse -> remove from path" sequence, which is characteristic of backtracking implemented with DFS.

## 总结 (Summary)
- "Traversal thinking" applied to path problems often involves maintaining the current path and updating it during DFS.
- Backtracking (undoing choices by removing elements from the path) is crucial after exploring a branch.
- This pattern is effective for problems like finding all paths with a certain sum, finding the path with max/min sum, or checking existence of specific paths.
- The logic for checking the condition (e.g., sum matches target) is usually done at leaf nodes or when a path segment is completed.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/02 - Tree Problems - Traversal Thinking Part 2|Next: Traversal Thinking Part 2]] |
"""
    folder_path = os.path.join(path, "Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns")
    ensure_dir(folder_path)
    with open(os.path.join(folder_path, "01 - Tree Problems - Traversal Thinking Part 1.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Created: {os.path.join(folder_path, '01 - Tree Problems - Traversal Thinking Part 1.md')}")

def create_traversal_thinking_part2_note(path):
    source_file_path = "labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树算法习题汇总/【练习】用「遍历」思维解题 II.md"
    content = r"""---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, pattern/tree_problem, course/labuladong]
aliases: [Tree Node Operations DFS, DFS Traversal for Node Properties, 二叉树遍历思维应用二]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's framework for solving tree problems using "traversal thinking", specifically for operations or checks on individual nodes.
> Original source context: [[{source_file_path}|【练习】用「遍历」思维解题 II]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/01 - Tree Problems - Traversal Thinking Part 1|Prev: Traversal Thinking Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/03 - Tree Problems - Traversal Thinking Part 3|Next: Traversal Thinking Part 3]] |

# Tree Problems: Traversal Thinking Part 2 - Node-based Operations

This part continues exploring the "traversal" thinking mode for binary tree problems. Here, the focus is on performing operations or calculations related to individual nodes, often accumulating a global result or modifying nodes based on some criteria during a DFS traversal.

## 核心概念 (Core Concept)
When a problem requires examining each node and possibly updating a global state or the node itself based on its properties or properties passed down from its ancestors, the traversal framework is ideal. The logic is placed in the pre-order, in-order, or post-order position depending on when the node's processing should occur relative to its children.

## 通用模板 (General Template - Counting Nodes Satisfying a Condition)
Let's consider counting nodes in a BST that fall within a given range `[low, high]` (similar to LeetCode 938. Range Sum of BST, but counting instead of summing).

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionCountNodesInRange:
    def __init__(self):
        self.count = 0

    def count_nodes_in_range(self, root: TreeNode, low: int, high: int) -> int:
        self.count = 0 # Reset for multiple calls if object is reused
        self._traverse(root, low, high)
        return self.count

    def _traverse(self, node: TreeNode, low: int, high: int):
        if not node:
            return

        # Pre-order, in-order, or post-order?
        # For simple counting or summing where order doesn't strictly matter
        # for the correctness of the check itself, any order works.
        # However, for BSTs, leveraging the BST property can optimize.

        # Pre-order check:
        if low <= node.val <= high:
            self.count += 1
        
        # If it's a BST, we can prune branches:
        if node.val > low: # Or just node.val >= low if range is inclusive like low=node.val
            self._traverse(node.left, low, high) # Only go left if current node.val is not too small
        
        if node.val < high: # Or just node.val <= high if range is inclusive
            self._traverse(node.right, low, high) # Only go right if current node.val is not too large

# Example Usage (BST):
# root = TreeNode(10, TreeNode(5, TreeNode(3), TreeNode(7)), TreeNode(15, None, TreeNode(18)))
# solver = SolutionCountNodesInRange()
# node_count = solver.count_nodes_in_range(root, 7, 15) 
# print(node_count) # Expected: Nodes 7, 10, 15. Count = 3.

```
### 模板解析 (Template Explanation - for BST example)
1.  **Global Accumulator:** `self.count` stores the total count of nodes satisfying the condition.
2.  **Base Case:** If `node` is `None`, return.
3.  **Node Processing (Pre-order example):**
    *   Check if `node.val` is within the `[low, high]` range. If yes, increment `self.count`.
4.  **Optimized Recursive Calls (for BST):**
    *   If `node.val > low` (or `node.val >= low`), it's possible the left subtree contains nodes in range, so recurse left. If `node.val < low`, the entire left subtree is too small and can be skipped.
    *   If `node.val < high` (or `node.val <= high`), it's possible the right subtree contains nodes in range, so recurse right. If `node.val > high`, the entire right subtree is too large.
    *   For a non-BST tree, you'd unconditionally recurse on `node.left` and `node.right`.

The key is that "traversal thinking" involves a function that primarily *explores* the tree. The result is often accumulated outside the recursive function's direct return value (e.g., instance/global variable) or by modifying nodes directly.

## 示例图示 (Diagram Example: Invert Binary Tree - LC226)
LeetCode 226. Invert Binary Tree is a good example of node-based operation using traversal.
```python
class SolutionInvertTree:
    def invertTree(self, root: TreeNode) -> TreeNode:
        self._traverse_invert(root)
        return root

    def _traverse_invert(self, node: TreeNode):
        if not node:
            return

        # Pre-order, in-order, or post-order for swapping children?
        # Let's analyze:
        # Pre-order: Swap children, then recurse. The recursion will operate on already swapped subtrees.
        # Post-order: Recurse on children (inverting them), then swap the (now inverted) children.
        # In-order: Recurse left, swap, recurse right. Original right child becomes new left child for recursion.
        
        # All three could work with careful thought. Post-order is often intuitive:
        # "Invert my left child, invert my right child, then swap them."
        
        # Pre-order approach:
        # 1. Swap children of current node
        node.left, node.right = node.right, node.left
        # 2. Recurse on the (new) left child
        self._traverse_invert(node.left)
        # 3. Recurse on the (new) right child
        self._traverse_invert(node.right)

        # Post-order approach:
        # self._traverse_invert(node.left)
        # self._traverse_invert(node.right)
        # node.left, node.right = node.right, node.left 
        # Both yield correct result for simple inversion.
```
**Visualization (Inverting children at each node):**
Original: `A -> (B, C)`
Post-order step at A (after B, C subtrees are inverted):
- `A.left` (orig B), `A.right` (orig C)
- Swap them: `A.left` becomes (inverted) C, `A.right` becomes (inverted) B.
Result: `A -> (InvC, InvB)`

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\small},
    level 1/.style={sibling distance=2cm},
    level 2/.style={sibling distance=1.5cm}
]
\node[treenode] (A) {A};
\node[treenode, below left=1cm and 0.5cm of A] (B) {B};
\node[treenode, below right=1cm and 0.5cm of A] (C) {C};
\node[treenode, below left=1cm and 0.25cm of B] (D) {D};
\node[treenode, below right=1cm and 0.25cm of B] (E) {E};

\draw (A) -- (B); \draw (A) -- (C);
\draw (B) -- (D); \draw (B) -- (E);

\node at (3, -1.5) {Original Tree};

\begin{scope}[xshift=6cm]
    \node[treenode] (A2) {A};
    \node[treenode, below left=1cm and 0.5cm of A2] (C2) {C}; % Swapped
    \node[treenode, below right=1cm and 0.5cm of A2] (B2) {B}; % Swapped
    \node[treenode, below left=1cm and 0.25cm of B2] (E2) {E}; % Swapped
    \node[treenode, below right=1cm and 0.25cm of B2] (D2) {D}; % Swapped

    \draw (A2) -- (C2); \draw (A2) -- (B2);
    \draw (B2) -- (E2); \draw (B2) -- (D2); % Original B's children are D,E. Now B2's children are E2,D2.
    \node at (3, -1.5) {Inverted Tree};
\end{scope}
\end{tikzpicture}
```
The traversal visits each node. At each node (say, in pre-order), it performs the swap of its direct children. Then it recursively calls for the (new) left and right children.

## 总结 (Summary)
- "Traversal thinking" for node-based operations means designing a DFS function that visits nodes and performs actions based on the node's data or parameters passed down.
- Results are often accumulated in global/instance variables, or nodes are modified in-place.
- The choice of pre-order, in-order, or post-order logic depends on whether a node's processing needs to happen before, between, or after its children are processed.
- For BSTs, traversal can be optimized by pruning branches that cannot satisfy the condition.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/01 - Tree Problems - Traversal Thinking Part 1|Prev: Traversal Thinking Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/03 - Tree Problems - Traversal Thinking Part 3|Next: Traversal Thinking Part 3]] |
"""
    folder_path = os.path.join(path, "Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns")
    ensure_dir(folder_path)
    with open(os.path.join(folder_path, "02 - Tree Problems - Traversal Thinking Part 2.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Created: {os.path.join(folder_path, '02 - Tree Problems - Traversal Thinking Part 2.md')}")

def create_traversal_thinking_part3_note(path):
    source_file_path = "labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树算法习题汇总/【练习】用「遍历」思维解题 III.md"
    content = r"""---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, pattern/tree_problem, course/labuladong]
aliases: [Advanced Tree Traversal, DFS with Extra Logic, 二叉树遍历思维应用三]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's framework for solving tree problems using "traversal thinking", particularly when combined with other algorithmic techniques.
> Original source context: [[{source_file_path}|【练习】用「遍历」思维解题 III]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/02 - Tree Problems - Traversal Thinking Part 2|Prev: Traversal Thinking Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/04 - Tree Problems - Decomposition Thinking Part 1|Next: Decomposition Thinking Part 1]] |

# Tree Problems: Traversal Thinking Part 3 - Advanced Applications & Combinations

This part of "traversal thinking" explores more complex scenarios where simple DFS traversal is augmented with additional logic or data structures to solve sophisticated tree problems. Often, these problems require carrying more state during the traversal or combining traversal insights with other algorithmic patterns.

## 核心概念 (Core Concept)
The "traversal" framework ([[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|recursive DFS]]) remains the backbone. However, the actions performed at pre-order, in-order, or post-order positions become more involved. This might include:
-   Maintaining complex state variables passed through recursive calls.
-   Using auxiliary data structures (e.g., hash maps) updated during traversal.
-   Combining traversal with insights from other areas like [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|dynamic programming]] or [[Interview/Concept/Algorithms/Greedy Algorithms/00 - Greedy Algorithm - Core Framework|greedy algorithms]] at a local node level.

## 通用模板 (General Template - Example: Construct String from Binary Tree LC606)

LeetCode 606. Construct String from Binary Tree: "You need to construct a string consists of parenthesis and integers from a binary tree with the preorder traversal way."
Example: `Input: root = [1,2,3,4] Output: "1(2(4))(3)"`
This problem naturally fits pre-order traversal thinking.

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionConstructString:
    def tree2str(self, root: TreeNode) -> str:
        if not root:
            return ""

        # Pre-order: current node's value
        result_str = str(root.val)

        # Handle left child
        if root.left or root.right: # Need () for left child if right child exists or left exists
            result_str += "(" + self.tree2str(root.left) + ")"
        
        # Handle right child
        # Only add () for right child if it exists
        if root.right:
            result_str += "(" + self.tree2str(root.right) + ")"
            
        return result_str

# Example usage:
# root = TreeNode(1, TreeNode(2, TreeNode(4)), TreeNode(3))
# solver = SolutionConstructString()
# print(solver.tree2str(root)) # Output: "1(2(4))(3)"

# root = TreeNode(1, TreeNode(2, None, TreeNode(4)), TreeNode(3))
# print(solver.tree2str(root)) # Output: "1(2()(4))(3)" (LC606 specific rule: omit () for null left if right exists is tricky, this handles general case)
# LeetCode 606 has specific rules about omitting empty parentheses "()" for null children.
# The rule: "You need to omit all the empty parenthesis pairs that don't affect the one-to-one mapping relationship between the string and the original binary tree."
# - If a node has a left child but no right child, the right child's () can be omitted.
# - If a node has a right child but no left child, the left child's () `MUST` be included as "()" to show the right child belongs to current root.

# Refined LC606 solution using traversal thinking:
class SolutionLC606:
    def tree2str(self, root: TreeNode) -> str:
        res = []
        self._dfs_construct(root, res)
        return "".join(res)

    def _dfs_construct(self, node: TreeNode, res: list[str]):
        if not node:
            return

        res.append(str(node.val))

        if not node.left and not node.right: # Leaf node
            return

        # Left child
        res.append("(")
        self._dfs_construct(node.left, res)
        res.append(")")

        # Right child - only if it exists
        if node.right:
            res.append("(")
            self._dfs_construct(node.right, res)
            res.append(")")

# This second _dfs_construct still needs refinement for LC606's specific empty () rules.
# The first recursive `tree2str` is closer to a decomposition mindset.
# A pure traversal for LC606 would build a string list:

class SolutionLC606_Traversal:
    def tree2str(self, root: TreeNode) -> str:
        if not root: return ""
        
        # Current node (Pre-order position)
        s = str(root.val)
        
        # Left child
        if root.left or root.right: # Need parentheses for left if either child exists
            s += "(" + self.tree2str(root.left) + ")"
        
        # Right child
        if root.right: # Need parentheses for right only if right itself exists
            s += "(" + self.tree2str(root.right) + ")"
            
        return s
# The LC606 example is subtle and actually better solved with pure decomposition.
# The "traversal thinking" here applies if we imagine a string builder being passed around.
```
This example demonstrates that a problem might seem like simple traversal but the rules for constructing the output (handling `null` children specifically) can make it lean more towards decomposition logic for clarity. The key is that "traversal thinking" focuses on "what to do at *this* node" using information available *at* this node or passed down.

## 示例图示 (Diagram Example: Max Path Sum - LC124)
LeetCode 124. Binary Tree Maximum Path Sum. This problem is more complex and typically solved using a modified post-order traversal (which blends into decomposition thinking, as the function returns useful info from subtrees).

A true "traversal thinking" approach for a complex problem might involve:
- A DFS function `traverse(node, current_state_params...)`.
- `current_state_params` could include things like `max_sum_ending_at_parent_going_downwards`.
- A global variable `max_overall_path_sum` is updated whenever a new, larger path sum is discovered.

The processing at each node would involve:
1.  Considering paths that pass through the current node.
2.  Deciding whether to extend a path from a parent or start a new path at this node.
3.  Updating `max_overall_path_sum`.
4.  Passing relevant information down to children (e.g., "what's the max path sum ending at *you* if you connect to me?").

This can become very intricate with pure traversal thinking if the subproblem definition isn't clear. For LC124, the common solution defines a recursive helper that returns the "max path sum starting at this node and going downwards". This is fundamentally a decomposition approach.

## 总结 (Summary)
- Advanced applications of "traversal thinking" involve managing more complex state during DFS or combining traversal with other algorithmic ideas.
- The core remains visiting nodes and performing actions at pre/in/post-order positions.
- For highly complex problems where optimal substructure is clear, "decomposition thinking" (often leading to DP on trees) might be more structured, where the recursive function is defined by what it *returns* for a subtree, rather than primarily by side effects.
- Many hard tree problems require a blend: a traversal framework where the recursive calls themselves are designed with a decomposition mindset (i.e., they return meaningful values about their subtrees). This is the essence of [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]].

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/02 - Tree Problems - Traversal Thinking Part 2|Prev: Traversal Thinking Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/04 - Tree Problems - Decomposition Thinking Part 1|Next: Decomposition Thinking Part 1]] |
"""
    folder_path = os.path.join(path, "Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns")
    ensure_dir(folder_path)
    with open(os.path.join(folder_path, "03 - Tree Problems - Traversal Thinking Part 3.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Created: {os.path.join(folder_path, '03 - Tree Problems - Traversal Thinking Part 3.md')}")

# --- Decomposition Thinking Patterns ---
def create_decomposition_thinking_part1_note(path):
    source_file_path = "labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树算法习题汇总/【练习】用「分解问题」思维解题 I.md"
    content = r"""---
tags: [concept/algorithms, concept/tree_traversal, concept/recursion, concept/divide_and_conquer, pattern/tree_problem, course/labuladong]
aliases: [Tree Decomposition Thinking, Divide and Conquer Trees, 二叉树分解问题思维应用一, Tree Construction]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's framework for solving tree problems using "decomposition thinking" (divide and conquer), particularly for tree construction tasks.
> Original source context: [[{source_file_path}|【练习】用「分解问题」思维解题 I]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/03 - Tree Problems - Traversal Thinking Part 3|Prev: Traversal Thinking Part 3]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/05 - Tree Problems - Decomposition Thinking Part 2|Next: Decomposition Thinking Part 2]] |

# Tree Problems: Decomposition Thinking Part 1 - Tree Construction

"Decomposition thinking," also known as the divide and conquer approach for trees ([[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]]), is particularly powerful for problems involving the construction or reconstruction of binary trees. The core idea is to define what a recursive function should *return* for a given subproblem (e.g., "build and return the root of the subtree for this segment of an array").

## 核心概念 (Core Concept)
The main principle is to identify how a larger tree can be constructed if its left and right subtrees are already constructed. The recursive function typically takes parameters defining the scope of the current subproblem (e.g., a sub-array or indices into pre-order/in-order traversals) and returns the root of the constructed subtree for that scope.

## 通用模板 (General Template - Constructing from Preorder and Inorder Traversals)
A classic example is LeetCode 105. Construct Binary Tree from Preorder and Inorder Traversal.
Given `preorder = [3,9,20,15,7]` and `inorder = [9,3,15,20,7]`.

**Recursive Function Definition:** `build(preorder_sub, inorder_sub)` returns the root of the tree constructed from these sub-traversals.

1.  **Base Case:** If `preorder_sub` or `inorder_sub` is empty, return `None` (no tree to build).
2.  **Find Root:** The first element of `preorder_sub` is always the root of the current subtree. Let this be `root_val`.
3.  **Create Root Node:** `root_node = TreeNode(root_val)`.
4.  **Partition Inorder Traversal:** Find `root_val` in `inorder_sub`. This value divides `inorder_sub` into:
    *   `inorder_left_sub`: Elements to the left of `root_val` (all in the left subtree).
    *   `inorder_right_sub`: Elements to the right of `root_val` (all in the right subtree).
5.  **Partition Preorder Traversal:** Based on the sizes of `inorder_left_sub` and `inorder_right_sub`:
    *   `preorder_left_sub`: The next `len(inorder_left_sub)` elements in `preorder_sub` (after the initial `root_val`).
    *   `preorder_right_sub`: The remaining elements in `preorder_sub`.
6.  **Recursive Construction (Conquer & Combine):**
    *   `root_node.left = build(preorder_left_sub, inorder_left_sub)`
    *   `root_node.right = build(preorder_right_sub, inorder_right_sub)`
7.  **Return `root_node`**.

This logic naturally occurs in the **post-order position** of the conceptual `build` function's execution: the left and right children are fully constructed *before* they are assigned to the current `root_node`.

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionConstructFromPreIn:
    def buildTree(self, preorder: list[int], inorder: list[int]) -> TreeNode | None:
        if not preorder or not inorder:
            return None

        # Root is the first element in preorder traversal
        root_val = preorder[0]
        root = TreeNode(root_val)

        # Find root_val in inorder traversal to split into left/right subtrees
        # For efficiency, create a map of inorder values to their indices
        # This is usually done once at the beginning if passing indices instead of slices
        inorder_root_idx = -1
        for i in range(len(inorder)):
            if inorder[i] == root_val:
                inorder_root_idx = i
                break
        
        # Split inorder array
        inorder_left_subtree = inorder[0:inorder_root_idx]
        inorder_right_subtree = inorder[inorder_root_idx+1:]

        # Split preorder array
        # Left subtree in preorder will have len(inorder_left_subtree) elements
        # after the root element (preorder[0])
        num_left_nodes = len(inorder_left_subtree)
        preorder_left_subtree = preorder[1 : 1 + num_left_nodes]
        preorder_right_subtree = preorder[1 + num_left_nodes :]

        # Recursively build left and right subtrees
        root.left = self.buildTree(preorder_left_subtree, inorder_left_subtree)
        root.right = self.buildTree(preorder_right_subtree, inorder_right_subtree)
        
        return root

# Note: Passing slices like this creates new lists and can be inefficient (O(N^2) overall).
# A more optimized version passes indices (pointers) into the original arrays.
# See [[Interview/Practice/LeetCode/LC105 - Construct Binary Tree from Preorder and Inorder Traversal|LC105 solution]] for optimized approach.
```
The optimized approach using indices is a core part of Labuladong's teaching for these construction problems.

## 示例图示 (Diagram Example: Construction from Preorder & Inorder)
`preorder = [3,9,20,15,7]`, `inorder = [9,3,15,20,7]`

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\small},
    array_box/.style={rectangle, draw, fill=yellow!10, font=\sffamily\tiny, minimum height=0.5cm},
    call_box/.style={rectangle, draw, fill=blue!10, rounded corners, text width=4cm, align=center, font=\sffamily\scriptsize},
    level 1/.style={sibling distance=4cm, level distance=2cm},
    level 2/.style={sibling distance=2.5cm}
]

% Initial call
\node[call_box] (call_root) at (0,0) {
    `build([3,9,20,15,7], [9,3,15,20,7])`\\
    Root val = 3. Node(3) created.
};

% Left child call
\node[call_box] (call_left) at (-3,-3) {
    `build([9], [9])` (pre=[9], in=[9])\\
    Root val = 9. Node(9) created.\\
    Left/Right empty. Returns Node(9).
};

% Right child call
\node[call_box] (call_right) at (3,-3) {
    `build([20,15,7], [15,20,7])`\\
    Root val = 20. Node(20) created.
};

\draw[->] (call_root) -- (call_left) node[midway, above, sloped] {`root.left = ...`};
\draw[->] (call_root) -- (call_right) node[midway, above, sloped] {`root.right = ...`};

% Further decomposition of right child call
\node[call_box] (call_right_left) at (1,-6) {
    `build([15], [15])` (pre=[15], in=[15])\\
    Node(15) created & returned.
};
\node[call_box] (call_right_right) at (5,-6) {
    `build([7], [7])` (pre=[7], in=[7])\\
    Node(7) created & returned.
};
\draw[->] (call_right) -- (call_right_left) node[midway, above, sloped] {`Node(20).left = ...`};
\draw[->] (call_right) -- (call_right_right) node[midway, above, sloped] {`Node(20).right = ...`};


% Resulting tree structure (conceptual)
\node at (0,-8) {Resulting Tree:};
\node[treenode] (n3) at (0,-9) {3}
    child {node[treenode] (n9) {9}}
    child {node[treenode] (n20) {20}
        child {node[treenode] (n15) {15}}
        child {node[treenode] (n7) {7}}
    };
\end{tikzpicture}
```
The diagram shows the recursive calls. Each call processes its segment of preorder/inorder arrays, identifies its local root, and then delegates construction of left/right subtrees to further recursive calls. The assignment `root.left = ...` and `root.right = ...` happens after the recursive calls return, which is post-order logic.

## 总结 (Summary)
- "Decomposition thinking" is key for tree construction problems. Define a recursive function that builds and returns a subtree for a given part of the input (e.g., segments of traversal arrays).
- Identifying the root of the current subtree is usually the first step.
- The properties of different traversals (preorder, inorder, postorder) are used to partition the remaining input for recursive calls to build left and right subtrees.
- The actual linking of subtrees (`root.left = ..., root.right = ...`) happens in a post-order fashion relative to the processing of the current node's value.
- Optimizing by passing array indices instead of slicing arrays is crucial for achieving efficient $O(N)$ solutions for construction problems.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/03 - Tree Problems - Traversal Thinking Part 3|Prev: Traversal Thinking Part 3]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/05 - Tree Problems - Decomposition Thinking Part 2|Next: Decomposition Thinking Part 2]] |
"""
    folder_path = os.path.join(path, "Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns")
    ensure_dir(folder_path)
    with open(os.path.join(folder_path, "04 - Tree Problems - Decomposition Thinking Part 1.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Created: {os.path.join(folder_path, '04 - Tree Problems - Decomposition Thinking Part 1.md')}")

def create_decomposition_thinking_part2_note(path):
    source_file_path = "labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树算法习题汇总/【练习】用「分解问题」思维解题 II.md"
    content = r"""---
tags: [concept/algorithms, concept/tree_traversal, concept/recursion, concept/divide_and_conquer, pattern/tree_problem, course/labuladong]
aliases: [Tree Structural Problems, Symmetric Tree, Invert Tree, 二叉树分解问题思维应用二]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's framework for solving tree problems using "decomposition thinking," focusing on structural or symmetric properties.
> Original source context: [[{source_file_path}|【练习】用「分解问题」思维解题 II]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/04 - Tree Problems - Decomposition Thinking Part 1|Prev: Decomposition Thinking Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/06 - Tree Problems - Post-Order Solutions Part 1|Next: Post-Order Solutions Part 1]] |

# Tree Problems: Decomposition Thinking Part 2 - Symmetric & Structural Problems

This part continues exploring "decomposition thinking" ([[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]]) for binary tree problems. The focus here is on problems that involve checking or manipulating the structure of the tree, often based on symmetry or relationships between subtrees.

## 核心概念 (Core Concept)
Decomposition thinking involves defining a recursive function that solves a problem for a subtree rooted at a given node, typically by:
1.  Solving the problem for its left and right children (recursive calls).
2.  Combining the results from the children with the current node's properties to solve the problem for the current node's subtree.
This inherently uses post-order logic for the combination step.

## 通用模板 (General Template - Symmetric Tree Example)
A classic example is LeetCode 101. Symmetric Tree, which asks if a tree is a mirror image of itself.

**Recursive Function Definition:** `is_mirror(node1, node2)`
This helper function checks if the subtree rooted at `node1` is a mirror image of the subtree rooted at `node2`.

1.  **Base Cases:**
    *   If both `node1` and `node2` are `None`, they are symmetric (empty subtrees). Return `True`.
    *   If one is `None` and the other is not, they are not symmetric. Return `False`.
    *   If `node1.val != node2.val`, they are not symmetric. Return `False`.
2.  **Recursive Step (Combine Results):**
    *   The tree is symmetric if `node1.left` is a mirror of `node2.right` AND `node1.right` is a mirror of `node2.left`.
    *   Return `is_mirror(node1.left, node2.right) and is_mirror(node1.right, node2.left)`.

The main function `isSymmetric(root)` calls `is_mirror(root.left, root.right)` if root is not None.

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionSymmetricTree:
    def isSymmetric(self, root: TreeNode) -> bool:
        if not root:
            return True
        return self._is_mirror(root.left, root.right)

    def _is_mirror(self, node1: TreeNode, node2: TreeNode) -> bool:
        # Both are None, symmetric
        if not node1 and not node2:
            return True
        # One is None, the other is not, not symmetric
        if not node1 or not node2:
            return False
        
        # Values must be equal
        if node1.val != node2.val:
            return False
            
        # Check if (node1's left subtree is mirror of node2's right subtree)
        # AND (node1's right subtree is mirror of node2's left subtree)
        return self._is_mirror(node1.left, node2.right) and \
               self._is_mirror(node1.right, node2.left)

# Example Usage:
# root_sym = TreeNode(1, TreeNode(2, TreeNode(3), TreeNode(4)), TreeNode(2, TreeNode(4), TreeNode(3)))
# solver = SolutionSymmetricTree()
# print(solver.isSymmetric(root_sym)) # True

# root_asym = TreeNode(1, TreeNode(2, None, TreeNode(3)), TreeNode(2, None, TreeNode(3)))
# print(solver.isSymmetric(root_asym)) # False (2's right 3 vs 2's right 3. Should be 2's left vs 2's right)
```

### 模板解析 (Template Explanation)
-   The function `_is_mirror` is defined by what it returns: a boolean indicating if two subtrees are mirror images.
-   The solution for the current pair `(node1, node2)` is built upon the solutions for sub-pairs `(node1.left, node2.right)` and `(node1.right, node2.left)`.
-   This is a classic example of "decomposition" where the problem is broken into smaller, self-similar structural comparisons.

## 示例图示 (Diagram Example: Symmetric Tree Check)
Consider `root = [1,2,2,3,4,4,3]`
```
      1
     / \
    2   2
   / \ / \
  3  4 4  3
```
`isSymmetric(root)` calls `_is_mirror(root.left, root.right)`, i.e., `_is_mirror(Node(2)_left, Node(2)_right)`.

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\small},
    call_box/.style={rectangle, draw, fill=blue!10, rounded corners, text width=5.5cm, align=center, font=\sffamily\scriptsize},
    level 1/.style={sibling distance=3cm, level distance=1.5cm},
    level 2/.style={sibling distance=1.5cm}
]

% Tree visualization
\node[treenode] (n1) at (0,0) {1}
    child {node[treenode] (n2L) {2}
        child {node[treenode] (n3L) {3}}
        child {node[treenode] (n4L) {4}}
    }
    child {node[treenode] (n2R) {2}
        child {node[treenode] (n4R) {4}}
        child {node[treenode] (n3R) {3}}
    };

% is_mirror call visualization
\node[call_box] (call1) at (0, -4) {
    `_is_mirror(Node(2)_L, Node(2)_R)`:\\
    Values match (2==2). Check children:\\
    1. `_is_mirror(Node(2)_L.left, Node(2)_R.right)` $\rightarrow$ `_is_mirror(Node(3)_L, Node(3)_R)`\\
    2. `_is_mirror(Node(2)_L.right, Node(2)_R.left)` $\rightarrow$ `_is_mirror(Node(4)_L, Node(4)_R)`
};

\node[call_box, fill=green!20] (call_3_3) at (-3, -6.5) {
    `_is_mirror(Node(3)_L, Node(3)_R)`:\\
    Values match (3==3).\\
    Children are null, returns True.
};
\node[call_box, fill=green!20] (call_4_4) at (3, -6.5) {
    `_is_mirror(Node(4)_L, Node(4)_R)`:\\
    Values match (4==4).\\
    Children are null, returns True.
};

\draw[->, dashed] (call1) -- (call_3_3);
\draw[->, dashed] (call1) -- (call_4_4);
\node at (0, -7.5) {Both sub-calls return True $\implies$ `_is_mirror(Node(2)_L, Node(2)_R)` returns True. Overall symmetric.};

\end{tikzpicture}
```
The problem is broken down by comparing `node1.left` with `node2.right` and `node1.right` with `node2.left` recursively. This ensures the "mirror" property is checked at each level.

## 总结 (Summary)
- "Decomposition thinking" is highly effective for problems involving structural properties of trees, like symmetry or equivalence.
- The recursive function is defined to solve the problem for a given node or pair of nodes, relying on results from recursive calls on their children/sub-components.
- Base cases are crucial for terminating recursion (e.g., null nodes, value mismatches).
- The combination logic (usually in post-order) synthesizes the overall solution from subproblem solutions.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/04 - Tree Problems - Decomposition Thinking Part 1|Prev: Decomposition Thinking Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/06 - Tree Problems - Post-Order Solutions Part 1|Next: Post-Order Solutions Part 1]] |
"""
    folder_path = os.path.join(path, "Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns")
    ensure_dir(folder_path)
    with open(os.path.join(folder_path, "05 - Tree Problems - Decomposition Thinking Part 2.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Created: {os.path.join(folder_path, '05 - Tree Problems - Decomposition Thinking Part 2.md')}")

# --- Post-Order Patterns ---
def create_post_order_pattern_part1_note(path):
    source_file_path = "labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树算法习题汇总/【练习】利用后序位置解题 I.md"
    content = r"""---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, concept/recursion, pattern/tree_problem, course/labuladong]
aliases: [Post-Order Traversal Benefits, Tree Aggregation Post-Order, 二叉树后序遍历应用一]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's emphasis on leveraging the post-order traversal position for solving tree problems, particularly for basic aggregations and property calculations.
> Original source context: [[{source_file_path}|【练习】利用后序位置解题 I]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/05 - Tree Problems - Decomposition Thinking Part 2|Prev: Decomposition Thinking Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/07 - Tree Problems - Post-Order Solutions Part 2|Next: Post-Order Solutions Part 2]] |

# Tree Problems: Leveraging Post-Order Traversal Part 1 - Basic Aggregations & Properties

The post-order position in a Depth-First Search (DFS) traversal of a binary tree ([[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Recursive DFS]]) is uniquely powerful. Code in the post-order position executes *after* both the left and right subtrees have been fully processed. This means that when processing a node `curr` in its post-order slot, the results or properties of `curr.left` and `curr.right` subtrees are available (typically via the return values of the recursive calls).

## 核心概念 (Core Concept)
Post-order traversal naturally fits the "decomposition" or "divide and conquer" thinking mode ([[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]]). If a problem's solution for a tree/subtree can be derived from the solutions of its left and right subtrees, post-order is the place to combine these sub-solutions.

**Why Post-Order is Special:**
When `dfs(node)` is about to execute its post-order logic for `node`:
- `dfs(node.left)` has completed and returned the result for the left subtree.
- `dfs(node.right)` has completed and returned the result for the right subtree.
- Thus, at `node`, we have all necessary information from its children to compute the result for the subtree rooted at `node`.

## 通用模板 (General Template - Calculate Tree Height/Max Depth)
A classic example is calculating the maximum depth (or height) of a binary tree (LeetCode 104. Maximum Depth of Binary Tree).

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionMaxDepth:
    def maxDepth(self, root: TreeNode) -> int:
        # Define what the recursive function returns: the depth of the subtree rooted at 'node'.
        if not root:
            return 0 # Base case: depth of an empty tree is 0

        # Recursively get depths of left and right subtrees
        left_depth = self.maxDepth(root.left)
        right_depth = self.maxDepth(root.right)

        # === Post-order position ===
        # The depth of the current tree is 1 (for the current root node)
        # plus the maximum of the depths of its left and right subtrees.
        current_depth = 1 + max(left_depth, right_depth)
        
        return current_depth

# Example Usage:
# root = TreeNode(3, TreeNode(9), TreeNode(20, TreeNode(15), TreeNode(7)))
# solver = SolutionMaxDepth()
# print(solver.maxDepth(root)) # Expected: 3
```
### 模板解析 (Template Explanation)
1.  **Function Definition:** `maxDepth(node)` is defined to return the maximum depth of the subtree rooted at `node`.
2.  **Base Case:** If `node` is `None`, its depth is 0.
3.  **Recursive Calls:** `left_depth = maxDepth(node.left)` and `right_depth = maxDepth(node.right)` compute the depths of the children's subtrees.
4.  **Post-Order Logic:** After `left_depth` and `right_depth` are known, the depth of the current `node`'s subtree is `1 + max(left_depth, right_depth)`. This calculation happens *after* the recursive calls return, hence it's in the post-order position.

## 示例图示 (Diagram Example: Max Depth Calculation)
Tree:
```
    A
   / \
  B   C
     /
    D
```
`maxDepth(A)`:
1. Calls `maxDepth(B)` and `maxDepth(C)`.
2. `maxDepth(B)`:
   - Calls `maxDepth(B.left=None)` -> returns 0.
   - Calls `maxDepth(B.right=None)` -> returns 0.
   - Post-order for B: `1 + max(0,0) = 1`. Returns 1 (depth of subtree B).
3. `maxDepth(C)`:
   - Calls `maxDepth(D)`.
     - `maxDepth(D)` calls `maxDepth(None)` (for left) and `maxDepth(None)` (for right), both return 0.
     - Post-order for D: `1 + max(0,0) = 1`. Returns 1.
   - `left_depth_C = 1` (from D). `right_depth_C = maxDepth(C.right=None)` -> returns 0.
   - Post-order for C: `1 + max(1,0) = 2`. Returns 2 (depth of subtree C).
4. Post-order for A:
   - `left_depth_A = 1` (from B).
   - `right_depth_A = 2` (from C).
   - `1 + max(1,2) = 3`. Returns 3.

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=1cm, font=\sffamily\small},
    retval/.style={font=\sffamily\tiny, blue, fill=yellow!10, draw, rounded corners},
    level 1/.style={sibling distance=3cm},
    level 2/.style={sibling distance=2cm},
]
\node[treenode] (A) at (0,0) {A};
\node[treenode] (B) at (-1.5,-1.5) {B};
\node[treenode] (C) at (1.5,-1.5) {C};
\node[treenode] (D) at (0.75,-3) {D}; % Child of C

\draw (A) -- (B); \draw (A) -- (C);
\draw (C) -- (D);

% Return values conceptually
\node[retval, above right=0.1cm of B] {returns 1};
\node[retval, above right=0.1cm of D] {returns 1};
\node[retval, above right=0.1cm of C] {returns 2};
\node[retval, above right=0.1cm of A] {returns 3};

\node at (0,-4) [text width=6cm, align=center, draw, fill=gray!10, rounded corners]
    {Post-order: Each node calculates its depth based on children's returned depths.
    D: $1+max(0,0)=1$. B: $1+max(0,0)=1$. C: $1+max(depth(D)=1, depth(null)=0)=2$. A: $1+max(depth(B)=1, depth(C)=2)=3$.};
\end{tikzpicture}
```

## 总结 (Summary)
- The post-order traversal position is ideal for problems where a node's property or the solution for its subtree depends on the already computed properties/solutions of its children's subtrees.
- This pattern naturally aligns with the "decomposition" (divide and conquer) approach to solving tree problems.
- Common applications include calculating tree height/depth, tree size, checking for balance, finding lowest common ancestor (LCA, where decisions are based on results from left/right searches), and many dynamic programming problems on trees.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/05 - Tree Problems - Decomposition Thinking Part 2|Prev: Decomposition Thinking Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/07 - Tree Problems - Post-Order Solutions Part 2|Next: Post-Order Solutions Part 2]] |
"""
    folder_path = os.path.join(path, "Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns")
    ensure_dir(folder_path)
    with open(os.path.join(folder_path, "06 - Tree Problems - Post-Order Solutions Part 1.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Created: {os.path.join(folder_path, '06 - Tree Problems - Post-Order Solutions Part 1.md')}")

def create_post_order_pattern_part2_note(path):
    source_file_path = "labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树算法习题汇总/【练习】利用后序位置解题 II.md"
    content = r"""---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, concept/recursion, pattern/tree_problem, course/labuladong]
aliases: [Advanced Post-Order, Tree DP Post-Order, 二叉树后序遍历应用二]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's emphasis on leveraging the post-order traversal position for solving tree problems, particularly those with more complex dependencies on children's states.
> Original source context: [[{source_file_path}|【练习】利用后序位置解题 II]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/06 - Tree Problems - Post-Order Solutions Part 1|Prev: Post-Order Solutions Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/08 - Tree Problems - Post-Order Solutions Part 3|Next: Post-Order Solutions Part 3]] |

# Tree Problems: Leveraging Post-Order Traversal Part 2 - Complex Dependencies

Building upon [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/06 - Tree Problems - Post-Order Solutions Part 1|basic aggregations]], the post-order position is also critical for problems where the solution for a node depends on more nuanced information or multiple pieces of data returned from its children's subtrees. This often appears in dynamic programming on trees or problems requiring sophisticated state combination.

## 核心概念 (Core Concept)
The strength of post-order processing lies in its "bottom-up" nature. When the code at a node `curr`'s post-order slot executes, `curr` has full knowledge of what its children's subtrees "decided" or "computed". This allows `curr` to make an informed decision or computation for the subtree it roots.

## 通用模板 (General Template - Example: Lowest Common Ancestor LC236)
LeetCode 236. Lowest Common Ancestor of a Binary Tree ([[Interview/Practice/LeetCode/LC236 - Lowest Common Ancestor of a Binary Tree|LC236]]) is a prime example. The function `find_lca(root, p, q)` typically returns:
- `p` if `p` is found in `root`'s subtree (and `q` is not, or `p` is an ancestor of `q`).
- `q` if `q` is found in `root`'s subtree (and `p` is not, or `q` is an ancestor of `p`).
- The LCA node if `p` and `q` are found in different subtrees of `root`.
- `None` if neither `p` nor `q` are found in `root`'s subtree.

The logic to combine these possibilities happens in post-order.
```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionLCA:
    def lowestCommonAncestor(self, root: TreeNode, p: TreeNode, q: TreeNode) -> TreeNode | None:
        # Base cases for recursion
        if not root:
            return None
        if root == p or root == q: # If root is p or q, it's a candidate
            return root

        # Recursively search in left and right subtrees
        left_result = self.lowestCommonAncestor(root.left, p, q)
        right_result = self.lowestCommonAncestor(root.right, p, q)

        # === Post-order position ===
        # Now, left_result and right_result contain information from children
        
        # Case 1: p and q are found in different subtrees of current 'root'.
        # Then, current 'root' is the LCA.
        if left_result and right_result:
            return root
        
        # Case 2: p and q are both in the left subtree (or one is root.left).
        # Then left_result is the LCA.
        if left_result:
            return left_result
            
        # Case 3: p and q are both in the right subtree (or one is root.right).
        # Then right_result is the LCA.
        if right_result:
            return right_result
            
        # Case 4: Neither p nor q are in the subtrees of current 'root'.
        return None
```
The full logic and explanation can be found at [[Interview/Concept/Algorithms/Tree Traversal/07 - Lowest Common Ancestor (LCA) Framework|LCA Framework]].

### 模板解析 (Template Explanation)
1.  **Function Definition:** `lowestCommonAncestor(root, p, q)` returns the LCA of `p` and `q` if both are present in the subtree rooted at `root`, or returns `p` if only `p` is present, `q` if only `q` is present, or `None` if neither.
2.  **Base Cases:** If `root` is `None`, or `root` itself is `p` or `q`.
3.  **Recursive Calls:** Get results from left and right children.
4.  **Post-Order Logic:** Based on `left_result` and `right_result`:
    *   If both are non-null, `root` is the LCA.
    *   If only one is non-null, that non-null result is propagated upwards (it's either `p`, `q`, or their LCA found deeper).
    *   If both are null, neither `p` nor `q` were found below.

## 示例图示 (Diagram Example: LCA)
Tree: `root = [3,5,1,6,2,0,8,null,null,7,4]`, `p=5`, `q=1`. LCA is `3`.

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=8mm, font=\sffamily\scriptsize},
    targetnode/.style={treenode, fill=yellow!30},
    lcanode/.style={treenode, fill=green!30, double},
    call/.style={font=\sffamily\tiny\bfseries, align=center},
    arrow/.style={->, dashed},
    level 1/.style={sibling distance=5cm, level distance=1.8cm},
    level 2/.style={sibling distance=2.5cm},
    level 3/.style={sibling distance=1.2cm}
]

\node[lcanode] (r3) at (0,0) {3}
    child{ node[targetnode] (n5) {5} 
        child{ node[treenode] (n6) {6} }
        child{ node[treenode] (n2) {2}
            child{ node[treenode] (n7) {7} }
            child{ node[treenode] (n4) {4} }
        }
    }
    child{ node[targetnode] (n1) {1}
        child{ node[treenode] (n0) {0} }
        child{ node[treenode] (n8) {8} }
    };

% Annotations for LCA call at root (3)
\node[call, below left=0.5cm and -0.5cm of r3] (call_left_3) {Call LCA(5, p=5, q=1)\\$\rightarrow$ Returns 5};
\node[call, below right=0.5cm and -0.5cm of r3] (call_right_3) {Call LCA(1, p=5, q=1)\\$\rightarrow$ Returns 1};

\draw[arrow, blue] (r3.south) to[bend right=20] (call_left_3.north);
\draw[arrow, blue] (r3.south) to[bend left=20] (call_right_3.north);

\node[call, below=0.5cm of call_left_3, fill=green!10, draw, rounded corners, text width=6cm] (combine_3) at (0, -4.5) {
    Post-order at Node 3:\\
    `left_result = 5` (Node 5)\\
    `right_result = 1` (Node 1)\\
    Since both are non-null, Node 3 is the LCA. Return Node 3.
};
\draw[arrow, red, thick] (call_left_3.south) -- (combine_3.north west);
\draw[arrow, red, thick] (call_right_3.south) -- (combine_3.north east);

\end{tikzpicture}
```
The decision that node `3` is the LCA is made only after the recursive calls for its children (node `5` and node `1`) have returned their findings.

## 总结 (Summary)
- Post-order traversal is extremely powerful for problems where the solution for a node needs complete information from its subtrees.
- Many "find X in tree" or "property Y of tree" problems can be elegantly solved by defining what a recursive function should return for a subtree, and then combining these results in the post-order slot.
- This often forms the basis of dynamic programming solutions on trees.
- Key questions to ask for such problems:
    1. What information do I need from my children's subtrees to solve the problem for my current subtree?
    2. How do I combine this information with my own node's data?
    3. What should my recursive function return to its parent?

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/06 - Tree Problems - Post-Order Solutions Part 1|Prev: Post-Order Solutions Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/08 - Tree Problems - Post-Order Solutions Part 3|Next: Post-Order Solutions Part 3]] |
"""
    folder_path = os.path.join(path, "Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns")
    ensure_dir(folder_path)
    with open(os.path.join(folder_path, "07 - Tree Problems - Post-Order Solutions Part 2.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Created: {os.path.join(folder_path, '07 - Tree Problems - Post-Order Solutions Part 2.md')}")

def create_post_order_pattern_part3_note(path):
    source_file_path = "labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树算法习题汇总/【练习】利用后序位置解题 III.md"
    content = r"""---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, concept/recursion, pattern/tree_problem, course/labuladong]
aliases: [Complex Post-Order, Tree DP with Parent Info, 二叉树后序遍历应用三]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's framework, illustrating advanced uses of post-order traversal where information might flow both up (from children) and down (from parent via parameters).
> Original source context: [[{source_file_path}|【练习】利用后序位置解题 III]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/07 - Tree Problems - Post-Order Solutions Part 2|Prev: Post-Order Solutions Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/09 - BST Problems - Properties and Iteration Part 1|Next: BST Problems Part 1]] |

# Tree Problems: Leveraging Post-Order Traversal Part 3 - Advanced State & Parent Interaction

This section explores advanced scenarios where post-order traversal is used, possibly in combination with information passed down from parent nodes (via function parameters) or by returning more complex state upwards. The core idea remains that decisions or computations at a node leverage fully processed information from its children.

## 核心概念 (Core Concept)
Labuladong highlights that some difficult tree problems require a blend:
1.  **Information from children (via return values):** This is the standard post-order strength.
2.  **Information from parent/ancestors (via parameters):** Sometimes, the decision at a node also depends on context from above.

When both are needed, the recursive function signature might become `dfs(node, parent_info...)` and it would still primarily use post-order logic to combine `parent_info` with `dfs(node.left, ...)` and `dfs(node.right, ...)`.

## 通用模板 (General Template - Example: Max Path Sum LC124)

LeetCode 124. Binary Tree Maximum Path Sum ([[Interview/Practice/LeetCode/LC124 - Binary Tree Maximum Path Sum|LC124]]) is a good example of a problem requiring sophisticated post-order logic.
A "path" can start and end at any node.

**Recursive Function Definition:** `max_gain(node)`
This helper function computes the maximum path sum starting at `node` and going downwards (into one of its subtrees, or just the node itself). It returns this "gain" to its parent.
Crucially, it also updates a global/instance variable `max_overall_sum` if a path through `node` (potentially using both left and right children, forming an "arch") is greater than the current overall maximum.

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionMaxPathSum:
    def __init__(self):
        self.max_so_far = float('-inf')

    def maxPathSum(self, root: TreeNode) -> int:
        self.max_so_far = float('-inf') # Reset for multiple calls if object is reused
        self._max_gain_from_subtree(root)
        return self.max_so_far

    def _max_gain_from_subtree(self, node: TreeNode) -> int:
        if not node:
            return 0 # Gain from a null subtree is 0

        # Recursively get max gain from left and right children
        # Important: if a child's gain is negative, we don't include it in a path *going upwards*.
        gain_left = max(self._max_gain_from_subtree(node.left), 0)
        gain_right = max(self._max_gain_from_subtree(node.right), 0)

        # === Post-order position ===
        # The price to start a new path (an "arch") with current 'node' as the highest point
        price_new_path_through_node = node.val + gain_left + gain_right
        
        # Update global maximum sum found so far
        self.max_so_far = max(self.max_so_far, price_new_path_through_node)
        
        # For recursion upwards: return the max gain from this node if it's part of a path
        # extending from its parent. A path can only go down one branch (left or right).
        return node.val + max(gain_left, gain_right)

# Example Usage:
# root = TreeNode(-10, TreeNode(9), TreeNode(20, TreeNode(15), TreeNode(7)))
# solver = SolutionMaxPathSum()
# print(solver.maxPathSum(root)) # Expected: 42 (path 15-20-7)
```
### 模板解析 (Template Explanation)
1.  **Global Maximum:** `self.max_so_far` stores the maximum path sum found anywhere in the tree.
2.  **Recursive Helper `_max_gain_from_subtree(node)`:**
    *   **Definition:** Returns the maximum sum of a path that *starts at `node` and goes downwards* into at most one of its subtrees. This is the "gain" this node can offer to its parent if the parent extends a path through it.
    *   **Base Case:** If `node` is `None`, gain is 0.
    *   **Recursive Calls:** Get `gain_left` and `gain_right`. If a child's gain is negative, we don't want to include it in a path extending upwards, so we take `max(child_gain, 0)`.
    *   **Post-Order Logic:**
        *   Calculate `price_new_path_through_node = node.val + gain_left + gain_right`. This is the sum of a path that forms an "arch" with `node` as its peak (it uses `node`, and potentially its left downward path and right downward path). This path *cannot* extend further upwards to `node`'s parent.
        *   Update `self.max_so_far = max(self.max_so_far, price_new_path_through_node)`.
        *   **Return Value:** For the parent, `node` can only contribute itself plus the *better* of its left or right downward paths. So, return `node.val + max(gain_left, gain_right)`.

## 示例图示 (Diagram Example: Max Path Sum)
Tree: `[-10, 9, 20, null, null, 15, 7]`
```
    -10
    / \
   9  20
     /  \
    15   7
```

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=8mm, font=\sffamily\small},
    callinfo/.style={font=\sffamily\tiny, align=center, text width=2.5cm, fill=yellow!10, draw, rounded corners},
    maxval/.style={font=\sffamily\bfseries\small, red},
    level 1/.style={sibling distance=3cm},
    level 2/.style={sibling distance=2cm}
]
\node[treenode] (n_10) at (0,0) {-10};
\node[treenode] (n9) at (-1.5,-1.5) {9};
\node[treenode] (n20) at (1.5,-1.5) {20};
\node[treenode] (n15) at (0.75,-3) {15};
\node[treenode] (n7) at (2.25,-3) {7};

\draw (n_10) -- (n9); \draw (n_10) -- (n20);
\draw (n20) -- (n15); \draw (n20) -- (n7);

% Annotations (conceptual trace)
\node[callinfo] at (-2.5, -3) {`_max_gain(9)`\\Returns: $9+max(0,0)=9$\\Arch sum: $9+0+0=9$\\`max_so_far` = max(-inf, 9) = 9};

\node[callinfo] at (-0.25, -4.2) {`_max_gain(15)`\\Returns: $15+max(0,0)=15$\\Arch sum: $15+0+0=15$\\`max_so_far` = max(9, 15) = 15};

\node[callinfo] at (3.25, -4.2) {`_max_gain(7)`\\Returns: $7+max(0,0)=7$\\Arch sum: $7+0+0=7$\\`max_so_far` = max(15, 7) = 15};

\node[callinfo] at (1.5, -0.75) {
    `_max_gain(20)`:
    `gain_left` (from 15) = 15
    `gain_right` (from 7) = 7
    Arch sum: $20+15+7 = 42$
    `max_so_far` = max(15, 42) = \maxval{42}
    Returns: $20+max(15,7) = 20+15 = 35$
};

\node[callinfo] at (0, 1.25) {
    `_max_gain(-10)`:
    `gain_left` (from 9) = 9
    `gain_right` (from 20) = 35
    Arch sum: $-10+9+35 = 34$
    `max_so_far` = max(42, 34) = \maxval{42}
    Returns: $-10+max(9,35) = -10+35 = 25$
};
\node at (0, -5.5) {Final `max_so_far` is \maxval{42}};
\end{tikzpicture}
```
This diagram traces how `_max_gain_from_subtree` returns values upwards while `max_so_far` captures the best "arch" path found. The post-order nature is critical: a node processes after its children have reported their maximum downward gains.

## 总结 (Summary)
- Complex tree problems solvable with post-order traversal often involve recursive helper functions that return rich state information about subtrees.
- Simultaneously, a global or instance variable might be updated to track an overall optimum that can be formed by combining information from left and right children at a common ancestor (forming an "arch" or a path that doesn't necessarily extend upwards).
- This pattern is common in "maximum path sum" type problems or other dynamic programming on trees where the optimal solution for a subtree contributes to, but is not always identical to, the part of it that propagates to the parent.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/07 - Tree Problems - Post-Order Solutions Part 2|Prev: Post-Order Solutions Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/09 - BST Problems - Properties and Iteration Part 1|Next: BST Problems Part 1]] |
"""
    folder_path = os.path.join(path, "Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns")
    ensure_dir(folder_path)
    with open(os.path.join(folder_path, "08 - Tree Problems - Post-Order Solutions Part 3.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Created: {os.path.join(folder_path, '08 - Tree Problems - Post-Order Solutions Part 3.md')}")

# --- BST Problem Patterns ---
def create_bst_problems_part1_note(path):
    source_file_path = "labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树算法习题汇总/【练习】二叉搜索树经典例题 I.md"
    content = r"""---
tags: [concept/algorithms, concept/tree_traversal, concept/bst, pattern/tree_problem, course/labuladong]
aliases: [BST Properties, In-order Traversal BST, 二叉搜索树特性应用一]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's approach to solving Binary Search Tree (BST) problems, focusing on leveraging BST properties, especially in-order traversal.
> Original source context: [[{source_file_path}|【练习】二叉搜索树经典例题 I]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/08 - Tree Problems - Post-Order Solutions Part 3|Prev: Post-Order Solutions Part 3]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/10 - BST Problems - Modification and Construction Part 2|Next: BST Problems Part 2]] |

# BST Problems: Leveraging Properties & In-Order Traversal (Part 1)

Binary Search Trees (BSTs) have unique properties that can be exploited to solve problems more efficiently than on general binary trees. The most fundamental property is that **an in-order traversal of a BST yields its elements in sorted order.** Many BST problems revolve around this or the definition: `node.left.val < node.val < node.right.val` for all nodes. See [[Interview/Concept/Data Structures/Tree/Binary Search Tree/00 - BST - Introduction and Operations|BST Introduction]].

## 核心概念 (Core Concept)
1.  **Sorted In-order Traversal:** This is the cornerstone. If a problem involves sorted data, order statistics (k-th smallest/largest), or ranges, in-order traversal is often key.
2.  **Local BST Property:** For any node, all values in its left subtree are smaller, and all values in its right subtree are larger. This allows for efficient searching and pruning of search space.

## 通用模板 (General Template - Kth Smallest Element in BST LC230)
LeetCode 230. Kth Smallest Element in a BST is a classic example.

**Approach 1: In-order traversal and store in a list**
Perform a full in-order traversal, store all elements in a list, then return the element at index `k-1`.
```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionKthSmallest_List:
    def kthSmallest(self, root: TreeNode, k: int) -> int:
        inorder_list = []
        self._inorder_traverse(root, inorder_list)
        if k > 0 and k <= len(inorder_list):
            return inorder_list[k-1]
        return -1 # Should not happen if k is valid

    def _inorder_traverse(self, node: TreeNode, lst: list):
        if not node:
            return
        self._inorder_traverse(node.left, lst)
        lst.append(node.val)
        self._inorder_traverse(node.right, lst)
```
- Time: $O(N)$ to traverse all nodes. Space: $O(N)$ for list and recursion stack.

**Approach 2: Optimized In-order traversal (Stop early)**
We can stop the in-order traversal once we've found the k-th element.
```python
class SolutionKthSmallest_Optimized:
    def __init__(self):
        self.k_val = 0
        self.count = 0
        self.result = -1

    def kthSmallest(self, root: TreeNode, k: int) -> int:
        self.k_val = k
        self.count = 0
        self.result = -1 # In case k is out of bounds or tree is empty
        self._inorder_stop_early(root)
        return self.result

    def _inorder_stop_early(self, node: TreeNode):
        if not node or self.count >= self.k_val: # Pruning if already found
            return

        # Traverse left
        self._inorder_stop_early(node.left)
        
        # Process current node (in-order position)
        if self.count < self.k_val: # Check again due to left recursion possibly finding it
            self.count += 1
            if self.count == self.k_val:
                self.result = node.val
                return # Found, can stop further traversal
        
        # Traverse right (only if not yet found)
        if self.count < self.k_val:
            self._inorder_stop_early(node.right)
```
- Time: $O(H+k)$ on average where $H$ is height, if balanced. Worst case $O(N)$. Space: $O(H)$ for recursion.

## 示例图示 (Diagram Example: Kth Smallest)
BST: `[3,1,4,null,2]`, `k=1`
In-order: `1, 2, 3, 4`. 1st smallest is `1`.

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\small},
    visitednode/.style={treenode, fill=yellow!30},
    kthnode/.style={treenode, fill=green!30, double},
    level 1/.style={sibling distance=2cm},
    level 2/.style={sibling distance=1.5cm},
    trav_note/.style={font=\sffamily\tiny, align=center, text width=3cm}
]

\node[treenode] (n3) at (0,0) {3};
\node[treenode] (n1) at (-1,-1.5) {1};
\node[treenode] (n4) at (1,-1.5) {4};
\node[treenode] (n2) at (-0.5,-3) {2}; % Child of 1

\draw (n3) -- (n1); \draw (n3) -- (n4);
\draw (n1) -- (n2); % 1's right child is 2

% Traversal Steps for k=1 (Optimized)
\node[trav_note] at (4, 0) {`_inorder(Node(3))`};
\node[trav_note, below=0.5cm of {(4,0)}] (call_1) {`_inorder(Node(1))` (left of 3)};
\node[trav_note, below=0.5cm of call_1] (call_1_left_null) {`_inorder(null)` (left of 1), returns};
\node[trav_note, below=0.5cm of call_1_left_null] (process_1) {Process Node(1) (in-order)\\`count`=0 $\rightarrow$ 1.\\`count==k` (1==1)? Yes!\\`result = 1`. Return.};

% Highlight nodes
\begin{scope}[on background layer]
    \node[visitednode, fit=(n1)] {}; % Node 1 is visited
    \node[kthnode, fit=(n1)] {}; % Node 1 is the k-th
\end{scope}

\node at (0,-4) [text width=6cm, align=center, draw, fill=gray!10, rounded corners]
    {For k=1: Traverse to Node(1). Process it. `count` becomes 1. Since `count == k`, `result = 1`. Traversal stops.};
\end{tikzpicture}
```

## 总结 (Summary)
- The sorted nature of BST in-order traversal is fundamental. Use it for problems involving order, rank, or ranges.
- The local BST property (`left < root < right`) allows efficient pruning in search-like traversals. If searching for a value `x`:
    - If `x < root.val`, go left.
    - If `x > root.val`, go right.
    - If `x == root.val`, found.
- Many "validation" problems (e.g., is this a valid BST?) also rely on checking these properties recursively or via in-order traversal to see if it produces a sorted sequence. See [[Interview/Practice/LeetCode/LC98 - Validate Binary Search Tree|LC98 - Validate BST]].

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/08 - Tree Problems - Post-Order Solutions Part 3|Prev: Post-Order Solutions Part 3]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/10 - BST Problems - Modification and Construction Part 2|Next: BST Problems Part 2]] |
"""
    folder_path = os.path.join(path, "Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns")
    ensure_dir(folder_path)
    with open(os.path.join(folder_path, "09 - BST Problems - Properties and Iteration Part 1.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Created: {os.path.join(folder_path, '09 - BST Problems - Properties and Iteration Part 1.md')}")

def create_bst_problems_part2_note(path):
    source_file_path = "labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树算法习题汇总/【练习】二叉搜索树经典例题 II.md"
    content = r"""---
tags: [concept/algorithms, concept/tree_traversal, concept/bst, pattern/tree_problem, course/labuladong]
aliases: [BST Modification, BST Construction, 二叉搜索树特性应用二]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's approach to solving Binary Search Tree (BST) problems, focusing on modification and construction tasks that preserve BST properties.
> Original source context: [[{source_file_path}|【练习】二叉搜索树经典例题 II]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/09 - BST Problems - Properties and Iteration Part 1|Prev: BST Problems Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/11 - Tree Problems - Level-Order (BFS) Part 1|Next: Level-Order (BFS) Part 1]] |

# BST Problems: Modification & Construction (Part 2)

This part delves into Binary Search Tree (BST) problems that involve modifying an existing BST (like insertion or deletion) or constructing a BST from given data, while always maintaining the BST invariants. The core [[Interview/Concept/Data Structures/Tree/Binary Search Tree/00 - BST - Introduction and Operations|BST properties]] guide these operations.

## 核心概念 (Core Concept)
Operations that modify a BST (insert, delete) or construct one must ensure that for every node `n`, all values in `n.left` are less than `n.val`, and all values in `n.right` are greater than `n.val`. This often involves recursive "decomposition thinking."

## 通用模板 (General Template - Inserting into a BST LC701)
LeetCode 701. Insert into a Binary Search Tree is a good example. The goal is to insert a `val` into the BST and return the root of the modified BST.

**Recursive Function Definition:** `insertIntoBST(root, val)` returns the root of the (potentially modified) subtree after inserting `val`.

1.  **Base Case:** If `root` is `None`, the new `val` forms a new subtree. Return `TreeNode(val)`.
2.  **Recursive Step (Decomposition):**
    *   If `val < root.val`: The `val` belongs in the left subtree. Recursively call `insertIntoBST(root.left, val)`. The result of this call (the new root of the modified left subtree) becomes `root.left`.
    *   If `val > root.val`: The `val` belongs in the right subtree. Recursively call `insertIntoBST(root.right, val)`. The result becomes `root.right`.
    *   (If `val == root.val`, typically do nothing for BSTs if duplicates are not allowed, or handle as per problem spec. LC701 assumes unique values or doesn't require special handling for duplicates other than placing them.)
3.  **Return `root`** (the current node, possibly with a modified child pointer).

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionInsertIntoBST:
    def insertIntoBST(self, root: TreeNode | None, val: int) -> TreeNode | None:
        if not root:
            return TreeNode(val) # Base case: found the spot, create new node

        if val < root.val:
            root.left = self.insertIntoBST(root.left, val)
        elif val > root.val:
            root.right = self.insertIntoBST(root.right, val)
        # If val == root.val, do nothing (standard BST behavior for duplicates often implies no insertion or specific handling)
        # For LC701, the problem statement implies val will be inserted if it's not already there following BST rules.
        
        return root # Return the (possibly modified) root of this subtree
```
### 模板解析 (Template Explanation)
- This is a prime example of "decomposition thinking." The function `insertIntoBST(node, val)` is defined to return the root of the subtree rooted at `node` *after* `val` has been inserted into it.
- The assignments `root.left = ...` and `root.right = ...` happen in a post-order fashion (after the recursive calls return), linking the modified subtrees back to the parent.

## 示例图示 (Diagram Example: Inserting 5 into BST `[4,2,7,1,3]`)
Original BST:
```
    4
   / \
  2   7
 / \
1   3
```
Call `insertIntoBST(root, 5)`:
1. `insertIntoBST(Node(4), 5)`: `5 > 4`, so `Node(4).right = insertIntoBST(Node(7), 5)`.
2. `insertIntoBST(Node(7), 5)`: `5 < 7`, so `Node(7).left = insertIntoBST(None, 5)`.
3. `insertIntoBST(None, 5)`: Base case, returns `TreeNode(5)`.
4. Back to call 2: `Node(7).left` becomes `Node(5)`. `insertIntoBST(Node(7), 5)` returns `Node(7)` (now with left child 5).
5. Back to call 1: `Node(4).right` becomes `Node(7)` (which now has Node(5) as left child). `insertIntoBST(Node(4), 5)` returns `Node(4)`.

Resulting BST:
```
    4
   / \
  2   7
 / \ /
1  3 5
```

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\small},
    newnode/.style={treenode, fill=green!30},
    callflow/.style={font=\sffamily\tiny, align=center, text width=4cm},
    level 1/.style={sibling distance=2.5cm},
    level 2/.style={sibling distance=1.5cm},
    level 3/.style={sibling distance=1cm}
]
% Initial Tree
\node[treenode] (n4_orig) at (-3,0) {4}
    child {node[treenode] (n2_orig) {2}
        child {node[treenode] (n1_orig) {1}}
        child {node[treenode] (n3_orig) {3}}
    }
    child {node[treenode] (n7_orig) {7}};
\node at (-3, -3) {Initial BST};

% Call Flow
\node[callflow, rectangle, draw, fill=yellow!10] (cf1) at (3, 1.5) {`insert(Node(4), 5)`\\`5>4` $\rightarrow$ go right};
\node[callflow, rectangle, draw, fill=yellow!10] (cf2) at (3, 0) {`insert(Node(7), 5)`\\`5<7` $\rightarrow$ go left};
\node[callflow, rectangle, draw, fill=yellow!10] (cf3) at (3, -1.5) {`insert(None, 5)`\\Returns `Node(5)`};

\draw[->, dashed] (cf1) -- (cf2);
\draw[->, dashed] (cf2) -- (cf3);
\draw[->, dashed, blue, bend left] (cf3.west) to node[midway,above,sloped] {`Node(7).left=Node(5)`} (cf2.west);
\draw[->, dashed, blue, bend left] (cf2.east) to node[midway,above,sloped] {`Node(4).right=Node(7)`} (cf1.east);


% Resulting Tree
\node[treenode] (n4_final) at (-3,-4.5) {4}
    child {node[treenode] (n2_final) {2}
        child {node[treenode] (n1_final) {1}}
        child {node[treenode] (n3_final) {3}}
    }
    child {node[treenode] (n7_final) {7}
        child {node[newnode] (n5_final) {5}} % Inserted node
    };
\node at (-3, -7.5) {BST after inserting 5};

\end{tikzpicture}
```

## 总结 (Summary)
- Modifying or constructing BSTs using decomposition thinking involves recursive functions that operate on subtrees and return the (potentially new) root of that modified/constructed subtree.
- BST properties guide the recursion: compare the target value with the current node's value to decide whether to go left or right.
- Base cases typically involve handling `None` nodes (e.g., finding an empty spot for insertion, or returning `None` for an empty sub-construction).
- The actual modification (like linking a new node or a reconstructed subtree) often happens in a post-order fashion after recursive calls return.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/09 - BST Problems - Properties and Iteration Part 1|Prev: BST Problems Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/11 - Tree Problems - Level-Order (BFS) Part 1|Next: Level-Order (BFS) Part 1]] |
"""
    folder_path = os.path.join(path, "Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns")
    ensure_dir(folder_path)
    with open(os.path.join(folder_path, "10 - BST Problems - Modification and Construction Part 2.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Created: {os.path.join(folder_path, '10 - BST Problems - Modification and Construction Part 2.md')}")

# --- Level Order (BFS) Patterns ---
def create_level_order_pattern_part1_note(path):
    source_file_path = "labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树算法习题汇总/【练习】运用层序遍历解题 I.md"
    content = r"""---
tags: [concept/algorithms, concept/tree_traversal, concept/bfs, pattern/tree_problem, course/labuladong]
aliases: [Tree BFS Applications, Level Order Views, 二叉树层序遍历应用一]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's discussion on using level-order traversal (BFS) for binary tree problems, particularly for tasks requiring a level-by-level view.
> Original source context: [[{source_file_path}|【练习】运用层序遍历解题 I]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/10 - BST Problems - Modification and Construction Part 2|Prev: BST Problems Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/12 - Tree Problems - Level-Order (BFS) Part 2|Next: Level-Order (BFS) Part 2]] |

# Tree Problems: Using Level-Order Traversal (BFS) Part 1 - Level-based Views

While most binary tree problems can be solved recursively (DFS), some are more naturally or efficiently solved using [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Level-Order Traversal (BFS)]]. BFS is ideal when the problem involves processing the tree layer by layer, or when information about nodes at the same depth is important.

## 核心概念 (Core Concept)
Level-order traversal uses a queue to explore the tree. Nodes are visited in order of their depth: all nodes at depth `d` are visited before any nodes at depth `d+1`. The standard "Pattern 2" from Labuladong's BFS explanation (see [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Binary Tree Level-Order Traversal (BFS)]]) is key here, as it processes nodes one full level at a time.

## 通用模板 (General Template - Collecting Nodes by Level)
This is the direct application of LeetCode 102. Binary Tree Level Order Traversal.

```python
from collections import deque

class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionLevelOrder:
    def levelOrder(self, root: TreeNode | None) -> list[list[int]]:
        if not root:
            return []

        result_per_level = []
        queue = deque([root])

        while queue:
            level_size = len(queue) # Number of nodes at the current level
            current_level_nodes = []

            for _ in range(level_size): # Process all nodes at this level
                node = queue.popleft()
                current_level_nodes.append(node.val)

                if node.left:
                    queue.append(node.left)
                if node.right:
                    queue.append(node.right)
            
            result_per_level.append(current_level_nodes)
            
        return result_per_level

# Example Usage:
# root = TreeNode(3, TreeNode(9), TreeNode(20, TreeNode(15), TreeNode(7)))
# solver = SolutionLevelOrder()
# print(solver.levelOrder(root)) # Expected: [[3], [9, 20], [15, 7]]
```
### 模板解析 (Template Explanation)
1.  **Queue Initialization:** Start with the `root` in the queue.
2.  **Outer Loop:** Continues as long as there are nodes to process (`while queue`).
3.  **Level Processing:**
    *   `level_size = len(queue)`: Determines how many nodes are in the current level.
    *   `current_level_nodes = []`: To store values of nodes at this level.
    *   **Inner Loop (`for _ in range(level_size)`):** Dequeues `level_size` nodes, processes them (adds value to `current_level_nodes`), and enqueues their children for the next level.
4.  **Store Level:** After processing all nodes for the current level, `current_level_nodes` is added to `result_per_level`.

## 示例图示 (Diagram Example: Level Order Grouping)
Tree: `[3,9,20,null,null,15,7]`
```
    3
   / \
  9  20
    /  \
   15   7
```
```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\small},
    queue_state/.style={rectangle, draw, fill=yellow!20, font=\sffamily\tiny, align=center, text width=4cm},
    level_label/.style={font=\sffamily\bfseries\small, blue},
    level 1/.style={sibling distance=3cm},
    level 2/.style={sibling distance=2cm}
]

% Tree
\node[treenode] (n3) at (0,0) {3};
\node[treenode] (n9) at (-1.5,-1.5) {9};
\node[treenode] (n20) at (1.5,-1.5) {20};
\node[treenode] (n15) at (0.75,-3) {15};
\node[treenode] (n7) at (2.25,-3) {7};
\draw (n3) -- (n9); \draw (n3) -- (n20);
\draw (n20) -- (n15); \draw (n20) -- (n7);

% Queue processing
\node[level_label] at (5,0) {Level 0 Processing};
\node[queue_state] (q1) at (5,-0.75) {Queue: `deque([Node(3)])`\\`level_size=1`. Dequeue 3. Add 3 to `lvl_nodes`.\\Enqueue 9, 20. Result: `[[3]]`};

\node[level_label] at (5,-2.5) {Level 1 Processing};
\node[queue_state] (q2) at (5,-3.25) {Queue: `deque([Node(9), Node(20)])`\\`level_size=2`. Dequeue 9, then 20.\\ Add 9, 20 to `lvl_nodes`. Enqueue 15, 7.\\Result: `[[3], [9,20]]`};

\node[level_label] at (5,-5) {Level 2 Processing};
\node[queue_state] (q3) at (5,-5.75) {Queue: `deque([Node(15), Node(7)])`\\`level_size=2`. Dequeue 15, then 7.\\Add 15, 7 to `lvl_nodes`. No children.\\Result: `[[3], [9,20], [15,7]]`};

\node at (5,-7) {Queue empty. Done.};
\end{tikzpicture}
```
This iterative, level-by-level processing is characteristic of BFS and is directly applicable to problems requiring such views (e.g., finding level sums, right side view, zigzag traversal with minor modifications).

## 总结 (Summary)
- Level-order traversal (BFS) is used when problems require processing a tree layer by layer.
- The key is using a queue and, for per-level grouping, processing `len(queue)` elements in an inner loop before moving to the "next level" of enqueued children.
- This pattern forms the basis for solutions to many "view" related tree problems (e.g., right side view, level averages) and finding shortest paths in terms of levels.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/10 - BST Problems - Modification and Construction Part 2|Prev: BST Problems Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/12 - Tree Problems - Level-Order (BFS) Part 2|Next: Level-Order (BFS) Part 2]] |
"""
    folder_path = os.path.join(path, "Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns")
    ensure_dir(folder_path)
    with open(os.path.join(folder_path, "11 - Tree Problems - Level-Order (BFS) Part 1.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Created: {os.path.join(folder_path, '11 - Tree Problems - Level-Order (BFS) Part 1.md')}")

def create_level_order_pattern_part2_note(path):
    source_file_path = "labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树算法习题汇总/【练习】运用层序遍历解题 II.md"
    content = r"""---
tags: [concept/algorithms, concept/tree_traversal, concept/bfs, pattern/tree_problem, course/labuladong]
aliases: [Advanced BFS Tree, Tree Width, Connect Nodes at Same Level, 二叉树层序遍历应用二]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's discussion on advanced applications of level-order traversal (BFS) for binary tree problems.
> Original source context: [[{source_file_path}|【练习】运用层序遍历解题 II]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/11 - Tree Problems - Level-Order (BFS) Part 1|Prev: Level-Order (BFS) Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/13 - Tree Problems - Combining Traversal and Decomposition|Next: Combining Modes]] |

# Tree Problems: Using Level-Order Traversal (BFS) Part 2 - Width & Connectivity

This part explores more specialized applications of [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Level-Order Traversal (BFS)]] for binary trees, often focusing on properties related to the "width" of the tree at different levels or connecting nodes within the same level.

## 核心概念 (Core Concept)
The standard BFS template that processes nodes level by level (Pattern 2 from [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Binary Tree Level-Order Traversal (BFS)]]) is the foundation. The specific logic within the inner loop (processing nodes of a single level) is adapted to the problem.

## 通用模板 (General Template - Example: Max Width of Binary Tree LC662)
LeetCode 662. Maximum Width of Binary Tree. The width of one level is defined as the length between the end-nodes (the leftmost and rightmost non-null nodes in the level, where the null nodes between the end-nodes are also counted into the length calculation).

To solve this, we need to assign an "index" or "position" to each node as if it were in a complete binary tree.
- If a node is at `pos`, its left child is at `2*pos` and right child at `2*pos + 1` (or `2*pos-1` and `2*pos` if 1-indexed).
- During BFS, store `(node, position)` tuples in the queue.
- For each level, the width is `max_pos_in_level - min_pos_in_level + 1`.

```python
from collections import deque

class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionMaxWidth:
    def widthOfBinaryTree(self, root: TreeNode | None) -> int:
        if not root:
            return 0

        max_width = 0
        # Queue stores tuples: (node, position_index)
        # Start root at position 0 (or 1, be consistent)
        queue = deque([(root, 0)]) 

        while queue:
            level_size = len(queue)
            level_start_pos = -1 # Position of the first node in this level
            level_end_pos = -1   # Position of the last node in this level
            
            for i in range(level_size):
                node, pos = queue.popleft()

                if i == 0: # First node of the level
                    level_start_pos = pos
                if i == level_size - 1: # Last node of the level
                    level_end_pos = pos
                
                # Enqueue children with their calculated positions
                # To prevent large position numbers, we can re-index relative to level_start_pos
                # For simplicity here, using absolute positions.
                # Relative indexing: child_pos = (pos - level_start_pos) * 2 for left.
                if node.left:
                    queue.append((node.left, 2 * pos)) 
                if node.right:
                    queue.append((node.right, 2 * pos + 1))
            
            current_level_width = level_end_pos - level_start_pos + 1
            max_width = max(max_width, current_level_width)
            
        return max_width

# Example Usage:
# root = TreeNode(1, TreeNode(3, TreeNode(5), TreeNode(3)), TreeNode(2, None, TreeNode(9)))
# solver = SolutionMaxWidth()
# print(solver.widthOfBinaryTree(root)) 
# Level 0: [1] (pos 0). Width 1.
# Level 1: [3,2] (pos 0,1 if re-indexed, or 0,1 from root=0). Width (1-0)+1=2.
# Level 2: [5,3,null,9] (pos 0,1,_,3 if re-indexed, or 0,1,_,3 from root=0). Width (3-0)+1=4.
# Max width should be 4.
# Careful with indexing: If root is 0, left is 2*0=0, right is 2*0+1=1. This is for segment tree like indexing.
# For heap-like indexing if root is 1: left=2*idx, right=2*idx+1.
# If root is 0: left child at 2*idx + 1, right child at 2*idx + 2.
# Let's use the root=0, left=2p+1, right=2p+2 common for BFS level indexing
# Corrected indexing for LC662:
class SolutionMaxWidth_LC662_Corrected:
    def widthOfBinaryTree(self, root: TreeNode | None) -> int:
        if not root: return 0
        max_w = 0
        queue = deque([(root, 0)]) # (node, position_index)

        while queue:
            level_length = len(queue)
            _, level_head_index = queue[0] # Position of the first node in this level
            
            for i in range(level_length):
                curr_node, curr_idx = queue.popleft()
                
                # Calculate width if this is the last node of the level
                if i == level_length - 1:
                    max_w = max(max_w, curr_idx - level_head_index + 1)
                
                if curr_node.left:
                    queue.append((curr_node.left, 2 * (curr_idx - level_head_index) + 1)) # Relative indexing
                if curr_node.right:
                    queue.append((curr_node.right, 2 * (curr_idx - level_head_index) + 2))
            # Corrected approach: Use absolute indexing, subtract start index of level at end of level
            # This avoids issues with large index values when re-indexing.

# Simpler LC662 approach with absolute indices normalized at each level.
# Each node stores (node, its_index_in_level). The actual value of index can be large.
# At each level, width is (last_node_index - first_node_index + 1).
class SolutionMaxWidth_LC662_Normalized:
    def widthOfBinaryTree(self, root: TreeNode | None) -> int:
        if not root: return 0
        max_width = 0
        queue = deque([(root, 0)]) # (node, index) - root is at index 0

        while queue:
            level_size = len(queue)
            # The first element's index in the queue for this level becomes the "offset"
            level_start_index = queue[0][1] 
            
            first_node_idx_in_level = 0
            last_node_idx_in_level = 0

            for i in range(level_size):
                node, original_idx = queue.popleft()
                
                # Normalized index for current level processing
                current_normalized_idx = original_idx - level_start_index

                if i == 0:
                    first_node_idx_in_level = current_normalized_idx # Will be 0
                if i == level_size - 1:
                    last_node_idx_in_level = current_normalized_idx
                
                if node.left:
                    # Children's original_idx calculated from parent's original_idx
                    queue.append((node.left, 2 * original_idx + 1))
                if node.right:
                    queue.append((node.right, 2 * original_idx + 2))
            
            max_width = max(max_width, last_node_idx_in_level - first_node_idx_in_level + 1)
            
        return max_width
```

## 示例图示 (Diagram Example: Populating Next Right Pointers - LC116/117)
LeetCode 116/117. Populating Next Right Pointers in Each Node.
This problem requires connecting nodes at the same level using a `next` pointer. BFS is ideal.

```python
class Node: # Definition for LC116/117
    def __init__(self, val: int = 0, left: 'Node' = None, right: 'Node' = None, next: 'Node' = None):
        self.val = val
        self.left = left
        self.right = right
        self.next = next

class SolutionConnectNodes:
    def connect(self, root: 'Node') -> 'Node':
        if not root:
            return None
        
        queue = deque([root])
        while queue:
            level_size = len(queue)
            prev_node_in_level = None
            for i in range(level_size):
                node = queue.popleft()
                
                # Connect to previous node in the same level
                if prev_node_in_level:
                    prev_node_in_level.next = node
                
                prev_node_in_level = node # Update for the next iteration

                if node.left:
                    queue.append(node.left)
                if node.right:
                    queue.append(node.right)
            # The last node in the level will have its 'next' pointer as None (default)
        return root
```
```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\small},
    nextptr/.style={->, thick, blue, dashed, bend left=10},
    level_conn/.style={font=\sffamily\tiny, align=center, text width=5cm},
    level 1/.style={sibling distance=3cm},
    level 2/.style={sibling distance=1.5cm}
]
% Tree
\node[treenode] (n1) at (0,0) {1};
\node[treenode] (n2) at (-1.5,-1.5) {2};
\node[treenode] (n3) at (1.5,-1.5) {3};
\node[treenode] (n4) at (-2.25,-3) {4};
\node[treenode] (n5) at (-0.75,-3) {5};
\node[treenode] (n6) at (0.75,-3) {6};
\node[treenode] (n7) at (2.25,-3) {7};

\draw (n1) -- (n2); \draw (n1) -- (n3);
\draw (n2) -- (n4); \draw (n2) -- (n5);
\draw (n3) -- (n6); \draw (n3) -- (n7);

% Next pointers
\draw[nextptr] (n2.east) to node[midway, below, font=\tiny] {next} (n3.west);
\draw[nextptr] (n4.east) to node[midway, below, font=\tiny] {next} (n5.west);
\draw[nextptr] (n5.east) to node[midway, below, font=\tiny] {next} (n6.west);
\draw[nextptr] (n6.east) to node[midway, below, font=\tiny] {next} (n7.west);

% Explanation
\node[level_conn, rectangle, draw, fill=yellow!10] at (0,-4.5) {
    Level 0: Node 1. `prev_node=null`. After loop, `prev_node=1`. `1.next=null`.
    Level 1: Nodes 2, 3.
    - Process 2: `prev_node=null`. `prev_node=2`.
    - Process 3: `prev_node=2`. `2.next=3`. `prev_node=3`. `3.next=null`.
    Level 2: Nodes 4, 5, 6, 7. (Similar logic)
};
\end{tikzpicture}
```

## 总结 (Summary)
- Advanced BFS applications on trees often involve processing nodes at each level with more specific logic than just collecting values.
- For problems like "Max Width," assigning positional indices to nodes (as if in a complete tree) is a common strategy within the BFS framework.
- For problems like "Populating Next Right Pointers," BFS allows easy iteration through nodes of the same level to establish connections.
- The core BFS template (process level by level using `queue` and `level_size`) remains highly adaptable.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/11 - Tree Problems - Level-Order (BFS) Part 1|Prev: Level-Order (BFS) Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/13 - Tree Problems - Combining Traversal and Decomposition|Next: Combining Modes]] |
"""
    folder_path = os.path.join(path, "Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns")
    ensure_dir(folder_path)
    with open(os.path.join(folder_path, "12 - Tree Problems - Level-Order (BFS) Part 2.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Created: {os.path.join(folder_path, '12 - Tree Problems - Level-Order (BFS) Part 2.md')}")

# --- Combining Modes ---
def create_both_modes_thinking_note(path):
    source_file_path = "labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树算法习题汇总/【练习】同时运用两种思维解题.md"
    content = r"""---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, concept/recursion, pattern/tree_problem, course/labuladong]
aliases: [Hybrid Tree Thinking, Dual Approach Tree Problems, 二叉树双思维模式解题]
---
> [!NOTE] Source Annotation
> Content inspired by Labuladong's philosophy that some tree problems can be approached using either "traversal" or "decomposition" thinking, and understanding both deepens mastery.
> Original source context: [[{source_file_path}|【练习】同时运用两种思维解题]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/12 - Tree Problems - Level-Order (BFS) Part 2|Prev: Level-Order (BFS) Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | (End of specific patterns, consider linking to Tree Traversal Index) |

# Tree Problems: Combining or Choosing Between Traversal & Decomposition Thinking

Many binary tree problems can be solved effectively using either the "traversal" (DFS with side effects) or "decomposition" (divide and conquer, function returns sub-result) thinking modes outlined in [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]]. Some problems are particularly illustrative of how both approaches can lead to a solution, or how one might be more intuitive or efficient than the other. Understanding when and how to apply each, or even combine them, is a sign of advanced understanding.

## 核心概念 (Core Concept)
-   **Traversal Thinking:** Focuses on what each node *does* during a traversal. Results are often accumulated in external variables. Pre-order, in-order, and post-order positions are key for injecting logic.
-   **Decomposition Thinking:** Focuses on what a recursive function *returns* for a subtree. The solution for a node is built from the returned solutions of its children's subtrees (typically post-order).

This note explores problems where either perspective is viable or where a blend is optimal.

## 示例问题 (Illustrative Problem: Max Depth of Binary Tree LC104)
LeetCode 104. Maximum Depth of Binary Tree ([[Interview/Practice/LeetCode/LC104 - Maximum Depth of Binary Tree|LC104]]) is a prime example where both modes apply clearly.

### Approach 1: Traversal Thinking
Maintain a `current_depth` during DFS. When a leaf (or null beyond leaf) is reached, update a global `max_overall_depth`.

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionMaxDepth_Traversal:
    def __init__(self):
        self.max_d = 0
    
    def _traverse(self, node: TreeNode, current_depth: int):
        if not node:
            # Reached end of a path, update max_d if current_depth is greater
            # Or, if depth is #nodes, process at leaf. If depth is #edges, this is fine.
            # Let's assume depth = number of nodes on path.
            # If leaf is base, update self.max_d = max(self.max_d, current_depth) here.
            return

        # Update max_d at each node, especially if definition of depth is number of nodes.
        # For path length to leaf, it's better to check at leaf or just beyond.
        # If a leaf is depth D, its null children path is D. Update at null.
        self.max_d = max(self.max_d, current_depth + 1) # +1 for current node

        self._traverse(node.left, current_depth + 1)
        self._traverse(node.right, current_depth + 1)

    # Alternative traversal for path length (edges):
    def _traverse_path_len(self, node: TreeNode, path_len: int):
        if not node: # Path ended before this node
            return
        
        if not node.left and not node.right: # This is a leaf
            self.max_d = max(self.max_d, path_len) # path_len is number of edges to this leaf
            return
            
        self._traverse_path_len(node.left, path_len + 1)
        self._traverse_path_len(node.right, path_len + 1)

    def maxDepth(self, root: TreeNode) -> int:
        if not root: return 0
        self.max_d = 0
        # Using path_len as number of edges to current node (root has path_len 0)
        # self._traverse_path_len(root, 0) 
        # return self.max_d + 1 # if max_d stores max edges, depth (nodes) is edges+1

        # If depth = number of nodes. Start root with depth 1.
        self._traverse_nodes_depth(root, 1)
        return self.max_d

    def _traverse_nodes_depth(self, node: TreeNode, current_node_depth: int):
        if not node:
            return
        
        self.max_d = max(self.max_d, current_node_depth)
        
        self._traverse_nodes_depth(node.left, current_node_depth + 1)
        self._traverse_nodes_depth(node.right, current_node_depth + 1)

```
This uses an instance variable `self.max_d` and passes `current_depth` down.

### Approach 2: Decomposition Thinking
The function `maxDepth(node)` returns the depth of the subtree rooted at `node`.
```python
class SolutionMaxDepth_Decomposition:
    def maxDepth(self, root: TreeNode) -> int:
        if not root:
            return 0 # Depth of an empty tree is 0
        
        left_subtree_depth = self.maxDepth(root.left)
        right_subtree_depth = self.maxDepth(root.right)
        
        # Post-order: combine results
        return 1 + max(left_subtree_depth, right_subtree_depth)
```
This is often considered more elegant and purely functional for this specific problem.

## 何时选择哪种思维 (When to Choose Which Mode)

-   **Decomposition:**
    -   **Pros:** Often leads to cleaner, more functional code, especially if the problem has clear optimal substructure (e.g., height, size, sum, isBalanced, isSymmetric). The function's return value directly contributes to solving the parent's problem. Avoids global/instance state for the result.
    -   **Cons:** May not be intuitive for problems requiring complex path tracking or "global" context beyond direct parent-child relationships.

-   **Traversal:**
    -   **Pros:** More natural for problems that involve "visiting" nodes sequentially and performing actions based on a path or accumulated state (e.g., path sum, serializing/deserializing, Kth smallest if iterating in order). Backtracking problems are a prime example of traversal thinking.
    -   **Cons:** Can lead to reliance on instance/global variables if not careful, making the function less "pure." Passing complex state down can make function signatures cumbersome.

## 示例图示 (Diagram: Conceptual Difference)

```tikz
\begin{tikzpicture}[
    mode_box/.style={rectangle, draw, rounded corners, fill=blue!10, text width=5cm, align=center, minimum height=2cm, font=\sffamily\small},
    arrow/.style={->, thick, >=stealth}
]

% Traversal Thinking
\node[mode_box] (traversal) at (0,0) {
    \textbf{Traversal Thinking (DFS)}\\
    `void traverse(node, path_state)`\\
    - Global/instance `result` variable.\\
    - Logic in Pre/In/Post order slots.\\
    - Example: Accumulate all root-to-leaf paths.
};
\node[below=0.5cm of traversal, font=\sffamily\tiny, text width=5cm, align=center] (trav_focus)
    {Focus: Action at each node during a fixed exploration sequence. Result built via side-effects.};

% Decomposition Thinking
\node[mode_box] (decomposition) at (7,0) {
    \textbf{Decomposition Thinking (D&C)}\\
    `ReturnType solve(node)`\\
    - Function returns solution for `node`'s subtree.\\
    - `res_L = solve(node.left)`\\
    - `res_R = solve(node.right)`\\
    - `return combine(node.val, res_L, res_R)` (Post-order).
};
\node[below=0.5cm of decomposition, font=\sffamily\tiny, text width=5cm, align=center] (decomp_focus)
    {Focus: Defining subproblem solution. Result built by combining sub-solutions.};

\node[cloud, draw, cloud puffs=10, cloud puff arc=120, aspect=2.5, fill=orange!20] at (3.5, -3) {
    Many problems can use either, but one might be more natural or efficient.
    Advanced problems might blend: e.g., a traversal function that calls a decomposition-style helper.
};
\end{tikzpicture}
```

## 总结 (Summary)
- Many tree problems allow solutions from both "traversal" and "decomposition" perspectives.
- **Traversal thinking** is like exploring the tree and taking notes or actions as you go, often using global/instance variables or passed-down state.
- **Decomposition thinking** is about defining what a function should compute for a subtree and how to build that from its children's results.
- Understanding both modes enhances problem-solving flexibility. For simple property calculations (height, size), decomposition is often cleaner. For path-finding or stateful exploration, traversal (with backtracking) is common.
- The "best" approach depends on the specific problem, clarity of implementation, and sometimes efficiency nuances (though both are typically DFS-based and $O(N)$ for visiting nodes).

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/12 - Tree Problems - Level-Order (BFS) Part 2|Prev: Level-Order (BFS) Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | Next: (Consider linking to [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Core Principles]] or main Tree Traversal Index) |
"""
    folder_path = os.path.join(path, "Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns")
    ensure_dir(folder_path)
    with open(os.path.join(folder_path, "13 - Tree Problems - Combining Traversal and Decomposition.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Created: {os.path.join(folder_path, '13 - Tree Problems - Combining Traversal and Decomposition.md')}")

# --- Update Index Files ---
def update_tree_traversal_index(path):
    index_path = os.path.join(path, "Interview/Concept/Algorithms/Tree Traversal/index.md")
    
    new_section_header = "## Problem-Solving Patterns (Labuladong Inspired)"
    new_link_to_patterns_index = "- [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Binary Tree Problem-Solving Patterns]]"
    
    existing_content = ""
    if os.path.exists(index_path):
        with open(index_path, 'r', encoding='utf-8') as f:
            existing_content = f.read()
    else: # Create a basic index if it doesn't exist
        existing_content = r"""---
tags: [index, concept/algorithms, concept/tree_traversal, concept/dfs, concept/bfs]
aliases: [Tree Traversal Algorithm Index, Tree DFS BFS]
---

# Tree Traversal Algorithms

This section covers algorithms for traversing tree data structures, primarily focusing on Depth-First Search (DFS) and Breadth-First Search (BFS) strategies and their variants.

## Binary Tree Traversals
- [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Binary Tree Recursive Traversal (DFS)]]
  - Pre-order Traversal
  - In-order Traversal
  - Post-order Traversal
- [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Binary Tree Level-Order Traversal (BFS)]]
- [[Interview/Concept/Algorithms/Tree Traversal/06 - Binary Tree Iterative Traversal with Stack (DFS Simulation)|Binary Tree Iterative Traversal (Stack-based DFS)]]


## N-ary Tree Traversals
- [[Interview/Concept/Algorithms/Tree Traversal/02 - N-ary Tree Recursive Traversal (DFS)|N-ary Tree Recursive Traversal (DFS)]]
  - Pre-order Traversal
  - Post-order Traversal
- [[Interview/Concept/Algorithms/Tree Traversal/03 - N-ary Tree Level-Order Traversal (BFS)|N-ary Tree Level-Order Traversal (BFS)]]

## Core Principles & Frameworks
- [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree - Core Algorithmic Principles]]
- [[Interview/Concept/Algorithms/Tree Traversal/07 - Lowest Common Ancestor (LCA) Framework|Lowest Common Ancestor (LCA) Framework]]


## Comparison and Use Cases
- [[Interview/Concept/Algorithms/Tree Traversal/04 - DFS vs BFS - When to Use Which|DFS vs. BFS - When to Use Which]]

## Visualization
```mermaid
graph TD
    TT["Tree Traversal"] --> BT_T["Binary Tree Traversals"]
    TT --> NT_T["N-ary Tree Traversals"]
    TT --> CorePrinc["[[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Core Principles]]"]
    TT --> Compare["[[Interview/Concept/Algorithms/Tree Traversal/04 - DFS vs BFS - When to Use Which|DFS vs. BFS Comparison]]"]
    TT --> LCAFramework["[[Interview/Concept/Algorithms/Tree Traversal/07 - Lowest Common Ancestor (LCA) Framework|LCA Framework]]"]


    BT_T --> BT_DFS["[[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Recursive (DFS)]]"]
    BT_DFS --> PreO["Pre-order"]
    BT_DFS --> InO["In-order"]
    BT_DFS --> PostO["Post-order"]
    BT_T --> BT_BFS["[[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Level-Order (BFS)]]"]
    BT_T --> BT_Iter_DFS["[[Interview/Concept/Algorithms/Tree Traversal/06 - Binary Tree Iterative Traversal with Stack (DFS Simulation)|Iterative Stack (DFS)]]"]


    NT_T --> NT_DFS["[[Interview/Concept/Algorithms/Tree Traversal/02 - N-ary Tree Recursive Traversal (DFS)|Recursive (DFS)]]"]
    NT_DFS --> NT_PreO["Pre-order"]
    NT_DFS --> NT_PostO["Post-order"]
    NT_T --> NT_BFS["[[Interview/Concept/Algorithms/Tree Traversal/03 - N-ary Tree Level-Order Traversal (BFS)|Level-Order (BFS)]]"]

    classDef main fill:#e0ffff,stroke:#008b8b,stroke-width:2px;
    class TT main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
"""
    
    updated_content = existing_content
    if new_section_header not in updated_content:
        # Add the new section and link at a logical place, e.g., before "Visualization" or "Parent"
        parent_link_str = "--- Parent:"
        vis_str = "## Visualization"
        
        insertion_point_found = False
        if vis_str in updated_content:
            parts = updated_content.split(vis_str, 1)
            updated_content = parts[0] + new_section_header + "\n" + new_link_to_patterns_index + "\n\n" + vis_str + parts[1]
            insertion_point_found = True
        elif parent_link_str in updated_content:
            parts = updated_content.split(parent_link_str, 1)
            updated_content = parts[0] + new_section_header + "\n" + new_link_to_patterns_index + "\n\n" + parent_link_str + parts[1]
            insertion_point_found = True
            
        if not insertion_point_found: # Append if other markers not found
             updated_content += "\n" + new_section_header + "\n" + new_link_to_patterns_index + "\n"

    # Update Mermaid diagram if it exists
    mermaid_start_tag = "```mermaid"
    mermaid_end_tag = "```"
    if mermaid_start_tag in updated_content:
        mermaid_block_start = updated_content.rfind(mermaid_start_tag) # Find last mermaid block
        mermaid_block_end = updated_content.find(mermaid_end_tag, mermaid_block_start + len(mermaid_start_tag))
        
        if mermaid_block_start != -1 and mermaid_block_end != -1:
            mermaid_content = updated_content[mermaid_block_start + len(mermaid_start_tag) : mermaid_block_end].strip()
            
            new_mermaid_nodes = """
    PatternIndex["Problem Patterns"]
    TT --> PatternIndex["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Problem Patterns]]"]
"""
            if 'PatternIndex' not in mermaid_content:
                # Add before classDef or at end of main connections
                lines = mermaid_content.splitlines()
                insert_idx = len(lines)
                for i, line in enumerate(lines):
                    if "classDef" in line:
                        insert_idx = i
                        break
                lines.insert(insert_idx, new_mermaid_nodes)
                updated_mermaid_content = "\n".join(lines)
                updated_content = updated_content[:mermaid_block_start + len(mermaid_start_tag)] + "\n" + updated_mermaid_content + "\n" + updated_content[mermaid_block_end:]

    ensure_dir(os.path.dirname(index_path))
    with open(index_path, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    print(f"Updated: {index_path}")


# --- Main Script ---
if __name__ == "__main__":
    kb_root = "../"  # Assuming script is in a subfolder, and KB root is one level up.
    
    print(f"Knowledge base operations will be relative to: {os.path.abspath(kb_root)}")

    # Create pattern notes
    create_traversal_thinking_part1_note(kb_root)
    create_traversal_thinking_part2_note(kb_root)
    create_traversal_thinking_part3_note(kb_root)
    create_decomposition_thinking_part1_note(kb_root)
    create_decomposition_thinking_part2_note(kb_root)
    create_post_order_pattern_part1_note(kb_root)
    create_post_order_pattern_part2_note(kb_root)
    create_post_order_pattern_part3_note(kb_root)
    create_bst_problems_part1_note(kb_root)
    create_bst_problems_part2_note(kb_root)
    create_level_order_pattern_part1_note(kb_root)
    create_level_order_pattern_part2_note(kb_root)
    create_both_modes_thinking_note(kb_root)
    
    # Create/Update index files
    create_tree_problem_patterns_index(kb_root)
    update_tree_traversal_index(kb_root)
    
    # (Optional: update_algorithms_index(kb_root) if significant structural changes were made that affect it)
    
    print("Script finished. All specified files for Labuladong Tree Problem Patterns created/updated.")

