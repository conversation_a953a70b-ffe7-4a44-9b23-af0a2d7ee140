
import os
import textwrap

# Helper function to create directories if they don't exist
def ensure_dir(filepath):
    directory = os.path.dirname(filepath)
    if not os.path.exists(directory):
        os.makedirs(directory)
    print(f"Ensured directory: {directory}")

# --- Content Generation Functions ---

def create_trie_introduction(kb_root):
    filepath = os.path.join(kb_root, "Concept/Data Structures/Trie/00 - Trie - Introduction and Principles.md")
    ensure_dir(filepath)
    content = r"""---
tags: [concept/data_structures, concept/tree, concept/trie, concept/prefix_tree, type/introduction]
aliases: [Trie, Prefix Tree, Dictionary Tree, 字典树, 前缀树]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/二叉树结构的种种变换/Trie_字典树_前缀树原理及可视化.md]].
> Labuladong positions Trie as an extension of [[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|N-ary tree]] structures, specifically optimized for string operations.

# Trie (Prefix Tree / Dictionary Tree): Principles and Visualization

A Trie, also known as a prefix tree or dictionary tree, is a specialized tree-like data structure used for efficient retrieval of keys in a dataset of strings. Each node in a Trie represents a character (or part of a character/string), and paths from the root to a node represent prefixes.

## 🌲 Core Concept: An N-ary Tree for Strings

A Trie can be seen as a specific type of [[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|N-ary tree]] where:
- Each node (except possibly the root) corresponds to a character.
- The children of a node are indexed by characters. For example, a node might have children for 'a', 'b', 'c', etc.
- Paths from the root to a particular node represent a prefix.
- Some nodes might be marked as the end of a complete key (string) stored in the Trie, often also storing an associated value if implementing a TrieMap.

**Visualization Example (Labuladong's Visualizer):**
Imagine inserting "apple", "app", "appl":
```mermaid
graph TD
    Root["(root)"]
    Root -- "a" --> A["a"]
    A -- "p" --> AP["ap"]
    AP -- "p" --> APP["app (val=2)"]
    APP -- "l" --> APPL["appl (val=3)"]
    APPL -- "e" --> APPLE["apple (val=1)"]

    style Root fill:#f9f,stroke:#333,stroke-width:2px
    style APP fill:#lightgreen,stroke:#333,stroke-width:2px
    style APPL fill:#lightgreen,stroke:#333,stroke-width:2px
    style APPLE fill:#lightgreen,stroke:#333,stroke-width:2px
```
> In this conceptual diagram based on Labuladong's visualization, nodes like "app", "appl", "apple" would be marked as end-of-word and could store associated values.

## 🔑 Key Applications and Advantages

### 1. Efficient Prefix Operations
Tries excel at operations involving prefixes:
- **`startsWith(prefix)` / `hasKeyWithPrefix(prefix)`:** Check if any stored key begins with the given prefix. Complexity: $O(L)$, where $L$ is the length of the prefix.
- **`keysWithPrefix(prefix)`:** Retrieve all keys that start with the given prefix. Complexity depends on the number of matching keys and their lengths, but finding the prefix node is $O(L)$. This is ideal for auto-completion features.
- **`shortestPrefixOf(string)`:** Find the shortest stored key that is a prefix of a given string.
- **`longestPrefixOf(string)`:** Find the longest stored key that is a prefix of a given string (useful in routing tables, for example).

These operations are much less efficient with standard [[Interview/Concept/Programming Languages/Python/05 - Python Dict (Hash Map) for Interviews|hash maps]] or [[Interview/Concept/Data Structures/Tree/Binary Search Tree/index|binary search trees]].

### 2. Space Efficiency for Common Prefixes
If many strings share common prefixes (e.g., "application", "apply", "appeal"), the Trie stores the common prefix "app" only once, saving space compared to storing each full string separately in a hash map.
- **Example:** Storing "apple", "app", "appl".
    - Hash Map: Stores 3 distinct string objects: "apple" (5 chars), "app" (3 chars), "appl" (4 chars) = 12 characters total for keys.
    - Trie: Stores characters along paths. The shared "app" prefix is stored once. Total characters for key structure: 'a', 'p', 'p', 'l', 'e' = 5 characters essentially define all keys.

### 3. String Sorting (Implicit)
Traversing a Trie in a specific order (e.g., visiting children in alphabetical order) can retrieve all stored strings in lexicographical order.

### 4. Full-text Search and Pattern Matching
Tries and their variations (like Suffix Trees or Aho-Corasick automata) are fundamental in algorithms for searching patterns in text.

## 🔧 Trie Node Structure (Conceptual)

A common way to implement a Trie node:
```python
class TrieNode:
    def __init__(self):
        self.children = {}  # Maps character to TrieNode
        self.is_end_of_word = False # Flag to mark end of a word
        self.value = None # Optional: To store value if it's a TrieMap

# For a fixed alphabet (e.g., lowercase English letters):
# self.children = [None] * 26 
```
- **`children`:** A dictionary (or array for fixed alphabets) mapping a character to the child `TrieNode` representing that character.
- **`is_end_of_word` (or similar flag):** A boolean indicating if the path from the root to this node forms a complete key stored in the Trie.
- **`value`:** If implementing a `TrieMap`, this field stores the value associated with the key. For a `TrieSet`, only `is_end_of_word` is needed.

## TrieMap vs. TrieSet
- **`TrieMap<ValueType>`:** Stores key-value pairs. Keys are strings. `TrieNode` would have a `value` field.
- **`TrieSet`:** Stores a set of unique strings. `TrieNode` would have an `is_end_of_word` flag. It's essentially a `TrieMap` where the value is a boolean or not explicitly stored if `is_end_of_word` suffices.

## Complexity
For a Trie storing $N$ keys, where $L_{avg}$ is the average key length and $L_{max}$ is the maximum key length:
- **Insertion:** $O(L_{avg})$ or $O(L_{max})$ for one key.
- **Search (for a key):** $O(L)$ where $L$ is the length of the key being searched.
- **Deletion:** $O(L)$.
- **Prefix-based operations (e.g., `startsWith`):** $O(P)$ where $P$ is the length of the prefix.
- **Space Complexity:** In the worst case, if there are no shared prefixes, it can be $O(\sum L_i)$ (sum of lengths of all keys), similar to storing strings directly. However, with significant prefix sharing, it's much better. The number of nodes is at most $\sum L_i$.

## 🛠️ Basic Operations (Conceptual for TrieMap)

### Insert (`put(key, value)`)
1. Start from the root.
2. For each character in the `key`:
   a. If the character does not exist as a child of the current node, create a new child node for it.
   b. Move to that child node.
3. After processing all characters, mark the final node as `is_end_of_word = True` and set its `value`.

### Search (`get(key)`)
1. Start from the root.
2. For each character in the `key`:
   a. If the character does not exist as a child of the current node, the `key` is not in the Trie. Return `null` or raise an error.
   b. Move to that child node.
3. After processing all characters, if the final node is marked `is_end_of_word`, return its `value`. Otherwise, the `key` is a prefix of another key but not a key itself.

### Starts With (`startsWith(prefix)`)
1. Start from the root.
2. For each character in the `prefix`:
   a. If the character does not exist as a child of the current node, no key starts with this `prefix`. Return `False`.
   b. Move to that child node.
3. If all characters in `prefix` are processed successfully, return `True`.

Labuladong's course typically delves into the full implementation in later sections or exercises, focusing on these principles first.

## 总结 (Summary)
- Trie (Prefix Tree) is an N-ary tree optimized for string storage and prefix-based operations.
- Nodes represent characters, and paths from the root represent prefixes or full keys.
- Offers space savings for common prefixes and fast $O(L)$ prefix operations.
- Key methods include `insert`, `search`, `startsWith`, `keysWithPrefix`, etc.
- `TrieSet` is a simplified `TrieMap` for storing sets of strings.

---
Parent: [[Interview/Concept/Data Structures/Tree/index|Trees (Data Structure)]]
Next: [[Interview/Concept/Data Structures/Trie/01 - TrieMap and TrieSet Implementation|TrieMap and TrieSet Implementation]] (Placeholder for Labuladong's typical follow-up on implementation)
Related: [[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|N-ary Trees]], [[Interview/Concept/Programming Languages/Python/05 - Python Dict (Hash Map) for Interviews|Hash Maps]] (for comparison)
"""
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created: {filepath}")

def create_binary_heap_introduction(kb_root):
    filepath = os.path.join(kb_root, "Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization.md")
    ensure_dir(filepath)
    content = r"""---
tags: [concept/data_structures, concept/tree, concept/heap, concept/priority_queue, type/introduction]
aliases: [Binary Heap, Priority Queue Basics, 二叉堆原理, Min-Heap, Max-Heap]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/二叉树结构的种种变换/二叉堆核心原理及可视化.md]].
> Labuladong presents Binary Heap as an extension of [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Binary Tree]] structures, used for dynamic sorting and implementing Priority Queues.

# Binary Heap: Principles and Visualization

A Binary Heap is a specialized tree-based data structure that satisfies the heap property. It's typically implemented as a [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Complete Binary Tree]] and can be efficiently stored in an array. Heaps are commonly used to implement Priority Queues.

## 🌲 Core Concepts

1.  **Structure Property: Complete Binary Tree**
    - A binary heap is always a complete binary tree. This means all levels are fully filled except possibly the last level, which is filled from left to right.
    - This structure allows the heap to be stored compactly in an array, where parent-child relationships can be determined by index arithmetic:
        - For a 0-indexed array `arr`:
            - Parent of node at `i`: `(i-1) // 2`
            - Left child of node at `i`: `2*i + 1`
            - Right child of node at `i`: `2*i + 2`

2.  **Heap Property (Order Property):**
    - **Min-Heap:** For every node `X`, the value of `X` is less than or equal to the values of its children. This implies the minimum element is always at the root.
    - **Max-Heap:** For every node `X`, the value of `X` is greater than or equal to the values of its children. This implies the maximum element is always at the root.

Labuladong's visualizer often demonstrates these properties dynamically.

```mermaid
graph TD
    subgraph "Min-Heap Example"
        direction TB
        R1[1] --> C1_1[3]
        R1 --> C1_2[2]
        C1_1 --> G1_1[7]
        C1_1 --> G1_2[4]
        C1_2 --> G1_3[5]
        C1_2 --> G1_4[6]
    end
    subgraph "Max-Heap Example"
        direction TB
        R2[10] --> C2_1[8]
        R2 --> C2_2[9]
        C2_1 --> G2_1[3]
        C2_1 --> G2_2[2]
        C2_2 --> G2_3[5]
        C2_2 --> G2_4[1]
    end
```

## 🛠️ Key Operations: Swim and Sink

To maintain the heap property after insertions or deletions, two primary operations are used:

1.  **Swim (or Sift-Up, Heapify-Up, Bubble-Up, Percolate-Up):**
    - **Purpose:** When a new element is added to the heap (usually at the next available spot at the bottom to maintain completeness) or an element's value is decreased (in a min-heap) / increased (in a max-heap), it might violate the heap property with its parent.
    - **Action:** The element "swims" up the tree by repeatedly comparing itself with its parent and swapping if the heap property is violated, until its correct position is found or it reaches the root.
    - **Visualization (Min-Heap, adding 0 to `[1,2,3]`):**
        - Initial heap (array `[1,2,3]`): `1 -> (2,3)`
        - Add 0: `[1,2,3,0]`. Tree: `1 -> (2,3), 2 -> (0, None)`
        - 0 swims: `0` vs parent `2`. Swap. `[1,0,3,2]`. Tree: `1 -> (0,3), 0 -> (2,None)`
        - 0 swims: `0` vs parent `1`. Swap. `[0,1,3,2]`. Tree: `0 -> (1,3), 1 -> (2,None)`
        - 0 is at root, or heap property restored.

2.  **Sink (or Sift-Down, Heapify-Down, Bubble-Down, Percolate-Down):**
    - **Purpose:** When the root element is removed (common in `pop` operations from a priority queue) or an element's value is increased (in a min-heap) / decreased (in a max-heap), it might violate the heap property with its children.
    - **Action:** The element (often, the last element in the heap is moved to the root after a pop) "sinks" down the tree. It's repeatedly compared with its children. In a min-heap, it's swapped with the smaller child if it's larger than that child. This continues until it's in a valid position (smaller than its children or it becomes a leaf).
    - **Visualization (Min-Heap, removing root 1 from `[1,2,3,4,5]` and moving 5 to root):**
        - Initial: `1 -> (2,3), 2->(4,5)`
        - Remove 1. Move 5 (last element) to root. Array: `[5,2,3,4]`. Tree: `5 -> (2,3), 2->(4,None)`
        - 5 sinks: `5` vs children `2,3`. Smaller child is `2`. Swap `5` with `2`. Array: `[2,5,3,4]`. Tree: `2 -> (5,3), 5->(4,None)`
        - 5 sinks: `5` vs child `4`. Swap `5` with `4`. Array: `[2,4,3,5]`. Tree: `2 -> (4,3), 4->(5,None)`
        - 5 is a leaf or heap property restored.

Labuladong's visualizer interactively shows these `sink` and `swim` operations.

## 🚀 Applications

1.  **Priority Queue:**
    - Heaps are the most common way to implement priority queues.
    - `insert` operation: Add element to end, then `swim`. $O(\log N)$.
    - `extractMin` (Min-Heap) / `extractMax` (Max-Heap): Remove root, replace with last element, then `sink`. $O(\log N)$.
    - `peekMin/Max`: $O(1)$.
    - A detailed implementation is usually covered in [[Interview/Concept/Data Structures/Heap/01 - Binary Heap - Priority Queue Implementation|Priority Queue Implementation]].

2.  **Heap Sort:**
    - An in-place sorting algorithm with $O(N \log N)$ time complexity.
    - **Phase 1 (Heapify):** Build a max-heap from the input array. $O(N)$.
    - **Phase 2 (Sortdown):** Repeatedly swap the root (max element) with the last element of the heap, reduce heap size by one, and `sink` the new root to restore heap property. $O(N \log N)$.

## Complexity of Heap Operations
Assuming $N$ elements in the heap:
- **Insert:** $O(\log N)$ (due to swim)
- **Delete Min/Max (Pop):** $O(\log N)$ (due to sink)
- **Peek Min/Max:** $O(1)$
- **Build Heap (Heapify an array of N elements):** $O(N)$ (amortized, more efficient than $N$ insertions)

## 总结 (Summary)
- Binary Heap is a complete binary tree satisfying the min-heap or max-heap property.
- Efficiently implemented using an array.
- Core operations `swim` (up) and `sink` (down) maintain heap property, both $O(\log N)$.
- Primary applications: Priority Queues and Heap Sort.
- `peek` is $O(1)$, `insert/delete` are $O(\log N)$.

---
Parent: [[Interview/Concept/Data Structures/Tree/index|Trees (Data Structure)]]
Next: [[Interview/Concept/Data Structures/Heap/01 - Binary Heap - Priority Queue Implementation|Binary Heap - Priority Queue Implementation]]
Related: [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Binary Trees]], [[Interview/Concept/Algorithms/Sorting/Heap Sort|Heap Sort]]
"""
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created: {filepath}")

def create_segment_tree_introduction(kb_root):
    filepath = os.path.join(kb_root, "Concept/Data Structures/Tree/Segment Tree/00 - Segment Tree - Principles and Visualization.md")
    ensure_dir(filepath)
    content = r"""---
tags: [concept/data_structures, concept/tree, concept/segment_tree, type/introduction, concept/range_query]
aliases: [Segment Tree Basics, Interval Tree, 线段树原理]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/二叉树结构的种种变换/线段树核心原理及可视化.md]].
> Labuladong introduces Segment Tree as a derivative of [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Binary Tree]] structures, designed for efficient range queries and updates.

# Segment Tree: Principles and Visualization

A Segment Tree is a versatile tree data structure, typically a [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|binary tree]], used for storing information about intervals or segments. Each node in the segment tree represents an interval (or segment) of an array or range. It is particularly efficient for range query problems (e.g., sum of elements in a range, minimum/maximum in a range) and range update problems.

## 🌲 Core Concept

- **Structure:** A segment tree is a (usually) balanced binary tree.
    - **Leaf Nodes:** Represent individual elements of the original array or elementary intervals.
    - **Internal Nodes:** Represent the union (or aggregation) of the intervals of its children. For example, if a node represents the interval `[L, R]`, its left child might represent `[L, M]` and its right child `[M+1, R]`, where `M` is the midpoint. The value stored in the internal node is an aggregate (e.g., sum, min, max) of the values in its children's intervals.
- **Efficiency:** Allows for range queries and range updates in $O(\log N)$ time, where $N$ is the size of the original array/range.

**Visualization (from Labuladong's examples):**
For an array `nums = [1, 3, 5, 7, 9, 11]`:
A segment tree for sums might look like this:

```mermaid
graph TD
    R_0_5["[0,5]\nSum=36"] --> L_0_2["[0,2]\nSum=9"]
    R_0_5 --> R_3_5["[3,5]\nSum=27"]

    L_0_2 --> LL_0_1["[0,1]\nSum=4"]
    L_0_2 --> LR_2_2["[2,2]\nSum=5 (Leaf)"]
    
    R_3_5 --> RL_3_4["[3,4]\nSum=16"]
    R_3_5 --> RR_5_5["[5,5]\nSum=11 (Leaf)"]

    LL_0_1 --> LLL_0_0["[0,0]\nSum=1 (Leaf)"]
    LL_0_1 --> LLR_1_1["[1,1]\nSum=3 (Leaf)"]

    RL_3_4 --> RLL_3_3["[3,3]\nSum=7 (Leaf)"]
    RL_3_4 --> RLR_4_4["[4,4]\nSum=9 (Leaf)"]

    style R_0_5 fill:#f9f,stroke:#333,stroke-width:2px
    style LR_2_2 fill:#lightgreen,stroke:#333
    style RR_5_5 fill:#lightgreen,stroke:#333
    style LLL_0_0 fill:#lightgreen,stroke:#333
    style LLR_1_1 fill:#lightgreen,stroke:#333
    style RLL_3_3 fill:#lightgreen,stroke:#333
    style RLR_4_4 fill:#lightgreen,stroke:#333
```

## 🛠️ Key Operations

1.  **Build:** Constructing the segment tree from an initial array. This typically takes $O(N)$ time.
    - Each leaf node takes the value of an array element.
    - Each internal node's value is computed from its children (e.g., `parent.sum = left_child.sum + right_child.sum`).

2.  **Query (Range Query):** Finding an aggregate value over a given range `[queryL, queryR]`.
    - Traverses the tree. If a node's interval is completely within the query range, its value is used.
    - If a node's interval partially overlaps, recurse on its children.
    - If a node's interval is completely outside, it's ignored.
    - Complexity: $O(\log N)$.

3.  **Update (Point Update):** Modifying the value of a single element in the original array and updating the segment tree accordingly.
    - Update the corresponding leaf node.
    - Propagate changes upwards to affected parent nodes.
    - Complexity: $O(\log N)$.

Labuladong's visualizer for a basic segment tree demonstrates `query` and `update` (point update).

## Variations and Enhancements

### 1. Dynamic Segment Tree (Implicit Segment Tree / Dynamic Node Allocation)
- **Problem Solved:** Standard segment trees built on an array require space proportional to $N$. If $N$ is very large (e.g., $10^9$) but only a few points/ranges are actually used/updated, pre-allocating the full tree is infeasible.
- **Solution ("Dynamic开点" - Dynamic Node Creation):** Nodes in the segment tree are created only when they are needed (e.g., during an update operation on a specific point that falls within a node's range that hasn't been created yet).
- **Implementation:** Child pointers in nodes are initially `null`. They are instantiated when that part of the range is first accessed.
- Labuladong's visualizer for "dynamic segment tree" shows this "on-demand" node creation.

### 2. Segment Tree with Lazy Propagation (for Range Updates)
- **Problem Solved:** Updating a range of elements (e.g., add `val` to all elements in `nums[i...j]`) efficiently. A naive approach of $k$ point updates would be $O(k \log N)$, which is too slow if $k$ is large.
- **Solution ("Lazy Propagation"):** When a range update applies to a node's entire interval, instead of propagating the update all the way to the leaves immediately, store a "lazy tag" at that node. This tag indicates a pending update for its entire sub-interval.
    - The node's aggregate value is updated based on the range update.
    - The lazy tag is propagated down to children only when necessary (e.g., when a query needs to access a child, or another update passes through this node).
- **Complexity for Range Update:** $O(\log N)$.
- Labuladong's visualizer for "lazy update segment tree" shows how updates are cached in internal nodes and pushed down during queries.

## 🚀 Use Cases
Segment trees are powerful for problems involving:
- Range Sum Queries (RSQ)
- Range Minimum/Maximum Queries (RMQ)
- Range Updates (e.g., add a value to all elements in a range, set all elements in a range to a value)
- Geometric problems (e.g., counting points in a rectangle)
- Problems where data can be aggregated associatively over intervals.

## Array vs. Pointer-based Implementation
- **Array-based:** Simpler for full segment trees. Parent/child indices are calculated arithmetically. Requires about $2N$ to $4N$ array space.
- **Pointer-based (Nodes with `left`/`right` children):** More flexible for dynamic segment trees and can be more intuitive for understanding the tree structure.

## 总结 (Summary)
- Segment Tree is a binary tree for efficient range queries and updates on an array.
- Each node represents an interval and stores an aggregate value for that interval.
- Basic operations (build, query, point update) are $O(\log N)$.
- **Dynamic Segment Trees** use on-demand node creation for sparse data over large ranges.
- **Lazy Propagation** enables efficient $O(\log N)$ range updates.
- It's a fundamental data structure in competitive programming and for optimizing certain types of data analysis tasks.

---
Parent: [[Interview/Concept/Data Structures/Tree/index|Trees (Data Structure)]]
Next: [[Interview/Concept/Data Structures/Tree/Segment Tree/01 - Segment Tree Implementation - Basic|Segment Tree Implementation - Basic]] (Placeholder for Labuladong's typical follow-up on implementation)
Related: [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Binary Trees]], [[Interview/Concept/Algorithms/Divide and Conquer/index|Divide and Conquer]] (as the query/update logic often follows this paradigm)
"""
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created: {filepath}")

def update_existing_tree_indices(kb_root):
    # This function will update Interview/Concept/Data Structures/Tree/index.md
    # and Interview/Concept/Data Structures/index.md to include new tree types.

    # Update /Concept/Data Structures/Tree/index.md
    tree_index_path = os.path.join(kb_root, "Concept/Data Structures/Tree/index.md")
    ensure_dir(tree_index_path)
    
    tree_index_content = ""
    try:
        with open(tree_index_path, "r", encoding="utf-8") as f:
            tree_index_content = f.read()
    except FileNotFoundError:
        # Create a basic structure if it doesn't exist
        tree_index_content = r"""---
tags: [index, concept/data_structures, concept/tree]
aliases: [Tree Data Structures Index]
---

# Tree Data Structures

This section covers various types of tree data structures and related concepts.

## Tree Types & Concepts:
- [[Interview/Concept/Data Structures/Tree/Binary Tree/index|Binary Trees]]
- [[Interview/Concept/Data Structures/Tree/N-ary Tree/index|N-ary Trees (Multiway Trees)]]
""" # Base content

    new_tree_type_links = [
        "- [[Interview/Concept/Data Structures/Trie/index|Tries (Prefix Trees)]]",
        "- [[Interview/Concept/Data Structures/Heap/index|Heaps (Priority Queues)]]",
        "- [[Interview/Concept/Data Structures/Tree/Segment Tree/index|Segment Trees]]"
    ]

    # Add new links if not already present
    for link in new_tree_type_links:
        if link not in tree_index_content:
            # Append after the "## Tree Types & Concepts:" block or last existing link
            if "## Tree Types & Concepts:" in tree_index_content:
                parts = tree_index_content.split("## Tree Types & Concepts:", 1)
                current_links = parts[1].splitlines()
                # Find where to insert the new link
                insert_idx = 0
                for i, line in enumerate(current_links):
                    if line.strip().startswith("- [["):
                        insert_idx = i + 1
                    elif line.strip() and not line.strip().startswith("- [["): # Reached end of list
                        break
                current_links.insert(insert_idx, link)
                tree_index_content = parts[0] + "## Tree Types & Concepts:" + "\n".join(current_links)
            else: # Fallback if header is missing somehow
                 tree_index_content += "\n" + link


    # Update or add Mermaid diagram
    mermaid_header_tag = "## Visualization"
    new_mermaid_content = r"""
## Visualization
```mermaid
graph TD
    TreeDS["Tree Data Structures"] --> BT["[[Interview/Concept/Data Structures/Tree/Binary Tree/index|Binary Trees]]"]
    TreeDS --> NT["[[Interview/Concept/Data Structures/Tree/N-ary Tree/index|N-ary Trees]]"]
    TreeDS --> Trie["[[Interview/Concept/Data Structures/Trie/index|Tries]]"]
    TreeDS --> Heap["[[Interview/Concept/Data Structures/Heap/index|Heaps]]"]
    TreeDS --> SegTree["[[Interview/Concept/Data Structures/Tree/Segment Tree/index|Segment Trees]]"]
    TreeDS --> AlgoLink["Algorithms (see [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal]])"]

    BT --> BTIntro["[[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Intro & Types]]"]
    NT --> NTIntro["[[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|Intro]]"]
    Trie --> TrieIntro["[[Interview/Concept/Data Structures/Trie/00 - Trie - Introduction and Principles|Intro & Principles]]"]
    Heap --> HeapIntro["[[Interview/Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization|Intro & Principles]]"]
    SegTree --> SegTreeIntro["[[Interview/Concept/Data Structures/Tree/Segment Tree/00 - Segment Tree - Principles and Visualization|Intro & Principles]]"]

    classDef main fill:#ccffcc,stroke:#006400,stroke-width:2px;
    class TreeDS main;
```

---
Parent: [[Interview/Concept/Data Structures/index|Data Structures Index]]
"""
    
    if mermaid_header_tag in tree_index_content:
        # Replace existing mermaid diagram more reliably
        start_mermaid = tree_index_content.find(mermaid_header_tag)
        # Find the end of the mermaid block or end of file if not properly terminated
        end_mermaid_diagram_tag = "```"
        end_mermaid_block = tree_index_content.find(end_mermaid_diagram_tag, start_mermaid + len(mermaid_header_tag) + len("```mermaid") )
        end_of_mermaid_section = tree_index_content.find("---", start_mermaid) # End before next YAML/section

        if end_mermaid_block != -1: # if ``` found
             end_mermaid_block = tree_index_content.find(end_mermaid_diagram_tag, end_mermaid_block + len(end_mermaid_diagram_tag)) # find the closing ```
             if end_mermaid_block != -1:
                end_mermaid_block += len(end_mermaid_diagram_tag)


        if end_of_mermaid_section != -1 and (end_mermaid_block == -1 or end_of_mermaid_section < end_mermaid_block):
            # If '---' comes before the end of mermaid block, or mermaid block end not found
            tree_index_content = tree_index_content[:start_mermaid] + new_mermaid_content
        elif end_mermaid_block != -1 : # mermaid block properly found
            tree_index_content = tree_index_content[:start_mermaid] + new_mermaid_content + tree_index_content[end_mermaid_block:]
        else: # No clear end, append after header
             tree_index_content = tree_index_content[:start_mermaid] + new_mermaid_content
            
    else: # Mermaid section doesn't exist, append it before the final --- Parent:
        parent_link_section = "---" + "\n" + "Parent: [[Interview/Concept/Data Structures/index|Data Structures Index]]"
        if parent_link_section in tree_index_content:
            parts = tree_index_content.split(parent_link_section, 1)
            tree_index_content = parts[0] + new_mermaid_content.strip() + "\n\n" + parent_link_section + parts[1]
        else: # Fallback if specific structure not found
            tree_index_content += "\n" + new_mermaid_content.strip()


    with open(tree_index_path, "w", encoding="utf-8") as f:
        f.write(tree_index_content)
    print(f"Updated: {tree_index_path}")


    # Update /Concept/Data Structures/index.md
    ds_index_path = os.path.join(kb_root, "Concept/Data Structures/index.md")
    ensure_dir(ds_index_path)
    
    ds_index_content = ""
    try:
        with open(ds_index_path, "r", encoding="utf-8") as f:
            ds_index_content = f.read()
    except FileNotFoundError:
        ds_index_content = r"""---
tags: [index, concept/data_structures]
aliases: [Data Structures Index Main]
---

# Data Structures

This index covers fundamental data structures, their properties, operations, and common use cases in algorithms and interviews.

## Core Data Structures:
- `[[Interview/Concept/Data Structures/Array/index|Arrays & Dynamic Arrays]]`
- `[[Interview/Concept/Data Structures/Linked List/index|Linked Lists]]`
- `[[Interview/Concept/Data Structures/Stack/index|Stacks]]`
- `[[Interview/Concept/Data Structures/Queue/index|Queues]]`
- `[[Interview/Concept/Data Structures/Hash Table/index|Hash Tables (Hash Maps & Hash Sets)]]`
- `[[Interview/Concept/Data Structures/Tree/index|Trees (General)]]`
""" # Base Content

    new_ds_links = [
        "  - `[[Interview/Concept/Data Structures/Trie/index|Tries (Prefix Trees)]]` (Placeholder update)", # Example for Trie if it was just placeholder
        "  - `[[Interview/Concept/Data Structures/Heap/index|Heaps (Priority Queues)]]` (Placeholder update)",
        "  - `[[Interview/Concept/Data Structures/Tree/Segment Tree/index|Segment Trees]]` (Placeholder update)"
    ]
    
    # Ensure main Tree link is not a placeholder
    ds_index_content = ds_index_content.replace("`[[Interview/Concept/Data Structures/Tree/index|Trees (General)]]` (Placeholder)", "[[Interview/Concept/Data Structures/Tree/index|Trees (General)]]")
    ds_index_content = ds_index_content.replace("`[[Interview/Concept/Data Structures/Trie/index|Tries (Prefix Trees)]]` (Placeholder)", "[[Interview/Concept/Data Structures/Trie/index|Tries (Prefix Trees)]]")
    ds_index_content = ds_index_content.replace("`[[Interview/Concept/Data Structures/Heap/index|Heaps (Priority Queues)]]` (Placeholder)", "[[Interview/Concept/Data Structures/Heap/index|Heaps (Priority Queues)]]")


    # Update Mermaid diagram for Data Structures Index
    mermaid_header_tag_ds = "## Visualization of Data Structures"
    new_mermaid_ds_content = r"""
## Visualization of Data Structures
```mermaid
graph TD
    DS["Data Structures"] --> Arrays["(Arrays)"]
    DS --> LinkedLists["(Linked Lists)"]
    DS --> Stacks["[[Interview/Concept/Data Structures/Stack/index|Stacks]]"]
    DS --> Queues["[[Interview/Concept/Data Structures/Queue/index|Queues]]"]
    DS --> HashTables["(Hash Tables)"]
    DS --> Trees["[[Interview/Concept/Data Structures/Tree/index|Trees]]"]
    DS --> Heaps["[[Interview/Concept/Data Structures/Heap/index|Heaps]]"]
    DS --> Graphs["(Graphs)"]
    DS --> Tries["[[Interview/Concept/Data Structures/Trie/index|Tries]]"]

    Stacks --> StackApps["[[Interview/Concept/Data Structures/Stack/Applications/index|Applications]]"]
    Queues --> QueueApps["[[Interview/Concept/Data Structures/Queue/Applications/index|Applications]]"]
    HashTables --> HashMap["(Hash Map)"]
    HashTables --> HashSet["(Hash Set)"]
    Trees --> BT["[[Interview/Concept/Data Structures/Tree/Binary Tree/index|Binary Tree]]"]
    Trees --> NT["[[Interview/Concept/Data Structures/Tree/N-ary Tree/index|N-ary Tree]]"]
    Trees --> SegT["[[Interview/Concept/Data Structures/Tree/Segment Tree/index|Segment Tree]]"]
    Trees --> BST["(BST)"]


    classDef ds_cat fill:#fafad2,stroke:#b8860b,stroke-width:2px;
    class DS, Arrays, LinkedLists, Stacks, Queues, HashTables, Trees, Heaps, Graphs, Tries ds_cat;
```
"""
    if mermaid_header_tag_ds in ds_index_content:
        start_mermaid_ds = ds_index_content.find(mermaid_header_tag_ds)
        end_mermaid_ds_diagram_tag = "```"
        end_mermaid_ds_block = ds_index_content.find(end_mermaid_ds_diagram_tag, start_mermaid_ds + len(mermaid_header_tag_ds) + len("```mermaid"))
        if end_mermaid_ds_block != -1:
            end_mermaid_ds_block = ds_index_content.find(end_mermaid_ds_diagram_tag, end_mermaid_ds_block + len(end_mermaid_ds_diagram_tag))
            if end_mermaid_ds_block != -1:
                end_mermaid_ds_block += len(end_mermaid_ds_diagram_tag)
        
        if end_mermaid_ds_block != -1:
            ds_index_content = ds_index_content[:start_mermaid_ds] + new_mermaid_ds_content + ds_index_content[end_mermaid_ds_block:]
        else: # Fallback if end tag not found
            ds_index_content = ds_index_content[:start_mermaid_ds] + new_mermaid_ds_content
    else:
        ds_index_content += "\n" + new_mermaid_ds_content

    with open(ds_index_path, "w", encoding="utf-8") as f:
        f.write(ds_index_content)
    print(f"Updated: {ds_index_path}")


def create_trie_index(kb_root):
    filepath = os.path.join(kb_root, "Concept/Data Structures/Trie/index.md")
    ensure_dir(filepath)
    content = r"""---
tags: [index, concept/data_structures, concept/tree, concept/trie]
aliases: [Trie Index, Prefix Tree Index]
---

# Trie (Prefix Tree) Concepts

This section covers concepts and implementations related to Tries.

## Core Concepts:
- [[Interview/Concept/Data Structures/Trie/00 - Trie - Introduction and Principles|Trie - Introduction and Principles]]

## Implementations and Problems:
- `[[Interview/Concept/Data Structures/Trie/01 - TrieMap and TrieSet Implementation|TrieMap and TrieSet Implementation]]` (Placeholder for Labuladong's typical implementation note)
- `[[Interview/Practice/LeetCode/LC208 - Implement Trie (Prefix Tree)|LC208 - Implement Trie (Prefix Tree)]]` (Placeholder for a common LeetCode problem)

## Visualization
```mermaid
graph TD
    TrieConcept["Trie Concepts"] --> IntroTrie["[[Interview/Concept/Data Structures/Trie/00 - Trie - Introduction and Principles|Introduction]]"]
    TrieConcept --> ImplementationsTrie["Implementations & Problems"]
    ImplementationsTrie --> ImplNote["(TrieMap/Set Implementation Note)"]
    ImplementationsTrie --> LC208["(LC208 Implement Trie)"]

    classDef main fill:#e6fff2,stroke:#00994d,stroke-width:2px;
    class TrieConcept main;
```

---
Parent: [[Interview/Concept/Data Structures/Tree/index|Trees (Data Structure)]]
"""
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created: {filepath}")

def create_heap_index(kb_root):
    filepath = os.path.join(kb_root, "Concept/Data Structures/Heap/index.md")
    ensure_dir(filepath)
    content = r"""---
tags: [index, concept/data_structures, concept/tree, concept/heap, concept/priority_queue]
aliases: [Heap Index, Priority Queue Index]
---

# Heap and Priority Queue Concepts

This section covers concepts related to Heaps and their primary application as Priority Queues.

## Core Concepts:
- [[Interview/Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization|Binary Heap - Principles and Visualization]]
  - Min-Heap / Max-Heap
  - Swim and Sink operations

## Implementations:
- `[[Interview/Concept/Data Structures/Heap/01 - Binary Heap - Priority Queue Implementation|Binary Heap - Priority Queue Implementation]]` (Placeholder for Labuladong's typical implementation note)

## Related Algorithms:
- `[[Interview/Concept/Algorithms/Sorting/Heap Sort|Heap Sort]]` (Placeholder)

## Visualization
```mermaid
graph TD
    HeapConcept["Heap Concepts"] --> IntroHeap["[[Interview/Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization|Introduction & Principles]]"]
    IntroHeap --> MinMax["Min-Heap / Max-Heap"]
    IntroHeap --> SwimSink["Swim / Sink Operations"]
    
    HeapConcept --> ImplementationsHeap["Implementations"]
    ImplementationsHeap --> PQImpl["(Priority Queue Implementation)"]
    
    HeapConcept --> AlgoHeap["Related Algorithms"]
    AlgoHeap --> HeapSort["(Heap Sort)"]

    classDef main fill:#ffe6e6,stroke:#cc0000,stroke-width:2px;
    class HeapConcept main;
```

---
Parent: [[Interview/Concept/Data Structures/Tree/index|Trees (Data Structure)]] 
(Or [[Interview/Concept/Data Structures/index|Data Structures Index]] if considered more top-level than just a tree type)
"""
    # Decide parent link based on broader structure. Heaps are often taught with trees but also stand alone.
    # For consistency with provided files, linking to Tree index.
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created: {filepath}")

def create_segment_tree_index(kb_root):
    filepath = os.path.join(kb_root, "Concept/Data Structures/Tree/Segment Tree/index.md")
    ensure_dir(filepath)
    content = r"""---
tags: [index, concept/data_structures, concept/tree, concept/segment_tree, concept/range_query]
aliases: [Segment Tree Index, Interval Tree Index]
---

# Segment Tree Concepts

This section covers concepts, implementations, and applications of Segment Trees.

## Core Concepts:
- [[Interview/Concept/Data Structures/Tree/Segment Tree/00 - Segment Tree - Principles and Visualization|Segment Tree - Principles and Visualization]]
  - Range Queries (Sum, Min/Max)
  - Point Updates
  - Dynamic Segment Trees
  - Lazy Propagation for Range Updates

## Implementations:
- `[[Interview/Concept/Data Structures/Tree/Segment Tree/01 - Segment Tree Implementation - Basic|Segment Tree Implementation - Basic]]` (Placeholder)
- `[[Interview/Concept/Data Structures/Tree/Segment Tree/02 - Segment Tree with Lazy Propagation|Segment Tree with Lazy Propagation]]` (Placeholder)

## Visualization
```mermaid
graph TD
    SegTConcept["Segment Tree Concepts"] --> IntroSegT["[[Interview/Concept/Data Structures/Tree/Segment Tree/00 - Segment Tree - Principles and Visualization|Introduction & Principles]]"]
    IntroSegT --> Ops["Queries & Updates"]
    IntroSegT --> DynamicSegT["Dynamic Segment Trees"]
    IntroSegT --> LazyProp["Lazy Propagation"]
    
    SegTConcept --> ImplementationsSegT["Implementations"]
    ImplementationsSegT --> BasicImpl["(Basic Implementation)"]
    ImplementationsSegT --> LazyImpl["(Lazy Propagation Implementation)"]

    classDef main fill:#e6faff,stroke:#007acc,stroke-width:2px;
    class SegTConcept main;
```

---
Parent: [[Interview/Concept/Data Structures/Tree/index|Trees (Data Structure)]]
"""
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created: {filepath}")

# --- Main Script ---
if __name__ == "__main__":
    kb_root_for_script = "./" 
    
    print(f"Knowledge base operations target root: {kb_root_for_script}")

    # Create new concept notes from Labuladong's content
    create_trie_introduction(kb_root_for_script)
    create_binary_heap_introduction(kb_root_for_script)
    create_segment_tree_introduction(kb_root_for_script)
    
    # Create/Update index files for these new structures
    create_trie_index(kb_root_for_script)
    create_heap_index(kb_root_for_script)
    create_segment_tree_index(kb_root_for_script)
    
    # Update higher-level index files to include these new structures
    update_existing_tree_indices(kb_root_for_script) # This updates Tree/index.md and Data Structures/index.md
    
    print(f"Script finished. New tree structure concept notes and relevant indexes created/updated in '{kb_root_for_script}'.")

