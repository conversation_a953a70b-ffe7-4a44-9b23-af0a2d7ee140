
import os
import textwrap

# Helper function to create directories if they don't exist
def ensure_dir(directory):
    os.makedirs(directory, exist_ok=True)

# --- Content Generation Functions ---

def create_framework_thinking_philosophy(path):
    content = r"""---
tags: [#concept/algorithms, #concept, #concept/data_structures, #type/problem_solving_framework, #type, #meta/learning_philosophy, #meta]
aliases: [Algorithm Framework Thinking, Data Structure Core Ideas, Labuladong Framework, 学习数据结构和算法的框架思维]
---

> [!NOTE] Source Annotation
> This note synthesizes core ideas from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/学习数据结构和算法的框架思维.md]].
> It provides a high-level perspective on understanding and mastering data structures and algorithms.

# Framework Thinking for Data Structures and Algorithms (Labuladong)

Labuladong emphasizes a "framework thinking" approach to learning data structures and algorithms, which involves understanding their fundamental building blocks and common operational patterns. This allows for a more structured and efficient way to tackle a wide range of problems.

## 🧱 Data Structures: Based on Arrays and Linked Lists

**Core Idea:** All common data structures are essentially variations or combinations of two fundamental storage mechanisms:
1.  **Arrays (Sequential Storage):** Elements are stored in contiguous memory locations.
    - **Pros:** $O(1)$ random access (by index), space-efficient for elements themselves.
    - **Cons:** Fixed size (requiring $O(N)$ resizing for [[Interview/Concept/Data Structures/Array/01 - Dynamic Array - Principles and Amortized Analysis|dynamic arrays]]), $O(N)$ for insertions/deletions in the middle.
    - Examples derived: [[Interview/Concept/Data Structures/Stack/index|Stacks]] (array-based), [[Interview/Concept/Data Structures/Queue/index|Queues]] (array-based, often [[Interview/Concept/Data Structures/Array/02 - Circular Array (Ring Buffer)|circular arrays]]), [[Interview/Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization|Heaps]] (array representation of complete binary tree), [[Interview/Concept/Data Structures/Hash Map/00 - Hash Map - Core Principles|Hash Maps]] (underlying array for buckets).
2.  **Linked Lists (Linked Storage):** Elements store data and pointers to next (and possibly previous) elements.
    - **Pros:** Flexible size, $O(1)$ insertion/deletion if node reference is known (and doubly linked for previous).
    - **Cons:** $O(N)$ for access by index (requires traversal), extra space for pointers.
    - Examples derived: Stacks (linked-list based), Queues (linked-list based), [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Trees]] (nodes with child pointers), [[Interview/Concept/Data Structures/Graph/01 - Graph - Representation (Adjacency List, Adjacency Matrix)|Graphs]] (adjacency lists).

Understanding this underlying duality helps in choosing the right base for custom data structures or analyzing trade-offs of existing ones.

## ⚙️ Data Structure Operations: Traversal and Access (CRUD)

**Core Idea:** The fundamental operations on any data structure are **Create, Read, Update, Delete (CRUD)**, which boil down to **traversal** (visiting elements) and **access** (interacting with specific elements).

Traversal patterns:
-   **Linear Iteration:** `for` loops, `while` loops (common for arrays, lists).
    ```python
    # Array traversal
    # for item in array: process(item)
    # Linked list traversal
    # curr = head; while curr: process(curr.val); curr = curr.next
    ```
-   **Non-linear Recursion:** Common for tree and graph structures.
    - [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Binary Tree DFS (Pre-order, In-order, Post-order)]]
    - [[Interview/Concept/Algorithms/Tree Traversal/02 - N-ary Tree Recursive Traversal (DFS)|N-ary Tree DFS]]
    - [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|Graph DFS]] (often with a `visited` set)

These traversal frameworks are the skeletons upon which specific algorithmic logic is built.

## 💡 Algorithms: Refined Exhaustive Search (穷举)

**Core Idea:** Many algorithms, at their heart, are about **exhaustive search (穷举)** – systematically exploring all possible solutions or states. The "art" of algorithm design lies in making this search efficient.

**Two Key Challenges in Exhaustive Search:**
1.  **No Omissions (无遗漏):** Ensure all valid possibilities are considered. This requires a robust traversal or generation framework (e.g., correct [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|backtracking framework]] for permutations).
2.  **No Redundancy (无冗余):** Avoid re-computing the same information or exploring identical states multiple times. This is where optimizations like memoization in [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming]], pruning in backtracking, or using appropriate data structures (like sets for visited states in BFS/DFS) come in.

**Thinking Dimensions for Algorithm Problems:**
1.  **How to exhaustively search?** (Define the search space, states, transitions).
2.  **How to search *smartly*?** (Identify and eliminate redundant computations, prune unpromising branches).

**Examples:**
-   **[[Interview/Concept/Algorithms/Backtracking/index|Backtracking]] (e.g., Permutations, Combinations, Subsets):** The difficulty is often in correctly formulating the recursive structure to explore all possibilities without missing any or generating duplicates. This is "how to exhaustively search."
-   **[[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]] (e.g., Fibonacci, Coin Change):** The initial brute-force recursive solution exhaustively explores subproblems. The "smart" part is adding memoization or using tabulation to avoid recomputing overlapping subproblems. The primary challenge is often finding the state transition equation (the "how to exhaustively search" part via recursion).
-   **[[Interview/Concept/Algorithms/Two Pointers/index|Two Pointers]], [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window]], [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary Search]]:** These are "smart search" techniques for array/list problems, often improving upon a naive $O(N^2)$ or $O(N)$ full scan.
-   **[[Interview/Concept/Algorithms/Greedy Algorithms/00 - Greedy Algorithm - Core Framework|Greedy Algorithms]]:** A special case of "smart search" where a specific local choice property allows us to avoid exploring many branches, directly leading to an optimum.

## 🌲 Two Thinking Modes for Tree Problems (and beyond)

Labuladong particularly emphasizes two modes of recursive thinking for tree-like problems, which also extend to general recursion and [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming]] / [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking]]:

1.  **"Traversal" Thinking (遍历思维):**
    - Focus: Iterating through the structure (e.g., nodes of a tree, states in a search space).
    - Mechanism: A recursive function (often `void` or returning `None`) that explores. Results are accumulated in external variables or via side effects.
    - Analogy: A "visitor" pattern. The function `traverse(node, state_params...)` carries information along.
    - Examples: [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Standard tree traversals (pre/in/post)]] for collecting node values, [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|backtracking algorithms]].
    - This mode is about "what to do at each node/state."

2.  **"Decomposition" Thinking (分解问题思维 / Divide and Conquer):**
    - Focus: Defining what the recursive function computes and *returns* for a given subproblem. The solution for a larger problem is built from solutions of smaller subproblems.
    - Mechanism: A recursive function that `returns` a meaningful result for the subproblem it solves.
    - Analogy: [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer]]. The function `solve(sub_problem_input)` returns the solution for that input.
    - Examples: Calculating tree height (`maxDepth(node) = 1 + max(maxDepth(left), maxDepth(right))`), many [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming]] solutions.
    - This mode is about "how to combine results from subproblems."

These two modes are further detailed in [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursion - One Perspective, Two Thinking Modes]] and applied extensively in [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree - Core Algorithmic Principles]].

## 总结 (Summary)
- Master data structures by understanding their array/linked-list foundations and traversal/access patterns.
- Approach algorithms by first considering how to exhaustively search, then how to optimize that search by eliminating redundancy and leveraging problem-specific information.
- For recursive problems (especially on trees or implicit graphs), employ "traversal" thinking or "decomposition" thinking to structure your solution.
- This framework-based approach aims to make algorithm design more systematic and less about "aha!" moments for every new problem.

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
Related: [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursion Thinking Modes]], [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]], [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming Framework]], [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking Framework]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/00 - Framework Thinking (Labuladong Philosophy).md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/00 - Framework Thinking (Labuladong Philosophy).md")

def update_backtracking_core_framework(path):
    content = r"""---
tags: [concept/algorithms, concept/backtracking, concept/dfs, type/framework, pattern/recursion]
aliases: [Backtracking Algorithm Framework, 回溯算法框架, DFS for Backtracking]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/回溯算法解题套路框架.md]].
> This note outlines the general framework for solving problems using the backtracking algorithm.

# Backtracking: Core Framework

Backtracking is an algorithmic technique for solving problems recursively by trying to build a solution incrementally, one piece at a time, removing those solutions that fail to satisfy the constraints of the problem at any point in time (this "removing" is the "backtracking").

Abstractly, solving a backtracking problem is like traversing a **decision tree**. Each leaf node of this tree represents a potential complete solution. The goal is to explore this tree to find all (or some) valid solutions.

## 🌲 The Decision Tree Perspective

When you are at a node in the decision tree, you need to consider three things:

1.  **Path (路径):** The sequence of choices you have made so far to reach the current node.
2.  **Choices List (选择列表):** The set of available choices you can make from the current node to move to the next level of the tree.
3.  **End Condition (结束条件):** The condition that signifies you've reached a leaf node (i.e., a decision path is complete) and can no longer make choices.

## 🛠️ The Generic Backtracking Algorithm Pseudocode

The core of a backtracking algorithm is a recursive function. The general structure is:

```python
result = [] # To store all valid solutions

def backtrack(path, choices_list):
    if is_solution(path): # End condition met
        result.append(copy(path)) # Add a copy of the current path to results
        return

    for choice in choices_list:
        # 1. Make a choice
        path.add(choice)
        # (Update choices_list for the next step, e.g., remove 'choice')
        
        # 2. Recurse with the new path and updated choices
        backtrack(path, updated_choices_list)
        
        # 3. Undo the choice (Backtrack)
        path.remove(choice)
        # (Restore choices_list if necessary, e.g., add 'choice' back)
```

**Key Idea:** The magic happens in the `for` loop. Before the recursive call (`backtrack(...)`), we "make a choice." After the recursive call returns, we "undo the choice." This allows the algorithm to explore different branches of the decision tree.

### Understanding "Make Choice" and "Undo Choice"

These actions are performed at specific times during the traversal of the (conceptual) decision tree, analogous to pre-order and post-order traversal in a standard tree DFS.

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=1cm, font=\sffamily},
    edge_label/.style={font=\sffamily\scriptsize, midway},
    action_box/.style={rectangle, draw, rounded corners, fill=yellow!20, font=\sffamily\tiny, align=center}
]

\node[treenode] (root) at (0,0) {Root};
\node[treenode] (child1) at (-2,-2) {Child 1};
\node[treenode] (child2) at (2,-2) {Child 2};

\draw[->] (root) -- (child1) node[edge_label, left] {Choice A};
\draw[->] (root) -- (child2) node[edge_label, right] {Choice B};

% Actions for Child 1 branch
\node[action_box, below left=0.1cm and -0.5cm of child1, text width=2.5cm] (make_A) {
    \textbf{Make Choice A:}\\
    `path.add(A)`\\
    `used[A] = true` (if applicable)
};
\draw[->, dashed, blue] (root.south west) -- (make_A.north);

\node[action_box, below right=0.1cm and -0.5cm of child1, text width=2.5cm] (undo_A) {
    \textbf{Undo Choice A (Backtrack):}\\
    `path.remove(A)`\\
    `used[A] = false` (if applicable)
};
\node at ($(child1.south) + (0,-0.5)$) {`backtrack(path_with_A, ...)`};
\draw[->, dashed, red] ($(child1.south) + (0,-1)$) -- (undo_A.north);


\node at (0, -4.5) [text width=8cm, align=center, draw, fill=gray!10, rounded corners]
    {
        When `backtrack` is called for a child node (e.g., after choosing 'A'), the `path` reflects this choice.
        After the recursive call returns (meaning the entire subtree under 'A' has been explored),
        'A' is removed from `path` to allow exploration of other choices (like 'B').
    };

\end{tikzpicture}
```
- **Pre-order position (Make Choice):** Before recursively exploring a child, update the `path` and any state variables (like `used` elements) to reflect the choice made to reach that child.
- **Post-order position (Undo Choice):** After the recursive call for a child returns (meaning that entire branch of the decision tree has been explored), revert the `path` and state variables to what they were *before* making that choice. This is crucial for exploring other branches correctly.

## Example: Permutations (LC46)

Let's illustrate with LeetCode 46: [[Interview/Practice/LeetCode/LC46 - Permutations|Permutations]]. Given `nums = [1,2,3]`, find all permutations.

The decision tree looks like this:
![](/algo/images/backtracking/1.jpg)
*(Source: Labuladong's article - Visual representation of the decision tree for permuting [1,2,3])*

- **Path:** The current permutation being built (e.g., `[1, 2]`).
- **Choices List:** Numbers from `nums` not yet in the `path`.
- **End Condition:** `len(path) == len(nums)`.

**Python Solution (Illustrative, for full solution see [[Interview/Practice/LeetCode/LC46 - Permutations|LC46]]):**
```python
class Solution:
    def permute(self, nums: list[int]) -> list[list[int]]:
        result = []
        track = [] # Current path
        used = [False] * len(nums) # To keep track of used numbers

        def backtrack_permute():
            if len(track) == len(nums): # End condition
                result.append(track.copy()) # Add a copy of the path
                return

            for i in range(len(nums)):
                if used[i]:
                    # nums[i] is already in track, skip
                    ![](/algo/images/backtracking/6.jpg) # Visual cue for pruning
                    continue
                
                # Make choice
                track.append(nums[i])
                used[i] = True
                
                backtrack_permute() # Recurse
                
                # Undo choice
                track.pop()
                used[i] = False
        
        backtrack_permute()
        return result

```
In this permutation example:
- "Making a choice" means adding `nums[i]` to `track` and marking `used[i] = True`.
- "Undoing a choice" means removing `nums[i]` from `track` (via `pop()`) and setting `used[i] = False`.

## 总结 (Summary)

1.  **Decision Tree:** Visualize the problem as exploring a decision tree.
2.  **Identify:**
    *   **Path:** What represents the choices made so far?
    *   **Choices List:** What are the available options at the current step?
    *   **End Condition:** When is a solution complete?
3.  **Framework:**
    ```python
    def backtrack(path, choices_list):
        if is_solution(path):
            add_to_results(path)
            return
        for choice in choices_list:
            make_choice(path, choice)
            backtrack(new_path, updated_choices_list)
            undo_choice(path, choice)
    ```
4.  **Efficiency:** Backtracking often involves exploring many possibilities, so its time complexity can be high (e.g., $O(N!)$ for permutations). Pruning (intelligently avoiding exploration of unpromising branches) can sometimes optimize it, but the core is exhaustive search.

Understanding this framework allows you to tackle a wide range of problems involving permutations, combinations, subsets, and other search-based puzzles.
For more detailed patterns on permutations, combinations, and subsets, see [[Interview/Concept/Algorithms/Backtracking/Patterns/01 - Backtracking for Permutations, Combinations, Subsets|Backtracking for Permutations, Combinations, Subsets]].

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
Next: [[Interview/Concept/Algorithms/Backtracking/Patterns/01 - Backtracking for Permutations, Combinations, Subsets|Backtracking for Permutations, Combinations, Subsets]]
Related: [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursion Thinking Modes]], [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Tree DFS]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Backtracking"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Updated: Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework.md")

def create_backtracking_patterns_permutations_combinations_subsets(path):
    content = r"""---
tags: [concept/algorithms, concept/backtracking, pattern/permutations, pattern/combinations, pattern/subsets, type/problem_solving_pattern]
aliases: [Permutations Combinations Subsets using Backtracking, 排列组合子集问题回溯法]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/回溯算法秒杀所有排列_组合_子集问题.md]] and related problem pages.
> This note details how to solve permutation, combination, and subset problems using the [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|backtracking framework]].

# Backtracking for Permutations, Combinations, and Subsets

Problems involving permutations, combinations, and subsets are common in interviews and are classic applications of the backtracking algorithm. These problems require generating all possible arrangements or selections of elements from a given set, often with specific constraints.

## 🌲 Core Idea: Decision Tree Exploration

All these problems can be solved by systematically exploring a decision tree where:
- Each level of the tree represents a decision point (e.g., choosing an element).
- Paths from the root to a node represent a partial construction (e.g., a partial permutation).
- Leaf nodes represent complete, valid solutions.

The [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|backtracking framework]] is used to traverse this decision tree. The main variations lie in how "choices" are made and how "paths" (current selections) are managed.

## Key Problem Variations

Labuladong categorizes these problems based on two main factors:
1.  **Uniqueness of elements in input `nums`**:
    - Elements are unique.
    - Elements can have duplicates.
2.  **Reusability of elements**:
    - Each element can be used at most once (no re-selection of the same element instance).
    - Each element can be used multiple times (re-selection allowed).

This leads to several common forms. We'll focus on adapting the backtracking framework for each.

### Visualization of Decision Trees

**1. Subsets / Combinations Tree (Conceptual for `nums=[1,2,3]`):**
   Each element can either be included or excluded.
   ![](/algo/images/permutation/1.jpeg)
   *(Source: Labuladong. This tree structure is typical for subset and combination problems. The path from root to any node represents a subset/combination. Ordering of elements in the input `nums` matters for how the tree is built if we want to avoid duplicate combinations.)*

**2. Permutations Tree (Conceptual for `nums=[1,2,3]`):**
   The order of chosen elements matters.
   ![](/algo/images/permutation/2.jpeg)
   *(Source: Labuladong. This tree structure is for permutation problems. Each path from root to a leaf is a full permutation.)*

## General Backtracking Structure for These Problems

```python
def solve(nums, ...):
    results = []
    current_path = []

    def backtrack(start_index, current_path, ...other_params): # start_index is common for combinations/subsets
        # 1. End Condition / Add to results
        if should_add_to_results(current_path, ...):
            results.append(current_path.copy())
            # For some problems (like subsets), every path is a solution.
            # For others (like k-combinations), only paths of length k.
            # For permutations, only full-length paths.
            # May or may not return here depending on problem (e.g. permutations require full length)

        if should_stop_exploring_this_branch(current_path, ...):
            return

        # 2. Iterate through choices
        # The 'choices' depend on the problem type (permutations vs combinations/subsets)
        # and constraints (uniqueness, reusability).
        for i in range(start_index, len(nums)): # Common for combinations/subsets to avoid duplicates
                                                # For permutations, usually iterate 0 to len(nums) and use 'used' array.
            # Optional: Pruning for duplicates in input if elements are sorted
            # if i > start_index and nums[i] == nums[i-1] and ...:
            #     continue 

            # Make choice
            current_path.append(nums[i])
            # For permutations with 'used' array: used[i] = True

            # Recurse
            # Next start_index for combinations: i + 1 (cannot reuse current element)
            # Next start_index for combinations with reusability: i (can reuse current element)
            # For permutations, pass down the full nums and updated 'used' array.
            backtrack(next_start_index_or_same, current_path, ...) 

            # Undo choice
            current_path.pop()
            # For permutations with 'used' array: used[i] = False
            
    # Initial call
    # Sort nums first if dealing with duplicates to simplify pruning.
    # if has_duplicates_and_no_reuse: nums.sort()
    backtrack(0, current_path, ...)
    return results
```

Let's break down specifics for each type.

---
### 🧩 I. Subsets (子集)

**Problem:** Find all possible subsets of a given set of numbers.
**Example:** `nums = [1,2,3]` $\rightarrow$ `[[], [1], [2], [3], [1,2], [1,3], [2,3], [1,2,3]]`

#### A. Elements Unique, No Reuse (LC78. Subsets)

- Each element can either be in a subset or not.
- The decision tree branches on "include `nums[i]`" or "exclude `nums[i]`".
- A common way to implement is to iterate, and for each element, decide whether to add it to the current subset. To avoid duplicate subsets (like `[1,2]` and `[2,1]` if order didn't matter), we process elements in order.

```python
# [[Interview/Practice/LeetCode/LC78 - Subsets|LC78 - Subsets]]
class Solution_LC78:
    def subsets(self, nums: list[int]) -> list[list[int]]:
        results = []
        current_subset = []

        def backtrack(start_index):
            # Add the subset formed by the current path
            results.append(current_subset.copy())

            # Choices: elements from start_index to end
            for i in range(start_index, len(nums)):
                # Make choice: include nums[i]
                current_subset.append(nums[i])
                # Recurse: explore subsets starting with nums[i]
                # Next element to consider is nums[i+1]
                backtrack(i + 1)
                # Undo choice: exclude nums[i] (backtrack)
                current_subset.pop()
        
        backtrack(0)
        return results
```
- `start_index` ensures that we only consider elements at or after `start_index`, preventing re-selection of earlier elements in a different order, thus generating unique subsets.
- Every node in the decision tree represents a valid subset.

#### B. Elements Can Have Duplicates, No Reuse (LC90. Subsets II)

- If `nums = [1,2,2]`, subsets are `[], [1], [2], [1,2], [2,2], [1,2,2]`. `[2]` from first '2' is same as `[2]` from second '2'.
- **Strategy:** Sort `nums` first. Then, in the `for` loop for choices, if `nums[i]` is the same as `nums[i-1]` and we *didn't* pick `nums[i-1]` in the *previous level of decision* (i.e., `i > start_index`), skip `nums[i]`. This avoids duplicate subsets caused by identical elements.

```python
# [[Interview/Practice/LeetCode/LC90 - Subsets II|LC90 - Subsets II]]
class Solution_LC90:
    def subsetsWithDup(self, nums: list[int]) -> list[list[int]]:
        results = []
        current_subset = []
        nums.sort() # Crucial for duplicate handling

        def backtrack(start_index):
            results.append(current_subset.copy())

            for i in range(start_index, len(nums)):
                # Pruning: if current element is same as previous,
                # and previous was NOT chosen in this path (i > start_index means
                # we are considering a new element at the current depth, not recursing deeper),
                # skip to avoid duplicate subsets.
                if i > start_index and nums[i] == nums[i-1]:
                    continue
                
                current_subset.append(nums[i])
                backtrack(i + 1)
                current_subset.pop()
        
        backtrack(0)
        return results
```

---
### 🔄 II. Combinations (组合)

**Problem:** Find all combinations of `k` numbers from `n` numbers (e.g., `nums = [1,2,3,4]`, `k=2`).
**Example:** `nums = [1,2,3,4], k=2` $\rightarrow$ `[[1,2], [1,3], [1,4], [2,3], [2,4], [3,4]]`
- Combinations are essentially subsets of a fixed size `k`. The logic is very similar to subsets.

#### A. Elements Unique, No Reuse, Fixed Size `k` (LC77. Combinations)

```python
# [[Interview/Practice/LeetCode/LC77 - Combinations|LC77 - Combinations]]
class Solution_LC77:
    def combine(self, n: int, k: int) -> list[list[int]]:
        # nums would be [1, 2, ..., n]
        nums = list(range(1, n + 1))
        results = []
        current_combination = []

        def backtrack(start_index):
            # End condition: combination has reached size k
            if len(current_combination) == k:
                results.append(current_combination.copy())
                return

            # Pruning: if remaining elements are not enough to form a k-sized combination
            # (len(nums) - i) < (k - len(current_combination))
            # i.e., nums_to_pick_from < nums_needed
            # if (len(nums) - start_index) < (k - len(current_combination)):
            #     return

            for i in range(start_index, len(nums)):
                # Optimization: if we pick nums[i], we need k - len(current_combination) - 1 more elements.
                # We must have enough elements left: len(nums) - 1 - i >= k - len(current_combination) - 1
                # This means: len(nums) - i >= k - len(current_combination)
                if (len(nums) - i) < (k - len(current_combination)):
                    break # Further elements won't make it either.

                current_combination.append(nums[i])
                backtrack(i + 1) # Next element must be > current, so i + 1
                current_combination.pop()
        
        backtrack(0)
        return results
```
- The end condition checks `len(current_combination) == k`.

#### B. Elements Can Have Duplicates, No Reuse, Target Sum (LC40. Combination Sum II)

- Given `candidates` (may have duplicates) and `target`. Find unique combinations summing to `target`. Each number used once.
- **Strategy:** Sort `candidates`. Use `start_index` for `backtrack`. Skip duplicates as in Subsets II.

```python
# [[Interview/Practice/LeetCode/LC40 - Combination Sum II|LC40 - Combination Sum II]]
class Solution_LC40:
    def combinationSum2(self, candidates: list[int], target: int) -> list[list[int]]:
        results = []
        current_combination = []
        candidates.sort()

        def backtrack(start_index, current_sum):
            if current_sum == target:
                results.append(current_combination.copy())
                return
            if current_sum > target: # Pruning
                return

            for i in range(start_index, len(candidates)):
                # Skip duplicates: if current element is same as previous,
                # and we are not at the very first element of this level's choices (i > start_index)
                if i > start_index and candidates[i] == candidates[i-1]:
                    continue
                
                # Pruning: if current candidate makes sum exceed target
                if current_sum + candidates[i] > target:
                    break # Since candidates is sorted, further elements will also exceed

                current_combination.append(candidates[i])
                backtrack(i + 1, current_sum + candidates[i]) # i + 1: cannot reuse same element instance
                current_combination.pop()

        backtrack(0, 0)
        return results
```

#### C. Elements Unique, Can Reuse, Target Sum (LC39. Combination Sum)

- Each number can be used multiple times.
- **Strategy:** When recursing, `backtrack(i, ...)` instead of `backtrack(i + 1, ...)` . This allows `candidates[i]` to be chosen again.

```python
# [[Interview/Practice/LeetCode/LC39 - Combination Sum|LC39 - Combination Sum]]
class Solution_LC39:
    def combinationSum(self, candidates: list[int], target: int) -> list[list[int]]:
        results = []
        current_combination = []
        # No need to sort if elements are unique and can be reused, 
        # but sorting can help with pruning if done carefully.
        # For this standard version, sorting isn't strictly necessary but often done.
        # nums.sort() 

        def backtrack(start_index, current_sum):
            if current_sum == target:
                results.append(current_combination.copy())
                return
            if current_sum > target: # Pruning
                return

            for i in range(start_index, len(candidates)):
                # No duplicate skipping needed if elements are unique.
                # If candidates could have duplicates AND reuse, sorting + skipping would be needed.
                
                # Pruning: if current candidate makes sum exceed target
                # This pruning is more effective if candidates are sorted.
                # if current_sum + candidates[i] > target:
                #     if candidates_are_sorted: break 
                #     else: continue 

                current_combination.append(candidates[i])
                # Key difference: backtrack(i, ...) allows reusing candidates[i]
                backtrack(i, current_sum + candidates[i]) 
                current_combination.pop()
        
        backtrack(0, 0)
        return results
```

---
### 🔢 III. Permutations (排列)

**Problem:** Find all possible orderings of a given set of numbers.
**Example:** `nums = [1,2,3]` $\rightarrow$ `[[1,2,3], [1,3,2], [2,1,3], [2,3,1], [3,1,2], [3,2,1]]`

#### A. Elements Unique, No Reuse (LC46. Permutations)

- **Strategy:** Use a `used` boolean array to keep track of which elements from `nums` have been included in the `current_permutation`. Iterate `0..len(nums)-1` for choices at each step.

```python
# [[Interview/Practice/LeetCode/LC46 - Permutations|LC46 - Permutations]]
class Solution_LC46:
    def permute(self, nums: list[int]) -> list[list[int]]:
        results = []
        current_permutation = []
        used = [False] * len(nums)

        def backtrack():
            if len(current_permutation) == len(nums):
                results.append(current_permutation.copy())
                return

            for i in range(len(nums)):
                if used[i]:
                    continue # Element already used
                
                current_permutation.append(nums[i])
                used[i] = True
                
                backtrack()
                
                used[i] = False
                current_permutation.pop()
        
        backtrack()
        return results
```

#### B. Elements Can Have Duplicates, No Reuse (LC47. Permutations II)

- If `nums = [1,1,2]`, permutations: `[1,1,2], [1,2,1], [2,1,1]`.
- **Strategy:** Sort `nums` first. Use a `used` array. Add a condition to skip duplicates: if `nums[i] == nums[i-1]` and `used[i-1]` is `False` (meaning `nums[i-1]` was chosen, used in a permutation, and then *backtracked* / unused for the current exploration path at this level), then skip `nums[i]`. This ensures that for a set of identical numbers, they are picked in a fixed relative order within the permutation generation process to avoid duplicates.

```python
# [[Interview/Practice/LeetCode/LC47 - Permutations II|LC47 - Permutations II]]
class Solution_LC47:
    def permuteUnique(self, nums: list[int]) -> list[list[int]]:
        results = []
        current_permutation = []
        nums.sort() # Crucial for duplicate handling
        used = [False] * len(nums)

        def backtrack():
            if len(current_permutation) == len(nums):
                results.append(current_permutation.copy())
                return

            for i in range(len(nums)):
                if used[i]:
                    continue
                
                # Pruning for duplicates:
                # If current num is same as previous, AND previous num is NOT used yet (used[i-1] == False),
                # it means the permutation starting with previous num (which is same as current) has already
                # been generated and fully explored (that's why used[i-1] is false now).
                # So, we skip current num to avoid duplicate permutations.
                if i > 0 and nums[i] == nums[i-1] and not used[i-1]: # Important: not used[i-1]
                    continue
                
                current_permutation.append(nums[i])
                used[i] = True
                
                backtrack()
                
                used[i] = False
                current_permutation.pop()
                
        backtrack()
        return results
```
**Explanation of `not used[i-1]` for Permutations II:**
When `nums[i] == nums[i-1]`:
- If `used[i-1]` is `True`, it means `nums[i-1]` is part of the *current* `current_permutation` being built. We are allowed to pick `nums[i]` (the second identical number).
- If `used[i-1]` is `False`, it means `nums[i-1]` was considered at this level of decision (or an earlier identical number), included in some permutations, and then backtracked (marked as unused). Now, if we consider `nums[i]` (which is identical to `nums[i-1]`), we would generate permutations that are duplicates of those already generated starting with `nums[i-1]`. So we skip. This ensures identical elements are picked in their original sorted order.

## 总结 (Summary)

- **Subsets & Combinations:** Generally use a `start_index` parameter in `backtrack(start_index, ...)` to ensure elements are picked in order and avoid duplicates (for combinations) or redundant explorations.
    - To allow reuse of elements (e.g., Combination Sum): recurse with `backtrack(i, ...)` (same index).
    - To not allow reuse: recurse with `backtrack(i + 1, ...)` (next index).
- **Permutations:** Generally iterate through all elements `0..len(nums)-1` at each step and use a `used` array to track elements already in the current path.
- **Handling Duplicates in Input (`nums`):**
    - **Always sort `nums` first.**
    - **Subsets/Combinations (no reuse):** If `i > start_index and nums[i] == nums[i-1]`, skip `nums[i]`.
    - **Permutations (no reuse):** If `i > 0 and nums[i] == nums[i-1] and not used[i-1]`, skip `nums[i]`.

Mastering these distinctions and the core backtracking template allows for a unified approach to this entire class of problems. The key is careful management of the "choices list" (often implicit via `start_index` or `used` array) and the conditions for pruning duplicate solution branches.

---
Parent: [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking - Core Framework]]
Related LeetCode Problems:
- [[Interview/Practice/LeetCode/LC78 - Subsets|LC78 - Subsets]]
- [[Interview/Practice/LeetCode/LC90 - Subsets II|LC90 - Subsets II]] (Placeholder)
- [[Interview/Practice/LeetCode/LC77 - Combinations|LC77 - Combinations]] (Placeholder)
- [[Interview/Practice/LeetCode/LC39 - Combination Sum|LC39 - Combination Sum]] (Placeholder)
- [[Interview/Practice/LeetCode/LC40 - Combination Sum II|LC40 - Combination Sum II]] (Placeholder)
- [[Interview/Practice/LeetCode/LC216 - Combination Sum III|LC216 - Combination Sum III]] (Placeholder)
- [[Interview/Practice/LeetCode/LC46 - Permutations|LC46 - Permutations]]
- [[Interview/Practice/LeetCode/LC47 - Permutations II|LC47 - Permutations II]] (Placeholder)
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Backtracking/Patterns"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Backtracking/Patterns/01 - Backtracking for Permutations, Combinations, Subsets.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Backtracking/Patterns/01 - Backtracking for Permutations, Combinations, Subsets.md")

def update_backtracking_index(path):
    content = r"""---
tags: [index, concept/algorithms, concept/backtracking, pattern/recursion]
aliases: [Backtracking Index, Backtracking Algorithms]
---

# Backtracking Algorithms

This section covers the backtracking algorithmic paradigm, a refined approach to brute-force search, often implemented recursively. It's used for problems that involve making a sequence of choices to find all (or some) solutions satisfying certain constraints.

## Core Concepts & Framework:
- [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking - Core Framework]]
  - Decision Tree Perspective
  - Path, Choices List, End Condition
  - Make Choice & Undo Choice (Backtrack)

## Common Patterns & Applications:
- [[Interview/Concept/Algorithms/Backtracking/Patterns/01 - Backtracking for Permutations, Combinations, Subsets|Backtracking for Permutations, Combinations, and Subsets]]
  - Subsets (e.g., [[Interview/Practice/LeetCode/LC78 - Subsets|LC78]], [[Interview/Practice/LeetCode/LC90 - Subsets II|LC90]])
  - Combinations (e.g., [[Interview/Practice/LeetCode/LC77 - Combinations|LC77]], [[Interview/Practice/LeetCode/LC39 - Combination Sum|LC39]], [[Interview/Practice/LeetCode/LC40 - Combination Sum II|LC40]])
  - Permutations (e.g., [[Interview/Practice/LeetCode/LC46 - Permutations|LC46]], [[Interview/Practice/LeetCode/LC47 - Permutations II|LC47]])
- Solving Puzzles (e.g., N-Queens, Sudoku)
- Pathfinding in graphs/matrices (e.g., finding all paths)

## Visualization
```mermaid
graph TD
    BTConcept["Backtracking"] --> Framework["[[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Core Framework]]"]
    Framework --> DecisionTree["Decision Tree Model"]
    Framework --> PathChoicesEnd["Path, Choices, End Condition"]
    Framework --> MakeUndo["Make/Undo Choice"]

    BTConcept --> Patterns["Common Patterns"]
    Patterns --> PermCombSub["[[Interview/Concept/Algorithms/Backtracking/Patterns/01 - Backtracking for Permutations, Combinations, Subsets|Permutations, Combinations, Subsets]]"]
    Patterns --> Puzzles["Solving Puzzles (N-Queens, Sudoku)"]
    Patterns --> Pathfinding["Pathfinding"]
    
    PermCombSub --> LC46["[[Interview/Practice/LeetCode/LC46 - Permutations|LC46 - Permutations]] (Example)"]

    classDef main fill:#fce8b2,stroke:#f39c12,stroke-width:2px;
    class BTConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
Related: [[Interview/Concept/Algorithms/Recursion/index|Recursion Strategies]], [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|Graph DFS]] (as DFS is closely related)
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Backtracking"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Backtracking/index.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Updated: Interview/Concept/Algorithms/Backtracking/index.md")

def update_complexity_analysis_intro(path):
    content = r"""---
tags: [concept/algorithms, concept/complexity_analysis, type/introduction, concept/big_o_notation]
aliases: [Time Complexity Introduction, Space Complexity Introduction, Big O Notation, 算法复杂度分析指南, 算法时空复杂度分析实用指南]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/算法时空复杂度分析实用指南.md]].
> This note covers the basics of Big O notation and how to approach complexity analysis, incorporating practical advice.

# Algorithm Time and Space Complexity: A Practical Guide

Understanding how to analyze the time and space complexity of algorithms is a fundamental skill for software engineers. It helps in choosing efficient solutions and predicting performance. This guide focuses on practical aspects, especially using Big O notation.

## 💡 Why Complexity Analysis Matters

- **Predict Performance:** Estimate how an algorithm will perform as input size grows.
- **Compare Algorithms:** Objectively compare different solutions to the same problem.
- **Identify Bottlenecks:** Pinpoint parts of code that might be slow or consume too much memory.
- **Reverse Engineering Problems (Labuladong's Insight):** The constraints on input size (e.g., `N <= 10^5`) can strongly hint at the required time complexity (e.g., $O(N \log N)$ or $O(N)$ for $N=10^5$), guiding your choice of algorithm and helping avoid approaches that would be too slow.
    - If `N` is very small (e.g., `N <= 20`), an exponential solution like $O(2^N)$ or $O(N!)$ (often [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|backtracking]]) might be acceptable.
    - If `N` is around $10^3$, $O(N^2)$ might pass.
    - If `N` is $10^5$ to $10^6$, $O(N \log N)$ or $O(N)$ is usually required. For such inputs, $O(N^2)$ would likely result in a Time Limit Exceeded (TLE) error.

## Big O Notation: The Language of Complexity

Big O notation describes the **asymptotic upper bound** of an algorithm's resource usage (time or space) as the input size ($N$) approaches infinity. It focuses on the growth rate, abstracting away machine-specific constants and lower-order terms.

### Key Principles of Big O

1.  **Ignore Constant Factors:**
    - $O(2N)$ is simplified to $O(N)$.
    - $O(100)$ (a constant number of operations) is $O(1)$.
    - $O(N/2)$ is $O(N)$.
    The constant `c` in the formal definition $f(n) \le c \cdot g(n)$ accounts for these.

2.  **Keep Only the Fastest-Growing Term (Dominant Term):**
    - $O(N^2 + N + 100)$ becomes $O(N^2)$.
    - $O(N + \log N)$ becomes $O(N)$.
    - $O(2^N + N^3)$ becomes $O(2^N)$.
    As $N$ grows large, the term with the highest growth rate dominates the overall complexity.

3.  **Logarithms:**
    - The base of the logarithm usually doesn't matter in Big O notation: $O(\log_2 N) = O(\log_{10} N) = O(\ln N)$, all commonly written as $O(\log N)$. This is because $\log_a N = \frac{\log_b N}{\log_b a}$, and $1/\log_b a$ is a constant factor.
    - Logarithmic complexity often arises in algorithms that repeatedly divide the problem size (e.g., [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|binary search]], balanced tree operations, [[Interview/Concept/Algorithms/Sorting/Merge Sort|merge sort]]).

4.  **Multiple Variables:** If complexity depends on multiple input sizes (e.g., $M$ and $N$), include all relevant variables: $O(M+N)$, $O(M \cdot N)$.

### Common Complexity Classes (Growth Order)

Slowest $\rightarrow$ Fastest Growth (Higher means less efficient for large inputs):
- **$O(1)$ (Constant):** Execution time is independent of input size. E.g., accessing an array element by index, arithmetic operations.
- **$O(\log N)$ (Logarithmic):** Time increases logarithmically with input size. Very efficient. E.g., binary search, operations on balanced BSTs.
- **$O(N)$ (Linear):** Time increases linearly with input size. E.g., iterating through an array once, finding max element.
- **$O(N \log N)$ (Log-linear or Linearithmic):** Common for efficient sorting algorithms. E.g., merge sort, heap sort, quick sort (average).
- **$O(N^2)$ (Quadratic):** Time increases with the square of input size. E.g., nested loops iterating $N$ times each, simple sorting like bubble sort/selection sort.
- **$O(N^3)$ (Cubic):** E.g., triple nested loops.
- **$O(2^N)$ (Exponential):** Time grows very rapidly. Often seen in brute-force solutions to combinatorial problems (e.g., generating all subsets without optimization).
- **$O(N!)$ (Factorial):** Even faster growth. E.g., generating all permutations by brute force.

```tikz
\begin{tikzpicture}[font=\sffamily\small]
\begin{axis}[
    title={Common Complexity Growth Rates},
    xlabel={Input Size (N)},
    ylabel={Operations (Time)},
    xmin=1, xmax=15, % Adjusted xmax for better visualization of faster growing functions
    ymin=0, ymax=60, % Adjusted ymax
    legend pos=north west,
    grid=major,
    axis lines=left,
    xtick={1,5,10,15},
    ytick={0,10,20,30,40,50,60},
    no markers, 
    samples=100, % Increased samples for smoother curves
]

\addplot [domain=1:15, blue, thick, very thick] {5} node[right, pos=0.8, font=\tiny] {$O(1)$}; % Constant, scaled for visibility
\addplot [domain=1:15, red, thick] {ln(x)/ln(2) * 2.5} node[above, pos=0.7, font=\tiny] {$O(\log N)$}; % Scaled log N
\addplot [domain=1:15, green, thick] {x} node[above right, pos=0.9, font=\tiny] {$O(N)$};
\addplot [domain=1:15, orange, thick] {x*ln(x)/ln(2)} node[above, pos=0.8, font=\tiny] {$O(N \log N)$};
\addplot [domain=1:15, purple, thick, samples=16, smooth] {x^2 / 4} node[above right, pos=0.9, font=\tiny] {$O(N^2)$}; % Scaled N^2

% Note: Exponential O(2^N) and Factorial O(N!) grow too fast for this N range and ymax.
% To show O(2^N), one might plot for N up to 5 or 6 with a much larger ymax.
% Example for O(2^N): \addplot [domain=1:6, brown, thick, samples=6, smooth] {2^x};
\end{axis}
\node at (current bounding box.south) [below=5pt, text width=10cm, align=center, font=\sffamily\scriptsize]
    {Illustrative graph. Actual function shapes and crossing points depend on constant factors hidden by Big O notation. The key is the relative growth rate as N increases.};
\end{tikzpicture}
```

### Big O is an Upper Bound (Asymptotic Upper Bound)
Technically, $O(g(N))$ means the algorithm's complexity is *no worse than* $c \cdot g(N)$ for sufficiently large $N$. So, an $O(N)$ algorithm is also correctly described as $O(N^2)$, but we always strive to find the "tightest" reasonable upper bound. For example, a brute-force recursive solution to Fibonacci is $O(2^N)$. While $O(N!)$ is also an upper bound, $O(2^N)$ is a tighter and more informative one.

## 🛠️ Analyzing Non-Recursive Algorithms

### Time Complexity
1.  **Sequence of Statements:** The total time is the sum of the times for individual statements. In Big O, this becomes the maximum complexity among them. $O(A) + O(B) = O(\max(A, B))$.
2.  **Loops:**
    - Complexity of the loop body $\times$ Number of iterations.
    - Example: `for i in range(N): # O(1) operation` is $O(1) \times N = O(N)$.
    - Example: Nested loops
      ```python
      # for i in range(N):       # N iterations
      #     for j in range(M):   # M iterations
      #         # O(1) operation
      ```
      This is $N \times M \times O(1) = O(NM)$. If $M=N$, it's $O(N^2)$.
    - Example: Dependent nested loops
      ```python
      # for i in range(N):       # Outer loop N times
      #     for j in range(i): # Inner loop runs 0, 1, ..., N-1 times
      #         # O(1) operation
      ```
      Total operations: $0 + 1 + 2 + ... + (N-1) = \frac{N(N-1)}{2} = O(N^2)$.
3.  **Conditional Statements (`if-else`):** Take the complexity of the branch with the worst-case (highest) complexity.

### Space Complexity
Refers to the *auxiliary* (or extra) space used by the algorithm, not including the space taken by the input itself.
- Simple variables (integers, booleans, pointers): Usually $O(1)$ each.
- Data Structures: Space used by lists, hash maps, sets, etc., created by the algorithm.
    - E.g., creating a hash map that stores $N$ items is $O(N)$ space.
    - E.g., creating a copy of an input array of size $N$ is $O(N)$ space.

## Common Pitfalls Leading to Incorrect Complexity (Labuladong's Reminders)

1.  **Ignoring Cost of Standard Library Functions:**
    - Python `list.insert(0, val)` or `list.pop(0)` (removing from beginning) is $O(N)$, not $O(1)$.
    - Python string concatenation `s += char` in a loop can be $O(N^2)$ because strings are immutable. Each concatenation creates a new string and copies content. Prefer `"".join(list_of_chars)` for $O(N)$ construction.
    - Slicing `my_list[a:b]` creates a new list of size $b-a$, taking $O(b-a)$ time and space.
2.  **Misunderstanding Parameter Passing (especially in C++/Java):**
    - In C++, passing large objects (like `std::vector`) by value causes a full copy ($O(N)$ time and space). Pass by reference (`&`) or const reference (`const &`) if modification is not needed.
    - Python passes object references. Assigning an argument to a new large object inside a function creates that new object. Modifying a mutable object passed as an argument modifies the original.
3.  **Ignoring Standard Output Time (Print Statements):**
    - `print()` or `console.log()` are I/O operations and can be relatively slow. Excessive printing, especially in loops, can cause TLE even if the core algorithm is efficient. Remove debug prints before final submission.
4.  **Incorrect Interface Implementation Assumptions (Java specific example by Labuladong):**
    - If a Java function takes `List<Integer> list`, and your code assumes `list.get(i)` is $O(1)$ (like for `ArrayList`), but the actual passed object is a `LinkedList` (where `get(i)` is $O(N)$), your complexity analysis will be wrong. If performance on `get(i)` is critical, it might be safer to copy the input `List` into an `ArrayList`.

## Analyzing Recursive Algorithms (Brief Introduction)

Recursive algorithm complexity analysis is often more involved and typically uses one of these methods:

1.  **Recurrence Relation:** Express $T(N)$ (time for input size $N$) in terms of $T(\text{smaller } N_k)$ (time for subproblems of smaller sizes).
    - Example: [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]] has $T(N) = 2T(N/2) + O(N)$ (two subproblems of size $N/2$, plus $O(N)$ work to merge).
    - Example: Naive Fibonacci $T(N) = T(N-1) + T(N-2) + O(1)$.
2.  **Solving the Recurrence:**
    - **Master Theorem:** A "cookbook" method for solving recurrences of the form $T(N) = aT(N/b) + f(N)$. (See [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer Framework]]).
    - **Recursion Tree Method:** Visualize the recursive calls as a tree. Sum the work done at each level and across all levels.
        - Example for $T(N) = 2T(N/2) + N$: Tree has $\log N$ levels. Each level does $N$ work. Total $O(N \log N)$.
        - Example for naive Fibonacci: Tree is exponential.
    - **Substitution Method:** Guess a solution form and prove it by mathematical induction.

**Space Complexity of Recursion:**
- Primarily determined by the **maximum depth of the recursion stack**.
- If a recursive call for input $N$ makes a call for $N-1$ (e.g., factorial, some DFS), stack depth can be $O(N)$.
- If it divides the problem by a constant factor (e.g., $N/2$ in merge sort, binary search), stack depth is $O(\log N)$.
- Additional space for local variables within each recursive call also contributes, but stack depth is often the dominant factor for auxiliary space.

These are covered in more detail in notes related to specific recursive paradigms like [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming]], [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking]], and [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer]].

## Amortized Analysis

Amortized analysis considers the average cost of an operation over a sequence of operations, even if some individual operations in the sequence are expensive.
- **Example:** [[Interview/Concept/Data Structures/Array/01 - Dynamic Array - Principles and Amortized Analysis|Dynamic Array]] `append`. Most appends are $O(1)$. When the array is full, a resize operation occurs ($O(N)$ to copy elements to a new, larger array). However, resizing happens infrequently enough that the *average* cost of append over many operations is $O(1)$.
- This is important for understanding the true practical efficiency of data structures like Python's `list` or Java's `ArrayList`.

## 总结 (Summary)
- Complexity analysis, particularly using Big O notation, is crucial for evaluating algorithm efficiency and scalability.
- Big O focuses on the asymptotic growth rate, ignoring constant factors and lower-order terms.
- When analyzing code, be mindful of the cost of loops, nested structures, library function calls, and I/O.
- For non-recursive algorithms, sum complexities for sequential parts and multiply for loops.
- Recursive algorithm analysis typically involves setting up and solving recurrence relations (e.g., via recursion tree or Master Theorem) for time complexity, and considering maximum stack depth for space complexity.
- Amortized analysis provides a more realistic average-case performance for operations with occasional high costs.
- Always consider input constraints to guide your choice of algorithm complexity.

---
Parent: [[Interview/Concept/Algorithms/Complexity Analysis/index|Complexity Analysis Index]]
Next: [[Interview/Concept/Algorithms/Complexity Analysis/01 - Asymptotic Analysis Guide|Asymptotic Analysis Guide]] (for more formal details, Master Theorem, etc.)
Related: [[Interview/Practice/LeetCode/00 - LeetCode Platform Guide and Tips|LeetCode Guide]] (for context on performance limits in online judges)
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Complexity Analysis"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Complexity Analysis/00 - Introduction to Time and Space Complexity.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Updated: Interview/Concept/Algorithms/Complexity Analysis/00 - Introduction to Time and Space Complexity.md")

def create_complexity_analysis_index(path):
    content = r"""---
tags: [index, concept/algorithms, concept/complexity_analysis]
aliases: [Complexity Analysis Index, Big O Index]
---

# Complexity Analysis

This section covers concepts related to analyzing the time and space complexity of algorithms.

## Core Concepts:
- [[Interview/Concept/Algorithms/Complexity Analysis/00 - Introduction to Time and Space Complexity|Introduction to Time and Space Complexity]]
  - Big O Notation Basics
- `[[Interview/Concept/Algorithms/Complexity Analysis/01 - Asymptotic Analysis Guide|Asymptotic Analysis Guide]]` (Placeholder for detailed guide)
  - Formal Definitions (Big O, Big Omega, Big Theta)
  - Analyzing Loops and Recursion
  - Amortized Analysis

## Visualization
```mermaid
graph TD
    CA["Complexity Analysis"] --> Intro["[[Interview/Concept/Algorithms/Complexity Analysis/00 - Introduction to Time and Space Complexity|Introduction]]"]
    CA --> Guide["(Detailed Asymptotic Analysis Guide)"]
    
    Intro --> BigO["Big O Notation"]
    Guide --> FormalDefs["(Formal Definitions O, Ω, Θ)"]
    Guide --> LoopRec["(Loop/Recursion Analysis)"]
    Guide --> Amortized["(Amortized Analysis)"]

    classDef main fill:#fcf3cf,stroke:#f39c12,stroke-width:2px;
    class CA main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Complexity Analysis"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Complexity Analysis/index.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Complexity Analysis/index.md")

def create_asymptotic_analysis_guide_placeholder(path):
    content = r"""---
tags: [concept/algorithms, concept/complexity_analysis, type/guide, placeholder]
aliases: [Asymptotic Notation, Formal Complexity Analysis]
---
> [!NOTE] Source Annotation
> This is a placeholder for a more detailed guide on asymptotic analysis, including formal definitions of Big O, Omega, Theta, recursion tree method, Master Theorem, etc.
> It would build upon [[Interview/Concept/Algorithms/Complexity Analysis/00 - Introduction to Time and Space Complexity|Introduction to Time and Space Complexity]].

# Asymptotic Analysis Guide (Detailed)

*(Content to be added: Formal definitions, Master Theorem, Recursion Tree method, analysis of complex loops and recursive patterns, amortized analysis examples.)*

---
Parent: [[Interview/Concept/Algorithms/Complexity Analysis/index|Complexity Analysis Index]]
Previous: [[Interview/Concept/Algorithms/Complexity Analysis/00 - Introduction to Time and Space Complexity|Introduction to Time and Space Complexity]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Complexity Analysis"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Complexity Analysis/01 - Asymptotic Analysis Guide.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Complexity Analysis/01 - Asymptotic Analysis Guide.md (Placeholder)")


def update_divide_and_conquer_framework(path):
    content = r"""---
tags: [concept/algorithms, concept/divide_and_conquer, type/framework, pattern/recursion]
aliases: [Divide and Conquer Paradigm, 分治算法框架]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/分治算法解题套路框架.md]].
> Labuladong distinguishes "Divide and Conquer思想 (thought/idea)" from "Divide and Conquer算法 (algorithm)". The latter implies improved complexity through division.

# Divide and Conquer: Algorithmic Framework

The Divide and Conquer paradigm is a powerful problem-solving strategy that involves breaking down a problem into smaller, more manageable subproblems, solving these subproblems (often recursively), and then combining their solutions to form the solution to the original problem.

## 💭 Distinguishing "Divide and Conquer Idea" from "Divide and Conquer Algorithm"

- **Divide and Conquer Idea (Decomposition Thinking):** This is a broad concept where a problem is broken into subproblems. Many recursive algorithms use this idea, such as simple tree traversals that calculate properties (e.g., node count = left_count + right_count + 1) or basic recursive Fibonacci. This is one of the "Two Thinking Modes" described in [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursion - One Perspective, Two Thinking Modes]].
- **Divide and Conquer *Algorithm* (Strict Sense):** Refers to algorithms where breaking the problem down and solving subproblems leads to a *more efficient solution (lower time complexity)* than solving the problem directly without decomposition.
    - Examples: [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]], [[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]], Karatsuba algorithm for fast multiplication, Strassen's algorithm for matrix multiplication.
    - If decomposition doesn't yield a better complexity, it's just "recursive decomposition" or "using the divide and Conquer idea," not strictly a "Divide and Conquer Algorithm" in this specialized sense.

This note focuses on the general recursive structure common to algorithms that employ the divide and conquer strategy, especially those leading to efficiency gains.

## 📜 General Steps of Divide and Conquer

A typical Divide and Conquer algorithm involves three main steps:

1.  **Divide:** Break the given problem into a number of subproblems that are smaller instances of the same problem.
2.  **Conquer:** Solve the subproblems recursively. If the subproblem sizes are small enough (base case), solve them directly.
3.  **Combine:** Combine the solutions to the subproblems into the solution for the original problem.

## 🌲 Recursive Structure (Often Tree-Like)

The process of a Divide and Conquer algorithm can often be visualized as a recursion tree.

```python
def divide_and_conquer_template(problem_input):
    # 1. Base Case: If the problem is small enough, solve it directly.
    if is_small_enough(problem_input):
        return solve_directly(problem_input)

    # 2. Divide: Break problem_input into subproblems: sub1, sub2, ...
    subproblems = divide_problem(problem_input)
    
    sub_solutions = []
    # 3. Conquer: Solve subproblems recursively.
    for sub_problem in subproblems:
        sub_solutions.append(divide_and_conquer_template(sub_problem))
        
    # 4. Combine: Combine sub_solutions to solve the original problem_input.
    result = combine_solutions(sub_solutions, problem_input) # problem_input might be needed for context
    
    return result

# Helper functions (to be defined based on the specific problem)
# def is_small_enough(p): ...
# def solve_directly(p): ...
# def divide_problem(p): ...
# def combine_solutions(sols, p_orig): ...
```

## 🔗 Connection to Binary Tree Traversals

Labuladong often draws parallels between Divide and Conquer algorithms and binary tree traversals:
- **[[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]]:**
    - `sort(array)`:
        - Divide: Split `array` into `left_half` and `right_half`.
        - Conquer: `sorted_left = sort(left_half)`, `sorted_right = sort(right_half)`.
        - Combine: `merge(sorted_left, sorted_right)`.
    - This structure is analogous to a **post-order traversal**. The "work" (merging) is done after the recursive calls (conquering subproblems) return.
- **[[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]]:**
    - `sort(array, low, high)`:
        - Divide: Partition `array` around a pivot `p` such that elements `< p` are left, `> p` are right. (This is the main "work" or "processing").
        - Conquer: `sort(array, low, p-1)`, `sort(array, p+1, high)`.
        - Combine: Trivial (already sorted in place).
    - This structure is analogous to a **pre-order traversal**. The main "work" (partitioning) is done before the recursive calls.

## ⏳ Complexity Analysis (Master Theorem)

The time complexity of Divide and Conquer algorithms is often analyzed using recurrence relations. The Master Theorem provides a way to solve many common recurrences of the form:
$T(N) = aT(N/b) + f(N)$
- $N$: size of the problem.
- $a$: number of subproblems.
- $N/b$: size of each subproblem (assuming all subproblems are roughly equal size).
- $f(N)$: cost of dividing the problem and combining the solutions.

For example:
- **Merge Sort:** $T(N) = 2T(N/2) + O(N)$ (2 subproblems of size N/2, $O(N)$ to merge). Solution: $O(N \log N)$.
- Some tree algorithms: $T(N) = 2T(N/2) + O(1)$ (e.g., counting nodes in a balanced tree). Solution: $O(N)$.

## Example: Merge K Sorted Lists (LeetCode 23)
[[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Sorted Lists]]

This problem asks to merge $k$ sorted linked lists into one sorted linked list.
A divide and conquer approach:
1.  **Base Cases:**
    - If $k=0$ lists, return `null`.
    - If $k=1$ list, return that list.
2.  **Divide:** Split the $k$ lists into two halves: first $k/2$ lists and remaining $k - k/2$ lists.
3.  **Conquer:** Recursively merge the first $k/2$ lists into `list1`. Recursively merge the other half into `list2`.
4.  **Combine:** Merge `list1` and `list2` (standard two-list merge).

```python
# Conceptual structure for merging k lists
# def mergeKLists(lists):
#     if not lists: return None
#     if len(lists) == 1: return lists[0]
#
#     mid = len(lists) // 2
#     left_half = lists[:mid]
#     right_half = lists[mid:]
#
#     merged_left = mergeKLists(left_half)   # Conquer
#     merged_right = mergeKLists(right_half) # Conquer
#
#     return merge_two_lists(merged_left, merged_right) # Combine
#
# def merge_two_lists(l1, l2): ... (standard 2-list merge)
```
This approach effectively builds a "merge tree." If there are $k$ lists, the height of this recursion tree is $O(\log k)$. At each level of the tree, roughly $N$ total elements are processed during merges (where $N$ is total number of nodes across all lists). So, complexity is $O(N \log k)$.

## When is Divide and Conquer Effective?

- **Independent Subproblems:** The subproblems should ideally be solvable independently.
- **Efficiency Gain:** The cost of dividing and combining, plus solving subproblems, should be less than solving the original problem monolithically. This often happens when $f(N)$ is relatively small compared to the work saved by reducing problem size, or when subproblems significantly overlap and can be memoized (leading to dynamic programming, a special case of D&C).
- **Balanced Partitions (Often Ideal):** Algorithms like Merge Sort perform best when subproblems are of roughly equal size. Quick Sort's worst-case $O(N^2)$ occurs with unbalanced partitions.

## 总结 (Summary)
- **Divide and Conquer** is a strategy that breaks a problem into smaller subproblems, solves them recursively, and combines their solutions.
- It's a form of [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|recursive decomposition thinking]].
- Strictly speaking, a "Divide and Conquer Algorithm" implies that this decomposition leads to a more efficient solution (lower time complexity) than direct approaches.
- Key examples include Merge Sort, Quick Sort, and many tree algorithms.
- The Master Theorem is often used to analyze the complexity of D&C algorithms.
- The effectiveness relies on the cost of division/combination relative to the reduction in problem size and the independence of subproblems.

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
Previous: [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursion Thinking Modes]]
Next: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]] (as a related paradigm)
Related: [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]], [[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Divide and Conquer"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Updated: Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework.md")

def create_divide_and_conquer_index(path):
    content = r"""---
tags: [index, concept/algorithms, concept/divide_and_conquer]
aliases: [Divide and Conquer Index]
---

# Divide and Conquer Algorithms

This section covers the Divide and Conquer algorithmic paradigm.

## Core Concepts:
- [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer - Framework]]
  - General Steps (Divide, Conquer, Combine)
  - Relation to Tree Traversals
  - Complexity Analysis (Master Theorem)

## Classic Examples:
- [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]]
- [[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]]
- [[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Sorted Lists]] (application of D&C)
- Karatsuba Algorithm (Fast Multiplication) - Placeholder
- Strassen's Algorithm (Matrix Multiplication) - Placeholder

## Visualization
```mermaid
graph TD
    DnCConcept["Divide & Conquer"] --> Framework["[[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Framework]]"]
    Framework --> Steps["(Divide, Conquer, Combine)"]
    Framework --> MasterTh["(Master Theorem)"]

    DnCConcept --> Examples["Classic Examples"]
    Examples --> MergeS["[[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]]"]
    Examples --> QuickS["[[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]]"]
    Examples --> MergeKL["[[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|Merge K Lists]]"]
    Examples --> Others["(Karatsuba, Strassen...)"]
    
    classDef main fill:#d5e8d4,stroke:#82b366,stroke-width:2px;
    class DnCConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Divide and Conquer"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Divide and Conquer/index.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Divide and Conquer/index.md")

def update_dynamic_programming_framework(path):
    content = r"""---
tags: [concept/algorithms, concept/dynamic_programming, type/framework, pattern/recursion, pattern/memoization, pattern/tabulation]
aliases: [DP Framework, Dynamic Programming Basics, 动态规划框架]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/动态规划解题套路框架.md]].
> Labuladong emphasizes that DP is essentially optimized brute-force search (窮举) through the identification of optimal substructure and overlapping subproblems.

# Dynamic Programming: Introduction and Framework

Dynamic Programming (DP) is a powerful algorithmic technique for solving optimization problems, typically those asking for a "maximum" or "minimum" value. At its core, DP is about intelligently exploring all possible solutions (brute-force or "穷举") and finding the best one.

## 🎯 What is Dynamic Programming?

DP problems usually involve finding an optimal solution (e.g., minimum cost, maximum profit, longest sequence). The key idea is to break down a complex problem into simpler, overlapping subproblems, solve each subproblem only once, and store their solutions to avoid redundant computations.

**Labuladong's Core Idea:** "Dynamic programming is essentially brute-force search with clever optimizations."

### The Three Pillars of Dynamic Programming:

1.  **Optimal Substructure:** An optimal solution to the overall problem can be constructed from optimal solutions to its subproblems. This means if you solve the subproblems optimally, you can combine these solutions to get the overall optimal solution.
    - *Example (Coin Change):* The minimum coins to make amount `A` can be found by taking one coin `c` and then finding the minimum coins for `A-c`. The subproblem `min_coins(A-c)` must be solved optimally.
    - Subproblems must be independent.

2.  **Overlapping Subproblems:** The recursive solution to the problem involves solving the same subproblems multiple times.
    - *Example (Fibonacci):* `fib(5)` calls `fib(4)` and `fib(3)`. `fib(4)` also calls `fib(3)`. `fib(3)` is an overlapping subproblem.
    - DP addresses this by storing the results of subproblems (memoization or tabulation) so they are computed only once.

3.  **State Transition Equation (状态转移方程):** A mathematical recurrence relation that defines how the solution to a problem (or state) can be derived from solutions to its subproblems (or previous/smaller states). This is the heart of a DP solution.

## 💡 Labuladong's Thinking Framework for DP

To derive the state transition equation and solve a DP problem, Labuladong suggests a three-step thinking process:

1.  **Clearly Define "States" (明确「状态」):**
    - Identify the variables that change as the problem is broken down into subproblems. These variables define the parameters of your `dp` function or the dimensions of your `dp` table.
    - *Example (Fibonacci):* The state is `n` (the n-th Fibonacci number).
    - *Example (Coin Change):* The state is `amount` (the target sum).

2.  **Identify "Choices" (明确「选择」):**
    - For each state, what are the decisions or actions you can take that lead to a transition to other states (subproblems)?
    - *Example (Coin Change):* For a given `amount`, the choices are which coin `c` from `coins` to use next. This leads to the subproblem of making `amount - c`.

3.  **Define the `dp` function/array's meaning (定义 `dp` 数组/函数的含义):**
    - Specify precisely what `dp(state_variables...)` computes or what `dp_table[state_indices...]` stores.
    - This definition is crucial for correctly formulating the recursive calls or iterative updates.
    - *Example (Fibonacci):* `dp(n)` returns the n-th Fibonacci number.
    - *Example (Coin Change):* `dp(amount)` returns the minimum number of coins to make up `amount`.

## 🛠️ Two Main Implementation Approaches

Once the state transition equation is formulated, DP problems can be solved using two general approaches:

### 1. Top-Down with Memoization (自顶向下带备忘录的递归)
   - This is a direct translation of the recursive state transition equation.
   - A "memo" (e.g., array or hash map) is used to store the results of already computed subproblems.
   - Before computing a subproblem, check the memo. If the result exists, return it. Otherwise, compute, store in memo, and then return.

   **General Structure (Recursive with Memoization):**
   ```python
   # memo = {} # or array initialized with a special value

   # def dp(state1, state2, ...):
   #     if (state1, state2, ...) in memo:
   #         return memo[(state1, state2, ...)]
       
   #     if base_case(state1, state2, ...):
   #         return base_case_value
       
   #     result = initial_value_for_optimization (e.g., infinity for min, -infinity for max)
   #     for choice in all_possible_choices_for_current_state:
   #         # Transition to new_state based on choice
   #         sub_problem_result = dp(new_state1, new_state2, ...)
   #         # Combine/update result
   #         result = optimize(result, combine(choice_effect, sub_problem_result))
           
   #     memo[(state1, state2, ...)] = result
   #     return result
   ```

### 2. Bottom-Up with Tabulation (自底向上 DP table迭代)
   - This involves building a `dp` table iteratively, starting from the base cases and progressively computing solutions for larger subproblems.
   - The `dp` table dimensions correspond to the states.
   - The iteration order must ensure that when `dp[state_indices...]` is computed, the values of subproblems it depends on (e.g., `dp[smaller_state_indices...]`) have already been computed.

   **General Structure (Iterative with DP Table):**
   ```python
   # dp_table = initialize_table_with_base_cases_or_default_values(...)

   # # Iterate through all states
   # for state1 in all_values_of_state1:
   #     for state2 in all_values_of_state2:
   #         # ...
   #         for choice in all_possible_choices_for_current_state:
   #             # Update dp_table[state1][state2]... based on previous states and choice
   #             dp_table[state1][state2]... = optimize(dp_table[state1][state2]..., 
   #                                                   combine(choice_effect, dp_table[prev_state1]...))
   # return dp_table[final_state_indices...]
   ```
Both approaches solve the same subproblems and generally have the same time complexity after optimization. The choice often comes down to conceptual clarity or ease of implementation for a specific problem.

## Example 1: Fibonacci Number (LeetCode 509)
[[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 - Fibonacci Number]]
- **Strictly speaking, not a DP problem** as it doesn't involve求最值 (optimization). However, it perfectly illustrates **overlapping subproblems** and the two DP solution approaches.
- **State:** `n` (the index of the Fibonacci number).
- **Choices:** Not applicable in the typical DP "choice" sense; the recurrence is fixed.
- **`dp(n)` definition:** Returns the n-th Fibonacci number.
- **State Transition:** $fib(n) = fib(n-1) + fib(n-2)$.
- **Base Cases:** $fib(0)=0, fib(1)=1$.

Labuladong's article shows:
1.  **Brute-force recursion:** $O(2^N)$ time due to recomputing subproblems. (Visualized in `div_mydata-fib`)
2.  **Top-down with memoization:** $O(N)$ time, $O(N)$ space for memo and recursion stack. (Visualized in `div_mydata-fib2`)
3.  **Bottom-up with DP table:** $O(N)$ time, $O(N)$ space for DP table. (Visualized in `div_mydata-fib3`)
4.  **Bottom-up with space optimization:** $O(N)$ time, $O(1)$ space (only need last two values). (Visualized in `div_fibonacci-number`)

## Example 2: Coin Change (LeetCode 322)
[[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 - Coin Change]]
Minimum coins to make amount `A` using `coins` array.
- **State:** `amount` (the current amount to make change for).
- **Choices:** For each `coin` in `coins`, choose to use it (if `amount >= coin`).
- **`dp(n)` definition:** Minimum coins to make amount `n`.
- **State Transition:** $dp(n) = \min_{c \in coins, n \ge c} \{1 + dp(n-c)\}$
- **Base Cases:** $dp(0)=0$. $dp(k) = \infty$ (or -1) if $k < 0$.

Labuladong's article shows:
1.  **Brute-force recursion:** Exponential time. (Visualized in `div_coin-change-brute-force`)
2.  **Top-down with memoization:** $O(A \cdot C)$ time (Amount * NumCoins), $O(A)$ space. (Visualized in `div_coin-change`)
3.  **Bottom-up with DP table:** $O(A \cdot C)$ time, $O(A)$ space.

## 总结 (Summary)
- Dynamic Programming solves optimization problems by breaking them into overlapping subproblems with optimal substructure.
- **Core Framework:**
    1.  Define **States**.
    2.  Identify **Choices** for each state.
    3.  Define the **`dp` function/array meaning**.
    4.  Formulate the **State Transition Equation**.
- **Implementation Methods:**
    - **Top-Down (Memoized Recursion):** Natural translation of recurrence.
    - **Bottom-Up (Tabulation):** Iterative filling of DP table.
- Key to DP is first writing the (often exponential) brute-force recursive solution based on the state transition, then optimizing it with memoization or by converting to tabulation.

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
Previous: [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer Framework]]
Next: [[Interview/Concept/Algorithms/Dynamic Programming/01 - DP - Optimal Substructure|DP - Optimal Substructure]] (Placeholder for more detailed pillar explanations)
Related Problems: [[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 - Fibonacci Number]], [[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 - Coin Change]]
Further Reading: [[Interview/Concept/Algorithms/Dynamic Programming/Memoization|Memoization]], [[Interview/Concept/Algorithms/Dynamic Programming/Tabulation|Tabulation]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Dynamic Programming"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Updated: Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework.md")

def create_dynamic_programming_index(path):
    content = r"""---
tags: [index, concept/algorithms, concept/dynamic_programming]
aliases: [Dynamic Programming Index, DP Index]
---

# Dynamic Programming (DP)

This section covers the principles, techniques, and problem-solving frameworks for Dynamic Programming.

## Core Concepts & Framework:
- [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming - Introduction and Framework]]
  - Three Pillars: Optimal Substructure, Overlapping Subproblems, State Transition Equation
  - Labuladong's Thinking Framework: States, Choices, DP Definition
  - Top-Down (Memoization) vs. Bottom-Up (Tabulation)
- `[[Interview/Concept/Algorithms/Dynamic Programming/01 - DP - Optimal Substructure|DP - Optimal Substructure]]` (Placeholder)
- `[[Interview/Concept/Algorithms/Dynamic Programming/02 - DP - Overlapping Subproblems|DP - Overlapping Subproblems]]` (Placeholder)
- [[Interview/Concept/Algorithms/Dynamic Programming/Memoization|Memoization (Top-Down DP)]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Tabulation|Tabulation (Bottom-Up DP)]]
- `[[Interview/Concept/Algorithms/Dynamic Programming/05 - DP - Space Optimization|DP - Space Optimization Techniques]]` (Placeholder)

## Problem Patterns & Examples:
- **Sequence DP:**
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/Longest Increasing Subsequence|Longest Increasing Subsequence (LIS)]]` (Placeholder)
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/Longest Common Subsequence|Longest Common Subsequence (LCS)]]` (Placeholder)
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/Edit Distance|Edit Distance]]` (Placeholder)
- **Knapsack Problems:**
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Knapsack/0-1 Knapsack|0/1 Knapsack]]` (Placeholder)
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Knapsack/Unbounded Knapsack|Unbounded Knapsack (Complete Knapsack)]]` (Placeholder)
- **Interval DP / Game DP:**
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Interval DP/Burst Balloons|Burst Balloons (戳气球)]]` (Placeholder)
- **Pathfinding on Grids:**
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Grid DP/Unique Paths|Unique Paths]]` (Placeholder)
- **Tree DP:**
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Tree DP/index|Tree DP Patterns]]`

## LeetCode Examples Discussed:
- [[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 - Fibonacci Number]] (Illustrates overlapping subproblems & DP approaches)
- [[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 - Coin Change]] (Classic DP optimization problem)

## Visualization
```mermaid
graph TD
    DPConcept["Dynamic Programming"] --> IntroFramework["[[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Introduction & Framework]]"]
    IntroFramework --> Pillars["(Optimal Substructure, Overlapping Subproblems, State Transition)"]
    IntroFramework --> Approaches["(Memoization vs Tabulation)"]

    DPConcept --> Techniques["Core Techniques"]
    Techniques --> Memo["[[Interview/Concept/Algorithms/Dynamic Programming/Memoization|Memoization]]"]
    Techniques --> Tab["[[Interview/Concept/Algorithms/Dynamic Programming/Tabulation|Tabulation]]"]
    Techniques --> SpaceOpt["(Space Optimization)"]

    DPConcept --> Patterns["Problem Patterns"]
    Patterns --> SeqDP["Sequence DP (LIS, LCS, Edit Distance)"]
    Patterns --> KnapsackDP["Knapsack (0/1, Unbounded)"]
    Patterns --> IntervalDP["Interval/Game DP"]
    Patterns --> GridDP["Grid DP (Unique Paths)"]
    Patterns --> TreeDP["Tree DP ([[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Tree DP/index|Index]])"]


    DPConcept --> ExamplesLC["LeetCode Examples"]
    ExamplesLC --> LC509["[[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 Fibonacci]]"]
    ExamplesLC --> LC322["[[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 Coin Change]]"]
    
    classDef main fill:#e6ccff,stroke:#9933ff,stroke-width:2px;
    class DPConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Dynamic Programming"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Dynamic Programming/index.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Dynamic Programming/index.md")
    
    # Create placeholder pattern files/folders
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP"))
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Knapsack"))
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Interval DP"))
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Grid DP"))
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Tree DP"))

    placeholder_content = r"""---
tags: [concept/algorithms, concept/dynamic_programming, pattern/placeholder, type/placeholder]
aliases: []
---
> [!NOTE] Source Annotation
> This is a placeholder note. Content to be added.

# Placeholder for DP Pattern

*(Details for this specific DP pattern will be added here.)*

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Index]]
"""
    placeholders = [
        "Interview/Concept/Algorithms/Dynamic Programming/01 - DP - Optimal Substructure.md",
        "Interview/Concept/Algorithms/Dynamic Programming/02 - DP - Overlapping Subproblems.md",
        "Interview/Concept/Algorithms/Dynamic Programming/05 - DP - Space Optimization.md",
        "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/Longest Increasing Subsequence.md",
        "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/Longest Common Subsequence.md",
        "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/Edit Distance.md",
        "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Knapsack/0-1 Knapsack.md",
        "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Knapsack/Unbounded Knapsack.md",
        "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Interval DP/Burst Balloons.md",
        "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Grid DP/Unique Paths.md",
    ]
    for ph_path in placeholders:
        full_ph_path = os.path.join(path, ph_path)
        if not os.path.exists(full_ph_path):
             with open(full_ph_path, 'w', encoding='utf-8') as f:
                f.write(placeholder_content.replace("Placeholder for DP Pattern", os.path.basename(ph_path).replace(".md","")))
             print(f"Created placeholder: {ph_path}")

    # Create Tree DP index
    tree_dp_index_content = r"""---
tags: [index, concept/algorithms, concept/dynamic_programming, pattern/tree_dp]
aliases: [Tree DP Index, DP on Trees]
---

# Dynamic Programming on Trees Patterns

This section covers dynamic programming patterns applied to tree structures.

- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Tree DP/00 - Unique Binary Search Trees (LC95 LC96)|Unique Binary Search Trees (LC95, LC96)]]
- (Placeholder for other Tree DP patterns like House Robber III)

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Index]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Tree DP"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Tree DP/index.md"), 'w', encoding='utf-8') as f:
        f.write(tree_dp_index_content)
    print("Created: Interview/Concept/Algorithms/Dynamic Programming/Patterns/Tree DP/index.md")


def update_greedy_algorithms_framework(path):
    content = r"""---
tags: [concept/algorithms, concept/greedy, type/framework, pattern/optimization]
aliases: [Greedy Algorithm Framework, 贪心算法框架]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/贪心算法解题套路框架.md]].
> This note outlines the general framework and thinking process for solving problems using greedy algorithms.

# Greedy Algorithm: Core Framework

Greedy algorithms make locally optimal choices at each step with the hope of finding a global optimum. While not always guaranteed to find the global optimum for all problems, they are effective for a specific class of problems where a greedy choice property holds.

## 🧠 The Essence of Greedy Algorithms

Labuladong uses a simple example: if you have 1-yuan and 100-yuan banknotes and can choose 10 notes to maximize total value, the greedy choice is to always pick a 100-yuan note. This intuitively leads to the optimal solution.

The core idea is that a series of "best" local choices will lead to the "best" overall solution.

**The Computer's Perspective (Exhaustive Search First):**
Algorithmically, one should first consider exhaustive search. For the banknote problem (choose 10 notes, options 1-yuan or 100-yuan):
- This is 10 choices, each with 2 options, leading to $2^{10}$ possibilities.
- A recursive solution might look like:
  ```python
  # def findMax(n_choices_left):
  #     if n_choices_left == 0: return 0
  #     # Option 1: take 1-yuan note
  #     res1 = 1 + findMax(n_choices_left - 1)
  #     # Option 2: take 100-yuan note
  #     res2 = 100 + findMax(n_choices_left - 1)
  #     return max(res1, res2)
  ```
- **Greedy Optimization:** We observe that `100 + findMax(n-1)` is always greater than `1 + findMax(n-1)`. This "greedy choice property" allows us to simplify the recursion to always pick 100. The complexity drops from $O(2^N)$ to $O(N)$ (iterative) or $O(1)$ (direct formula $100 \times N$) in this specific simple example after optimization.

The visualization for this from Labuladong (`div_greedy-cash-example`) shows the full recursive tree and how greedy choice prunes it.

## 🤔 When Can We Use a Greedy Approach?

A problem can be solved using a greedy algorithm if it exhibits two key properties:

1.  **Greedy Choice Property:**
    A globally optimal solution can be arrived at by making a locally optimal (greedy) choice. This means that the choice made at each step does not depend on future choices and does not prevent reaching the global optimum.
    Proving this property can sometimes involve an "exchange argument" (proof by contradiction): assume there's a better solution that doesn't include the greedy choice, then show that by including the greedy choice, you can achieve an equally good or better solution.

2.  **Optimal Substructure:**
    An optimal solution to the problem contains within it optimal solutions to subproblems. This is similar to [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming]]. If a greedy choice reduces the problem to a smaller subproblem, the optimal solution to this subproblem, combined with the greedy choice, should yield an optimal solution to the original problem.

## 🛠️ General Steps for Designing a Greedy Algorithm

1.  **Understand the Problem:** Clearly define what needs to be optimized (minimized or maximized).
2.  **Identify Greedy Choices:** Determine what a "local optimum" or "best immediate choice" looks like at each step. This often involves sorting the input data based on some criteria.
3.  **Prove Correctness (Informally or Formally):**
    - Verify the greedy choice property. Does making this local choice prevent finding the global optimum?
    - Verify optimal substructure. Does solving the subproblem (after making the greedy choice) optimally lead to an overall optimal solution?
4.  **Implement the Algorithm:** Iterate through the problem, making greedy choices at each step and building up the solution.

## Visualization: Example - Activity Selection Problem

A classic greedy problem: Given a set of activities with start and finish times, select the maximum number of non-overlapping activities.

- **Greedy Choice:** Sort activities by their finish times. Select the activity that finishes earliest. Then, from the remaining compatible activities, select the next one that finishes earliest, and so on.

```tikz
\begin{tikzpicture}[
    activity/.style={rectangle, draw, fill=blue!20, minimum height=0.6cm, text centered, font=\sffamily\small},
    time_axis/.style={->, thick},
    selected_activity/.style={activity, fill=green!30}
]
    % Time axis
    \draw[time_axis] (0,0) -- (11,0) node[right] {Time};
    \foreach \x in {0,...,10} {
        \draw (\x,0.1) -- (\x,-0.1) node[below, font=\tiny] {\x};
    }

    % Activities (start_time, end_time, label_y_offset, name)
    % A: [1,4], B: [3,5], C: [0,6], D: [5,7]
    % Sorted by finish time: A(4), B(5), C(6), D(7), ...

    % Activities on timeline
    \node[selected_activity] (act_A) at (2.5, 1.5) {A (1-4)}; \draw (1,1.3) rectangle (4,1.7);
    \node[activity, fill=red!20] (act_B) at (4, 2.3) {B (3-5)}; \draw (3,2.1) rectangle (5,2.5); % Conflicts
    \node[activity, fill=red!20] (act_C) at (3, 3.1) {C (0-6)}; \draw (0,2.9) rectangle (6,3.3); % Conflicts
    \node[selected_activity] (act_D) at (6, 1.5) {D (5-7)}; \draw (5,1.3) rectangle (7,1.7);

    \node at (5.5, -1) [draw, fill=yellow!10, rounded corners, text width=9cm, align=center, font=\sffamily\small]
    {
        1. Activities sorted by finish time: A(4), B(5), C(6), D(7), ...\\
        2. Select A (finishes at 4). Last finish time = 4.\\
        3. Consider B (starts 3, ends 5). Conflicts (3 < 4). Skip.\\
        4. Consider C (starts 0, ends 6). Conflicts (0 < 4). Skip.\\
        5. Consider D (starts 5, ends 7). Compatible (5 >= 4). Select D. Last finish time = 7.\\
        Selected: A, D.
    };
\end{tikzpicture}
```

## Greedy vs. Dynamic Programming
- **Greedy:** Makes a choice that looks best *now* and sticks with it. Doesn't reconsider past choices. Simple and fast IF it works.
- **DP:** Explores all possible choices (often by trying each choice and solving the resulting subproblem) and finds the best overall solution by combining optimal subproblem solutions. Generally more robust but can be more complex.

If a problem can be solved by a greedy algorithm, it's usually simpler and more efficient than a DP solution. However, not all optimization problems have the greedy choice property. The [[Interview/Practice/LeetCode/LC322 - Coin Change|Coin Change problem]] (finding minimum coins) is a classic example where a simple greedy approach (always taking the largest possible coin) fails, and DP is required.

## 总结 (Summary)
- Greedy algorithms aim for a global optimum by making locally optimal choices at each step.
- Success relies on the **greedy choice property** (local optimum leads to global optimum) and **optimal substructure** (optimal solution contains optimal sub-solutions).
- Common strategy: Sort input based on some criteria, then iterate and make greedy choices.
- Often simpler and faster than DP if applicable, but proving correctness can be non-trivial and is essential. Many problems that seem greedy are not.

---
Parent: [[Interview/Concept/Algorithms/Greedy Algorithms/index|Greedy Algorithms Index]]
Related Problems: [[Interview/Practice/LeetCode/LC45 - Jump Game II|LC45 - Jump Game II]], [[Interview/Practice/LeetCode/LC55 - Jump Game|LC55 - Jump Game]]
Related Concepts: [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming]] (for comparison)
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Greedy Algorithms"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Greedy Algorithms/00 - Greedy Algorithm - Core Framework.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Updated: Interview/Concept/Algorithms/Greedy Algorithms/00 - Greedy Algorithm - Core Framework.md")

def create_greedy_algorithms_index(path):
    content = r"""---
tags: [index, concept/algorithms, concept/greedy]
aliases: [Greedy Algorithms Index]
---

# Greedy Algorithms

This section covers the greedy algorithmic paradigm.

## Core Concepts:
- [[Interview/Concept/Algorithms/Greedy Algorithms/00 - Greedy Algorithm - Core Framework|Greedy Algorithm - Core Framework]]
  - Greedy Choice Property
  - Optimal Substructure

## LeetCode Examples:
- [[Interview/Practice/LeetCode/LC45 - Jump Game II|LC45 - Jump Game II]]
- [[Interview/Practice/LeetCode/LC55 - Jump Game|LC55 - Jump Game]] (Can be solved with Greedy)

## Visualization
```mermaid
graph TD
    GreedyConcept["Greedy Algorithms"] --> Framework["[[Interview/Concept/Algorithms/Greedy Algorithms/00 - Greedy Algorithm - Core Framework|Framework]]"]
    Framework --> GCP["Greedy Choice Property"]
    Framework --> OS["Optimal Substructure"]

    GreedyConcept --> ExamplesLC["LeetCode Examples"]
    ExamplesLC --> LC45["[[Interview/Practice/LeetCode/LC45 - Jump Game II|LC45 Jump Game II]]"]
    ExamplesLC --> LC55["[[Interview/Practice/LeetCode/LC55 - Jump Game|LC55 Jump Game]]"]

    classDef main fill:#e6ffe6,stroke:#006400,stroke-width:2px;
    class GreedyConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Greedy Algorithms"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Greedy Algorithms/index.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Greedy Algorithms/index.md")

def create_hashing_patterns_index(path):
    content = r"""---
tags: [index, concept/algorithms, pattern/hashing]
aliases: [Hashing Algorithm Patterns, Hash Table Use Cases]
---

# Hashing Algorithm Patterns

This section outlines common algorithmic patterns that leverage hash tables (hash maps and hash sets) for efficient solutions. Hashing provides average $O(1)$ time complexity for lookups, insertions, and deletions, making it a powerful tool.

## Core Hashing Patterns:

-   [[Interview/Concept/Algorithms/Hashing/Two Sum Pattern|Two Sum Pattern]]
    -   *Description:* Finding two elements in a collection that sum to a target.
    -   *Hash Use:* Store elements and their indices (or just elements) for quick complement lookups.
-   [[Interview/Concept/Algorithms/Hashing/Detecting Duplicates with Sets|Detecting Duplicates with Sets]]
    -   *Description:* Checking if a collection contains any repeated elements.
    -   *Hash Use:* Add elements to a hash set; if an element is already present, a duplicate is found.
-   [[Interview/Concept/Algorithms/Hashing/Frequency Counting with Hash Maps|Frequency Counting with Hash Maps]]
    -   *Description:* Counting the occurrences of each unique element in a collection.
    -   *Hash Use:* Store elements as keys and their counts as values in a hash map.
-   **Anagram Detection** (Placeholder: `[[Interview/Concept/Algorithms/Hashing/Anagram Detection Pattern|Anagram Detection Pattern]]`)
    -   *Description:* Determining if two strings are anagrams of each other.
    -   *Hash Use:* Use hash maps to store character counts for each string and compare the maps.
-   **Group Anagrams** (Placeholder: `[[Interview/Concept/Algorithms/Hashing/Group Anagrams Pattern|Group Anagrams Pattern]]`)
    -   *Description:* Grouping a list of strings by their anagrams.
    -   *Hash Use:* Use a canonical representation of anagrams (e.g., sorted string or character count tuple) as keys in a hash map, with lists of anagrams as values.
-   **Caching / Memoization** (Often related to [[Interview/Concept/Algorithms/Dynamic Programming/Memoization|Dynamic Programming Memoization]])
    -   *Description:* Storing results of expensive function calls to avoid redundant computations.
    -   *Hash Use:* Use a hash map to store function arguments (or a hash of them) as keys and results as values.

## Visualization of Hashing Applications

```mermaid
graph TD
    A["Hashing Patterns"] --> B["[[Interview/Concept/Algorithms/Hashing/Two Sum Pattern|Two Sum]]"]
    A --> C["[[Interview/Concept/Algorithms/Hashing/Detecting Duplicates with Sets|Detect Duplicates (Set)]]"]
    A --> D["[[Interview/Concept/Algorithms/Hashing/Frequency Counting with Hash Maps|Frequency Count (Map)]]"]
    A --> E["(Anagram Detection)"]
    A --> F["(Group Anagrams)"]
    A --> G["(Caching/Memoization)"]

    subgraph "Core Idea: Fast Lookups/Storage"
        direction LR
        H["Hash Map (Key-Value)"]
        I["Hash Set (Unique Keys)"]
    end
    
    B --> H
    C --> I
    D --> H
    E --> H
    F --> H
    G --> H

    classDef pattern fill:#e6f2ff,stroke:#aaccff,stroke-width:2px;
    class A,B,C,D,E,F,G pattern;
```

These patterns are fundamental in solving a wide array of interview problems efficiently. Understanding when and how to apply them is key.

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Hashing"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Hashing/index.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Hashing/index.md")

def update_hashing_two_sum_pattern(path):
    content = r"""---
tags: [concept/algorithms, pattern/hashing, pattern/two_pointer, problem_type/array_sum]
aliases: [Two Sum Problem, Target Sum Pair]
---

> [!NOTE] Source Annotation
> This is a common algorithmic pattern, exemplified by [[Interview/Practice/LeetCode/LC1 - Two Sum|LC1 - Two Sum]].

# Two Sum Pattern

The "Two Sum" pattern is a fundamental algorithmic problem that asks to find two numbers in a collection (usually an array) that add up to a given target value. Variations might ask for the numbers themselves, their indices, or simply whether such a pair exists.

## Problem Definition

Given an array of numbers `nums` and a target value `target`, find two distinct elements `nums[i]` and `nums[j]` such that `nums[i] + nums[j] = target`.

## Common Approaches

### 1. Brute-Force
- Iterate through all possible pairs of numbers in the array.
- For each pair, check if their sum equals the target.
- **Time Complexity:** $O(N^2)$ for an array of size $N$.
- **Space Complexity:** $O(1)$.
- **Example:** See brute-force solution in [[Interview/Practice/LeetCode/LC1 - Two Sum]].

### 2. Sorting and Two Pointers
- If modifying the array or its order is allowed, or if only the values are needed (not original indices):
    1. Sort the array: $O(N \log N)$.
    2. Use two pointers, one starting at the beginning (`left`) and one at the end (`right`).
    3. Calculate `current_sum = nums[left] + nums[right]`.
        - If `current_sum == target`, a pair is found.
        - If `current_sum < target`, increment `left` to increase the sum.
        - If `current_sum > target`, decrement `right` to decrease the sum.
    4. Repeat until pointers cross.
- **Time Complexity:** $O(N \log N)$ (dominated by sort). The two-pointer scan is $O(N)$.
- **Space Complexity:** $O(1)$ or $O(N)$ depending on the sort implementation (in-place or not).
- **Note:** This approach is difficult if original indices must be returned and the array contains duplicates, as sorting loses original index information unless pairs of (value, original_index) are sorted.

### 3. Hash Map (Dictionary)
- This is often the most efficient approach when original indices are required.
- Iterate through the array once. For each element `x` at index `i`:
    1. Calculate the `complement = target - x`.
    2. Check if `complement` exists in the hash map.
        - If yes, and its stored index is not `i` (if elements can't be reused for themselves), a pair is found. The elements are `x` and `complement`. Their indices are `i` and the stored index of `complement`.
        - If no, add `x` and its index `i` to the hash map.
- **Time Complexity:** $O(N)$ on average, as hash map operations (insertion and lookup) are $O(1)$ on average.
- **Space Complexity:** $O(N)$ in the worst case for the hash map.
- **Example:** See hash map solution in [[Interview/Practice/LeetCode/LC1 - Two Sum]]. This method uses a [[Interview/Concept/Programming Languages/Python/05 - Python Dict (Hash Map) for Interviews|Python Dict]] for implementation.

## Visualization (Hash Map Approach)

Consider finding `a + b = target`.
When processing element `b` at `index_b`:
- We need `a = target - b`.
- If `a` was seen before, it's in our hash map `seen_elements_map`.
- `map_entry = {value: index}`.

```tikz
\begin{tikzpicture}[
    node/.style={draw, rectangle, minimum height=0.8cm, minimum width=1cm},
    box/.style={draw, rounded corners, fill=blue!10, text width=3.5cm, align=center, inner sep=5pt},
    arrow/.style={->, >=stealth, thick}
]
    \node[box] (algo) at (0,0) {
        For each num in nums at idx: \\
        complement = target - num \\
        Is complement in hash\_map?
    };

    \node[box, fill=green!10] (yes) at (4,1) {
        YES: Found pair! \\
        idx\_complement = hash\_map[complement] \\
        Return [idx\_complement, idx]
    };

    \node[box, fill=red!10] (no) at (4,-1) {
        NO: Add num to map \\
        hash\_map[num] = idx \\
        Continue loop.
    };

    \draw[arrow] (algo.east) -| node[above, midway, font=\tiny] {Test} (2,0) |- (yes.west);
    \draw[arrow] (algo.east) -| (2,0) |- (no.west);
\end{tikzpicture}
```

## Key Considerations
- **Distinct Elements:** Can the two numbers be the same element at the same index? (Usually no). Can they be two distinct elements with the same value? (Usually yes).
- **Return Value:** Indices or the numbers themselves?
- **Existence of Solution:** Is a solution guaranteed? What to return if not found?
- **Multiple Solutions:** If multiple pairs exist, which one to return? (Usually any one is fine, or the first found).

## Variations
- **Two Sum II - Input array is sorted:** Directly apply the two-pointer approach for $O(N)$ time, $O(1)$ space.
- **Two Sum III - Data structure design:** Design a class that supports `add` and `find` operations for a Two Sum query.
- **Two Sum IV - Input is a BST:** Traverse the BST (e.g., inorder to get sorted list, then two pointers) or use a hash set during traversal.
- **k-Sum Problem:** Find `k` numbers that sum to target. Can often be solved by reducing to `(k-1)-Sum` recursively, with Two Sum as the base case.

## 总结 (Summary)
- The Two Sum pattern is a cornerstone for many array/collection-based problems.
- The hash map approach provides an efficient $O(N)$ time solution, trading space for time.
- Understanding this pattern is crucial for tackling more complex sum-related problems.

---
Parent: [[Interview/Concept/Algorithms/Hashing/index|Hashing Patterns]]
Related Problems: [[Interview/Practice/LeetCode/LC1 - Two Sum|LC1 - Two Sum]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Hashing"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Hashing/Two Sum Pattern.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Updated: Interview/Concept/Algorithms/Hashing/Two Sum Pattern.md")

def update_hashing_detect_duplicates(path):
    content = r"""---
tags: [concept/algorithms, pattern/hashing, pattern/set_usage, problem_type/array_uniqueness]
aliases: [Duplicate Detection using Sets, Uniqueness Check with HashSet]
---

> [!NOTE] Source Annotation
> This pattern is commonly used in problems like [[Interview/Practice/LeetCode/LC217 - Contains Duplicate|LC217 - Contains Duplicate]].

# Detecting Duplicates with Sets Pattern

A frequent algorithmic task is to determine if a collection (like an array or list) contains any duplicate elements. The most straightforward and often efficient way to achieve this is by using a hash set.

## Problem Definition

Given a collection of items, determine if any item appears more than once.

## Hash Set Approach

The core idea is to iterate through the collection, adding each item to a hash set. Before adding, check if the item is already in the set.

**Algorithm:**
1.  Initialize an empty hash set (e.g., `seen_elements`).
2.  For each `item` in the input `collection`:
    a.  If `item` is already in `seen_elements`:
        i.  A duplicate is found. Return `True` (or the duplicate item, depending on problem requirements).
    b.  Else (if `item` is not in `seen_elements`):
        i.  Add `item` to `seen_elements`.
3.  If the loop finishes without finding any duplicates, it means all elements are unique. Return `False`.

This uses a [[Interview/Concept/Programming Languages/Python/06 - Python Set (Hash Set) for Interviews|Python Set]] when implemented in Python.

## Visualization

Collection: `[A, B, C, A, D]`

```tikz
\begin{tikzpicture}[
    node_arr/.style={draw, rectangle, minimum size=0.7cm, font=\sffamily},
    node_set/.style={draw, ellipse, minimum width=1cm, fill=blue!10, font=\sffamily\small},
    arrow/.style={->, >=stealth, thick},
    current_item_label/.style={font=\sffamily\bfseries\small, below=0.5cm}
]

    % Array Elements
    \node[node_arr] (elA1) at (0,0) {A};
    \node[node_arr] (elB) at (1.2,0) {B};
    \node[node_arr] (elC) at (2.4,0) {C};
    \node[node_arr] (elA2) at (3.6,0) {A};
    \node[node_arr] (elD) at (4.8,0) {D};

    % Set (evolving)
    \node[font=\sffamily\bfseries\small] at (2.4, -1.2) {Hash Set seen\_elements:};

    % Step 1: Process A
    \draw[arrow, red] (elA1.south) -- ++(0,-0.3) node[current_item_label] {Item: A};
    \node[node_set] (set1) at (0.5, -2.5) {A};
    \node at (0.5, -3.2) {Add A};

    % Step 2: Process B
    \draw[arrow, red] (elB.south) -- ++(0,-0.3) node[current_item_label, xshift=1.2cm] {Item: B};
    \node[node_set] (set2) at (2, -2.5) {A, B};
    \node at (2, -3.2) {Add B};

    % Step 3: Process C
    \draw[arrow, red] (elC.south) -- ++(0,-0.3) node[current_item_label, xshift=2.4cm] {Item: C};
    \node[node_set] (set3) at (3.5, -2.5) {A, B, C};
    \node at (3.5, -3.2) {Add C};

    % Step 4: Process A (duplicate)
    \draw[arrow, red] (elA2.south) -- ++(0,-0.3) node[current_item_label, xshift=3.6cm] {Item: A};
    \node[node_set, fill=red!20] (set4) at (2.4, -4.5) {A, B, C};
    \node[font=\sffamily\bfseries\small, fill=green!20, draw, rounded corners] at (2.4, -5.2) {A is in set! Duplicate found. Return True.};
    \draw[arrow, blue, dashed] (set4.north) .. controls (3.6, -4) .. (elA2.south east);

\end{tikzpicture}
```

## Complexity Analysis
-   **Time Complexity:** $O(N)$ on average, where $N$ is the number of items in the collection. This assumes that hash set insertion (`add`) and lookup (`in`) operations take $O(1)$ time on average. In the worst-case (many hash collisions), these can degrade to $O(N)$, making the overall complexity $O(N^2)$, but this is rare with good hash functions.
-   **Space Complexity:** $O(M)$ on average, where $M$ is the number of unique items in the collection. In the worst case, if all items are unique, $M=N$, so space is $O(N)$.

## Advantages
-   **Simplicity:** The logic is straightforward to implement.
-   **Efficiency:** Generally very fast for typical inputs.

## When to Use
-   When you need to check for the presence of duplicates in a collection.
-   When the order of elements does not need to be preserved (as sets are unordered).
-   When the elements are hashable.

## Variations and Considerations
-   **Finding All Duplicates:** Modify the logic to store counts (using a hash map) or collect all items that, when encountered, are already in the `seen_elements` set.
-   **Limited Range of Values:** If values are integers within a small, known range, a boolean array (or bitset) can act as a specialized set and might be slightly faster or more space-efficient.
-   **Sorting:** An alternative approach is to sort the collection ($O(N \log N)$ time) and then iterate through it, checking adjacent elements for equality ($O(N)$ time). This avoids extra space (if in-place sort is used) but is slower overall if $N \log N > N$.

## Summary
- Using a hash set provides an elegant and typically $O(N)$ time-efficient solution for detecting duplicates.
- The trade-off is $O(M)$ space for the hash set, where $M$ is the count of unique elements.
- This pattern is fundamental for problems requiring uniqueness checks.

---
Parent: [[Interview/Concept/Algorithms/Hashing/index|Hashing Patterns]]
Related Problems: [[Interview/Practice/LeetCode/LC217 - Contains Duplicate|LC217 - Contains Duplicate]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Hashing"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Hashing/Detecting Duplicates with Sets.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Updated: Interview/Concept/Algorithms/Hashing/Detecting Duplicates with Sets.md")

def update_hashing_frequency_counting(path):
    content = r"""---
tags: [concept/algorithms, pattern/hashing, pattern/frequency_counting, problem_type/array_analysis]
aliases: [Frequency Counting, Counting Occurrences, Hash Map for Counts]
---

> [!NOTE] Source Annotation
> This pattern is widely applicable, for example, in problems like [[Interview/Practice/LeetCode/LC136 - Single Number|LC136 - Single Number]] (to find element with specific frequency) or counting character frequencies in a string.

# Frequency Counting with Hash Maps Pattern

A common algorithmic task involves counting the occurrences (frequency) of each distinct item in a collection (e.g., elements in an array, characters in a string). Hash maps (dictionaries in Python) are exceptionally well-suited for this pattern due to their efficient key-value storage and retrieval.

## Problem Definition

Given a collection of items, determine the frequency of each unique item.

## Hash Map Approach

The core idea is to iterate through the collection, using the items themselves as keys in a hash map and their counts as values.

**Algorithm:**
1.  Initialize an empty hash map (e.g., `frequency_map`).
2.  For each `item` in the input `collection`:
    a.  If `item` is already a key in `frequency_map`:
        i.  Increment its corresponding value (count).
    b.  Else (`item` is not yet a key):
        i.  Add `item` to `frequency_map` as a new key with a value (count) of 1.
3.  After iterating through the entire collection, `frequency_map` will contain each unique item and its total frequency.

In Python, this is easily done using a [[Interview/Concept/Programming Languages/Python/05 - Python Dict (Hash Map) for Interviews|Python Dict]]. The `get(key, default_value)` method is particularly useful.

**Python Implementation Sketch:**
```python
from collections import Counter # Python's specialized counter

def count_frequencies(collection):
    frequency_map = {}
    for item in collection:
        frequency_map[item] = frequency_map.get(item, 0) + 1
    return frequency_map

# Using collections.Counter for a more concise solution:
def count_frequencies_with_counter(collection):
    return Counter(collection)
```

## Visualization

Collection: `['apple', 'banana', 'apple', 'orange', 'banana', 'apple']`

```tikz
\begin{tikzpicture}[
    node_arr/.style={draw, rectangle, minimum size=0.7cm, font=\sffamily\small},
    node_map_item/.style={draw, rectangle, fill=blue!10, text width=2.5cm, align=center, font=\sffamily\tiny},
    arrow/.style={->, >=stealth, thick},
    current_item_label/.style={font=\sffamily\bfseries\small, below=0.5cm}
]

    % Array Elements (simplified display)
    \node[node_arr] (el1) at (0,0) {apple};
    \node[node_arr] (el2) at (1.5,0) {banana};
    \node[node_arr] (el3) at (3,0) {apple};
    \node at (4.5,0) {...};

    % Map (evolving)
    \node[font=\sffamily\bfseries\small] at (2.25, -1.2) {frequency\_map (Item: Count)};

    % Step 1: Process 'apple'
    \draw[arrow, red] (el1.south) -- ++(0,-0.3) node[current_item_label, xshift=0cm] {Item: apple};
    \node[node_map_item] (map1) at (2.25, -2.5) {apple: 1};
    \node at (2.25, -3.2) {Add/Increment 'apple'};

    % Step 2: Process 'banana'
    \draw[arrow, red] (el2.south) -- ++(0,-0.3) node[current_item_label, xshift=1.5cm] {Item: banana};
    \node[node_map_item] (map2) at (2.25, -4.5) {apple: 1\\banana: 1};
    \node at (2.25, -5.2) {Add/Increment 'banana'};

    % Step 3: Process 'apple' again
    \draw[arrow, red] (el3.south) -- ++(0,-0.3) node[current_item_label, xshift=3cm] {Item: apple};
    \node[node_map_item, fill=yellow!30] (map3) at (2.25, -6.5) {apple: 2\\banana: 1};
    \node at (2.25, -7.2) {Increment 'apple'};

    \node at (2.25, -8) {... After all items ...};
    \node[node_map_item, fill=green!20] (map_final) at (2.25, -9) {apple: 3\\banana: 2\\orange: 1};

\end{tikzpicture}
```

## Complexity Analysis
-   **Time Complexity:** $O(N)$ on average, where $N$ is the number of items in the collection. Each item is processed once, and hash map operations (lookup, insertion, update) take $O(1)$ time on average.
-   **Space Complexity:** $O(M)$ on average, where $M$ is the number of unique items in the collection. The hash map stores one entry for each unique item.

## Advantages
-   **Efficiency:** Provides a linear time solution for counting frequencies.
-   **Flexibility:** Works with any collection of hashable items.
-   **Simplicity:** The logic is generally easy to implement, especially with Python's `dict.get()` or `collections.Counter`.

## When to Use
-   Whenever you need to count occurrences of items in a list, string, or other iterable.
-   As a preliminary step for other algorithms that rely on frequency information (e.g., finding the mode, checking for anagrams, building Huffman trees).
-   Problems asking for items that appear k times, most frequent k items, etc.

## Variations
-   **Top K Frequent Elements:** After building the frequency map, use a min-heap or sorting to find the top K frequent elements.
-   **Character Frequencies in a String:** A common application for problems like anagram detection or palindrome permutation.
-   **Fixed Range of Integers:** If items are integers within a small, known range (e.g., ASCII characters or numbers from 0-100), an array can be used as a direct-address table for frequency counting, potentially offering slightly better constant factors than a hash map.

## Summary
- Hash maps offer an efficient and general way to count item frequencies in $O(N)$ time and $O(M)$ space (where M is unique items).
- Python's `dict` and `collections.Counter` make this pattern particularly easy to implement.
- This is a foundational pattern for many data analysis and algorithmic tasks.

---
Parent: [[Interview/Concept/Algorithms/Hashing/index|Hashing Patterns]]
Related Problems: [[Interview/Practice/LeetCode/LC136 - Single Number|LC136 - Single Number]] (uses this to find count=1)
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Hashing"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Hashing/Frequency Counting with Hash Maps.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Updated: Interview/Concept/Algorithms/Hashing/Frequency Counting with Hash Maps.md")

def create_array_manipulation_index(path):
    content = r"""---
tags: [index, concept/algorithms, concept/array_manipulation]
aliases: [Array Manipulation Index, Array Techniques]
---

# Array Manipulation Techniques

This section covers various techniques for efficient array manipulation.

## Core Techniques:
- [[Interview/Concept/Algorithms/Array Manipulation/00 - 2D Array Fancy Traversal|2D Array Fancy Traversal]]
- [[Interview/Concept/Algorithms/Array Manipulation/01 - Difference Array Technique|Difference Array Technique]]
- [[Interview/Concept/Algorithms/Array Manipulation/02 - Prefix Sum Array Technique|Prefix Sum Array Technique]]

## Visualization
```mermaid
graph TD
    ArrayManip["Array Manipulation"] --> Trav2D["[[Interview/Concept/Algorithms/Array Manipulation/00 - 2D Array Fancy Traversal|2D Traversal]]"]
    ArrayManip --> DiffArr["[[Interview/Concept/Algorithms/Array Manipulation/01 - Difference Array Technique|Difference Array]]"]
    ArrayManip --> PrefixSumArr["[[Interview/Concept/Algorithms/Array Manipulation/02 - Prefix Sum Array Technique|Prefix Sum Array]]"]

    classDef main fill:#f0fff0,stroke:#2e8b57,stroke-width:2px;
    class ArrayManip main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Array Manipulation"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Array Manipulation/index.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Array Manipulation/index.md")

def create_difference_array_technique(path):
    content = r"""---
tags: [concept/algorithms, concept/array_manipulation, type/technique, pattern/difference_array, course/labuladong]
aliases: [Difference Array, 差分数组]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷数组算法/小而美的算法技巧：差分数组.md]].
> This note explains the Difference Array technique for efficient range updates on an array.

| [[Interview/Concept/Algorithms/Array Manipulation/index|Back to Array Manipulation Index]] | [[Interview/Concept/Algorithms/Array Manipulation/02 - Prefix Sum Array Technique|Next: Prefix Sum Array]] |

# Difference Array Technique

The Difference Array is a clever technique used to efficiently perform range update operations on an array. If you need to frequently add (or subtract) a value to all elements within a specific range `[i, j]` of an array, using a difference array can reduce the complexity of each range update to $O(1)$.

## ❓ The Problem with Naive Range Updates
Given an array `nums`, if we need to add `val` to `nums[i...j]`, a naive approach would be:
```python
# for k in range(i, j + 1):
#     nums[k] += val
```
This takes $O(N)$ time for each update in the worst case (if the range covers most of the array). If there are many such updates, the total time can be prohibitive.

## 💡 Core Idea of Difference Array

1.  **Definition:** A difference array `diff` is constructed such that `diff[i] = nums[i] - nums[i-1]` for `i > 0`, and `diff[0] = nums[0]`.
    ```
    nums: [1, 3, 7, 4, 6]
    diff: [1, 2, 4, -3, 2]  (1=1; 2=3-1; 4=7-3; -3=4-7; 2=6-4)
    ```

2.  **Reconstruction:** The original array `nums` can be reconstructed from `diff` using prefix sums:
    `nums[0] = diff[0]`
    `nums[i] = diff[i] + nums[i-1]` for `i > 0`.
    This is equivalent to `nums[i] = prefix_sum(diff, i)`.

3.  **Range Update Magic:** To add `val` to `nums[i...j]`:
    - Add `val` to `diff[i]`. This affects `nums[i]` and all subsequent elements `nums[k]` for `k > i` during reconstruction (because `nums[k]` calculation includes `diff[i]` via the cascading sum).
    - Subtract `val` from `diff[j+1]` (if `j+1` is within bounds). This counteracts the effect of adding `val` to `diff[i]` for all elements `nums[k]` where `k > j`.
    - This pair of operations takes $O(1)$ time.

**Visualizing the Update:**
`![](/algo/images/difference/3.jpeg)` (Source: Labuladong)
- If `diff[i] += val`, then `res[i], res[i+1], ...` all increase by `val`.
- If `diff[j+1] -= val`, then `res[j+1], res[j+2], ...` all decrease by `val`.
- The net effect is that only `res[i...j]` are increased by `val`.

## 🛠️ Implementation: `Difference` Class (Labuladong)

Labuladong provides a helper class for this:
```python
class Difference:
    # Difference array
    def __init__(self, nums: list[int]):
        assert len(nums) > 0
        self.diff = [0] * len(nums)
        # Construct difference array
        self.diff[0] = nums[0]
        for i in range(1, len(nums)):
            self.diff[i] = nums[i] - nums[i-1]

    # Increment elements in closed interval [i, j] by val
    def increment(self, i: int, j: int, val: int) -> None:
        self.diff[i] += val
        if j + 1 < len(self.diff):
            self.diff[j + 1] -= val
        # If j+1 is out of bounds, it means the increment affects all elements from i to the end.
        # No counteraction is needed beyond the array.

    # Return the result array after all increments
    def result(self) -> list[int]:
        res = [0] * len(self.diff)
        # Reconstruct result array from difference array
        res[0] = self.diff[0]
        for i in range(1, len(self.diff)):
            res[i] = res[i-1] + self.diff[i]
        return res

# Example Usage:
# nums = [0, 0, 0, 0, 0] # Initial array (e.g., for LC370 Range Addition)
# df = Difference(nums)
# df.increment(1, 3, 2)  # Add 2 to nums[1..3]
# # diff becomes: [0, 2, 0, 0, -2] if len=5. For nums=[0,0,0,0,0], diff=[0,0,0,0,0] initially.
# # after increment(1,3,2) on nums=[0,0,0,0,0]:
# # Initial diff from [0,0,0,0,0] is [0,0,0,0,0]
# # diff[1]+=2 -> [0,2,0,0,0]
# # diff[3+1]-=2 -> diff[4]-=2 -> [0,2,0,0,-2]
# final_nums = df.result() # [0, 2, 2, 2, 0]
```
Labuladong's visualizer `div_diff-array-example` for an array `[8,2,6,3,1]` and `increment(1,3,3)`:
Initial `nums = [8,2,6,3,1]`
`diff = [8, -6, 4, -3, -2]`
`increment(1,3,3)`:
- `diff[1] += 3`: `diff[1]` becomes `-6 + 3 = -3`. `diff = [8, -3, 4, -3, -2]`.
- `diff[3+1] -= 3`: `diff[4]` becomes `-2 - 3 = -5`. `diff = [8, -3, 4, -3, -5]`.
Resulting `nums`:
- `res[0] = 8`
- `res[1] = 8 + (-3) = 5`
- `res[2] = 5 + 4 = 9`
- `res[3] = 9 + (-3) = 6`
- `res[4] = 6 + (-5) = 1`
So, `nums` becomes `[8, 5, 9, 6, 1]`.
Original elements `nums[1..3]` were `[2,6,3]`. After adding 3: `[5,9,6]`. This matches.

##Complexity Analysis
- **Constructor `__init__`**: $O(N)$ to build the `diff` array.
- **`increment(i, j, val)`**: $O(1)$.
- **`result()`**: $O(N)$ to reconstruct the `nums` array.

If there are $M$ range updates and we need the final array:
- Naive: $M \times O(N) = O(MN)$.
- Difference Array: $O(N)$ (initial construction) + $M \times O(1)$ (updates) + $O(N)$ (final reconstruction) = $O(N+M)$.
This is a significant improvement if $M$ is large.

## 🚀 Applications

- **[[Interview/Practice/LeetCode/LC370 - Range Addition|LC370 - Range Addition]] (🔒):** Direct application.
- **[[Interview/Practice/LeetCode/LC1109 - Corporate Flight Bookings|LC1109 - Corporate Flight Bookings]]:** Problem describes flight bookings which are range updates on seats for flight numbers.
- **[[Interview/Practice/LeetCode/LC1094 - Car Pooling|LC1094 - Car Pooling]]:** Passenger trips define ranges (stations) and capacity changes (value). After all "updates" (passengers getting on/off), check if any station's passenger count exceeds capacity.

## ⚖️ Comparison with Prefix Sum
- **[[Interview/Concept/Algorithms/Array Manipulation/02 - Prefix Sum Array Technique|Prefix Sum Array]]**: Optimizes range *query* (sum) to $O(1)$ after $O(N)$ preprocessing, assuming array is immutable.
- **Difference Array**: Optimizes range *update* (add/subtract) to $O(1)$, with $O(N)$ for initial setup and final reconstruction.

They address opposite problems: prefix sums for fast queries on static arrays, difference arrays for fast updates with a final query.

## 拓展延伸 (Further Extensions - Labuladong)
Labuladong mentions that for problems requiring both frequent range updates AND frequent range queries, more advanced data structures like [[Interview/Concept/Data Structures/Tree/Segment Tree/00 - Segment Tree - Principles and Visualization|Segment Trees]] or Binary Indexed Trees (BITs) are needed.

## 总结 (Summary)
- The Difference Array technique allows for $O(1)$ updates to a range `[i, j]` by modifying only `diff[i]` and `diff[j+1]`.
- The original array can be reconstructed from the difference array in $O(N)$ time.
- It's highly effective when there are many range updates and only a final view of the array is needed, or when properties of the final array (like max value in Car Pooling) are required.
- It's a complementary technique to Prefix Sums.

---
| [[Interview/Concept/Algorithms/Array Manipulation/index|Back to Array Manipulation Index]] | [[Interview/Concept/Algorithms/Array Manipulation/02 - Prefix Sum Array Technique|Next: Prefix Sum Array]] |
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Array Manipulation"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Array Manipulation/01 - Difference Array Technique.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Array Manipulation/01 - Difference Array Technique.md")

def create_prefix_sum_array_technique(path):
    content = r"""---
tags: [concept/algorithms, concept/array_manipulation, type/technique, pattern/prefix_sum, course/labuladong]
aliases: [Prefix Sum Array, 前缀和数组]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷数组算法/小而美的算法技巧：前缀和数组.md]].
> This note explains the Prefix Sum Array technique for efficient range sum queries.

| [[Interview/Concept/Algorithms/Array Manipulation/01 - Difference Array Technique|Prev: Difference Array]] | [[Interview/Concept/Algorithms/Array Manipulation/index|Back to Array Manipulation Index]] |

# Prefix Sum Array Technique

The Prefix Sum Array (or cumulative sum array) is a technique used to quickly calculate the sum of elements within a given range `[i, j]` of an array. After an $O(N)$ preprocessing step to compute the prefix sum array, any range sum query can be answered in $O(1)$ time. This is highly efficient if the original array is immutable and range sum queries are frequent.

## 💡 Core Idea

1.  **Definition:** Given an array `nums`, its prefix sum array `preSum` is defined such that `preSum[k]` stores the sum of the first `k` elements of `nums`.
    - A common convention (used by Labuladong) is to make `preSum` one element longer than `nums`, with `preSum[0] = 0`.
    - `preSum[i]` then stores `nums[0] + nums[1] + ... + nums[i-1]`.
    - So, `preSum[k] = sum(nums[0...k-1])`.

2.  **Construction:**
    - `preSum[0] = 0`
    - `preSum[i] = preSum[i-1] + nums[i-1]` for `i > 0`.

3.  **Range Sum Query:** To find the sum of `nums[left...right]` (inclusive):
    - `sum(nums[left...right]) = nums[left] + ... + nums[right]`
    - This is equivalent to `(nums[0] + ... + nums[right]) - (nums[0] + ... + nums[left-1])`
    - Using the prefix sum array: `sum(nums[left...right]) = preSum[right+1] - preSum[left]`.

**Visualization (from Labuladong `![](/algo/images/difference/1.jpeg)`):**
`nums = [-2, 0, 3, -5, 2, -1]`
`preSum` construction:
- `preSum[0] = 0`
- `preSum[1] = preSum[0] + nums[0] = 0 + (-2) = -2`
- `preSum[2] = preSum[1] + nums[1] = -2 + 0 = -2`
- `preSum[3] = preSum[2] + nums[2] = -2 + 3 = 1`
- `preSum[4] = preSum[3] + nums[3] = 1 + (-5) = -4`
- `preSum[5] = preSum[4] + nums[4] = -4 + 2 = -2`
- `preSum[6] = preSum[5] + nums[5] = -2 + (-1) = -3`
So, `preSum = [0, -2, -2, 1, -4, -2, -3]`.

Query `sumRange(0, 2)` (sum of `nums[0..2]` = `-2 + 0 + 3 = 1`):
`preSum[2+1] - preSum[0] = preSum[3] - preSum[0] = 1 - 0 = 1`.

Query `sumRange(2, 5)` (sum of `nums[2..5]` = `3 + (-5) + 2 + (-1) = -1`):
`preSum[5+1] - preSum[2] = preSum[6] - preSum[2] = -3 - (-2) = -1`.

## 🛠️ Implementation: 1D Prefix Sum (Labuladong's `NumArray` for LC303)

```python
class NumArray:
    # Prefix sum array
    def __init__(self, nums: list[int]):
        # preSum[0] = 0, to simplify sumRange calculation
        self.preSum = [0] * (len(nums) + 1)
        # Calculate prefix sums
        for i in range(len(nums)):
            self.preSum[i+1] = self.preSum[i] + nums[i]
            # Or, using nums[i-1] convention from article:
            # self.preSum[i] = self.preSum[i-1] + nums[i-1] for i=1 to len(preSum)-1

    # Query sum of closed interval [left, right]
    def sumRange(self, left: int, right: int) -> int:
        # sum(nums[left...right]) = preSum[right+1] - preSum[left]
        return self.preSum[right + 1] - self.preSum[left]

# Example Usage:
# numArray = NumArray([-2, 0, 3, -5, 2, -1])
# print(numArray.sumRange(0, 2)) # Output: 1
# print(numArray.sumRange(2, 5)) # Output: -1
```
Labuladong's visualizer: `div_range-sum-query-immutable`.

## 🧱 2D Prefix Sum (for Matrices)

The concept extends to 2D arrays (matrices) for calculating sum of sub-rectangles.
Problem: [[Interview/Practice/LeetCode/LC304 - Range Sum Query 2D - Immutable|LC304 - Range Sum Query 2D - Immutable]].

- **Definition:** `preSum[r][c]` stores the sum of the rectangle from `matrix[0][0]` to `matrix[r-1][c-1]`.
- **Construction:**
  `preSum[r][c] = matrix[r-1][c-1] + preSum[r-1][c] + preSum[r][c-1] - preSum[r-1][c-1]`.
  This uses the principle of inclusion-exclusion.
  ```tikz
  \begin{tikzpicture}[
      rect_style/.style={draw, minimum width=1cm, minimum height=1cm, font=\sffamily\tiny},
      label_style/.style={font=\sffamily\scriptsize}
  ]
  % Grid for preSum[r-1][c-1], matrix[r-1][c-1], etc.
  \node[rect_style, fill=blue!10] (A) at (0,1) {$preSum[r-1][c-1]$}; % Area A
  \node[rect_style, fill=green!10] (B) at (1,1) {$col_{c-1} \text{ up to } row_{r-2}$}; % Area B (part of preSum[r][c-1] excluding A)
  \node[rect_style, fill=yellow!10] (C) at (0,0) {$row_{r-1} \text{ up to } col_{c-2}$}; % Area C (part of preSum[r-1][c] excluding A)
  \node[rect_style, fill=red!20] (D) at (1,0) {$matrix[r-1][c-1]$}; % Area D

  \node[label_style] at (3,1.5) {$preSum[r-1][c] = A+C$};
  \node[label_style] at (3,0.5) {$preSum[r][c-1] = A+B$};

  \node[label_style, text width=5cm, align=center] at (3,-1) {
    $preSum[r][c] = (A+B+C+D)$ \\
    $= D + (A+C) + (A+B) - A$ \\
    $= matrix[r-1][c-1] + preSum[r-1][c] + preSum[r][c-1] - preSum[r-1][c-1]$
  };
  \end{tikzpicture}
  ```

- **Range Sum Query `sumRegion(row1, col1, row2, col2)`:**
  Sum of rectangle defined by top-left `(row1, col1)` and bottom-right `(row2, col2)`.
  `sum = preSum[row2+1][col2+1] - preSum[row1][col2+1] - preSum[row2+1][col1] + preSum[row1][col1]`.
  This also uses inclusion-exclusion.
  Visual: `![](/algo/images/presum/5.jpeg)` (from Labuladong)

### Implementation: 2D Prefix Sum (`NumMatrix` for LC304)
```python
class NumMatrix:
    def __init__(self, matrix: list[list[int]]):
        if not matrix or not matrix[0]:
            # Handle empty matrix case if necessary, though problem constraints usually ensure non-empty.
            # For this template, let's assume valid matrix.
            self.preSum = None # Or raise error
            return

        rows, cols = len(matrix), len(matrix[0])
        # preSum[r][c] stores sum of rectangle from origin to matrix[r-1][c-1]
        self.preSum = [[0] * (cols + 1) for _ in range(rows + 1)]

        for r in range(1, rows + 1):
            for c in range(1, cols + 1):
                self.preSum[r][c] = matrix[r-1][c-1] + \
                                   self.preSum[r-1][c] + \
                                   self.preSum[r][c-1] - \
                                   self.preSum[r-1][c-1]

    def sumRegion(self, row1: int, col1: int, row2: int, col2: int) -> int:
        if not self.preSum: return 0 # Or handle error

        # Sum of matrix[row1..row2][col1..col2]
        return self.preSum[row2+1][col2+1] - \
               self.preSum[row1][col2+1] - \
               self.preSum[row2+1][col1] + \
               self.preSum[row1][col1]

# Example Usage:
# matrix = [[3,0,1,4,2],[5,6,3,2,1],[1,2,0,1,5],[4,1,0,1,7],[1,0,3,0,5]]
# numMatrix = NumMatrix(matrix)
# print(numMatrix.sumRegion(2,1,4,3)) # Expected: 8
```
Labuladong's visualizer: `div_range-sum-query-2d-immutable`.

## Complexity Analysis
- **1D Prefix Sum:**
    - Constructor: $O(N)$ time, $O(N)$ space.
    - `sumRange`: $O(1)$ time.
- **2D Prefix Sum:**
    - Constructor: $O(R \times C)$ time, $O(R \times C)$ space (where R, C are rows/cols).
    - `sumRegion`: $O(1)$ time.

## Limitations
1.  **Immutable Source Array:** Prefix sum technique assumes the original `nums` array (or `matrix`) does not change after `preSum` is computed. If `nums` changes, `preSum` must be recomputed.
2.  **Operation Must Have Inverse:** To find `sum(nums[left...right])` using `preSum[right+1] - preSum[left]`, subtraction is the inverse of addition. This works for sums, products (with division as inverse, careful with zeros). It doesn't work for operations like min/max if only prefix min/max is stored (e.g., `max(A[i..j])` is not `prefix_max[j] ??? prefix_max[i-1]`). For range min/max queries, [[Interview/Concept/Data Structures/Tree/Segment Tree/00 - Segment Tree - Principles and Visualization|Segment Trees]] or Sparse Tables are used.

## 总结 (Summary)
- Prefix Sum Array allows $O(1)$ range sum queries after $O(N)$ (or $O(RC)$ for 2D) preprocessing.
- A common trick is to make the `preSum` array 1-indexed or have `preSum[0]=0` to simplify range query formulas.
- Extends to 2D for sum of sub-rectangles.
- Ideal for scenarios with frequent range sum queries on a static array.
- Complements [[Interview/Concept/Algorithms/Array Manipulation/01 - Difference Array Technique|Difference Array]], which is for fast range updates.

---
| [[Interview/Concept/Algorithms/Array Manipulation/01 - Difference Array Technique|Prev: Difference Array]] | [[Interview/Concept/Algorithms/Array Manipulation/index|Back to Array Manipulation Index]] |
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Array Manipulation"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Array Manipulation/02 - Prefix Sum Array Technique.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Array Manipulation/02 - Prefix Sum Array Technique.md")

def create_2d_array_fancy_traversal(path):
    content = r"""---
tags: [concept/algorithms, topic/array, topic/matrix, pattern/matrix_traversal]
aliases: [2D Array Traversal, Matrix Traversal Techniques, 二维数组的花式遍历]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷数组算法/二维数组的花式遍历技巧.md]].
> This note covers techniques for rotating and spirally traversing 2D arrays (matrices).

# 2D Array: Fancy Traversal Techniques

Working with 2D arrays (matrices) often involves specific traversal patterns beyond simple row-by-row or column-by-column iteration. Labuladong highlights two common "fancy" techniques: matrix rotation and spiral traversal.

## I.順/逆時針旋轉矩陣 (Clockwise/Counter-Clockwise Matrix Rotation)

Rotating an $N \times N$ matrix by 90 degrees in-place is a common problem ([[Interview/Practice/LeetCode/LC48 - Rotate Image|LC48 - Rotate Image]]).

**Core Idea for 90-degree Clockwise Rotation:**
The trick is to decompose the rotation into two simpler steps:
1.  **Transpose the matrix:** Swap `matrix[i][j]` with `matrix[j][i]`. This flips the matrix along its main diagonal.
2.  **Reverse each row:** For each row, reverse its elements.

**Example:** `matrix = [[1,2,3],[4,5,6],[7,8,9]]`

1.  **Transpose:**
    ```
    1 4 7
    2 5 8
    3 6 9
    ```
2.  **Reverse each row:**
    ```
    7 4 1  (Row 1 reversed)
    8 5 2  (Row 2 reversed)
    9 6 3  (Row 3 reversed)
    ```
    This is the 90-degree clockwise rotated matrix.

**Visualization (from Labuladong):**
`![](/algo/images/2d-array/2.gif)` (Shows transpose)
`![](/algo/images/2d-array/3.gif)` (Shows row reversal)

**For 90-degree Counter-Clockwise Rotation:**
1.  **Transpose the matrix.**
2.  **Reverse each column.** (Alternatively: Reverse each row, then transpose. Or, transpose along anti-diagonal then reverse rows/cols).
    A simpler two-step for counter-clockwise:
    1. Reverse each row.
    2. Transpose the matrix.
    OR
    3. Reverse each column. (Harder to do in place if thinking row-major)
    4. Transpose the matrix.
    OR (often easiest)
    5. Transpose along the *anti-diagonal* (swap `matrix[i][j]` with `matrix[N-1-j][N-1-i]`).
    6. Reverse each row.
    OR (even simpler analogy to clockwise)
    7. Reverse columns (flip horizontally).
    8. Transpose matrix.

Let's stick to Labuladong's likely implication which builds on the clockwise method:
Clockwise: Transpose $\rightarrow$ Reverse Rows
Counter-Clockwise: Transpose $\rightarrow$ Reverse Columns (conceptually)
Or, more practically for implementation:
Counter-Clockwise: Reverse Rows $\rightarrow$ Transpose
   Example: `[[1,2,3],[4,5,6],[7,8,9]]`
   1. Reverse Rows: `[[3,2,1],[6,5,4],[9,8,7]]`
   2. Transpose: `[[3,6,9],[2,5,8],[1,4,7]]` (This is counter-clockwise)

Labuladong also mentions that string reversal ([[Interview/Practice/LeetCode/LC151 - Reverse Words in a String|LC151]]) and list rotation ([[Interview/Practice/LeetCode/LC61 - Rotate List|LC61]]) share a similar "multi-step reversal" thinking pattern.

## II. 螺旋遍历二维数组 (Spiral Matrix Traversal)

Traversing a matrix in a spiral pattern is another common task ([[Interview/Practice/LeetCode/LC54 - Spiral Matrix|LC54]], [[Interview/Practice/LeetCode/LC59 - Spiral Matrix II|LC59]]).

**Core Idea:** Simulate the spiral path by defining boundaries (top, bottom, left, right) and iteratively traversing the outermost layer, then shrinking the boundaries inwards.

1.  Initialize boundaries: `top = 0`, `bottom = num_rows - 1`, `left = 0`, `right = num_cols - 1`.
2.  Initialize result list and count of elements to visit (`total_elements = num_rows * num_cols`).
3.  Loop while `len(result) < total_elements`:
    a.  **Traverse Right (top row):** From `left` to `right` along `matrix[top]`. Add elements to result. Increment `top`.
    b.  **Traverse Down (rightmost column):** From `top` to `bottom` along `matrix[col=right]`. Add elements. Decrement `right`.
    c.  **Traverse Left (bottom row):** From `right` to `left` (descending) along `matrix[bottom]`. Add elements. Decrement `bottom`. (Check `top <= bottom` before this step if not square).
    d.  **Traverse Up (leftmost column):** From `bottom` to `top` (descending) along `matrix[col=left]`. Add elements. Increment `left`. (Check `left <= right` before this step).
4.  Handle edge cases like single row/column matrices carefully within the boundary checks.

**Visualization (from Labuladong):**
`![](/algo/images/2d-array/4.gif)` (Shows the spiral path and shrinking boundaries)

The implementation needs careful boundary condition checks, especially after shrinking a boundary (e.g., after traversing right and incrementing `top`, check if `top <= bottom` before traversing down).

## 总结 (Summary)
- **Matrix Rotation (90-degree clockwise):** Achieved by transposing the matrix and then reversing each row. Counter-clockwise can be done by reversing rows then transposing.
- **Spiral Traversal:** Simulate the path by maintaining `top, bottom, left, right` boundaries. Traverse one layer at a time (right, down, left, up) and shrink the boundaries.
- These techniques often appear in coding interviews and require careful implementation of loops and boundary conditions.

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]] (Or a new `Interview/Concept/Algorithms/Array Manipulation/index.md`)
Related Problems:
- [[Interview/Practice/LeetCode/LC48 - Rotate Image|LC48 - Rotate Image]]
- [[Interview/Practice/LeetCode/LC54 - Spiral Matrix|LC54 - Spiral Matrix]]
- [[Interview/Practice/LeetCode/LC59 - Spiral Matrix II|LC59 - Spiral Matrix II]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Array Manipulation"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Array Manipulation/00 - 2D Array Fancy Traversal.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Array Manipulation/00 - 2D Array Fancy Traversal.md")


def update_greedy_framework(path): # Alias for update_greedy_algorithms_framework
    update_greedy_algorithms_framework(path)
    print("Note: update_greedy_framework is an alias, called update_greedy_algorithms_framework.")

def create_tree_dp_unique_bsts(path):
    content = r"""---
tags: [concept/algorithms, concept/dynamic_programming, pattern/tree_dp, pattern/catalan_numbers, topic/bst, course/labuladong]
aliases: [Unique BSTs, Number of BSTs, Generating All BSTs, 不同的二叉搜索树]
---
> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷二叉树算法/二叉搜索树心法（构造篇）.md]].
> This note covers problems related to counting and generating all unique Binary Search Trees (BSTs) for a given number of nodes, often involving dynamic programming or recursive construction with memoization.

| [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Tree DP/index|Back to Tree DP Index]] | (Consider linking to general DP or BST concepts) |

# Unique Binary Search Trees (LC95 & LC96)

Problems involving counting or generating all unique Binary Search Trees (BSTs) given a set of distinct values (e.g., 1 to `n`) are classic examples that can be solved using dynamic programming and recursive construction.

## 1. Count Unique BSTs (LC96)
[[Interview/Practice/LeetCode/LC96 - Unique Binary Search Trees|LC96 - Unique Binary Search Trees]]
Given an integer `n`, return the number of structurally unique BST's which has exactly `n` nodes of unique values from 1 to `n`. This is equivalent to finding the n-th Catalan number.

**Recursive Decomposition Approach (with Memoization for DP):**
Let `G(n)` be the number of unique BSTs that can be formed with `n` nodes (values 1 to `n`).
To form a BST with `n` nodes, we can choose any `i` (from 1 to `n`) as the root.
- If `i` is the root:
    - The left subtree will be formed by `i-1` nodes (values 1 to `i-1`). Number of ways: `G(i-1)`.
    - The right subtree will be formed by `n-i` nodes (values `i+1` to `n`, which can be mapped to 1 to `n-i` for structure). Number of ways: `G(n-i)`.
- The total number of BSTs with `i` as root is `G(i-1) * G(n-i)`.
- Sum this over all possible roots `i`: $G(n) = \sum_{i=1}^{n} G(i-1) \cdot G(n-i)$
- Base cases: `G(0) = 1` (one way to form an empty tree), `G(1) = 1` (one way for a single node tree).

```python
class SolutionLC96:
    def numTrees(self, n: int) -> int:
        # memo[i] will store G(i)
        memo = {} 

        def count_unique_bsts(num_nodes: int) -> int:
            if num_nodes == 0 or num_nodes == 1:
                return 1
            if num_nodes in memo:
                return memo[num_nodes]
            
            total_trees = 0
            # Iterate through all possible root values (or rather, number of nodes in left/right)
            # For 'num_nodes' total nodes, if 'i' nodes are in the left subtree,
            # then 1 node is root, and 'num_nodes - 1 - i' nodes are in right subtree.
            for nodes_in_left_subtree in range(num_nodes): # 0 to num_nodes-1
                nodes_in_right_subtree = num_nodes - 1 - nodes_in_left_subtree
                
                total_trees += count_unique_bsts(nodes_in_left_subtree) * \
                               count_unique_bsts(nodes_in_right_subtree)
            
            memo[num_nodes] = total_trees
            return total_trees

        return count_unique_bsts(n)

    # Tabulation (Bottom-up DP) approach for LC96
    def numTrees_tabulation(self, n: int) -> int:
        # G[i] stores the number of unique BST's for i nodes.
        G = [0] * (n + 1)
        G[0] = 1 # Empty tree
        G[1] = 1 # Single node tree
        
        # Calculate G[i] for i from 2 to n
        for num_total_nodes in range(2, n + 1):
            for num_left_nodes in range(num_total_nodes): # Number of nodes in left subtree (0 to num_total_nodes-1)
                # Root takes 1 node.
                # num_right_nodes = num_total_nodes - 1 - num_left_nodes
                num_right_nodes = num_total_nodes - 1 - num_left_nodes
                G[num_total_nodes] += G[num_left_nodes] * G[num_right_nodes]
        
        return G[n]
```

## 2. Generate All Unique BSTs (LC95)
[[Interview/Practice/LeetCode/LC95 - Unique Binary Search Trees II|LC95 - Unique Binary Search Trees II]]
Given an integer `n`, return all the structurally unique BST's which has exactly `n` nodes of unique values from 1 to `n`. Return the answer in any order.

**Recursive Decomposition Approach:**
Define `build(low, high)`: returns a list of all unique BSTs that can be formed using values from `low` to `high` inclusive.
1.  Base Case: If `low > high`, no nodes in this range. Return `[None]` (representing an empty subtree choice for a parent).
2.  Iterate `i` from `low` to `high` (current root value):
    a.  Recursively get all possible left subtrees: `left_subtrees = build(low, i - 1)`.
    b.  Recursively get all possible right subtrees: `right_subtrees = build(i + 1, high)`.
    c.  For each `left_tree` in `left_subtrees` and each `right_tree` in `right_subtrees`:
        i.  Create `root = TreeNode(i)`.
        ii. `root.left = left_tree`.
        iii. `root.right = right_tree`.
        iv. Add `root` to the list of results for the current `(low, high)` range.
3.  Return the list of constructed root nodes.
Memoization can be used if `build(low, high)` calls overlap significantly, mapping `(low, high)` tuples to lists of tree roots.

```python
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None): self.val = val; self.left = left; self.right = right

class SolutionLC95:
    def generateTrees(self, n: int) -> list['TreeNode' | None]: # Type hint for TreeNode
        if n == 0:
            return []
        
        memo = {} # For memoization: (low, high) -> list_of_roots

        def _build_unique_bsts(low: int, high: int) -> list['TreeNode' | None]:
            if low > high:
                return [None] # Base case: represents an empty spot for a child
            
            if (low, high) in memo:
                return memo[(low, high)]

            all_trees_for_range = []
            # Iterate through all possible values for the root in the range [low, high]
            for root_val in range(low, high + 1):
                # Recursively generate all possible left subtrees
                left_subtrees = _build_unique_bsts(low, root_val - 1)
                # Recursively generate all possible right subtrees
                right_subtrees = _build_unique_bsts(root_val + 1, high)
                
                # Combine left and right subtrees with the current root
                # Need to define TreeNode or import it
                # Assuming TreeNode is defined elsewhere or this is part of a class
                # that has TreeNode definition available (e.g., from LeetCode context)
                for left_tree_root in left_subtrees:
                    for right_tree_root in right_subtrees:
                        # Replace 'TreeNode' with the actual class name if different
                        # For LeetCode, it's usually provided or implicitly available
                        current_root = TreeNode(root_val) 
                        current_root.left = left_tree_root
                        current_root.right = right_tree_root
                        all_trees_for_range.append(current_root)
            
            memo[(low, high)] = all_trees_for_range
            return all_trees_for_range

        return _build_unique_bsts(1, n)
```

## 总结 (Summary)
- Counting unique BSTs (LC96) is a DP problem solvable by finding a recurrence relation based on choosing each element as a root and summing up combinations of left/right subtrees. This is related to Catalan numbers.
- Generating all unique BSTs (LC95) uses a recursive construction approach. For a given range of values `[low, high]`, iterate through each possible root `i` in this range. Then, recursively generate all possible left subtrees (from `[low, i-1]`) and right subtrees (from `[i+1, high]`) and combine them. Memoization is used to store and reuse lists of generated subtrees for specific ranges.
- Both problems demonstrate the "decomposition" thinking where the problem for `n` items (or a range `[low,high]`) is broken down by considering each item as a potential root and solving for sub-ranges.

---
| [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Tree DP/index|Back to Tree DP Index]] | (Consider linking to general DP or BST concepts) |
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Tree DP"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Tree DP/00 - Unique Binary Search Trees (LC95 LC96).md"), 'w', encoding='utf-8') as f:
        # Add TreeNode definition for standalone script execution if needed
        # For LeetCode, TreeNode is usually predefined.
        f.write("# Definition for a binary tree node.\n# class TreeNode:\n#     def __init__(self, val=0, left=None, right=None):\n#         self.val = val\n#         self.left = left\n#         self.right = right\n\n" + content)
    print("Created: Interview/Concept/Algorithms/Dynamic Programming/Patterns/Tree DP/00 - Unique Binary Search Trees (LC95 LC96).md")

# ---- Main Script Logic ----
if __name__ == "__main__":
    # Define kb_root relative to the script's location or as an absolute path
    # This script expects to be run from a directory that is a sibling to "Interview" or contains "Interview"
    # For example, if script is in /Users/<USER>/Obsidian/MyVault/scripts/
    # and notes are in /Users/<USER>/Obsidian/MyVault/Interview/
    # then kb_root should point to /Users/<USER>/Obsidian/MyVault/
    kb_root = os.path.join(os.path.dirname(__file__), "../") # Adjust if script is elsewhere
    kb_root = os.path.abspath(kb_root) 
    print(f"Knowledge base operations will be relative to: {kb_root}")

    # Create/Update Conceptual Frameworks
    create_framework_thinking_philosophy(kb_root)
    
    update_backtracking_core_framework(kb_root)
    create_backtracking_patterns_permutations_combinations_subsets(kb_root)
    update_backtracking_index(kb_root)

    update_complexity_analysis_intro(kb_root)
    create_complexity_analysis_index(kb_root)
    create_asymptotic_analysis_guide_placeholder(kb_root)

    update_divide_and_conquer_framework(kb_root)
    create_divide_and_conquer_index(kb_root)

    update_dynamic_programming_framework(kb_root)
    create_dynamic_programming_index(kb_root)
    create_tree_dp_unique_bsts(kb_root) # As an example of a DP pattern

    update_greedy_algorithms_framework(kb_root)
    create_greedy_algorithms_index(kb_root)

    # Update/Create Hashing Patterns
    create_hashing_patterns_index(kb_root)
    update_hashing_two_sum_pattern(kb_root)
    update_hashing_detect_duplicates(kb_root)
    update_hashing_frequency_counting(kb_root)

    # Array Manipulation Techniques
    create_array_manipulation_index(kb_root)
    create_difference_array_technique(kb_root)
    create_prefix_sum_array_technique(kb_root)
    create_2d_array_fancy_traversal(kb_root)

    # Update general Algorithm Index
    # This function needs to be intelligent about adding to existing index
    # For now, a simple version, assuming it might overwrite or need manual merge
    algo_index_path = os.path.join(kb_root, "Interview/Concept/Algorithms/index.md")
    ensure_dir(os.path.dirname(algo_index_path))
    
    algo_index_content = r"""---
tags: [index, concept/algorithms]
aliases: [Algorithms Index, Algorithm Concepts]
---

# Algorithm Concepts

This index covers various algorithm design paradigms, specific algorithms, and analytical techniques relevant to coding interviews.

## Algorithm Categories / Patterns
- [[Interview/Concept/Algorithms/00 - Framework Thinking (Labuladong Philosophy)|Framework Thinking (Labuladong Philosophy)]]
- [[Interview/Concept/Algorithms/Array Manipulation/index|Array Manipulation]]
- [[Interview/Concept/Algorithms/Backtracking/index|Backtracking]]
- [[Interview/Concept/Algorithms/Complexity Analysis/index|Complexity Analysis]]
- [[Interview/Concept/Algorithms/Divide and Conquer/index|Divide and Conquer]]
- [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]]
- [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal Algorithms]]
- [[Interview/Concept/Algorithms/Greedy Algorithms/index|Greedy Algorithms]]
- [[Interview/Concept/Algorithms/Hashing/index|Hashing Patterns & Techniques]]
- [[Interview/Concept/Algorithms/Linked List/index|Linked List Techniques]]
- [[Interview/Concept/Algorithms/Randomized Algorithms/index|Randomized Algorithms]]
- [[Interview/Concept/Algorithms/Recursion/index|Recursion Strategies]]
- [[Interview/Concept/Algorithms/Searching/index|Searching Algorithms]]
- [[Interview/Concept/Algorithms/Sliding Window/index|Sliding Window]]
- [[Interview/Concept/Algorithms/Sorting/index|Sorting Algorithms]]
- [[Interview/Concept/Algorithms/String Matching/index|String Matching]]
- [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal Algorithms]]
- [[Interview/Concept/Algorithms/Two Pointers/index|Two Pointers]]
- [[Interview/Concept/Algorithms/Problem Solving Patterns/index|Problem Solving Patterns]]


## Visualization of Algorithm Areas
```mermaid
graph TD
    Algo["Algorithms"]

    Algo --> FrameworkThink["[[Interview/Concept/Algorithms/00 - Framework Thinking (Labuladong Philosophy)|Framework Thinking]]"]
    Algo --> ArrayManip["[[Interview/Concept/Algorithms/Array Manipulation/index|Array Manipulation]]"]
    Algo --> Backtracking["[[Interview/Concept/Algorithms/Backtracking/index|Backtracking]]"]
    Algo --> Complexity["[[Interview/Concept/Algorithms/Complexity Analysis/index|Complexity Analysis]]"]
    Algo --> DnC["[[Interview/Concept/Algorithms/Divide and Conquer/index|Divide & Conquer]]"]
    Algo --> DP["[[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]]"]
    Algo --> GraphT["[[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal]]"]
    Algo --> Greedy["[[Interview/Concept/Algorithms/Greedy Algorithms/index|Greedy Algorithms]]"]
    Algo --> Hashing["[[Interview/Concept/Algorithms/Hashing/index|Hashing]]"]
    Algo --> LinkedListTech["[[Interview/Concept/Algorithms/Linked List/index|Linked List Techniques]]"]
    Algo --> RandAlgo["[[Interview/Concept/Algorithms/Randomized Algorithms/index|Randomized Algos]]"]
    Algo --> Recursion["[[Interview/Concept/Algorithms/Recursion/index|Recursion]]"]
    Algo --> SearchAlgo["[[Interview/Concept/Algorithms/Searching/index|Searching]]"]
    Algo --> SlidingWindow["[[Interview/Concept/Algorithms/Sliding Window/index|Sliding Window]]"]
    Algo --> Sorting["[[Interview/Concept/Algorithms/Sorting/index|Sorting]]"]
    Algo --> StrMatch["[[Interview/Concept/Algorithms/String Matching/index|String Matching]]"]
    Algo --> TreeT["[[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal]]"]
    Algo --> TwoPointers["[[Interview/Concept/Algorithms/Two Pointers/index|Two Pointers]]"]
    Algo --> PSP["[[Interview/Concept/Algorithms/Problem Solving Patterns/index|Problem Solving Patterns]]"]


    classDef category fill:#e0ffff,stroke:#008b8b,stroke-width:2px;
    class Algo, FrameworkThink, ArrayManip, Backtracking, Complexity, DnC, DP, GraphT, Greedy, Hashing, LinkedListTech, RandAlgo, Recursion, SearchAlgo, SlidingWindow, Sorting, StrMatch, TreeT, TwoPointers, PSP category;
```

An important purpose of this folder is to build educaitonal philosophical algorhtm concepts along with robust framework thinking, step by step, motivation, intuition, etc. to help ppl truly learn the algorithm efficiently and effectively.
"""
    # Create index file for Problem Solving Patterns
    psp_index_path = os.path.join(kb_root, "Interview/Concept/Algorithms/Problem Solving Patterns/index.md")
    ensure_dir(os.path.dirname(psp_index_path))
    psp_index_content = r"""---
tags: [index, concept/algorithms, concept/problem_solving_pattern]
aliases: [Problem Solving Patterns Index, Algorithmic Meta-Strategies]
---

# Problem Solving Patterns

This section outlines common meta-strategies and patterns for approaching algorithmic problems. These patterns often complement specific algorithmic paradigms like DP, Greedy, etc.

## Core Patterns:
- [[Interview/Concept/Algorithms/Problem Solving Patterns/Constraint Satisfaction|Constraint Satisfaction]]
- [[Interview/Concept/Algorithms/Problem Solving Patterns/Case Analysis|Case Analysis]]
- [[Interview/Concept/Algorithms/Problem Solving Patterns/Problem Reframing|Problem Reframing]]

## Visualization
```mermaid
graph TD
    PSP["Problem Solving Patterns"] --> CS["[[Interview/Concept/Algorithms/Problem Solving Patterns/Constraint Satisfaction|Constraint Satisfaction]]"]
    PSP --> CA["[[Interview/Concept/Algorithms/Problem Solving Patterns/Case Analysis|Case Analysis]]"]
    PSP --> PR["[[Interview/Concept/Algorithms/Problem Solving Patterns/Problem Reframing|Problem Reframing]]"]

    classDef main fill:#fff5e6,stroke:#ffaa00,stroke-width:2px;
    class PSP main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
"""
    with open(psp_index_path, 'w', encoding='utf-8') as f:
        f.write(psp_index_content)
    print(f"Created/Updated: {psp_index_path}")

    with open(algo_index_path, 'w', encoding='utf-8') as f:
        f.write(algo_index_content)
    print(f"Created/Updated: {algo_index_path}")
    
    print("Script finished. Selected files created/updated.")

