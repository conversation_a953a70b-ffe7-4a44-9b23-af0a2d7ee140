
import os
import textwrap

# Helper function to create directories if they don't exist
def ensure_dir(directory):
    os.makedirs(directory, exist_ok=True)

# --- Content Generation Functions ---

def create_dsu_principles_and_motivation(path):
    content = r"""---
tags: [concept/data_structures, concept/graph, concept/union_find, concept/dsu, type/introduction, type/principles, concept/connectivity, course/labuladong]
aliases: [Union-Find Basics, Disjoint Set Union, DSU, 并查集原理, Dynamic Connectivity]
---

> [!NOTE] Source Annotation
> Content primarily adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/图结构基础及算法概览/Union Find 并查集原理.md|Union Find 并查集原理 by Labuladong]].
> This note expands on the existing introduction to Union-Find.

# Union-Find (Disjoint Set Union - DSU): Principles and Motivation

The Union-Find (or Disjoint Set Union/DSU) data structure is designed to efficiently manage a collection of disjoint sets. It's particularly powerful for solving **dynamic connectivity** problems in (undirected) graphs: determining if two nodes are in the same connected component, merging components, and counting components.

## ❓ The Dynamic Connectivity Problem

Given a set of $N$ elements (often representing nodes in a graph), we want to support two main operations:
1.  **`union(p, q)`:** Connect elements `p` and `q`. If they are already connected, do nothing. This effectively merges the sets (or connected components) containing `p` and `q`.
2.  **`connected(p, q)`:** Determine if `p` and `q` are in the same set (connected component).
Additionally, we might want:
3.  **`count()`:** Return the number of disjoint sets (connected components).

**Example Scenario (from Labuladong):**
Imagine 10 nodes (0-9) initially all disconnected. Each is its own connected component.
- `union(0, 1)`: Nodes 0 and 1 become connected. Components: `{0,1}, {2}, ..., {9}`. Count = 9.
- `union(1, 2)`: Nodes 0, 1, 2 become connected (due to transitivity: 0-1, 1-2 $\implies$ 0-2). Components: `{0,1,2}, {3}, ..., {9}`. Count = 8.
- `connected(0, 2)`: Returns `true`.
- `connected(0, 5)`: Returns `false`.

Labuladong's article illustrates this with a sequence of images for nodes 0-9:
- Initial state: `![](/algo/images/unionfind/1.jpg)` (Each node separate)
- After `union(0,1)` and `union(1,2)`: `![](/algo/images/unionfind/2.jpg)` (0,1,2 form a component)

**Connectivity Properties:**
Connectivity is an equivalence relation:
- **Reflexive:** `p` is connected to `p`.
- **Symmetric:** If `p` is connected to `q`, then `q` is connected to `p`.
- **Transitive:** If `p` is connected to `q`, and `q` is connected to `r`, then `p` is connected to `r`.
The disjoint sets formed by Union-Find are precisely these equivalence classes.

## Why Standard Graph Traversals Are Not Ideal for *Dynamic* Connectivity

If we model the problem using a standard [[Interview/Concept/Data Structures/Graph/01 - Graph - Representation (Adjacency List, Adjacency Matrix)|graph representation]] (e.g., adjacency list):
- **`union(p, q)`:** Add an edge between `p` and `q`. This is $O(1)$ for adjacency lists.
- **`connected(p, q)`:** Requires a [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|DFS]] or [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|BFS]] starting from `p` to see if `q` is reachable. This is $O(V+E)$ time.
- **`count()`:** Requires traversing the entire graph to identify all connected components, also $O(V+E)$.

If there are many `connected` or `count` queries interspersed with `union` operations, the $O(V+E)$ cost per query becomes too slow. The Union-Find data structure aims to make `union` and `connected` operations nearly constant time (amortized $O(\alpha(N))$, where $\alpha$ is the inverse Ackermann function, which is extremely slow-growing and practically constant for all realistic $N$).

## 🌲 Union-Find as a Forest of Trees

The core idea of Union-Find is to represent each set (connected component) as a tree structure.
- Each element is a node in one of these trees.
- Each tree has a unique **root** node that serves as the representative (or identifier) of the set.
- To check if `p` and `q` are connected, we find the roots of the trees containing `p` and `q`. If the roots are the same, `p` and `q` are in the same set and thus connected.
- To union sets containing `p` and `q`, we find their respective roots. If the roots are different, we make one root a child of the other, effectively merging the two trees (and thus the two sets).

**Data Representation:**
A simple way to represent this forest of trees is using a **parent array**:
- `parent[i]` stores the parent of element `i`.
- If `parent[i] == i`, then `i` is a root node.

**Initial State:**
Initially, each element `i` is in its own set, so `parent[i] = i` for all `i`. The number of components `count` is $N$.

Labuladong illustrates the tree structure dynamically:
- `union(0,1)`: `parent[1] = 0`. Tree: `0 <- 1`.
- `union(2,3)`: `parent[3] = 2`. Tree: `2 <- 3`.
- `union(0,2)` (effectively `union(find(0), find(2))`): `parent[root_of_2 (2)] = root_of_0 (0)`. So `parent[2] = 0`.
The forest becomes: `0 <- 1` and `0 <- 2 <- 3`.

```tikz
\begin{tikzpicture}[
    tn/.style={circle, draw, font=\sffamily\small, minimum size=6mm, inner sep=1pt},
    arr/.style={->, >=stealth, thick}
]
    % Initial state: 0, 1, 2, 3 are separate components (roots)
    \node at (2.25, 1) {Initial State};
    \node[tn] (n0_init) at (0,0) {0}; \node[below=0.05cm of n0_init, font=\tiny] {p[0]=0};
    \node[tn] (n1_init) at (1.5,0) {1}; \node[below=0.05cm of n1_init, font=\tiny] {p[1]=1};
    \node[tn] (n2_init) at (3,0) {2}; \node[below=0.05cm of n2_init, font=\tiny] {p[2]=2};
    \node[tn] (n3_init) at (4.5,0) {3}; \node[below=0.05cm of n3_init, font=\tiny] {p[3]=3};

    % After union(0,1) -> parent[1]=0
    \node at (2.25, -1.5) {After union(0,1)};
    \node[tn] (u0) at (0,-2.5) {0}; \node[below=0.05cm of u0, font=\tiny] {p[0]=0};
    \node[tn] (u1) at (0,-3.5) {1}; \node[below=0.05cm of u1, font=\tiny] {p[1]=0};
    \draw[arr] (u1) -- (u0); % 1 points to 0
    
    \node[tn] (u2) at (3,-2.5) {2}; \node[below=0.05cm of u2, font=\tiny] {p[2]=2};
    \node[tn] (u3) at (4.5,-2.5) {3}; \node[below=0.05cm of u3, font=\tiny] {p[3]=3};

    % After union(0,2) -> e.g. parent[root_of_2_component (2)] = root_of_0_component (0) => parent[2]=0
    % Assuming union(2,3) was also called, so parent[3]=2
    \node at (2.25, -5) {If also union(2,3) then union(0,2)};
    \node[tn] (w0) at (1.5,-6) {0}; \node[below=0.05cm of w0, font=\tiny] {p[0]=0};
    \node[tn] (w1) at (0.75,-7) {1}; \node[below=0.05cm of w1, font=\tiny] {p[1]=0};
    \node[tn] (w2) at (2.25,-7) {2}; \node[below=0.05cm of w2, font=\tiny] {p[2]=0};
    \node[tn] (w3) at (2.25,-8) {3}; \node[below=0.05cm of w3, font=\tiny] {p[3]=2};
    \draw[arr] (w1) -- (w0);
    \draw[arr] (w2) -- (w0);
    \draw[arr] (w3) -- (w2);
\end{tikzpicture}
```

### Core Operations (Conceptual)

1.  **`find(p)`:** Returns the representative (root) of the set containing `p`.
    - Start at `p`. While `parent[p] != p`, move up: `p = parent[p]`.
    - The final `p` is the root.
2.  **`union(p, q)`:**
    - `rootP = find(p)`
    - `rootQ = find(q)`
    - If `rootP != rootQ` (they are in different sets):
        - Set `parent[rootQ] = rootP` (or `parent[rootP] = rootQ`). Make one root child of other.
        - Decrement `count`.
3.  **`connected(p, q)`:**
    - Return `find(p) == find(q)`.
4.  **`count()`:** Returns the current value of `count`.

## ⚠️ The Problem with Naive Union-Find

The `find` operation, as described above, can be slow if the trees become tall and skewed (like a linked list). In the worst case, `find` can take $O(N)$ time. If `union` calls `find` twice, `union` can also be $O(N)$.
Labuladong's visualization panel `div_uf-native-issue` (and image `![](/algo/images/unionfind/3.gif)`) demonstrates this: repeatedly unioning adjacent elements like `union(0,1), union(1,2), union(2,3), ...` can create a linked-list-like tree, degrading performance.

## Optimizations (Key to Efficiency)

Two main optimizations make Union-Find nearly constant time:
1.  **Path Compression (路径压缩):** During `find(p)`, after finding the root, make all nodes on the path from `p` to the root point directly to the root. This flattens the tree.
2.  **Union by Size/Rank (按大小/秩合并):** When unioning two trees, make the root of the smaller tree a child of the root of the larger tree. "Size" refers to the number of nodes in the tree. "Rank" is an upper bound on the height of the tree. This helps keep trees shorter.

These optimizations, when used together, lead to the amortized $O(\alpha(N))$ complexity. The details of these optimizations and their impact are covered in [[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/01 - Union-Find - Implementation with Optimizations|Union-Find - Implementation with Optimizations]].

## 总结 (Summary)
- Union-Find (DSU) efficiently solves dynamic connectivity problems.
- It represents sets as a forest of trees, using a parent array.
- Core operations: `find` (get set representative/root), `union` (merge sets), `connected` (check if in same set).
- Naive implementation can lead to $O(N)$ `find` operations due to skewed trees.
- **Optimizations (Path Compression and Union by Size/Rank) are crucial** for achieving nearly constant time (amortized $O(\alpha(N))$) performance.
- The structure does not explicitly store the graph's edges, only connectivity information.

---
Parent: [[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/index|DSU / Union-Find Index]]
Next: [[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/01 - Union-Find - Implementation with Optimizations|Union-Find - Implementation with Optimizations]]
Related: [[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|N-ary Trees]], [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal]] (for alternative connectivity checking)
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Data Structures/Disjoint Set Union (DSU)"))
    with open(os.path.join(path, "Interview/Concept/Data Structures/Disjoint Set Union (DSU)/00 - Union-Find - Principles and Motivation.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created/Updated: Interview/Concept/Data Structures/Disjoint Set Union (DSU)/00 - Union-Find - Principles and Motivation.md")

def create_dsu_implementation_with_optimizations(path):
    content = r"""---
tags: [concept/data_structures, concept/graph, concept/union_find, concept/dsu, type/implementation, concept/path_compression, concept/union_by_size_rank, course/labuladong]
aliases: [Optimized Union-Find, Union-Find Implementation, Path Compression, Union by Size, Union by Rank]
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/图结构基础及算法概览/Union Find 并查集原理.md|Union Find 并查集原理 by Labuladong]], specifically sections detailing optimizations.

# Union-Find: Implementation with Optimizations

The [[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/00 - Union-Find - Principles and Motivation|naive Union-Find algorithm]] can suffer from performance degradation if the trees representing sets become tall and skewed. Two key optimizations, **Path Compression** and **Union by Size/Rank**, dramatically improve its efficiency, leading to nearly constant amortized time per operation.

## 1. Path Compression (路径压缩)

> [!DEFINITION] Path Compression
> During a `find(x)` operation, after the root of `x` is found, all nodes along the path from `x` to the root are made to point directly to the root.

**Motivation:** This flattens the tree structure, making subsequent `find` operations for nodes on this path (and their descendants) much faster.

**Implementation:**
The `find` operation is modified. A common way is recursive:
```python
# (Inside UF class)
# self.parent = [...] # Parent array

def find(self, x: int) -> int:
    if self.parent[x] != x: # If x is not its own root
        # Path compression: make parent[x] point directly to the root of x's set.
        # This happens recursively up the chain.
        self.parent[x] = self.find(self.parent[x]) 
    return self.parent[x]
```
Labuladong's article visualizes this:
- Before path compression (`![](/algo/images/unionfind/4.gif)`): `find(7)` might traverse 7 -> 5 -> 3 -> 1 (root).
- After path compression (`![](/algo/images/unionfind/5.gif)`): 7, 5, 3 all point directly to 1.

```tikz
\begin{tikzpicture}[
    tn/.style={circle, draw, font=\sffamily\small, minimum size=6mm, inner sep=1pt},
    arr/.style={->, >=stealth, thick},
    arr_comp/.style={arr, red, dashed, shorten >=1pt, shorten <=1pt}
]
    % Before Path Compression
    \node at (1.5, 1) {Before Path Compression on find(7)};
    \node[tn] (r1) at (1.5,0) {1 (root)};
    \node[tn] (n3) at (1.5,-1) {3}; \draw[arr] (n3) -- (r1);
    \node[tn] (n5) at (1.5,-2) {5}; \draw[arr] (n5) -- (n3);
    \node[tn] (n7) at (1.5,-3) {7}; \draw[arr] (n7) -- (n5);
    \node[tn] (n0) at (0.5,-1) {0}; \draw[arr] (n0) -- (r1);
    \node[tn] (n6) at (2.5,-2) {6}; \draw[arr] (n6) -- (n3);

    % After Path Compression
    \node at (6.5, 1) {After Path Compression on find(7)};
    \node[tn] (r1_after) at (6.5,0) {1 (root)};
    \node[tn] (n3_after) at (5.5,-1) {3}; \draw[arr_comp] (n3_after) -- (r1_after);
    \node[tn] (n5_after) at (6.5,-1) {5}; \draw[arr_comp] (n5_after) -- (r1_after);
    \node[tn] (n7_after) at (7.5,-1) {7}; \draw[arr_comp] (n7_after) -- (r1_after);
    \node[tn] (n0_after) at (4.5,-1) {0}; \draw[arr] (n0_after) -- (r1_after); % Unchanged by find(7)
    \node[tn] (n6_after) at (5.5,-2) {6}; \draw[arr] (n6_after) -- (n3_after); % Pointing to 3, which now points to root
                                                                             % Further finds would compress this too.
\end{tikzpicture}
```

## 2. Union by Size / Rank (按大小/秩合并)

> [!DEFINITION] Union by Size/Rank
> When merging two sets (trees) in a `union(p, q)` operation, instead of arbitrarily making one root the child of the other, attach the root of the smaller tree to the root of the larger tree. "Size" refers to the number of elements in the set. "Rank" (an alternative, often simpler to implement) refers to an upper bound on the height of the tree.

**Motivation:** This strategy helps keep the trees flatter and less skewed, reducing the average depth of nodes and thus speeding up `find` operations.

**Implementation (Union by Size):**
- Maintain an additional array `sz[]`, where `sz[i]` stores the size of the tree rooted at `i` (only meaningful if `i` is a root).
- When `union(p,q)`:
    - Find roots: `rootP = find(p)`, `rootQ = find(q)`.
    - If `rootP != rootQ`:
        - If `sz[rootP] < sz[rootQ]`, then `parent[rootP] = rootQ` and `sz[rootQ] += sz[rootP]`.
        - Else, `parent[rootQ] = rootP` and `sz[rootP] += sz[rootQ]`.

**Implementation (Union by Rank):** (Rank is like an estimated height)
- Maintain an array `rank[]`, where `rank[i]` is the rank of the tree rooted at `i`. Initially, all ranks are 0.
- When `union(p,q)`:
    - Find roots: `rootP = find(p)`, `rootQ = find(q)`.
    - If `rootP != rootQ`:
        - If `rank[rootP] < rank[rootQ]`, then `parent[rootP] = rootQ`. (Rank of `rootQ` doesn't change).
        - Else if `rank[rootP] > rank[rootQ]`, then `parent[rootQ] = rootP`. (Rank of `rootP` doesn't change).
        - Else (`rank[rootP] == rank[rootQ]`), arbitrarily set `parent[rootQ] = rootP` and increment `rank[rootP]` by 1.

Labuladong's code typically uses Union by Size.
Visual for Union by Size (`![](/algo/images/unionfind/6.gif)`): Shows connecting smaller tree to larger tree.

## Full Union-Find Class (Python, with Path Compression and Union by Size)

```python
class UF:
    def __init__(self, n: int):
        # parent[i] = parent of element i
        self.parent = list(range(n))
        # sz[i] = size of component rooted at i (only valid if i is a root)
        self.sz = [1] * n
        # Number of disjoint sets
        self.num_components = n

    # Find the root of the set containing x, with path compression
    def find(self, x: int) -> int:
        if self.parent[x] != x:
            self.parent[x] = self.find(self.parent[x]) # Path compression
        return self.parent[x]

    # Unite the sets containing x and y using union by size
    def union(self, x: int, y: int) -> bool:
        rootX = self.find(x)
        rootY = self.find(y)

        if rootX == rootY:
            return False # x and y are already in the same set

        # Attach smaller tree under root of larger tree
        if self.sz[rootX] < self.sz[rootY]:
            self.parent[rootX] = rootY
            self.sz[rootY] += self.sz[rootX]
        else:
            self.parent[rootY] = rootX
            self.sz[rootX] += self.sz[rootY]
        
        self.num_components -= 1
        return True

    # Check if x and y are in the same set
    def connected(self, x: int, y: int) -> bool:
        return self.find(x) == self.find(y)

    # Return the number of disjoint sets
    def count(self) -> int:
        return self.num_components
```
Labuladong's `div_uf-optimized` visualizer panel demonstrates the behavior with both optimizations.

## Complexity Analysis (with both optimizations)
- **`find`:** Amortized $O(\alpha(N))$, where $\alpha$ is the Inverse Ackermann function. $\alpha(N)$ is extremely slow-growing; for all practical values of $N$, $\alpha(N) < 5$. So, it's nearly constant time.
- **`union`:** Also amortized $O(\alpha(N))$ because it calls `find` twice.
- **`connected`:** Amortized $O(\alpha(N))$.
- **`count`:** $O(1)$.
- **Constructor:** $O(N)$ to initialize `parent` and `sz` arrays.

## 总结 (Summary)
- **Path Compression** during `find` operations flattens the trees by making nodes on the find path point directly to the root.
- **Union by Size (or Rank)** during `union` operations attaches the smaller tree to the root of the larger tree, helping to keep trees balanced and shallow.
- When both optimizations are used, Union-Find operations (`find`, `union`, `connected`) achieve an amortized time complexity of nearly constant time ($O(\alpha(N))$).
- This makes Union-Find extremely efficient for dynamic connectivity problems.

---
Parent: [[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/00 - Union-Find - Principles and Motivation|Union-Find - Principles and Motivation]]
Next: [[Interview/Practice/LeetCode/LC130 - Surrounded Regions|LC130 - Surrounded Regions]] (as an application example)
Related Concepts: [[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|N-ary Trees]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Data Structures/Disjoint Set Union (DSU)"))
    with open(os.path.join(path, "Interview/Concept/Data Structures/Disjoint Set Union (DSU)/01 - Union-Find - Implementation with Optimizations.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Data Structures/Disjoint Set Union (DSU)/01 - Union-Find - Implementation with Optimizations.md")

def create_dsu_index(path):
    content = r"""---
tags: [index, concept/data_structures, concept/dsu, concept/union_find, concept/graph, course/labuladong]
aliases: [DSU Index, Union-Find Index, Disjoint Set Union Index]
---

# Disjoint Set Union (DSU) / Union-Find

This section covers the Union-Find data structure, also known as Disjoint Set Union (DSU), used for managing disjoint sets and solving dynamic connectivity problems.

## Core Concepts & Implementation:
- [[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/00 - Union-Find - Principles and Motivation|Union-Find - Principles and Motivation]]
  - Dynamic Connectivity Problem
  - Representation as a Forest
  - Challenges with Naive Implementation
- [[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/01 - Union-Find - Implementation with Optimizations|Union-Find - Implementation with Optimizations]]
  - Path Compression
  - Union by Size/Rank

## Applications:
- Kruskal's algorithm for Minimum Spanning Trees ([[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/01 - Kruskal Algorithm|Kruskal Algorithm]])
- Detecting cycles in undirected graphs
- Network connectivity problems
- LeetCode Problems:
    - [[Interview/Practice/LeetCode/LC130 - Surrounded Regions|LC130 - Surrounded Regions]]
    - [[Interview/Practice/LeetCode/LC323 - Number of Connected Components in an Undirected Graph|LC323 - Number of Connected Components]]
    - [[Interview/Practice/LeetCode/LC684 - Redundant Connection|LC684 - Redundant Connection]]
    - [[Interview/Practice/LeetCode/LC990 - Satisfiability of Equality Equations|LC990 - Satisfiability of Equality Equations]]

## Placeholder for Exercises List:
- [[Interview/Practice/Topic Exercises/Union-Find Exercises Index|Union-Find Exercises Collection]]

## Visualization
```mermaid
graph TD
    DSUConcept["DSU / Union-Find"] --> Principles["[[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/00 - Union-Find - Principles and Motivation|Principles & Motivation]]"]
    DSUConcept --> Implementation["[[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/01 - Union-Find - Implementation with Optimizations|Implementation & Optimizations]]"]
    
    Principles --> ProbDef["Dynamic Connectivity"]
    Principles --> ForestRep["Forest Representation"]
    Principles --> NaiveIssues["Naive Implementation Issues"]
    
    Implementation --> PathComp["Path Compression"]
    Implementation --> UnionBySizeRank["Union by Size/Rank"]

    DSUConcept --> Apps["Applications"]
    Apps --> Kruskal["Kruskal's MST"]
    Apps --> CycleDetect["Cycle Detection"]
    Apps --> LCProbs["LeetCode Problems (LC130, LC323, etc.)"]
    
    DSUConcept --> ExercisesLink["[[Interview/Practice/Topic Exercises/Union-Find Exercises Index|Exercises List]]"]

    classDef main fill:#fff0e6,stroke:#ff8c00,stroke-width:2px;
    class DSUConcept main;
```

---
Parent: [[Interview/Concept/Data Structures/index|Data Structures Index]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Data Structures/Disjoint Set Union (DSU)"))
    with open(os.path.join(path, "Interview/Concept/Data Structures/Disjoint Set Union (DSU)/index.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Updated: Interview/Concept/Data Structures/Disjoint Set Union (DSU)/index.md")

def create_mst_index(path):
    content = r"""---
tags: [index, concept/algorithms, concept/graph_traversal, topic/mst, course/labuladong]
aliases: [Minimum Spanning Tree Index, MST Index]
---

# Minimum Spanning Tree (MST) Algorithms

This section covers algorithms for finding the Minimum Spanning Tree (MST) of a connected, undirected, weighted graph.

## Core Concepts:
- [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/00 - MST - Introduction|MST - Introduction and Properties]]

## Algorithms:
- [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/01 - Kruskal Algorithm|Kruskal's Algorithm]]
- [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/02 - Prim Algorithm|Prim's Algorithm]]

## LeetCode Examples:
- [[Interview/Practice/LeetCode/LC1135 - Connecting Cities With Minimum Cost|LC1135 - Connecting Cities With Minimum Cost]]
- [[Interview/Practice/LeetCode/LC1584 - Min Cost to Connect All Points|LC1584 - Min Cost to Connect All Points]]
- [[Interview/Practice/LeetCode/LC261 - Graph Valid Tree|LC261 - Graph Valid Tree]] (Related, as MST properties can determine if a graph is a tree and its cost)

## Visualization
```mermaid
graph TD
    MSTConcept["Minimum Spanning Tree"] --> IntroMST["[[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/00 - MST - Introduction|Introduction & Properties]]"]
    MSTConcept --> Algos["Algorithms"]
    Algos --> Kruskal["[[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/01 - Kruskal Algorithm|Kruskal]]"]
    Algos --> Prim["[[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/02 - Prim Algorithm|Prim]]"]

    MSTConcept --> ExamplesMST["LeetCode Examples (LC1135, LC1584)"]

    classDef main fill:#e6ffe6,stroke:#006400,stroke-width:2px;
    class MSTConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal Algorithms]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/index.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/index.md")

def create_mst_introduction(path):
    content = r"""---
tags: [concept/algorithms, concept/graph, topic/mst, type/introduction, course/labuladong]
aliases: [MST Introduction, Minimum Spanning Forest]
---

> [!NOTE] Source Annotation
> General concepts adapted from typical graph theory and Labuladong's discussions on MST algorithms like [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/Kruskal 最小生成树算法.md|Kruskal]] and [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/Prim 最小生成树算法.md|Prim]].

# Minimum Spanning Tree (MST): Introduction and Properties

A **Minimum Spanning Tree (MST)** or Minimum Weight Spanning Tree is a subset of the edges of a connected, edge-weighted undirected graph that connects all the vertices together, without any cycles and with the minimum possible total edge weight. If a graph is not connected, then one can find a **Minimum Spanning Forest (MSF)**, which is a union of MSTs for each connected component.

## 核心概念 (Core Concepts)

1.  **Spanning Tree:**
    - A subgraph that includes all vertices of the original graph.
    - Is a tree (i.e., it's acyclic and connected).
    - For a graph with $V$ vertices, any spanning tree will have exactly $V-1$ edges.

2.  **Minimum Spanning Tree:**
    - Among all possible spanning trees of a given weighted graph, an MST is one whose sum of edge weights is minimized.
    - An MST might not be unique. If multiple edges have the same weight, there could be multiple MSTs with the same minimum total weight.

**Illustration:**
Consider a weighted undirected graph:
```tikz
\begin{tikzpicture}[
    vertex/.style={circle, draw, fill=blue!20, minimum size=7mm},
    edge_label/.style={font=\sffamily\scriptsize, fill=white, inner sep=1pt}
]
    \node[vertex] (A) at (0,2) {A};
    \node[vertex] (B) at (2,2) {B};
    \node[vertex] (C) at (0,0) {C};
    \node[vertex] (D) at (2,0) {D};

    \draw (A) -- (B) node[midway, edge_label] {1};
    \draw (A) -- (C) node[midway, edge_label] {4};
    \draw (B) -- (C) node[midway, edge_label, sloped] {2};
    \draw (B) -- (D) node[midway, edge_label] {5};
    \draw (C) -- (D) node[midway, edge_label] {3};

    % MST Highlighted
    \begin{scope}[xshift=4cm]
        \node[vertex] (A_mst) at (0,2) {A};
        \node[vertex] (B_mst) at (2,2) {B};
        \node[vertex] (C_mst) at (0,0) {C};
        \node[vertex] (D_mst) at (2,0) {D};

        \draw[very thick, red] (A_mst) -- (B_mst) node[midway, edge_label] {1};
        \draw[very thick, red] (B_mst) -- (C_mst) node[midway, edge_label, sloped] {2};
        \draw[very thick, red] (C_mst) -- (D_mst) node[midway, edge_label] {3};
        
        \draw[dashed, gray] (A_mst) -- (C_mst);
        \draw[dashed, gray] (B_mst) -- (D_mst);
        \node at (1, -1) {MST (Cost: 1+2+3=6)};
    \end{scope}
\end{tikzpicture}
```
In the example, edges (A,B), (B,C), (C,D) form an MST with total weight 6.

## Properties of MSTs

1.  **Cut Property (or Cut-Crossing Property):**
    - For any cut (a partition of the graph's vertices into two disjoint sets), if the weight of an edge $(u,v)$ crossing the cut (i.e., $u$ in one set, $v$ in the other) is strictly smaller than the weights of all other edges crossing the cut, then this edge belongs to all MSTs of the graph.
    - This property is fundamental to the correctness of both [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/01 - Kruskal Algorithm|Kruskal's]] and [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/02 - Prim Algorithm|Prim's]] algorithms.

2.  **Cycle Property:**
    - For any cycle in the graph, if the weight of an edge in the cycle is strictly greater than the weights of all other edges in the cycle, then this edge cannot belong to any MST.
    - Removing the heaviest edge from a cycle does not disconnect the graph and results in a spanning tree with smaller or equal weight.

## Algorithms for Finding MST
Two main greedy algorithms are used:
-   [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/01 - Kruskal Algorithm|Kruskal's Algorithm]]: Sorts all edges by weight and adds them to the MST if they don't form a cycle with already added edges (checked using Union-Find).
-   [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/02 - Prim Algorithm|Prim's Algorithm]]: Starts from an arbitrary vertex and greedily grows the MST by adding the cheapest edge connecting a vertex in the current MST to a vertex outside it (similar to Dijkstra's).

## Applications
- Network design (e.g., laying cables for internet, power grids with minimum cost).
- Cluster analysis.
- Approximating solutions for NP-hard problems like the Traveling Salesperson Problem.
- Image segmentation.

## 总结 (Summary)
- An MST is a spanning tree of a connected, undirected, weighted graph with the minimum possible total edge weight.
- It connects all vertices with $V-1$ edges and contains no cycles.
- Key properties (Cut Property, Cycle Property) underpin greedy algorithms like Kruskal's and Prim's for finding MSTs.
- MSTs have wide applications in network optimization and other areas.

---
Parent: [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/index|MST Algorithms Index]]
Next: [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/01 - Kruskal Algorithm|Kruskal's Algorithm]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/00 - MST - Introduction.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/00 - MST - Introduction.md")

def create_kruskal_algorithm(path):
    content = r"""---
tags: [concept/algorithms, concept/graph, topic/mst, algorithm/kruskal, pattern/greedy, concept/union_find, course/labuladong]
aliases: [Kruskal's Algorithm, Kruskal MST]
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/Kruskal 最小生成树算法.md|Kruskal 最小生成树算法 by Labuladong]].

# Kruskal's Algorithm for Minimum Spanning Tree (MST)

Kruskal's algorithm is a greedy algorithm used to find a [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/00 - MST - Introduction|Minimum Spanning Tree (MST)]] for a connected, undirected, weighted graph. The core idea is to iteratively add edges with the smallest weights that do not form a cycle with the edges already chosen.

## 核心思想 (Core Idea)
Kruskal's algorithm builds the MST by selecting edges in increasing order of their weights.
1.  **Sort Edges:** Sort all edges in the graph by their weights in non-decreasing order.
2.  **Iterate and Add:** Iterate through the sorted edges. For each edge $(u, v)$ with weight $w$:
    - If adding edge $(u,v)$ does not form a cycle with the edges already selected for the MST, add $(u,v)$ to the MST.
    - If adding $(u,v)$ forms a cycle, discard it.
3.  **Termination:** Stop when $V-1$ edges have been added to the MST (where $V$ is the number of vertices), or when all edges have been considered.

The cycle detection is efficiently handled using a [[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/00 - Union-Find - Principles and Motivation|Union-Find (Disjoint Set Union)]] data structure.

## 算法步骤 (Algorithm Steps)

1.  Create a list of all edges from the graph: `(weight, u, v)`.
2.  Sort this list of edges by weight in ascending order.
3.  Initialize a Union-Find data structure with $V$ elements, each in its own set.
4.  Initialize an empty list `mst_edges` to store the edges of the MST, and `mst_weight = 0`.
5.  Iterate through the sorted edges `(w, u, v)`:
    a.  Use the `find` operation of Union-Find to check if `u` and `v` are already in the same connected component (i.e., `find(u) == find(v)`).
    b.  If `find(u) != find(v)` (they are in different components), adding this edge will not form a cycle:
        i.  Add the edge `(u,v)` to `mst_edges`.
        ii. Add its weight `w` to `mst_weight`.
        iii. Perform `union(u, v)` in the Union-Find structure to merge their components.
        iv. Increment the count of edges added to the MST. If this count reaches $V-1$, the MST is complete, and the algorithm can terminate.
6.  Return `mst_edges` and `mst_weight`.

Labuladong's article uses images like `![](/algo/images/kruskal/1.png)` to show graph and MSTs. A key step is depicted in `![](/algo/images/kruskal/2.png)` (sorting edges) and `![](/algo/images/kruskal/3.gif)` (iteratively adding edges and checking for cycles with Union-Find).

**Example Visualized (Conceptual):**
Edges sorted: `(e1, w1), (e2, w2), ..., (em, wm)` where `w1 <= w2 <= ...`
UF starts with all nodes separate.
- Try `e1=(u,v)`: If `find(u)!=find(v)`, add `e1` to MST, `union(u,v)`.
- Try `e2=(x,y)`: If `find(x)!=find(y)`, add `e2` to MST, `union(x,y)`.
- ...

```tikz
\begin{tikzpicture}[
    vertex/.style={circle, draw, fill=blue!10, minimum size=6mm, font=\sffamily\small},
    edge_candidate/.style={line width=0.5pt, gray, dashed},
    edge_mst/.style={line width=1.5pt, red},
    edge_discarded/.style={line width=1pt, orange, dotted, text=orange},
    edge_label/.style={font=\tiny, fill=white, inner sep=1pt}
]
    \node[vertex] (A) at (0,2) {A}; \node[vertex] (B) at (2,2) {B};
    \node[vertex] (C) at (0,0) {C}; \node[vertex] (D) at (2,0) {D};
    \node[vertex] (E) at (4,1) {E};

    % Edges with weights: (A,B,1), (C,D,2), (A,C,3), (B,D,4), (B,E,5), (D,E,6)
    \draw[edge_mst] (A) -- (B) node[midway, edge_label] {1}; % Add (A,B)
    \draw[edge_mst] (C) -- (D) node[midway, edge_label] {2}; % Add (C,D)
    \draw[edge_mst] (A) -- (C) node[midway, edge_label] {3}; % Add (A,C) - connects {A,B} and {C,D}
    \draw[edge_discarded] (B) -- (D) node[midway, edge_label, sloped] {4 (Cycle!)}; % Discard (B,D) - forms cycle A-B-D-C-A
    \draw[edge_mst] (B) -- (E) node[midway, edge_label, sloped] {5}; % Add (B,E) - V-1 edges reached (4 for 5 nodes)
    \draw[edge_candidate] (D) -- (E) node[midway, edge_label, sloped] {6 (Not needed)};

    \node at (2,-1.5) [text width=6cm, align=center, draw, fill=yellow!10, rounded corners]
        {Sorted edges processed. UF checks for cycles. Red edges form MST. Total weight = 1+2+3+5=11.};
\end{tikzpicture}
```

## Python Implementation
```python
class UF: # Union-Find data structure from previous notes
    def __init__(self, n: int):
        self.parent = list(range(n))
        self.sz = [1] * n
        self.num_components = n
    def find(self, x: int) -> int:
        if self.parent[x] != x:
            self.parent[x] = self.find(self.parent[x])
        return self.parent[x]
    def union(self, x: int, y: int) -> bool:
        rootX = self.find(x)
        rootY = self.find(y)
        if rootX == rootY: return False
        if self.sz[rootX] < self.sz[rootY]:
            self.parent[rootX] = rootY
            self.sz[rootY] += self.sz[rootX]
        else:
            self.parent[rootY] = rootX
            self.sz[rootX] += self.sz[rootY]
        self.num_components -= 1
        return True
    def connected(self, x: int, y: int) -> bool:
        return self.find(x) == self.find(y)

class Kruskal:
    def minSpanningTree(self, num_vertices: int, edges: list[list[int]]) -> tuple[list[tuple[int,int,int]], int]:
        # edges is a list of [u, v, weight]
        
        # Sort all edges by weight
        sorted_edges = sorted(edges, key=lambda x: x[2])
        
        uf = UF(num_vertices) # Assuming vertices are 0 to num_vertices-1
        mst_edges = []
        mst_weight = 0
        edges_count = 0
        
        for u, v, weight in sorted_edges:
            if uf.find(u) != uf.find(v): # If u and v are not already connected
                uf.union(u, v)
                mst_edges.append((u, v, weight))
                mst_weight += weight
                edges_count += 1
                if edges_count == num_vertices - 1: # MST is complete
                    break
        
        # Check if MST is formed (all nodes connected)
        # This can be done by checking uf.count() == 1 if graph was initially connected.
        # Or if edges_count == num_vertices - 1 (for connected input graph)
        if edges_count != num_vertices - 1 and num_vertices > 0: # Check for num_vertices > 0 for empty graph
             # If the graph was not connected to begin with, 
             # we'd form a minimum spanning forest.
             # The problem usually implies a connected graph for MST.
             # If it could be disconnected, we'd return the forest and its total weight.
             # For LeetCode problems, this often implies failure to connect all.
             pass # print("Graph may not be fully connected, or not enough edges.")


        return mst_edges, mst_weight

# Example for LC1584 - Min Cost to Connect All Points
# Here points are (x,y) coordinates, edges are Manhattan distances.
# Vertices 0 to n-1.
# Solution for LC1584:
# class Solution:
#     def minCostConnectPoints(self, points: list[list[int]]) -> int:
#         n = len(points)
#         if n <= 1:
#             return 0
#         
#         edges = []
#         for i in range(n):
#             for j in range(i + 1, n):
#                 dist = abs(points[i][0] - points[j][0]) + abs(points[i][1] - points[j][1])
#                 edges.append((i, j, dist))
#         
#         kruskal_solver = Kruskal()
#         _, mst_weight = kruskal_solver.minSpanningTree(n, edges)
#         return mst_weight
```

## Complexity Analysis
-   Let $V$ be the number of vertices and $E$ be the number of edges.
-   **Sorting Edges:** $O(E \log E)$.
-   **Union-Find Operations:** In the loop, there are $E$ iterations. Each iteration involves two `find` operations and at most one `union` operation. With path compression and union by size/rank, these operations are nearly constant time, amortized $O(\alpha(V))$ where $\alpha$ is the inverse Ackermann function.
    - Total for Union-Find part: $O(E \cdot \alpha(V))$.
-   **Overall Time Complexity:** Dominated by sorting, so $O(E \log E)$. Since $E$ can be up to $O(V^2)$, this can also be written as $O(E \log V)$ because $\log E = O(\log(V^2)) = O(2 \log V) = O(\log V)$.
-   **Space Complexity:** $O(E)$ to store the edges for sorting, and $O(V)$ for the Union-Find data structure. So, $O(V+E)$.

## 总结 (Summary)
- Kruskal's algorithm finds an MST by greedily adding the smallest weight edges that do not form a cycle.
- It relies on sorting all edges by weight and using a Union-Find data structure for efficient cycle detection.
- Time complexity is typically $O(E \log E)$ or $O(E \log V)$.
- Space complexity is $O(V+E)$.
- It's conceptually simple, especially if Union-Find is already understood.

---
Parent: [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/index|MST Algorithms Index]]
Previous: [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/00 - MST - Introduction|MST - Introduction]]
Next: [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/02 - Prim Algorithm|Prim's Algorithm]]
Related: [[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/00 - Union-Find - Principles and Motivation|Union-Find]], [[Interview/Concept/Algorithms/Greedy Algorithms/00 - Greedy Algorithm - Core Framework|Greedy Algorithms]]
Problems: [[Interview/Practice/LeetCode/LC1135 - Connecting Cities With Minimum Cost|LC1135]], [[Interview/Practice/LeetCode/LC1584 - Min Cost to Connect All Points|LC1584]], [[Interview/Practice/LeetCode/LC261 - Graph Valid Tree|LC261]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/01 - Kruskal Algorithm.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/01 - Kruskal Algorithm.md")

def create_prim_algorithm(path):
    content = r"""---
tags: [concept/algorithms, concept/graph, topic/mst, algorithm/prim, pattern/greedy, concept/priority_queue, course/labuladong]
aliases: [Prim's Algorithm, Prim MST]
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/Prim 最小生成树算法.md|Prim 最小生成树算法 by Labuladong]].
> Labuladong highlights the similarity between Prim's algorithm and Dijkstra's algorithm.

# Prim's Algorithm for Minimum Spanning Tree (MST)

Prim's algorithm is another greedy algorithm for finding a [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/00 - MST - Introduction|Minimum Spanning Tree (MST)]] in a connected, undirected, weighted graph. It works by growing a single tree, one edge at a time, until it spans all vertices.

## 核心思想 (Core Idea)
Prim's algorithm starts with an arbitrary vertex and greedily adds the cheapest edge connecting a vertex in the partially built MST to a vertex not yet in the MST. This process is repeated until all vertices are included.

**Analogy to Dijkstra:**
Labuladong points out that Prim's algorithm is very similar to [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01 - Dijkstra Algorithm|Dijkstra's algorithm]].
- **Dijkstra:** Finds shortest paths from a source. The state `distTo[v]` stores the shortest known distance from source to `v`. The priority queue stores `(dist_to_v, v)`.
- **Prim:** Builds an MST. A similar "distance" array (let's call it `min_edge_to_join[v]`) stores the minimum weight of an edge connecting `v` (a vertex not yet in MST) to the current MST. The priority queue stores `(min_edge_weight_to_v, v)`.

The key difference is in the update logic:
- **Dijkstra update:** `distTo[w] = distTo[v] + edge(v,w).weight` (sum of path).
- **Prim update:** `min_edge_to_join[w] = edge(v,w).weight` (just the edge weight itself, if it's cheaper than current way to connect `w` to MST).

## 算法步骤 (Algorithm Steps)

1.  Initialize `mst_weight = 0` and an empty list `mst_edges`.
2.  Maintain a boolean array `in_mst[V]` to track vertices already included in the MST, initially all `false`.
3.  Maintain an array `min_edge_to_join[V]` (or similar) where `min_edge_to_join[v]` stores the minimum weight of an edge connecting vertex `v` to the current MST. Initialize with infinity for all vertices, except for a start vertex (e.g., vertex 0) where `min_edge_to_join[0] = 0` (or handled by pushing it to PQ initially).
4.  Use a Min-Priority Queue (`pq`) storing pairs `(weight, vertex_id)`, ordered by `weight`. Initially, add `(0, start_vertex)` to `pq`.
5.  While `pq` is not empty and the MST does not yet include all $V$ vertices (or $V-1$ edges):
    a.  Extract `(w, u)` with the minimum weight `w` from `pq`.
    b.  If `u` is already in `in_mst`, continue (this edge is redundant or forms a cycle with edges considered to connect `u` to the MST previously via a more expensive path).
    c.  Add `u` to the MST: `in_mst[u] = true`. Add `w` to `mst_weight`. If tracking edges, add the edge that connected `u` with weight `w`.
    d.  For each neighbor `v` of `u`:
        i.  If `v` is not in `in_mst` AND the edge `(u,v)` with weight `edge_weight(u,v)` is cheaper than the current `min_edge_to_join[v]`:
            - Update `min_edge_to_join[v] = edge_weight(u,v)`.
            - Add `(edge_weight(u,v), v)` to `pq`. (If `v` was already in `pq` with a higher weight, this new entry will be prioritized, or use PQ decrease-key if available).

This process is very similar to Dijkstra's, but instead of accumulating path sums, we are interested in the weight of the single edge that connects a new vertex to the growing MST.

Labuladong's article shows the highlighted code differences between Prim and Dijkstra:
- `State` object in Prim would store `(vertex_id, edge_weight_to_join_mst)`.
- Update logic in `for neighbor v of u`: if `v` not in MST and `edge(u,v).weight < distTo[v]` (where `distTo[v]` now means min edge to connect `v` to MST), then update `distTo[v]` and add/update `v` in PQ.

## Python Implementation
(Adapting Dijkstra's structure, using a Min-Heap for the priority queue)
```python
import heapq

class Prim:
    def minSpanningTree(self, num_vertices: int, edges_input: list[list[int]]) -> tuple[list[tuple[int,int,int]], int]:
        # Build adjacency list: graph[u] = list of (v, weight)
        adj = [[] for _ in range(num_vertices)]
        for u, v, weight in edges_input:
            adj[u].append((v, weight))
            adj[v].append((u, weight)) # Undirected graph

        # min_edge_to_join[v] = minimum edge weight connecting v to current MST
        # This is analogous to distTo in Dijkstra.
        # We also need to store which edge gives this min weight to reconstruct MST.
        # For simplicity in weight calculation, we can just use priority queue.

        in_mst = [False] * num_vertices
        mst_weight = 0
        edges_count = 0
        mst_edges = [] # To store selected edges for reconstruction

        # (weight, to_vertex, from_vertex_for_this_edge)
        # Start with vertex 0. No edge connects it initially to "previous MST", so weight is effectively 0.
        # More robust: select an arbitrary start, e.g. 0
        # Push edges from start node if it's easier, or use a distTo-like array.
        
        # Let's use a min_edge_to_join array and PQ more like standard Prim's.
        # Initialize min_edge_to_join[i] to infinity.
        # min_edge_to_join[start_node] = 0.
        # pq.push((0, start_node)).
        
        # Simplified Prim that's closer to Dijkstra structure from Labuladong:
        # PQ stores (cost_to_add_node, node_to_add)
        # Start by adding node 0 to MST (implicitly)
        
        pq = [] # Min-heap: (weight, u, v) where (u,v) is edge, u in MST, v not
        
        # Start with vertex 0 (or any vertex)
        start_node = 0
        in_mst[start_node] = True
        
        # Add all edges from start_node to PQ
        for neighbor, weight in adj[start_node]:
            if not in_mst[neighbor]:
                heapq.heappush(pq, (weight, start_node, neighbor))
        
        while pq and edges_count < num_vertices - 1:
            weight, u_in_mst, v_not_in_mst = heapq.heappop(pq)
            
            if in_mst[v_not_in_mst]:
                continue # v_not_in_mst is already in MST, skip this edge
            
            # Add v_not_in_mst to MST via edge (u_in_mst, v_not_in_mst)
            in_mst[v_not_in_mst] = True
            mst_weight += weight
            mst_edges.append((u_in_mst, v_not_in_mst, weight))
            edges_count += 1
            
            if edges_count == num_vertices - 1:
                break
                
            # Add new edges from newly added v_not_in_mst to PQ
            for next_neighbor, next_weight in adj[v_not_in_mst]:
                if not in_mst[next_neighbor]:
                    heapq.heappush(pq, (next_weight, v_not_in_mst, next_neighbor))
            
        if edges_count != num_vertices - 1 and num_vertices > 1: # Check for connected graph
            return [], -1 # Or handle as per problem (e.g. for MSF)
            
        return mst_edges, mst_weight

# Example for LC1584 - Min Cost to Connect All Points
# (Similar to Kruskal, just call Prim's solver)
# class Solution:
#     def minCostConnectPoints(self, points: list[list[int]]) -> int:
#         n = len(points)
#         if n <= 1: return 0
#         
#         adj = [[] for _ in range(n)]
#         # Build a complete graph where edge weights are Manhattan distances
#         for i in range(n):
#             for j in range(i + 1, n):
#                 dist = abs(points[i][0] - points[j][0]) + abs(points[i][1] - points[j][1])
#                 adj[i].append((j, dist))
#                 adj[j].append((i, dist))
#
#         # Prim's using the min_edge_to_join array method
#         min_edge_cost = [float('inf')] * n
#         in_mst = [False] * n
#         min_edge_cost[0] = 0 # Cost to add node 0 to MST is 0
#
#         pq = [(0, 0)] # (cost_to_add_node, node_id)
#         mst_total_weight = 0
#         nodes_in_mst = 0
#
#         while pq and nodes_in_mst < n:
#             cost, u = heapq.heappop(pq)
#
#             if in_mst[u]:
#                 continue
#
#             in_mst[u] = True
#             mst_total_weight += cost
#             nodes_in_mst += 1
#
#             for v, weight_uv in adj[u]:
#                 if not in_mst[v] and weight_uv < min_edge_cost[v]:
#                     min_edge_cost[v] = weight_uv
#                     heapq.heappush(pq, (weight_uv, v))
#        
#         return mst_total_weight if nodes_in_mst == n else -1 # Check connectivity
```

The second Python solution template within the `Solution` class for LC1584 is a more standard Prim's implementation often seen, which closely mirrors Dijkstra's structure using a `distTo`-like array (`min_edge_cost`) and a priority queue of nodes to visit.

## Complexity Analysis
- Let $V$ be the number of vertices and $E$ be the number of edges.
- Using a binary heap for the priority queue:
    - Each vertex is added to MST once. When extracted from PQ, its edges are processed.
    - Total $V$ `extract-min` operations from PQ: $V \cdot O(\log V)$.
    - Each edge $(u,v)$ can lead to an update of `min_edge_to_join[v]` and an update (or add) to PQ. At most $E$ such updates. Each PQ update: $O(\log V)$. Total $E \cdot O(\log V)$.
    - **Overall Time Complexity:** $O(E \log V)$ or $O((V+E)\log V)$ if $E$ is very small. If using a Fibonacci heap, it can be $O(E + V \log V)$.
- **Space Complexity:** $O(V+E)$ for adjacency list, $O(V)$ for `in_mst`, `min_edge_to_join`, and priority queue. So, $O(V+E)$.

## 总结 (Summary)
- Prim's algorithm finds an MST by greedily growing a tree from a starting vertex.
- It's similar in structure to Dijkstra's algorithm, using a priority queue to select the next cheapest edge to add a new vertex to the MST.
- The key difference in update logic: Prim considers individual edge weights to connect to MST, while Dijkstra sums path weights.
- Time complexity is typically $O(E \log V)$ using a binary heap.
- Like Kruskal's, it's a greedy algorithm for MST.

---
Parent: [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/index|MST Algorithms Index]]
Previous: [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/01 - Kruskal Algorithm|Kruskal's Algorithm]]
Related: [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01 - Dijkstra Algorithm|Dijkstra's Algorithm]], [[Interview/Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization|Priority Queues]]
Problems: [[Interview/Practice/LeetCode/LC1135 - Connecting Cities With Minimum Cost|LC1135]], [[Interview/Practice/LeetCode/LC1584 - Min Cost to Connect All Points|LC1584]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/02 - Prim Algorithm.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/02 - Prim Algorithm.md")

def create_dijkstra_algorithm(path):
    content = r"""---
tags: [concept/algorithms, concept/graph_traversal, concept/shortest_path, algorithm/dijkstra, pattern/greedy, concept/priority_queue, course/labuladong]
aliases: [Dijkstra's Algorithm, Dijkstra Shortest Path]
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/Dijkstra 算法核心原理及实现.md|Dijkstra 算法核心原理及实现 by Labuladong]].
> Dijkstra's is described as BFS + Greedy, for single-source shortest path in graphs with non-negative edge weights.

# Dijkstra's Algorithm: Core Principles and Implementation

Dijkstra's algorithm is a classic algorithm for finding the shortest paths from a single source vertex to all other vertices in a weighted graph where edge weights are **non-negative**. It's a greedy algorithm that incrementally builds a set of vertices for which the shortest path is known.

## 核心思想 (Core Idea)
Labuladong summarizes Dijkstra as: **Standard BFS algorithm + Greedy Thinking**.
1.  **BFS Structure:** Similar to Breadth-First Search, it explores the graph.
2.  **Greedy Choice:** Instead of a standard FIFO queue, Dijkstra's uses a **Min-Priority Queue**. The priority queue stores nodes to visit, prioritized by their current shortest known distance from the source node. The algorithm always picks the node from the priority queue that is "closest" to the source.
3.  **`distTo` Array:** An array `distTo[v]` maintains the shortest known distance from the source `s` to vertex `v`. Initially, `distTo[s] = 0` and `distTo[v] = infinity` for all other `v`.
4.  **Relaxation:** When exploring from a node `u` to a neighbor `v` through edge `(u,v)`:
    - If a shorter path to `v` is found via `u` (i.e., `distTo[u] + weight(u,v) < distTo[v]`), then update `distTo[v]` and add/update `v` in the priority queue with its new, shorter distance.

> [!WARNING] Negative Edge Weights
> Dijkstra's algorithm does **not** work correctly if the graph contains negative edge weights. The greedy choice of always picking the "closest" unvisited node can be invalidated if a later path involving a negative edge leads to an even shorter path to an already "settled" node. For graphs with negative edges (but no negative cycles), [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/02 - Bellman-Ford Algorithm|Bellman-Ford Algorithm]] should be used.

## 算法步骤 (Algorithm Steps)

1.  Initialize `distTo[V]` array: `distTo[start_node] = 0`, all other `distTo[v] = infinity`.
2.  Initialize a Min-Priority Queue (`pq`). Add `(0, start_node)` to `pq` (pair: `(distance_from_start, vertex_id)`).
3.  While `pq` is not empty:
    a.  Extract `(current_dist, u)` with the minimum `current_dist` from `pq`.
    b.  If `current_dist > distTo[u]`, continue (this means we found a shorter path to `u` earlier and this PQ entry is stale).
    c.  For each neighbor `v` of `u` with edge weight `w_uv`:
        i.  If `distTo[u] + w_uv < distTo[v]`:
            -   This is a shorter path to `v` through `u`.
            -   Update `distTo[v] = distTo[u] + w_uv`.
            -   Add `(distTo[v], v)` to `pq`. (If `v` is already in `pq` with a larger distance, this new entry will be prioritized, or use PQ's decrease-key operation if available).
4.  After the loop, `distTo[v]` contains the shortest path distance from `start_node` to `v`.

## Python Implementation (Labuladong's Style)
Labuladong presents Dijkstra using a `State` class, similar to his BFS structure, to store `(node_id, dist_from_start)`.

```python
import heapq

class State:
    def __init__(self, node_id: int, dist_from_start: float):
        self.node_id = node_id
        self.dist_from_start = dist_from_start

    # For min-priority queue (heapq implements min-heap)
    def __lt__(self, other: 'State'):
        return self.dist_from_start < other.dist_from_start

class Dijkstra:
    def shortestPath(self, start_node: int, num_vertices: int, adj: list[list[tuple[int, int]]]) -> list[float]:
        # adj[u] = list of (v, weight) for edges from u
        
        distTo = [float('inf')] * num_vertices
        distTo[start_node] = 0
        
        pq = [State(start_node, 0)] # Min-heap of State objects
        
        while pq:
            current_state = heapq.heappop(pq)
            current_node_id = current_state.node_id
            current_dist = current_state.dist_from_start
            
            # If we found a shorter path already, skip stale entry
            if current_dist > distTo[current_node_id]:
                continue
            
            # Explore neighbors
            for neighbor_node_id, weight_edge in adj[current_node_id]:
                dist_to_neighbor_via_current = current_dist + weight_edge
                if dist_to_neighbor_via_current < distTo[neighbor_node_id]:
                    # Found a shorter path to neighbor_node_id
                    distTo[neighbor_node_id] = dist_to_neighbor_via_current
                    heapq.heappush(pq, State(neighbor_node_id, distTo[neighbor_node_id]))
                    
        return distTo

# Example Usage:
# Assume a graph:
# 0 --1-- 1
# | \     |
# 4  2    3
# |   \   |
# 2 --5-- 3
#
# num_vertices = 4
# adj = [
#     [(1, 1), (2, 4), (3,2)],  # Neighbors of 0: (1,1), (2,4), (3,2)
#     [(0, 1), (2, 3), (3,5)],  # Neighbors of 1
#     [(0, 4), (1, 3), (3,5)],  # Neighbors of 2
#     [(0, 2), (1, 5), (2,5)]   # Neighbors of 3
# ]
# dijkstra_solver = Dijkstra()
# distances_from_0 = dijkstra_solver.shortestPath(0, num_vertices, adj)
# print(distances_from_0) # Expected: [0, 1, 2, 2] (Path to 3 is 0->3 (cost 2), or 0->1->2 cost 1+3=4. Path 0->3 is shorter.
# For the provided graph (assuming it's my own interpretation from the text):
# 0: (1,1), (3,2) -> path 0-1 cost 1, path 0-3 cost 2
# 1: (0,1), (2,3) -> from 0, path 0-1-2 cost 1+3=4
# 2: (1,3)
# 3: (0,2)
# So distTo: [0, 1 (0->1), 4 (0->1->2), 2 (0->3)]

# If Labuladong's example graph from `div_dijkstra` is more complex, this would change.
# The general structure remains.
```
Labuladong's visualization panel `div_dijkstra` shows the algorithm in action. Key moments include:
-   Initialization of `distTo` and PQ.
-   Extraction of min-distance node from PQ.
-   Relaxation of edges to its neighbors, potentially updating `distTo` and PQ.

## Optimality Proof Sketch (Why Dijkstra Works)
Dijkstra's algorithm works due to its greedy nature and the assumption of non-negative edge weights.
When a node `u` is extracted from the priority queue, `distTo[u]` is the shortest path distance from `s` to `u`.
**Proof by contradiction:** Assume `u` is extracted, but there exists a shorter path $P'$ from `s` to `u`.
This path $P'$ must pass through some node `x` currently in the MST (black set in CLRS terms) and then an edge `(x,y)` where `y` is on the frontier (gray set, in PQ) or is `u` itself.
`s --...--> x --edge--> y --...--> u`
Since `distTo[x]` is already shortest (by induction), and `weight(x,y) >= 0`, `distTo[y]` (which is `distTo[x] + weight(x,y)`) would have been $\le$ cost of $P'$ up to $y$.
If `y` is on the path $P'$ before `u`, then `distTo[y]` must be $\le distTo[u]$ (because edges are non-negative). Thus, `y` would have been extracted from PQ before `u`. This leads to a contradiction, implying `distTo[u]` was indeed minimal when `u` was extracted.

## Complexity Analysis
-   Let $V$ be the number of vertices and $E$ be the number of edges.
-   **Using Binary Heap for Priority Queue:**
    -   Each vertex is added to PQ at most once when its `distTo` is first finalized (or relaxed).
    -   $V$ `extract-min` operations: $V \cdot O(\log V)$.
    -   At most $E$ `decrease-key` (or `add` if not supporting decrease-key efficiently) operations: $E \cdot O(\log V)$.
    -   **Total Time Complexity:** $O(E \log V)$ or $O((V+E)\log V)$. If $E \approx V^2$ (dense graph), this is $O(V^2 \log V)$.
-   **Using Fibonacci Heap for Priority Queue:**
    -   `extract-min`: Amortized $O(\log V)$.
    -   `decrease-key`: Amortized $O(1)$.
    -   **Total Time Complexity:** $O(E + V \log V)$. Better for dense graphs.
-   **Space Complexity:** $O(V+E)$ for adjacency list, $O(V)$ for `distTo` array and priority queue. So, $O(V+E)$.

## Point-to-Point Shortest Path Optimization
If we only need the shortest path from `start_node` to a specific `target_node`, we can terminate Dijkstra's algorithm as soon as `target_node` is extracted from the priority queue. At this point, `distTo[target_node]` will hold the shortest path distance.

## 总结 (Summary)
- Dijkstra's algorithm finds the single-source shortest paths in a weighted graph with non-negative edge weights.
- It's a greedy algorithm using a min-priority queue to always explore the "closest" unvisited node.
- Key operations: Initialization of `distTo`, PQ management, edge relaxation.
- Time complexity is typically $O(E \log V)$ with a binary heap.
- Cannot handle negative edge weights.

---
Parent: [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/index|Shortest Path Algorithms Index]]
Next: [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01a - Dijkstra Algorithm - Variants and Constraints|Dijkstra Algorithm - Variants and Constraints]]
Related: [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Graph BFS]], [[Interview/Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization|Priority Queues]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal/Shortest Path"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01 - Dijkstra Algorithm.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created/Updated: Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01 - Dijkstra Algorithm.md")

def create_dijkstra_variants(path):
    content = r"""---
tags: [concept/algorithms, concept/graph_traversal, concept/shortest_path, algorithm/dijkstra, pattern/constraints, course/labuladong]
aliases: [Constrained Dijkstra, Dijkstra with K-Edge Limit, K-Stop Dijkstra]
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/Dijkstra 拓展：带限制的最短路问题.md|Dijkstra 拓展：带限制的最短路问题 by Labuladong]].

# Dijkstra's Algorithm: Variants and Constraints

The standard [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01 - Dijkstra Algorithm|Dijkstra's algorithm]] finds the shortest path in terms of total weight. However, many real-world problems impose additional constraints, such as limiting the number of edges (stops) in a path, or other resource limits. Labuladong shows how to adapt Dijkstra's by modifying the `State` object and the relaxation logic.

## Core Idea: Augmenting State
To handle additional constraints, the `State` object stored in the priority queue needs to be augmented to include information about these constraints. The `distTo` array (or equivalent structure for memoization) might also need to become multi-dimensional to store optimal values for different constraint states.

## Example: Shortest Path with At Most `k` Edges

**Problem:** Find the shortest path from `start_node` to `target_node` using at most `k` edges.

**Modified State:**
The `State` object in the priority queue will now be `(node_id, dist_from_start, edges_used)`.
The priority queue will still prioritize by `dist_from_start`.

**Modified `distTo` (or equivalent memoization):**
Instead of `distTo[node]` storing the min distance to `node`, we need `distTo[node][edges_count]` to store the min distance to `node` using exactly `edges_count` edges.
Initialize `distTo[i][j] = infinity`.
`distTo[start_node][0] = 0`.

**Modified Dijkstra Algorithm:**
1.  Initialize `distTo[num_vertices][k+1]` with infinity. `distTo[start_node][0] = 0`.
2.  Priority Queue `pq` stores `State(node_id, dist_from_start, edges_used)`. Add `State(start_node, 0, 0)` to `pq`.
3.  While `pq` is not empty:
    a.  Extract `current_state = (u, current_dist, edges_so_far)` with min `current_dist`.
    b.  If `current_dist > distTo[u][edges_so_far]`, continue (stale entry).
    c.  If `u == target_node`, we might have found *a* path. If we only care about shortest path *regardless* of edge count up to k, this isn't necessarily the final answer yet unless a global min is tracked. However, if we want the shortest path *using exactly* `edges_so_far` edges, this is it for this `edges_so_far` count. (The problem usually implies any number of edges up to `k`).
    d.  If `edges_so_far == k`, we cannot take any more edges from node `u`. Continue.
    e.  For each neighbor `v` of `u` with edge `(u,v)` of weight `w_uv`:
        i.  `new_dist_to_v = current_dist + w_uv`.
        ii. `new_edges_used = edges_so_far + 1`.
        iii. If `new_dist_to_v < distTo[v][new_edges_used]`:
            -   Update `distTo[v][new_edges_used] = new_dist_to_v`.
            -   Push `State(v, new_dist_to_v, new_edges_used)` to `pq`.
4.  After the loop, the answer is `min(distTo[target_node][j] for j in 0..k)`.

### Python Implementation (Conceptual)
```python
import heapq

class StateKEdges:
    def __init__(self, node_id: int, dist_from_start: float, edges_used: int):
        self.node_id = node_id
        self.dist_from_start = dist_from_start
        self.edges_used = edges_used

    def __lt__(self, other: 'StateKEdges'):
        return self.dist_from_start < other.dist_from_start

class DijkstraWithKEdges:
    def shortestPathWithKEdges(self, start_node: int, target_node: int, k_limit: int, 
                               num_vertices: int, adj: list[list[tuple[int, int]]]) -> float:
        # distTo[node_id][edges] = min_dist
        distTo = [[float('inf')] * (k_limit + 1) for _ in range(num_vertices)]
        
        distTo[start_node][0] = 0
        pq = [StateKEdges(start_node, 0, 0)]
        
        min_dist_to_target = float('inf')

        while pq:
            current_state = heapq.heappop(pq)
            u, current_dist, edges_so_far = current_state.node_id, current_state.dist_from_start, current_state.edges_used
            
            if current_dist > distTo[u][edges_so_far]:
                continue

            # If this node is the target, update overall minimum if this path is better
            # Note: Dijkstra ensures that when we pop the *target node itself* with some edge_count,
            # that's the shortest path to target *with that specific edge_count*.
            # The final answer will be min over all distTo[target_node][0...k_limit].
            # This check here is an optimization if we only care about reaching target.
            # if u == target_node:
            #    min_dist_to_target = min(min_dist_to_target, current_dist)
            #    # We might continue if a path with fewer edges but higher cost was found,
            #    # and a path with more edges (still <= k) could be shorter.

            if edges_so_far == k_limit: # Cannot take more edges
                continue
                
            for v, weight_uv in adj[u]:
                new_dist_to_v = current_dist + weight_uv
                new_edges_used = edges_so_far + 1 # This is the (edges_so_far+1)-th edge
                
                if new_dist_to_v < distTo[v][new_edges_used]:
                    distTo[v][new_edges_used] = new_dist_to_v
                    heapq.heappush(pq, StateKEdges(v, new_dist_to_v, new_edges_used))
        
        # The result is the minimum distance to target_node using at most k_limit edges
        final_answer = float('inf')
        for edges in range(k_limit + 1):
            final_answer = min(final_answer, distTo[target_node][edges])
            
        return final_answer if final_answer != float('inf') else -1 # Or appropriate "not found"
```

**Complexity Analysis (with K-edge limit):**
- The state space effectively increases by a factor of `k_limit`.
- Number of states in PQ can be up to $V \cdot K$.
- Time Complexity: $O(E \cdot K \cdot \log(V \cdot K))$ or $O(E \cdot K + V \cdot K \log (V \cdot K))$ with Fibonacci heap.
- Space Complexity: $O(V \cdot K)$ for `distTo` and PQ.

Labuladong's `div_dijkstra-k-limit` visualization would show this. The core idea is that nodes in the PQ are not just `(cost, node)` but `(cost, node, constraint_value)`.

## Generalizing for Other Constraints
This approach of augmenting the state can be generalized:
- **Limited Fuel:** `State(node, cost, fuel_remaining)`
- **Specific Edge Types:** `State(node, cost, last_edge_type_used)`
- **Time Windows:** `State(node, arrival_time)`

The `distTo` table becomes multi-dimensional, e.g., `distTo[node][fuel_remaining]`. The number of dimensions and their sizes determine the complexity.

## Key Considerations
1.  **State Definition:** Crucial to capture all relevant information for making decisions and distinguishing paths.
2.  **Priority Queue Ordering:** Still based on the primary optimization objective (e.g., minimum distance).
3.  **`distTo` / Memoization Table:** Must be indexed by all components of the augmented state.
4.  **Pruning/Termination:**
    - When extracting from PQ, if a better path to `(node, constraint_state)` was already found, skip.
    - If constraint limits are exceeded (e.g., `edges_used == k_limit`), don't expand further along that path for that constraint.

This technique transforms standard Dijkstra into a more powerful tool for solving a wider range of constrained shortest path problems, effectively performing a search in an expanded state space graph.

## 总结 (Summary)
- To solve shortest path problems with additional constraints using Dijkstra's, augment the `State` object stored in the priority queue to include constraint-related information.
- The `distTo` array (or memoization table) must also be expanded to store the best-known cost for each `(node, constraint_state)` pair.
- The relaxation step considers transitions in this augmented state space.
- Complexity increases based on the size of the constraint dimensions.

---
Parent: [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01 - Dijkstra Algorithm|Dijkstra Algorithm]]
Next: [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/02 - Bellman-Ford Algorithm|Bellman-Ford Algorithm]] (Placeholder)
Problems: [[Interview/Practice/LeetCode/LC787 - Cheapest Flights Within K Stops|LC787 - Cheapest Flights Within K Stops]] (A common example that can use this type of modified Dijkstra/Bellman-Ford)
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal/Shortest Path"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01a - Dijkstra Algorithm - Variants and Constraints.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01a - Dijkstra Algorithm - Variants and Constraints.md")

def create_graph_cycle_detection_directed(path):
    content = r"""---
tags: [concept/algorithms, concept/graph, topic/cycle_detection, algorithm/dfs, algorithm/bfs, course/labuladong]
aliases: [Directed Graph Cycle Detection, DAG Cycle Check]
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/环检测及拓扑排序算法.md|环检测及拓扑排序算法 by Labuladong]].

# Cycle Detection in Directed Graphs

Detecting cycles is a fundamental problem in graph theory, especially for directed graphs, as it's a prerequisite for algorithms like [[Interview/Concept/Algorithms/Graph Traversal/04 - Topological Sort|Topological Sort]]. A directed graph has a cycle if there is a path that starts and ends at the same vertex.

## 1. DFS-based Cycle Detection

The DFS-based approach uses the concept of tracking nodes currently in the recursion stack (the "path" being explored).

**Core Idea:**
- Maintain two boolean arrays:
    - `visited[]`: Marks nodes that have been visited at least once (to avoid redundant computations on already processed components).
    - `onPath[]`: Marks nodes currently in the active recursion stack for the current DFS traversal.
- During DFS traversal from a node `u`:
    - Mark `onPath[u] = true`.
    - For each neighbor `v` of `u`:
        - If `onPath[v]` is true, a cycle is detected (we've encountered a node already on the current path).
        - Else if `!visited[v]`, recursively call DFS on `v`.
    - After exploring all neighbors of `u` (post-order position), mark `onPath[u] = false` (backtracking, `u` is no longer on the active path).

**Python Implementation (Conceptual):**
```python
class DFSCycleDetection:
    def __init__(self, num_vertices):
        self.num_vertices = num_vertices
        self.visited = [False] * num_vertices
        self.onPath = [False] * num_vertices
        self.has_cycle = False

    def _build_graph(self, prerequisites): # Placeholder for graph building
        graph = [[] for _ in range(self.num_vertices)]
        for to_node, from_node in prerequisites: # Example: [course, prereq]
            graph[from_node].append(to_node)
        return graph

    def _dfs(self, graph, u):
        if self.has_cycle: # Optimization: if cycle already found, stop
            return

        self.visited[u] = True
        self.onPath[u] = True

        for v in graph[u]:
            if self.onPath[v]: # Found a back edge to a node on current path
                self.has_cycle = True
                return
            if not self.visited[v]:
                self._dfs(graph, v)
                if self.has_cycle: # Propagate cycle detection
                    return
        
        self.onPath[u] = False # Backtrack

    def canFinish(self, numCourses: int, prerequisites: list[list[int]]) -> bool: # LC207 example
        self.num_vertices = numCourses
        self.visited = [False] * numCourses
        self.onPath = [False] * numCourses
        self.has_cycle = False
        
        graph = self._build_graph(prerequisites)

        for i in range(numCourses):
            if not self.visited[i] and not self.has_cycle:
                self._dfs(graph, i)
        
        return not self.has_cycle
```
Labuladong's article shows `div_course-schedule` for visualization. The key is the state of `onPath` changing during recursion. Image `![](/algo/images/topological-sort/4.jpeg)` in the source illustrates `onPath` detecting a cycle.

## 2. BFS-based Cycle Detection (Kahn's Algorithm variation)

This approach is often tied to [[Interview/Concept/Algorithms/Graph Traversal/04 - Topological Sort|Topological Sort]]. If a topological sort can include all nodes, the graph is a DAG (Directed Acyclic Graph). If not, it contains a cycle.

**Core Idea (Kahn's Algorithm style):**
1.  **Calculate In-degrees:** For each node, count its in-degree (number of incoming edges).
2.  **Initialize Queue:** Add all nodes with an in-degree of 0 to a queue. These are nodes with no prerequisites.
3.  **Process Queue:**
    - While the queue is not empty:
        - Dequeue a node `u`. Increment a counter for visited/processed nodes.
        - For each neighbor `v` of `u`:
            - Decrement `in_degree[v]`.
            - If `in_degree[v]` becomes 0, enqueue `v`.
4.  **Check for Cycle:** If the `count` of processed nodes is equal to the total number of vertices, the graph is a DAG (no cycle). Otherwise, there is a cycle (some nodes could not be processed because their in-degrees never reached 0 due to being part of a cycle).

**Python Implementation (Conceptual for LC207):**
```python
from collections import deque, defaultdict

class BFSCycleDetection:
    def canFinish(self, numCourses: int, prerequisites: list[list[int]]) -> bool:
        graph = defaultdict(list)
        in_degree = [0] * numCourses

        for to_node, from_node in prerequisites:
            graph[from_node].append(to_node)
            in_degree[to_node] += 1

        queue = deque()
        for i in range(numCourses):
            if in_degree[i] == 0:
                queue.append(i)
        
        nodes_processed = 0
        while queue:
            u = queue.popleft()
            nodes_processed += 1
            for v in graph[u]:
                in_degree[v] -= 1
                if in_degree[v] == 0:
                    queue.append(v)
        
        return nodes_processed == numCourses
```
Labuladong's article uses images `![](/algo/images/topological-sort/5.jpeg)` through `![](/algo/images/topological-sort/12.jpeg)` to illustrate the BFS/Kahn's process.

## Complexity Analysis

- **DFS-based:**
    - Time: $O(V+E)$, as each vertex and edge is visited once.
    - Space: $O(V)$ for `visited`, `onPath` arrays, and recursion stack depth.
- **BFS-based (Kahn's):**
    - Time: $O(V+E)$ for building graph, calculating in-degrees, and BFS traversal.
    - Space: $O(V+E)$ for graph representation, $O(V)$ for `in_degree` array and queue.

## 总结 (Summary)
- Cycle detection in directed graphs is crucial for problems like course scheduling (checking for circular dependencies).
- **DFS Approach:** Uses `visited` (globally processed) and `onPath` (current recursion path) arrays. A cycle exists if DFS encounters a node already on `onPath`.
- **BFS Approach (Kahn's Algorithm):** Uses in-degrees of nodes. If a topological sort cannot include all nodes, a cycle exists.
- Both methods are $O(V+E)$.

---
Parent: [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal Algorithms]]
Next: [[Interview/Concept/Algorithms/Graph Traversal/04 - Topological Sort|Topological Sort]]
Related Problems: [[Interview/Practice/LeetCode/LC207 - Course Schedule|LC207]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal/03 - Cycle Detection in Directed Graphs.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Graph Traversal/03 - Cycle Detection in Directed Graphs.md")

def create_topological_sort(path):
    content = r"""---
tags: [concept/algorithms, concept/graph, topic/topological_sort, algorithm/dfs, algorithm/bfs, course/labuladong]
aliases: [Topological Ordering, Kahn's Algorithm for TopoSort]
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/环检测及拓扑排序算法.md|环检测及拓扑排序算法 by Labuladong]].

# Topological Sort

Topological Sort or Topological Ordering of a directed graph is a linear ordering of its vertices such that for every directed edge $(u, v)$ from vertex $u$ to vertex $v$, $u$ comes before $v$ in the ordering.

> [!IMPORTANT] Prerequisite: DAG
> A topological sort is only possible if and only if the graph is a **Directed Acyclic Graph (DAG)**. If the graph contains a cycle, no valid topological sort exists. Therefore, [[Interview/Concept/Algorithms/Graph Traversal/03 - Cycle Detection in Directed Graphs|cycle detection]] is often a prerequisite.

## Applications
- Task scheduling (e.g., course prerequisites, build dependencies).
- Resolving symbol dependencies in linkers.
- Data serialization.

## 1. DFS-based Topological Sort

**Core Idea:** The reverse of the post-order traversal of a DAG yields a topological sort.
1.  Perform a DFS traversal on the graph.
2.  While doing DFS, keep track of `visited` nodes (to avoid re-processing) and `onPath` nodes (to detect cycles). If a cycle is detected, topological sort is impossible.
3.  Collect nodes in a list based on their post-order completion times (i.e., when a node's DFS exploration finishes after all its descendants).
4.  The final topological sort is the reverse of this post-order list.

**Why does reverse post-order work?**
In a DFS, a node `u` finishes (post-order) only after all its descendants `v` (nodes reachable from `u`) have finished. So, in the post-order list, all descendants `v` of `u` will appear *before* `u`. Reversing this list places `u` before all its descendants, satisfying the topological sort condition for edges $(u,v)$.

**Python Implementation (Conceptual for LC210):**
```python
class DFSTopologicalSort:
    def __init__(self):
        self.post_order_result = []
        self.has_cycle = False
        # visited and onPath would be initialized based on num_vertices

    def _dfs(self, graph, u): # Assumes visited, onPath, has_cycle, post_order_result are instance vars
        if self.has_cycle: return
        if self.onPath[u]:
            self.has_cycle = True
            return
        if self.visited[u]:
            return

        self.visited[u] = True
        self.onPath[u] = True
        for v in graph[u]:
            self._dfs(graph, v)
            if self.has_cycle: return
        
        # Post-order position: Add u to post_order_result
        self.post_order_result.append(u)
        self.onPath[u] = False

    def findOrder(self, numCourses: int, prerequisites: list[list[int]]) -> list[int]: # LC210 example
        self.post_order_result = []
        self.has_cycle = False
        self.visited = [False] * numCourses
        self.onPath = [False] * numCourses
        
        graph = [[] for _ in range(numCourses)] # Build graph
        for to_node, from_node in prerequisites:
            graph[from_node].append(to_node)

        for i in range(numCourses):
            if not self.visited[i]:
                self._dfs(graph, i)
        
        if self.has_cycle:
            return []
        
        return self.post_order_result[::-1] # Reverse of post-order
```
Labuladong's visualizer `div_course-schedule-ii` (for LC210) would demonstrate this.
Image `![](/algo/images/topological-sort/2.jpeg)` and `![](/algo/images/topological-sort/3.jpeg)` in the source illustrate how reverse post-order is a topological sort for a binary tree (a special DAG).

## 2. BFS-based Topological Sort (Kahn's Algorithm)

This algorithm is based on the idea of iteratively removing nodes with an in-degree of 0.

**Core Idea (Kahn's Algorithm):**
1.  **Calculate In-degrees:** For each node, count its in-degree.
2.  **Initialize Queue:** Add all nodes with an in-degree of 0 to a queue. These are the initial nodes with no prerequisites.
3.  **Initialize Result List:** `topo_order = []`.
4.  **Process Queue:**
    - While the queue is not empty:
        - Dequeue a node `u`. Add `u` to `topo_order`.
        - For each neighbor `v` of `u`:
            - Decrement `in_degree[v]` (as `u`'s dependency is now met).
            - If `in_degree[v]` becomes 0, enqueue `v`.
5.  **Check for Cycle:** If `len(topo_order)` is equal to the total number of vertices, `topo_order` is a valid topological sort. Otherwise, the graph has a cycle, and topological sort is not possible.

**Python Implementation (Conceptual for LC210):**
```python
from collections import deque, defaultdict

class BFSTopologicalSort:
    def findOrder(self, numCourses: int, prerequisites: list[list[int]]) -> list[int]:
        graph = defaultdict(list)
        in_degree = [0] * numCourses

        for to_node, from_node in prerequisites:
            graph[from_node].append(to_node)
            in_degree[to_node] += 1

        queue = deque()
        for i in range(numCourses):
            if in_degree[i] == 0:
                queue.append(i)
        
        topo_order = []
        while queue:
            u = queue.popleft()
            topo_order.append(u)
            for v in graph[u]:
                in_degree[v] -= 1
                if in_degree[v] == 0:
                    queue.append(v)
        
        if len(topo_order) == numCourses:
            return topo_order
        else:
            return [] # Cycle detected
```
Labuladong's images `![](/algo/images/topological-sort/5.jpeg)` through `![](/algo/images/topological-sort/13.jpeg)` illustrate this BFS/Kahn's process. The order of nodes dequeued forms the topological sort.

## Complexity Analysis

- **DFS-based:**
    - Time: $O(V+E)$ for DFS.
    - Space: $O(V)$ for `visited`, `onPath`, recursion stack, and `post_order_result`.
- **BFS-based (Kahn's):**
    - Time: $O(V+E)$ for graph building, in-degree calculation, and BFS.
    - Space: $O(V+E)$ for graph, $O(V)$ for `in_degree` array, queue, and `topo_order` list.

## 总结 (Summary)
- Topological sort provides a linear ordering of vertices in a DAG such that for every directed edge $(u,v)$, $u$ comes before $v$.
- It's only possible for DAGs. Cycle detection is often part of the process.
- **DFS Approach:** Result is the reverse of the post-order traversal sequence.
- **BFS Approach (Kahn's Algorithm):** Iteratively process nodes with an in-degree of 0. The sequence of processed nodes is a topological sort.
- Both methods are $O(V+E)$.

---
Parent: [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal Algorithms]]
Previous: [[Interview/Concept/Algorithms/Graph Traversal/03 - Cycle Detection in Directed Graphs|Cycle Detection]]
Related Problems: [[Interview/Practice/LeetCode/LC207 - Course Schedule|LC207]], [[Interview/Practice/LeetCode/LC210 - Course Schedule II|LC210]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal/04 - Topological Sort.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Graph Traversal/04 - Topological Sort.md")

def create_bipartite_graph_checking(path):
    content = r"""---
tags: [concept/algorithms, concept/graph, topic/bipartite_graph, algorithm/dfs, algorithm/bfs, course/labuladong]
aliases: [Bipartite Graph Detection, Two-Coloring Graph]
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/二分图判定算法.md|二分图判定算法 by Labuladong]].

# Bipartite Graph Checking

A graph is **bipartite** if its vertices can be divided into two disjoint and independent sets, $U$ and $V$, such that every edge connects a vertex in $U$ to one in $V$. In other words, there are no edges connecting two vertices within the same set. This is equivalent to the **two-coloring problem**: can we color the vertices of the graph with two colors (e.g., red and blue) such that no two adjacent vertices have the same color?

> [!PROPERTY] Bipartite Graph Property
> A graph is bipartite if and only if it contains no odd-length cycles.

## Algorithm: Two-Coloring using Traversal (DFS or BFS)

The general idea is to attempt to color the graph with two colors.
1.  Initialize a `colors` array (or map) to store the color of each vertex (e.g., 0 for uncolored, 1 for color A, -1 for color B).
2.  Iterate through each vertex in the graph. If a vertex is uncolored:
    a.  Start a traversal (DFS or BFS) from this vertex. Assign it an initial color (e.g., color 1).
    b.  During traversal, when moving from a vertex `u` (with `color[u]`) to an adjacent vertex `v`:
        i.  If `v` is uncolored: Assign `v` the opposite color of `u` (`color[v] = -color[u]`) and continue traversal from `v`.
        ii. If `v` is already colored: Check if `color[v] == color[u]`. If they are the same, then an edge connects two nodes of the same color, meaning the graph is not bipartite. Return `false`.
3.  If the traversal completes for all components without finding any same-colored adjacent vertices, the graph is bipartite. Return `true`.

## 1. DFS-based Bipartite Check

```python
class DFSBipartiteCheck:
    def __init__(self, num_vertices):
        self.num_vertices = num_vertices
        self.colors = {} # Using dict for sparse graphs or non-0-indexed nodes
                        # For 0..N-1 nodes, can use [0] * N (0=uncolored, 1=colorA, -1=colorB)
        self.is_bipartite = True

    def _dfs(self, graph, u, current_color):
        if not self.is_bipartite: return # Stop if already found not bipartite

        self.colors[u] = current_color
        
        for v in graph.get(u, []): # graph[u] for list of lists
            if v not in self.colors: # If neighbor v is uncolored
                self._dfs(graph, v, -current_color) # Color with opposite color
                if not self.is_bipartite: return # Propagate failure
            elif self.colors[v] == current_color: # Neighbor v is colored SAME as u
                self.is_bipartite = False
                return
    
    def check(self, graph_adj_list) -> bool: # graph_adj_list: {node: [neighbors]}
        self.colors = {} 
        self.is_bipartite = True
        
        # graph_nodes = list(graph_adj_list.keys()) # If graph can be disconnected
        # Or, if nodes are 0 to N-1:
        graph_nodes = range(self.num_vertices)


        for node in graph_nodes:
            if node not in self.colors and self.is_bipartite:
                self._dfs(graph_adj_list, node, 1) # Start with color 1
        
        return self.is_bipartite
```
Labuladong's visualization `div_is-graph-bipartite-dfs` would show this coloring process.

## 2. BFS-based Bipartite Check

```python
from collections import deque

class BFSBipartiteCheck:
    def check(self, num_vertices: int, graph_adj_list) -> bool: # graph_adj_list: {node: [neighbors]}
        colors = {} # 0=uncolored, 1=colorA, -1=colorB
        
        # graph_nodes = list(graph_adj_list.keys()) # If graph can be disconnected
        # Or, if nodes are 0 to N-1:
        graph_nodes = range(num_vertices)

        for start_node in graph_nodes:
            if start_node not in colors: # If not yet colored, start BFS for this component
                colors[start_node] = 1 # Start with color 1
                queue = deque([start_node])
                
                while queue:
                    u = queue.popleft()
                    current_u_color = colors[u]
                    
                    for v in graph_adj_list.get(u, []):
                        if v not in colors:
                            colors[v] = -current_u_color # Assign opposite color
                            queue.append(v)
                        elif colors[v] == current_u_color:
                            # Edge connects two nodes of same color
                            return False # Not bipartite
        return True # All components successfully two-colored
```
Labuladong's visualization `div_is-graph-bipartite-bfs` would show this layer-by-layer coloring.

## Complexity Analysis (for both DFS and BFS)
- **Time Complexity:** $O(V+E)$, where $V$ is the number of vertices and $E$ is the number of edges. Each vertex and edge is visited a constant number of times.
- **Space Complexity:** $O(V)$ for storing the `colors` array/map and for the recursion stack (DFS) or queue (BFS).

## Applications
- Checking if a graph can be two-colored.
- Many problems involve partitioning a set of items into two groups based on conflicts (e.g., "Possible Bipartition" LC886). If items are vertices and conflicts are edges, the problem becomes checking if the conflict graph is bipartite.

## 总结 (Summary)
- A graph is bipartite if its vertices can be colored with two colors such that no two adjacent vertices share the same color.
- This is equivalent to the graph having no odd-length cycles.
- Bipartite checking can be done efficiently using DFS or BFS traversal.
    - Start coloring an uncolored node.
    - Color its neighbors with the opposite color.
    - If an edge is found connecting two nodes of the same color, the graph is not bipartite.
- Both algorithms run in $O(V+E)$ time.

---
Parent: [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal Algorithms]]
Related Problems: [[Interview/Practice/LeetCode/LC785 - Is Graph Bipartite|LC785]], [[Interview/Practice/LeetCode/LC886 - Possible Bipartition|LC886]]
"""
    ensure_dir(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal"))
    with open(os.path.join(path, "Interview/Concept/Algorithms/Graph Traversal/05 - Bipartite Graph Checking.md"), 'w', encoding='utf-8') as f:
        f.write(content)
    print("Created: Interview/Concept/Algorithms/Graph Traversal/05 - Bipartite Graph Checking.md")

# --- Placeholder LeetCode Problem Notes ---
def create_lc_placeholder(path, lc_id, lc_name, related_concept_path, related_concept_name, labuladong_source_file_path=None):
    lc_filename = f"LC{lc_id} - {lc_name.replace(' ', '_')}.md"
    lc_tags = f"#problem/leetcode #problem #lc/{lc_id} #lc #topic/placeholder #topic #pattern/placeholder #pattern"
    if labuladong_source_file_path:
        lc_tags += " #course/labuladong_mention #course"
    
    content = rf"""---
tags: [{lc_tags.replace("#","")}]
aliases: [LC{lc_id}, LeetCode {lc_id}]
---
> [!NOTE] Source Annotation
> Problem: LeetCode {lc_id} - {lc_name}
> This is a placeholder note. Solution details to be added.
"""
    if labuladong_source_file_path:
        content += f"> Mentioned in Labuladong's notes related to [[{labuladong_source_file_path}|Labuladong's Discussion]].\n"

    content += rf"""
# LeetCode {lc_id}: {lc_name}

## Problem Statement
(To be filled from LeetCode - Visit [https://leetcode.com/problems/{lc_name.lower().replace(' ','-')}/](https://leetcode.com/problems/{lc_name.lower().replace(' ','-')}/))
*Note: Auto-generated URL might be incorrect.*

## Solution Approach
(To be filled, likely using concepts from [[{related_concept_path}|{related_concept_name}]])

### Python Solution (Placeholder)
```python
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None): self.val = val; self.left = left; self.right = right
# class ListNode:
#     def __init__(self, val=0, next=None): self.val = val; self.next = next
class Solution:
    def solve(self, params): # Signature will vary based on problem
        # TODO: Implement solution
        pass
```

## Complexity Analysis
(To be filled)

## 总结 (Summary)
(To be filled)

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[{related_concept_path}|{related_concept_name}]]
"""
    ensure_dir(os.path.join(path, "Interview/Practice/LeetCode"))
    with open(os.path.join(path, "Interview/Practice/LeetCode", lc_filename), 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Created Placeholder: Interview/Practice/LeetCode/{lc_filename}")

def create_topic_exercise_index(path, topic_name, file_name_suffix):
    file_name = f"{topic_name.replace(' ', '_')}_{file_name_suffix}.md"
    content = rf"""---
tags: [index, practice/topic_exercises, topic/{topic_name.lower().replace(' ', '_')}, course/labuladong_mention]
aliases: [{topic_name} Exercise Collection, {topic_name} Problems]
---

> [!NOTE] Source Annotation
> This index page is a placeholder corresponding to a Labuladong exercise collection note titled "【练习】{topic_name}经典习题".
> The actual list of problems would be derived from that source if its content were available.

# {topic_name} Exercises Collection

This page serves as an index or collection point for exercises related to {topic_name}, inspired by Labuladong's curated lists.

*(Problem links and summaries would be listed here based on the content of "【练习】{topic_name}经典习题.md")*

## Example Problems (Illustrative - Actuals Depend on Source)
- *(Placeholder for Problem 1 related to {topic_name})*
- *(Placeholder for Problem 2 related to {topic_name})*

---
Parent: [[Interview/Practice/index|Practice Problems]]
Related Concept: [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01 - Dijkstra Algorithm|Dijkstra Algorithm Core]]
""" # A generic related concept, adjust if possible
    ensure_dir(os.path.join(path, "Interview/Practice/Topic Exercises"))
    with open(os.path.join(path, "Interview/Practice/Topic Exercises", file_name), 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Created Placeholder Exercise Index: Interview/Practice/Topic Exercises/{file_name}")


# --- Main Script ---
if __name__ == "__main__":
    # Define KB Root relative to the script location
    script_dir = os.path.dirname(os.path.abspath(__file__)) 
    kb_root = os.path.abspath(os.path.join(script_dir, "../")) # Adjust if script is elsewhere
    
    print(f"Knowledge base operations will be relative to: {kb_root}")

    # Union-Find
    create_dsu_principles_and_motivation(kb_root)
    create_dsu_implementation_with_optimizations(kb_root)
    create_dsu_index(kb_root) # Updates the index for DSU

    # MST
    ensure_dir(os.path.join(kb_root, "Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree"))
    create_mst_index(kb_root)
    create_mst_introduction(kb_root)
    create_kruskal_algorithm(kb_root)
    create_prim_algorithm(kb_root)

    # Dijkstra
    ensure_dir(os.path.join(kb_root, "Interview/Concept/Algorithms/Graph Traversal/Shortest Path"))
    create_dijkstra_algorithm(kb_root) # This will update/expand the existing Dijkstra note
    create_dijkstra_variants(kb_root)

    # Cycle Detection, Topological Sort, Bipartite Check
    ensure_dir(os.path.join(kb_root, "Interview/Concept/Algorithms/Graph Traversal"))
    create_graph_cycle_detection_directed(kb_root)
    create_topological_sort(kb_root)
    create_bipartite_graph_checking(kb_root)
    
    # Create LeetCode placeholders mentioned in the new files
    # Union-Find problems
    create_lc_placeholder(kb_root, "130", "Surrounded Regions", "Interview/Concept/Data Structures/Disjoint Set Union (DSU)/00 - Union-Find - Principles and Motivation", "Union-Find", labuladong_source_file_path="Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/Union-Find 并查集算法.md")
    create_lc_placeholder(kb_root, "323", "Number of Connected Components in an Undirected Graph", "Interview/Concept/Data Structures/Disjoint Set Union (DSU)/00 - Union-Find - Principles and Motivation", "Union-Find", labuladong_source_file_path="Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/Union-Find 并查集算法.md")
    create_lc_placeholder(kb_root, "684", "Redundant Connection", "Interview/Concept/Data Structures/Disjoint Set Union (DSU)/00 - Union-Find - Principles and Motivation", "Union-Find", labuladong_source_file_path="Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/Union-Find 并查集算法.md")
    create_lc_placeholder(kb_root, "990", "Satisfiability of Equality Equations", "Interview/Concept/Data Structures/Disjoint Set Union (DSU)/00 - Union-Find - Principles and Motivation", "Union-Find", labuladong_source_file_path="Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/Union-Find 并查集算法.md")
    
    # Kruskal problems
    create_lc_placeholder(kb_root, "1135", "Connecting Cities With Minimum Cost", "Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/01 - Kruskal Algorithm", "Kruskal's Algorithm", labuladong_source_file_path="Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/Kruskal 最小生成树算法.md")
    create_lc_placeholder(kb_root, "1584", "Min Cost to Connect All Points", "Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/01 - Kruskal Algorithm", "Kruskal's Algorithm", labuladong_source_file_path="Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/Kruskal 最小生成树算法.md")
    create_lc_placeholder(kb_root, "261", "Graph Valid Tree", "Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/01 - Kruskal Algorithm", "Kruskal's (related to cycle detection for trees)", labuladong_source_file_path="Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/Kruskal 最小生成树算法.md")

    # Prim problems (shares some with Kruskal as they are MST problems)
    # LC1135, LC1584 are already created above, can be related to Prim as well.

    # Dijkstra variants problems
    create_lc_placeholder(kb_root, "787", "Cheapest Flights Within K Stops", "Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01a - Dijkstra Algorithm - Variants and Constraints", "Dijkstra with Constraints / Bellman-Ford", labuladong_source_file_path="Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/Dijkstra 拓展：带限制的最短路问题.md")

    # Cycle Detection / Topo Sort problems
    create_lc_placeholder(kb_root, "207", "Course Schedule", "Interview/Concept/Algorithms/Graph Traversal/03 - Cycle Detection in Directed Graphs", "Cycle Detection", labuladong_source_file_path="Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/环检测及拓扑排序算法.md")
    create_lc_placeholder(kb_root, "210", "Course Schedule II", "Interview/Concept/Algorithms/Graph Traversal/04 - Topological Sort", "Topological Sort", labuladong_source_file_path="Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/环检测及拓扑排序算法.md")

    # Bipartite Graph problems
    create_lc_placeholder(kb_root, "785", "Is Graph Bipartite", "Interview/Concept/Algorithms/Graph Traversal/05 - Bipartite Graph Checking", "Bipartite Graph Checking", labuladong_source_file_path="Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/二分图判定算法.md")
    create_lc_placeholder(kb_root, "886", "Possible Bipartition", "Interview/Concept/Algorithms/Graph Traversal/05 - Bipartite Graph Checking", "Bipartite Graph Checking", labuladong_source_file_path="Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典图算法/二分图判定算法.md")

    # Create placeholder Topic Exercise Index files
    ensure_dir(os.path.join(kb_root, "Interview/Practice/Topic Exercises"))
    create_topic_exercise_index(kb_root, "Dijkstra Algorithm", "Exercises_Index")
    create_topic_exercise_index(kb_root, "Union-Find", "Exercises_Index")
    
    # Update main Graph Traversal index (if it exists, otherwise it will be created by a broader update)
    graph_traversal_index_path = os.path.join(kb_root, "Interview/Concept/Algorithms/Graph Traversal/index.md")
    if os.path.exists(graph_traversal_index_path):
        # This part is tricky to automate perfectly without more sophisticated parsing.
        # For now, just notify to manually update.
        print(f"NOTE: Please manually update the Mermaid diagram and links in {graph_traversal_index_path} to include MST, Cycle Detection, Topo Sort, Bipartite Checking.")
    else:
        # Create a basic one if it doesn't exist (though it likely does from previous runs)
        ensure_dir(os.path.dirname(graph_traversal_index_path))
        base_graph_traversal_index_content = r"""---
tags: [index, concept/algorithms, concept/graph_traversal, concept/dfs, concept/bfs, concept/shortest_path, topic/mst, topic/cycle_detection, topic/topological_sort, topic/bipartite_graph]
aliases: [Graph Traversal Algorithm Index, Graph Search Algorithms]
---

# Graph Traversal Algorithms

This section covers algorithms for traversing and searching graph data structures, including fundamental exploration techniques and specific graph problems.

## Basic Traversal Techniques
- [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|Graph DFS Traversal]]
- [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Graph BFS Traversal]]
- [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Framework (Shortest Path)]]

## Shortest Path Algorithms
- [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/index|Shortest Path Algorithms Index]]
  - [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/00 - Graph Shortest Path Algorithms - Overview|Overview]]
  - [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01 - Dijkstra Algorithm|Dijkstra's Algorithm]]
  - [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01a - Dijkstra Algorithm - Variants and Constraints|Dijkstra - Variants & Constraints]]
  - `[[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/02 - Bellman-Ford Algorithm|Bellman-Ford Algorithm]]` (Placeholder)
  - `[[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/03 - Floyd-Warshall Algorithm|Floyd-Warshall Algorithm]]` (Placeholder)

## Minimum Spanning Tree (MST)
- [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/index|Minimum Spanning Tree Index]]
  - [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/00 - MST - Introduction|MST Introduction]]
  - [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/01 - Kruskal Algorithm|Kruskal's Algorithm]]
  - [[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/02 - Prim Algorithm|Prim's Algorithm]]

## Connectivity, Cycles, and Ordering
- [[Interview/Concept/Algorithms/Graph Traversal/03 - Cycle Detection in Directed Graphs|Cycle Detection (Directed Graphs)]]
- [[Interview/Concept/Algorithms/Graph Traversal/04 - Topological Sort|Topological Sort]]
- [[Interview/Concept/Algorithms/Graph Traversal/05 - Bipartite Graph Checking|Bipartite Graph Checking]]
- [[Interview/Concept/Data Structures/Graph/Connected Components|Connected Components]]

## Visualization
```mermaid
graph TD
    GT["Graph Traversal"] --> Basic["Basic Traversal (DFS, BFS, BFS Framework)"]
    GT --> SP["Shortest Path Algorithms"]
    GT --> MST["Minimum Spanning Tree (MST)"]
    GT --> OtherGraphProbs["Connectivity, Cycles, Ordering"]

    SP --> SP_Overview["[[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/00 - Graph Shortest Path Algorithms - Overview|Overview]]"]
    SP_Overview --> Dijkstra["[[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01 - Dijkstra Algorithm|Dijkstra]] & [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01a - Dijkstra Algorithm - Variants and Constraints|Variants]]"]
    SP_Overview --> BellmanFord["(Bellman-Ford)"]
    SP_Overview --> FloydWarshall["(Floyd-Warshall)"]

    MST --> MST_Intro["[[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/00 - MST - Introduction|MST Intro]]"]
    MST --> Kruskal["[[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/01 - Kruskal Algorithm|Kruskal]]"]
    MST --> Prim["[[Interview/Concept/Algorithms/Graph Traversal/Minimum Spanning Tree/02 - Prim Algorithm|Prim]]"]
    
    OtherGraphProbs --> CycleDet["[[Interview/Concept/Algorithms/Graph Traversal/03 - Cycle Detection in Directed Graphs|Cycle Detection]]"]
    OtherGraphProbs --> TopoSort["[[Interview/Concept/Algorithms/Graph Traversal/04 - Topological Sort|Topological Sort]]"]
    OtherGraphProbs --> Bipartite["[[Interview/Concept/Algorithms/Graph Traversal/05 - Bipartite Graph Checking|Bipartite Check]]"]
    OtherGraphProbs --> ConnComp["[[Interview/Concept/Data Structures/Graph/Connected Components|Connected Components]]"]

    classDef main fill:#f0e6ff,stroke:#8a2be2,stroke-width:2px;
    class GT main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
"""
        with open(graph_traversal_index_path, 'w', encoding='utf-8') as f:
            f.write(base_graph_traversal_index_content)
        print(f"Created/Updated: {graph_traversal_index_path} with new structure.")

    print("Script finished. Specified files created/updated.")

