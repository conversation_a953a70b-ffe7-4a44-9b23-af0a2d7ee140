import os
import textwrap

# Helper function to create directories if they don't exist
def ensure_dir(filepath):
    directory = os.path.dirname(filepath)
    if not os.path.exists(directory):
        os.makedirs(directory)
    print(f"Ensured directory: {directory}")

# --- Content Generation Functions ---

def create_recursion_thinking_modes(kb_root):
    filepath = os.path.join(kb_root, "Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes.md")
    ensure_dir(filepath)
    content = r"""---
tags: [concept/algorithms, concept/recursion, concept/dfs, concept/divide_and_conquer, type/problem_solving_framework]
aliases: [Recursive Thinking, Tree Perspective of Recursion, Traverse vs Decompose in Recursion, 递归思维模式]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/一个视角 _ 两种思维模式搞定递归.md]].
> Labuladong emphasizes that understanding recursion through the "tree" perspective and applying "traversal" or "decomposition" thinking modes are key to mastering recursive algorithms.

# Recursion: One Perspective, Two Thinking Modes

Recursion is a powerful problem-solving technique where a function calls itself to solve smaller instances of the same problem. The most effective way to understand and design recursive algorithms is through the **tree perspective**.

## 🌲 The Tree Perspective of Recursion

Any recursive algorithm can be visualized as operations on a **recursion tree**.
- The initial call is the root of this tree.
- Each recursive call made by a function becomes a child node.
- The `base cases` of the recursion are the leaf nodes of this tree.

The execution of a recursive function is like a traversal (often DFS-like) of this implicit recursion tree.

### Example 1: Fibonacci Sequence
The Fibonacci sequence defined as $fib(n) = fib(n-1) + fib(n-2)$ (with $fib(0)=0, fib(1)=1$) naturally forms a binary recursion tree.
- `fib(5)` calls `fib(4)` and `fib(3)`.
- `fib(4)` calls `fib(3)` and `fib(2)`, and so on.
Labuladong's visualizer (`div_mydata-fib`) clearly shows this tree and how the values propagate up from the leaves (base cases) to the root.

### Example 2: Permutations
Generating all permutations of a set of numbers (e.g., `[1,2,3]`) also forms a recursion tree.
- The root represents the initial state (no numbers chosen).
- Children of a node represent choosing one of the remaining available numbers.
- Paths from the root to a leaf represent a complete permutation.
Labuladong's visualizer (`div_permutations`) shows this process.

## 🧠 Two Thinking Modes for Designing Recursive Algorithms

When faced with a problem that seems suitable for recursion (i.e., can be broken down into smaller, self-similar subproblems, or involves exhaustive search), there are two primary ways to approach the design:

### 1. Decomposition Thinking Mode (Divide and Conquer Style)

- **Core Idea:** Define the recursive function based on what it **returns**. The function solves a subproblem and returns its result. The solution to the original problem is constructed from the results of these subproblems.
- **Function Signature:** The function usually has a meaningful return value representing the solution to `problem(input_parameters)`.
- **Process:**
    1.  **Define the function's meaning:** What does `recursive_function(sub_problem_input)` compute and return?
    2.  **Base Case(s):** Identify the simplest versions of the problem that can be solved directly.
    3.  **Recursive Step:** Assume `recursive_function` correctly solves smaller subproblems. Use the results of these subproblem calls to construct the solution for the current problem.
- **Examples:**
    - **Fibonacci:** `fib(n)` is defined as returning the n-th Fibonacci number. It uses `fib(n-1)` and `fib(n-2)` (solutions to subproblems) to compute its result.
    - **Max Depth of Binary Tree (LeetCode 104):** `maxDepth(root)` returns the max depth of the tree rooted at `root`. It's computed as `1 + max(maxDepth(root.left), maxDepth(root.right))`. The problem is *decomposed* into finding max depths of subtrees.
        - Labuladong's visualizer `div_maximum-depth-of-binary-tree-divide` shows this.

```python
# Conceptual: Max Depth using Decomposition
# def maxDepth_decompose(root):
#     if root is None: return 0
#     left_depth = maxDepth_decompose(root.left)   # Result from left subproblem
#     right_depth = maxDepth_decompose(root.right) # Result from right subproblem
#     return 1 + max(left_depth, right_depth)      # Combine results
```

### 2. Traversal Thinking Mode (Iterative Exploration Style)

- **Core Idea:** The recursive function's primary role is to **traverse** the implicit recursion tree or state space. The "answer" is often accumulated in global variables, passed by reference, or built up as a side effect during the traversal.
- **Function Signature:** The function often has a `void` return type (or returns nothing explicitly in Python). It takes parameters that represent the current state of the traversal (e.g., current path, current depth).
- **Process:**
    1.  **Define state parameters:** What information needs to be carried along during the traversal?
    2.  **Base Case(s):** When to stop a particular path of exploration (e.g., reached a leaf, found a solution, invalid state).
    3.  **Recursive Step (Choices/Transitions):** For each possible "next step" or "choice" from the current state, make the choice, recursively call the function for the new state, and then *undo* the choice (backtrack) if necessary to explore other options.
- **Examples:**
    - **Permutations:** The `backtrack` function explores choices (which number to pick next). When a full permutation (path to a leaf) is formed, it's added to a global result list.
    - **Max Depth of Binary Tree (LeetCode 104) - Traversal Approach:**
        - Maintain a `current_depth` variable.
        - Pre-order: Increment `current_depth`. If leaf, update `max_overall_depth`. Recurse.
        - Post-order: Decrement `current_depth`.
        - Labuladong's visualizer `div_maximum-depth-of-binary-tree-traverse` illustrates this.

```python
# Conceptual: Max Depth using Traversal
# class Solution:
#     def __init__(self):
#         self.max_overall_depth = 0
#         self.current_depth = 0
#
#     def maxDepth_traverse(self, root):
#         self._traverse(root)
#         return self.max_overall_depth
#
#     def _traverse(self, node):
#         if node is None:
#             # Optional: update max_overall_depth here if path ends just before null
#             # self.max_overall_depth = max(self.max_overall_depth, self.current_depth)
#             return
#
#         self.current_depth += 1 # Pre-order action
#         if node.left is None and node.right is None: # Leaf node
#             self.max_overall_depth = max(self.max_overall_depth, self.current_depth)
#
#         self._traverse(node.left)
#         self._traverse(node.right)
#
#         self.current_depth -= 1 # Post-order action (backtrack depth)
```

## 🤔 Choosing the Right Mode

- **Decomposition ("Define and Conquer"):**
    - Suitable when the problem has optimal substructure and overlapping subproblems (can lead to dynamic programming).
    - The definition of what the recursive function *returns* is key.
    - Often more elegant for problems where the result can be directly computed from sub-results (e.g., tree traversals that build a list, tree property calculations like height/sum).
- **Traversal ("Iterate and Accumulate"):**
    - Suitable for problems requiring exploration of a state space, finding all solutions, or when intermediate path information is critical.
    - The parameters passed into the recursive function to maintain state are key.
    - Backtracking is a specialized form of this (e.g., N-Queens, Sudoku, permutations, combinations).

Sometimes, a problem can be solved using either mode, as shown with the "Max Depth of Binary Tree" example. The choice might depend on clarity or ease of implementation.

## 总结 (Summary)
1.  **Tree Perspective:** Always visualize recursion as traversing a tree. This helps in understanding control flow and base cases.
2.  **Two Thinking Modes:**
    - **Decomposition:** Define what `recursive_func(input)` returns. Use results of `recursive_func(sub_input)` to solve for `input`.
    - **Traversal:** `recursive_func(state_params)` explores possibilities. Results are often accumulated as side effects or in global/passed-by-reference variables. Backtracking is common here.
3.  **Application:**
    - Decomposition often maps to [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]] or [[Interview/Concept/Algorithms/Divide and Conquer/index|Divide and Conquer]].
    - Traversal often maps to [[Interview/Concept/Algorithms/Backtracking/index|Backtracking]] or general [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|DFS]] state-space search.

Mastering these two modes and the tree perspective provides a powerful framework for tackling a wide range of recursive problems.

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
Previous: [[Interview/Concept/Algorithms/Tree Traversal/04 - DFS vs BFS - When to Use Which|DFS vs BFS - When to Use Which]] (Example of tree traversals)
Next: [[Interview/Concept/Algorithms/Backtracking/index|Backtracking Framework]] (as an application of traversal thinking)
Related: [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Binary Tree Recursive Traversal]]
"""
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created: {filepath}")

def create_bfs_framework(kb_root):
    filepath = os.path.join(kb_root, "Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path).md")
    ensure_dir(filepath)
    content = r"""---
tags: [concept/algorithms, concept/graph_traversal, concept/bfs, concept/shortest_path, type/framework, pattern/bfs]
aliases: [BFS Algorithm Framework, Breadth-First Search Template, BFS for Shortest Path]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/BFS 算法解题套路框架.md]].
> This note details the BFS framework, particularly for finding the shortest path in unweighted graphs or minimum steps in state-space searches.

# BFS Algorithm Framework for Shortest Path / Minimum Steps

Breadth-First Search (BFS) is fundamentally a graph traversal algorithm that explores neighbor nodes first before moving to the next level neighbors. This property makes it ideal for finding the shortest path in an unweighted graph or the minimum number of steps to reach a target state in a state-space search.

## 🖼️ Core BFS Framework (for shortest path/steps)

This framework is based on Labuladong's "写法二" (Pattern 2) from [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Binary Tree Level-Order Traversal (BFS)]], adapted for general graph/state-space search.

```python
from collections import deque

def shortest_path_bfs(start_node, target_node, get_neighbors_func, is_valid_node_func=None):
    '''
    Finds the shortest path (number of steps) from start_node to target_node.

    Args:
        start_node: The initial state/node.
        target_node: The goal state/node.
        get_neighbors_func: A function that takes a node and returns an iterable of its neighbors.
                            def get_neighbors(node) -> list_of_neighbors: ...
        is_valid_node_func: Optional function to check if a neighbor is valid (e.g., not a "deadend").
                            def is_valid(node) -> bool: ...
                            If None, all neighbors are considered valid initially.

    Returns:
        Minimum number of steps if target is reachable, otherwise -1.
    '''
    if is_valid_node_func and not is_valid_node_func(start_node):
        return -1 # Starting in an invalid state (e.g. deadend)

    queue = deque([start_node])
    visited = {start_node}  # To avoid cycles and redundant work
    steps = 0

    while queue:
        level_size = len(queue)  # Number of nodes at the current level (current step)

        for _ in range(level_size):
            current_node = queue.popleft()

            if current_node == target_node:
                return steps

            for neighbor in get_neighbors_func(current_node):
                if is_valid_node_func and not is_valid_node_func(neighbor):
                    continue # Skip invalid neighbors (e.g., deadends)
                
                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append(neighbor)
        
        steps += 1 # Move to the next level / increment step count

    return -1 # Target not reachable
```

**Key Components:**
1.  **`queue`:** A [[Interview/Concept/Programming Languages/Python/03 - Python Deque for Interviews|deque]] to store nodes to visit (FIFO).
2.  **`visited`:** A [[Interview/Concept/Programming Languages/Python/06 - Python Set (Hash Set) for Interviews|set]] to keep track of already visited nodes to prevent cycles and redundant processing.
3.  **`steps`:** An integer to count the number of levels traversed (which corresponds to the length of the path in unweighted scenarios).
4.  **Outer `while` loop:** Continues as long as there are nodes to explore.
5.  **Inner `for` loop (with `level_size`):** Processes all nodes at the current `steps` distance from the `start_node`. This is crucial for correctly counting steps layer by layer.
6.  **Neighbor Generation:** A function `get_neighbors_func(node)` is needed to define how to get from one state to adjacent states.
7.  **Validity Check (Optional):** A function `is_valid_node_func(node)` can be used to filter out invalid states (like "deadends" in the Open the Lock problem).

## 🧩 Problem Abstraction: The Key Skill

The main challenge in applying BFS is often abstracting the given problem into a graph/state-space search:
- **What is a "node" or "state"?** (e.g., a string representation of a lock combination, a tuple representing a puzzle board).
- **What is the `start_node`?** (e.g., "0000" for the lock, initial `board` for puzzle).
- **What is the `target_node`?** (e.g., the target lock combination, the solved puzzle state).
- **How to define `get_neighbors_func(current_node)`?** What are the valid transitions from one state to another? (e.g., turning one dial of the lock, sliding the '0' tile).
- **Are there invalid states (`is_valid_node_func`)?** (e.g., `deadends` in the lock problem).

Once these are defined, the BFS framework can be applied.

### Example 1: LeetCode 752. Open the Lock
- **Node/State:** A 4-digit string (e.g., "0000", "1000").
- **`start_node`:** "0000".
- **`target_node`:** The `target` string.
- **`get_neighbors_func(combo_str)`:** For each of the 4 digits, generate two neighbors: one by turning up (+1, with '9'->'0' wrap) and one by turning down (-1, with '0'->'9' wrap). Total 8 potential neighbors.
- **`is_valid_node_func(combo_str)`:** Check if `combo_str` is in the `deadends` set. If yes, it's invalid.

```python
# Solution for LC752 (Python, using the framework logic)
from collections import deque

class SolutionLC752: # Renamed for clarity within this script
    def openLock(self, deadends: list[str], target: str) -> int:
        dead_set = set(deadends)
        
        if "0000" in dead_set:
            return -1
        if target == "0000":
            return 0

        q = deque(["0000"])
        visited = {"0000"}
        steps = 0

        while q:
            level_size = len(q)
            for _ in range(level_size):
                current_combo = q.popleft()
                if current_combo == target:
                    return steps

                for i in range(4): # For each of the 4 dials
                    original_char_code = ord(current_combo[i])
                    # Turn up
                    new_char_up_code = ord('0') if current_combo[i] == '9' else original_char_code + 1
                    neighbor_up_list = list(current_combo)
                    neighbor_up_list[i] = chr(new_char_up_code)
                    neighbor_up = "".join(neighbor_up_list)
                    if neighbor_up not in visited and neighbor_up not in dead_set:
                        visited.add(neighbor_up)
                        q.append(neighbor_up)

                    # Turn down
                    new_char_down_code = ord('9') if current_combo[i] == '0' else original_char_code - 1
                    neighbor_down_list = list(current_combo)
                    neighbor_down_list[i] = chr(new_char_down_code)
                    neighbor_down = "".join(neighbor_down_list)
                    if neighbor_down not in visited and neighbor_down not in dead_set:
                        visited.add(neighbor_down)
                        q.append(neighbor_down)
            steps += 1
        return -1
```

### Example 2: LeetCode 773. Sliding Puzzle
- **Node/State:** A string representation of the 2x3 board (e.g., "412503").
- **`start_node`:** Stringified initial `board`.
- **`target_node`:** "123450".
- **`get_neighbors_func(board_str)`:** Find the index of '0'. Based on its 2D position (derived from 1D index), find valid swap positions (neighbors). Generate new board strings for each valid swap.
    - Precomputed `neighbor_map = [[1,3],[0,2,4],[1,5],[0,4],[1,3,5],[2,4]]` helps map 1D index of '0' to 1D indices of its swappable neighbors.
- **`is_valid_node_func`:** Not explicitly needed here beyond `visited` check, as all reachable board states are "valid" unless they are the target.

The provided solution in Labuladong's text for Sliding Puzzle follows this BFS framework structure.

## 🔄 Bidirectional BFS Optimization

For problems where the `target_node` is known, BFS can sometimes be optimized using **Bidirectional BFS**.
- **Idea:** Start BFS simultaneously from `start_node` (forward search) and `target_node` (backward search, by reversing transitions if needed).
- **Termination:** The search stops when the frontiers of the two searches meet.
- **Data Structures:** Two queues (or sets, for faster intersection checks) `q1`, `q2` and two `visited` sets `visited1`, `visited2`.
- **Strategy:** In each step, expand the smaller of the two frontiers to potentially meet faster.
- **Path Length:** If `steps1` is steps from start and `steps2` from end, total steps is `steps1 + steps2`. In Labuladong's OpenLock bidirectional example, `step` is incremented once per combined expansion (one level from `q1`), and when `q1`'s neighbors are found in `q2`, `step` is the answer.

**Conceptual Bidirectional BFS Framework (for Open the Lock):**
```python
# Conceptual from Labuladong's OpenLock BiBFS
# q1 = {"0000"}
# q2 = {target}
# visited = {"0000", target}
# dead_set = set(deadends)
# step = 0
#
# while q1 and q2:
#     step += 1
#     # Always expand the smaller set
#     if len(q1) > len(q2):
#         q1, q2 = q2, q1 # Swap to expand smaller frontier
#
#     next_level_nodes = set()
#     for current_node in q1:
#         # ... generate neighbors ...
#         for neighbor in get_neighbors_func(current_node):
#             if neighbor in q2: # Intersection found
#                 return step
#             if neighbor not in visited and neighbor not in dead_set:
#                 visited.add(neighbor)
#                 next_level_nodes.add(neighbor)
#     q1 = next_level_nodes
# return -1
```
- **Benefit:** Can significantly reduce the search space explored. If branching factor is $b$ and shortest path is $d$, standard BFS explores $O(b^d)$ states. Bidirectional BFS explores roughly $2 \times O(b^{d/2})$ states, which can be much smaller.
- **Limitation:** Requires knowing the `target_node` explicitly. Not applicable if the goal is defined by a property rather than a specific state (e.g., "find any leaf node").

## 总结 (Summary)
- BFS is a go-to algorithm for shortest path in unweighted graphs or minimum steps in state-space search.
- The core framework involves a queue, a `visited` set, and layer-by-layer processing.
- **Problem Abstraction is Key:** Define states, start/target, transitions (neighbors), and validity.
- **Bidirectional BFS** can optimize by searching from both start and target, meeting in the middle, but requires a known target.
- Understanding this framework allows tackling a wide range of "minimum steps/path" problems.

---
Parent: [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal Algorithms]]
Previous: [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Graph BFS Traversal]] (general graph BFS)
Next: [[Interview/Practice/LeetCode/LC752 - Open the Lock|LC752 - Open the Lock]] (as an example application)
Related: [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Tree BFS]]
"""
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created: {filepath}")

def create_lc752_open_the_lock(kb_root):
    filepath = os.path.join(kb_root, "Practice/LeetCode/LC752 - Open the Lock.md")
    ensure_dir(filepath)
    content = r"""---
tags: [problem/leetcode, lc/medium, topic/graph, topic/bfs, pattern/bfs_shortest_path, pattern/bidirectional_bfs]
aliases: [LC752, LeetCode 752, Open the Lock, 打开转盘锁]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 752. Open the Lock
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/BFS 算法解题套路框架.md]].

# LeetCode 752: Open the Lock

## Problem Statement

You have a lock with four circular wheels, each with digits '0'-'9'. The lock starts at "0000". You are given a list of `deadends` (strings representing locked states) and a `target` string. Each move consists of turning one wheel one digit. Find the minimum number of turns required to reach the `target` from "0000" without passing through any `deadends`. If it's impossible, return -1.

**Official Link:** [LeetCode 752. Open the Lock](https://leetcode.com/problems/open-the-lock/)

**Example 1:**
Input: `deadends = ["0201","0101","0102","1212","2002"]`, `target = "0202"`
Output: `6`
Explanation: A possible sequence: "0000" -> "1000" -> "1100" -> "1200" -> "1201" -> "1202" -> "0202".

## Solution Approach: BFS

This problem asks for the minimum number of turns, which is a classic shortest path problem on an unweighted graph. The states of the graph are all possible 4-digit combinations ("0000" to "9999"), and an edge exists between two combinations if one can be transformed into the other by a single wheel turn. This is a perfect fit for the [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Framework]].

### Problem Abstraction for BFS

1.  **Node/State:** A 4-digit string representing the lock's current combination.
2.  **`start_node`:** "0000".
3.  **`target_node`:** The input `target` string.
4.  **`get_neighbors(combo_str)`:** For a given `combo_str`, generate its 8 potential neighbors:
    - For each of the 4 wheels (indices 0, 1, 2, 3):
        - Turn wheel up by 1 (e.g., '0' -> '1', '9' -> '0').
        - Turn wheel down by 1 (e.g., '1' -> '0', '0' -> '9').
5.  **Invalid States (`deadends`):** Any combination listed in `deadends` cannot be visited.

### Standard BFS Solution (Python)

```python
from collections import deque

class Solution:
    def _plus_one(self, s: str, j: int) -> str:
        chars = list(s)
        if chars[j] == '9':
            chars[j] = '0'
        else:
            chars[j] = str(int(chars[j]) + 1)
        return "".join(chars)

    def _minus_one(self, s: str, j: int) -> str:
        chars = list(s)
        if chars[j] == '0':
            chars[j] = '9'
        else:
            chars[j] = str(int(chars[j]) - 1)
        return "".join(chars)

    def _get_neighbors(self, combo_str: str) -> list[str]:
        neighbors = []
        for i in range(4): # For each of the 4 dials
            neighbors.append(self._plus_one(combo_str, i))
            neighbors.append(self._minus_one(combo_str, i))
        return neighbors

    def openLock(self, deadends: list[str], target: str) -> int:
        dead_set = set(deadends)
        
        if "0000" in dead_set:
            return -1
        if target == "0000": # Already at target
            return 0

        q = deque(["0000"])
        visited = {"0000"}
        steps = 0

        while q:
            level_size = len(q)
            for _ in range(level_size):
                current_combo = q.popleft()
                
                if current_combo == target:
                    return steps

                for neighbor in self._get_neighbors(current_combo):
                    if neighbor not in visited and neighbor not in dead_set:
                        visited.add(neighbor)
                        q.append(neighbor)
            steps += 1
        
        return -1 # Target not reachable
```

**Complexity (Standard BFS):**
- State space size: $10^4$ possible combinations.
- Each state has 8 neighbors.
- Time: $O(N \cdot k + D)$, where $N=10^4$ is number of states, $k=8$ is neighbors, $D$ is length of `deadends` list (for set conversion). Roughly $O(10^4)$.
- Space: $O(10^4)$ for `visited` set and `queue`.

### Bidirectional BFS Solution (Python)

Bidirectional BFS can be more efficient by exploring from both start and target simultaneously.

```python
from collections import deque

class Solution: # (Using same helper methods _plus_one, _minus_one, _get_neighbors as above)
    def _plus_one(self, s: str, j: int) -> str:
        chars = list(s)
        if chars[j] == '9': chars[j] = '0'
        else: chars[j] = str(int(chars[j]) + 1)
        return "".join(chars)

    def _minus_one(self, s: str, j: int) -> str:
        chars = list(s)
        if chars[j] == '0': chars[j] = '9'
        else: chars[j] = str(int(chars[j]) - 1)
        return "".join(chars)

    def _get_neighbors(self, combo_str: str) -> list[str]:
        neighbors = []
        for i in range(4):
            neighbors.append(self._plus_one(combo_str, i))
            neighbors.append(self._minus_one(combo_str, i))
        return neighbors

    def openLock(self, deadends: list[str], target: str) -> int:
        dead_set = set(deadends)
        if "0000" in dead_set: return -1
        if target == "0000": return 0
        if target in dead_set: return -1 # Added check: if target itself is a deadend

        # Use sets for frontiers for O(1) intersection check
        q1 = {"0000"}
        q2 = {target}
        visited = {"0000", target} # Add both start and target to visited initially
        steps = 0

        while q1 and q2:
            # Optimization: always expand the smaller frontier
            if len(q1) > len(q2):
                q1, q2 = q2, q1 # Swap frontiers

            next_level_nodes = set()
            for current_combo in q1:
                if current_combo == target : # Should be caught by intersection check, but safe
                    return steps

                for neighbor in self._get_neighbors(current_combo):
                    if neighbor in q2: # Intersection found!
                        return steps + 1 # Current steps for q1 + 1 step for neighbor to reach q2's frontier
                    
                    if neighbor not in visited and neighbor not in dead_set:
                        visited.add(neighbor)
                        next_level_nodes.add(neighbor)
            
            q1 = next_level_nodes
            steps += 1
            # Note on steps: If intersection found, q1 expanded 'steps' times,
            # and q2 (other frontier) expanded 'steps' or 'steps-1' times.
            # The path connects after one more step from q1's current nodes.
            # Labuladong's implementation increments step at start of loop AFTER first check.
            # This version increments step after expanding one frontier.
            # The critical point is how `steps` relates to path length upon intersection.
            # If q1 expands for 's1' steps and q2 for 's2' steps, total is s1+s2.
            # Here, steps for q1. `neighbor in q2` means neighbor is on frontier of q2.
            # The total steps is current `steps` (for q1) + 1 (for neighbor to bridge).

        return -1
```
**Correction on step count for BiBFS (Labuladong's style):**
Labuladong's version correctly increments `step` at the start of each expansion cycle for `q1`.
If `q1` has taken `s` steps, its current nodes are at distance `s`. If a neighbor (distance `s+1`) is in `q2`, it means `q2` has *also* reached that neighbor. The number of steps `q2` took to reach that common neighbor isn't explicitly tracked as `s2` in the same loop variable. Instead, `visited` ensures that `q2` also doesn't explore beyond `q1`. The sum of steps is handled by the single `steps` variable.

Revised BiBFS `steps` logic to align with common pattern:
```python
# ... (inside openLock method, after initial checks and q1, q2, visited setup)
        steps = 0
        while q1 and q2:
            # Optimization: always expand the smaller frontier
            if len(q1) > len(q2):
                q1, q2 = q2, q1 

            temp_q = set() # To store next level for the currently expanding frontier (q1)
            for combo in q1:
                # Check for intersection with the *other* frontier
                # This check might be redundant if done after neighbor generation
                # if combo in q2: return steps 

                for neighbor in self._get_neighbors(combo):
                    if neighbor in q2: # Intersection!
                        return steps + 1
                    if neighbor not in visited and neighbor not in dead_set:
                        visited.add(neighbor)
                        temp_q.add(neighbor)
            
            steps += 1
            q1 = temp_q # q1 now represents nodes at 'steps' distance from its original start

        return -1
```
**Complexity (Bidirectional BFS):**
- Roughly $O(2 \cdot b^{d/2})$ where $b=8$ (branching factor), $d$ is shortest path.
- Still $O(10^4)$ in worst case, but practically faster for many cases. Space is similar.

## Visualization
Imagine the state space as a large graph.
- **Standard BFS:** Ripples expanding from "0000".
    ```tikz
    \begin{tikzpicture}[tn/.style={circle, draw, font=\sffamily\tiny, minimum size=4mm, inner sep=0.5pt}]
        \node[tn, fill=blue!20] (s) at (0,0) {0000};
        \node[tn, fill=red!20] (t) at (5,0) {Target};
        \draw[dashed, blue!50] (s) circle (0.5cm);
        \draw[dashed, blue!50] (s) circle (1cm);
        \draw[dashed, blue!50] (s) circle (1.5cm);
        \draw[dashed, blue!50] (s) circle (2cm); \node at (2.3,0) {...};
        \node at (2.5, -1) {Standard BFS expands from start};
    \end{tikzpicture}
    ```
- **Bidirectional BFS:** Ripples expanding from "0000" and `target` simultaneously. They meet earlier.
    ```tikz
    \begin{tikzpicture}[tn/.style={circle, draw, font=\sffamily\tiny, minimum size=4mm, inner sep=0.5pt}]
        \node[tn, fill=blue!20] (s) at (0,0) {0000};
        \node[tn, fill=red!20] (t) at (5,0) {Target};
        \draw[dashed, blue!50] (s) circle (0.5cm);
        \draw[dashed, blue!50] (s) circle (1cm);
        \draw[dashed, red!50] (t) circle (0.5cm);
        \draw[dashed, red!50] (t) circle (1cm);
        \draw[dashed, red!50] (t) circle (1.5cm);
        \node[tn,fill=green!30] (meet) at (2.5,0) {Meet!};
        \draw[<->, thick, green!70!black] (1.2,0) -- (meet);
        \draw[<->, thick, green!70!black] (3.8,0) -- (meet);
        \node at (2.5, -1) {Bidirectional BFS expands from both ends};
    \end{tikzpicture}
    ```

## 总结 (Summary)
- "Open the Lock" is a shortest path problem on an implicit graph of lock combinations.
- Standard BFS is a natural fit:
    - States are 4-digit strings.
    - Transitions involve turning one wheel up/down.
    - `visited` set handles cycles, `deadends` set prunes invalid states.
- Bidirectional BFS can optimize by reducing the search radius, effective when the target state is known.
- The core BFS framework remains the same; the main work is in defining states and transitions.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Framework]], [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Graph BFS]]
Related Problems: [[Interview/Practice/LeetCode/LC773 - Sliding Puzzle|LC773 - Sliding Puzzle]]
"""
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created: {filepath}")

def create_lc773_sliding_puzzle(kb_root):
    filepath = os.path.join(kb_root, "Practice/LeetCode/LC773 - Sliding Puzzle.md")
    ensure_dir(filepath)
    content = r"""---
tags: [problem/leetcode, lc/hard, topic/graph, topic/bfs, pattern/bfs_shortest_path, topic/matrix]
aliases: [LC773, LeetCode 773, Sliding Puzzle]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 773. Sliding Puzzle
> Solution and explanation adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/BFS 算法解题套路框架.md]].

# LeetCode 773: Sliding Puzzle

## Problem Statement

On a 2x3 `board`, there are 5 tiles labeled `1` through `5`, and an empty square represented by `0`. A move consists of choosing `0` and a 4-directionally adjacent number and swapping it. The solved state is `[[1,2,3],[4,5,0]]`. Given an initial `board`, return the minimum number of moves to solve it, or -1 if unsolvable.

**Official Link:** [LeetCode 773. Sliding Puzzle](https://leetcode.com/problems/sliding-puzzle/)

**Example 1:**
Input: `board = [[1,2,3],[4,0,5]]`
Output: `1`
Explanation: Swap 0 and 5 in one move.

**Example 2:**
Input: `board = [[4,1,2],[5,0,3]]`
Output: `5`

## Solution Approach: BFS

This problem asks for the minimum number of moves, which signifies a shortest path problem on an unweighted graph. The states of the graph are the different configurations of the puzzle board. An edge exists between two board configurations if one can be transformed into the other by a single valid move (swapping '0' with an adjacent tile). This fits the [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Framework]].

### Problem Abstraction for BFS

1.  **Node/State:** A string representation of the 2x3 board. Serializing the 2D array into a 1D string (e.g., row by row) makes it hashable for the `visited` set and usable as keys in a queue if needed.
    - `[[r1c1, r1c2, r1c3], [r2c1, r2c2, r2c3]]` becomes `"r1c1r1c2r1c3r2c1r2c2r2c3"`.
2.  **`start_node`:** The string representation of the initial `board`.
3.  **`target_node`:** "123450".
4.  **`get_neighbors(board_str)`:**
    a. Find the index of '0' in `board_str`.
    b. Determine which 1D indices are adjacent to '0's 2D position. Labuladong uses a precomputed `neighbor_map` for this:
       ```
       # 2D board:
       # 0 1 2
       # 3 4 5
       # neighbor_map[i] = list of 1D indices adjacent to 1D index i
       neighbor_map = [
           [1, 3],    # Neighbors of index 0
           [0, 2, 4], # Neighbors of index 1
           [1, 5],    # Neighbors of index 2
           [0, 4],    # Neighbors of index 3
           [1, 3, 5], # Neighbors of index 4
           [2, 4]     # Neighbors of index 5
       ]
       ```
    c. For each valid adjacent index, create a new board string by swapping '0' with the tile at that adjacent index.
5.  **Invalid States:** None, other than those already visited.

### Standard BFS Solution (Python)

```python
from collections import deque

class Solution:
    def slidingPuzzle(self, board: list[list[int]]) -> int:
        # Target board string
        target_str = "123450"
        
        # Convert initial board to string
        start_str_list = []
        for r in range(2):
            for c in range(3):
                start_str_list.append(str(board[r][c]))
        start_str = "".join(start_str_list)

        if start_str == target_str:
            return 0

        # Precomputed neighbor mapping for 1D string indices
        # Index: 0  1  2
        #        3  4  5
        neighbor_map = [
            [1, 3],    # Neighbors of index 0
            [0, 2, 4], # Neighbors of index 1
            [1, 5],    # Neighbors of index 2
            [0, 4],    # Neighbors of index 3
            [1, 3, 5], # Neighbors of index 4
            [2, 4]     # Neighbors of index 5
        ]

        q = deque([(start_str, 0)]) # (board_string, steps)
        visited = {start_str}
        
        while q:
            current_board_str, steps = q.popleft()

            if current_board_str == target_str:
                return steps
            
            zero_idx = -1
            for i in range(len(current_board_str)):
                if current_board_str[i] == '0':
                    zero_idx = i
                    break
            
            # Generate neighbors by swapping '0'
            for neighbor_idx in neighbor_map[zero_idx]:
                new_board_list = list(current_board_str)
                new_board_list[zero_idx], new_board_list[neighbor_idx] = new_board_list[neighbor_idx], new_board_list[zero_idx]
                new_board_str = "".join(new_board_list)
                
                if new_board_str not in visited:
                    visited.add(new_board_str)
                    q.append((new_board_str, steps + 1))
                    
        return -1 # Target not reachable

# Example usage:
# sol = Solution()
# board1 = [[1,2,3],[4,0,5]]
# print(f"Board {board1}: Min moves = {sol.slidingPuzzle(board1)}") # Expected: 1

# board2 = [[4,1,2],[5,0,3]]
# print(f"Board {board2}: Min moves = {sol.slidingPuzzle(board2)}") # Expected: 5

# board3 = [[1,2,3],[5,4,0]] # Unsolvable based on parity, but BFS will explore until queue empty
# print(f"Board {board3}: Min moves = {sol.slidingPuzzle(board3)}") # Expected: -1
```
**Note on `steps` in BFS:** The provided solution `q.append((new_board_str, steps + 1))` directly associates the step count with the state in the queue. Labuladong's general BFS framework increments `steps` after processing each level. Both are valid ways to track steps. The general framework is:

```python
# General BFS framework step tracking
# q = deque([start_node])
# visited = {start_node}
# steps = 0
# while q:
#     level_size = len(q)
#     for _ in range(level_size):
#         curr = q.popleft()
#         if curr == target: return steps
#         for neighbor in get_neighbors(curr):
#             if neighbor not in visited:
#                 visited.add(neighbor)
#                 q.append(neighbor)
#     steps += 1
# return -1
```
The solution above integrates `steps` into the queue tuple `(state, steps_to_reach_state)`.

### Complexity
- **State Space:** The number of permutations of 6 items (0-5) is $6! = 720$. So, the number of states is relatively small.
- **Neighbors:** Max 4 neighbors (for '0' in a corner of 2x3, it's 2, edge is 3, center on longer side is 4).
- **Time:** $O(N \cdot k \cdot L)$, where $N$ is number of states (720), $k$ is max neighbors (4), $L$ is length of state string (6, for string ops like slicing/joining). Roughly constant time as state space is fixed and small. $O(6! \times \text{avg_degree} \times \text{string_len})$.
- **Space:** $O(N \cdot L)$ for `visited` set and `queue`. $O(6! \times \text{string_len})$.

## Bidirectional BFS Optimization

Since the target state ("123450") is known, bidirectional BFS can be applied.
The logic is similar to [[Interview/Practice/LeetCode/LC752 - Open the Lock|LC752 Open the Lock's bidirectional approach]]:
- Maintain two sets `q1` (frontier from start) and `q2` (frontier from target).
- In each step, expand the smaller frontier.
- If a state generated from `q1`'s expansion is found in `q2`, the path is found.

The Python solution would involve managing two sets and alternating expansions, similar to the `openLock` bidirectional solution.

## Visualization
The search explores states layer by layer:
- **Step 0:** `start_str`
- **Step 1:** Neighbors of `start_str`
- **Step 2:** Neighbors of Step 1 nodes, etc.
Until `target_str` is found.

```tikz
\begin{tikzpicture}[
    state/.style={draw, rectangle, rounded corners, font=\tiny\sffamily, minimum width=1.5cm, text centered},
    level_conn/.style={->, gray}
]
\node[state, fill=blue!20] (s0) at (0,0) {Initial Board Str};
\node[state] (s1_1) at (-3,-1.5) {Neighbor 1};
\node[state] (s1_2) at (0,-1.5) {Neighbor 2};
\node[state] (s1_3) at (3,-1.5) {Neighbor ...};

\draw[level_conn] (s0) -- (s1_1);
\draw[level_conn] (s0) -- (s1_2);
\draw[level_conn] (s0) -- (s1_3);

\node[state] (s2_1) at (-4,-3) {N of N1};
\node[state] (s2_2) at (-2,-3) {N of N1};
\node at (0,-3) {...};
\node[state,fill=green!30] (target) at (4,-4.5) {Target Board Str ("123450")};

\draw[level_conn] (s1_1) -- (s2_1);
\draw[level_conn] (s1_1) -- (s2_2);
\draw[level_conn, red, thick, dashed] (s1_3) -- (target); % Conceptual shortest path found

\node at (0,-5.5) [text width=8cm, align=center, draw, rounded corners, fill=yellow!10]
    {BFS explores states layer by layer. Path length = number of steps/levels. String representation of board state is key.};
\end{tikzpicture}
```

## 总结 (Summary)
- The Sliding Puzzle problem can be modeled as finding the shortest path in a state-space graph.
- BFS is suitable for finding this minimum number of moves.
- **Key steps in applying BFS:**
    1.  Represent board states uniquely (e.g., as strings).
    2.  Define how to generate neighboring states (valid moves of '0').
    3.  Use a `visited` set to avoid cycles and redundant work.
- The fixed small size of the board (2x3) keeps the state space manageable.
- Bidirectional BFS can offer a practical speedup.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Graph Traversal/BFS Framework (Shortest Path)|BFS Framework]], [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Graph BFS]]
Related Problems: [[Interview/Practice/LeetCode/LC752 - Open the Lock|LC752 - Open the Lock]]
"""
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created: {filepath}")


def update_algo_recursion_index(kb_root):
    filepath = os.path.join(kb_root, "Concept/Algorithms/Recursion/index.md")
    ensure_dir(filepath)
    
    content = r"""---
tags: [index, concept/algorithms, concept/recursion]
aliases: [Recursion Index]
---

# Recursion Concepts

This section covers core ideas and frameworks for understanding and applying recursion in algorithms.

## Core Concepts:
- [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursion - One Perspective, Two Thinking Modes]]
  - The Tree Perspective of Recursion
  - Decomposition Thinking Mode (Divide and Conquer)
  - Traversal Thinking Mode (Iterative Exploration / Backtracking)

## Related Algorithmic Paradigms:
- [[Interview/Concept/Algorithms/Backtracking/index|Backtracking]] (often implemented recursively)
- [[Interview/Concept/Algorithms/Divide and Conquer/index|Divide and Conquer]]
- [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]] (recursive solutions with memoization)
- [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal Algorithms]] (classic examples of recursion)

## Visualization
```mermaid
graph TD
    RecConcept["Recursion Concepts"] --> CoreRec["[[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|One Perspective, Two Modes]]"]
    CoreRec --> TreePersp["Tree Perspective"]
    CoreRec --> DecompMode["Decomposition Mode"]
    CoreRec --> TravMode["Traversal Mode"]

    RecConcept --> RelatedParadigm["Related Paradigms"]
    RelatedParadigm --> BacktrackL["[[Interview/Concept/Algorithms/Backtracking/index|Backtracking]]"]
    RelatedParadigm --> DCL["[[Interview/Concept/Algorithms/Divide and Conquer/index|Divide & Conquer]]"]
    RelatedParadigm --> DPL["[[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]]"]
    RelatedParadigm --> TTL["[[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal]]"]

    classDef main fill:#ffe6cc,stroke:#ff8000,stroke-width:2px;
    class RecConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
"""
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created/Updated: {filepath}")

def update_algorithms_main_index_for_recursion_bfs(kb_root):
    filepath = os.path.join(kb_root, "Concept/Algorithms/index.md")
    ensure_dir(filepath)
    
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            content = f.read()
    except FileNotFoundError:
        content = r"""---
tags: [index, concept/algorithms]
aliases: [Algorithms Index, Algorithm Concepts]
---
# Algorithm Concepts Index
This index covers various algorithm design paradigms...
## Algorithm Categories / Patterns
""" 

    new_links = [
        "- [[Interview/Concept/Algorithms/Recursion/index|Recursion Strategies]]"
        # BFS Framework is under Graph Traversal, so Graph Traversal link should exist.
    ]
    
    header_tag = "## Algorithm Categories / Patterns"
    if header_tag in content:
        parts = content.split(header_tag, 1)
        current_links_text = parts[1].split("## Visualization of Algorithm Areas", 1)[0]
        updated_links_text = current_links_text
        for link in new_links:
            if link.split("|")[0] not in updated_links_text: # Check if main link part exists
                 # Find where to insert the new link
                lines = updated_links_text.strip().splitlines()
                insert_idx = len(lines)
                for i, line_content in enumerate(lines):
                    if line_content.strip().startswith("- [[") :
                        insert_idx = i+1 # after last existing item
                    elif line_content.strip() and not line_content.strip().startswith("- [["):
                        insert_idx = i # before next non-list item
                        break
                lines.insert(insert_idx, link)
                updated_links_text = "\n".join(lines) + "\n"


        content = parts[0] + header_tag + "\n" + updated_links_text.strip() + "\n"
        if "## Visualization of Algorithm Areas" in parts[1]:
             content += "## Visualization of Algorithm Areas" + parts[1].split("## Visualization of Algorithm Areas", 1)[1]
    else: # Header not found
        content += "\n\n" + header_tag
        for link in new_links:
            content += "\n" + link

    # Update Mermaid diagram
    mermaid_header = "## Visualization of Algorithm Areas"
    if mermaid_header in content:
        mermaid_start_idx = content.find("```mermaid", content.find(mermaid_header))
        mermaid_end_idx = content.find("```", mermaid_start_idx + len("```mermaid"))
        if mermaid_start_idx != -1 and mermaid_end_idx != -1:
            mermaid_code = content[mermaid_start_idx + len("```mermaid") : mermaid_end_idx].strip()
            lines = mermaid_code.splitlines()
            
            # Add Recursion node if not exists
            rec_node_def = '    Algo --> Recursion["[[Interview/Concept/Algorithms/Recursion/index|Recursion]]"]'
            if 'Recursion["' not in mermaid_code:
                # Find a good spot, e.g., after GraphT or before DP
                inserted = False
                for i, line in enumerate(lines):
                    if 'GraphT["' in line:
                        lines.insert(i + 1, rec_node_def)
                        inserted = True
                        break
                if not inserted: # Append if specific point not found
                     # Insert before classDef if possible
                    class_def_line_idx = -1
                    for i, line in enumerate(lines):
                        if "classDef category" in line:
                            class_def_line_idx = i
                            break
                    if class_def_line_idx != -1:
                        lines.insert(class_def_line_idx, rec_node_def)
                    else: # append at the end of mermaid block lines
                        lines.append(rec_node_def)


            # Update classDef
            class_def_line_idx = -1
            for i, line in enumerate(lines):
                if "classDef category" in line:
                    class_def_line_idx = i
                    break
            
            if class_def_line_idx != -1:
                if "Recursion" not in lines[class_def_line_idx]:
                    lines[class_def_line_idx] = lines[class_def_line_idx].replace("category;", "Recursion, category;")
            else: # Add classDef if it's missing
                 lines.append("    classDef category fill:#e0ffff,stroke:#008b8b,stroke-width:2px;")
                 lines.append("    class Algo, Hashing, Sorting, Searching, TreeT, GraphT, Recursion, DP, Greedy, BitManip category;")


            mermaid_code = "\n".join(lines)
            content = content[:mermaid_start_idx + len("```mermaid")] + "\n" + mermaid_code + "\n" + content[mermaid_end_idx:]
            
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Updated main algorithms index for Recursion/BFS: {filepath}")

# --- Main Script ---
if __name__ == "__main__":
    kb_root_for_script = "./" 
    
    print(f"Knowledge base operations target root: {kb_root_for_script}")

    # Create/Update concept notes
    create_recursion_thinking_modes(kb_root_for_script)
    create_bfs_framework(kb_root_for_script) # This also includes BiBFS concepts

    # Create LeetCode problem notes as examples for BFS framework
    create_lc752_open_the_lock(kb_root_for_script)
    create_lc773_sliding_puzzle(kb_root_for_script)
        
    # Update/Create index files
    update_algo_recursion_index(kb_root_for_script)
    # The BFS Framework is part of Graph Traversal, so its index update is covered by previous runs
    # or by `update_graph_traversal_index` if it were called here.
    # Ensure the main LeetCode index links to these new problems
    lc_index_path = os.path.join(kb_root_for_script, "Practice/LeetCode/index.md")
    if os.path.exists(lc_index_path):
        with open(lc_index_path, "r", encoding="utf-8") as f:
            lc_content = f.read()
        
        new_lc_links = [
            "- [[Interview/Practice/LeetCode/LC752 - Open the Lock|LC752 - Open the Lock]]",
            "- [[Interview/Practice/LeetCode/LC773 - Sliding Puzzle|LC773 - Sliding Puzzle]]"
        ]
        
        # Add links under "Easy" or "Medium/Hard" - for now, add to a general area
        # A more robust update would parse existing categories.
        # Let's assume adding to "Easy" for demonstration, or a new "BFS Problems" category.
        # For simplicity, just check and append if not present.
        
        insert_header = "### Easy" # or another relevant header like "### Medium"
        if insert_header not in lc_content:
            # If no "Easy" header, append to end of problem list or create it
            lc_content += f"\n{insert_header}\n"

        parts = lc_content.split(insert_header, 1)
        prefix = parts[0] + insert_header + "\n"
        suffix = parts[1] if len(parts) > 1 else ""
        
        current_links_in_section = ""
        if suffix:
            next_header_idx = suffix.find("\n###")
            if next_header_idx != -1:
                current_links_in_section = suffix[:next_header_idx]
                suffix = suffix[next_header_idx:]
            else:
                current_links_in_section = suffix
                suffix = ""
        
        for link in new_lc_links:
            if link.split("|")[0] not in current_links_in_section:
                current_links_in_section += link + "\n"
        
        lc_content = prefix + current_links_in_section.strip() + "\n" + suffix.strip()


        # Update Mermaid for LC index
        mermaid_lc_header = "## Visualization of Practice Areas"
        if mermaid_lc_header in lc_content:
            mermaid_lc_start = lc_content.find("```mermaid", lc_content.find(mermaid_lc_header))
            mermaid_lc_end = lc_content.find("```", mermaid_lc_start + len("```mermaid"))
            if mermaid_lc_start != -1 and mermaid_lc_end != -1:
                mermaid_lc_code = lc_content[mermaid_lc_start + len("```mermaid") : mermaid_lc_end].strip()
                lines_lc = mermaid_lc_code.splitlines()
                
                # Add LC752, LC773 nodes and links
                lc752_node = '    Easy --> LC752["[[Interview/Practice/LeetCode/LC752 - Open the Lock|LC752 - Open the Lock]]"]'
                lc773_node = '    Easy --> LC773["[[Interview/Practice/LeetCode/LC773 - Sliding Puzzle|LC773 - Sliding Puzzle]]"]'
                # (assuming they are easy/medium, adjust category if needed)
                
                # Links to categories
                lc752_cat_link = '    LC752 --> CatGraphBFS' # New category for Graph/BFS
                lc773_cat_link = '    LC773 --> CatGraphBFS'
                cat_graph_bfs_def = '        CatGraphBFS["Graph/BFS"]'


                if 'LC752["' not in mermaid_lc_code: lines_lc.insert(lines_lc.index('    Easy --> LC2073["[[Interview/Practice/LeetCode/LC2073 - Time Needed to Buy Tickets|LC2073 - Time to Buy Tickets]]"]') +1 , lc752_node)
                if 'LC773["' not in mermaid_lc_code: lines_lc.insert(lines_lc.index(lc752_node) +1 , lc773_node)
                
                subgraph_end_idx = -1
                for i, line in enumerate(lines_lc):
                    if "end" in line and "subgraph" in lines_lc[i-1]: # find end of subgraph
                        subgraph_end_idx = i
                        break
                if subgraph_end_idx != -1:
                    if cat_graph_bfs_def not in mermaid_lc_code : lines_lc.insert(subgraph_end_idx, cat_graph_bfs_def)
                else: # if no subgraph, append
                    if cat_graph_bfs_def not in mermaid_lc_code : lines_lc.append(cat_graph_bfs_def)

                if lc752_cat_link not in mermaid_lc_code: lines_lc.append(lc752_cat_link)
                if lc773_cat_link not in mermaid_lc_code: lines_lc.append(lc773_cat_link)

                mermaid_lc_code = "\n".join(lines_lc)
                lc_content = lc_content[:mermaid_lc_start + len("```mermaid")] + "\n" + mermaid_lc_code + "\n" + lc_content[mermaid_lc_end:]

        with open(lc_index_path, "w", encoding="utf-8") as f:
            f.write(lc_content)
        print(f"Updated LeetCode index: {lc_index_path}")

    update_algorithms_main_index_for_recursion_bfs(kb_root_for_script)
    
    print(f"Script finished. Recursion, BFS Framework, and related LeetCode example notes created/updated in '{kb_root_for_script}'.")

