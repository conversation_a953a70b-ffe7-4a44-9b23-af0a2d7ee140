
import os
import textwrap

# Helper function to create directories if they don't exist
def ensure_dir(filepath):
    directory = os.path.dirname(filepath)
    if not os.path.exists(directory):
        os.makedirs(directory)
    print(f"Ensured directory: {directory}")

# --- Content Generation Functions ---

def create_dp_introduction_framework(kb_root):
    filepath = os.path.join(kb_root, "Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework.md")
    ensure_dir(filepath)
    content = r"""---
tags: [concept/algorithms, concept/dynamic_programming, type/framework, pattern/recursion, pattern/memoization, pattern/tabulation]
aliases: [DP Framework, Dynamic Programming Basics, 动态规划框架]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/动态规划解题套路框架.md]].
> Labuladong emphasizes that DP is essentially optimized brute-force search (窮举) through the identification of optimal substructure and overlapping subproblems.

# Dynamic Programming: Introduction and Framework

Dynamic Programming (DP) is a powerful algorithmic technique for solving optimization problems, typically those asking for a "maximum" or "minimum" value. At its core, DP is about intelligently exploring all possible solutions (brute-force or "穷举") and finding the best one.

## 🎯 What is Dynamic Programming?

DP problems usually involve finding an optimal solution (e.g., minimum cost, maximum profit, longest sequence). The key idea is to break down a complex problem into simpler, overlapping subproblems, solve each subproblem only once, and store their solutions to avoid redundant computations.

**Labuladong's Core Idea:** "Dynamic programming is essentially brute-force search with clever optimizations."

### The Three Pillars of Dynamic Programming:

1.  **Optimal Substructure:** An optimal solution to the overall problem can be constructed from optimal solutions to its subproblems. This means if you solve the subproblems optimally, you can combine these solutions to get the overall optimal solution.
    - *Example (Coin Change):* The minimum coins to make amount `A` can be found by taking one coin `c` and then finding the minimum coins for `A-c`. The subproblem `min_coins(A-c)` must be solved optimally.
    - Subproblems must be independent.

2.  **Overlapping Subproblems:** The recursive solution to the problem involves solving the same subproblems multiple times.
    - *Example (Fibonacci):* `fib(5)` calls `fib(4)` and `fib(3)`. `fib(4)` also calls `fib(3)`. `fib(3)` is an overlapping subproblem.
    - DP addresses this by storing the results of subproblems (memoization or tabulation) so they are computed only once.

3.  **State Transition Equation (状态转移方程):** A mathematical recurrence relation that defines how the solution to a problem (or state) can be derived from solutions to its subproblems (or previous/smaller states). This is the heart of a DP solution.

## 💡 Labuladong's Thinking Framework for DP

To derive the state transition equation and solve a DP problem, Labuladong suggests a three-step thinking process:

1.  **Clearly Define "States" (明确「状态」):**
    - Identify the variables that change as the problem is broken down into subproblems. These variables define the parameters of your `dp` function or the dimensions of your `dp` table.
    - *Example (Fibonacci):* The state is `n` (the n-th Fibonacci number).
    - *Example (Coin Change):* The state is `amount` (the target sum).

2.  **Identify "Choices" (明确「选择」):**
    - For each state, what are the decisions or actions you can take that lead to a transition to other states (subproblems)?
    - *Example (Coin Change):* For a given `amount`, the choices are which coin `c` from `coins` to use next. This leads to the subproblem of making `amount - c`.

3.  **Define the `dp` function/array's meaning (定义 `dp` 数组/函数的含义):**
    - Specify precisely what `dp(state_variables...)` computes or what `dp_table[state_indices...]` stores.
    - This definition is crucial for correctly formulating the recursive calls or iterative updates.
    - *Example (Fibonacci):* `dp(n)` returns the n-th Fibonacci number.
    - *Example (Coin Change):* `dp(amount)` returns the minimum number of coins to make up `amount`.

## 🛠️ Two Main Implementation Approaches

Once the state transition equation is formulated, DP problems can be solved using two general approaches:

### 1. Top-Down with Memoization (自顶向下带备忘录的递归)
   - This is a direct translation of the recursive state transition equation.
   - A "memo" (e.g., array or hash map) is used to store the results of already computed subproblems.
   - Before computing a subproblem, check the memo. If the result exists, return it. Otherwise, compute, store in memo, and then return.

   **General Structure (Recursive with Memoization):**
   ```python
   # memo = {} # or array initialized with a special value

   # def dp(state1, state2, ...):
   #     if (state1, state2, ...) in memo:
   #         return memo[(state1, state2, ...)]
       
   #     if base_case(state1, state2, ...):
   #         return base_case_value
       
   #     result = initial_value_for_optimization (e.g., infinity for min, -infinity for max)
   #     for choice in all_possible_choices_for_current_state:
   #         # Transition to new_state based on choice
   #         sub_problem_result = dp(new_state1, new_state2, ...)
   #         # Combine/update result
   #         result = optimize(result, combine(choice_effect, sub_problem_result))
           
   #     memo[(state1, state2, ...)] = result
   #     return result
   ```

### 2. Bottom-Up with Tabulation (自底向上 DP table迭代)
   - This involves building a `dp` table iteratively, starting from the base cases and progressively computing solutions for larger subproblems.
   - The `dp` table dimensions correspond to the states.
   - The iteration order must ensure that when `dp[state_indices...]` is computed, the values of subproblems it depends on (e.g., `dp[smaller_state_indices...]`) have already been computed.

   **General Structure (Iterative with DP Table):**
   ```python
   # dp_table = initialize_table_with_base_cases_or_default_values(...)

   # # Iterate through all states
   # for state1 in all_values_of_state1:
   #     for state2 in all_values_of_state2:
   #         # ...
   #         for choice in all_possible_choices_for_current_state:
   #             # Update dp_table[state1][state2]... based on previous states and choice
   #             dp_table[state1][state2]... = optimize(dp_table[state1][state2]..., 
   #                                                   combine(choice_effect, dp_table[prev_state1]...))
   # return dp_table[final_state_indices...]
   ```
Both approaches solve the same subproblems and generally have the same time complexity after optimization. The choice often comes down to conceptual clarity or ease of implementation for a specific problem.

## Example 1: Fibonacci Number (LeetCode 509)
[[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 - Fibonacci Number]]
- **Strictly speaking, not a DP problem** as it doesn't involve求最值 (optimization). However, it perfectly illustrates **overlapping subproblems** and the two DP solution approaches.
- **State:** `n` (the index of the Fibonacci number).
- **Choices:** Not applicable in the typical DP "choice" sense; the recurrence is fixed.
- **`dp(n)` definition:** Returns the n-th Fibonacci number.
- **State Transition:** $fib(n) = fib(n-1) + fib(n-2)$.
- **Base Cases:** $fib(0)=0, fib(1)=1$.

Labuladong's article shows:
1.  **Brute-force recursion:** $O(2^N)$ time due to recomputing subproblems. (Visualized in `div_mydata-fib`)
2.  **Top-down with memoization:** $O(N)$ time, $O(N)$ space for memo and recursion stack. (Visualized in `div_mydata-fib2`)
3.  **Bottom-up with DP table:** $O(N)$ time, $O(N)$ space for DP table. (Visualized in `div_mydata-fib3`)
4.  **Bottom-up with space optimization:** $O(N)$ time, $O(1)$ space (only need last two values). (Visualized in `div_fibonacci-number`)

## Example 2: Coin Change (LeetCode 322)
[[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 - Coin Change]]
Minimum coins to make amount `A` using `coins` array.
- **State:** `amount` (the current amount to make change for).
- **Choices:** For each `coin` in `coins`, choose to use it (if `amount >= coin`).
- **`dp(n)` definition:** Minimum coins to make amount `n`.
- **State Transition:** $dp(n) = \min_{c \in coins, n \ge c} \{1 + dp(n-c)\}$
- **Base Cases:** $dp(0)=0$. $dp(k) = \infty$ (or -1) if $k < 0$.

Labuladong's article shows:
1.  **Brute-force recursion:** Exponential time. (Visualized in `div_coin-change-brute-force`)
2.  **Top-down with memoization:** $O(A \cdot C)$ time (Amount * NumCoins), $O(A)$ space. (Visualized in `div_coin-change`)
3.  **Bottom-up with DP table:** $O(A \cdot C)$ time, $O(A)$ space.

## 总结 (Summary)
- Dynamic Programming solves optimization problems by breaking them into overlapping subproblems with optimal substructure.
- **Core Framework:**
    1.  Define **States**.
    2.  Identify **Choices** for each state.
    3.  Define the **`dp` function/array meaning**.
    4.  Formulate the **State Transition Equation**.
- **Implementation Methods:**
    - **Top-Down (Memoized Recursion):** Natural translation of recurrence.
    - **Bottom-Up (Tabulation):** Iterative filling of DP table.
- Key to DP is first writing the (often exponential) brute-force recursive solution based on the state transition, then optimizing it with memoization or by converting to tabulation.

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
Previous: [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer Framework]]
Next: [[Interview/Concept/Algorithms/Dynamic Programming/01 - DP - Optimal Substructure|DP - Optimal Substructure]] (Placeholder for more detailed pillar explanations)
Related Problems: [[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 - Fibonacci Number]], [[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 - Coin Change]]
Further Reading: [[Interview/Concept/Algorithms/Dynamic Programming/Memoization|Memoization]], [[Interview/Concept/Algorithms/Dynamic Programming/Tabulation|Tabulation]]
"""
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created: {filepath}")


def create_dp_index_file(kb_root):
    filepath = os.path.join(kb_root, "Concept/Algorithms/Dynamic Programming/index.md")
    ensure_dir(filepath)
    content = r"""---
tags: [index, concept/algorithms, concept/dynamic_programming]
aliases: [Dynamic Programming Index, DP Index]
---

# Dynamic Programming (DP)

This section covers the principles, techniques, and problem-solving frameworks for Dynamic Programming.

## Core Concepts & Framework:
- [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming - Introduction and Framework]]
  - Three Pillars: Optimal Substructure, Overlapping Subproblems, State Transition Equation
  - Labuladong's Thinking Framework: States, Choices, DP Definition
  - Top-Down (Memoization) vs. Bottom-Up (Tabulation)
- `[[Interview/Concept/Algorithms/Dynamic Programming/01 - DP - Optimal Substructure|DP - Optimal Substructure]]` (Placeholder)
- `[[Interview/Concept/Algorithms/Dynamic Programming/02 - DP - Overlapping Subproblems|DP - Overlapping Subproblems]]` (Placeholder)
- [[Interview/Concept/Algorithms/Dynamic Programming/Memoization|Memoization (Top-Down DP)]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Tabulation|Tabulation (Bottom-Up DP)]]
- `[[Interview/Concept/Algorithms/Dynamic Programming/05 - DP - Space Optimization|DP - Space Optimization Techniques]]` (Placeholder)

## Problem Patterns & Examples:
- **Sequence DP:**
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/Longest Increasing Subsequence|Longest Increasing Subsequence (LIS)]]` (Placeholder)
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/Longest Common Subsequence|Longest Common Subsequence (LCS)]]` (Placeholder)
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/Edit Distance|Edit Distance]]` (Placeholder)
- **Knapsack Problems:**
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Knapsack/0-1 Knapsack|0/1 Knapsack]]` (Placeholder)
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Knapsack/Unbounded Knapsack|Unbounded Knapsack (Complete Knapsack)]]` (Placeholder)
- **Interval DP / Game DP:**
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Interval DP/Burst Balloons|Burst Balloons (戳气球)]]` (Placeholder)
- **Pathfinding on Grids:**
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Grid DP/Unique Paths|Unique Paths]]` (Placeholder)

## LeetCode Examples Discussed:
- [[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 - Fibonacci Number]] (Illustrates overlapping subproblems & DP approaches)
- [[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 - Coin Change]] (Classic DP optimization problem)

## Visualization
```mermaid
graph TD
    DPConcept["Dynamic Programming"] --> IntroFramework["[[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Introduction & Framework]]"]
    IntroFramework --> Pillars["(Optimal Substructure, Overlapping Subproblems, State Transition)"]
    IntroFramework --> Approaches["(Memoization vs Tabulation)"]

    DPConcept --> Techniques["Core Techniques"]
    Techniques --> Memo["[[Interview/Concept/Algorithms/Dynamic Programming/Memoization|Memoization]]"]
    Techniques --> Tab["[[Interview/Concept/Algorithms/Dynamic Programming/Tabulation|Tabulation]]"]
    Techniques --> SpaceOpt["(Space Optimization)"]

    DPConcept --> Patterns["Problem Patterns"]
    Patterns --> SeqDP["Sequence DP (LIS, LCS, Edit Distance)"]
    Patterns --> KnapsackDP["Knapsack (0/1, Unbounded)"]
    Patterns --> IntervalDP["Interval/Game DP"]
    Patterns --> GridDP["Grid DP (Unique Paths)"]

    DPConcept --> ExamplesLC["LeetCode Examples"]
    ExamplesLC --> LC509["[[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 Fibonacci]]"]
    ExamplesLC --> LC322["[[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 Coin Change]]"]
    
    classDef main fill:#e6ccff,stroke:#9933ff,stroke-width:2px;
    class DPConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
"""
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created: {filepath}")

def create_lc509_fibonacci(kb_root):
    filepath = os.path.join(kb_root, "Practice/LeetCode/LC509 - Fibonacci Number.md")
    ensure_dir(filepath)
    content = r"""---
tags: [problem/leetcode, lc/easy, topic/dynamic_programming, topic/recursion, pattern/memoization, pattern/tabulation]
aliases: [LC509, LeetCode 509, Fibonacci Number]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 509. Fibonacci Number
> Solution approaches (brute-force, memoization, tabulation, space-optimized tabulation) adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/动态规划解题套路框架.md]].

# LeetCode 509: Fibonacci Number

## Problem Statement

The Fibonacci numbers, commonly denoted `F(n)`, form a sequence, called the Fibonacci sequence, such that each number is the sum of the two preceding ones, starting from 0 and 1. That is:
- `F(0) = 0`
- `F(1) = 1`
- `F(N) = F(N - 1) + F(N - 2)`, for `N > 1`.

Given `n`, calculate `F(n)`.

**Official Link:** [LeetCode 509. Fibonacci Number](https://leetcode.com/problems/fibonacci-number/)

**Example 1:**
Input: `n = 2`
Output: `1`
Explanation: `F(2) = F(1) + F(0) = 1 + 0 = 1`.

## Solution Approaches (Python)

This problem is a classic introduction to dynamic programming concepts, primarily illustrating **overlapping subproblems** and how to optimize them. While not strictly an "optimization" problem (no min/max), it demonstrates DP techniques. See [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|DP Introduction and Framework]].

### 1. Brute-Force Recursion
Directly translate the mathematical recurrence.
```python
class Solution:
    def fib_recursive(self, n: int) -> int:
        if n == 0:
            return 0
        if n == 1:
            return 1
        return self.fib_recursive(n - 1) + self.fib_recursive(n - 2)
```
- **Time Complexity:** $O(2^N)$. The recursion tree has $2^N$ nodes in the worst case, and each node does $O(1)$ work. This is highly inefficient due to recomputing the same Fibonacci numbers multiple times (overlapping subproblems).
- **Space Complexity:** $O(N)$ due to recursion stack depth.
- Labuladong's visualizer `div_mydata-fib` shows this exponential tree.

### 2. Top-Down with Memoization (Recursive DP)
Store results of subproblems to avoid recomputation.
```python
class Solution:
    def fib_memoization(self, n: int) -> int:
        memo = {} # Using a dictionary for memoization
        # Alternatively, an array `memo = [-1] * (n + 1)` could be used if n is not too large.
        
        def dp(k: int) -> int:
            if k == 0: return 0
            if k == 1: return 1
            if k in memo:
                return memo[k]
            
            memo[k] = dp(k - 1) + dp(k - 2)
            return memo[k]
            
        return dp(n)
```
- **Time Complexity:** $O(N)$. Each Fibonacci number `F(i)` for `i` from 0 to `n` is computed only once.
- **Space Complexity:** $O(N)$ for the `memo` dictionary/array and $O(N)$ for the recursion stack.
- Labuladong's visualizer `div_mydata-fib2` shows the "pruned" recursion tree.

### 3. Bottom-Up with Tabulation (Iterative DP)
Build up solutions from base cases.
```python
class Solution:
    def fib_tabulation(self, n: int) -> int:
        if n == 0: return 0
        if n == 1: return 1
        
        dp_table = [0] * (n + 1) # dp_table[i] will store F(i)
        dp_table[0] = 0
        dp_table[1] = 1
        
        for i in range(2, n + 1):
            dp_table[i] = dp_table[i-1] + dp_table[i-2]
            
        return dp_table[n]
```
- **Time Complexity:** $O(N)$. A single loop iterates `n` times.
- **Space Complexity:** $O(N)$ for the `dp_table`.
- Labuladong's visualizer `div_mydata-fib3` shows the filling of the `dp_table`.

### 4. Bottom-Up with Space Optimization
Notice that `F(n)` only depends on `F(n-1)` and `F(n-2)`. We only need to store the last two values.
```python
class Solution:
    def fib(self, n: int) -> int: # This is the final optimized version
        if n == 0: return 0
        if n == 1: return 1
        
        prev2 = 0  # Represents F(i-2)
        prev1 = 1  # Represents F(i-1)
        current = 0 # Represents F(i)
        
        for _ in range(2, n + 1): # Loop from i=2 up to n
            current = prev1 + prev2
            prev2 = prev1
            prev1 = current
            
        return current # Which is F(n)
```
- **Time Complexity:** $O(N)$.
- **Space Complexity:** $O(1)$. Only constant extra space for `prev1`, `prev2`, `current`.
- Labuladong's visualizer `div_fibonacci-number` refers to this optimized approach.

## Visualization Summary
- **Brute-Force Recursion:** Exponentially large tree with many repeated sub-computations.
    ![](/algo/images/dynamic-programming/1.jpg)
- **Memoized Recursion:** "Prunes" the recursion tree; computes each unique subproblem once.
    ![](/algo/images/dynamic-programming/3.jpg) (Conceptual graph of computed subproblems)
- **Tabulation:** Iteratively fills a DP table, equivalent to the memoized results but bottom-up.
    ![](/algo/images/dynamic-programming/4.jpg) (DP table filling, analogous to pruned tree)

## 总结 (Summary)
- The Fibonacci sequence problem is a classic example to demonstrate dynamic programming concepts, especially handling **overlapping subproblems**.
- **Brute-force recursion** is simple but inefficient ($O(2^N)$).
- **Memoization (Top-Down DP)** optimizes recursion by storing subproblem results, achieving $O(N)$ time and $O(N)$ space.
- **Tabulation (Bottom-Up DP)** iteratively builds solutions, also $O(N)$ time and $O(N)$ space.
- **Space-Optimized Tabulation** can reduce space to $O(1)$ for Fibonacci by only keeping track of the necessary previous two states.
- This problem helps build intuition for identifying and optimizing recursive solutions that involve re-computing the same work.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|DP Introduction and Framework]], [[Interview/Concept/Algorithms/Dynamic Programming/Memoization|Memoization]], [[Interview/Concept/Algorithms/Dynamic Programming/Tabulation|Tabulation]]
Related Problems: Many DP problems build on these foundational optimization ideas.
"""
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created: {filepath}")

def create_lc322_coin_change(kb_root):
    filepath = os.path.join(kb_root, "Practice/LeetCode/LC322 - Coin Change.md")
    ensure_dir(filepath)
    content = r"""---
tags: [problem/leetcode, lc/medium, topic/dynamic_programming, pattern/knapsack, pattern/memoization, pattern/tabulation]
aliases: [LC322, LeetCode 322, Coin Change, 零钱兑换]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 322. Coin Change
> Solution approaches (brute-force, memoization, tabulation) adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/动态规划解题套路框架.md]].

# LeetCode 322: Coin Change

## Problem Statement

You are given an integer array `coins` representing coins of different denominations and an integer `amount` representing a total amount of money.
Return *the fewest number of coins that you need to make up that amount*. If that amount of money cannot be made up by any combination of the coins, return `-1`.
You may assume that you have an infinite number of each kind of coin.

**Official Link:** [LeetCode 322. Coin Change](https://leetcode.com/problems/coin-change/)

**Example 1:**
Input: `coins = [1,2,5]`, `amount = 11`
Output: `3`
Explanation: `11 = 5 + 5 + 1`

## Solution Approach: Dynamic Programming

This is a classic optimization problem ("fewest number") suitable for dynamic programming. We follow Labuladong's DP framework from [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|DP Introduction and Framework]].

### DP Framework Application

1.  **Define States:** The primary state that changes is the `amount` we are trying to make change for. So, `dp(n)` will represent something about amount `n`.
2.  **Identify Choices:** For a given `amount`, the choices are which coin `c` from the `coins` array to use. If we use coin `c`, the remaining amount to make change for becomes `amount - c`.
3.  **Define `dp` function/array meaning:** `dp(n)` = the fewest number of coins needed to make up amount `n`.
4.  **Formulate State Transition Equation:**
    To compute `dp(n)`, we consider each coin `c` in `coins`:
    If we use coin `c`, we need `1 + dp(n-c)` coins in total.
    We want the minimum among all choices of `c`.
    So, $dp(n) = \min_{c \in \text{coins, } n \ge c} \{1 + dp(n-c)\}$.
5.  **Base Cases:**
    - $dp(0) = 0$ (0 coins needed to make amount 0).
    - $dp(k) = -1$ or $\infty$ (if $k < 0$, representing an invalid state or impossibility). We use $\infty$ for `min` calculations and convert to -1 for final output if still $\infty$.

### 1. Brute-Force Recursion (Top-Down)
This directly implements the recurrence.
```python
class Solution:
    def coinChange_recursive(self, coins: list[int], amount: int) -> int:
        
        def dp(n: int) -> int:
            # Base cases
            if n == 0: return 0
            if n < 0: return float('inf') # Use infinity for min() to work, effectively -1

            min_coins = float('inf')
            for coin in coins:
                sub_problem_result = dp(n - coin)
                # If sub_problem_result is inf, it means n-coin is not makeable
                # Adding 1 to inf is still inf.
                min_coins = min(min_coins, 1 + sub_problem_result)
            
            return min_coins

        result = dp(amount)
        return result if result != float('inf') else -1

# This will TLE (Time Limit Exceeded) due to recomputing overlapping subproblems.
# Example: coins = [1,2,5], amount = 11.
# dp(11) depends on dp(10), dp(9), dp(6)
# dp(10) depends on dp(9), dp(8), dp(5)
# dp(9) is computed multiple times.
```
- **Complexity:** Exponential, $O(C^A)$ where $C$ is number of coins, $A$ is amount, due to overlapping subproblems.
- Labuladong's visualizer `div_coin-change-brute-force` would show this large tree.

### 2. Top-Down with Memoization (Recursive DP)
Store results of `dp(n)` to avoid recomputation.
```python
class Solution:
    def coinChange_memoization(self, coins: list[int], amount: int) -> int:
        memo = {} # Using a dictionary for memoization

        def dp(n: int) -> int:
            if n == 0: return 0
            if n < 0: return float('inf') # Represents impossibility for min calculation
            
            if n in memo:
                return memo[n]

            min_coins = float('inf')
            for coin in coins:
                sub_problem_result = dp(n - coin)
                # No need to check if sub_problem_result is inf before adding 1,
                # 1 + inf is still inf. min() will handle it.
                min_coins = min(min_coins, 1 + sub_problem_result)
            
            memo[n] = min_coins
            return min_coins

        result = dp(amount)
        return result if result != float('inf') else -1
```
- **Time Complexity:** $O(A \cdot C)$, where $A$ is `amount` and $C$ is `len(coins)`. Each state `dp(i)` from `0` to `A` is computed once. Each computation involves a loop of $C$ coins.
- **Space Complexity:** $O(A)$ for the `memo` and $O(A)$ for recursion stack depth in worst case (e.g., using only coin '1').
- Labuladong's visualizer `div_coin-change` shows the effect of memoization.

### 3. Bottom-Up with Tabulation (Iterative DP)
Build a `dp_table` from base cases up to `amount`.
`dp_table[i]` = fewest coins to make amount `i`.
```python
class Solution:
    def coinChange(self, coins: list[int], amount: int) -> int: # Final DP solution
        # dp_table[i] will store the minimum coins to make amount i
        # Initialize with a value larger than any possible coin count (e.g., amount + 1)
        # amount + 1 is a good "infinity" because max coins for amount A is A (using all 1s)
        dp_table = [amount + 1] * (amount + 1)
        
        # Base case
        dp_table[0] = 0
        
        # Iterate for each amount from 1 to `amount`
        for i in range(1, amount + 1):
            # For each coin, see if it can improve dp_table[i]
            for coin in coins:
                if i - coin >= 0: # If current amount `i` can accommodate `coin`
                    # dp_table[i - coin] is min coins for amount `i-coin`
                    # Add 1 for the current `coin`
                    dp_table[i] = min(dp_table[i], 1 + dp_table[i - coin])
        
        # If dp_table[amount] is still amount + 1, it means amount cannot be made
        return dp_table[amount] if dp_table[amount] != amount + 1 else -1

```
- **Time Complexity:** $O(A \cdot C)$. Outer loop runs $A$ times, inner loop $C$ times.
- **Space Complexity:** $O(A)$ for `dp_table`.

## Visualization
Labuladong's image for the DP table calculation process:
![](/algo/images/dynamic-programming/6.jpg)
This shows how `dp[i]` is derived from `dp[i-coin]`. For `dp_table[i]`, you're essentially trying each `coin` as the *last coin* used to reach `i`, and taking the best option.

## 总结 (Summary)
- The Coin Change problem asks for the minimum number of coins to make a target amount, a classic DP optimization problem.
- **State:** `amount` to be made.
- **Choices:** Which `coin` to use next.
- **DP Definition:** `dp(n)` = min coins for amount `n`.
- **State Transition:** $dp(n) = \min(1 + dp(n-c))$ for all $c \in coins$.
- Solvable with brute-force recursion (inefficient), memoized recursion ($O(AC)$ time, $O(A)$ space), or tabulation ($O(AC)$ time, $O(A)$ space).
- The tabulation approach is often preferred for its explicitness and avoidance of recursion depth issues.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|DP Introduction and Framework]], [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Knapsack/Unbounded Knapsack|Unbounded Knapsack]] (as this is a form of it)
Related Problems: [[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 - Fibonacci Number]]
"""
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created: {filepath}")


def update_main_algorithms_index_for_dp(kb_root):
    filepath = os.path.join(kb_root, "Concept/Algorithms/index.md")
    ensure_dir(filepath)
    
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            content = f.read()
    except FileNotFoundError:
        content = r"""---
tags: [index, concept/algorithms]
aliases: [Algorithms Index, Algorithm Concepts]
---
# Algorithm Concepts Index
This index covers various algorithm design paradigms...
## Algorithm Categories / Patterns
""" 

    new_link_dp = "- [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]]"
    
    header_tag = "## Algorithm Categories / Patterns"
    if header_tag in content:
        parts = content.split(header_tag, 1)
        current_links_text = parts[1].split("## Visualization of Algorithm Areas", 1)[0] # Text before Mermaid
        updated_links_text = current_links_text
        if new_link_dp.split("|")[0] not in updated_links_text:
            lines = updated_links_text.strip().splitlines()
            insert_idx = 0
            # Find a good spot, e.g., before Greedy or after D&C
            for i, line_content in enumerate(lines):
                if "Divide and Conquer" in line_content: # Insert after D&C
                    insert_idx = i + 1
                    break
                elif "Greedy Algorithms" in line_content and insert_idx == 0: # Or before Greedy
                    insert_idx = i
                    break
                if line_content.strip().startswith("- [["): # Keep track of last list item
                    insert_idx = i + 1
            
            lines.insert(insert_idx, new_link_dp)
            updated_links_text = "\n".join(lines) + "\n"
        
        content = parts[0] + header_tag + "\n" + updated_links_text.strip() + "\n"
        if "## Visualization of Algorithm Areas" in parts[1]:
             content += "## Visualization of Algorithm Areas" + parts[1].split("## Visualization of Algorithm Areas", 1)[1]

    else: # Header not found
        content += "\n\n" + header_tag + "\n" + new_link_dp


    # Update Mermaid diagram
    mermaid_header = "## Visualization of Algorithm Areas"
    if mermaid_header in content:
        mermaid_start_idx = content.find("```mermaid", content.find(mermaid_header))
        mermaid_end_idx = content.find("```", mermaid_start_idx + len("```mermaid"))
        if mermaid_start_idx != -1 and mermaid_end_idx != -1:
            mermaid_code = content[mermaid_start_idx + len("```mermaid") : mermaid_end_idx].strip()
            lines = mermaid_code.splitlines()
            
            dp_node_def = '    Algo --> DP["[[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]]"]'
            dp_links_to_examples = [
                '    DP --> FibLC["[[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 Fibonacci]]"]',
                '    DP --> CoinLC["[[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 CoinChange]]"]'
            ]

            if 'DP["' not in mermaid_code: # Add DP node
                inserted_dp_node = False
                for i, line in enumerate(lines):
                    if 'DnC["' in line: # After D&C
                        lines.insert(i + 1, dp_node_def)
                        inserted_dp_node = True
                        break
                if not inserted_dp_node: # Fallback
                    class_def_line_idx = -1
                    for i, line in enumerate(lines):
                        if "classDef category" in line: class_def_line_idx = i; break
                    if class_def_line_idx != -1: lines.insert(class_def_line_idx, dp_node_def)
                    else: lines.append(dp_node_def)

            # Add links from DP to examples
            for link in dp_links_to_examples:
                if link.split("-->")[1].split("[")[0].strip() not in mermaid_code: # check if specific link part is missing
                    lines.append(link)


            class_def_line_idx = -1
            for i, line in enumerate(lines):
                if "classDef category" in line: class_def_line_idx = i; break
            if class_def_line_idx != -1 and "DP" not in lines[class_def_line_idx]:
                lines[class_def_line_idx] = lines[class_def_line_idx].replace("DnC", "DP, DnC") # Assuming DnC was added before
            
            mermaid_code = "\n".join(lines)
            content = content[:mermaid_start_idx + len("```mermaid")] + "\n" + mermaid_code + "\n" + content[mermaid_end_idx:]
            
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Updated main algorithms index for Dynamic Programming: {filepath}")


# --- Main Script ---
if __name__ == "__main__":
    kb_root_for_script = "./" 
    
    print(f"Knowledge base operations target root: {kb_root_for_script}")

    # Create DP Introduction and Framework note
    create_dp_introduction_framework(kb_root_for_script)

    # Create LeetCode problem notes as examples for DP framework
    create_lc509_fibonacci(kb_root_for_script)
    create_lc322_coin_change(kb_root_for_script)
        
    # Create/Update DP index file
    create_dp_index_file(kb_root_for_script)
    
    # Update main Algorithms index to include DP
    update_main_algorithms_index_for_dp(kb_root_for_script)

    # Update main LeetCode index to link these new problems
    lc_index_path = os.path.join(kb_root_for_script, "Practice/LeetCode/index.md")
    if os.path.exists(lc_index_path):
        with open(lc_index_path, "r", encoding="utf-8") as f:
            lc_content = f.read()
        
        new_lc_dp_links = [
            "- [[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 - Fibonacci Number]]",
            "- [[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 - Coin Change]]"
        ]
        
        # Attempt to add under "Easy" or "Medium" categories, or just append
        easy_header = "### Easy"
        medium_header = "### Medium"
        target_header = easy_header # Default to Easy for LC509
        
        # Add LC509 to Easy
        if target_header in lc_content:
            if new_lc_dp_links[0].split("|")[0] not in lc_content:
                 lc_content = lc_content.replace(target_header, target_header + "\n" + new_lc_dp_links[0])
        elif new_lc_dp_links[0].split("|")[0] not in lc_content: # If Easy header not found, append with header
            lc_content += f"\n{target_header}\n{new_lc_dp_links[0]}\n"

        # Add LC322 to Medium
        target_header = medium_header
        if target_header in lc_content:
            if new_lc_dp_links[1].split("|")[0] not in lc_content:
                lc_content = lc_content.replace(target_header, target_header + "\n" + new_lc_dp_links[1])
        elif new_lc_dp_links[1].split("|")[0] not in lc_content: # If Medium header not found, append with header
             lc_content += f"\n{target_header}\n{new_lc_dp_links[1]}\n"


        # Update Mermaid for LC index for DP problems
        mermaid_lc_header = "## Visualization of Practice Areas"
        if mermaid_lc_header in lc_content:
            mermaid_lc_start = lc_content.find("```mermaid", lc_content.find(mermaid_lc_header))
            mermaid_lc_end = lc_content.find("```", mermaid_lc_start + len("```mermaid"))
            if mermaid_lc_start != -1 and mermaid_lc_end != -1:
                mermaid_lc_code = lc_content[mermaid_lc_start + len("```mermaid") : mermaid_lc_end].strip()
                lines_lc = mermaid_lc_code.splitlines()
                
                lc509_node = '    Easy --> LC509["[[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 - Fibonacci]]"]' # In Easy
                lc322_node = '    Medium --> LC322["[[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 - Coin Change]]"]' # In Medium

                cat_dp_def = '        CatDP["Dynamic Programming"]'
                lc509_cat_link = '    LC509 --> CatDP'
                lc322_cat_link = '    LC322 --> CatDP'

                # Insert nodes
                easy_section_exists = any('Easy["Easy Problems"]' in line for line in lines_lc)
                medium_section_exists = any('Medium["(Medium Problems)"]' in line for line in lines_lc)

                if easy_section_exists and 'LC509["' not in mermaid_lc_code:
                    idx_easy_end = -1
                    for i, line in enumerate(lines_lc):
                        if "Easy -->" in line: idx_easy_end = i
                    lines_lc.insert(idx_easy_end + 1 if idx_easy_end !=-1 else 2, lc509_node)
                
                if medium_section_exists and 'LC322["' not in mermaid_lc_code:
                    idx_medium_end = -1
                    for i, line in enumerate(lines_lc):
                        if "Medium -->" in line: idx_medium_end = i
                    lines_lc.insert(idx_medium_end + 1 if idx_medium_end != -1 else (idx_easy_end + 2 if idx_easy_end !=-1 else 3) , lc322_node)


                # Insert category and links
                subgraph_end_idx = -1
                for i, line in enumerate(lines_lc):
                    if "end" in line and "subgraph" in lines_lc[i-1]: subgraph_end_idx = i; break
                
                if subgraph_end_idx != -1:
                    if cat_dp_def not in mermaid_lc_code: lines_lc.insert(subgraph_end_idx, cat_dp_def)
                else: # if no subgraph, append
                    if cat_dp_def not in mermaid_lc_code : lines_lc.append(cat_dp_def)

                if lc509_cat_link not in mermaid_lc_code: lines_lc.append(lc509_cat_link)
                if lc322_cat_link not in mermaid_lc_code: lines_lc.append(lc322_cat_link)

                mermaid_lc_code = "\n".join(lines_lc)
                lc_content = lc_content[:mermaid_lc_start + len("```mermaid")] + "\n" + mermaid_lc_code + "\n" + lc_content[mermaid_lc_end:]

        with open(lc_index_path, "w", encoding="utf-8") as f:
            f.write(lc_content)
        print(f"Updated LeetCode index for DP problems: {lc_index_path}")


    print(f"Script finished. Dynamic Programming concepts and LeetCode examples created/updated in '{kb_root_for_script}'.")

