
import os
import textwrap
import re

# --- Configuration ---
KB_ROOT = "../"  # Assuming the script is run from the directory containing "Interview" folder

# --- Helper Functions ---
def ensure_dir(directory_path):
    os.makedirs(directory_path, exist_ok=True)

def write_file(file_path, content):
    ensure_dir(os.path.dirname(file_path))
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(textwrap.dedent(content))
    print(f"Created/Updated: {file_path}")

def convert_labuladong_links(md_content, current_file_path_from_vault_root=""):
    # Mapping of Labuladong's /algo/ paths to Obsidian paths
    # This needs to be comprehensive based on common links in his notes
    link_mappings = {
        "/algo/essential-technique/dynamic-programming-framework/": "[[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming Framework]]",
        "https://mp.weixin.qq.com/s/qvlfyKBiXVX7CCwWFR-XKg": "[Dynamic Programming FAQ (WeChat)]({})".format("https://mp.weixin.qq.com/s/qvlfyKBiXVX7CCwWFR-XKg"), # External
        "/algo/essential-technique/binary-tree-summary/": "[[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Principles]]",
        "/algo/essential-technique/backtrack-framework/": "[[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking Framework]]",
        "/algo/practice-in-action/two-views-of-backtrack/": "[[Interview/Concept/Algorithms/Backtracking/02 - Backtracking - Two Exhaustion Perspectives (Ball-Box Model)|Two Views of Backtrack]]",
        "/algo/dynamic-programming/longest-increasing-subsequence/": "[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/00 - Longest Increasing Subsequence (LIS)|Longest Increasing Subsequence]]", # Points to itself, if LIS.md uses it
        "/algo/dynamic-programming/space-optimization/": "[[Interview/Concept/Algorithms/Dynamic Programming/05 - DP - Space Optimization|DP Space Optimization]]",
        "/algo/dynamic-programming/faq-summary/": "[[Interview/Concept/Algorithms/Dynamic Programming/01 - DP - Optimal Substructure and Traversal FAQ|DP FAQ Summary]]",
        "/algo/data-structure-basic/binary-tree-traverse-basic/": "[[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Binary Tree Traversal Basics]]",
        "/algo/data-structure-basic/n-ary-tree-traverse-basic/": "[[Interview/Concept/Algorithms/Tree Traversal/02 - N-ary Tree Recursive Traversal (DFS)|N-ary Tree Traversal Basics]]",
        "/algo/essential-technique/algorithm-summary/": "[[Interview/Concept/Algorithms/00 - Framework Thinking (Labuladong Philosophy)|Algorithm Learning Framework]]",
        "/algo/dynamic-programming/subsequence-problem/": "[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/Subsequence Problem Template|Subsequence Problem Template]]", # Placeholder for a general template
        "/algo/essential-technique/binary-search-framework/": "[[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary Search Framework]]",
        # LeetCode links are external and should be preserved
        # Add more mappings as needed
    }

    # Pattern for Markdown reference links: [text][id] and [id]: url
    link_defs = {}
    # First pass: collect all link definitions
    for match in re.finditer(r"^\[([^^\]]+)\]:\s*(.+)$", md_content, re.MULTILINE):
        link_id = match.group(1)
        url = match.group(2).strip()
        link_defs[link_id] = url

    # Function to replace a single link
    def replace_link(match_obj):
        text = match_obj.group(1)
        link_id_or_url = match_obj.group(2)
        
        url = link_defs.get(link_id_or_url, link_id_or_url) # Get URL if it's an ID

        if url in link_mappings:
            # It's an internal /algo/ link that needs conversion
            return link_mappings[url].replace("]]", f"|{text}]]") if text else link_mappings[url]
        elif url.startswith("https://leetcode.com/problems/") or url.startswith("https://leetcode.cn/problems/"):
            # Keep LeetCode links as external Markdown links
            problem_name = url.split("/problems/")[1].split("/")[0]
            # Could convert to [[LCXXX - Problem Name|text]] if LC notes exist for all
            # For now, keep as external to avoid missing LC notes
            return f"[{text}]({url})"
        elif url.startswith("http://") or url.startswith("https://"):
            # Other external links
            return f"[{text}]({url})"
        else:
            # If it's some other kind of relative link or fragment, keep as is or decide policy
            # For now, assume these might be section links, keep them relative
            return f"[{text}]({url})" # Or match_obj.group(0) to return original

    # Second pass: replace links in the content: [text](url_or_id) or [text][id]
    # Regex for [text](url) or [text][id]
    # This regex is a bit tricky because of nested brackets or escaped brackets.
    # A simpler approach for common cases:
    md_content = re.sub(r"\[([^\]^\[]*)\]\[([^\]^\[]*)\]", replace_link, md_content) # For [text][id]
    md_content = re.sub(r"\[([^\]^\[]*)\]\(([^)]*)\)", replace_link, md_content)    # For [text](url)

    # Remove original link definitions from the bottom of the file
    md_content = re.sub(r"^\[([^^\]]+)\]:\s*.+$", "", md_content, flags=re.MULTILINE)
    md_content = md_content.replace('\r\n', '\n') # Normalize line endings

    return md_content.strip()


def create_leetcode_note_placeholder(lc_id_str, problem_name, related_concept_path, related_concept_name):
    lc_number = ''.join(filter(str.isdigit, lc_id_str))
    file_name = f"LC{lc_number} - {problem_name.replace(' ', '_')}.md"
    file_path = os.path.join(KB_ROOT, "Interview/Practice/LeetCode", file_name)

    # Check if file already exists with content (simple check)
    if os.path.exists(file_path) and os.path.getsize(file_path) > 150: # Assuming placeholder is small
        print(f"Note {file_path} already exists with content, skipping placeholder creation.")
        return file_path.replace(KB_ROOT + os.sep, "").replace("\\", "/")

    content = rf"""---
tags: [problem/leetcode, problem, lc/{lc_number}, lc, topic/dynamic_programming, topic, pattern/unknown, pattern, course/labuladong_mention, course]
aliases: [LC{lc_number}, LeetCode {lc_number}, {problem_name}]
---
> [!NOTE] Source Annotation
> Problem: LeetCode {lc_number}. {problem_name}
> This is a placeholder note. Detailed solution to be added.
> Mentioned in [[{related_concept_path.replace(KB_ROOT + os.sep, "").replace("\\", "/")}|{related_concept_name}]].

# LeetCode {lc_number}: {problem_name}

## Problem Statement
(To be filled from LeetCode - Visit [https://leetcode.com/problems/{problem_name.lower().replace(' ', '-')}/](https://leetcode.com/problems/{problem_name.lower().replace(' ', '-')}/))
*Note: Auto-generated URL might be incorrect.*


## Solution Approach
(To be filled)

### Python Solution (Placeholder)
```python
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None): self.val = val; self.left = left; self.right = right
# class ListNode:
#     def __init__(self, val=0, next=None): self.val = val; self.next = next
class Solution:
    def solve(self, params): # Signature will vary based on problem
        # TODO: Implement solution
        pass
```

## Complexity Analysis
(To be filled)

## 总结 (Summary)
(To be filled)

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[{related_concept_path.replace(KB_ROOT + os.sep, "").replace("\\", "/")}|{related_concept_name}]]
"""
    write_file(file_path, content)
    return file_path.replace(KB_ROOT + os.sep, "").replace("\\", "/")

# --- Content Generation Functions ---

def create_dp_framework_00():
    file_path = os.path.join(KB_ROOT, "Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework.md")
    source_file_md_path = "Interview/labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/动态规划解题套路框架.md"
    
    # Links to LeetCode problems mentioned in this file
    lc509_path = create_leetcode_note_placeholder("LC509", "Fibonacci Number", file_path, "DP Introduction and Framework")
    lc322_path = create_leetcode_note_placeholder("LC322", "Coin Change", file_path, "DP Introduction and Framework")

    # Raw content from the provided Markdown file
    content_raw = r"""
# 动态规划解题套路框架

本文讲解的例题

|LeetCode | 力扣 | 难度 |
|:---:|:---:|:---:|

|[322. Coin Change][1] | [322. 零钱兑换][2] | 🟠 |
|:---:|:---:|:---:|
|[509. Fibonacci Number][3] | [509. 斐波那契数][4] |  |

前置知识

阅读本文前，你需要先学习：

-   [二叉树的遍历框架][5]
-   [多叉树结构及遍历框架][6]

  <details class="hint-container details" open="" data-v-c1e14f40=""><summary class="video-summary-title" data-v-c1e14f40=""><div class="video-summary-title" data-v-c1e14f40=""><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" data-v-c1e14f40=""><path fill="currentColor" d="M16.275 10.51q.292-.187.292-.51t-.292-.51l-3.836-2.47q-.298-.212-.619-.038t-.32.547v4.942q0 .373.32.547t.618-.037zM8.115 17q-.69 0-1.152-.462T6.5 15.385V4.615q0-.69.463-1.153T8.116 3h10.769q.69 0 1.153.462t.462 1.153v10.77q0 .69-.462 1.152T18.884 17zm0-1h10.77q.23 0 .423-.192t.192-.423V4.615q0-.23-.192-.423T18.884 4H8.116q-.231 0-.424.192t-.192.423v10.77q0 .23.192.423t.423.192m-3 4q-.69 0-1.153-.462T3.5 18.385V7.115q0-.213.143-.356T4 6.616t.357.143t.143.357v11.269q0 .23.192.423t.423.192h11.27q.213 0 .356.143t.144.357t-.144.357t-.356.143zM7.5 4v12z" data-v-c1e14f40=""></path></svg>&nbsp; <strong data-v-c1e14f40="">课程视频</strong>&nbsp;</div></summary><div class="video-container" data-v-c1e14f40=""><div class="loader" data-v-c1e14f40=""><div class="spinner" data-v-c1e14f40=""></div></div><div style="" class="video-mask" data-v-c1e14f40=""><img src="/algo/images/vod/dp-core.jpg" alt="Video Thumbnail" loading="lazy" class="cover-image" data-v-c1e14f40=""><button class="play-button" data-v-c1e14f40=""></button></div><video style="display:none;" id="player-container-id" preload="metadata" playsinline="" data-v-c1e14f40=""></video></div></details> 

动态规划问题（Dynamic Programming）应该是很多读者头疼的，不过这类问题也是最具有技巧性，最有意思的。本站使用了整整一个章节专门来写这个算法，动态规划的重要性也可见一斑。

本文解决几个问题：

动态规划是什么？解决动态规划问题有什么技巧？如何学习动态规划？

刷题刷多了就会发现，算法技巧就那几个套路，我们后续的动态规划系列章节，都在使用本文的解题框架思维，如果你心里有数，就会轻松很多。所以本文放在第一章，希望能够成为解决动态规划问题的一部指导方针，下面上干货。

首先，**动态规划问题的一般形式就是求最值**。动态规划其实是运筹学的一种最优化方法，只不过在计算机问题上应用比较多，比如说让你求最长递增子序列呀，最小编辑距离呀等等。

既然是要求最值，核心问题是什么呢？**求解动态规划的核心问题是穷举**。因为要求最值，肯定要把所有可行的答案穷举出来，然后在其中找最值呗。

动态规划这么简单，就是穷举就完事了？我看到的动态规划问题都很难啊！

首先，虽然动态规划的核心思想就是穷举求最值，但是问题可以千变万化，穷举所有可行解其实并不是一件容易的事，需要你熟练掌握递归思维，只有列出**正确的「状态转移方程」**，才能正确地穷举。

而且，你需要判断算法问题是否**具备「最优子结构」**，是否能够通过子问题的最值得到原问题的最值。

另外，动态规划问题**存在「重叠子问题」**，如果暴力穷举的话效率会很低，所以需要你使用「备忘录」或者「DP table」来优化穷举过程，避免不必要的计算。

以上提到的重叠子问题、最优子结构、状态转移方程就是动态规划三要素。具体什么意思等会会举例详解，但是在实际的算法问题中，写出状态转移方程是最困难的，这也就是为什么很多朋友觉得动态规划问题困难的原因，我来提供我总结的一个思维框架，辅助你思考状态转移方程：

**明确「状态」-> 明确「选择」 -> 定义 `dp` 数组/函数的含义**。

按上面的套路走，最后的解法代码就会是如下的框架：

```python
# 自顶向下递归的动态规划
# def dp(状态1, 状态2, ...):
#     for 选择 in 所有可能的选择:
#         # 此时的状态已经因为做了选择而改变
#         result = 求最值(result, dp(状态1, 状态2, ...))
#     return result

# 自底向上迭代的动态规划
# 初始化 base case
# dp[0][0][...] = base case
# # 进行状态转移
# for 状态1 in 状态1的所有取值：
#     for 状态2 in 状态2的所有取值：
#         for ...
#             dp[状态1][状态2][...] = 求最值(选择1，选择2...)
```

下面通过斐波那契数列问题和凑零钱问题来详解动态规划的基本原理。前者主要是让你明白什么是重叠子问题（斐波那契数列没有求最值，所以严格来说不是动态规划问题），后者主要举集中于如何列出状态转移方程。

## 一、斐波那契数列

力扣第 509 题「[斐波那契数][4]」就是这个问题，请读者不要嫌弃这个例子简单，**只有简单的例子才能让你把精力充分集中在算法背后的通用思想和技巧上，而不会被那些隐晦的细节问题搞的莫名其妙**。想要困难的例子，接下来的动态规划系列里有的是。

### 暴力递归

斐波那契数列的数学形式就是递归的，写成代码就是这样：

```python
# f(n) 计算第 n 个斐波那契数
# def fib(n: int) -> int:
#     # base case
#     if n == 0 or n == 1:
#         return n
#     return fib(n - 1) + fib(n - 2)
```
> [!INFO] Base Case Convention
> 这里我们按照力扣的题目描述，认为 base case 是 `f(0) = 0` 和 `f(1) = 1`，但在有些斐波那契数列的描述中说 base case 是 `f(1) = 1` 和 `f(2) = 1`，其实它们都是一样的。

学校老师讲递归的时候似乎都是拿这个举例。我们也知道这样写代码虽然简洁易懂，但是十分低效，低效在哪里？假设 `n = 20`，请画出递归树：

![](/algo/images/dynamic-programming/1.jpg)

这个递归树怎么理解？就是说想要计算原问题 `f(20)`，我就得先计算出子问题 `f(19)` 和 `f(18)`，然后要计算 `f(19)`，我就要先算出子问题 `f(18)` 和 `f(17)`，以此类推。最后遇到 `f(1)` 或者 `f(0)` 的时候，结果已知，就能直接返回结果，递归树不再向下生长了。


> [!TIP] Visualizing Recursion Tree
> 借助算法可视化面板可以更好地帮你理解这个过程，`f(20)` 的递归树太大，我们展示一下计算 `f(5)` 的递归过程吧。
>
> 请你打开下面的可视化面板，点击 `if (n == 0 || n == 1)` 这一行代码，即可看到递归树从上向下延伸，遇到叶子节点（base case）后逐层向上返回结果：
>
> ```text
> Algorithm visualize
> id: div_mydata-fib
> ```

**递归算法的时间复杂度怎么计算？就是用子问题个数乘以解决一个子问题需要的时间**。

首先计算子问题个数，即递归树中节点的总数。这棵递归树的高度为 $N$，所以二叉树的节点总数为 $O(2^N)$。

然后计算解决一个子问题的时间，在本算法中，没有循环，只有 `f(n - 1) + f(n - 2)` 一个加法操作，时间为 $O(1)$。

所以，这个算法的时间复杂度为二者相乘，即 $O(2^N)$，指数级别，爆炸。

观察递归树，很明显发现了算法低效的原因：存在大量重复计算。

比如 `f(18)` 被计算了两次，而且你可以看到，以 `f(18)` 为根的这个递归树体量巨大，多算一遍，会耗费大量的时间。更何况还不止 `f(18)` 这一个节点被重复计算，所以这个算法效率很差。

![](/algo/images/dynamic-programming/1.jpg)

这就是动态规划问题的第一个性质：**重叠子问题**。下面，我们想办法解决这个问题。

### 带备忘录的递归解法

即然耗时的原因是重复计算，那么我们可以造一个「备忘录」，每次算出某个子问题的答案后顺便记到「备忘录」里；每次遇到一个子问题别急着计算，先去「备忘录」里查一查，如果发现之前已经解决过这个问题了，直接把答案拿出来用，不要再耗时去计算了。

对于斐波那契数列问题，我们需要一个备忘录记录子问题 `f(x)` 的值，其中 `x` 是一个非负整数，所以一般用一个一维数组 `memo` 充当备忘录就可以了，让 `memo[x]` 存储子问题 `f(x)` 的返回值。

当然，你也可以用一个哈希表来存储，思想都是一样的。

```python
# # Python example (using a dictionary as memo)
# memo_fib = {}
# def fib_memo(n: int) -> int:
#     if n == 0 or n == 1: return n
#     if n in memo_fib: return memo_fib[n]
#     memo_fib[n] = fib_memo(n - 1) + fib_memo(n - 2)
#     return memo_fib[n]

# # Using array as memo (as in Labuladong's Java/C++ example)
# def fib_memo_array_wrapper(n: int) -> int:
#     # 备忘录全初始化为 -1 (or some other indicator of not computed)
#     # 因为数组的索引从 0 开始，所以需要 n + 1 个空间
#     memo = [-1] * (n + 1)
#     return dp_fib_array(memo, n)

# def dp_fib_array(memo: list, n: int) -> int:
#     if n == 0 or n == 1: return n
#     if memo[n] != -1: return memo[n]
#     memo[n] = dp_fib_array(memo, n - 1) + dp_fib_array(memo, n - 2)
#     return memo[n]
```

现在，画出递归树，你就知道「备忘录」到底做了什么。

![](/algo/images/dynamic-programming/2.jpg)

实际上，带「备忘录」的递归算法，把一棵存在巨量冗余的递归树通过「剪枝」，改造成了一幅不存在冗余的递归图，极大减少了子问题（即递归图中节点）的个数，每个子问题都只会被计算一次：

![](/algo/images/dynamic-programming/3.jpg)

**递归算法的时间复杂度怎么计算？就是用子问题个数乘以解决一个子问题需要的时间**。

子问题个数，即图中节点的总数，由于本算法不存在冗余计算，子问题就是 `f(0)`, `f(1)`, `f(2)` ... `f(n)`，数量和输入规模 `n` 成正比，所以子问题个数为 $O(N)$。

解决一个子问题的时间，同上，没有什么循环，时间为 $O(1)$。

所以，本算法的时间复杂度是 $O(N)$，比起指数级复杂度的暴力算法，已经非常高效了。

> [!TIP] Visualizing Memoization
> 这里也结合算法可视化面板来感受剪枝的效果。`fib(20)` 的递归树太大，我们看看 `fib(5)` 的递归过程吧。
>
> 请你点开这个可视化面板，多次点击 `if (n == 0 || n == 1)` 这一行代码即可看到递归树的生长过程。注意看，当遇到重复节点时，递归树不会向下生长，而是直接返回结果，这就是「剪枝」的效果：
>
> ```text
> Algorithm visualize
> id: div_mydata-fib2
> ```

### 自顶向下 vs 自底向上

其实如果你只掌握上面的内容，就已经掌握动态规划的解题方法了：无非就是先写出暴力解法，然后用「备忘录」剪枝消除重叠子问题嘛，动态规划就是这么简单。

不过肯定有读者会提问，为什么我见过的很多动态规划解法就是几个 for 循环，好像并不包含递归，也没见到什么备忘录之类的东西，这是怎么回事呢？

实际上，动态规划解法确实有两种表现形式：

第一种是带备忘录的递归解法，或称为「自顶向下」的解法，也就是我们上面展示的，一个递归函数带一个 `memo` 备忘录。

第二种是 DP table 的迭代解法，或称为「自底向上」的解法，也就是你说的，用 for 循环去迭代 `dp` 数组进行求解。

**这两者的本质是一样的，可以互相转化。迭代解法中的那个 `dp` 数组，就是递归解法中的 `memo` 数组**。

为啥叫「自顶向下」？比如刚才的递归解法，多次点击 `if (n == 0 || n == 1)` 可以看到递归树从上向下生长，从一个规模较大的原问题 `f(5)`，向下逐渐分解规模，直到 `f(0)` 和 `f(1)` 这两个 base case，然后逐层返回答案，这就叫「自顶向下」。
(Reference visualization `div_mydata-fib2` again here.)

啥叫「自底向上」？就是反过来嘛。我们直接从最底下、最简单、问题规模最小、已知结果的 `f(0)` 和 `f(1)`（base case）开始往上推出 `f(2), f(3)...` 最后推出我们想要的 `f(n)`，这就是「自底向上」。

**其实「自底向上」和「自顶向下」本质是一样的，只是视角不同而已**。

> [!TIP] Visualizing Top-Down vs Bottom-Up Equivalence
> 比如我把上面写的带备忘录的递归解法稍微改一改，把对 base case `n == 0 || n == 1` 的处理从递归函数 `dp` 中移到 `memo` 数组中，这应该没问题吧？我们再来看 `fib(5)` 的计算过程。
>
> 你可以多次点击 `memo[n] = dp(memo, n - 1) + dp(memo, n - 2)` 这一行代码，**请注意递归树和 `memo` 数组的变化**：
>
> ```text
> Algorithm visualize
> id: div_fib-memo-ii
> ```
>
> 可以看到，递归树从下向上传递结果的过程，就是 `memo` 数组从 base case 向右推算的过程，这就叫自底向上，是不是很直观？

到这里你应该也观察出来了，其实整个计算过程就是在从左到右计算 `memo` 的值，那又何苦用递归了，搞这么复杂。一个 for 循环是不是就够用了？

### `dp` 数组的迭代（递推）解法

有了上一步的启发，我们不再使用递归函数，直接创建一个数组（DP table），用一个 for 循环从 base case 开始从左到右进行计算即可。

```python
# def fib_tabulation_iterative(n: int) -> int:
#     if n == 0 or n == 1:
#         return n
#     # dp table
#     dp = [0] * (n + 1)
#     # base case
#     dp[0] = 0
#     dp[1] = 1
#     # 状态转移
#     for i in range(2, n + 1):
#         dp[i] = dp[i - 1] + dp[i - 2]
#     return dp[n]
```

> [!TIP] Visualizing Tabulation
> ```text
> Algorithm visualize
> id: div_mydata-fib3
> ```
>
> 画个图就很好理解了，而且你发现这个 DP table 特别像之前那个「剪枝」后的结果，只是反过来算而已：
> ![](/algo/images/dynamic-programming/4.jpg)

实际上，带备忘录的递归解法中的那个「备忘录」`memo` 数组，最终完成后就是这个解法中的 `dp` 数组，你对比一下可视化面板中两个算法执行的过程可以更直观地看出它俩的联系。

所以说自顶向下、自底向上两种解法本质其实是差b差不多bu'duo的，大部分情况下，效率也基本相同。

### 拓展延伸

这里，引出「状态转移方程」这个名词，实际上就是描述问题结构的数学形式：

$F(n) = \begin{cases} 0, & \text{if } n = 0 \\ 1, & \text{if } n = 1 \\ F(n-1) + F(n-2), & \text{if } n > 1 \end{cases}$

为啥叫「状态转移方程」？其实就是为了听起来高端。
`F(n)` 的函数参数会不断变化，所以你把参数 `n` 想做一个状态，这个状态 `n` 是由状态 `n - 1` 和状态 `n - 2` 转移（相加）而来，这就叫状态转移，仅此而已。

你会发现，上面的几种解法中的所有操作，例如 `return fib(n - 1) + fib(n - 2)`，`dp[i] = dp[i - 1] + dp[i - 2]`，以及对备忘录或 DP table 的初始化操作，都是围绕这个方程式的不同表现形式。
可见列出「状态转移方程」的重要性，它是解决问题的核心，而且很容易发现，其实状态转移方程直接代表着暴力解法。

**千万不要看不起暴力解，动态规划问题最困难的就是写出这个暴力解，即状态转移方程**。
只要写出暴力解，优化方法无非是用备忘录或者 DP table，再无奥妙可言。

这个例子的最后，讲一个细节优化。
细心的读者会发现，根据斐波那契数列的状态转移方程，当前状态 `n` 只和之前的 `n-1, n-2` 两个状态有关，其实并不需要那么长的一个 DP table 来存储所有的状态，只要想办法存储之前的两个状态就行了。
所以，可以进一步优化，把空间复杂度降为 $O(1)$。这也就是我们最常见的计算斐波那契数的算法：

```python
# def fib_space_optimized(n: int) -> int:
#     if n == 0 or n == 1:
#         # base case
#         return n
#     # 分别代表 dp[i - 1] 和 dp[i - 2]
#     dp_i_1, dp_i_2 = 1, 0
#     for i in range(2, n + 1):
#         # dp[i] = dp[i - 1] + dp[i - 2];
#         dp_i = dp_i_1 + dp_i_2
#         # 滚动更新
#         dp_i_2 = dp_i_1
#         dp_i_1 = dp_i
#     return dp_i_1
```

> [!TIP] Visualizing Space Optimization
> ```text
> Algorithm visualize
> id: div_fibonacci-number
> ```
> This is related to [[Interview/Concept/Algorithms/Dynamic Programming/05 - DP - Space Optimization|DP Space Optimization]].

这一般是动态规划问题的最后一步优化，如果我们发现每次状态转移只需要 DP table 中的一部分，那么可以尝试缩小 DP table 的大小，只记录必要的数据，从而降低空间复杂度。

上述例子就相当于把 DP table 的大小从 `n` 缩小到 2，即把空间复杂度下降了一个量级。我会在后文 [[Interview/Concept/Algorithms/Dynamic Programming/05 - DP - Space Optimization|对动态规划发动降维打击]] 进一步讲解这个压缩空间复杂度的技巧，一般来说用来把一个二维的 DP table 压缩成一维，即把空间复杂度从 $O(N^2)$ 压缩到 $O(N)$。

有人会问，动态规划的另一个重要特性「最优子结构」，怎么没有涉及？下面会涉及。斐波那契数列的例子严格来说不算动态规划，因为没有涉及求最值，以上旨在说明重叠子问题的消除方法，演示得到最优解法逐步求精的过程。下面，看第二个例子，凑零钱问题。

## 二、凑零钱问题

这是力扣第 322 题「[零钱兑换][2]」：

给你 `k` 种面值的硬币，面值分别为 `c1, c2 ... ck`，每种硬币的数量无限，再给一个总金额 `amount`，问你**最少**需要几枚硬币凑出这个金额，如果不可能凑出，算法返回 -1 。

```python
# coins 中是可选硬币面值，amount 是目标金额
# def coinChange(coins: list[int], amount: int) -> int:
#    pass
```

比如说 `k = 3`，面值分别为 1，2，5，总金额 `amount = 11`。那么最少需要 3 枚硬币凑出，即 11 = 5 + 5 + 1。

你认为计算机应该如何解决这个问题？显然，就是把所有可能的凑硬币方法都穷举出来，然后找找看最少需要多少枚硬币。

### 暴力递归

首先，这个问题是动态规划问题，因为它具有「最优子结构」的。**要符合「最优子结构」，子问题间必须互相独立**。啥叫相互独立？你肯定不想看数学证明，我用一个直观的例子来讲解。

比如说，假设你考试，每门科目的成绩都是互相独立的。你的原问题是考出最高的总成绩，那么你的子问题就是要把语文考到最高，数学考到最高…… 为了每门课考到最高，你要把每门课相应的选择题分数拿到最高，填空题分数拿到最高…… 当然，最终就是你每门课都是满分，这就是最高的总成绩。
得到了正确的结果：最高的总成绩就是总分。因为这个过程符合最优子结构，「每门科目考到最高」这些子问题是互相独立，互不干扰的。

但是，如果加一个条件：你的语文成绩和数学成绩会互相制约，不能同时达到满分，数学分数高，语文分数就会降低，反之亦然。
这样的话，显然你能考到的最高总成绩就达不到总分了，按刚才那个思路就会得到错误的结果。因为「每门科目考到最高」的子问题并不独立，语文数学成绩户互相影响，无法同时最优，所以最优子结构被破坏。
> [!INFO] Optimal Substructure Deeper Dive
> 关于最优子结构的问题，后文 [[Interview/Concept/Algorithms/Dynamic Programming/01 - DP - Optimal Substructure and Traversal FAQ|动态规划答疑篇]] 还会再举例探讨。

回到凑零钱问题，为什么说它符合最优子结构呢？假设你有面值为 `1, 2, 5` 的硬币，你想求 `amount = 11` 时的最少硬币数（原问题），如果你知道凑出 `amount = 10, 9, 6` 的最少硬币数（子问题），你只需要把子问题的答案加一（再选一枚面值为 `1, 2, 5` 的硬币），求个最小值，就是原问题的答案。因为硬币的数量是没有限制的，所以子问题之间没有相互制，是互相独立的。

那么，既然知道了这是个动态规划问题，就要思考如何列出正确的状态转移方程？

1、**确定「状态」，也就是原问题和子问题中会变化的变量**。由于硬币数量无限，硬币的面额也是题目给定的，只有目标金额会不断地向 base case 靠近，所以唯一的「状态」就是目标金额 `amount`。

2、**确定「选择」，也就是导致「状态」产生变化的行为**。目标金额为什么变化呢，因为你在选择硬币，你每选择一枚硬币，就相当于减少了目标金额。所以说所有硬币的面值，就是你的「选择」。

3、**明确 `dp` 函数/数组的定义**。我们这里讲的是自顶向下的解法，所以会有一个递归的 `dp` 函数，一般来说函数的参数就是状态转移中会变化的量，也就是上面说到的「状态」；函数的返回值就是题目要求我们计算的量。就本题来说，状态只有一个，即「目标金额」，题目要求我们计算凑出目标金额所需的最少硬币数量。

**所以我们可以这样定义 `dp` 函数：`dp(n)` 表示，输入一个目标金额 `n`，返回凑出目标金额 `n` 所需的最少硬币数量**。
那么根据这个定义，我们的最终答案就是 `dp(amount)` 的返回值。

搞清楚上面这几个关键点，解法的伪码就可以写出来了：
```python
# # 伪码框架
# def coinChange(coins: list[int], amount: int) -> int:
#     # 题目要求的最终结果是 dp(amount)
#     return dp(coins, amount)

# # 定义：要凑出金额 n，至少要 dp(coins, n) 个硬币
# def dp(coins: list[int], n: int) -> int:
#     # 做选择，选择需要硬币最少的那个结果
#     res = float('inf')
#     for coin in coins:
#         # 计算子问题的结果
#         # sub_problem = dp(coins, n - coin)
#         # if sub_problem == -1: continue # 子问题无解则跳过
#         # res = min(res, 1 + sub_problem)
#         res = min(res, 1 + dp(coins, n - coin)) # Simplified line
#     return res if res != float('inf') else -1 # or handle -1 for base case
```
根据伪码，我们加上 base case 即可得到最终的答案。显然目标金额为 0 时，所需硬币数量为 0；当目标金额小于 0 时，无解，返回 -1 (或者一个代表无穷大的特殊值在递归中):

```python
# class Solution:
#     def coinChange_recursive_brute(self, coins: list[int], amount: int) -> int:
#         # 题目要求的最终结果是 dp(amount)
#         return self._dp_brute(coins, amount)

#     # 定义：要凑出目标金额 n，至少要 _dp_brute(coins, n) 个硬币
#     def _dp_brute(self, coins: list[int], n: int) -> int:
#         # base case
#         if n == 0: return 0
#         if n < 0: return -1 # Or float('inf') to simplify min logic

#         res = float('inf')
#         for coin in coins:
#             # 计算子问题的结果
#             subProblem = self._dp_brute(coins, n - coin)
#             # 子问题无解则跳过
#             if subProblem == -1: 
#                 continue
#             # 在子问题中选择最优解，然后加一
#             res = min(res, subProblem + 1)
        
#         return res if res != float('inf') else -1
```
> [!INFO] Function Signatures
> 这里 `coinChange` 和 `dp` 函数的签名有时会看到不同。为了后文讲解方便，有时会另写一个 `dp` 函数来实现主要逻辑，或者在类中使用 helper method。
> 另外，我经常看到有读者留言问，子问题的结果为什么要加 1（`subProblem + 1`），而不是加硬币金额之类的。我这里统一提示一下，动态规划问题的关键是 `dp` 函数/数组的定义，你这个函数的返回值代表什么？你回过头去搞清楚这一点，然后就知道为什么要给子问题的返回值加 1 了。

> [!TIP] Visualizing Coin Change Recursion
> ```text
> Algorithm visualize
> id: div_coin-change-brute-force
> ```

至此，状态转移方程其实已经完成了，以上算法已经是暴力解法了，以上代码的数学形式就是状态转移方程：
$dp(n) = \begin{cases} 0, & n = 0 \\ -1 \text{ (or } \infty), & n < 0 \\ \min_{c \in \text{coins}} \{dp(n - c) + 1\}, & n > 0 \end{cases}$

至此，这个问题其实就解决了，只不过需要消除一下重叠子问题，比如 `amount = 11, coins = {1,2,5}` 时画出递归树看看：
![](/algo/images/dynamic-programming/5.jpg)

**递归算法的时间复杂度分析：子问题总数 x 解决每个子问题所需的时间**。
子问题总数为递归树的节点个数，但算法会进行剪枝，剪枝的时机和题目给定的具体硬币面额有关，所以可以想象，这棵树生长的并不规则，确切算出树上有多少节点是比较困难的。对于这种情况，我们一般的做法是按照最坏的情况估算一个时间复杂度的上界。
假设目标金额为 `n`，给定的硬币个数为 `k`，那么递归树最坏情况下高度为 `n`（全用面额为 1 的硬币），然后再假设这是一棵满 `k` 叉树，则节点的总数在 $k^n$ 这个数量级。
接下来看每个子问题的复杂度，由于每次递归包含一个 for 循环，复杂度为 $O(k)$，相乘得到总时间复杂度为 $O(k^n)$，指数级别。

### 带备忘录的递归
类似之前斐波那契数列的例子，只需要稍加修改，就可以通过备忘录消除子问题：

```python
# class Solution:
#     memo_coin_change = {}
#     def coinChange_memo(self, coins: list[int], amount: int) -> int:
#         self.memo_coin_change = {} # Reset memo for each call if it's an instance method
#         return self._dp_memo(coins, amount)

#     def _dp_memo(self, coins: list[int], n: int) -> int:
#         if n == 0: return 0
#         if n < 0: return -1
#         if n in self.memo_coin_change:
#             return self.memo_coin_change[n]

#         res = float('inf')
#         for coin in coins:
#             subProblem = self._dp_memo(coins, n - coin)
#             if subProblem == -1: continue
#             res = min(res, subProblem + 1)
        
#         self.memo_coin_change[n] = res if res != float('inf') else -1
#         return self.memo_coin_change[n]
```
> [!TIP] Visualizing Coin Change with Memoization
> ```text
> Algorithm visualize
> id: div_coin-change
> ```

不画图了，很显然「备忘录」大大减小了子问题数目，完全消除了子问题的冗余，所以子问题总数不会超过金额数 `n`，即子问题数目为 $O(N)$ (where N is amount). 处理一个子问题的时间不变，仍是 $O(K)$ (where K is number of coins)，所以总的时间复杂度是 $O(N \cdot K)$。

### `dp` 数组的迭代解法
当然，我们也可以自底向上使用 dp table 来消除重叠子问题，关于「状态」「选择」和 base case 与之前没有区别，`dp` 数组的定义和刚才 `dp` 函数类似，也是把「状态」，也就是目标金额作为变量。不过 `dp` 函数体现在函数参数，而 `dp` 数组体现在数组索引：
**`dp` 数组的定义：当目标金额为 `i` 时，至少需要 `dp[i]` 枚硬币凑出**。

根据我们文章开头给出的动态规划代码框架可以写出如下解法：
```python
# class Solution:
#     def coinChange(self, coins: list[int], amount: int) -> int:
#         # dp[i] will store the minimum coins needed for amount i
#         # Initialize with a value larger than any possible number of coins (e.g., amount + 1)
#         dp = [amount + 1] * (amount + 1) 
#         dp[0] = 0 # Base case: 0 coins for amount 0

#         # Iterate through all amounts from 1 to amount
#         for i in range(1, amount + 1):
#             # For each amount, iterate through all coin denominations
#             for coin in coins:
#                 if i - coin >= 0: # If the current coin can be used
#                     # dp[i - coin] would be min coins for remaining amount
#                     # Add 1 for the current coin
#                     dp[i] = min(dp[i], 1 + dp[i - coin])
        
#         # If dp[amount] is still amount + 1, it means amount cannot be made up
#         return dp[amount] if dp[amount] != amount + 1 else -1
```
> [!INFO] DP Table Initialization
> 为啥 `dp` 数组中的值都初始化为 `amount + 1` 呢，因为凑成 `amount` 金额的硬币数最多只可能等于 `amount`（全用 1 元面值的硬币），所以初始化为 `amount + 1` 就相当于初始化为正无穷，便于后续取最小值。为啥不直接初始化为 int 型的最大值 `Integer.MAX_VALUE` 呢？因为后面有 `dp[i - coin] + 1`，这就会导致整型溢出。

![](/algo/images/dynamic-programming/6.jpg)

## 三、最后总结

第一个斐波那契数列的问题，解释了如何通过「备忘录」或者「dp table」的方法来优化递归树，并且明确了这两种方法本质上是一样的，只是自顶向下和自底向上的不同而已。
第二个凑零钱的问题，展示了如何流程化确定「状态转移方程」，只要通过状态转移方程写出暴力递归解，剩下的也就是优化递归树，消除重叠子问题而已。

如果你不太了解动态规划，还能看到这里，真得给你鼓掌，相信你已经掌握了这个算法的设计技巧。
**计算机解决问题其实没有任何特殊的技巧，它唯一的解决办法就是穷举**，穷举所有可能性。算法设计无非就是先思考「如何穷举」，然后再追求「如何聪明地穷举」。

列出状态转移方程，就是在解决「如何穷举」的问题。之所以说它难，一是因为很多穷举需要递归实现，二是因为有的问题本身的解空间复杂，不那么容易穷举完整。
备忘录、DP table 就是在追求「如何聪明地穷举」。用空间换时间的思路，是降低时间复杂度的不二法门，除此之外，试问，还能玩出啥花活？

之后我们会有一章专门讲解动态规划问题，如果有任何问题都可以随时回来重读本文，希望读者在阅读每个题目和解法时，多往「状态」和「选择」上靠，才能对这套框架产生自己的理解，运用自如。
"""
    
    processed_content = convert_labuladong_links(content_raw, file_path)
    # Add links to problem notes using the lc_paths
    processed_content = processed_content.replace("[322. Coin Change][1]", f"[[{lc322_path}|322. Coin Change]][1]")
    processed_content = processed_content.replace("[322. 零钱兑换][2]", f"[[{lc322_path}|322. 零钱兑换]][2]")
    processed_content = processed_content.replace("[509. Fibonacci Number][3]", f"[[{lc509_path}|509. Fibonacci Number]][3]")
    processed_content = processed_content.replace("[509. 斐波那契数][4]", f"[[{lc509_path}|509. 斐波那契数]][4]")
    
    content = f"""---
tags: [concept/algorithms, concept/dynamic_programming, type/framework, pattern/recursion, pattern/memoization, pattern/tabulation]
aliases: [DP Framework, Dynamic Programming Basics, 动态规划框架]
source_file_path: {source_file_md_path}
---

> [!NOTE] Source Annotation
> Content adapted from [[{source_file_md_path}|动态规划解题套路框架 by Labuladong]].
> Labuladong emphasizes that DP is essentially optimized brute-force search (窮举) through the identification of optimal substructure and overlapping subproblems.

{processed_content}

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Index]]
Next: [[Interview/Concept/Algorithms/Dynamic Programming/01 - DP - Optimal Substructure and Traversal FAQ|DP - Optimal Substructure and Traversal FAQ]]
Related Problems: [[{lc509_path}|LC509 - Fibonacci Number]], [[{lc322_path}|LC322 - Coin Change]]
Further Reading: [[Interview/Concept/Algorithms/Dynamic Programming/Memoization|Memoization]], [[Interview/Concept/Algorithms/Dynamic Programming/Tabulation|Tabulation]]
"""
    write_file(file_path, content)


def create_dp_optimal_substructure_01():
    file_path = os.path.join(KB_ROOT, "Interview/Concept/Algorithms/Dynamic Programming/01 - DP - Optimal Substructure and Traversal FAQ.md")
    source_file_md_path = "Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/动态规划基本技巧/最优子结构原理和 dp 数组遍历方向.md"
    
    content_raw = r"""
# 最优子结构原理和 dp 数组遍历方向

前置知识

阅读本文前，你需要先学习：

-   [动态规划核心框架][1]

本文是旧文 [动态规划答疑篇][2] 的修订版，根据我的不断学习总结以及读者的评论反馈，我给扩展了更多内容，力求使本文成为继 [动态规划核心套路框架][3] 之后的一篇全面答疑文章。以下是正文。

这篇文章就给你讲明白以下几个问题：

1、到底什么才叫「最优子结构」，和动态规划什么关系。

2、如何判断一个问题是动态规划问题，即如何看出是否存在重叠子问题。

3、为什么经常看到将 `dp` 数组的大小设置为 `n + 1` 而不是 `n`。

4、为什么动态规划遍历 `dp` 数组的方式五花八门，有的正着遍历，有的倒着遍历，有的斜着遍历。

loading...
"""
    processed_content = convert_labuladong_links(content_raw, file_path)
    content = f"""---
tags: [concept/algorithms, concept/dynamic_programming, type/faq, concept/optimal_substructure, concept/dp_array_traversal]
aliases: [Optimal Substructure, DP Array Traversal, DP FAQ]
source_file_path: {source_file_md_path}
---

> [!NOTE] Source Annotation
> Content adapted from [[{source_file_md_path}|最优子结构原理和 dp 数组遍历方向 by Labuladong]].
> This note clarifies the concept of optimal substructure and discusses DP array traversal strategies.

{processed_content}

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Index]]
Previous: [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|DP Introduction and Framework]]
Next: [[Interview/Concept/Algorithms/Dynamic Programming/02 - DP - Base Cases and Memoization Init|DP - Base Cases and Memoization Init]]
"""
    write_file(file_path, content)

def create_dp_base_cases_02():
    file_path = os.path.join(KB_ROOT, "Interview/Concept/Algorithms/Dynamic Programming/02 - DP - Base Cases and Memoization Init.md")
    source_file_md_path = "Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/动态规划基本技巧/base case 和备忘录的初始值怎么定？.md"

    lc931_path = create_leetcode_note_placeholder("LC931", "Minimum Falling Path Sum", file_path, "DP Base Cases and Memoization Init")

    content_raw = r"""
# base case 和备忘录的初始值怎么定？

本文讲解的例题

|LeetCode | 力扣 | 难度 |
|:---:|:---:|:---:|

|[931. Minimum Falling Path Sum][1] | [931. 下降路径最小和][2] | 🟠 |
|:---:|:---:|:---:|

前置知识

阅读本文前，你需要先学习：

-   [动态规划核心框架][3]

很多读者对动态规划问题的 base case、备忘录初始值等问题存在疑问，本文就专门讲一讲这类问题，顺便聊一聊怎么通过题目的蛛丝马迹揣测出题人的小心思，辅助我们解题。

看下力扣第 931 题「[下降路径最小和][4]」，输入为一个 `n * n` 的二维数组 `matrix`，请你计算从第一行落到最后一行，经过的路径和最小为多少：

<details class="hint-container details" open=""><summary><strong>931. 下降路径最小和</strong>&nbsp;| <span><a target="_blank" href="https://leetcode.cn/problems/minimum-falling-path-sum/" rel="noopener noreferrer">力扣</a> | </span><span><a target="_blank" href="https://leetcode.com/problems/minimum-falling-path-sum/" rel="noopener noreferrer">LeetCode</a> |</span> &nbsp;🟠</summary><div><p>给你一个 <code>n x n</code> 的<strong> 方形 </strong>整数数组&nbsp;<code>matrix</code> ，请你找出并返回通过 <code>matrix</code> 的<strong>下降路径</strong><em> </em>的<strong> </strong><strong>最小和</strong> 。</p><p><strong>下降路径</strong> 可以从第一行中的任何元素开始，并从每一行中选择一个元素。在下一行选择的元素和当前行所选元素最多相隔一列（即位于正下方或者沿对角线向左或者向右的第一个元素）。具体来说，位置 <code>(row, col)</code> 的下一个元素应当是 <code>(row + 1, col - 1)</code>、<code>(row + 1, col)</code> 或者 <code>(row + 1, col + 1)</code> 。</p><p><strong>示例 1：</strong></p><p><img alt="" src="/algo/images/lc/uploads/2021/11/03/failing1-grid.jpg" style="height: 500px; width: 499px;"></p><pre><strong>输入：</strong>matrix = [[2,1,3],[6,5,4],[7,8,9]]
<strong>输出：</strong>13
<strong>解释：</strong>如图所示，为和最小的两条下降路径
</pre><p><strong>示例 2：</strong></p><p><img alt="" src="/algo/images/lc/uploads/2021/11/03/failing2-grid.jpg" style="height: 365px; width: 164px;"></p><pre><strong>输入：</strong>matrix = [[-19,57],[-40,-5]]
<strong>输出：</strong>-59
<strong>解释：</strong>如图所示，为和最小的下降路径
</pre><p><strong>提示：</strong></p><ul><li><code>n == matrix.length == matrix[i].length</code></li><li><code>1 &lt;= n &lt;= 100</code></li><li><code>-100 &lt;= matrix[i][j] &lt;= 100</code></li></ul></div><strong style="font-size:small;">题目来源：<a href="https://leetcode.cn/problems/minimum-falling-path-sum/" target="_blank">力扣 931. 下降路径最小和</a>。</strong></details>

函数签名如下：

```python
# def minFallingPathSum(matrix: list[list[int]]) -> int:
#     pass
```

今天这道题不算是困难的题目，所以**我借这道题来讲讲 base case 的返回值、备忘录的初始值、索引越界情况的返回值如何确定**。

不过还是要根据 [动态规划的标准套路][8] 讲一下这道题的解题思路。

## 解题思路

首先，我们可以定义一个 `dp` 数组：
loading...
"""
    processed_content = convert_labuladong_links(content_raw, file_path)
    # Specific replacements for this file's LC links
    processed_content = processed_content.replace("[931. Minimum Falling Path Sum][1]", f"[[{lc931_path}|931. Minimum Falling Path Sum]][1]")
    processed_content = processed_content.replace("[931. 下降路径最小和][2]", f"[[{lc931_path}|931. 下降路径最小和]][2]")
    processed_content = processed_content.replace("「[下降路径最小和][4]」", f"「[[{lc931_path}|下降路径最小和]][4]」")


    content = f"""---
tags: [concept/algorithms, concept/dynamic_programming, type/faq, concept/dp_base_case, concept/memoization_initialization]
aliases: [DP Base Cases, Memoization Initialization, 备忘录初始值]
source_file_path: {source_file_md_path}
---

> [!NOTE] Source Annotation
> Content adapted from [[{source_file_md_path}|base case 和备忘录的初始值怎么定？ by Labuladong]].
> This note explains how to determine base cases and memoization table initial values in DP, using [[{lc931_path}|LC931 Minimum Falling Path Sum]] as an example.

{processed_content}

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Index]]
Previous: [[Interview/Concept/Algorithms/Dynamic Programming/01 - DP - Optimal Substructure and Traversal FAQ|DP Optimal Substructure FAQ]]
Next: [[Interview/Concept/Algorithms/Dynamic Programming/03 - DP - Two Exhaustion Perspectives|DP Two Exhaustion Perspectives]]
Related Problems: [[{lc931_path}|LC931 - Minimum Falling Path Sum]]
"""
    write_file(file_path, content)

def create_dp_exhaustion_perspectives_03():
    file_path = os.path.join(KB_ROOT, "Interview/Concept/Algorithms/Dynamic Programming/03 - DP - Two Exhaustion Perspectives.md")
    source_file_md_path = "Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/动态规划基本技巧/动态规划穷举的两种视角.md"

    lc115_path = create_leetcode_note_placeholder("LC115", "Distinct Subsequences", file_path, "DP Two Exhaustion Perspectives")

    content_raw = r"""
# 动态规划穷举的两种视角

本文讲解的例题

|LeetCode | 力扣 | 难度 |
|:---:|:---:|:---:|

|[115. Distinct Subsequences][1] | [115. 不同的子序列][2] | 🔴 |
|:---:|:---:|:---:|

前置知识

阅读本文前，你需要先学习：

-   [二叉树系列算法（纲领篇）][3]
-   [动态规划核心框架][4]
-   [球盒模型：回溯算法穷举的两种视角][5]

本文我会带大家复习一下动态规划相关问题的一系列解题套路，然后着重讨论一下动态规划穷举时不同视角的问题。

## 动态规划解题组合拳

首先，[我的刷题心得][7] 讲了，我们刷的算法问题的本质是「穷举」，动态规划问题也不例外，你必须想办法穷举所有可能的解，然后从中筛选出符合题目要求的解。

另外，动态规划问题穷举的过程中会出现重叠子问题导致的冗余计算，所以前文 [动态规划核心套路框架][8] 中告诉你如何一步一步把暴力穷举解法优化成效率更高的动态规划解法。

然而，想要写出暴力解需要依据状态转移方程，状态转移方程是动态规划的解题核心，可不是那么容易想出来的。不过，前文 [动态规划设计：数学归纳法][9] 告诉你，思考状态转移方程的一个基本方法是数学归纳法，即明确 `dp` 函数或数组的定义，然后使用这个定义，从已知的「状态」中推导出未知的「状态」。

**接下来就是本文要着重探讨的问题了：就算 `dp` 函数/数组的定义相同，如果你使用不同的「视角」进行穷举，效率也不见得是相同的**。

关于穷举「视角」的问题，前文 [球盒模型：回溯算法穷举的两种视角][10] 讲了回溯算法中不同的穷举视角导致的不同解法，其实这种视角的切换在动态规划类型问题中依然存在。前文对排列的举例非常有助于你理解穷举视角的问题，这里再简单提一下。

loading...
"""
    processed_content = convert_labuladong_links(content_raw, file_path)
    processed_content = processed_content.replace("[115. Distinct Subsequences][1]", f"[[{lc115_path}|115. Distinct Subsequences]][1]")
    processed_content = processed_content.replace("[115. 不同的子序列][2]", f"[[{lc115_path}|115. 不同的子序列]][2]")

    content = f"""---
tags: [concept/algorithms, concept/dynamic_programming, type/perspective, pattern/recursion_thinking]
aliases: [DP Exhaustion Views, DP Recursive Perspectives, DP 穷举视角]
source_file_path: {source_file_md_path}
---

> [!NOTE] Source Annotation
> Content adapted from [[{source_file_md_path}|动态规划穷举的两种视角 by Labuladong]].
> This note discusses different perspectives for structuring DP solutions, drawing parallels with backtracking. It uses [[{lc115_path}|LC115 Distinct Subsequences]] as an example.

{processed_content}

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Index]]
Previous: [[Interview/Concept/Algorithms/Dynamic Programming/02 - DP - Base Cases and Memoization Init|DP Base Cases and Memoization Init]]
Next: [[Interview/Concept/Algorithms/Dynamic Programming/04 - DP - vs Backtracking Conversion|DP vs Backtracking Conversion]]
Related Problems: [[{lc115_path}|LC115 - Distinct Subsequences]]
"""
    write_file(file_path, content)

def create_dp_vs_backtracking_04():
    file_path = os.path.join(KB_ROOT, "Interview/Concept/Algorithms/Dynamic Programming/04 - DP - vs Backtracking Conversion.md")
    source_file_md_path = "Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/动态规划基本技巧/动态规划和回溯算法的思维转换.md"

    lc139_path = create_leetcode_note_placeholder("LC139", "Word Break", file_path, "DP vs Backtracking Conversion")
    lc140_path = create_leetcode_note_placeholder("LC140", "Word Break II", file_path, "DP vs Backtracking Conversion")

    content_raw = r"""
# 动态规划和回溯算法的思维转换

本文讲解的例题

|LeetCode | 力扣 | 难度 |
|:---:|:---:|:---:|

|[139. Word Break][1] | [139. 单词拆分][2] | 🟠 |
|:---:|:---:|:---:|
|[140. Word Break II][3] | [140. 单词拆分 II][4] | 🔴 |

前置知识

阅读本文前，你需要先学习：

-   [二叉树系列算法（纲领篇）][5]
-   [动态规划核心框架][6]

之前 [手把手带你刷二叉树（纲领篇）][7] 把递归穷举划分为「遍历」和「分解问题」两种思路，其中「遍历」的思路扩展延伸一下就是 [回溯算法][8]，「分解问题」的思路可以扩展成 [动态规划算法][9]。

这种思维转换不止局限于二叉树相关的算法，本文就跳出二叉树类型问题，来看看实际算法题中如何把问题抽象成树形结构，见招拆招逐步优化，从而进行「遍历」和「分解问题」的思维转换，从回溯算法顺滑地切换到动态规划算法。

先说句题外话，前文 [动态规划核心框架详解][10] 说，**标准的动态规划问题一定是求最值的**，因为动态规划类型问题有一个性质叫做「最优子结构」，即从子问题的最优解推导出原问题的最优解。

但在我们平常的语境中，就算不是求最值的题目，只要看见使用备忘录消除重叠子问题，我们一般都称它为动态规划算法。严格来讲这是不符合动态规划问题的定义的，说这种解法叫做「带备忘录的 DFS 算法」可能更准确些。不过咱也不用太纠结这种名词层面的细节，既然大家叫的顺口，就叫它动态规划也无妨。

本文讲解的两道题目也不是求最值的，但依然会把他们的解法称为动态规划解法，这里提前跟大家说下这个变通，免得严谨的读者疑惑。其他不多说了，直接看题目吧。

loading...
"""
    processed_content = convert_labuladong_links(content_raw, file_path)
    processed_content = processed_content.replace("[139. Word Break][1]", f"[[{lc139_path}|139. Word Break]][1]")
    processed_content = processed_content.replace("[139. 单词拆分][2]", f"[[{lc139_path}|139. 单词拆分]][2]")
    processed_content = processed_content.replace("[140. Word Break II][3]", f"[[{lc140_path}|140. Word Break II]][3]")
    processed_content = processed_content.replace("[140. 单词拆分 II][4]", f"[[{lc140_path}|140. 单词拆分 II]][4]")

    content = f"""---
tags: [concept/algorithms, concept/dynamic_programming, concept/backtracking, type/comparison, type/mindset_conversion]
aliases: [DP vs Backtracking, Converting Backtracking to DP]
source_file_path: {source_file_md_path}
---

> [!NOTE] Source Annotation
> Content adapted from [[{source_file_md_path}|动态规划和回溯算法的思维转换 by Labuladong]].
> This note discusses converting between backtracking and DP mindsets, using [[{lc139_path}|LC139 Word Break]] and [[{lc140_path}|LC140 Word Break II]] as examples.

{processed_content}

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Index]]
Previous: [[Interview/Concept/Algorithms/Dynamic Programming/03 - DP - Two Exhaustion Perspectives|DP Two Exhaustion Perspectives]]
Next: [[Interview/Concept/Algorithms/Dynamic Programming/05 - DP - Space Optimization|DP Space Optimization]]
Related Problems: [[{lc139_path}|LC139 - Word Break]], [[{lc140_path}|LC140 - Word Break II]]
"""
    write_file(file_path, content)

def create_dp_space_optimization_05():
    file_path = os.path.join(KB_ROOT, "Interview/Concept/Algorithms/Dynamic Programming/05 - DP - Space Optimization.md")
    source_file_md_path = "Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/动态规划基本技巧/对动态规划进行空间压缩.md"
    
    content_raw = r"""
# 对动态规划进行空间压缩

前置知识

阅读本文前，你需要先学习：

-   [动态规划核心框架][1]

动态规划算法的主要表现形式是带备忘录的递归解法，或者递推的迭代解法，这两种解法本质上都是一样的，效率也差不多。

本文将介绍动态规划迭代写法的一个优势，就是可以将 `dp` 数组进行空间压缩（一般称为滚动数组技巧），降低空间复杂度。

简单说就是，某些情况下状态转移方程仅依赖于相邻的状态，那么就没必要维护整个 `dp` 数组，仅维护所需的几个状态即可，这样可以降低空间复杂度。

我个人认为空间压缩技巧不是必须掌握的，原因是：

1、一般的笔试中对空间的要求并不高，大部分情况下，即便不使用这个优化技巧也能通过所有测试用例。

2、使用空间压缩技巧之后代码可读性会变得非常差，不利于理解和调试。

所以本文作为选学内容，有兴趣的读者可以仔细学习理解一下。

loading...
"""
    processed_content = convert_labuladong_links(content_raw, file_path)
    content = f"""---
tags: [concept/algorithms, concept/dynamic_programming, type/optimization, concept/space_optimization, pattern/rolling_array]
aliases: [DP Space Compression, Rolling Array DP]
source_file_path: {source_file_md_path}
---

> [!NOTE] Source Annotation
> Content adapted from [[{source_file_md_path}|对动态规划进行空间压缩 by Labuladong]].
> This note explains space optimization techniques for DP, like using a rolling array.

{processed_content}

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Index]]
Previous: [[Interview/Concept/Algorithms/Dynamic Programming/04 - DP - vs Backtracking Conversion|DP vs Backtracking Conversion]]
Next: [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/00 - Longest Increasing Subsequence (LIS)|DP Patterns: LIS]]
"""
    write_file(file_path, content)

def create_dp_pattern_lis():
    dir_path = os.path.join(KB_ROOT, "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP")
    ensure_dir(dir_path)
    file_path = os.path.join(dir_path, "00 - Longest Increasing Subsequence (LIS).md")
    source_file_md_path = "Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/动态规划基本技巧/动态规划设计：最长递增子序列.md"

    lc300_path = create_leetcode_note_placeholder("LC300", "Longest Increasing Subsequence", file_path, "DP Design: LIS")
    lc354_path = create_leetcode_note_placeholder("LC354", "Russian Doll Envelopes", file_path, "DP Design: LIS")

    content_raw = r"""
# 动态规划设计：最长递增子序列

本文讲解的例题

|LeetCode | 力扣 | 难度 |
|:---:|:---:|:---:|

|[300. Longest Increasing Subsequence][1] | [300. 最长递增子序列][2] | 🟠 |
|:---:|:---:|:---:|
|[354. Russian Doll Envelopes][3] | [354. 俄罗斯套娃信封问题][4] | 🔴 |

前置知识

阅读本文前，你需要先学习：

-   [动态规划核心框架][5]

也许有读者看了前文 [动态规划详解][6]，学会了动态规划的套路：找到了问题的「状态」，明确了 `dp` 数组/函数的含义，定义了 base case；但是不知道如何确定「选择」，也就是找不到状态转移的关系，依然写不出动态规划解法，怎么办？

不要担心，动态规划的难点本来就在于寻找正确的状态转移方程，本文就借助经典的「最长递增子序列问题」来讲一讲设计动态规划的通用技巧：**数学归纳思想**。

最长递增子序列（Longest Increasing Subsequence，简写 LIS）是非常经典的一个算法问题，比较容易想到的是动态规划解法，时间复杂度 O(N^2)，我们借这个问题来由浅入深讲解如何找状态转移方程，如何写出动态规划解法。比较难想到的是利用二分查找，时间复杂度是 O(NlogN)，我们通过一种简单的纸牌游戏来辅助理解这种巧妙的解法。

力扣第 300 题「[最长递增子序列][7]」就是这个问题：

<details class="hint-container details" open=""><summary><strong>300. 最长递增子序列</strong>&nbsp;| <span><a target="_blank" href="https://leetcode.cn/problems/longest-increasing-subsequence/" rel="noopener noreferrer">力扣</a> | </span><span><a target="_blank" href="https://leetcode.com/problems/longest-increasing-subsequence/" rel="noopener noreferrer">LeetCode</a> |</span> &nbsp;🟠</summary><div><p>给你一个整数数组 <code>nums</code> ，找到其中最长严格递增子序列的长度。</p><p><strong>子序列&nbsp;</strong>是由数组派生而来的序列，删除（或不删除）数组中的元素而不改变其余元素的顺序。例如，<code>[3,6,2,7]</code> 是数组 <code>[0,3,1,6,2,2,7]</code> 的<span data-keyword="subsequence-array">子序列</span>。</p>&nbsp;<p><strong>示例 1：</strong></p><pre><strong>输入：</strong>nums = [10,9,2,5,3,7,101,18]
<strong>输出：</strong>4
<strong>解释：</strong>最长递增子序列是 [2,3,7,101]，因此长度为 4 。
</pre><p><strong>示例 2：</strong></p><pre><strong>输入：</strong>nums = [0,1,0,3,2,3]
<strong>输出：</strong>4
</pre><p><strong>示例 3：</strong></p><pre><strong>输入：</strong>nums = [7,7,7,7,7,7,7]
<strong>输出：</strong>1
</pre><p><strong>提示：</strong></p><ul><li><code>1 &lt;= nums.length &lt;= 2500</code></li><li><code>-10<sup>4</sup> &lt;= nums[i] &lt;= 10<sup>4</sup></code></li></ul><p><b>进阶：</b></p><ul><li>你能将算法的时间复杂度降低到&nbsp;<code>O(n log(n))</code> 吗?</li></ul></div><strong style="font-size:small;">题目来源：<a href="https://leetcode.cn/problems/longest-increasing-subsequence/" target="_blank">力扣 300. 最长递增子序列</a>。</strong></details>

```python
# 函数签名
# def lengthOfLIS(nums: list[int]) -> int:
#     pass
```

比如说输入 `nums=[10,9,2,5,3,7,101,18]`，其中最长的递增子序列是 `[2,3,7,101]`，所以算法的输出应该是 4。
注意「子序列」和「子串」这两个名词的区别，子串一定是连续的，而子序列不一定是连续的。下面先来设计动态规划算法解决这个问题。

## 一、动态规划解法

动态规划的核心设计思想是数学归纳法。

相信大家对数学归纳法都不陌生，高中就学过，而且思路很简单。比如我们想证明一个数学结论，那么**我们先假设这个结论在 `k < n` 时成立，然后根据这个假设，想办法推导证明出 `k = n` 的时候此结论也成立**。如果能够证明出来，那么就说明这个结论对于 `k` 等于任何数都成立。

类似的，我们设计动态规划算法，不是需要一个 dp 数组吗？我们可以假设 `dp[0...i-1]` 都已经被算出来了，然后问自己：怎么通过这些结果算出 `dp[i]`？

直接拿最长递增子序列这个问题举例你就明白了。不过，首先要定义清楚 dp 数组的含义，即 `dp[i]` 的值到底代表着什么？
**我们的定义是这样的：`dp[i]` 表示以 `nums[i]` 这个数结尾的最长递增子序列的长度**。
> [!INFO] DP Definition Rationale
> 为什么这样定义呢？这是解决子序列问题的一个套路，后文 [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/Subsequence Problem Template|动态规划之子序列问题解题模板]] 总结了几种常见套路。你读完本章所有的动态规划问题，就会发现 `dp` 数组的定义方法也就那几种。

根据这个定义，我们就可以推出 base case：`dp[i]` 初始值为 1，因为以 `nums[i]` 结尾的最长递增子序列起码要包含它自己。

举两个例子：
![](/algo/images/lis/8.jpeg)

这个 GIF 展示了算法演进的过程：
![](/algo/images/lis/gif1.gif)

根据这个定义，我们的最终结果（子序列的最大长度）应该是 dp 数组中的最大值。
```python
# res = 0
# for val in dp:
#     res = max(res, val)
# return res
```

读者也许会问，刚才的算法演进过程中每个 `dp[i]` 的结果是我们肉眼看出来的，我们应该怎么设计算法逻辑来正确计算每个 `dp[i]` 呢？
这就是动态规划的重头戏，如何设计算法逻辑进行状态转移，才能正确运行呢？这里需要使用数学归纳的思想：
**假设我们已经知道了 `dp[0..4]` 的所有结果，我们如何通过这些已知结果推出 `dp[5]` 呢**？
![](/algo/images/lis/6.jpeg)

根据刚才我们对 `dp` 数组的定义，现在想求 `dp[5]` 的值，也就是想求以 `nums[5]` 为结尾的最长递增子序列。
**`nums[5] = 3`，既然是递增子序列，我们只要找到前面那些结尾比 3 小的子序列，然后把 3 接到这些子序列末尾，就可以形成一个新的递增子序列，而且这个新的子序列长度加一**。

`nums[5]` 前面有哪些元素小于 `nums[5]`？这个好算，用 for 循环比较一波就能把这些元素找出来。
以这些元素为结尾的最长递增子序列的长度是多少？回顾一下我们对 `dp` 数组的定义，它记录的正是以每个元素为末尾的最长递增子序列的长度。

以我们举的例子来说，`nums[0]` 和 `nums[4]` 都是小于 `nums[5]` 的，然后对比 `dp[0]` 和 `dp[4]` 的值，我们让 `nums[5]` 和更长的递增子序列结合，得出 `dp[5] = 3`：
![](/algo/images/lis/7.jpeg)

```python
# for j in range(i):
#     if nums[i] > nums[j]:
#         dp[i] = max(dp[i], dp[j] + 1)
```
当 `i = 5` 时，这段代码的逻辑就可以算出 `dp[5]`。其实到这里，这道算法题我们就基本做完了。
读者也许会问，我们刚才只是算了 `dp[5]` 呀，`dp[4]`, `dp[3]` 这些怎么算呢？类似数学归纳法，你已经可以算出 `dp[5]` 了，其他的就都可以算出来：
```python
# for i in range(len(nums)):
#     for j in range(i):
#         # 寻找 nums[0..i-1] 中比 nums[i] 小的元素
#         if nums[i] > nums[j]:
#             # 把 nums[i] 接在后面，即可形成长度为 dp[j] + 1，
#             # 且以 nums[i] 为结尾的递增子序列
#             dp[i] = max(dp[i], dp[j] + 1)
```
结合我们刚才说的 base case，下面我们看一下完整代码：
```python
# class Solution:
#     def lengthOfLIS(self, nums: list[int]) -> int:
#         # 定义：dp[i] 表示以 nums[i] 这个数结尾的最长递增子序列的长度
#         dp = [1] * len(nums)
#         # base case：dp 数组全都初始化为 1
#         for i in range(len(nums)):
#             for j in range(i):
#                 if nums[i] > nums[j]: 
#                     dp[i] = max(dp[i], dp[j] + 1) 
#         
#         res = 0
#         if not dp: return 0 # Handle empty nums
#         for i in range(len(dp)):
#             res = max(res, dp[i])
#         return res
```
> ```text
> Algorithm visualize
> id: div_longest-increasing-subsequence
> ```

至此，这道题就解决了，时间复杂度 $O(N^2)$。总结一下如何找到动态规划的状态转移关系：
1、明确 `dp` 数组的定义。这一步对于任何动态规划问题都很重要，如果不得当或者不够清晰，会阻碍之后的步骤。
2、根据 `dp` 数组的定义，运用数学归纳法的思想，假设 `dp[0...i-1]` 都已知，想办法求出 `dp[i]`，一旦这一步完成，整个题目基本就解决了。
但如果无法完成这一步，很可能就是 `dp` 数组的定义不够恰当，需要重新定义 `dp` 数组的含义；或者可能是 `dp` 数组存储的信息还不够，不足以推出下一步的答案，需要把 `dp` 数组扩大成二维数组甚至三维数组。

目前的解法是标准的动态规划，但对最长递增子序列问题来说，这个解法不是最优的，可能无法通过所有测试用例了，下面讲讲更高效的解法。

## 二、二分查找解法

这个解法的时间复杂度为 $O(N \log N)$，但是说实话，正常人基本想不到这种解法（也许玩过某些纸牌游戏的人可以想出来）。所以大家了解一下就好，正常情况下能够给出动态规划解法就已经很不错了。

根据题目的意思，我都很难想象这个问题竟然能和二分查找扯上关系。其实最长递增子序列和一种叫做 patience game 的纸牌游戏有关，甚至有一种排序方法就叫做 patience sorting（耐心排序）。
为了简单起见，后文跳过所有数学证明，通过一个简化的例子来理解一下算法思路。

首先，给你一排扑克牌，我们像遍历数组那样从左到右一张一张处理这些扑克牌，最终要把这些牌分成若干堆。
![](/algo/images/lis/poker1.jpeg)

**处理这些扑克牌要遵循以下规则**：
只能把点数小的牌压到点数比它大的牌上；如果当前牌点数较大没有可以放置的堆，则新建一个堆，把这张牌放进去；如果当前牌有多个堆可供选择，则选择最左边的那一堆放置。

比如说上述的扑克牌最终会被分成这样 5 堆（我们认为纸牌 A 的牌面是最大的，纸牌 2 的牌面是最小的）。
![](/algo/images/lis/poker2.jpeg)

为什么遇到多个可选择堆的时候要放到最左边的堆上呢？因为这样可以保证牌堆顶的牌有序（2, 4, 7, 8, Q），证明略。
![](/algo/images/lis/poker3.jpeg)

按照上述规则执行，可以算出最长递增子序列，牌的堆数就是最长递增子序列的长度，证明略。
![](/algo/images/lis/poker4.jpeg)

我们只要把处理扑克牌的过程编程写出来即可。每次处理一张扑克牌不是要找一个合适的牌堆顶来放吗，牌堆顶的牌不是**有序**吗，这就能用到二分查找了：用二分查找来搜索当前牌应放置的位置。

> [!TIP] Binary Search Details
> 前文 [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|二分查找算法详解]] 详细介绍了二分查找的细节及变体，这里就完美应用上了，如果没读过强烈建议阅读。

```python
# class Solution:
#     def lengthOfLIS_binary_search(self, nums: list[int]) -> int:
#         top = [0] * len(nums) # top[i] stores the smallest ending element of an increasing subsequence of length i+1
#         piles = 0 # Number of active piles, also length of LIS found so far
        
#         for poker in nums:
#             # Binary search for the leftmost pile where top[mid] >= poker
#             left, right = 0, piles
#             while left < right:
#                 mid = (left + right) // 2
#                 if top[mid] >= poker:
#                     right = mid
#                 else: # top[mid] < poker
#                     left = mid + 1
            
#             # `left` is the index of the pile to place `poker`
#             # If `left == piles`, it means `poker` is larger than all existing pile tops,
#             # so a new pile is created.
#             if left == piles:
#                 piles += 1
#             top[left] = poker # Place poker on this pile (or start new pile)
            
#         return piles
```

> ```text
> Algorithm visualize
> id: div_mydata-lis
> ```

至此，二分查找的解法也讲解完毕。
这个解法确实很难想到。首先涉及数学证明，谁能想到按照这些规则执行，就能得到最长递增子序列呢？其次还有二分查找的运用，要是对二分查找的细节不清楚，给了思路也很难写对。
所以，这个方法作为思维拓展好了。但动态规划的设计方法应该完全理解：假设之前的答案已知，利用数学归纳的思想正确进行状态的推演转移，最终得到答案。

## 三、拓展到二维

我们看一个经常出现在生活中的有趣问题，力扣第 354 题「[俄罗斯套娃信封问题][16]」，先看下题目：
<details class="hint-container details" open=""><summary><strong>354. 俄罗斯套娃信封问题</strong>&nbsp;| <span><a target="_blank" href="https://leetcode.cn/problems/russian-doll-envelopes/" rel="noopener noreferrer">力扣</a> | </span><span><a target="_blank" href="https://leetcode.com/problems/russian-doll-envelopes/" rel="noopener noreferrer">LeetCode</a> |</span> &nbsp;🔴</summary><div><p>给你一个二维整数数组 <code>envelopes</code> ，其中 <code>envelopes[i] = [w<sub>i</sub>, h<sub>i</sub>]</code> ，表示第 <code>i</code> 个信封的宽度和高度。</p><p>当另一个信封的宽度和高度都比这个信封大的时候，这个信封就可以放进另一个信封里，如同俄罗斯套娃一样。</p><p>请计算 <strong>最多能有多少个</strong> 信封能组成一组“俄罗斯套娃”信封（即可以把一个信封放到另一个信封里面）。</p><p><strong>注意</strong>：不允许旋转信封。</p>&nbsp;<p><strong>示例 1：</strong></p><pre><strong>输入：</strong>envelopes = [[5,4],[6,4],[6,7],[2,3]]
<strong>输出：</strong>3
<strong>解释：</strong>最多信封的个数为 <code>3, 组合为: </code>[2,3] =&gt; [5,4] =&gt; [6,7]。</pre><p><strong>示例 2：</strong></p><pre><strong>输入：</strong>envelopes = [[1,1],[1,1],[1,1]]
<strong>输出：</strong>1
</pre><p><strong>提示：</strong></p><ul><li><code>1 &lt;= envelopes.length &lt;= 10<sup>5</sup></code></li><li><code>envelopes[i].length == 2</code></li><li><code>1 &lt;= w<sub>i</sub>, h<sub>i</sub> &lt;= 10<sup>5</sup></code></li></ul></div><strong style="font-size:small;">题目来源：<a href="https://leetcode.cn/problems/russian-doll-envelopes/" target="_blank">力扣 354. 俄罗斯套娃信封问题</a>。</strong></details>

**这道题目其实是最长递增子序列的一个变种，因为每次合法的嵌套是大的套小的，相当于在二维平面中找一个最长递增的子序列，其长度就是最多能嵌套的信封个数**。

前面说的标准 LIS 算法只能在一维数组中寻找最长子序列，而我们的信封是由 `(w, h)` 这样的二维数对形式表示的，如何把 LIS 算法运用过来呢？
![](/algo/images/nest-envelope/0.jpg)

读者也许会想，通过 `w × h` 计算面积，然后对面积进行标准的 LIS 算法。但是稍加思考就会发现这样不行，比如 `1 × 10` 大于 `3 × 3`，但是显然这样的两个信封是无法互相嵌套的。

这道题的解法比较巧妙：
**先对宽度 `w` 进行升序排序，如果遇到 `w` 相同的情况，则按照高度 `h` 降序排序；之后把所有的 `h` 作为一个数组，在这个数组上计算 LIS 的长度就是答案**。

画个图理解一下，先对这些数对进行排序：
![](/algo/images/nest-envelope/1.jpg)

然后在 `h` 上寻找最长递增子序列，这个子序列就是最优的嵌套方案：
![](/algo/images/nest-envelope/2.jpg)

**那么为什么这样就可以找到可以互相嵌套的信封序列呢**？稍微思考一下就明白了：
首先，对宽度 `w` 从小到大排序，确保了 `w` 这个维度可以互相嵌套，所以我们只需要专注高度 `h` 这个维度能够互相嵌套即可。
其次，两个 `w` 相同的信封不能相互包含，所以对于宽度 `w` 相同的信封，对高度 `h` 进行降序排序，保证二维 LIS 中不存在多个 `w` 相同的信封（因为题目说了长宽相同也无法嵌套）。

下面看解法代码：
```python
# class Solution:
#     # envelopes = [[w, h], [w, h]...]
#     def maxEnvelopes(self, envelopes: list[list[int]]) -> int:
#         n = len(envelopes)
#         # 按宽度升序排列，如果宽度一样，则按高度降序排列
#         # Python's sort is stable, so a secondary sort key needs to be negated for descending
#         envelopes.sort(key = lambda x: (x[0], -x[1]))
#         
#         # 对高度数组寻找 LIS
#         height = [a[1] for a in envelopes]
#         return self.lengthOfLIS_binary_search(height) # Use the O(N log N) LIS version
#         
#     def lengthOfLIS_binary_search(self, nums: list[int]) -> int:
#         # (Implementation from earlier in this note)
#         top = [0] * len(nums)
#         piles = 0
#         for poker in nums:
#             left, right = 0, piles
#             while left < right:
#                 mid = (left + right) // 2
#                 if top[mid] >= poker: # Strict LIS needs >, if non-decreasing LIS allow >=
#                                     # The problem implies strict increase for envelopes w.r.t height
#                                     # so, `top[mid] >= poker` or `top[mid] > poker` then `right=mid` depends on variant
#                                     # For LIS, if `top[mid] == poker`, we want to replace it only if it's smaller
#                                     # If `top[mid] > poker`, `right = mid`
#                                     # If `top[mid] < poker`, `left = mid + 1`
#                                     # If `top[mid] == poker`, `right = mid` (to find leftmost to replace, effectively ignoring duplicates)
#                                     # Let's use standard LIS logic from problem 300, where it's strictly increasing:
#                                     # `top[mid] < poker` -> `left = mid + 1`
#                                     # `top[mid] == poker` -> `right = mid` (or `left = mid` then handle)
#                                     # `top[mid] > poker` -> `right = mid`
#                                     # The code used in the article for LC300 is `left < right` loop:
#                                     # if top[mid] > poker: right = mid
#                                     # elif top[mid] < poker: left = mid + 1
#                                     # else: right = mid
#                                     # This finds the leftmost position top[idx] >= poker.
#                     if top[mid] >= poker:
#                         right = mid
#                     else: # top[mid] < poker
#                         left = mid + 1
#             
#             if left == piles:
#                 piles += 1
#             top[left] = poker
#         return piles
```
> ```text
> Algorithm visualize
> id: div_russian-doll-envelopes
> ```
为了复用之前的函数，我将代码分为了两个函数，你也可以合并代码，节省下 `height` 数组的空间。
由于增加了测试用例，这里必须使用二分搜索版的 `lengthOfLIS` 函数才能通过所有测试用例。这样的话算法的时间复杂度为 $O(N \log N)$，因为排序和计算 LIS 各需要 $O(N \log N)$ 的时间，加到一起还是 $O(N \log N)$；空间复杂度为 $O(N)$，因为计算 LIS 的函数中需要一个 `top` 数组。

"""
    processed_content = convert_labuladong_links(content_raw, file_path)
    processed_content = processed_content.replace("[300. Longest Increasing Subsequence][1]", f"[[{lc300_path}|300. Longest Increasing Subsequence]][1]")
    processed_content = processed_content.replace("[300. 最长递增子序列][2]", f"[[{lc300_path}|300. 最长递增子序列]][2]")
    processed_content = processed_content.replace("[354. Russian Doll Envelopes][3]", f"[[{lc354_path}|354. Russian Doll Envelopes]][3]")
    processed_content = processed_content.replace("[354. 俄罗斯套娃信封问题][4]", f"[[{lc354_path}|354. 俄罗斯套娃信封问题]][4]")
    processed_content = processed_content.replace("「[最长递增子序列][7]」", f"「[[{lc300_path}|最长递增子序列]][7]」")
    processed_content = processed_content.replace("「[俄罗斯套娃信封问题][16]」", f"「[[{lc354_path}|俄罗斯套娃信封问题]][16]」")


    content = f"""---
tags: [concept/algorithms, concept/dynamic_programming, pattern/sequence_dp, pattern/lis, topic/array, topic/binary_search]
aliases: [LIS, Longest Increasing Subsequence, 最长递增子序列, Russian Doll Envelopes]
source_file_path: {source_file_md_path}
---

> [!NOTE] Source Annotation
> Content adapted from [[{source_file_md_path}|动态规划设计：最长递增子序列 by Labuladong]].
> This note explains dynamic programming and binary search approaches for the Longest Increasing Subsequence (LIS) problem, and its application to [[{lc354_path}|LC354 Russian Doll Envelopes]]. The primary LIS problem is [[{lc300_path}|LC300 Longest Increasing Subsequence]].

{processed_content}

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/index|Sequence DP Patterns]]
Previous: [[Interview/Concept/Algorithms/Dynamic Programming/05 - DP - Space Optimization|DP Space Optimization]]
Related Problems: [[{lc300_path}|LC300 - Longest Increasing Subsequence]], [[{lc354_path}|LC354 - Russian Doll Envelopes]]
"""
    write_file(file_path, content)

def create_dp_index():
    file_path = os.path.join(KB_ROOT, "Interview/Concept/Algorithms/Dynamic Programming/index.md")
    # This function will update the existing index file by adding new links if they don't exist.
    
    current_content = ""
    if os.path.exists(file_path):
        with open(file_path, 'r', encoding='utf-8') as f:
            current_content = f.read()

    # Define new entries to add/ensure exist
    new_entries = {
        "Core Concepts & Framework:": [
            "[[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming - Introduction and Framework]]",
            "[[Interview/Concept/Algorithms/Dynamic Programming/01 - DP - Optimal Substructure and Traversal FAQ|DP - Optimal Substructure and Traversal FAQ]]",
            "[[Interview/Concept/Algorithms/Dynamic Programming/02 - DP - Base Cases and Memoization Init|DP - Base Cases and Memoization Init]]",
            "[[Interview/Concept/Algorithms/Dynamic Programming/03 - DP - Two Exhaustion Perspectives|DP - Two Exhaustion Perspectives]]",
            "[[Interview/Concept/Algorithms/Dynamic Programming/04 - DP - vs Backtracking Conversion|DP - vs Backtracking Conversion]]",
            "[[Interview/Concept/Algorithms/Dynamic Programming/05 - DP - Space Optimization|DP - Space Optimization Techniques]]"
        ],
        "Problem Patterns & Examples:": [
            "[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/00 - Longest Increasing Subsequence (LIS)|Longest Increasing Subsequence (LIS)]]"
        ]
    }

    # Ensure base structure
    if not current_content:
        current_content = """---
tags: [index, concept/algorithms, concept/dynamic_programming]
aliases: [Dynamic Programming Index, DP Index]
---

# Dynamic Programming (DP)

This section covers the principles, techniques, and problem-solving frameworks for Dynamic Programming.

## Core Concepts & Framework:
(Content will be added here)

## Problem Patterns & Examples:
(Content will be added here)

## LeetCode Examples Discussed:
(Content will be added here)

## Visualization
(Content will be added here)

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
"""

    # Helper to add entries under a specific header
    def add_entries_under_header(content_str, header_str, entries_to_add):
        if header_str not in content_str:
            # Add header if missing (simple append, might need better placement logic)
            content_str += f"\n## {header_str}\n"
        
        # Find position after header
        header_pos = content_str.find(header_str)
        insert_pos = content_str.find('\n', header_pos) + 1
        
        current_block = content_str[insert_pos:]
        next_section_pos = current_block.find("\n## ") # Find start of next major section
        if next_section_pos == -1:
            next_section_pos = len(current_block)
        
        existing_links_in_block = content_str[insert_pos : insert_pos + next_section_pos]
        
        new_links_to_insert_str = ""
        for entry in entries_to_add:
            # Check if entry (wikilink part) already exists to avoid duplicates
            link_target = entry.split('|')[0].replace("[[", "").replace("]]", "")
            if link_target not in existing_links_in_block:
                new_links_to_insert_str += f"- {entry}\n"
        
        return content_str[:insert_pos] + new_links_to_insert_str + content_str[insert_pos:]

    for header, entries in new_entries.items():
        current_content = add_entries_under_header(current_content, header, entries)
        
    # Update Visualization
    # (This part can be complex if trying to auto-generate a perfect mermaid diagram.
    # For now, I'll ensure the main categories are linked if they weren't before.)
    
    if "graph TD" not in current_content: # Add a basic mermaid diagram if none exists
        current_content += """
## Visualization
```mermaid
graph TD
    DPConcept["Dynamic Programming"] --> IntroFramework["[[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Introduction & Framework]]"]
    IntroFramework --> Pillars["(Optimal Substructure, Overlapping Subproblems, State Transition)"]
    IntroFramework --> Approaches["(Memoization vs Tabulation)"]

    DPConcept --> CoreTech["Core Techniques"]
    CoreTech --> OptSubFAQ["[[Interview/Concept/Algorithms/Dynamic Programming/01 - DP - Optimal Substructure and Traversal FAQ|Optimal Substructure]]"]
    CoreTech --> BaseCaseFAQ["[[Interview/Concept/Algorithms/Dynamic Programming/02 - DP - Base Cases and Memoization Init|Base Cases]]"]
    CoreTech --> ExhaustPersp["[[Interview/Concept/Algorithms/Dynamic Programming/03 - DP - Two Exhaustion Perspectives|Exhaustion Perspectives]]"]
    CoreTech --> DPvsBT["[[Interview/Concept/Algorithms/Dynamic Programming/04 - DP - vs Backtracking Conversion|DP vs Backtracking]]"]
    CoreTech --> SpaceOpt["[[Interview/Concept/Algorithms/Dynamic Programming/05 - DP - Space Optimization|Space Optimization]]"]

    DPConcept --> Patterns["Problem Patterns"]
    Patterns --> SeqDP["[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/index|Sequence DP]]"]
    SeqDP --> LIS["[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/00 - Longest Increasing Subsequence (LIS)|LIS]]"]
    
    classDef main fill:#e6ccff,stroke:#9933ff,stroke-width:2px;
    class DPConcept main;
```
"""
    # Create Sequence DP index if it doesn't exist
    seq_dp_index_path = os.path.join(KB_ROOT, "Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/index.md")
    if not os.path.exists(seq_dp_index_path):
        seq_dp_index_content = """---
tags: [index, concept/algorithms, concept/dynamic_programming, pattern/sequence_dp]
aliases: [Sequence DP Index, DP on Sequences]
---

# Sequence Dynamic Programming Patterns

This section covers DP patterns specifically applied to sequence-based problems.

- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/00 - Longest Increasing Subsequence (LIS)|Longest Increasing Subsequence (LIS)]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/Subsequence Problem Template|Subsequence Problem Template]] (Placeholder)
- (Placeholder for LCS, Edit Distance, etc.)

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Index]]
"""
        write_file(seq_dp_index_path, seq_dp_index_content)

    write_file(file_path, current_content)

def create_main_algorithm_index_if_not_exists():
    file_path = os.path.join(KB_ROOT, "Interview/Concept/Algorithms/index.md")
    if not os.path.exists(file_path) or os.path.getsize(file_path) < 100: # Crude check for placeholder
        content = """---
tags: [index, concept/algorithms]
aliases: [Algorithms Index, Algorithm Concepts]
---

# Algorithm Concepts

This index covers various algorithm design paradigms, specific algorithms, and analytical techniques relevant to coding interviews.

## Algorithm Categories / Patterns
- [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]]
- (Other algorithm categories...)

---
Parent: [[Interview/Concept/index|Interview Concepts Index]]
"""
        write_file(file_path, content)


# --- Main Script Execution ---
if __name__ == "__main__":
    # Ensure base "Interview/Concept/Algorithms" index exists
    create_main_algorithm_index_if_not_exists()

    # Create/Update DP related files
    create_dp_framework_00()
    create_dp_optimal_substructure_01()
    create_dp_base_cases_02()
    create_dp_exhaustion_perspectives_03()
    create_dp_vs_backtracking_04()
    create_dp_space_optimization_05()
    create_dp_pattern_lis()
    
    # Update the main DP index file
    create_dp_index()

    print("Dynamic Programming concept files generated/updated.")

