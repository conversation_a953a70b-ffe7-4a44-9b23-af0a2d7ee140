
import os
import textwrap

# --- Helper Functions ---
def ensure_dir(directory):
    os.makedirs(directory, exist_ok=True)

def write_file(filepath, content):
    ensure_dir(os.path.dirname(filepath))
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(textwrap.dedent(content))
    print(f"Created/Updated: {filepath}")

def update_index_file(index_filepath, new_entry_markdown, section_header_hint, fallback_header_hint="## Core Concepts & Framework:"):
    ensure_dir(os.path.dirname(index_filepath))
    
    if os.path.exists(index_filepath):
        with open(index_filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        link_content_parts = new_entry_markdown.split('|')
        if len(link_content_parts) > 1:
            link_content = link_content_parts[1][:-2] # Extracts "Display Text" from "[[File|Display Text]]"
        else:
            link_content = new_entry_markdown.split('/')[-1][:-2] if '/' in new_entry_markdown else new_entry_markdown # Fallback for simple links

        if link_content in content:
            # More robust check: check for the full link string to avoid partial matches on display text
            if new_entry_markdown in content:
                 print(f"Entry '{new_entry_markdown}' already exists in {index_filepath}")
                 return

        header_to_use = section_header_hint
        if header_to_use not in content:
            header_to_use = fallback_header_hint 
            if header_to_use not in content: 
                content += f"\n\n{header_to_use}\n{new_entry_markdown}\n"
                write_file(index_filepath, content)
                print(f"Appended entry for '{link_content}' to {index_filepath} under new fallback header.")
                return

        parts = content.split(header_to_use, 1)
        if len(parts) == 2:
            lines_after_header = parts[1].splitlines(True)
            insertion_point = 0
            for i, line in enumerate(lines_after_header):
                stripped_line = line.strip()
                if stripped_line.startswith("## ") and i > 0: 
                    insertion_point = i
                    break
                insertion_point = i + 1
            
            lines_after_header.insert(insertion_point, f"{new_entry_markdown}\n")
            content = parts[0] + header_to_use + "".join(lines_after_header)
        else: 
            content += f"\n{new_entry_markdown}\n"
        
        write_file(index_filepath, content)
        print(f"Updated {index_filepath} with entry for '{link_content}'.")
    else:
        index_content = f"""\
        ---
        tags: [index, placeholder]
        aliases: [Placeholder Index]
        ---
        # Placeholder Index
        This index file was created because it didn't exist.
        {section_header_hint}
        {new_entry_markdown}
        ---
        Parent: [[Interview/Concept/index|Main Concepts Index]] 
        """ # Adjusted parent for generic index
        write_file(index_filepath, index_content)
        print(f"Created: {index_filepath} with initial content and entry for '{link_content}'.")

def update_leetcode_index(path, problem_lc_id, problem_name, tags_string, difficulty_category):
    index_file = os.path.join(path, "Interview/Practice/LeetCode/index.md")
    ensure_dir(os.path.dirname(index_file))
    
    # Construct filename carefully, removing problematic characters for filenames
    safe_problem_name = "".join(c if c.isalnum() or c in (' ', '-') else '_' for c in problem_name).replace(' ', '_')
    problem_filename = f"LC{problem_lc_id}-{safe_problem_name}"
    new_entry_link = f"[[Interview/Practice/LeetCode/{problem_filename}|LC{problem_lc_id} - {problem_name}]]"
    
    difficulty_header = f"### {difficulty_category.capitalize()}"
    full_new_entry = f"- {new_entry_link}"

    if os.path.exists(index_file):
        with open(index_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if new_entry_link not in content:
            if difficulty_header in content:
                parts = content.split(difficulty_header, 1)
                header_and_before = parts[0] + difficulty_header
                after_header = parts[1]
                
                # Find the end of the current difficulty section or start of next
                lines_after_header = after_header.splitlines(True)
                insertion_point = 0
                if lines_after_header and not lines_after_header[0].strip(): # if first line after header is blank
                    insertion_point = 1

                for i, line in enumerate(lines_after_header):
                    if line.strip().startswith("### ") and i > 0: # Start of a new difficulty section
                        insertion_point = i
                        break
                    insertion_point = i + 1
                
                lines_after_header.insert(insertion_point, f"{full_new_entry}\n")
                content = header_and_before + "".join(lines_after_header)

            else: 
                content += f"\n\n{difficulty_header}\n{full_new_entry}\n"
            
            write_file(index_file, content)
            print(f"Updated: {index_file} with {problem_lc_id} - {problem_name}")
        else:
            print(f"Entry for {problem_lc_id} - {problem_name} already exists in {index_file}")
    else:
        index_content = f"""\
        ---
        tags: [index, practice/leetcode, interview_prep]
        aliases: [LeetCode Problem Solving, LC Practice]
        ---

        # LeetCode Practice Problems

        This section contains solutions, explanations, and conceptual links for various LeetCode problems, aimed at building practical problem-solving skills for interviews.

        ## Getting Started
        - [[Interview/Practice/LeetCode/00 - LeetCode Platform Guide and Tips|LeetCode Platform Guide and Tips]]

        {difficulty_header}
        {full_new_entry}

        ---
        Parent: [[Interview/Practice/index|Practice Problems]]
        """
        write_file(index_file, index_content)
        print(f"Created: {index_file} with {problem_lc_id} - {problem_name}")

# --- Content Generation Functions ---

def create_perfect_rectangle_concept(kb_root_path):
    filepath = os.path.join(kb_root_path, "Interview/Concept/Algorithms/Geometric Problems/00 - Perfect Rectangle Detection.md")
    content = r"""---
tags: [concept/algorithms, concept/geometric, pattern/area_counting, pattern/vertex_counting, pattern/hashing, topic/geometry]
aliases: [Perfect Rectangle Problem, Rectangle Cover Algorithm, 完美矩形判定]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/如何判定完美矩形.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/如何判定完美矩形.md|如何判定完美矩形 by Labuladong]].
> This pattern is key to solving [[Interview/Practice/LeetCode/LC391 - Perfect Rectangle|LC391 - Perfect Rectangle]].

# Perfect Rectangle Detection Pattern

The problem of determining if a set of smaller rectangles perfectly covers a larger, single rectangle without gaps or overlaps is a fascinating geometric puzzle. Labuladong's approach provides an elegant solution using two main criteria: area matching and vertex counting.

## 🎯 Core Idea

To confirm that a collection of small rectangles `rectangles[i] = [x1_i, y1_i, x2_i, y2_i]` forms a single "perfect" large rectangle, two conditions must simultaneously hold true:

1.  **Area Conservation:** The sum of the areas of all small rectangles must be equal to the area of the encompassing bounding box formed by these small rectangles.
2.  **Vertex Property:** In a perfect covering:
    *   The four corner points of the encompassing bounding box must each appear exactly once as a vertex among all small rectangles.
    *   All other internal vertices formed by the corners of small rectangles must appear an even number of times (typically 2 or 4 times, as they are shared by adjacent small rectangles).

If both these conditions are met, the small rectangles form a perfect rectangle.

## 🛠️ Algorithm Steps

1.  **Calculate Bounding Box and Total Area of Small Rectangles:**
    *   Initialize `min_x1 = float('inf')`, `min_y1 = float('inf')`, `max_x2 = float('-inf')`, `max_y2 = float('-inf')`.
    *   Initialize `total_small_rects_area = 0`.
    *   Iterate through each small rectangle `(x1, y1, x2, y2)`:
        *   Update `min_x1 = min(min_x1, x1)`, `min_y1 = min(min_y1, y1)`.
        *   Update `max_x2 = max(max_x2, x2)`, `max_y2 = max(max_y2, y2)`.
        *   Add its area to `total_small_rects_area`: `(x2 - x1) * (y2 - y1)`.

2.  **Check Area Conservation:**
    *   Calculate the area of the determined bounding box: `bounding_box_area = (max_x2 - min_x1) * (max_y2 - min_y1)`.
    *   If `total_small_rects_area != bounding_box_area`, then it's not a perfect rectangle. Return `False`. This condition alone handles cases with gaps or overlaps that alter the total area.
    ```tikz
    \begin{tikzpicture}[
        rect/.style={draw, minimum width=1cm, minimum height=1cm},
        bounding_box/.style={rect, dashed, red, thick},
        small_rect_A/.style={rect, fill=blue!20},
        small_rect_B/.style={rect, fill=green!20},
        label_style/.style={font=\sffamily\small}
    ]
        % Scenario 1: Gap
        \node[label_style] at (1, 2.5) {Scenario 1: Gap};
        \node[small_rect_A] (s1a) at (0,1) {A};
        \node[small_rect_A] (s1b) at (2,1) {B};
        \node[bounding_box] (bb1) at (1,1) [minimum width=3cm, minimum height=1cm] {};
        \node[label_style, below=0.2cm of bb1] {Area(A)+Area(B) < Area(BB)};

        % Scenario 2: Overlap
        \node[label_style] at (6, 2.5) {Scenario 2: Overlap};
        \node[small_rect_A] (s2a) at (5,1) {A};
        \node[small_rect_B, minimum width=1.5cm] (s2b) at (6,1) {B}; % B overlaps A
        \node[bounding_box] (bb2) at (5.75,1) [minimum width=2.5cm, minimum height=1cm] {};
        \node[label_style, below=0.2cm of bb2] {Area(A)+Area(B) > Area(BB)};
        
        % Correct fill implies this check must pass.
    \end{tikzpicture}
    ```

3.  **Count Vertex Occurrences:**
    *   Use a hash set (`points_count_set`) to track the occurrences of each vertex from the small rectangles. The vertex coordinates `(x, y)` can be stored as strings `"x,y"` or tuples `(x,y)` to be hashable.
    *   For each small rectangle `(x1, y1, x2, y2)`, consider its four corner points: `(x1,y1), (x1,y2), (x2,y1), (x2,y2)`.
    *   For each corner point:
        *   If the point is already in `points_count_set`, remove it (it has appeared an even number of times so far).
        *   If the point is not in `points_count_set`, add it (it has appeared an odd number of times so far).

4.  **Check Vertex Property:**
    *   After processing all small rectangles, `points_count_set` should contain exactly 4 points. If `len(points_count_set) != 4`, return `False`.
    *   These 4 points must be the corners of the bounding box: `(min_x1, min_y1)`, `(min_x1, max_y2)`, `(max_x2, min_y1)`, and `(max_x2, max_y2)`. Check if all four of these are present in `points_count_set`. If not, return `False`.
    ```tikz
    \begin{tikzpicture}[
        rect/.style={draw, fill=gray!10},
        point_highlight/.style={circle, draw, fill=red, inner sep=1.5pt},
        label_style/.style={font=\sffamily\small}
    ]
        % Perfect Rectangle Case
        \node[label_style] at (1.5, 3) {Perfect Rectangle};
        \draw[rect] (0,0) rectangle (1,1); \draw[rect] (1,0) rectangle (2,1);
        \draw[rect] (0,1) rectangle (1,2); \draw[rect] (1,1) rectangle (2,2);
        
        \node[point_highlight, label=below left:{(0,0)}] at (0,0) {};
        \node[point_highlight, label=above left:{(0,2)}] at (0,2) {};
        \node[point_highlight, label=below right:{(2,0)}] at (2,0) {};
        \node[point_highlight, label=above right:{(2,2)}] at (2,2) {};
        % Internal points (e.g., (1,1) appears 4 times, (0,1) 2 times, (1,0) 2 times, (1,2) 2 times, (2,1) 2 times)
        % are removed from the set.
        \node[label_style, below=1cm of current bounding box.south] {Only 4 bounding box corners remain in set.};
        
        % Imperfect Rectangle Case (e.g., an L-shape from 3 squares)
        \begin{scope}[xshift=5cm]
            \node[label_style] at (1, 3) {Imperfect (L-shape)};
            \draw[rect] (0,0) rectangle (1,1); 
            \draw[rect] (0,1) rectangle (1,2);
            \draw[rect] (1,1) rectangle (2,2);
            % Odd-occurrence points: (0,0), (1,0), (0,2), (2,2), (2,1), (1,1) - 6 points
            % (1,1) appears 3 times. (0,1) 2 times. (1,2) 2 times.
            \node[point_highlight, label=below left:{(0,0)}] at (0,0) {};
            \node[point_highlight, label=above left:{(0,2)}] at (0,2) {};
            \node[point_highlight, label=above right:{(2,2)}] at (2,2) {};
            \node[point_highlight, label=right:{(2,1)}] at (2,1) {};
            \node[point_highlight, label=below:{(1,1)}] at (1,1) {};
            \node[point_highlight, label=below:{(1,0)}] at (1,0) {};
            \node[label_style, below=1cm of current bounding box.south] {More than 4 points remain in set.};
        \end{scope}
    \end{tikzpicture}
    ```

5.  If all checks pass, return `True`.

## Python Implementation Sketch
```python
class SolutionPerfectRectangle:
    def isRectangleCover(self, rectangles: list[list[int]]) -> bool:
        min_x1, min_y1 = float('inf'), float('inf')
        max_x2, max_y2 = float('-inf'), float('-inf')
        
        actual_area_sum = 0
        points_set = set()

        for x1, y1, x2, y2 in rectangles:
            min_x1 = min(min_x1, x1)
            min_y1 = min(min_y1, y1)
            max_x2 = max(max_x2, x2)
            max_y2 = max(max_y2, y2)
            
            actual_area_sum += (x2 - x1) * (y2 - y1)
            
            # Vertices for current rectangle
            p1 = (x1, y1)
            p2 = (x1, y2)
            p3 = (x2, y1)
            p4 = (x2, y2)
            
            for point in [p1, p2, p3, p4]:
                if point in points_set:
                    points_set.remove(point)
                else:
                    points_set.add(point)
        
        # Check Area
        expected_bounding_box_area = (max_x2 - min_x1) * (max_y2 - min_y1)
        if actual_area_sum != expected_bounding_box_area:
            return False
            
        # Check Vertex Property
        if len(points_set) != 4:
            return False
        
        # Check if the 4 points in set are the corners of the bounding box
        if (min_x1, min_y1) not in points_set: return False
        if (min_x1, max_y2) not in points_set: return False
        if (max_x2, min_y1) not in points_set: return False
        if (max_x2, max_y2) not in points_set: return False
        
        return True
```

## Complexity Analysis
- **Time Complexity:** $O(N)$, where $N$ is the number of small rectangles.
    - Iterating through rectangles: $O(N)$.
    - Set operations (add, remove, check): Average $O(1)$ for hashable tuples. Each rectangle adds/removes 4 points. Total $O(N)$ for point processing.
- **Space Complexity:** $O(N)$ in the worst case for `points_set` if many distinct vertices appear an odd number of times (e.g., a "checkerboard" of non-touching squares).

## 总结 (Summary)
- Detecting a perfect rectangle formed by smaller rectangles involves two main checks:
    1.  The sum of areas of small rectangles must equal the area of their overall bounding box.
    2.  Exactly four unique vertices (the corners of the bounding box) must result from the "XOR" logic of counting vertex appearances. All internal shared vertices must appear an even number of times.
- This approach uses basic geometry and [[Interview/Concept/Algorithms/Hashing/Detecting Duplicates with Sets|hashing (via sets)]] to efficiently verify these properties.

---
Parent: [[Interview/Concept/Algorithms/Geometric Problems/index|Geometric Problem Patterns]]
Related Problem: [[Interview/Practice/LeetCode/LC391 - Perfect Rectangle|LC391 - Perfect Rectangle]]
"""
    write_file(filepath, content)
    # Create an index file for the new Geometric Problems folder if it doesn't exist
    geom_index_path = os.path.join(kb_root_path, "Interview/Concept/Algorithms/Geometric Problems/index.md")
    if not os.path.exists(geom_index_path):
        index_content = r"""---
tags: [index, concept/algorithms, topic/geometry]
aliases: [Geometric Algorithms Index]
---
# Geometric Algorithm Patterns

This section covers algorithmic patterns related to geometric problems.

- [[Interview/Concept/Algorithms/Geometric Problems/00 - Perfect Rectangle Detection|Perfect Rectangle Detection]]
"""
        write_file(geom_index_path, index_content)
        update_index_file(os.path.join(kb_root_path, "Interview/Concept/Algorithms/index.md"),
                          "- [[Interview/Concept/Algorithms/Geometric Problems/index|Geometric Problems]]",
                          "## Algorithm Categories / Patterns",
                          "## Core Concepts:")
    else: # if index exists, ensure the new concept is linked
        update_index_file(geom_index_path, "- [[Interview/Concept/Algorithms/Geometric Problems/00 - Perfect Rectangle Detection|Perfect Rectangle Detection]]", "## Core Concepts:")


def create_lc391(kb_root_path):
    filepath = os.path.join(kb_root_path, "Interview/Practice/LeetCode/LC391 - Perfect Rectangle.md")
    content = r"""---
tags: [problem/leetcode, lc/hard, topic/array, topic/geometry, pattern/area_counting, pattern/vertex_counting, pattern/hashing, course/labuladong, lc/lc391]
aliases: [LC391, Perfect Rectangle]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/如何判定完美矩形.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 391. Perfect Rectangle
> Solution based on [[Interview/Concept/Algorithms/Geometric Problems/00 - Perfect Rectangle Detection|Perfect Rectangle Detection Pattern]] derived from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/如何判定完美矩形.md|Labuladong's article]].

# LeetCode 391: Perfect Rectangle

## Problem Statement
Given an array `rectangles` where `rectangles[i] = [xi, yi, ai, bi]` represents an axis-aligned rectangle. The bottom-left point of the rectangle is `(xi, yi)` and the top-right point is `(ai, bi)`.
Return `true` if all the rectangles together form an exact cover of a rectangular region. Otherwise, return `false`.

**Official Link:** [LeetCode 391. Perfect Rectangle](https://leetcode.com/problems/perfect-rectangle/)

## Solution Approach
The solution involves checking two main conditions:
1.  **Area Conservation:** The sum of the areas of the small rectangles must equal the area of the bounding box they form.
2.  **Vertex Property:** Exactly four points (the corners of the bounding box) must have an odd count of appearances among all corners of the small rectangles. All other (internal) points must have an even count.

This is detailed in the [[Interview/Concept/Algorithms/Geometric Problems/00 - Perfect Rectangle Detection|Perfect Rectangle Detection Pattern]].

### Python Solution
```python
class Solution:
    def isRectangleCover(self, rectangles: list[list[int]]) -> bool:
        min_x1, min_y1 = float('inf'), float('inf')
        max_x2, max_y2 = float('-inf'), float('-inf')
        
        actual_area_sum = 0
        points_set = set() # Stores points that have appeared an odd number of times

        for x1, y1, x2, y2 in rectangles:
            # Update bounding box coordinates
            min_x1 = min(min_x1, x1)
            min_y1 = min(min_y1, y1)
            max_x2 = max(max_x2, x2)
            max_y2 = max(max_y2, y2)
            
            # Accumulate area of small rectangles
            actual_area_sum += (x2 - x1) * (y2 - y1)
            
            # Process vertices (store as tuples for hashability)
            p1 = (x1, y1)
            p2 = (x1, y2)
            p3 = (x2, y1)
            p4 = (x2, y2)
            
            for point in [p1, p2, p3, p4]:
                if point in points_set:
                    points_set.remove(point) # Seen even times
                else:
                    points_set.add(point)    # Seen odd times
        
        # Check Area Conservation
        expected_bounding_box_area = (max_x2 - min_x1) * (max_y2 - min_y1)
        if actual_area_sum != expected_bounding_box_area:
            return False
            
        # Check Vertex Property
        if len(points_set) != 4:
            return False
        
        # Check if the 4 points in the set are indeed the corners of the bounding box
        if (min_x1, min_y1) not in points_set: return False
        if (min_x1, max_y2) not in points_set: return False
        if (max_x2, min_y1) not in points_set: return False
        if (max_x2, max_y2) not in points_set: return False
        
        return True
```

## Complexity Analysis
- **Time Complexity:** $O(N)$, where $N$ is the number of rectangles. Each rectangle is processed once. Set operations take average $O(1)$.
- **Space Complexity:** $O(N)$ in the worst case for `points_set`.

## 总结 (Summary)
LC391 is solved by verifying area conservation and a specific vertex property. The sum of areas of input rectangles must match the area of their computed bounding box. Additionally, after "XORing" all vertices of the input rectangles (add if not present, remove if present), exactly four vertices must remain, and these must be the corners of the bounding box.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
"""
    write_file(filepath, content)


def create_trapping_rain_water_pattern(kb_root_path):
    filepath = os.path.join(kb_root_path, "Interview/Concept/Algorithms/Array Manipulation/Trapping Rain Water Pattern.md")
    content = r"""---
tags: [concept/algorithms, pattern/two_pointers, pattern/dynamic_programming, pattern/array_manipulation, topic/array, course/labuladong]
aliases: [Trapping Rain Water, 接雨水问题思路]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/如何高效解决接雨水问题.md
---

> [!NOTE] Source Annotation
> Content inspired by [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/如何高效解决接雨水问题.md|如何高效解决接雨水问题 by Labuladong]]. This problem is [[Interview/Practice/LeetCode/LC42 - Trapping Rain Water|LC42 - Trapping Rain Water]].

# Trapping Rain Water: Solution Patterns

The "Trapping Rain Water" problem (LC42) asks to calculate how much rainwater can be trapped between bars of varying heights represented by an array. The core insight is that the water trapped at any position `i` depends on the height of the walls to its left and right.

## 💧 Core Insight

For any bar `height[i]`, the amount of water it can trap above it is determined by:
`water_at_i = min(max_height_to_left[i], max_height_to_right[i]) - height[i]`
If this value is negative (i.e., `height[i]` is taller than one of the "walls"), no water is trapped at `i`, so it's 0.

```tikz
\begin{tikzpicture}[
    bar/.style={rectangle, draw, fill=blue!30, minimum width=0.5cm, anchor=south},
    water/.style={rectangle, fill=cyan!50, minimum width=0.5cm, anchor=south},
    label_style/.style={font=\sffamily\small}
]
    % Bars: height = [0,1,0,2,1,0,1,3,2,1,2,1]
    \node[bar, minimum height=0cm] at (0,0) {}; \node[label_style, below=0cm of {(0,0)}] {$h_0=0$};
    \node[bar, minimum height=1cm] at (1,0) {}; \node[label_style, below=0cm of {(1,0)}] {$h_1=1$};
    \node[bar, minimum height=0cm] (bar_i) at (2,0) {}; \node[label_style, below=0cm of {(2,0)}] {$h_2=0$};
    \node[bar, minimum height=2cm] (l_max_for_i) at (3,0) {}; \node[label_style, below=0cm of {(3,0)}] {$h_3=2$};
    \node[bar, minimum height=1cm] at (4,0) {}; \node[label_style, below=0cm of {(4,0)}] {$h_4=1$};
    \node[bar, minimum height=0cm] at (5,0) {}; \node[label_style, below=0cm of {(5,0)}] {$h_5=0$};
    \node[bar, minimum height=1cm] at (6,0) {}; \node[label_style, below=0cm of {(6,0)}] {$h_6=1$};
    \node[bar, minimum height=3cm] (r_max_for_i) at (7,0) {}; \node[label_style, below=0cm of {(7,0)}] {$h_7=3$};
    % ... rest of bars

    % Focus on index i=2 (h_2=0)
    \node[label_style, above=1cm of bar_i] (focus_label) {Focus on $i=2$};
    \draw[->, red, thick, dashed] (focus_label) -- (bar_i.north);

    % L_max for i=2 (height[1]=1 is max left for h[0..1])
    % More accurately, for h[2], max_left includes h[0..1]
    % No, for index i, l_max is max(height[0...i-1]), r_max is max(height[i+1...n-1])
    % Labuladong uses max(height[0...i]) and max(height[i...n-1]) for l_max[i] and r_max[i] respectively.
    % Let's use Labuladong's: l_max[i] is max height on left *including* i.
    % water[i] = min(max_left_of_i, max_right_of_i) - height[i]
    % For i=2 (height=0):
    % True L_max (max of elements to its strict left, for wall): max(height[0], height[1]) = max(0,1) = 1.
    % True R_max (max of elements to its strict right, for wall): max(height[3]...height[end]) = 3.
    % Water level at i=2 = min(1,3) = 1. Water trapped = 1 - 0 = 1.

    % The article's diagram uses l_max as highest bar to left of i (exclusive), and r_max as highest bar to right of i (exclusive)
    % If we take the context of article's diagram for calculation at `height[i]`:
    % l_max_val = max(height[0...i-1])
    % r_max_val = max(height[i+1...n-1])
    % water_level_at_i = min(l_max_val, r_max_val)
    % water_trapped_at_i = max(0, water_level_at_i - height[i])

    % For the diagram in the problem, for index i=2 (height[2]=0):
    % Max bar to its left (up to index 1) is height[1]=1.
    % Max bar to its right (from index 3 onwards) is height[7]=3.
    % Water level at index 2 is min(1,3)=1. Water trapped = 1-0=1.
    \node[water, minimum height=1cm] at (2,0) {};
    \draw[<->, green, thick] (l_max_for_i.north east) ++(0.1, -0.2) -- ++(0.1, -1.8) node[midway, right, label_style]{$L_{max}[i]$ (wall)};
    \draw[<->, green, thick] (r_max_for_i.north west) ++(-0.1, -0.2) -- ++(-0.1, -2.8) node[midway, left, label_style]{$R_{max}[i]$ (wall)};
    \node[label_style, text width=6cm] at (3.5, -2) {Water at $i = \min(L_{max}, R_{max}) - height[i]$ \\ (where $L_{max}$ is max height in $0..i$, $R_{max}$ is max height in $i..N-1$ in one DP approach, or strict left/right for another view)};

\end{tikzpicture}
```

## Solution Approaches

### 1. Brute Force $O(N^2)$
For each bar `i` from `1` to `N-2`:
- Find `max_left = max(height[0...i-1])`.
- Find `max_right = max(height[i+1...N-1])`.
- `water_level = min(max_left, max_right)`.
- If `water_level > height[i]`, add `water_level - height[i]` to total.
Time: $O(N)$ for each `i` to find maxes, total $O(N^2)$. Space: $O(1)$.

### 2. Using Precomputed Maxes (DP-like) $O(N)$
Precompute `left_max[i]` (max height from `0` to `i`) and `right_max[i]` (max height from `i` to `N-1`).
- `left_max[i] = max(left_max[i-1], height[i])`.
- `right_max[i] = max(right_max[i+1], height[i])` (iterate from right to left for this).
Then, for each `i`:
- The actual left wall for `height[i]` is `left_max[i-1]`.
- The actual right wall for `height[i]` is `right_max[i+1]`.
- Or, if `left_max[i]` includes `height[i]`: water at `i` is `min(left_max[i], right_max[i]) - height[i]`. This is Labuladong's formulation.
Labuladong's article shows arrays `l_max` and `r_max`.
```python
# class SolutionLC42_DP:
#     def trap(self, height: list[int]) -> int:
#         n = len(height)
#         if n <= 2: return 0
#         ans = 0
#         l_max = [0] * n
#         r_max = [0] * n

#         l_max[0] = height[0]
#         for i in range(1, n):
#             l_max[i] = max(height[i], l_max[i-1])

#         r_max[n-1] = height[n-1]
#         for i in range(n-2, -1, -1):
#             r_max[i] = max(height[i], r_max[i+1])

#         for i in range(1, n - 1): # Exclude ends as they can't trap water
#             water_level = min(l_max[i], r_max[i]) 
#             # If l_max[i] means max up to AND INCLUDING i from left, this is right.
#             # The water level is limited by the shorter of the two "accumulated maxes" at that point.
#             trapped_water = water_level - height[i]
#             if trapped_water > 0:
#                 ans += trapped_water
#         return ans
```
Time: $O(N)$ for 3 passes. Space: $O(N)$ for `l_max` and `r_max` arrays.

### 3. Two Pointers $O(N)$ Time, $O(1)$ Space (Optimized)
This is the most optimal approach.
- Maintain `left = 0`, `right = n-1`.
- Maintain `l_max_height = 0`, `r_max_height = 0`. (Max heights encountered so far from left and right ends respectively).
- `ans = 0`.
- Loop while `left <= right`:
    - `l_max_height = max(l_max_height, height[left])`
    - `r_max_height = max(r_max_height, height[right])`
    - **If `l_max_height < r_max_height`:**
        - The water level at `left` is determined by `l_max_height` (because `r_max_height` is even taller and won't be the bottleneck).
        - `ans += l_max_height - height[left]`
        - `left += 1`
    - **Else (`l_max_height >= r_max_height`):**
        - The water level at `right` is determined by `r_max_height`.
        - `ans += r_max_height - height[right]`
        - `right -= 1`
Return `ans`.

```python
class SolutionLC42_TwoPointers:
    def trap(self, height: list[int]) -> int:
        n = len(height)
        if n <= 2: return 0

        left, right = 0, n - 1
        l_max, r_max = 0, 0 # Max height seen from left and right so far
        ans = 0

        while left < right : # Stop when pointers meet/cross
            l_max = max(l_max, height[left])
            r_max = max(r_max, height[right])

            if l_max < r_max:
                # Water at 'left' is determined by l_max
                ans += l_max - height[left]
                left += 1
            else:
                # Water at 'right' is determined by r_max
                ans += r_max - height[right]
                right -= 1
        return ans
```
Labuladong's GIF `![](/algo/images/rain-water/2.gif)` shows this two-pointer movement.

## Complexity
- **DP with precomputed maxes:** Time $O(N)$, Space $O(N)$.
- **Two Pointers:** Time $O(N)$, Space $O(1)$.

## 总结 (Summary)
- Trapping rain water at a position `i` depends on `min(max_left_wall, max_right_wall) - height[i]`.
- Naive solution is $O(N^2)$.
- Precomputing max arrays (`l_max`, `r_max`) reduces time to $O(N)$ but uses $O(N)$ space.
- The optimized two-pointer approach achieves $O(N)$ time and $O(1)$ space by cleverly processing from both ends.

---
Parent: [[Interview/Concept/Algorithms/Array Manipulation/index|Array Manipulation]]
Related Problem: [[Interview/Practice/LeetCode/LC42 - Trapping Rain Water|LC42 - Trapping Rain Water]]
Related Concepts: [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]]
"""
    ensure_dir(os.path.dirname(filepath))
    write_file(filepath, content)
    
    array_manip_index_path = os.path.join(kb_root_path, "Interview/Concept/Algorithms/Array Manipulation/index.md")
    if not os.path.exists(array_manip_index_path):
        index_content = r"""---
tags: [index, concept/algorithms, concept/array_manipulation]
aliases: [Array Manipulation Index, Array Techniques]
---

# Array Manipulation Techniques

This section covers various techniques for efficient array manipulation.

## Core Techniques:
- [[Interview/Concept/Algorithms/Array Manipulation/Trapping Rain Water Pattern|Trapping Rain Water Pattern]]
"""
        write_file(array_manip_index_path, index_content)
        update_index_file(os.path.join(kb_root_path, "Interview/Concept/Algorithms/index.md"),
                          "- [[Interview/Concept/Algorithms/Array Manipulation/index|Array Manipulation]]",
                          "## Algorithm Categories / Patterns",
                          "## Core Concepts:")
    else:
        update_index_file(array_manip_index_path,
                          "- [[Interview/Concept/Algorithms/Array Manipulation/Trapping Rain Water Pattern|Trapping Rain Water Pattern]]",
                          "## Core Techniques:")

def create_lc42(kb_root_path):
    filepath = os.path.join(kb_root_path, "Interview/Practice/LeetCode/LC42 - Trapping Rain Water.md")
    content = r"""---
tags: [problem/leetcode, lc/hard, topic/array, pattern/two_pointers, pattern/dynamic_programming, course/labuladong, lc/lc42]
aliases: [LC42, Trapping Rain Water]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/如何高效解决接雨水问题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 42. Trapping Rain Water
> Solution approaches detailed in [[Interview/Concept/Algorithms/Array Manipulation/Trapping Rain Water Pattern|Trapping Rain Water Pattern]].

# LeetCode 42: Trapping Rain Water

## Problem Statement
Given `n` non-negative integers representing an elevation map where the width of each bar is 1, compute how much water it can trap after raining.

**Official Link:** [LeetCode 42. Trapping Rain Water](https://leetcode.com/problems/trapping-rain-water/)

## Solution Approach: Two Pointers (Optimized)
The most optimal solution uses a two-pointer approach with $O(N)$ time and $O(1)$ space. It relies on maintaining `l_max` (max height from left up to `left` pointer) and `r_max` (max height from right up to `right` pointer) and calculating trapped water based on the smaller of these two boundary maximums.

See [[Interview/Concept/Algorithms/Array Manipulation/Trapping Rain Water Pattern|Trapping Rain Water Pattern]] for detailed explanation of all approaches (Brute-Force, DP with precomputed maxes, and Two-Pointers).

### Python Solution (Two Pointers)
```python
class Solution:
    def trap(self, height: list[int]) -> int:
        n = len(height)
        if n <= 2: # Cannot trap water with 0, 1, or 2 bars
            return 0

        left, right = 0, n - 1
        l_max_height = 0 # Max height seen from the left up to 'left' pointer
        r_max_height = 0 # Max height seen from the right up to 'right' pointer
        trapped_water = 0

        while left < right:
            # Update current max heights from both ends
            l_max_height = max(l_max_height, height[left])
            r_max_height = max(r_max_height, height[right])

            # The crucial insight:
            # If l_max_height < r_max_height, then the water level at the `left` pointer
            # is determined by `l_max_height`. Any water trapped at `height[left]`
            # will be `l_max_height - height[left]`. We can safely process `left`
            # because we know `r_max_height` is greater, so it won't be the limiting factor
            # for the current `left` position.
            if l_max_height < r_max_height:
                trapped_water += l_max_height - height[left]
                left += 1
            # Else (l_max_height >= r_max_height), the water level at the `right` pointer
            # is determined by `r_max_height`.
            else:
                trapped_water += r_max_height - height[right]
                right -= 1
        
        return trapped_water
```

## Complexity Analysis
- **Time Complexity:** $O(N)$, one pass with two pointers.
- **Space Complexity:** $O(1)$.

## 总结 (Summary)
LC42 can be solved optimally using a two-pointer approach. By maintaining the maximum heights encountered from both left and right sides, we can determine the trapped water at each step by considering the shorter of the two "walls" (`l_max_height` and `r_max_height`) relative to the current bar being processed.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
"""
    write_file(filepath, content)

def create_lc11(kb_root_path):
    filepath = os.path.join(kb_root_path, "Interview/Practice/LeetCode/LC11 - Container With Most Water.md")
    content = r"""---
tags: [problem/leetcode, lc/medium, topic/array, pattern/two_pointers, course/labuladong, lc/lc11]
aliases: [LC11, Container With Most Water]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/如何高效解决接雨水问题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 11. Container With Most Water
> This problem is simpler than Trapping Rain Water but also uses a two-pointer approach. It's mentioned in the same Labuladong article as [[Interview/Practice/LeetCode/LC42 - Trapping Rain Water|LC42]].

# LeetCode 11: Container With Most Water

## Problem Statement
You are given an integer array `height` of length `n`. There are `n` vertical lines drawn such that the two endpoints of the `i`-th line are `(i, 0)` and `(i, height[i])`.
Find two lines that together with the x-axis form a container, such that the container contains the most water.
Return *the maximum amount of water a container can store*.
Notice that you cannot slant the container.

**Official Link:** [LeetCode 11. Container With Most Water](https://leetcode.com/problems/container-with-most-water/)

## Solution Approach: Two Pointers

The amount of water a container formed by lines at indices `i` and `j` (with `i < j`) can hold is `min(height[i], height[j]) * (j - i)`.
We want to maximize this value.

A brute-force approach would check all pairs, taking $O(N^2)$ time.
A more efficient two-pointer approach works in $O(N)$:
1.  Initialize `left = 0`, `right = len(height) - 1`.
2.  Initialize `max_area = 0`.
3.  Loop while `left < right`:
    a.  Calculate `current_width = right - left`.
    b.  Calculate `current_height = min(height[left], height[right])`.
    c.  `current_area = current_width * current_height`.
    d.  `max_area = max(max_area, current_area)`.
    e.  **Move the pointer pointing to the shorter line inward.** Why?
        - If we move the pointer of the taller line, the width decreases, and the height is still limited by the shorter line (or becomes even smaller if the new line is shorter). So, the area cannot increase.
        - If we move the pointer of the shorter line, the width decreases, but there's a *chance* that the new line encountered is taller, potentially leading to a larger height for the container, which might offset the decrease in width and result in a larger area.

### Python Solution
```python
class Solution:
    def maxArea(self, height: list[int]) -> int:
        left, right = 0, len(height) - 1
        max_area = 0

        while left < right:
            current_width = right - left
            # The height of the container is limited by the shorter line
            current_height = min(height[left], height[right])
            
            current_area = current_width * current_height
            max_area = max(max_area, current_area)
            
            # Move the pointer of the shorter line
            if height[left] < height[right]:
                left += 1
            else: # height[left] >= height[right]
                right -= 1
                
        return max_area
```

## Complexity Analysis
- **Time Complexity:** $O(N)$, as the `left` and `right` pointers each traverse the array at most once.
- **Space Complexity:** $O(1)$.

## 总结 (Summary)
LC11 is solved efficiently using a two-pointer approach. Start with the widest possible container (pointers at both ends). The area is limited by the shorter line. To potentially find a larger area, always move the pointer corresponding to the shorter line inward, as this is the only way the height of the container might increase.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Related Concepts: [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]]
"""
    write_file(filepath, content)


def create_lc659_split_array_pattern(kb_root_path):
    concept_filepath = os.path.join(kb_root_path, "Interview/Concept/Algorithms/Greedy Algorithms/Split Array into Consecutive Subsequences Pattern.md")
    concept_content = r"""---
tags: [concept/algorithms, pattern/greedy, pattern/heap, topic/array, course/labuladong]
aliases: [Split Array Consecutive Subsequences, 分割数组为连续子序列模式]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/谁能想到，斗地主也能玩出算法.md
---

> [!NOTE] Source Annotation
> Content conceptualized from Labuladong's "谁能想到，斗地主也能玩出算法" ([[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/谁能想到，斗地主也能玩出算法.md|Source]]), which addresses [[Interview/Practice/LeetCode/LC659 - Split Array into Consecutive Subsequences|LC659]]. The source article is currently "loading..." for its main content, so this outlines a common approach.

# Split Array into Consecutive Subsequences Pattern (LC659)

The problem of splitting a sorted array (possibly with duplicates) into one or more subsequences, where each subsequence consists of consecutive integers and has a minimum length (e.g., at least 3), is a common algorithmic challenge. [[Interview/Practice/LeetCode/LC659 - Split Array into Consecutive Subsequences|LC659]] is a prime example.

## 🎯 Core Idea: Greedy Approach with Frequency Maps and End-Tracking

A common and effective approach is greedy:
1.  **Frequency Map (`freq`):** Count the occurrences of each number in the input array `nums`.
2.  **End Map (`need` or `tails`):** Keep track of the number of consecutive subsequences that *end* at a particular number `x`. That is, `need[x]` (or `tails[x]`) would be the count of subsequences currently ending with `x-1` and are looking for `x` to continue.

**Algorithm Intuition:**
Iterate through each number `num` in `nums`:
-   If `freq[num] == 0`, this `num` has already been used up; continue.
-   **Option 1: Append to an existing subsequence.**
    -   If `need[num] > 0` (there's a subsequence ending at `num-1` waiting for `num`):
        -   Decrement `freq[num]` (use one `num`).
        -   Decrement `need[num]` (one less subsequence needs `num`).
        -   Increment `need[num+1]` (this subsequence now ends at `num` and needs `num+1`).
-   **Option 2: Start a new subsequence.**
    -   Else if `num` can start a new subsequence of length at least 3 (i.e., `freq[num] > 0`, `freq[num+1] > 0`, `freq[num+2] > 0`):
        -   Decrement `freq[num]`, `freq[num+1]`, `freq[num+2]`.
        -   Increment `need[num+3]` (a new subsequence `[num, num+1, num+2]` is formed, now needing `num+3`).
-   **Option 3: Cannot place `num`.**
    -   Else (cannot append `num` to an existing subsequence and cannot start a new one):
        -   It's impossible to partition; return `False`.

If all numbers are processed successfully, return `True`.

## Python Implementation Sketch (for LC659)
```python
import collections

class SolutionLC659:
    def isPossible(self, nums: list[int]) -> bool:
        freq = collections.Counter(nums)
        # need[x] stores the number of consecutive subsequences
        # that previously ended at x-1 and are now looking for x.
        need = collections.defaultdict(int)

        for num in nums:
            if freq[num] == 0: # This number has already been used
                continue

            if need[num] > 0: # num can extend an existing subsequence
                freq[num] -= 1
                need[num] -= 1
                need[num + 1] += 1
            elif freq[num] > 0 and freq.get(num + 1, 0) > 0 and freq.get(num + 2, 0) > 0:
                # num can start a new subsequence [num, num+1, num+2]
                freq[num] -= 1
                freq[num + 1] -= 1
                freq[num + 2] -= 1
                need[num + 3] += 1 # This new subsequence now needs num+3
            else:
                # Cannot place num
                return False
        
        return True
```

## Complexity
- **Time Complexity:** $O(N)$ for iterating through `nums` and frequency map operations.
- **Space Complexity:** $O(N)$ for `freq` and `need` maps in the worst case (all unique numbers).

## 总结 (Summary)
- The "Split Array into Consecutive Subsequences" problem (LC659) can be solved greedily.
- Use a frequency map for available numbers and another map (`need` or `tails`) to track how many subsequences are looking for the next number.
- Prioritize appending to existing subsequences over starting new ones.
- If a number cannot be placed, the partitioning is impossible.

---
Parent: [[Interview/Concept/Algorithms/Greedy Algorithms/index|Greedy Algorithms Index]]
Related Problem: [[Interview/Practice/LeetCode/LC659 - Split Array into Consecutive Subsequences|LC659]]
"""
    write_file(concept_filepath, concept_content)
    update_index_file(os.path.join(kb_root_path, "Interview/Concept/Algorithms/Greedy Algorithms/index.md"),
                      "- [[Interview/Concept/Algorithms/Greedy Algorithms/Split Array into Consecutive Subsequences Pattern|Split Array into Consecutive Subsequences Pattern]]",
                      "## Core Concepts:")

def create_lc659(kb_root_path):
    filepath = os.path.join(kb_root_path, "Interview/Practice/LeetCode/LC659 - Split Array into Consecutive Subsequences.md")
    content = r"""---
tags: [problem/leetcode, lc/medium, topic/array, topic/greedy, topic/heap, pattern/greedy_choice, lc/lc659, course/labuladong]
aliases: [LC659, Split Array into Consecutive Subsequences]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/谁能想到，斗地主也能玩出算法.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 659. Split Array into Consecutive Subsequences
> Discussed in [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/谁能想到，斗地主也能玩出算法.md|谁能想到，斗地主也能玩出算法 by Labuladong]].
> Solution uses pattern from [[Interview/Concept/Algorithms/Greedy Algorithms/Split Array into Consecutive Subsequences Pattern|Split Array into Consecutive Subsequences Pattern]].

# LeetCode 659: Split Array into Consecutive Subsequences

## Problem Statement
You are given an integer array `nums` that is sorted in non-decreasing order.
Determine if it is possible to split `nums` into one or more subsequences such that both of the following conditions are true:
- Each subsequence is a consecutive increasing sequence (i.e. each integer is exactly one more than the previous integer).
- All subsequences have a length of `3` or more.
Return `true` *if you can split `nums` according to the above conditions, or `false` otherwise*.

**Official Link:** [LeetCode 659. Split Array into Consecutive Subsequences](https://leetcode.com/problems/split-array-into-consecutive-subsequences/)

## Solution Approach: Greedy with Frequency and Need Maps

This problem is solved using a greedy strategy as detailed in [[Interview/Concept/Algorithms/Greedy Algorithms/Split Array into Consecutive Subsequences Pattern|Split Array into Consecutive Subsequences Pattern]].
We use two hash maps:
1. `freq`: To store the frequency of each number in `nums`.
2. `need`: To store how many subsequences are currently ending at `x-1` and thus "need" `x` to continue.

### Python Solution
```python
import collections

class Solution:
    def isPossible(self, nums: list[int]) -> bool:
        freq = collections.Counter(nums)
        # need[x] = count of subsequences ending at x-1, looking for x
        need = collections.defaultdict(int)

        for num in nums:
            if freq[num] == 0: # num already used up
                continue

            if need[num] > 0:
                # num can append to an existing subsequence
                freq[num] -= 1
                need[num] -= 1      # One less subsequence needs num
                need[num + 1] += 1  # This subsequence now needs num + 1
            elif freq[num] > 0 and freq.get(num + 1, 0) > 0 and freq.get(num + 2, 0) > 0:
                # num can start a new subsequence [num, num+1, num+2]
                freq[num] -= 1
                freq[num + 1] -= 1
                freq[num + 2] -= 1
                need[num + 3] += 1 # This new subsequence now needs num + 3
            else:
                # num cannot be placed
                return False
        
        return True
```

## Complexity Analysis
- **Time Complexity:** $O(N)$, where $N$ is the length of `nums`. We iterate through `nums` once, and hash map operations are $O(1)$ on average.
- **Space Complexity:** $O(N)$ for the `freq` and `need` hash maps in the worst case (if all numbers are distinct).

## 总结 (Summary)
LC659 asks if a sorted array can be partitioned into consecutive subsequences of length at least 3. A greedy approach works: for each number, try to append it to an existing valid subsequence. If not possible, try to start a new valid subsequence with it. If neither is possible, the partitioning fails. Frequency maps help manage available numbers and needed numbers efficiently.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
"""
    write_file(filepath, content)

def create_interval_problem_strategy(kb_root_path):
    filepath = os.path.join(kb_root_path, "Interview/Concept/Algorithms/Interval Problems/00 - General Interval Problem Solving Strategy.md")
    content = r"""---
tags: [concept/algorithms, pattern/interval_problems, topic/array, topic/sorting, topic/greedy, course/labuladong]
aliases: [Interval Problem Strategy, Solving Interval Questions, 区间问题解题方法]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/一个方法解决三道区间问题.md
---

> [!NOTE] Source Annotation
> Content conceptualized from Labuladong's "一个方法解决三道区间问题" ([[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/一个方法解决三道区间问题.md|Source]]), which discusses problems like [[Interview/Practice/LeetCode/LC1288 - Remove Covered Intervals|LC1288]], [[Interview/Practice/LeetCode/LC56 - Merge Intervals|LC56]], and [[Interview/Practice/LeetCode/LC986 - Interval List Intersections|LC986]]. The source file is "loading..." for main content, so this outlines general strategies.

# General Strategy for Solving Interval Problems

Interval problems are common in coding interviews. They typically involve a list of intervals, where each interval is represented by a start and end point `[start, end]`. The goal is often to merge overlapping intervals, find intersections, identify non-overlapping sets, etc.

## 🔑 Core Techniques

Labuladong usually emphasizes two key techniques for interval problems:

1.  **Sorting:**
    *   The most crucial first step is almost always to sort the intervals.
    *   **Common Sorting Criteria:**
        *   Sort by **start points** in ascending order.
        *   If start points are equal, sort by **end points** (either ascending or descending, depending on the problem logic). For example, in "Remove Covered Intervals" (LC1288), if starts are equal, sorting by end points descending helps identify covered intervals more easily.
    *   Sorting brings order to the intervals, allowing for efficient linear scan or merging logic.

2.  **Visualizing / Drawing:**
    *   Manually draw out examples of interval relationships on a number line. This helps to understand all possible cases of overlap, containment, or disjointness.
    *   Consider cases like:
        *   Interval A completely before Interval B.
        *   Interval A completely after Interval B.
        *   Interval A overlaps with Interval B (various ways: A starts first, B starts first, A contains B, B contains A).
        *   Interval A adjacent to Interval B.

```tikz
\begin{tikzpicture}[
    interval/.style={draw, very thick, -latex}, % Use -latex for interval appearance
    line_axis/.style={->, gray, thin},
    label_style/.style={font=\sffamily\small, above=2pt}
]
    % Axis
    \draw[line_axis] (-1,0) -- (11,0) node[right] {Timeline};
    \foreach \x in {0,...,10} { \draw (\x,0.1) -- (\x,-0.1) node[below, font=\tiny] {\x}; }

    % Case 1: A before B, no overlap
    \node[label_style] at (1.5, 1.5) {A: [0,2], B: [3,5] (No Overlap)};
    \draw[interval, blue] (0,1) -- (2,1) node[midway, label_style, black] {A};
    \draw[interval, red] (3,1) -- (5,1) node[midway, label_style, black] {B};

    % Case 2: A overlaps B (A starts first)
    \node[label_style] at (1.5, -0.5) {A: [1,4], B: [3,6] (Overlap)};
    \draw[interval, blue] (1,-1) -- (4,-1) node[midway, label_style, black] {A};
    \draw[interval, red] (3,-1.5) -- (6,-1.5) node[midway, label_style, black] {B};
    
    % Case 3: A contains B
    \node[label_style] at (7.5, 1.5) {A: [6,10], B: [7,8] (A contains B)};
    \draw[interval, blue] (6,1) -- (10,1) node[midway, label_style, black] {A};
    \draw[interval, red, yshift=-0.2cm] (7,1) -- (8,1) node[midway, label_style, black, yshift=-0.1cm] {B};

    % Case 4: B contains A (similar to above)
    \node[label_style] at (7.5, -0.5) {A: [7,8], B: [6,10] (B contains A)};
    \draw[interval, blue] (7,-1.5) -- (8,-1.5) node[midway, label_style, black] {A};
    \draw[interval, red, yshift=0.2cm] (6,-1.5) -- (10,-1.5) node[midway, label_style, black, yshift=0.1cm] {B};

\end{tikzpicture}
```

## Common Interval Problem Patterns

### 1. Merging Overlapping Intervals (e.g., [[Interview/Practice/LeetCode/LC56 - Merge Intervals|LC56]])
-   **Strategy:**
    1.  Sort intervals by start points.
    2.  Initialize `merged_intervals` with the first interval.
    3.  Iterate through the rest of the sorted intervals:
        -   If the current interval overlaps with the last interval in `merged_intervals`, merge them (update the end point of the last merged interval).
        -   Else (no overlap), add the current interval to `merged_intervals`.
-   **Overlap Condition:** `current_interval.start <= last_merged_interval.end`.
-   **Merge Logic:** `last_merged_interval.end = max(last_merged_interval.end, current_interval.end)`.

### 2. Removing Covered Intervals (e.g., [[Interview/Practice/LeetCode/LC1288 - Remove Covered Intervals|LC1288]])
-   An interval `[a,b]` is covered by `[c,d]` if `c <= a` and `b <= d`.
-   **Strategy:**
    1.  Sort intervals by start points (ascending). If start points are equal, sort by end points (descending). This places larger covering intervals first when starts are the same.
    2.  Iterate through sorted intervals, keeping track of the `previous_interval`. If the `current_interval` is covered by `previous_interval`, increment cover count. Otherwise, update `previous_interval = current_interval`.

### 3. Interval List Intersections (e.g., [[Interview/Practice/LeetCode/LC986 - Interval List Intersections|LC986]])
-   Given two sorted lists of disjoint intervals, find their intersections.
-   **Strategy (Two Pointers):**
    1.  Use two pointers, `i` for `listA` and `j` for `listB`.
    2.  While `i < len(listA)` and `j < len(listB)`:
        -   Calculate potential intersection: `intersect_start = max(A[i].start, B[j].start)`, `intersect_end = min(A[i].end, B[j].end)`.
        -   If `intersect_start <= intersect_end`, an intersection `[intersect_start, intersect_end]` exists; add it to results.
        -   Advance the pointer of the interval that finishes earlier:
            -   If `A[i].end < B[j].end`, increment `i`.
            -   Else, increment `j`.

## 总结 (Summary)
- Solving interval problems usually starts with **sorting** the intervals, typically by their start points.
- **Visualizing** the different ways intervals can relate to each other (overlap, containment, disjoint) is crucial for deriving the correct logic.
- Common patterns involve merging overlapping intervals, finding intersections using two pointers, or identifying covered/non-overlapping intervals.
- Many interval problems can be solved with greedy approaches after sorting, often in $O(N \log N)$ time (dominated by sort) and $O(N)$ or $O(1)$ space (for output or in-place modifications).

---
Parent: [[Interview/Concept/Algorithms/Interval Problems/index|Interval Problem Patterns Index]]
Related: [[Interview/Concept/Algorithms/Greedy Algorithms/Interval Scheduling Pattern|Interval Scheduling Pattern]] (a specific type of interval problem)
"""
    write_file(filepath, content)
    # Create an index file for the new Interval Problems folder if it doesn't exist
    interval_index_path = os.path.join(kb_root_path, "Interview/Concept/Algorithms/Interval Problems/index.md")
    if not os.path.exists(interval_index_path):
        index_content = r"""---
tags: [index, concept/algorithms, topic/interval]
aliases: [Interval Algorithms Index]
---
# Interval Problem Patterns

This section covers algorithmic patterns related to interval-based problems.

- [[Interview/Concept/Algorithms/Interval Problems/00 - General Interval Problem Solving Strategy|General Interval Problem Solving Strategy]]
"""
        write_file(interval_index_path, index_content)
        update_index_file(os.path.join(kb_root_path, "Interview/Concept/Algorithms/index.md"),
                          "- [[Interview/Concept/Algorithms/Interval Problems/index|Interval Problems]]",
                          "## Algorithm Categories / Patterns",
                          "## Core Concepts:")
    else:
        update_index_file(interval_index_path,
                          "- [[Interview/Concept/Algorithms/Interval Problems/00 - General Interval Problem Solving Strategy|General Interval Problem Solving Strategy]]",
                          "## Core Concepts:")

def create_lc1288(kb_root_path):
    filepath = os.path.join(kb_root_path, "Interview/Practice/LeetCode/LC1288 - Remove Covered Intervals.md")
    content = r"""---
tags: [problem/leetcode, lc/medium, topic/array, pattern/interval_problems, pattern/sorting, course/labuladong, lc/lc1288]
aliases: [LC1288, Remove Covered Intervals]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/一个方法解决三道区间问题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 1288. Remove Covered Intervals
> Solution idea from [[Interview/Concept/Algorithms/Interval Problems/00 - General Interval Problem Solving Strategy|General Interval Problem Solving Strategy]].

# LeetCode 1288: Remove Covered Intervals

## Problem Statement
Given an array `intervals` where `intervals[i] = [li, ri]` represent the interval `[li, ri)`, return *the number of remaining intervals after removing all intervals that are covered by another interval in the list*.
An interval `[a, b)` is covered by an interval `[c, d)` if and only if `c <= a` and `b <= d`.

**Official Link:** [LeetCode 1288. Remove Covered Intervals](https://leetcode.com/problems/remove-covered-intervals/)

## Solution Approach: Sorting
1.  **Sort intervals:** Sort by start point ascending. If start points are equal, sort by end point descending.
    This ensures that if `[c,d)` potentially covers `[a,b)` and `c=a`, the longer interval `[c,d)` comes first.
2.  **Iterate and Count:**
    - Initialize `count = 0` (non-covered intervals).
    - Initialize `prev_end = -1` (or `float('-inf')`).
    - For each interval `[start, end]` in the sorted list:
        - If `end > prev_end` (current interval is not covered by the "effective" previous non-covered interval):
            - Increment `count`.
            - Update `prev_end = end`.
        - (If `end <= prev_end`, it means the current interval is covered by some previous interval that extended at least as far as `prev_end`. Because we sorted by start points, and for equal start points by descending end points, any interval that *could* have covered this one and started at the same time would have been processed earlier and set a `prev_end` that covers the current interval.)
3.  Return `count`.

The logic is subtly about merging or extending coverage.
A more robust way to think:
- Keep track of `merged_start` and `merged_end` of the current "dominant" interval.
- If the current interval `[s, e]` is covered by `[merged_start, merged_end]`, it's removed.
- If it extends `merged_end`, update `merged_end`.
- If it's a new, disjoint interval, increment count and update `merged_start, merged_end`.

Let's follow Labuladong's typical logic for interval problems more closely if available, or a standard greedy approach.
The initial sort (start asc, end desc) is key.
`count_removed = 0`
`prev_start, prev_end = intervals[0]`
For interval `curr = intervals[i]` (from 1 to N-1):
  If `curr.start >= prev_start` and `curr.end <= prev_end`: `count_removed++`
  Else (not covered by prev):
    `prev_start = curr.start`
    `prev_end = curr.end`
Return `N - count_removed`.

### Python Solution
```python
class Solution:
    def removeCoveredIntervals(self, intervals: list[list[int]]) -> int:
        # Sort by start time ascending. If start times are equal,
        # sort by end time descending (longer interval comes first).
        intervals.sort(key=lambda x: (x[0], -x[1]))
        
        count = 0 # Count of non-covered intervals
        # prev_end tracks the maximum end point of the intervals considered so far
        # that form the current "covering" extent.
        prev_end = -1 # Or float('-inf')

        for start, end in intervals:
            # If the current interval's end extends beyond the previous max end,
            # it means this interval is not covered by the previous ones that started
            # at or before it (due to sorting).
            if end > prev_end:
                count += 1
                prev_end = end # Update the max reach
            # If end <= prev_end, this interval is covered by a previous one
            # that started at the same point (and was longer) or started earlier
            # and extended at least as far as prev_end.
            
        return count
```

## Complexity Analysis
- **Time Complexity:** $O(N \log N)$ for sorting. The iteration is $O(N)$.
- **Space Complexity:** $O(1)$ or $O(N)$ depending on sort.

## 总结 (Summary)
LC1288 requires finding the number of non-covered intervals. Sorting by start point (asc) and then by end point (desc for ties) is crucial. Then, iterate through the sorted intervals, keeping track of the maximum end point reached by a non-covered interval. If a new interval extends this maximum end point, it's also non-covered.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
"""
    write_file(filepath, content)

def create_lc56(kb_root_path):
    filepath = os.path.join(kb_root_path, "Interview/Practice/LeetCode/LC56 - Merge Intervals.md")
    content = r"""---
tags: [problem/leetcode, lc/medium, topic/array, pattern/interval_problems, pattern/sorting, pattern/merging_intervals, course/labuladong, lc/lc56]
aliases: [LC56, Merge Intervals]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/一个方法解决三道区间问题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 56. Merge Intervals
> Solution idea from [[Interview/Concept/Algorithms/Interval Problems/00 - General Interval Problem Solving Strategy|General Interval Problem Solving Strategy]].

# LeetCode 56: Merge Intervals

## Problem Statement
Given an array of `intervals` where `intervals[i] = [start_i, end_i]`, merge all overlapping intervals, and return *an array of the non-overlapping intervals that cover all the intervals in the input*.

**Official Link:** [LeetCode 56. Merge Intervals](https://leetcode.com/problems/merge-intervals/)

## Solution Approach: Sort + Merge
1.  **Sort intervals by start points.** This is crucial.
2.  Initialize `merged = []`. If `intervals` is empty, return `merged`.
3.  Add the first interval from sorted `intervals` to `merged`.
4.  Iterate through the rest of the sorted `intervals` (from the second interval onwards):
    - Let `last_merged_interval = merged[-1]`.
    - Let `current_interval = intervals[i]`.
    - **If `current_interval[0] <= last_merged_interval[1]` (Overlap Condition):**
        - There's an overlap. Merge `current_interval` into `last_merged_interval`.
        - `last_merged_interval[1] = max(last_merged_interval[1], current_interval[1])`.
    - **Else (No Overlap):**
        - Add `current_interval` to `merged`.
5.  Return `merged`.

### Python Solution
```python
class Solution:
    def merge(self, intervals: list[list[int]]) -> list[list[int]]:
        if not intervals:
            return []

        # Sort intervals by their start times
        intervals.sort(key=lambda x: x[0])

        merged_intervals = []
        merged_intervals.append(intervals[0]) # Add the first interval

        for i in range(1, len(intervals)):
            current_start, current_end = intervals[i]
            last_merged_start, last_merged_end = merged_intervals[-1]

            if current_start <= last_merged_end: # Overlap
                # Merge: update the end of the last interval in merged_intervals
                merged_intervals[-1][1] = max(last_merged_end, current_end)
            else: # No overlap
                merged_intervals.append([current_start, current_end])
                
        return merged_intervals
```

## Complexity Analysis
- **Time Complexity:** $O(N \log N)$ for sorting. The merging pass is $O(N)$.
- **Space Complexity:** $O(N)$ for storing the `merged_intervals` list (in the worst case, no intervals merge). $O(N)$ or $O(\log N)$ for sorting, depending on implementation.

## 总结 (Summary)
LC56 asks to merge overlapping intervals. The standard approach is to sort intervals by start times, then iterate through them, merging with the last interval in the result list if an overlap exists, or adding as a new interval otherwise.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
"""
    write_file(filepath, content)

def create_lc986(kb_root_path):
    filepath = os.path.join(kb_root_path, "Interview/Practice/LeetCode/LC986 - Interval List Intersections.md")
    content = r"""---
tags: [problem/leetcode, lc/medium, topic/array, pattern/interval_problems, pattern/two_pointers, course/labuladong, lc/lc986]
aliases: [LC986, Interval List Intersections]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/一个方法解决三道区间问题.md
---

> [!NOTE] Source Annotation
> Problem: LeetCode 986. Interval List Intersections
> Solution idea from [[Interview/Concept/Algorithms/Interval Problems/00 - General Interval Problem Solving Strategy|General Interval Problem Solving Strategy]].

# LeetCode 986: Interval List Intersections

## Problem Statement
You are given two lists of closed intervals, `firstList` and `secondList`, where `firstList[i] = [start_i, end_i]` and `secondList[j] = [start_j, end_j]`. Each list of intervals is pairwise disjoint and in sorted order.
Return *the intersection of these two interval lists*.
A closed interval `[a, b]` (with `a <= b`) denotes the set of real numbers `x` with `a <= x <= b`.
The intersection of two closed intervals is a set of real numbers that are either empty or represented as a closed interval. For example, the intersection of `[1, 3]` and `[2, 4]` is `[2, 3]`.

**Official Link:** [LeetCode 986. Interval List Intersections](https://leetcode.com/problems/interval-list-intersections/)

## Solution Approach: Two Pointers
Since both input lists are sorted and disjoint, we can use a two-pointer approach.
1.  Initialize two pointers, `i = 0` for `firstList` and `j = 0` for `secondList`.
2.  Initialize an empty list `intersections`.
3.  While `i < len(firstList)` and `j < len(secondList)`:
    a.  Let `interval1 = firstList[i]` and `interval2 = secondList[j]`.
    b.  Calculate the potential intersection:
        `start_intersect = max(interval1[0], interval2[0])`
        `end_intersect = min(interval1[1], interval2[1])`
    c.  If `start_intersect <= end_intersect` (a valid intersection exists):
        Add `[start_intersect, end_intersect]` to `intersections`.
    d.  Advance the pointer of the interval that finishes earlier:
        If `interval1[1] < interval2[1]`, increment `i`.
        Else (if `interval2[1] < interval1[1]`), increment `j`.
        Else (if `interval1[1] == interval2[1]`), increment both `i` and `j`.
4.  Return `intersections`.

### Python Solution
```python
class Solution:
    def intervalIntersection(self, firstList: list[list[int]], secondList: list[list[int]]) -> list[list[int]]:
        intersections = []
        i, j = 0, 0
        
        while i < len(firstList) and j < len(secondList):
            start1, end1 = firstList[i]
            start2, end2 = secondList[j]
            
            # Calculate intersection
            intersect_start = max(start1, start2)
            intersect_end = min(end1, end2)
            
            if intersect_start <= intersect_end:
                intersections.append([intersect_start, intersect_end])
            
            # Move pointer of the interval that ends earlier
            if end1 < end2:
                i += 1
            elif end2 < end1:
                j += 1
            else: # Both end at the same time
                i += 1
                j += 1
                
        return intersections
```

## Complexity Analysis
- **Time Complexity:** $O(M + N)$, where $M$ is the length of `firstList` and $N$ is the length of `secondList`. Each pointer traverses its list once.
- **Space Complexity:** $O(P)$, where $P$ is the number of intersecting intervals. In the worst case, this can be $O(M+N)$.

## 总结 (Summary)
LC986 finds intersections of two sorted lists of disjoint intervals. A two-pointer approach efficiently identifies overlapping segments by comparing current intervals from each list and advancing the pointer of the interval that finishes sooner.

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
"""
    write_file(filepath, content)

# --- Main Script Execution ---
if __name__ == "__main__":
    script_dir = os.path.dirname(os.path.abspath(__file__)) 
    kb_root_path = os.path.join(script_dir, "../") # Adjust to your KB root directory
    kb_root_path = os.path.normpath(kb_root_path)

    print(f"Knowledge base operations will be relative to: {kb_root_path}")

    # --- Perfect Rectangle (LC391) ---
    # Create concept note for Perfect Rectangle
    create_perfect_rectangle_concept(kb_root_path)
    # Create LeetCode problem note for LC391
    create_lc391(kb_root_path)
    # Update LeetCode index
    update_leetcode_index(kb_root_path, "391", "Perfect Rectangle", "lc/hard, topic/geometry, pattern/hashing", "Hard")

    # --- Trapping Rain Water (LC42, LC11) ---
    # Create concept note for Trapping Rain Water Pattern
    create_trapping_rain_water_pattern(kb_root_path)
    # Create LeetCode problem note for LC42
    create_lc42(kb_root_path)
    # Create LeetCode problem note for LC11
    create_lc11(kb_root_path)
    # Update LeetCode index
    update_leetcode_index(kb_root_path, "42", "Trapping Rain Water", "lc/hard, pattern/two_pointers, pattern/dynamic_programming", "Hard")
    update_leetcode_index(kb_root_path, "11", "Container With Most Water", "lc/medium, pattern/two_pointers", "Medium")

    # --- Interval Problems (LC1288, LC56, LC986) ---
    # Create general concept note for Interval Problems
    create_interval_problem_strategy(kb_root_path)
    # Create LeetCode problem notes (placeholders as source is loading)
    create_lc1288(kb_root_path)
    create_lc56(kb_root_path)
    create_lc986(kb_root_path)
    # Update LeetCode index
    update_leetcode_index(kb_root_path, "1288", "Remove Covered Intervals", "lc/medium, pattern/interval_problems", "Medium")
    update_leetcode_index(kb_root_path, "56", "Merge Intervals", "lc/medium, pattern/interval_problems", "Medium")
    update_leetcode_index(kb_root_path, "986", "Interval List Intersections", "lc/medium, pattern/interval_problems", "Medium")

    # --- Split Array into Consecutive Subsequences (LC659) ---
    # Create concept note
    create_lc659_split_array_pattern(kb_root_path)
    # Create LeetCode problem note
    create_lc659(kb_root_path)
    # Update LeetCode index
    update_leetcode_index(kb_root_path, "659", "Split Array into Consecutive Subsequences", "lc/medium, topic/greedy, topic/heap", "Medium")

    # Placeholder notes from the initial provided knowledge base (just ensure they exist if touched upon)
    # (These were already in the provided `AAA - Interview Knowledge Base.md` but making sure links will work)
    # Note: The content for these will be based on the logic in the existing AAA file.
    # This script part mainly ensures these LC files are created for linking and indexing.
    # For this script, I'll focus on structure and let the LLM fill based on the AAA content previously provided.
    
    lc_notes_from_aaa = {
        "LC509": ("Fibonacci Number", "Easy"),
        "LC322": ("Coin Change", "Medium"),
        "LC139": ("Word Break", "Medium"),
        "LC140": ("Word Break II", "Hard"),
        "LC300": ("Longest Increasing Subsequence", "Medium"),
        "LC354": ("Russian Doll Envelopes", "Hard"),
        "LC53": ("Maximum Subarray", "Medium"),
        "LC1143": ("Longest Common Subsequence", "Medium"),
        "LC583": ("Delete Operation for Two Strings", "Medium"),
        "LC712": ("Minimum ASCII Delete Sum for Two Strings", "Medium"),
        "LC516": ("Longest Palindromic Subsequence", "Medium"),
        "LC1312": ("Minimum Insertion Steps to Make a String Palindrome", "Hard"),
        # Stock trading placeholders
        "LC121": ("Best Time to Buy and Sell Stock", "Easy"),
        "LC122": ("Best Time to Buy and Sell Stock II", "Medium"),
        "LC123": ("Best Time to Buy and Sell Stock III", "Hard"),
        "LC188": ("Best Time to Buy and Sell Stock IV", "Hard"),
        "LC309": ("Best Time to Buy and Sell Stock with Cooldown", "Medium"),
        "LC714": ("Best Time to Buy and Sell Stock with Transaction Fee", "Medium"),
        # Game DP placeholders
        "LC312": ("Burst Balloons", "Hard"),
        "LC887": ("Super Egg Drop", "Hard"),
        "LC10": ("Regular Expression Matching", "Hard"),
        "LC787": ("Cheapest Flights Within K Stops", "Medium"), # Note was also LC787 - ... K Stops.md
        "LC64": ("Minimum Path Sum", "Medium"),
        # House Robber placeholders
        "LC198": ("House Robber", "Medium"),
        "LC213": ("House Robber II", "Medium"),
        "LC337": ("House Robber III", "Medium"),
    }

    for lc_id, (name, diff) in lc_notes_from_aaa.items():
        safe_name = "".join(c if c.isalnum() or c in (' ', '-') else '_' for c in name).replace(' ', '_')
        problem_file = os.path.join(kb_root_path, f"Interview/Practice/LeetCode/{lc_id} - {safe_name}.md")
        # If the problem file already exists (e.g., it was one of the primary files processed above)
        # this will just confirm its presence for linking, or create a placeholder if missing.
        if not os.path.exists(problem_file):
            content = f"""---
tags: [problem/leetcode, lc/{diff.lower()}, lc/{lc_id.lower()}, topic/placeholder, pattern/placeholder, course/labuladong_mention]
aliases: [{lc_id}, {name}]
---
# {lc_id}: {name}

## Problem Statement
(To be filled from LeetCode)

## Solution Approach
(To be filled, likely referencing concepts from Labuladong's notes or core algorithm patterns)

### Python Solution (Placeholder)
```python
class Solution:
    def solve(self, params): # Signature will vary
        # TODO: Implement solution
        pass
```

## Complexity Analysis
(To be filled)

## 总结 (Summary)
(To be filled)

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
"""
            write_file(problem_file, content)
        update_leetcode_index(kb_root_path, lc_id, name, f"lc/{diff.lower()}", diff.capitalize())

    print("Script finished. Knowledge base updated.")

