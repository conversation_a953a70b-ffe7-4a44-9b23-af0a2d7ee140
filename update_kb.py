import textwrap
import os

# Helper function to create directories if they don't exist
def ensure_dir(filepath):
    directory = os.path.dirname(filepath)
    if not os.path.exists(directory):
        os.makedirs(directory)
    print(f"Ensured directory: {directory}")

# --- Content Generation Functions ---

def create_divide_and_conquer_framework(kb_root):
    filepath = os.path.join(kb_root, "Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework and Principles.md")
    ensure_dir(filepath)
    content = r"""---
tags: [concept/algorithms, concept/divide_and_conquer, type/framework, concept/recursion]
aliases: [Divide and Conquer Algorithm, 分治算法框架, 分治思想]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/分治算法解题套路框架.md]].
> Labuladong distinguishes between the general "divide and conquer *thinking*" (分解问题思路) and specific "divide and conquer *algorithms*".

# Divide and Conquer: Framework and Principles

Divide and Conquer is a powerful algorithmic paradigm. It's important to distinguish between the broad "divide and conquer *thinking*" (which Labula<PERSON> calls "decomposition thinking" - 分解问题思路) and specific "divide and conquer *algorithms*".

## 🧠 Divide and Conquer Thinking (Decomposition)

This is a general approach to problem-solving, especially with [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|recursion]].
- **Core Idea:** Break down a large problem into smaller, self-similar subproblems. Solve the subproblems recursively. Combine their solutions to solve the original problem.
- **Examples:**
    - Fibonacci: `fib(n) = fib(n-1) + fib(n-2)`.
    - Counting nodes in a binary tree: `count(root) = count(root.left) + count(root.right) + 1`.
    - [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]] fundamentally uses this decomposition thinking, adding optimizations like memoization or tabulation.

Many recursive algorithms employ this decompositional thinking. However, not all of them are strictly termed "Divide and Conquer algorithms."

## 🎯 Divide and Conquer Algorithms (Narrow Definition)

A "Divide and Conquer algorithm" specifically refers to algorithms where **decomposing the problem and then solving leads to a better time complexity** than solving the problem directly without decomposition.

- **Key Characteristic:** The "divide" and "combine" steps, along with solving subproblems, result in overall efficiency gains.
- **Non-Example (Fibonacci):** The naive recursive Fibonacci `fib(n) = fib(n-1) + fib(n-2)` uses decomposition thinking but is *not* an efficient divide and conquer algorithm in this form because it has overlapping subproblems leading to exponential time complexity. [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]] techniques are needed to make it efficient.
- **Classic Examples:**
    - **[[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]]:** Divides array, sorts subarrays, merges sorted subarrays. $O(N \log N)$. Direct sorting methods like insertion sort are $O(N^2)$.
    - **[[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]]:** Partitions array, sorts subarrays. Average $O(N \log N)$.
    - **Binary Search:** Though simple, it divides the search space in half at each step.
    - **Closest Pair of Points:** A more advanced geometric algorithm.
    - **Strassen's Matrix Multiplication.**

Labuladong uses Bucket Sort as an example: directly using insertion sort is $O(N^2)$, but by dividing into buckets and then using insertion sort on smaller buckets, overall complexity can approach $O(N)$.

## 📜 General Steps of a Divide and Conquer Algorithm

1.  **Divide:** Break the problem into several smaller subproblems that are themselves smaller instances of the original problem.
2.  **Conquer:** Solve the subproblems recursively. If the subproblems are small enough (base case), solve them directly.
3.  **Combine:** Combine the solutions to the subproblems into the solution for the original problem.

```mermaid
graph TD
    P["Problem P"] --> D{"Divide"}
    D --> SP1["Subproblem P1"]
    D --> SP2["Subproblem P2"]
    D --> SPN["...Subproblem Pn"]

    SP1 --> C1["Conquer P1 (Recursive Call)"]
    SP2 --> C2["Conquer P2 (Recursive Call)"]
    SPN --> CN["Conquer Pn (Recursive Call)"]
    
    C1 --> S1["Solution S1"]
    C2 --> S2["Solution S2"]
    CN --> SN["Solution Sn"]

    S1 --> M{"Combine Solutions"}
    S2 --> M
    SN --> M
    M --> FinalS["Final Solution for P"]

    classDef step fill:#e6f2ff,stroke:#3366cc,stroke-width:2px;
    class D,C1,C2,CN,M step;
    classDef problem fill:#fff0b3,stroke:#ffcc00,stroke-width:2px;
    class P,SP1,SP2,SPN problem;
    classDef solution fill:#d6ffd6,stroke:#006400,stroke-width:2px;
    class S1,S2,SN,FinalS solution;
```

## Why Can Divide and Conquer Be More Efficient?

The efficiency gain often comes from how the problem size reduces and how the "combine" step is performed.
- If a problem of size $N$ is divided into $a$ subproblems of size $N/b$, and the divide/combine step takes $f(N)$ time, the recurrence relation is often of the form $T(N) = aT(N/b) + f(N)$. The Master Theorem can be used to solve such recurrences.
- If $f(N)$ is relatively small (e.g., $O(N)$ for Merge Sort where $a=2, b=2$), the overall complexity can be significantly better than a naive $O(N^2)$ approach.

## Example: Merge K Sorted Lists (LeetCode 23)

[[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Sorted Lists]] is a good example where divide and conquer provides an efficient solution.
- **Problem:** Merge $k$ sorted linked lists into one sorted linked list.
- **Naive approach:** Merge lists one by one. If average length is $L$, this is roughly $(L+L) + (2L+L) + ... + ((k-1)L+L) \approx O(k^2 L)$.
- **Divide and Conquer Approach:**
    1.  **Divide:** Split the $k$ lists into two halves: $k/2$ lists and $k/2$ lists.
    2.  **Conquer:** Recursively merge the lists in each half. This results in two sorted lists.
    3.  **Combine:** Merge the two resulting sorted lists (this takes $O(N_{total})$ where $N_{total}$ is total elements).
    - This approach is similar to Merge Sort and results in $O(N_{total} \log k)$ time.

```python
# Conceptual Python for Merge K Sorted Lists (Divide and Conquer)
# class ListNode: ...
# def merge_two_lists(l1, l2): ... (standard merge for two sorted lists)

# def merge_k_lists_dc(lists: list[ListNode], low: int, high: int) -> ListNode:
#     if low > high:
#         return None
#     if low == high: # Base case: single list
#         return lists[low]
    
#     mid = low + (high - low) // 2
    
#     # Conquer: recursively merge halves
#     left_merged = merge_k_lists_dc(lists, low, mid)
#     right_merged = merge_k_lists_dc(lists, mid + 1, high)
    
#     # Combine: merge the two sorted lists from subproblems
#     return merge_two_lists(left_merged, right_merged)

# # Initial call:
# # merged_list_head = merge_k_lists_dc(all_lists, 0, len(all_lists) - 1)
```
This structure is analogous to building a merge plan up a [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|binary tree]], where leaves are original lists and internal nodes are merged lists.

## 总结 (Summary)
- **Divide and Conquer Thinking (Decomposition):** A broad recursive strategy to break problems into subproblems and combine their solutions.
- **Divide and Conquer Algorithms:** Specific algorithms where this decomposition leads to a demonstrable improvement in time complexity compared to direct approaches.
- **Common Steps:** Divide, Conquer (recurse), Combine.
- Efficiency often analyzed using recurrence relations (e.g., Master Theorem).
- Classic examples include Merge Sort, Quick Sort, and problems like Merge K Sorted Lists.

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
Previous: [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursion Thinking Modes]]
Next: [[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Sorted Lists (Example)]]
Related: [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]], [[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]]
"""
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created: {filepath}")

def create_lc23_merge_k_sorted_lists(kb_root):
    filepath = os.path.join(kb_root, "Practice/LeetCode/LC23 - Merge K Sorted Lists.md")
    ensure_dir(filepath)
    content = r"""---
tags: [problem/leetcode, lc/hard, topic/linked_list, topic/heap, topic/divide_and_conquer, pattern/merge_k_sorted]
aliases: [LC23, Merge K Sorted Lists, 合并K个升序链表]
---

> [!NOTE] Source Annotation
> Problem: LeetCode 23. Merge K Sorted Lists.
> The Divide and Conquer solution is discussed in [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/分治算法解题套路框架.md]].
> A common alternative uses a Min-Heap.

# LeetCode 23: Merge K Sorted Lists

## Problem Statement

You are given an array of `k` linked-lists `lists`, each linked-list is sorted in ascending order.
Merge all the linked-lists into one sorted linked-list and return it.

**Official Link:** [LeetCode 23. Merge K Sorted Lists](https://leetcode.com/problems/merge-k-sorted-lists/)

**Example 1:**
Input: `lists = [[1,4,5],[1,3,4],[2,6]]`
Output: `[1,1,2,3,4,4,5,6]`

## Solution Approaches

### 1. Brute Force: Collect All and Sort
1. Iterate through all `k` lists, collect all node values into a single list.
2. Sort this list.
3. Create a new sorted linked list from the sorted values.
- **Time Complexity:** $O(N \log N)$, where $N$ is the total number of nodes across all lists. Dominated by sorting.
- **Space Complexity:** $O(N)$ for storing all values and for the new list.

### 2. Iterative Merging (One by One)
1. Take the first list as the initial merged list.
2. Iterate from the second list to the $k$-th list. In each step, merge the current merged list with the current list from the input array.
   - Merging two sorted lists of length $L_1$ and $L_2$ takes $O(L_1+L_2)$ time.
- **Time Complexity:** If average list length is $L_{avg}$, total nodes $N = k \cdot L_{avg}$.
  - Merge 1st and 2nd: $O(L_{avg} + L_{avg})$
  - Merge result (2 $L_{avg}$) with 3rd ($L_{avg}$): $O(2L_{avg} + L_{avg})$
  - ...
  - Sum is $O(L_{avg} \sum_{i=1}^{k-1} (i+1)) = O(L_{avg} \cdot k^2) = O(N \cdot k)$.
- **Space Complexity:** $O(1)$ if merging in-place (modifying pointers), or $O(N)$ for new list if not.

### 3. Using a Min-Heap (Priority Queue)
This is a very common and efficient approach.
1. Initialize a min-heap.
2. Add the head node of each of the `k` lists to the min-heap. The heap stores `(node.val, list_index, node_reference)` to handle value ties and access the node. Python's `heapq` can store `(value, tie_breaker_id, node)` where `tie_breaker_id` can be an increasing counter if node objects are not directly comparable for tie-breaking.
3. While the heap is not empty:
   a. Extract the node with the minimum value from the heap (let it be `min_node`).
   b. Add `min_node` to the result linked list.
   c. If `min_node.next` is not null, add `min_node.next` (from the same original list) to the heap.
- **Time Complexity:**
    - Adding $k$ initial heads to heap: $O(k \log k)$.
    - Total $N$ nodes. Each `pop` from heap is $O(\log k)$. Each `push` to heap is $O(\log k)$.
    - Total: $O(N \log k)$.
- **Space Complexity:** $O(k)$ for the heap (at most $k$ nodes in heap). $O(N)$ for the new list.

```python
import heapq

class ListNode:
    def __init__(self, val=0, next=None):
        self.val = val
        self.next = next
    # Need to add __lt__ for heapq if ListNode objects are directly stored and Python version < 3
    # or if values can be equal and nodes are not otherwise comparable.
    # A common trick is to store (value, id, node) where id is a unique counter.
    def __lt__(self, other): # Necessary for heapq with custom objects if values are equal
        return self.val < other.val


class SolutionHeap:
    def mergeKLists(self, lists: list[ListNode]) -> ListNode:
        min_heap = []
        # To handle cases where node values are equal and ListNode objects are not comparable
        # by default for tie-breaking in heapq, we can add a unique counter.
        unique_id = 0 
        for i, l_head in enumerate(lists):
            if l_head:
                heapq.heappush(min_heap, (l_head.val, unique_id, l_head))
                unique_id += 1
        
        dummy = ListNode(-1)
        current = dummy
        
        while min_heap:
            val, _, node = heapq.heappop(min_heap)
            current.next = node
            current = current.next
            
            if node.next:
                heapq.heappush(min_heap, (node.next.val, unique_id, node.next))
                unique_id += 1
                
        return dummy.next
```

### 4. Divide and Conquer
This approach recursively merges pairs of lists, then pairs of merged lists, and so on, similar to the merge step in Merge Sort. This is the method Labuladong refers to in the "分治算法解题套路框架" context.

1.  **Base Cases:**
    - If `lists` is empty, return `None`.
    - If `lists` contains one list, return that list.
2.  **Divide:** Split `lists` into two halves.
3.  **Conquer:** Recursively call `mergeKLists` on the left half and the right half. This will result in two sorted linked lists (`merged_left`, `merged_right`).
4.  **Combine:** Merge `merged_left` and `merged_right` using a standard two-list merge utility.

```python
# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, val=0, next=None):
#         self.val = val
#         self.next = next

class SolutionDivideConquer:
    def _merge_two_lists(self, l1: ListNode, l2: ListNode) -> ListNode:
        dummy = ListNode(-1)
        curr = dummy
        while l1 and l2:
            if l1.val < l2.val:
                curr.next = l1
                l1 = l1.next
            else:
                curr.next = l2
                l2 = l2.next
            curr = curr.next
        curr.next = l1 if l1 else l2
        return dummy.next

    def mergeKLists(self, lists: list[ListNode]) -> ListNode:
        if not lists:
            return None
        if len(lists) == 1:
            return lists[0]

        # Iterative Divide and Conquer (Pairwise merging)
        # More common in practice than pure recursion for this to avoid deep recursion stack
        # Amount is the number of lists remaining to be merged
        amount = len(lists)
        interval = 1
        while interval < amount:
            for i in range(0, amount - interval, interval * 2):
                lists[i] = self._merge_two_lists(lists[i], lists[i + interval])
            interval *= 2
        return lists[0] if amount > 0 else None

    # Pure Recursive Divide and Conquer (as described by Labuladong)
    def _merge_k_lists_recursive_helper(self, lists: list[ListNode], low: int, high: int) -> ListNode:
        if low > high:
            return None
        if low == high: # Base case: single list
            return lists[low]
        
        mid = low + (high - low) // 2
        
        left_merged = self._merge_k_lists_recursive_helper(lists, low, mid)
        right_merged = self._merge_k_lists_recursive_helper(lists, mid + 1, high)
        
        return self._merge_two_lists(left_merged, right_merged)

    def mergeKLists_recursive_entry(self, lists: list[ListNode]) -> ListNode:
        if not lists:
            return None
        return self._merge_k_lists_recursive_helper(lists, 0, len(lists) - 1)

```
The iterative pairwise merging (`while interval < amount`) is often preferred for `mergeKLists` to avoid deep Python recursion limits for large `k`. It still embodies the divide and conquer strategy by repeatedly merging pairs.

**Time Complexity (Divide and Conquer):**
- Merging two lists of total $N'$ nodes takes $O(N')$.
- There are $\log k$ levels of merging. At each level, a total of $N$ (total nodes in all lists) comparisons/operations are done.
- Total: $O(N \log k)$.
- Example: 8 lists.
  - Level 1: 4 merges (L1+L2, L3+L4, L5+L6, L7+L8)
  - Level 2: 2 merges
  - Level 3: 1 merge
- Each node participates in $\log k$ merges.
**Space Complexity (Divide and Conquer):**
- $O(\log k)$ for recursion stack if using pure recursion.
- $O(1)$ for iterative pairwise merging (modifies input list array, pointers are rearranged).
- $O(N)$ if creating new nodes for the merged list during `_merge_two_lists` instead of rewiring. If rewiring, it's $O(1)$ extra space for pointers beyond recursion.

## Visualization (Divide and Conquer for Merging Lists)

Imagine `lists = [L1, L2, L3, L4, L5, L6, L7, L8]`
```mermaid
graph TD
    subgraph "Level 0 (Initial Lists)"
        L1["L1"] -- merge --> M12["M(L1,L2)"]
        L2["L2"] -- merge --> M12
        L3["L3"] -- merge --> M34["M(L3,L4)"]
        L4["L4"] -- merge --> M34
        L5["L5"] -- merge --> M56["M(L5,L6)"]
        L6["L6"] -- merge --> M56
        L7["L7"] -- merge --> M78["M(L7,L8)"]
        L8["L8"] -- merge --> M78
    end

    subgraph "Level 1 Merges"
        M12 -- merge --> M1234["M(M12,M34)"]
        M34 -- merge --> M1234
        M56 -- merge --> M5678["M(M56,M78)"]
        M78 -- merge --> M5678
    end
    
    subgraph "Level 2 Merges"
        M1234 -- merge --> Final["M(M1234,M5678)"]
        M5678 -- merge --> Final
    end
    
    Final --> Result["Final Merged List"]

    classDef list fill:#cde4ff,stroke:#5a9ae5;
    class L1,L2,L3,L4,L5,L6,L7,L8 list;
    classDef merged fill:#e6ffcc,stroke:#66cc00;
    class M12,M34,M56,M78,M1234,M5678,Final,Result merged;
```
This shows $\log_2 k$ levels of merging.

## 总结 (Summary)
- **Merge K Sorted Lists** can be solved in multiple ways.
- **Brute Force (Collect & Sort):** $O(N \log N)$.
- **Iterative One-by-One Merge:** $O(Nk)$.
- **Min-Heap (Priority Queue):** $O(N \log k)$. Efficient and common. Requires $O(k)$ extra space for heap.
- **Divide and Conquer:** $O(N \log k)$. Similar time complexity to heap, can be $O(1)$ or $O(\log k)$ space for iterative/recursive versions (if merging by rewiring pointers).
- The choice between Min-Heap and Divide and Conquer often comes down to implementation preference or specific constraints (e.g., strict space limits might favor iterative D&C if in-place merging is done carefully, though Python's list of lists means $O(k)$ for list pointers anyway).

---
Parent: [[Interview/Practice/LeetCode/index|LeetCode Practice Problems]]
Concepts: [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework and Principles|Divide and Conquer]], [[Interview/Concept/Data Structures/Heap/index|Min-Heap]], [[Interview/Concept/Data Structures/Linked List/index|Linked Lists]]
"""
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created: {filepath}")


# --- Index File Update Functions ---
def update_algo_divide_and_conquer_index(kb_root):
    filepath = os.path.join(kb_root, "Concept/Algorithms/Divide and Conquer/index.md")
    ensure_dir(filepath)
    
    content = r"""---
tags: [index, concept/algorithms, concept/divide_and_conquer]
aliases: [Divide and Conquer Index, D&C Algorithms]
---

# Divide and Conquer Algorithms

This section covers the Divide and Conquer algorithmic paradigm, including its core principles and example applications.

## Core Concepts:
- [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework and Principles|Divide and Conquer - Framework and Principles]]
  - Distinction from general "decomposition thinking"
  - Steps: Divide, Conquer, Combine
  - Recurrence Relations and Master Theorem (brief mention)

## Classic Examples:
- [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]]
- [[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]]
- [[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|Merge K Sorted Lists]]

## Visualization
```mermaid
graph TD
    DCConcept["Divide & Conquer"] --> Framework["[[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework and Principles|Framework & Principles]]"]
    
    DCConcept --> Examples["Classic Examples"]
    Examples --> MergeSortL["[[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]]"]
    Examples --> QuickSortL["[[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]]"]
    Examples --> LC23L["[[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Lists]]"]

    classDef main fill:#d5e8d4,stroke:#82b366,stroke-width:2px;
    class DCConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
"""
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Created/Updated: {filepath}")

def update_algorithms_main_index_for_dc(kb_root):
    filepath = os.path.join(kb_root, "Concept/Algorithms/index.md")
    ensure_dir(filepath)
    
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            content = f.read()
    except FileNotFoundError:
        content = r"""---
tags: [index, concept/algorithms]
aliases: [Algorithms Index, Algorithm Concepts]
---
# Algorithm Concepts Index
This index covers various algorithm design paradigms...
## Algorithm Categories / Patterns
""" 

    new_link = "- [[Interview/Concept/Algorithms/Divide and Conquer/index|Divide and Conquer]]"
    
    header_tag = "## Algorithm Categories / Patterns"
    if header_tag in content:
        parts = content.split(header_tag, 1)
        current_links_text = parts[1].split("## Visualization of Algorithm Areas", 1)[0]
        updated_links_text = current_links_text
        if new_link.split("|")[0] not in updated_links_text:
            lines = updated_links_text.strip().splitlines()
            insert_idx = len(lines)
            for i, line_content in enumerate(lines):
                if line_content.strip().startswith("- [[") :
                    insert_idx = i+1 
                elif line_content.strip() and not line_content.strip().startswith("- [["):
                    insert_idx = i 
                    break
            lines.insert(insert_idx, new_link)
            updated_links_text = "\n".join(lines) + "\n"
        
        content = parts[0] + header_tag + "\n" + updated_links_text.strip() + "\n"
        if "## Visualization of Algorithm Areas" in parts[1]:
             content += "## Visualization of Algorithm Areas" + parts[1].split("## Visualization of Algorithm Areas", 1)[1]
    else: # Header not found
        content += "\n\n" + header_tag + "\n" + new_link

    # Update Mermaid diagram
    mermaid_header = "## Visualization of Algorithm Areas"
    if mermaid_header in content:
        mermaid_start_idx = content.find("```mermaid", content.find(mermaid_header))
        mermaid_end_idx = content.find("```", mermaid_start_idx + len("```mermaid"))
        if mermaid_start_idx != -1 and mermaid_end_idx != -1:
            mermaid_code = content[mermaid_start_idx + len("```mermaid") : mermaid_end_idx].strip()
            lines = mermaid_code.splitlines()
            
            dc_node_def = '    Algo --> DC["[[Interview/Concept/Algorithms/Divide and Conquer/index|Divide & Conquer]]"]'
            if 'DC["' not in mermaid_code:
                inserted = False
                for i, line in enumerate(lines):
                    if 'Greedy["(Greedy)"]' in line: # Example insertion point
                        lines.insert(i + 1, dc_node_def)
                        inserted = True
                        break
                if not inserted: # Fallback
                    class_def_line_idx = -1
                    for i, line in enumerate(lines):
                        if "classDef category" in line: class_def_line_idx = i; break
                    if class_def_line_idx != -1: lines.insert(class_def_line_idx, dc_node_def)
                    else: lines.append(dc_node_def)

            class_def_line_idx = -1
            for i, line in enumerate(lines):
                if "classDef category" in line: class_def_line_idx = i; break
            
            if class_def_line_idx != -1:
                if "DC" not in lines[class_def_line_idx] and "Divide & Conquer" not in lines[class_def_line_idx]:
                     lines[class_def_line_idx] = lines[class_def_line_idx].replace("category;", "DC, category;") # Add DC to class list
            else: # Add classDef if it's missing
                 lines.append("    classDef category fill:#e0ffff,stroke:#008b8b,stroke-width:2px;")
                 lines.append("    class Algo, Hashing, Sorting, Searching, TreeT, GraphT, Recursion, DC, DP, Greedy, BitManip category;")


            mermaid_code = "\n".join(lines)
            content = content[:mermaid_start_idx + len("```mermaid")] + "\n" + mermaid_code + "\n" + content[mermaid_end_idx:]
            
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    print(f"Updated main algorithms index for D&C: {filepath}")

# --- Main Script ---
if __name__ == "__main__":
    kb_root_for_script = "./" 
    
    print(f"Knowledge base operations target root: {kb_root_for_script}")

    create_divide_and_conquer_framework(kb_root_for_script)
    create_lc23_merge_k_sorted_lists(kb_root_for_script)
        
    update_algo_divide_and_conquer_index(kb_root_for_script)
    update_algorithms_main_index_for_dc(kb_root_for_script)
    
    # Update LeetCode Index for LC23
    lc_index_path = os.path.join(kb_root_for_script, "Practice/LeetCode/index.md")
    if os.path.exists(lc_index_path):
        with open(lc_index_path, "r", encoding="utf-8") as f:
            lc_content = f.read()
        
        new_lc_link = "- [[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Sorted Lists]]"
        
        # Add to "Hard" problems or appropriate category
        insert_header_lc = "### Hard (Placeholders)" # Or "### Hard" if it exists
        if insert_header_lc not in lc_content:
            # If no "Hard" header, find "Medium" or append to general list.
            # For now, let's try to find "Medium" and insert "Hard" after it, or append.
            medium_header = "### Medium (Placeholders)"
            if medium_header in lc_content:
                 parts = lc_content.split(medium_header, 1)
                 lc_content = parts[0] + medium_header + parts[1] + f"\n\n{insert_header_lc.replace(' (Placeholders)','')}\n" # Add Hard section
            elif "### Easy" in lc_content: # if Easy exists, add Hard after
                 parts = lc_content.split("### Easy", 1)
                 easy_section_end = parts[1].find("\n###") if "\n###" in parts[1] else len(parts[1])
                 lc_content = parts[0] + "### Easy" + parts[1][:easy_section_end] + f"\n\n{insert_header_lc.replace(' (Placeholders)','')}\n" + parts[1][easy_section_end:]
            else: # fallback append
                 lc_content += f"\n\n{insert_header_lc.replace(' (Placeholders)','')}\n"
            insert_header_lc = insert_header_lc.replace(' (Placeholders)','') # use the actual header now

        # Add the link to the section
        if insert_header_lc in lc_content:
            parts = lc_content.split(insert_header_lc, 1)
            prefix_lc = parts[0] + insert_header_lc + "\n"
            suffix_lc = parts[1] if len(parts) > 1 else ""
            
            current_links_in_lc_section = ""
            if suffix_lc:
                next_header_lc_idx = suffix_lc.find("\n###")
                if next_header_lc_idx != -1:
                    current_links_in_lc_section = suffix_lc[:next_header_lc_idx]
                    suffix_lc = suffix_lc[next_header_lc_idx:]
                else:
                    current_links_in_lc_section = suffix_lc
                    suffix_lc = ""
            
            if new_lc_link.split("|")[0] not in current_links_in_lc_section:
                current_links_in_lc_section += new_lc_link + "\n"
            
            lc_content = prefix_lc + current_links_in_lc_section.strip() + "\n" + suffix_lc.strip()

        # Update Mermaid diagram for LC index
        mermaid_lc_header = "## Visualization of Practice Areas"
        if mermaid_lc_header in lc_content:
            mermaid_lc_start = lc_content.find("```mermaid", lc_content.find(mermaid_lc_header))
            mermaid_lc_end = lc_content.find("```", mermaid_lc_start + len("```mermaid"))
            if mermaid_lc_start != -1 and mermaid_lc_end != -1:
                mermaid_lc_code = lc_content[mermaid_lc_start + len("```mermaid") : mermaid_lc_end].strip()
                lines_lc = mermaid_lc_code.splitlines()
                
                lc23_node = '    Hard --> LC23["[[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Lists]]"]'
                lc23_cat_link1 = '    LC23 --> CatLinkedList' # New category for LinkedList
                lc23_cat_link2 = '    LC23 --> CatHeap' # New category for Heap
                lc23_cat_link3 = '    LC23 --> CatDC' # New category for Divide & Conquer
                
                cat_ll_def = '        CatLinkedList["Linked List"]'
                cat_heap_def = '        CatHeap["Heap/Priority Queue"]'
                cat_dc_def = '        CatDC["Divide & Conquer"]'

                if 'LC23["' not in mermaid_lc_code:
                    # Find Hard section or append
                    hard_section_line_idx = -1
                    for i, line in enumerate(lines_lc):
                        if 'Hard["(Hard Problems)"]' in line: hard_section_line_idx = i; break
                    if hard_section_line_idx != -1 : lines_lc.insert(hard_section_line_idx + 1, lc23_node)
                    else: lines_lc.insert(lines_lc.index('    Practice --> Hard["(Hard Problems)"]')+1, lc23_node)


                # Add category definitions if not present within subgraph
                subgraph_end_idx = -1
                for i, line in enumerate(lines_lc):
                    if "end" in line and "subgraph" in lines_lc[i-1 if i > 0 else 0]: subgraph_end_idx = i; break
                
                if subgraph_end_idx != -1:
                    if cat_ll_def not in mermaid_lc_code : lines_lc.insert(subgraph_end_idx, cat_ll_def)
                    if cat_heap_def not in mermaid_lc_code : lines_lc.insert(subgraph_end_idx, cat_heap_def)
                    if cat_dc_def not in mermaid_lc_code : lines_lc.insert(subgraph_end_idx, cat_dc_def)
                else: # if no subgraph, append
                    if cat_ll_def not in mermaid_lc_code : lines_lc.append(cat_ll_def)
                    if cat_heap_def not in mermaid_lc_code : lines_lc.append(cat_heap_def)
                    if cat_dc_def not in mermaid_lc_code : lines_lc.append(cat_dc_def)

                if lc23_cat_link1 not in mermaid_lc_code: lines_lc.append(lc23_cat_link1)
                if lc23_cat_link2 not in mermaid_lc_code: lines_lc.append(lc23_cat_link2)
                if lc23_cat_link3 not in mermaid_lc_code: lines_lc.append(lc23_cat_link3)
                
                mermaid_lc_code = "\n".join(lines_lc)
                lc_content = lc_content[:mermaid_lc_start + len("```mermaid")] + "\n" + mermaid_lc_code + "\n" + lc_content[mermaid_lc_end:]

        with open(lc_index_path, "w", encoding="utf-8") as f:
            f.write(lc_content)
        print(f"Updated LeetCode index for LC23: {lc_index_path}")

    print(f"Script finished. Divide & Conquer framework and LC23 notes created/updated in '{kb_root_for_script}'.")
