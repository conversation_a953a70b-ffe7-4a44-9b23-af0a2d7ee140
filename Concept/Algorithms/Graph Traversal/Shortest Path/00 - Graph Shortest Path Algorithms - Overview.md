---
tags: [concept/algorithms, concept/graph_traversal, concept/shortest_path, type/overview, concept/dijkstra, concept/bellman_ford, concept/floyd_warshall]
aliases: [Shortest Path Algorithms, Graph Pathfinding Overview, 图最短路径概览]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/图结构基础及算法概览/图结构最短路径算法概览.md]].
> This note provides an overview of common shortest path problems and algorithms in graph theory.

# Graph Shortest Path Algorithms: Overview

Finding the shortest path between nodes in a graph is a classic and widely applicable problem in computer science. "Shortest" can refer to the minimum number of edges (for unweighted graphs) or the minimum total weight/cost (for weighted graphs).

## 📜 Problem Categories

Shortest path problems are generally categorized into:

1.  **Single-Source Shortest Path (SSSP):**
    - **Goal:** Find the shortest paths from a single source vertex `s` to all other vertices in the graph.
    - **Output:** Typically an array `distTo[]`, where `distTo[v]` is the shortest path cost from `s` to `v`.
    - If only the shortest path from `s` to a specific target `t` is needed, SSSP algorithms can often be adapted or stopped early once `t` is finalized.

2.  **All-Pairs Shortest Path (APSP):**
    - **Goal:** Find the shortest paths between all pairs of vertices `(u, v)` in the graph.
    - **Output:** Typically a 2D array `dist[u][v]` storing the shortest path cost from `u` to `v`.

## 🌟 Key Algorithms

### For Single-Source Shortest Path (SSSP)

1.  **[[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Breadth-First Search (BFS)]] (for Unweighted Graphs):**
    - **Principle:** Explores the graph layer by layer.
    - **Suitability:** Finds the shortest path in terms of the number of edges.
    - **Complexity:** $O(V+E)$.
    - **Cannot handle edge weights.**

2.  **[[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01 - Dijkstra Algorithm|Dijkstra's Algorithm]]**:
    - **Principle:** A greedy algorithm that maintains a set of visited nodes for which the shortest path is known. It repeatedly selects the unvisited node closest to the source. Similar to [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|BFS]] but uses a [[Interview/Concept/Data Structures/Heap/index|priority queue]] to select the "closest" node.
    - **Suitability:** Works for graphs with **non-negative edge weights**.
    - **Complexity:** $O(E \log V)$ or $O((E+V)\log V)$ with a binary heap based priority queue; $O(E + V \log V)$ with a Fibonacci heap.
    - **Cannot handle negative edge weights correctly.** (A negative edge might lead to a shorter path to an already "settled" node).

3.  **Bellman-Ford Algorithm:**
    - **Principle:** Relaxes all edges $V-1$ times. If after $V-1$ iterations, an edge can still be relaxed, it indicates a negative cycle reachable from the source.
    - **Suitability:** Works for graphs that may contain **negative edge weights**. Can detect negative cycles.
    - **Complexity:** $O(V \cdot E)$. Slower than Dijkstra's.

### For All-Pairs Shortest Path (APSP)

1.  **Floyd-Warshall Algorithm:**
    - **Principle:** A dynamic programming algorithm. It considers all possible intermediate vertices `k` for each pair of vertices `(i, j)`. `dist[i][j] = min(dist[i][j], dist[i][k] + dist[k][j])`.
    - **Suitability:** Works for graphs that may contain **negative edge weights** (but no negative cycles; if negative cycles exist, it can detect them by finding negative values on the diagonal `dist[i][i]` after completion).
    - **Complexity:** $O(V^3)$.

2.  **Running SSSP Algorithm from Each Vertex:**
    - If the graph has no negative edges, run Dijkstra's from each of the $V$ vertices: $V \times O(E \log V)$. This can be better than Floyd-Warshall for sparse graphs ($E \approx V$).
    - If the graph has negative edges (but no negative cycles), run Bellman-Ford from each vertex: $V \times O(VE) = O(V^2 E)$. This is often worse than Floyd-Warshall.

## ⚖️ The Impact of Negative Edge Weights

Negative edge weights significantly complicate shortest path problems:
- **Dijkstra's greedy approach fails:** It assumes that once a node is settled (shortest path found), it remains settled. A negative edge encountered later could provide a shorter path to this "settled" node.
    ```tikz
    \begin{tikzpicture}[tn/.style={circle, draw, font=\sffamily\small, minimum size=6mm, inner sep=1pt}, arr/.style={->, >=stealth, thick}]
        \node[tn] (S) at (0,0) {S};
        \node[tn] (A) at (2,1) {A};
        \node[tn] (B) at (2,-1) {B};
        \draw[arr] (S) -- node[midway, above, sloped, font=\tiny]{3} (A);
        \draw[arr] (S) -- node[midway, below, sloped, font=\tiny]{4} (B);
        \draw[arr] (B) -- node[midway, right, font=\tiny]{-10} (A);
        \node at (1,-2) [text width=6cm,align=center,font=\tiny\sffamily]
        {Dijkstra might pick S->A (cost 3) first. But S->B->A (cost 4-10 = -6) is shorter.};
    \end{tikzpicture}
    ```
- **Negative Cycles:** If a graph contains a cycle whose edges sum to a negative value, then the "shortest path" is undefined because one can traverse the cycle infinitely to get an arbitrarily small path cost. Algorithms like Bellman-Ford and Floyd-Warshall can detect such cycles.

## Visualization of Concepts (Conceptual)
```mermaid
graph TD
    SP["Shortest Path Problems"] --> SSSP["Single-Source (SSSP)"]
    SP --> APSP["All-Pairs (APSP)"]

    SSSP --> BFS_SP["[[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|BFS]] (Unweighted)"]
    SSSP --> Dijkstra["[[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01 - Dijkstra Algorithm|Dijkstra]] (Non-negative weights)"]
    SSSP --> BellmanFord["Bellman-Ford (Handles negative weights, detects negative cycles)"]

    APSP --> FloydWarshall["Floyd-Warshall (Handles negative weights, detects negative cycles)"]
    APSP --> RunSSSP["Run SSSP V times (e.g., Dijkstra from all V nodes)"]

    subgraph "Key Considerations"
        NegWeights["Negative Edge Weights"]
        NegCycles["Negative Cycles"]
    end
    
    Dijkstra -- "Fails with negative weights" --> NegWeights
    BellmanFord -- "Handles negative weights" --> NegWeights
    FloydWarshall -- "Handles negative weights" --> NegWeights
    BellmanFord -- "Detects negative cycles" --> NegCycles
    FloydWarshall -- "Detects negative cycles" --> NegCycles


    classDef main fill:#e6ffe6,stroke:#006400,stroke-width:2px;
    class SP main;
```

## 总结 (Summary)
- Shortest path problems involve finding paths with minimum total weight (or edge count).
- **Single-Source Shortest Path (SSSP):**
    - **BFS:** For unweighted graphs ($O(V+E)$).
    - **Dijkstra:** For non-negative weights ($O(E \log V)$).
    - **Bellman-Ford:** For graphs with negative weights, detects negative cycles ($O(VE)$).
- **All-Pairs Shortest Path (APSP):**
    - **Floyd-Warshall:** For graphs with negative weights, detects negative cycles ($O(V^3)$).
    - **Repeated SSSP:** Can be an alternative, efficiency depends on graph properties and SSSP algorithm used.
- The presence of negative edge weights and negative cycles dictates the choice of algorithm.

---
Parent: [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal Algorithms]]
Next Specific Algorithms:
- [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01 - Dijkstra Algorithm|Dijkstra's Algorithm]]
- `[[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/02 - Bellman-Ford Algorithm|Bellman-Ford Algorithm]]` (Placeholder)
- `[[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/03 - Floyd-Warshall Algorithm|Floyd-Warshall Algorithm]]` (Placeholder)
Related: [[Interview/Concept/Data Structures/Graph/00 - Graph - Introduction and Basic Terms|Graph Basics]]
