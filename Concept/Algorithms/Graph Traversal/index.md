---
tags: [index, concept/algorithms, concept/graph_traversal, concept/dfs, concept/bfs, concept/shortest_path]
aliases: [Graph Traversal Algorithm Index, Graph Search Algorithms]
---

# Graph Traversal Algorithms

This section covers algorithms for traversing and searching graph data structures, including fundamental exploration techniques and shortest path algorithms.

## Basic Traversal Techniques
- [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|Graph DFS Traversal]]
  - Using `visited` array for node traversal
  - Using `onPath` array for path-specific logic (e.g., cycle detection)
- [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Graph BFS Traversal]]
  - Finding shortest path in unweighted graphs

## Shortest Path Algorithms
- [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/00 - Graph Shortest Path Algorithms - Overview|Graph Shortest Path Algorithms - Overview]]
  - Single-Source Shortest Path (SSSP)
  - All-Pairs Shortest Path (APSP)
  - Impact of Negative Weights and Cycles
- Specific SSSP Algorithms:
  - [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01 - Dijkstra Algorithm|Dijkstra's Algorithm]] (Non-negative weights)
  - `[[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/02 - Bellman-Ford Algorithm|Bellman-Ford Algorithm]]` (Handles negative weights, detects negative cycles) - Placeholder
- Specific APSP Algorithms:
  - `[[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/03 - Floyd-Warshall Algorithm|Floyd-Warshall Algorithm]]` (Handles negative weights, detects negative cycles) - Placeholder

## Other Applications & Concepts
- `[[Interview/Concept/Algorithms/Graph Traversal/02 - Graph - Traversal Applications (Connectivity, Cycles, Paths)|Graph Traversal Applications (Connectivity, Cycles, Paths)]]` - Placeholder (e.g., Topological Sort, Bipartite Matching)

## Visualization
```mermaid
graph TD
    GT["Graph Traversal"] --> Basic["Basic Traversal"]
    GT --> SP["Shortest Path Algorithms"]
    GT --> Apps["Other Applications"]

    Basic --> G_DFS["[[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|Graph DFS]]"]
    Basic --> G_BFS["[[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Graph BFS]]"]

    SP --> SP_Overview["[[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/00 - Graph Shortest Path Algorithms - Overview|Overview]]"]
    SP_Overview --> SSSP_Cat["SSSP"]
    SP_Overview --> APSP_Cat["APSP"]
    
    SSSP_Cat --> Dijkstra["[[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/01 - Dijkstra Algorithm|Dijkstra]]"]
    SSSP_Cat --> BellmanFord["(Bellman-Ford)"]
    APSP_Cat --> FloydWarshall["(Floyd-Warshall)"]

    Apps --> App_Placeholder["(Connectivity, Cycles, TopoSort...)"]


    classDef main fill:#f0e6ff,stroke:#8a2be2,stroke-width:2px;
    class GT main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
