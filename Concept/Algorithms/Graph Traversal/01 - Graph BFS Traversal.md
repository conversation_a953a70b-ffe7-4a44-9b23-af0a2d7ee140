---
tags: [concept/algorithms, concept/graph_traversal, concept/bfs, concept/queue, type/algorithm]
aliases: [Graph BFS, Breadth First Search on Graphs, 图的BFS遍历]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/图结构基础及算法概览/图结构的 DFS_BFS 遍历.md]].
> Graph BFS is an extension of [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Tree Level-Order Traversal (BFS)]], with added cycle handling.

# Graph Breadth-First Search (BFS) Traversal

Breadth-First Search (BFS) for graphs is an algorithm for traversing or searching graph data structures. It starts at a source node and explores all of
the neighbor nodes at the present depth prior to moving on to the nodes at the next depth level.

## 🌲 Core Idea and Comparison to Tree BFS

Graph BFS uses a [[Interview/Concept/Data Structures/Queue/index|queue]] to manage the frontier of nodes to visit, similar to [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|level-order traversal for trees]]. The key addition for graphs is a `visited` set/array to avoid processing nodes multiple times and getting trapped in cycles.

**Tree BFS (Level-Order) Framework (Conceptual):**
```python
# from collections import deque
# class TreeNode: self.val, self.left, self.right
# def level_order_tree(root: TreeNode):
#     if not root: return []
#     q = deque([root])
#     result = []
#     while q:
#         node = q.popleft()
#         # result.append(node.val) # Process
#         if node.left: q.append(node.left)
#         if node.right: q.append(node.right)
#     return result
```

**Graph BFS Framework:**
```python
from collections import deque

# graph: Adjacency list representation, e.g., graph.get_neighbors(u)
# visited: A boolean array or set

def bfs_graph_single_component(graph, start_node):
    if start_node is None: return []
    
    q = deque([start_node])
    visited = {start_node} # Or visited = [False]*N; visited[start_node]=True
    traversal_order = []

    while q:
        u = q.popleft()
        traversal_order.append(u) # Process node u
        # print(f"Visiting node: {u}")

        for v_info in graph.get_neighbors(u):
            v = v_info[0] if isinstance(v_info, tuple) else v_info # Handle weighted/unweighted
            if v not in visited:
                visited.add(v)
                q.append(v)
    return traversal_order
```

## Algorithm for Traversing All Nodes / Shortest Path in Unweighted Graphs

BFS is particularly useful for finding the shortest path in terms of the number of edges in an unweighted graph.

```python
from collections import deque

# Assuming graph is an object with methods:
# graph.num_vertices() -> int
# graph.get_neighbors(u) -> list of neighbors of u

class GraphBFSTraversal:
    def __init__(self, graph):
        self.graph = graph

    def traverse_all_nodes(self, start_node_suggestion=0):
        visited = [False] * self.graph.num_vertices()
        traversal_path = []
        
        # Handle disconnected graphs by iterating through all possible start nodes
        # For a specific component starting at start_node_suggestion:
        
        q = deque()

        # Initial call for the first component (or specified start node)
        if not visited[start_node_suggestion]:
            q.append(start_node_suggestion)
            visited[start_node_suggestion] = True
        
        # Main BFS loop for the current component
        while q:
            u = q.popleft()
            traversal_path.append(u) # Process node

            for neighbor_info in self.graph.get_neighbors(u):
                neighbor_node = neighbor_info[0] if isinstance(neighbor_info, tuple) else neighbor_info
                if not visited[neighbor_node]:
                    visited[neighbor_node] = True
                    q.append(neighbor_node)
        
        # To ensure all components in a disconnected graph are visited:
        for i in range(self.graph.num_vertices()):
            if not visited[i]:
                # Start BFS for a new unvisited component
                q.append(i)
                visited[i] = True
                while q: # This inner loop should ideally be a call to a helper BFS function
                    u_comp = q.popleft()
                    traversal_path.append(u_comp)
                    for neighbor_info_comp in self.graph.get_neighbors(u_comp):
                        neighbor_node_comp = neighbor_info_comp[0] if isinstance(neighbor_info_comp, tuple) else neighbor_info_comp
                        if not visited[neighbor_node_comp]:
                            visited[neighbor_node_comp] = True
                            q.append(neighbor_node_comp)
                            
        return traversal_path

    def shortest_path_unweighted(self, start_node, end_node):
        if start_node == end_node:
            return [start_node]

        q = deque([(start_node, [start_node])]) # Store (node, current_path_to_node)
        visited = {start_node}

        while q:
            u, path = q.popleft()

            for neighbor_info in self.graph.get_neighbors(u):
                v = neighbor_info[0] if isinstance(neighbor_info, tuple) else neighbor_info
                if v == end_node:
                    return path + [v] # Path found
                if v not in visited:
                    visited.add(v)
                    q.append((v, path + [v]))
        
        return None # No path found
```
The visualization panel `div_graph-node-bfs-traverse2` in Labuladong's article shows the layer-by-layer exploration.

## Complexity
- **Time Complexity:** $O(V+E)$, where $V$ is the number of vertices and $E$ is the number of edges. Each vertex is enqueued and dequeued at most once. All edges are examined at most once (for directed graphs) or twice (for undirected graphs when considering adjacency list representation).
- **Space Complexity:** $O(V)$ in the worst case for storing the `queue` and `visited` set. The maximum size of the queue can be $O(V)$ (e.g., in a graph that is a single line, or a star graph where root has $V-1$ children).

## Applications of Graph BFS
- **Finding the shortest path** between two nodes in an unweighted graph.
- **Finding all reachable nodes** from a source node.
- **Testing graph connectivity.**
- **Level-order traversal** of a tree (which is a special type of graph).
- Used as a subroutine in other algorithms like Cheney's algorithm for garbage collection or finding minimum spanning trees (Prim's algorithm can use a BFS-like approach on the frontier).
- Solving puzzles that can be modeled as finding a shortest sequence of moves (e.g., Rubik's Cube, 15-puzzle).

## 总结 (Summary)
- Graph BFS explores layer by layer using a queue.
- A `visited` set/array is essential to handle cycles and avoid redundant work.
- Guarantees finding the shortest path in terms of number of edges in unweighted graphs.
- Time complexity is $O(V+E)$. Space complexity is $O(V)$.
- Widely used for shortest path problems in unweighted scenarios and as a general graph exploration technique.

---
Parent: [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal Algorithms]]
Previous: [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|Graph DFS Traversal]]
Next: [[Interview/Concept/Algorithms/Graph Traversal/02 - Graph - Traversal Applications (Connectivity, Cycles, Paths)|Graph Traversal Applications]] (Placeholder)
Related: [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Tree BFS]], [[Interview/Concept/Data Structures/Queue/index|Queues]]
