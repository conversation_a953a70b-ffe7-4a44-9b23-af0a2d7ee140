---
tags: [concept/algorithms, concept/graph_traversal, concept/dfs, concept/bfs, pattern/grid_traversal, pattern/island_problem, course/labuladong]
aliases: [Island Problems Framework, Grid DFS/BFS, 网格DFS/BFS框架, 岛屿问题]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第二章、经典暴力搜索算法/DFS_回溯算法/一文秒杀所有岛屿题目.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第二章、经典暴力搜索算法/DFS_回溯算法/一文秒杀所有岛屿题目.md|一文秒杀所有岛屿题目 by Labuladong]].
> This note outlines a general framework for solving "island" problems on a 2D grid using DFS or BFS.

# Island Problems: Unified Traversal Framework (DFS/BFS)

"Island" problems are a common category of grid traversal tasks where you typically need to count islands, find the largest island, identify specific types of islands, etc. A 2D grid can be viewed as a graph where each cell is a node, and adjacent cells (up, down, left, right) are its neighbors. The core of solving these problems is using [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|Depth-First Search (DFS)]] or [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Breadth-First Search (BFS)]] to explore connected components of '1's (land) in a grid of '0's (water) and '1's.

## 🌲 Core DFS Framework for 2D Grid

Labuladong emphasizes that the DFS traversal for a 2D grid is analogous to tree traversal, but for a "four-way" tree (quadtree-like connections) with the added complexity of needing a `visited` array to prevent cycles or redundant work.

```python
# General DFS framework for a grid
# (Assuming 'grid' is the input 2D array, 'visited' is a boolean grid of same dimensions)

# Optional: Directions array for cleaner neighbor iteration
# DIRS = [[-1, 0], [1, 0], [0, -1], [0, 1]] # Up, Down, Left, Right

def dfs_grid(grid: list[list[int]], r: int, c: int, visited: list[list[bool]]):
    rows, cols = len(grid), len(grid[0])

    # Base cases for recursion termination
    if not (0 <= r < rows and 0 <= c < cols): # Out of bounds
        return
    if visited[r][c]: # Already visited
        return
    if grid[r][c] == 0: # Water cell, or not part of current island type
        return

    # Mark as visited and process (e.g., part of current island)
    visited[r][c] = True
    # (Perform action for (r,c), e.g., increment island size, add to path for shape)

    # Recursively visit neighbors
    dfs_grid(grid, r - 1, c, visited) # Up
    dfs_grid(grid, r + 1, c, visited) # Down
    dfs_grid(grid, r, c - 1, visited) # Left
    dfs_grid(grid, r, c + 1, visited) # Right

    # (Post-order actions if needed)
```

**Main Loop for Counting Islands (Example - LC200):**
```python
# def count_islands(grid: list[list[int]]) -> int:
#     if not grid or not grid[0]:
#         return 0
#     rows, cols = len(grid), len(grid[0])
#     visited = [[False for _ in range(cols)] for _ in range(rows)]
#     island_count = 0

#     for r in range(rows):
#         for c in range(cols):
#             if grid[r][c] == 1 and not visited[r][c]:
#                 # Found an unvisited land cell, start DFS to find all parts of this island
#                 dfs_grid(grid, r, c, visited) # The dfs_grid "floods" this island
#                 island_count += 1
#     return island_count
```

## Key Adaptations for Different Island Problems

The basic DFS/BFS grid traversal framework is modified based on the specific problem:

1.  **Counting Islands ([[Interview/Practice/LeetCode/LC200 - Number of Islands|LC200]]):**
    - The main loop iterates through all cells. If an unvisited land cell (`grid[r][c] == 1`) is found, increment island count and start a DFS/BFS from it to mark all connected land cells as visited (effectively "sinking" or "consuming" that island).

2.  **Max Area of Island ([[Interview/Practice/LeetCode/LC695 - Max Area of Island|LC695]]):**
    - Modify DFS/BFS to return the size of the island it just traversed. Keep a global max.
    - `dfs_grid` can return `1 + sum(dfs_for_neighbors)`.

3.  **Number of Closed Islands ([[Interview/Practice/LeetCode/LC1254 - Number of Closed Islands|LC1254]]):**
    - A closed island is one completely surrounded by water ('0's).
    - First, DFS/BFS from all land cells on the *border* of the grid and mark all connected land cells as visited (or change them to water). These are not closed islands.
    - Then, iterate through the *inner* grid. Any remaining unvisited land cell is the start of a closed island. Count these using standard island counting.

4.  **Number of Enclaves ([[Interview/Practice/LeetCode/LC1020 - Number of Enclaves|LC1020]]):**
    - Similar to closed islands. An enclave is a land cell from which you cannot walk off the boundary of the grid.
    - DFS/BFS from all border land cells and mark reachable land.
    - Count remaining unvisited land cells; these form enclaves.

5.  **Count Sub Islands ([[Interview/Practice/LeetCode/LC1905 - Count Sub Islands|LC1905]]):**
    - Given `grid1` and `grid2`. An island in `grid2` is a sub-island if every land cell of this island in `grid2` is also a land cell in `grid1`.
    - Iterate through `grid2`. If `grid2[r][c] == 1` and it's unvisited:
        - Start a DFS/BFS on `grid2` from `(r,c)` to find all cells of this island.
        - During this traversal, check if *every* cell `(curr_r, curr_c)` of this `grid2` island also satisfies `grid1[curr_r][curr_c] == 1`. If any cell doesn't, this `grid2` island is not a sub-island.
        - Use a flag within the DFS/BFS to track this condition.

6.  **Number of Distinct Islands ([[Interview/Practice/LeetCode/LC694 - Number of Distinct Islands|LC694]] 🔒):**
    - Need to find a canonical representation (signature) for the shape of each island.
    - During DFS/BFS for an island, record the path taken relative to the starting cell of that island (e.g., "DLRU" for Down, Left, Right, Up moves, or sequence of relative coordinates `(dr, dc)`).
    - Store these signature strings in a set. The size of the set is the number of distinct island shapes.
    - Care must be taken with the starting point and path normalization to ensure identical shapes produce identical signatures.

## BFS Alternative
All these problems can also be solved with BFS. Replace the recursive `dfs_grid` with an iterative BFS using a queue:
```python
# from collections import deque
# DIRS = [[-1, 0], [1, 0], [0, -1], [0, 1]]

# def bfs_grid(grid, start_r, start_c, visited):
#     rows, cols = len(grid), len(grid[0])
#     q = deque([(start_r, start_c)])
#     visited[start_r][start_c] = True

#     while q:
#         r, c = q.popleft()
#         # Process (r,c)

#         for dr, dc in DIRS:
#             nr, nc = r + dr, c + dc
#             if 0 <= nr < rows and 0 <= nc < cols and \
#                not visited[nr][nc] and grid[nr][nc] == 1:
#                 visited[nr][nc] = True
#                 q.append((nr, nc))
```

## 总结 (Summary)
- Island problems on a 2D grid are fundamentally graph traversal problems.
- DFS or BFS can be used to explore connected components (islands).
- A `visited` grid is essential to avoid redundant work and infinite loops.
- The core traversal logic is adapted based on specific problem requirements:
    - Counting islands: Simple flood-fill and increment.
    - Max area: DFS/BFS returns size.
    - Closed/Enclaves: Eliminate border-connected islands first.
    - Sub-islands: Check against a second grid during traversal.
    - Distinct shapes: Serialize island path/shape during traversal.
- The time complexity is typically $O(R \times C)$ as each cell is visited at most a constant number of times. Space complexity is also $O(R \times C)$ for `visited` array and recursion stack/queue.

---
Parent: [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal Algorithms]]
Related: [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|Graph DFS]], [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Graph BFS]]
