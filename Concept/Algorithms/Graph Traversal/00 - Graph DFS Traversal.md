---
tags: [concept/algorithms, concept/graph_traversal, concept/dfs, concept/recursion, type/algorithm]
aliases: [Graph DFS, Depth First Search on Graphs, 图的DFS遍历]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/图结构基础及算法概览/图结构的 DFS_BFS 遍历.md]].
> Graph DFS is an extension of [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Tree DFS]], with added cycle handling.

# Graph Depth-First Search (DFS) Traversal

Depth-First Search (DFS) for graphs is an algorithm for traversing or searching tree or graph data structures. The algorithm starts at a root node (or an arbitrary starting node for a graph) and explores as far as possible along each branch before backtracking.

## 🌲 Core Idea and Comparison to Tree DFS

Graph DFS is very similar to [[Interview/Concept/Algorithms/Tree Traversal/02 - N-ary Tree Recursive Traversal (DFS)|N-ary Tree DFS]]. The main difference is that graphs can contain cycles, which could lead to infinite loops if not handled. To prevent this, DFS for graphs uses a `visited` set/array to keep track of nodes already visited.

**Tree DFS Framework (N-ary conceptual):**
```python
# class Node: self.val, self.children
# def traverse_tree(root: Node):
#     if root is None: return
#     # print(root.val) # Pre-order
#     for child in root.children:
#         traverse_tree(child)
#     # Post-order action
```

**Graph DFS Framework:**
```python
# graph: Adjacency list representation, e.g., graph[u] = [v1, v2...]
# visited: A boolean array or set to track visited nodes

def dfs_graph(graph, start_node, visited):
    if start_node in visited: # Or visited[start_node] if using boolean array
        return
    
    visited.add(start_node) # Mark as visited (Pre-order position)
    # print(f"Visiting node: {start_node}") # Process node

    for neighbor in graph.get_neighbors(start_node): # Assuming graph.get_neighbors(u)
        if neighbor not in visited: # Check before recursive call
            dfs_graph(graph, neighbor, visited)
    
    # Post-order action for start_node could be here
```
Labuladong's article "图结构的 DFS_BFS 遍历" shows a `Vertex` class based DFS first, then transitions to adjacency list/matrix based.

## Algorithm for Traversing All Nodes (Using `visited` array)

This version focuses on visiting each node once.

```python
# Assuming graph is an object with methods:
# graph.num_vertices() -> int
# graph.get_neighbors(u) -> list of neighbors of u (or (neighbor, weight) tuples)

class GraphDFSTraversal:
    def __init__(self, graph):
        self.graph = graph
        self.visited = [False] * graph.num_vertices() # Or a set for generic node IDs
        self.traversal_path = [] # To store order of visited nodes

    def _dfs_util(self, u):
        self.visited[u] = True
        self.traversal_path.append(u) # Process node (pre-order)

        # For weighted graphs, neighbor might be (v, weight)
        # For unweighted, neighbor is just v
        for neighbor_info in self.graph.get_neighbors(u):
            neighbor_node = neighbor_info
            if isinstance(neighbor_info, tuple): # Handle (neighbor, weight)
                neighbor_node = neighbor_info[0]
            
            if not self.visited[neighbor_node]:
                self._dfs_util(neighbor_node)
        
        # Post-order processing for u could be here

    def traverse_all_nodes(self, start_node_suggestion=0):
        # Call DFS from a starting node
        if not self.visited[start_node_suggestion]:
             self._dfs_util(start_node_suggestion)

        # If graph might be disconnected, iterate through all nodes
        for i in range(self.graph.num_vertices()):
            if not self.visited[i]:
                self._dfs_util(i) # Start DFS from unvisited components
        return self.traversal_path

# Example usage:
# Assuming 'my_graph' is an instance of a graph class like GraphAdjList from previous notes
# dfs_traverser = GraphDFSTraversal(my_graph)
# path = dfs_traverser.traverse_all_nodes(0) # Start from node 0
# print(f"DFS traversal order: {path}")
```
The visualization panel in Labuladong's article (`div_graph-node-dfs-traverse`) would show the `visited` array being updated and the recursive calls exploring paths.

## Algorithm for Finding Paths (Using `onPath` array for cycle detection in path context)

When the goal is to find paths (e.g., from a start to an end node, or detecting cycles specifically related to the current path), a slightly different mechanism using an `onPath` tracker is common, in addition to or sometimes instead of a global `visited` (depending on whether nodes can be revisited via different paths).

Labuladong's "遍历所有路径 (`onPath` 数组)" section in "图结构的 DFS_BFS 遍历" discusses this. The `onPath` array (or set) marks nodes currently in the recursion stack for the *current path being explored*.
- Mark node `u` in `onPath` before recursing to neighbors.
- Unmark node `u` from `onPath` after returning from recursive calls on its neighbors (backtracking).
- A cycle is detected if DFS tries to visit a node already in `onPath`.

```python
class GraphPathDFS:
    def __init__(self, graph):
        self.graph = graph
        # self.visited = [False] * graph.num_vertices() # Global visited can still be useful
        self.on_path = [False] * graph.num_vertices() # Tracks nodes in current recursion stack
        self.has_cycle = False
        # For finding a specific path:
        # self.path_to_target = [] 
        # self.found_target = False

    def _dfs_path_util(self, u): #, target_node (if finding path to target)
        # if self.found_target: return

        self.on_path[u] = True
        # self.visited[u] = True # Mark globally visited too if nodes shouldn't be re-explored at all

        # self.path_to_target.append(u) # Add to current path

        # if u == target_node:
        #     self.found_target = True
        #     return

        for neighbor_info in self.graph.get_neighbors(u):
            neighbor_node = neighbor_info[0] if isinstance(neighbor_info, tuple) else neighbor_info
            
            if self.on_path[neighbor_node]:
                self.has_cycle = True # Cycle detected
                # Potentially record cycle path if needed
                return 
            
            # If not globally visited (or if revisiting allowed but not on current path)
            # if not self.visited[neighbor_node]:
            if not self.on_path[neighbor_node]: # Simplified: just check on_path for cycle
                                                # For general path finding, you might need visited array too
                                                # to avoid redundant work if multiple paths to same node exist
                                                # but those paths aren't cycles themselves.
                self._dfs_path_util(neighbor_node) #, target_node)
                # if self.found_target: return
        
        # self.path_to_target.pop() # Backtrack: remove u from current path
        self.on_path[u] = False # Backtrack: remove u from recursion stack tracker

    def check_for_cycle(self, start_node=0):
        # Reset states for a fresh check
        self.on_path = [False] * self.graph.num_vertices()
        self.has_cycle = False
        # Potentially iterate all nodes if graph is disconnected to check all components
        # For simplicity, starting from one node:
        self._dfs_path_util(start_node)
        return self.has_cycle
```
This `onPath` logic is central to cycle detection and algorithms like topological sort.

## Complexity
- **Time Complexity:** $O(V+E)$, where $V$ is the number of vertices and $E$ is the number of edges. Each vertex and each edge is visited at most a constant number of times (once for directed, twice for undirected in the adjacency list traversal).
- **Space Complexity:**
    - $O(V)$ for the `visited` array (or `onPath` array).
    - $O(H)$ for the recursion stack, where $H$ is the maximum depth of the DFS tree. In the worst case, $H$ can be $V$ (e.g., for a path graph).

## Applications of Graph DFS
- **Finding connected components.**
- **Topological sorting** (for Directed Acyclic Graphs - DAGs).
- **Detecting cycles** in a graph.
- **Pathfinding** (finding *a* path, not necessarily the shortest).
- Solving mazes and puzzles (often via backtracking, which is a form of DFS).
- Finding bridges and articulation points in a graph.

## 总结 (Summary)
- Graph DFS explores branches deeply using recursion (or an explicit stack).
- A `visited` array/set is crucial to prevent infinite loops due to cycles when the goal is to visit each node once.
- An `onPath` array/set is used to track nodes in the current recursion path, essential for cycle detection related to the current exploration path and for backtracking algorithms.
- Time complexity is $O(V+E)$. Space complexity is $O(V)$ for visited/onPath arrays and $O(H)$ for recursion depth.

---
Parent: [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal Algorithms]]
Previous: [[Interview/Concept/Data Structures/Graph/01 - Graph - Representation (Adjacency List, Adjacency Matrix)|Graph Representation]]
Next: [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Graph BFS Traversal]]
Related: [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Tree DFS]], [[Interview/Concept/Algorithms/Backtracking/index|Backtracking]]
