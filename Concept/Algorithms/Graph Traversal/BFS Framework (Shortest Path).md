---
tags: [concept/algorithms, concept/graph_traversal, concept/bfs, concept/shortest_path, type/framework, pattern/bfs, course/labuladong]
aliases: [BFS Algorithm Framework, Breadth-First Search Template, BFS for Shortest Path, BFS算法框架, BFS套路]
---

> [!NOTE] Source Annotation
> Content primarily adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/BFS 算法解题套路框架.md|BFS 算法解题套路框架 by Labuladong]].
> This note details the BFS framework, particularly for finding the shortest path in unweighted graphs or minimum steps in state-space searches.

# BFS Algorithm Framework for Shortest Path / Minimum Steps

Breadth-First Search (BFS) is a fundamental graph traversal algorithm that explores neighbor nodes first before moving to the next level neighbors. This "layer-by-layer" exploration property makes it ideal for finding the shortest path in an unweighted graph or the minimum number of steps to reach a target state in a state-space search.

Labuladong emphasizes that BFS is essentially a "暴力穷举 (brute-force exhaustive search)" that explores a graph (or state space) systematically. Its core is built upon the [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|level-order traversal of a tree]], with additions like a `visited` set to handle cycles and prevent redundant work.

## 🖼️ Core BFS Framework (Labuladong's "写法二" - Pattern 2)

This framework is widely applicable for shortest path / minimum step problems.

```python
from collections import deque

def shortest_path_bfs(start_node, target_node, get_neighbors_func, is_valid_node_func=None):
    '''
    Finds the shortest path (number of steps) from start_node to target_node.

    Args:
        start_node: The initial state/node.
        target_node: The goal state/node.
        get_neighbors_func: A function that takes a node and returns an iterable of its neighbors.
                            def get_neighbors(node) -> list_of_neighbors: ...
        is_valid_node_func: Optional function to check if a neighbor is valid (e.g., not a "deadend").
                            def is_valid(node) -> bool: ...
                            If None, all neighbors are considered valid initially.

    Returns:
        Minimum number of steps if target is reachable, otherwise -1.
        To return the actual path, the queue would store (node, path_to_node) tuples,
        or parent pointers would be tracked.
    '''
    if is_valid_node_func and not is_valid_node_func(start_node):
        return -1 # Starting in an invalid state

    queue = deque([start_node])
    visited = {start_node}  # To avoid cycles and redundant work
    steps = 0 # Represents distance from start_node

    while queue:
        level_size = len(queue)  # Number of nodes at the current level (current step)

        for _ in range(level_size): # Process all nodes at this level
            current_node = queue.popleft()

            # === Process current_node ===
            # print(f"Visiting {current_node} at step {steps}")

            if current_node == target_node:
                return steps # Shortest path found

            # === Generate and enqueue neighbors ===
            for neighbor in get_neighbors_func(current_node):
                if is_valid_node_func and not is_valid_node_func(neighbor):
                    continue # Skip invalid neighbors

                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append(neighbor)

        steps += 1 # Increment steps after processing all nodes at the current level

    return -1 # Target not reachable
```

**Key Components:**
1.  **`queue`:** A [[Interview/Concept/Programming Languages/Python/03 - Python Deque for Interviews|deque]] stores nodes to visit (FIFO). It ensures layer-by-layer exploration.
2.  **`visited`:** A [[Interview/Concept/Programming Languages/Python/06 - Python Set (Hash Set) for Interviews|set]] tracks visited nodes, preventing cycles and redundant processing.
3.  **`steps` (or `depth`, `distance`):** Counts the number of levels traversed, which equals the shortest path length in unweighted graphs.
4.  **Level-by-Level Processing:** The `level_size` and inner `for` loop ensure all nodes at the current `steps` distance are processed before moving to `steps + 1`. This is crucial for correctness in shortest path finding.
5.  **Neighbor Generation (`get_neighbors_func`):** Defines state transitions.
6.  **Validity Check (`is_valid_node_func`):** Filters out prohibited states (e.g., obstacles, deadends).

Labuladong also mentions two other "写法" (writing styles) for BFS in his "图结构的 DFS/BFS 遍历" note (see [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Graph BFS Traversal]]), but Pattern 2 above is the most common for shortest path problems due to its explicit step/level tracking.

## 🧩 Problem Abstraction: The Key Skill

The primary challenge in applying BFS is often abstracting the given problem into a graph or state-space search:
-   **Node/State Definition:** What uniquely identifies a state in the problem? (e.g., a string, a tuple, an object).
-   **Start State:** The initial configuration.
-   **Target State(s):** The goal configuration(s).
-   **Transitions (`get_neighbors_func`):** What are the valid moves or changes from one state to another? This defines the edges of the implicit graph.
-   **Constraints/Invalid States (`is_valid_node_func`):** Are there states that cannot be entered or processed?

Once these components are clearly defined, the BFS framework can be applied almost mechanically.

## 💡 Example Problems

### [[Interview/Practice/LeetCode/LC773 - Sliding Puzzle|LC773 - Sliding Puzzle]]
- **Node/State:** String representation of the 2x3 board (e.g., "412503").
- **`start_node`:** Stringified initial `board`.
- **`target_node`:** "123450".
- **`get_neighbors_func(board_str)`:** Find '0', swap with adjacent tiles based on precomputed `neighbor_map`.
- **Visualization:** `![](/algo/images/sliding_puzzle/3.jpeg)` (shows '0' moving).

### [[Interview/Practice/LeetCode/LC752 - Open the Lock|LC752 - Open the Lock]]
- **Node/State:** 4-digit string (e.g., "0000").
- **`start_node`:** "0000".
- **`target_node`:** The `target` string.
- **`get_neighbors_func(combo_str)`:** For each of 4 dials, turn up (+1) or down (-1).
- **`is_valid_node_func(combo_str)`:** Check if `combo_str` is in `deadends`.

## 🔄 Bidirectional BFS Optimization

For problems where the `target_node` is known, **Bidirectional BFS** can significantly improve efficiency by searching from both `start_node` and `target_node` simultaneously.

```tikz
\begin{tikzpicture}[
    node/.style={circle, draw, minimum size=8mm, font=\sffamily\small},
    frontier_node/.style={node, fill=blue!20},
    target_node/.style={node, fill=red!20},
    meet_node/.style={node, fill=green!30, double},
    path/.style={->, >=stealth, thick, dashed},
    label_style/.style={font=\sffamily\scriptsize, text width=3cm, align=center}
]

% Normal BFS
\node[frontier_node] (s_bfs) at (0,2.5) {Start};
\node[target_node] (t_bfs) at (6,2.5) {Target};
\draw[path, blue!50] (s_bfs) edge[bend left=10] ++(1,0.5);
\draw[path, blue!50] (s_bfs) edge[bend right=10] ++(1,-0.5);
\draw[path, blue!50] ($(s_bfs)+(1,0.5)$) edge[bend left=10] ++(1,0.3);
\draw[path, blue!50] ($(s_bfs)+(1,-0.5)$) edge[bend right=10] ++(1,-0.3);
\node[label_style] at (3,3.5) {Standard BFS:\\Expands outwards from Start};
\draw[decorate,decoration={brace,amplitude=5pt}] (0,1.5) -- (6,1.5) node[midway,below,font=\sffamily\tiny]{Search Radius $\approx d$};

% Bidirectional BFS
\node[frontier_node] (s_bibfs) at (0,0) {Start};
\node[target_node] (t_bibfs) at (6,0) {Target};
\node[meet_node] (meet) at (3,0) {Meet!};
\draw[path, blue!50] (s_bibfs) edge[bend left=10] ++(1,0.3);
\draw[path, blue!50] (s_bibfs) edge[bend right=10] ++(1,-0.3);
\draw[path, red!50] (t_bibfs) edge[bend right=10] ++(-1,0.3);
\draw[path, red!50] (t_bibfs) edge[bend left=10] ++(-1,-0.3);
\draw[path, blue!50] ($(s_bibfs)+(1,0.3)$) -- (meet);
\draw[path, red!50] ($(t_bibfs)+(-1,-0.3)$) -- (meet);
\node[label_style] at (3,-1) {Bidirectional BFS:\\Expands from Start & Target, meets in middle};
\draw[decorate,decoration={brace,amplitude=5pt}] (0,-1.8) -- (3,-1.8) node[midway,below,font=\sffamily\tiny]{Radius $\approx d/2$};
\draw[decorate,decoration={brace,amplitude=5pt}] (3,-1.8) -- (6,-1.8) node[midway,below,font=\sffamily\tiny]{Radius $\approx d/2$};
\end{tikzpicture}
```

**Conceptual Bidirectional BFS Framework:**
1.  Initialize two queues (or sets for frontiers): `q1` (for forward search from start) and `q2` (for backward search from target).
2.  Initialize two `visited` sets: `visited1` and `visited2`.
3.  Add `start_node` to `q1` and `visited1`; add `target_node` to `q2` and `visited2`.
4.  `steps = 0`.
5.  Loop while `q1` and `q2` are not empty:
    a.  (Optimization) Always expand the smaller frontier. If `len(q1) > len(q2)`, swap them (along with their `visited` sets).
    b.  Increment `steps`.
    c.  Process one level from `q1`:
        i.  `level_size = len(q1)`.
        ii. For each `node` in `q1` (dequeued):
            - Generate `neighbors`.
            - If any `neighbor` is in `q2` (or `visited2`), an intersection is found! The shortest path length is `steps` (from `q1`'s start) + `steps_for_q2` (tracked separately or inferred based on symmetric step counting). Labuladong's common pattern: if `step` is incremented *before* level expansion, path length is `step` from `q1` side + `step` from `q2` side - 1, or if `step` is counted per full expansion of a frontier (as in main pattern), then `steps_from_q1 + steps_from_q2`. A common setup where `steps` is incremented once per pair of expansions means current `steps` is distance from one end, and collision means path is `steps` from current side + `steps-1` or `steps` from other. The specific formula depends on how `steps` is counted.
            The common way: `q1` expands, increments `step`. If it meets `q2` (whose frontier is `step-1` "layers" away from its origin), total steps is `step + (step-1) = 2*step - 1`. If `q2` expands in same main loop iteration, it increments step (to `step+1`), and if meets `q1`, total path is `step + step = 2*step`.
            Labuladong's OpenLock BiBFS structure for returning path length: `return step` when `neighbor` (from `q1` expansion) is found in `q2`. This implies `step` is carefully incremented for each "half" of the expansion.
    d. (If no intersection) Update `q1` with `next_level_nodes`.
- **Benefit:** Reduces search space from $O(b^d)$ to roughly $O(2 \cdot b^{d/2})$, where $b$ is branching factor, $d$ is path length.
- **Limitation:** Requires knowing the `target_node` explicitly. Also, generating "predecessors" for backward search from target might be non-trivial if state transitions are not easily reversible.

## 总结 (Summary)
- BFS is the go-to algorithm for shortest path in unweighted graphs or minimum steps in state-space search.
- The core framework uses a queue, a `visited` set, and processes nodes layer-by-layer to correctly count steps.
- **Problem Abstraction is Critical:** Define states, start/target, transitions (neighbors), and validity.
- **Bidirectional BFS** can significantly optimize by searching from both start and target, meeting in the middle, when the target is known.
- Understanding this framework empowers solving a wide array of "minimum steps/path" problems.

---
Parent: [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal Algorithms]]
Previous: [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Graph BFS Traversal]] (general graph BFS)
Related: [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Tree BFS]]
