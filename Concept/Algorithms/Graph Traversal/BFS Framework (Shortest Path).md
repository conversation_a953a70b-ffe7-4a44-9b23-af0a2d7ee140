---
tags: [concept/algorithms, concept/graph_traversal, concept/bfs, concept/shortest_path, type/framework, pattern/bfs]
aliases: [BFS Algorithm Framework, Breadth-First Search Template, BFS for Shortest Path]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/BFS 算法解题套路框架.md]].
> This note details the BFS framework, particularly for finding the shortest path in unweighted graphs or minimum steps in state-space searches.

# BFS Algorithm Framework for Shortest Path / Minimum Steps

Breadth-First Search (BFS) is fundamentally a graph traversal algorithm that explores neighbor nodes first before moving to the next level neighbors. This property makes it ideal for finding the shortest path in an unweighted graph or the minimum number of steps to reach a target state in a state-space search.

## 🖼️ Core BFS Framework (for shortest path/steps)

This framework is based on Labuladong's "写法二" (Pattern 2) from [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Binary Tree Level-Order Traversal (BFS)]], adapted for general graph/state-space search.

```python
from collections import deque

def shortest_path_bfs(start_node, target_node, get_neighbors_func, is_valid_node_func=None):
    '''
    Finds the shortest path (number of steps) from start_node to target_node.

    Args:
        start_node: The initial state/node.
        target_node: The goal state/node.
        get_neighbors_func: A function that takes a node and returns an iterable of its neighbors.
                            def get_neighbors(node) -> list_of_neighbors: ...
        is_valid_node_func: Optional function to check if a neighbor is valid (e.g., not a "deadend").
                            def is_valid(node) -> bool: ...
                            If None, all neighbors are considered valid initially.

    Returns:
        Minimum number of steps if target is reachable, otherwise -1.
    '''
    if is_valid_node_func and not is_valid_node_func(start_node):
        return -1 # Starting in an invalid state (e.g. deadend)

    queue = deque([start_node])
    visited = {start_node}  # To avoid cycles and redundant work
    steps = 0

    while queue:
        level_size = len(queue)  # Number of nodes at the current level (current step)

        for _ in range(level_size):
            current_node = queue.popleft()

            if current_node == target_node:
                return steps

            for neighbor in get_neighbors_func(current_node):
                if is_valid_node_func and not is_valid_node_func(neighbor):
                    continue # Skip invalid neighbors (e.g., deadends)
                
                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append(neighbor)
        
        steps += 1 # Move to the next level / increment step count

    return -1 # Target not reachable
```

**Key Components:**
1.  **`queue`:** A [[Interview/Concept/Programming Languages/Python/03 - Python Deque for Interviews|deque]] to store nodes to visit (FIFO).
2.  **`visited`:** A [[Interview/Concept/Programming Languages/Python/06 - Python Set (Hash Set) for Interviews|set]] to keep track of already visited nodes to prevent cycles and redundant processing.
3.  **`steps`:** An integer to count the number of levels traversed (which corresponds to the length of the path in unweighted scenarios).
4.  **Outer `while` loop:** Continues as long as there are nodes to explore.
5.  **Inner `for` loop (with `level_size`):** Processes all nodes at the current `steps` distance from the `start_node`. This is crucial for correctly counting steps layer by layer.
6.  **Neighbor Generation:** A function `get_neighbors_func(node)` is needed to define how to get from one state to adjacent states.
7.  **Validity Check (Optional):** A function `is_valid_node_func(node)` can be used to filter out invalid states (like "deadends" in the Open the Lock problem).

## 🧩 Problem Abstraction: The Key Skill

The main challenge in applying BFS is often abstracting the given problem into a graph/state-space search:
- **What is a "node" or "state"?** (e.g., a string representation of a lock combination, a tuple representing a puzzle board).
- **What is the `start_node`?** (e.g., "0000" for the lock, initial `board` for puzzle).
- **What is the `target_node`?** (e.g., the target lock combination, the solved puzzle state).
- **How to define `get_neighbors_func(current_node)`?** What are the valid transitions from one state to another? (e.g., turning one dial of the lock, sliding the '0' tile).
- **Are there invalid states (`is_valid_node_func`)?** (e.g., `deadends` in the lock problem).

Once these are defined, the BFS framework can be applied.

### Example 1: LeetCode 752. Open the Lock
- **Node/State:** A 4-digit string (e.g., "0000", "1000").
- **`start_node`:** "0000".
- **`target_node`:** The `target` string.
- **`get_neighbors_func(combo_str)`:** For each of the 4 digits, generate two neighbors: one by turning up (+1, with '9'->'0' wrap) and one by turning down (-1, with '0'->'9' wrap). Total 8 potential neighbors.
- **`is_valid_node_func(combo_str)`:** Check if `combo_str` is in the `deadends` set. If yes, it's invalid.

```python
# Solution for LC752 (Python, using the framework logic)
from collections import deque

class SolutionLC752: # Renamed for clarity within this script
    def openLock(self, deadends: list[str], target: str) -> int:
        dead_set = set(deadends)
        
        if "0000" in dead_set:
            return -1
        if target == "0000":
            return 0

        q = deque(["0000"])
        visited = {"0000"}
        steps = 0

        while q:
            level_size = len(q)
            for _ in range(level_size):
                current_combo = q.popleft()
                if current_combo == target:
                    return steps

                for i in range(4): # For each of the 4 dials
                    original_char_code = ord(current_combo[i])
                    # Turn up
                    new_char_up_code = ord('0') if current_combo[i] == '9' else original_char_code + 1
                    neighbor_up_list = list(current_combo)
                    neighbor_up_list[i] = chr(new_char_up_code)
                    neighbor_up = "".join(neighbor_up_list)
                    if neighbor_up not in visited and neighbor_up not in dead_set:
                        visited.add(neighbor_up)
                        q.append(neighbor_up)

                    # Turn down
                    new_char_down_code = ord('9') if current_combo[i] == '0' else original_char_code - 1
                    neighbor_down_list = list(current_combo)
                    neighbor_down_list[i] = chr(new_char_down_code)
                    neighbor_down = "".join(neighbor_down_list)
                    if neighbor_down not in visited and neighbor_down not in dead_set:
                        visited.add(neighbor_down)
                        q.append(neighbor_down)
            steps += 1
        return -1
```

### Example 2: LeetCode 773. Sliding Puzzle
- **Node/State:** A string representation of the 2x3 board (e.g., "412503").
- **`start_node`:** Stringified initial `board`.
- **`target_node`:** "123450".
- **`get_neighbors_func(board_str)`:** Find the index of '0'. Based on its 2D position (derived from 1D index), find valid swap positions (neighbors). Generate new board strings for each valid swap.
    - Precomputed `neighbor_map = [[1,3],[0,2,4],[1,5],[0,4],[1,3,5],[2,4]]` helps map 1D index of '0' to 1D indices of its swappable neighbors.
- **`is_valid_node_func`:** Not explicitly needed here beyond `visited` check, as all reachable board states are "valid" unless they are the target.

The provided solution in Labuladong's text for Sliding Puzzle follows this BFS framework structure.

## 🔄 Bidirectional BFS Optimization

For problems where the `target_node` is known, BFS can sometimes be optimized using **Bidirectional BFS**.
- **Idea:** Start BFS simultaneously from `start_node` (forward search) and `target_node` (backward search, by reversing transitions if needed).
- **Termination:** The search stops when the frontiers of the two searches meet.
- **Data Structures:** Two queues (or sets, for faster intersection checks) `q1`, `q2` and two `visited` sets `visited1`, `visited2`.
- **Strategy:** In each step, expand the smaller of the two frontiers to potentially meet faster.
- **Path Length:** If `steps1` is steps from start and `steps2` from end, total steps is `steps1 + steps2`. In Labuladong's OpenLock bidirectional example, `step` is incremented once per combined expansion (one level from `q1`), and when `q1`'s neighbors are found in `q2`, `step` is the answer.

**Conceptual Bidirectional BFS Framework (for Open the Lock):**
```python
# Conceptual from Labuladong's OpenLock BiBFS
# q1 = {"0000"}
# q2 = {target}
# visited = {"0000", target}
# dead_set = set(deadends)
# step = 0
#
# while q1 and q2:
#     step += 1
#     # Always expand the smaller set
#     if len(q1) > len(q2):
#         q1, q2 = q2, q1 # Swap to expand smaller frontier
#
#     next_level_nodes = set()
#     for current_node in q1:
#         # ... generate neighbors ...
#         for neighbor in get_neighbors_func(current_node):
#             if neighbor in q2: # Intersection found
#                 return step
#             if neighbor not in visited and neighbor not in dead_set:
#                 visited.add(neighbor)
#                 next_level_nodes.add(neighbor)
#     q1 = next_level_nodes
# return -1
```
- **Benefit:** Can significantly reduce the search space explored. If branching factor is $b$ and shortest path is $d$, standard BFS explores $O(b^d)$ states. Bidirectional BFS explores roughly $2 \times O(b^{d/2})$ states, which can be much smaller.
- **Limitation:** Requires knowing the `target_node` explicitly. Not applicable if the goal is defined by a property rather than a specific state (e.g., "find any leaf node").

## 总结 (Summary)
- BFS is a go-to algorithm for shortest path in unweighted graphs or minimum steps in state-space search.
- The core framework involves a queue, a `visited` set, and layer-by-layer processing.
- **Problem Abstraction is Key:** Define states, start/target, transitions (neighbors), and validity.
- **Bidirectional BFS** can optimize by searching from both start and target, meeting in the middle, but requires a known target.
- Understanding this framework allows tackling a wide range of "minimum steps/path" problems.

---
Parent: [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal Algorithms]]
Previous: [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|Graph BFS Traversal]] (general graph BFS)
Next: [[Interview/Practice/LeetCode/LC752 - Open the Lock|LC752 - Open the Lock]] (as an example application)
Related: [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Tree BFS]]
