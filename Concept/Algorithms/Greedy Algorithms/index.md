---
tags: [index, concept/algorithms, concept/greedy]
aliases: [Greedy Algorithms Index]
---

# Greedy Algorithms

This section covers the greedy algorithmic paradigm.

## Core Concepts:
- [[Interview/Concept/Algorithms/Greedy Algorithms/00 - Greedy Algorithm - Core Framework|Greedy Algorithm - Core Framework]]
  - Greedy Choice Property
  - Optimal Substructure

## LeetCode Examples:
- [[Interview/Practice/LeetCode/LC45 - Jump Game II|LC45 - Jump Game II]]
- [[Interview/Practice/LeetCode/LC55 - Jump Game|LC55 - Jump Game]] (Can be solved with Greedy)

## Visualization
```mermaid
graph TD
    GreedyConcept["Greedy Algorithms"] --> Framework["[[Interview/Concept/Algorithms/Greedy Algorithms/00 - Greedy Algorithm - Core Framework|Framework]]"]
    Framework --> GCP["Greedy Choice Property"]
    Framework --> OS["Optimal Substructure"]

    GreedyConcept --> ExamplesLC["LeetCode Examples"]
    ExamplesLC --> LC45["[[Interview/Practice/LeetCode/LC45 - Jump Game II|LC45 Jump Game II]]"]
    ExamplesLC --> LC55["[[Interview/Practice/LeetCode/LC55 - Jump Game|LC55 Jump Game]]"]

    classDef main fill:#e6ffe6,stroke:#006400,stroke-width:2px;
    class GreedyConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
