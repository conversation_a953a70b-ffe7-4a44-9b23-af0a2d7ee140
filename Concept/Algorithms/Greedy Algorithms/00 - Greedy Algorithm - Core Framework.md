---
tags: [concept/algorithms, concept/greedy, type/framework, pattern/optimization]
aliases: [Greedy Algorithm Framework, 贪心算法框架]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/贪心算法解题套路框架.md]].
> This note outlines the general framework and thinking process for solving problems using greedy algorithms.

# Greedy Algorithm: Core Framework

Greedy algorithms make locally optimal choices at each step with the hope of finding a global optimum. While not always guaranteed to find the global optimum for all problems, they are effective for a specific class of problems where a greedy choice property holds.

## 🧠 The Essence of Greedy Algorithms

Labuladong uses a simple example: if you have 1-yuan and 100-yuan banknotes and can choose 10 notes to maximize total value, the greedy choice is to always pick a 100-yuan note. This intuitively leads to the optimal solution.

The core idea is that a series of "best" local choices will lead to the "best" overall solution.

**The Computer's Perspective (Exhaustive Search First):**
Algorithmically, one should first consider exhaustive search. For the banknote problem (choose 10 notes, options 1-yuan or 100-yuan):
- This is 10 choices, each with 2 options, leading to $2^{10}$ possibilities.
- A recursive solution might look like:
  ```python
  # def findMax(n_choices_left):
  #     if n_choices_left == 0: return 0
  #     # Option 1: take 1-yuan note
  #     res1 = 1 + findMax(n_choices_left - 1)
  #     # Option 2: take 100-yuan note
  #     res2 = 100 + findMax(n_choices_left - 1)
  #     return max(res1, res2)
  ```
- **Greedy Optimization:** We observe that `100 + findMax(n-1)` is always greater than `1 + findMax(n-1)`. This "greedy choice property" allows us to simplify the recursion to always pick 100. The complexity drops from $O(2^N)$ to $O(N)$ (iterative) or $O(1)$ (direct formula $100 \times N$) in this specific simple example after optimization.

The visualization for this from Labuladong (`div_greedy-cash-example`) shows the full recursive tree and how greedy choice prunes it.

## 🤔 When Can We Use a Greedy Approach?

A problem can be solved using a greedy algorithm if it exhibits two key properties:

1.  **Greedy Choice Property:**
    A globally optimal solution can be arrived at by making a locally optimal (greedy) choice. This means that the choice made at each step does not depend on future choices and does not prevent reaching the global optimum.
    Proving this property can sometimes involve an "exchange argument" (proof by contradiction): assume there's a better solution that doesn't include the greedy choice, then show that by including the greedy choice, you can achieve an equally good or better solution.

2.  **Optimal Substructure:**
    An optimal solution to the problem contains within it optimal solutions to subproblems. This is similar to [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming]]. If a greedy choice reduces the problem to a smaller subproblem, the optimal solution to this subproblem, combined with the greedy choice, should yield an optimal solution to the original problem.

## 🛠️ General Steps for Designing a Greedy Algorithm

1.  **Understand the Problem:** Clearly define what needs to be optimized (minimized or maximized).
2.  **Identify Greedy Choices:** Determine what a "local optimum" or "best immediate choice" looks like at each step. This often involves sorting the input data based on some criteria.
3.  **Prove Correctness (Informally or Formally):**
    - Verify the greedy choice property. Does making this local choice prevent finding the global optimum?
    - Verify optimal substructure. Does solving the subproblem (after making the greedy choice) optimally lead to an overall optimal solution?
4.  **Implement the Algorithm:** Iterate through the problem, making greedy choices at each step and building up the solution.

## Visualization: Example - Activity Selection Problem

A classic greedy problem: Given a set of activities with start and finish times, select the maximum number of non-overlapping activities.

- **Greedy Choice:** Sort activities by their finish times. Select the activity that finishes earliest. Then, from the remaining compatible activities, select the next one that finishes earliest, and so on.

```tikz
\begin{tikzpicture}[
    activity/.style={rectangle, draw, fill=blue!20, minimum height=0.6cm, text centered, font=\sffamily\small},
    time_axis/.style={->, thick},
    selected_activity/.style={activity, fill=green!30}
]
    % Time axis
    \draw[time_axis] (0,0) -- (11,0) node[right] {Time};
    \foreach \x in {0,...,10} {
        \draw (\x,0.1) -- (\x,-0.1) node[below, font=\tiny] {\x};
    }

    % Activities (start_time, end_time, label_y_offset, name)
    % A: [1,4], B: [3,5], C: [0,6], D: [5,7]
    % Sorted by finish time: A(1,4), B(3,5), C(0,6), D(5,7)

    % Activities on timeline
    \node[selected_activity] (act_A) at (2.5, 1.5) {A (1-4)}; \draw (1,1.3) rectangle (4,1.7);
    \node[activity, fill=red!20] (act_B) at (4, 2.3) {B (3-5)}; \draw (3,2.1) rectangle (5,2.5); % Conflicts
    \node[activity, fill=red!20] (act_C) at (3, 3.1) {C (0-6)}; \draw (0,2.9) rectangle (6,3.3); % Conflicts
    \node[selected_activity] (act_D) at (6, 1.5) {D (5-7)}; \draw (5,1.3) rectangle (7,1.7);

    \node at (5.5, -1) [draw, fill=yellow!10, rounded corners, text width=9cm, align=center, font=\sffamily\small]
    {
        1. Activities sorted by finish time: A(4), B(5), C(6), D(7), ...\\
        2. Select A (finishes at 4). Last finish time = 4.\\
        3. Consider B (starts 3, ends 5). Conflicts (3 < 4). Skip.\\
        4. Consider C (starts 0, ends 6). Conflicts (0 < 4). Skip.\\
        5. Consider D (starts 5, ends 7). Compatible (5 >= 4). Select D. Last finish time = 7.\\
        Selected: A, D.
    };
\end{tikzpicture}
```

## Greedy vs. Dynamic Programming
- **Greedy:** Makes a choice that looks best *now* and sticks with it. Doesn't reconsider past choices. Simple and fast IF it works.
- **DP:** Explores all possible choices (often by trying each choice and solving the resulting subproblem) and finds the best overall solution by combining optimal subproblem solutions. Generally more robust but can be more complex.

If a problem can be solved by a greedy algorithm, it's usually simpler and more efficient than a DP solution. However, not all optimization problems have the greedy choice property. The [[Interview/Practice/LeetCode/LC322 - Coin Change|Coin Change problem]] (finding minimum coins) is a classic example where a simple greedy approach (always taking the largest possible coin) fails, and DP is required.

## 总结 (Summary)
- Greedy algorithms aim for a global optimum by making locally optimal choices at each step.
- Success relies on the **greedy choice property** (local optimum leads to global optimum) and **optimal substructure** (optimal solution contains optimal sub-solutions).
- Common strategy: Sort input based on some criteria, then iterate and make greedy choices.
- Often simpler and faster than DP if applicable, but proving correctness can be non-trivial and is essential. Many problems that seem greedy are not.

---
Parent: [[Interview/Concept/Algorithms/Greedy Algorithms/index|Greedy Algorithms Index]]
Related Problems: [[Interview/Practice/LeetCode/LC45 - Jump Game II|LC45 - Jump Game II]], [[Interview/Practice/LeetCode/LC55 - Jump Game|LC55 - Jump Game]]
Related Concepts: [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming]] (for comparison)
