---
tags: [concept/algorithms, pattern/greedy, pattern/heap, topic/array, course/labuladong]
aliases: [Split Array Consecutive Subsequences, 分割数组为连续子序列模式]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/谁能想到，斗地主也能玩出算法.md
---

> [!NOTE] Source Annotation
> Content conceptualized from Labuladong's "谁能想到，斗地主也能玩出算法" ([[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/谁能想到，斗地主也能玩出算法.md|Source]]), which addresses [[Interview/Practice/LeetCode/LC659 - Split Array into Consecutive Subsequences|LC659]]. The source article is currently "loading..." for its main content, so this outlines a common approach.

# Split Array into Consecutive Subsequences Pattern (LC659)

The problem of splitting a sorted array (possibly with duplicates) into one or more subsequences, where each subsequence consists of consecutive integers and has a minimum length (e.g., at least 3), is a common algorithmic challenge. [[Interview/Practice/LeetCode/LC659 - Split Array into Consecutive Subsequences|LC659]] is a prime example.

## 🎯 Core Idea: Greedy Approach with Frequency Maps and End-Tracking

A common and effective approach is greedy:
1.  **Frequency Map (`freq`):** Count the occurrences of each number in the input array `nums`.
2.  **End Map (`need` or `tails`):** Keep track of the number of consecutive subsequences that *end* at a particular number `x`. That is, `need[x]` (or `tails[x]`) would be the count of subsequences currently ending with `x-1` and are looking for `x` to continue.

**Algorithm Intuition:**
Iterate through each number `num` in `nums`:
-   If `freq[num] == 0`, this `num` has already been used up; continue.
-   **Option 1: Append to an existing subsequence.**
    -   If `need[num] > 0` (there's a subsequence ending at `num-1` waiting for `num`):
        -   Decrement `freq[num]` (use one `num`).
        -   Decrement `need[num]` (one less subsequence needs `num`).
        -   Increment `need[num+1]` (this subsequence now ends at `num` and needs `num+1`).
-   **Option 2: Start a new subsequence.**
    -   Else if `num` can start a new subsequence of length at least 3 (i.e., `freq[num] > 0`, `freq[num+1] > 0`, `freq[num+2] > 0`):
        -   Decrement `freq[num]`, `freq[num+1]`, `freq[num+2]`.
        -   Increment `need[num+3]` (a new subsequence `[num, num+1, num+2]` is formed, now needing `num+3`).
-   **Option 3: Cannot place `num`.**
    -   Else (cannot append `num` to an existing subsequence and cannot start a new one):
        -   It's impossible to partition; return `False`.

If all numbers are processed successfully, return `True`.

## Python Implementation Sketch (for LC659)
```python
import collections

class SolutionLC659:
    def isPossible(self, nums: list[int]) -> bool:
        freq = collections.Counter(nums)
        # need[x] stores the number of consecutive subsequences
        # that previously ended at x-1 and are now looking for x.
        need = collections.defaultdict(int)

        for num in nums:
            if freq[num] == 0: # This number has already been used
                continue

            if need[num] > 0: # num can extend an existing subsequence
                freq[num] -= 1
                need[num] -= 1
                need[num + 1] += 1
            elif freq[num] > 0 and freq.get(num + 1, 0) > 0 and freq.get(num + 2, 0) > 0:
                # num can start a new subsequence [num, num+1, num+2]
                freq[num] -= 1
                freq[num + 1] -= 1
                freq[num + 2] -= 1
                need[num + 3] += 1 # This new subsequence now needs num+3
            else:
                # Cannot place num
                return False

        return True
```

## Complexity
- **Time Complexity:** $O(N)$ for iterating through `nums` and frequency map operations.
- **Space Complexity:** $O(N)$ for `freq` and `need` maps in the worst case (all unique numbers).

## 总结 (Summary)
- The "Split Array into Consecutive Subsequences" problem (LC659) can be solved greedily.
- Use a frequency map for available numbers and another map (`need` or `tails`) to track how many subsequences are looking for the next number.
- Prioritize appending to existing subsequences over starting new ones.
- If a number cannot be placed, the partitioning is impossible.

---
Parent: [[Interview/Concept/Algorithms/Greedy Algorithms/index|Greedy Algorithms Index]]
Related Problem: [[Interview/Practice/LeetCode/LC659 - Split Array into Consecutive Subsequences|LC659]]
