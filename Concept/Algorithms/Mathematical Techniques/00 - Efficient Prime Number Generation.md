---
tags: [concept/algorithms, concept/mathematical_techniques, topic/number_theory, topic/prime_numbers, algorithm/sieve_of_eratosthenes, course/labuladong]
aliases: [Sieve of Eratosthenes, Prime Sieve, 素数筛选法, 高效寻找素数]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/数学运算技巧/如何高效寻找素数.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/数学运算技巧/如何高效寻找素数.md|如何高效寻找素数 by Labuladong]].
> This note explains the Sieve of Eratosthenes for efficiently finding all prime numbers up to a given limit.

# Efficient Prime Number Generation: Sieve of Eratosthenes

Finding prime numbers is a fundamental task in number theory and computer science. While testing primality of a single number `n` can be done by trial division up to `sqrt(n)`, finding *all* primes up to a limit `N` is more efficiently done using a sieve method. The Sieve of Eratosthenes is a classic and highly efficient algorithm for this purpose.

## Naive Primality Test
A common way to check if a number `n` is prime is to iterate from 2 up to `sqrt(n)` and check for divisibility.
```python
# def is_prime_naive(n: int) -> bool:
#     if n <= 1: return False
#     for i in range(2, int(n**0.5) + 1):
#         if n % i == 0:
#             return False
#     return True
```
To count primes up to `N` using this, one would call `is_prime_naive` for each number, leading to roughly $O(N \sqrt{N})$ complexity, which is too slow for large `N`.

## Sieve of Eratosthenes Algorithm

The Sieve of Eratosthenes works by iteratively marking as composite (i.e., not prime) the multiples of each prime, starting with the first prime number, 2.

**Algorithm Steps:**
1.  Create a boolean array `is_prime` of size `N+1`, initialized to `True`. `is_prime[i]` will be true if `i` is prime, false otherwise. Mark 0 and 1 as not prime.
2.  Iterate from `p = 2` up to `sqrt(N)`:
    a.  If `is_prime[p]` is `True` (meaning `p` is a prime number):
        i.  Mark all multiples of `p` (starting from `p*p`) as not prime. That is, for `j = p*p, p*p + p, p*p + 2p, ...` up to `N`, set `is_prime[j] = False`.
3.  After the loop, all indices `i` for which `is_prime[i]` is `True` are prime numbers. Count them or collect them.

**Why start marking multiples from `p*p`?**
- Multiples like `2*p, 3*p, ..., (p-1)*p` would have already been marked by smaller primes (2, 3, ..., factors of `p-1`). For example, when `p=5`, `2*5=10` was marked by 2, `3*5=15` by 3, `4*5=20` by 2. The first multiple of 5 not yet marked is `5*5=25`.

**Why iterate `p` only up to `sqrt(N)`?**
- If a number `x <= N` is composite, it must have a prime factor less than or equal to `sqrt(x)`, and thus less than or equal to `sqrt(N)`. All such composite numbers `x` would have been marked by their smallest prime factor `p <= sqrt(N)`. Any number `y > sqrt(N)` that remains marked as prime at this stage must indeed be prime, as if it were composite, it would have a prime factor `<= sqrt(y)`. If `y <= N`, then `sqrt(y) <= sqrt(N)`.

**Visualization (Labuladong's GIF `![](/algo/images/prime/1.gif)`):**
The GIF shows marking multiples:
- Start with 2: Mark 4, 6, 8, ...
- Next unmarked is 3: Mark 9, 12, 15, ... (6, 12 already marked)
- Next unmarked is 5: Mark 25, 30, 35, ...
And so on.

```tikz
\begin{tikzpicture}[
    cell/.style={rectangle, draw, minimum size=0.7cm, font=\sffamily\small},
    prime_cell/.style={cell, fill=green!20},
    marked_cell/.style={cell, fill=red!20, text=gray},
    current_prime_cell/.style={cell, fill=yellow!50, thick, draw=orange}
]
    \node at (5.5, 1) {\textbf{Sieve for N=20 (Conceptual Steps)}};

    % Initial: 2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20
    \matrix[column sep=0mm, row sep=0.2cm] at (5.5, 0) {
        \node[cell]{2}; & \node[cell]{3}; & \node[cell]{4}; & \node[cell]{5}; & \node[cell]{6}; &
        \node[cell]{7}; & \node[cell]{8}; & \node[cell]{9}; & \node[cell]{10}; & \node[cell]{11}; \\
        \node[cell]{12}; & \node[cell]{13}; & \node[cell]{14}; & \node[cell]{15}; & \node[cell]{16}; &
        \node[cell]{17}; & \node[cell]{18}; & \node[cell]{19}; & \node[cell]{20}; & \\
    };
    \node at (0, -0.3) {Initial (all True)};

    % p = 2
    \matrix[column sep=0mm, row sep=0.2cm] at (5.5, -2) {
        \node[current_prime_cell]{2}; & \node[cell]{3}; & \node[marked_cell]{4}; & \node[cell]{5}; & \node[marked_cell]{6}; &
        \node[cell]{7}; & \node[marked_cell]{8}; & \node[cell]{9}; & \node[marked_cell]{10}; & \node[cell]{11}; \\
        \node[marked_cell]{12}; & \node[cell]{13}; & \node[marked_cell]{14}; & \node[cell]{15}; & \node[marked_cell]{16}; &
        \node[cell]{17}; & \node[marked_cell]{18}; & \node[cell]{19}; & \node[marked_cell]{20}; & \\
    };
    \node at (0, -2.3) {p=2 (Mark multiples of 2)};

    % p = 3 (sqrt(20) is approx 4.47, so next prime p=3 is < sqrt(N))
    \matrix[column sep=0mm, row sep=0.2cm] at (5.5, -4) {
        \node[prime_cell]{2}; & \node[current_prime_cell]{3}; & \node[marked_cell]{4}; & \node[cell]{5}; & \node[marked_cell]{6}; &
        \node[cell]{7}; & \node[marked_cell]{8}; & \node[marked_cell]{9}; & \node[marked_cell]{10}; & \node[cell]{11}; \\
        \node[marked_cell]{12}; & \node[cell]{13}; & \node[marked_cell]{14}; & \node[marked_cell]{15}; & \node[marked_cell]{16}; &
        \node[cell]{17}; & \node[marked_cell]{18}; & \node[cell]{19}; & \node[marked_cell]{20}; & \\
    };
    \node at (0, -4.3) {p=3 (Mark multiples of 3: 9,15)};
    \node at (0, -5.3) {Next p would be 5. $5*5=25 > 20$. Loop for p stops.};

    % Final
    \matrix[column sep=0mm, row sep=0.2cm] at (5.5, -6) {
        \node[prime_cell]{2}; & \node[prime_cell]{3}; & \node[marked_cell]{4}; & \node[prime_cell]{5}; & \node[marked_cell]{6}; &
        \node[prime_cell]{7}; & \node[marked_cell]{8}; & \node[marked_cell]{9}; & \node[marked_cell]{10}; & \node[prime_cell]{11}; \\
        \node[marked_cell]{12}; & \node[prime_cell]{13}; & \node[marked_cell]{14}; & \node[marked_cell]{15}; & \node[marked_cell]{16}; &
        \node[prime_cell]{17}; & \node[marked_cell]{18}; & \node[prime_cell]{19}; & \node[marked_cell]{20}; & \\
    };
    \node at (0, -6.3) {Final Primes: 2,3,5,7,11,13,17,19};
\end{tikzpicture}
```

### Python Implementation (for [[Interview/Practice/LeetCode/LC204 - Count Primes|LC204 - Count Primes]])
```python
class Solution:
    def countPrimes(self, n: int) -> int: # Counts primes strictly less than n
        if n <= 2:
            return 0

        # is_prime[i] is True if i is prime. Size n for indices 0 to n-1.
        is_prime = [True] * n 
        is_prime[0] = is_prime[1] = False # 0 and 1 are not prime

        # Outer loop iterates p up to sqrt(n-1) effectively
        # Python range(start, stop) means stop is exclusive.
        # So, up to int((n-1)**0.5) inclusive for p.
        # Or, p*p < n is equivalent to p < sqrt(n).
        for p in range(2, int(n**0.5) + 1): # Iterate up to sqrt(n)
            if is_prime[p]:
                # Mark all multiples of p (starting from p*p) as not prime
                # Multiples are p*p, p*p+p, p*p+2p, ...
                # Slice assignment is efficient in Python for this:
                # for multiple in range(p*p, n, p):
                #    is_prime[multiple] = False
                is_prime[p*p : n : p] = [False] * len(is_prime[p*p : n : p])


        # Count primes
        count = 0
        for i in range(2, n):
            if is_prime[i]:
                count += 1
        return count
```

## Complexity Analysis
- **Time Complexity:** $O(N \log \log N)$.
  - The outer loop runs up to $\sqrt{N}$.
  - The inner loop for a prime `p` marks $N/p$ multiples (approximately).
  - The sum of reciprocals of primes $\sum_{p \le N} 1/p$ is $O(\log \log N)$.
  - So, total operations roughly $N \sum_{p \le \sqrt{N}} 1/p \approx N \log \log \sqrt{N} \approx N \log \log N$.
- **Space Complexity:** $O(N)$ for the boolean `is_prime` array.

## 总结 (Summary)
- The Sieve of Eratosthenes is an ancient but highly efficient algorithm for finding all prime numbers up to a given limit `N`.
- It works by iteratively marking multiples of primes as composite.
- Key optimizations:
    - Outer loop for prime candidates `p` goes up to `sqrt(N)`.
    - Inner loop for marking multiples of `p` starts from `p*p`.
- Time complexity is $O(N \log \log N)$, space complexity is $O(N)$.
- This is the standard method for generating multiple primes in a range.

---
Parent: [[Interview/Concept/Algorithms/Mathematical Techniques/index|Mathematical Techniques]]
Related: [[Interview/Practice/LeetCode/LC204 - Count Primes|LC204 - Count Primes]]
