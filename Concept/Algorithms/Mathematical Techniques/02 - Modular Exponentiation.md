---
tags: [concept/algorithms, concept/mathematical_techniques, topic/number_theory, algorithm/modular_exponentiation, course/labuladong]
aliases: [Modular Exponentiation, Exponentiation by Squaring, 模幂运算, 快速幂]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/数学运算技巧/如何高效进行模幂运算.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/数学运算技巧/如何高效进行模幂运算.md|如何高效进行模幂运算 by Labuladong]].
> This note explains the technique of modular exponentiation (exponentiation by squaring) for calculating $(a^b) \pmod m$ efficiently.

# Modular Exponentiation (Exponentiation by Squaring)

Modular exponentiation is a technique for efficiently computing large powers of a number modulo another number, i.e., $a^b \pmod m$. A naive approach of first calculating $a^b$ and then taking the modulus is infeasible if $a^b$ is very large, as it would overflow standard integer types.

The core idea relies on two properties:
1.  **Modular Arithmetic Property:** $(X \cdot Y) \pmod m = ((X \pmod m) \cdot (Y \pmod m)) \pmod m$.
    This allows us to keep intermediate products small by applying the modulus at each step.
2.  **Exponentiation by Squaring:**
    -   If $b$ is even, $a^b = (a^{b/2})^2 = (a^2)^{b/2}$.
    -   If $b$ is odd, $a^b = a \cdot a^{b-1}$, where $b-1$ is even.

## Algorithm

The algorithm can be implemented recursively or iteratively.

**Recursive Approach:**
`power(base, exp, mod)`:
1.  Base Case: If `exp == 0`, return `1` (since $a^0 = 1$).
2.  If `exp` is even:
    -   `sub_result = power(base, exp / 2, mod)`
    -   Return `(sub_result * sub_result) % mod`
3.  If `exp` is odd:
    -   `sub_result = power(base, (exp - 1) / 2, mod)`
    -   Return `(base * sub_result * sub_result) % mod`
    (Or, more directly: `return (base * power(base, exp - 1, mod)) % mod`)

Labuladong's article provides a more refined recursive version that avoids recomputing `power(base, exp-1, mod)` unnecessarily if `exp` is odd. The key is to reduce `exp` by half.

```python
# Labuladong's recursive modular exponentiation (Python adaptation)
def power_recursive(base, exp, mod):
    if exp == 0:
        return 1
    if exp % 2 == 0:
        # (base^2)^(exp/2) % mod
        sub_result = power_recursive(base, exp // 2, mod)
        return (sub_result * sub_result) % mod
    else:
        # base * (base^(exp-1)) % mod
        return (base * power_recursive(base, exp - 1, mod)) % mod

# Example: power_recursive(2, 10, 1000)
# pow(2,10) -> 2 * pow(2,9)
# pow(2,9) -> 2 * pow(2,8)
# pow(2,8) -> pow(2,4)^2
# pow(2,4) -> pow(2,2)^2
# pow(2,2) -> pow(2,1)^2
# pow(2,1) -> 2 * pow(2,0)
# pow(2,0) -> 1
# This seems to be one version.
# The "exponentiation by squaring" core is more like:
# pow(a,b): if b is even, (a^2)^(b/2). if b is odd, a * (a^2)^((b-1)/2).
# Or, if b is even, (a^(b/2))^2. if b is odd, a * a^(b-1).
```

Labuladong's example code for `superPow` (LC372) uses a helper:
```python
# int mypow(int a, int k, int base) { // base here is the modulus
#     if (k == 0) return 1;
#     a %= base; // Ensure 'a' is within mod range initially
#     if (k % 2 == 1) { // k is odd
#         return (a * mypow(a, k - 1, base)) % base;
#     } else { // k is even
#         int sub = mypow(a, k / 2, base);
#         return (sub * sub) % base;
#     }
# }
```
This version is standard.

**Iterative Approach (Binary Exponentiation):**
This is often preferred for performance and avoiding recursion depth limits.
1.  Initialize `res = 1`.
2.  `base = base % mod`. (Ensure base is within modulus from start).
3.  While `exp > 0`:
    a.  If `exp` is odd (i.e., `exp % 2 == 1` or `exp & 1`), then `res = (res * base) % mod`.
    b.  `base = (base * base) % mod`. (Square the base).
    c.  `exp = exp // 2` (or `exp >>= 1`). (Halve the exponent).
4.  Return `res`.

This works because $b$ can be written in binary form, e.g., $b = b_k 2^k + ... + b_1 2^1 + b_0 2^0$.
Then $a^b = a^{\sum b_i 2^i} = \prod a^{b_i 2^i} = \prod (a^{2^i})^{b_i}$.
The loop processes terms $(a^{2^i})$ (which is `base` squared in each step). If $b_i=1$ (exponent is odd at that bit position), this term is multiplied into `res`.

```tikz
\begin{tikzpicture}[
    step_box/.style={rectangle, draw, rounded corners, fill=blue!10, font=\sffamily\small, text width=3.5cm, align=center},
    var_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\scriptsize, text width=2cm, align=center},
    arrow/.style={->, thick, red}
]
    \node[step_box] (init) at (0,0) {Init: `res = 1`\\ `base %= mod`};
    \node[step_box] (loop) at (4,0) {While `exp > 0`};
    \node[step_box] (check_odd) at (4,-2) {If `exp` is odd:\\`res = (res*base)%mod`};
    \node[step_box] (update_base) at (0,-2) {`base=(base*base)%mod`};
    \node[step_box] (update_exp) at (0,-4) {`exp = exp // 2`};
    \node[step_box] (return_res) at (4,-4) {Return `res`};

    \draw[arrow] (init) -- (loop);
    \draw[arrow] (loop) -- node[midway,left,font=\tiny]{(exp odd)} (check_odd);
    \draw[arrow] (loop) -- node[midway,right,font=\tiny]{(exp even)} (update_base);
    \draw[arrow] (check_odd) -- (update_base);
    \draw[arrow] (update_base) -- (update_exp);
    \draw[arrow] (update_exp) -| (loop);
    \path (loop) edge [loop above, font=\tiny, looseness=6] node {exp > 0} ();
    \draw[arrow] (loop) -- node[midway,right,font=\tiny]{(exp == 0)} (return_res);
\end{tikzpicture}
```

## Applications
- **Cryptography:** RSA algorithm relies heavily on modular exponentiation.
- **Number Theory Problems:** Calculating powers in modular fields.
- **Combinatorics:** Problems involving large numbers of combinations/permutations modulo a prime.
- **LeetCode Problem:** [[Interview/Practice/LeetCode/LC372 - Super Pow|LC372 - Super Pow]] - calculating $a^b \pmod{1337}$ where $b$ is given as an array of digits. The main challenge there is handling the large exponent $b$ using modular arithmetic properties for exponents (related to Euler's totient theorem or Fermat's Little Theorem if modulus is prime, or simply iteratively processing digits of $b$).
    - For LC372, $a^b \pmod m$. If $b = [d_k, d_{k-1}, ..., d_0]$, then $b = \sum d_i 10^i$.
    - $a^{\sum d_i 10^i} = \prod a^{d_i 10^i}$.
    - Labuladong's LC372 solution uses the property: $a^{123} = (a^{12})^{10} \cdot a^3$.
      This means if `b = [c1, c2, ..., ck]`, then `a^b = (a^[c1,...,c(k-1)])^10 * a^ck`.
      This can be solved by iterating through digits of `b`.

## Complexity
- **Time Complexity:** $O(\log b)$ for both recursive and iterative methods, as the exponent `b` is halved in each step.
- **Space Complexity:**
    - Recursive: $O(\log b)$ for the recursion stack.
    - Iterative: $O(1)$.

## 总结 (Summary)
- Modular exponentiation computes $(a^b) \pmod m$ efficiently, crucial when $a^b$ is very large.
- It uses properties of modular arithmetic $(X \cdot Y) \pmod m = ((X \pmod m) \cdot (Y \pmod m)) \pmod m$.
- Exponentiation by squaring (recursive or iterative binary exponentiation) reduces complexity to $O(\log b)$.
- The iterative method is generally preferred for space efficiency.

---
Parent: [[Interview/Concept/Algorithms/Mathematical Techniques/index|Mathematical Techniques]]
Related: [[Interview/Practice/LeetCode/LC372 - Super Pow|LC372 - Super Pow]]
