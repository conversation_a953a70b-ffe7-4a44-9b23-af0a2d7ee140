---
tags: [index, concept/algorithms, concept/mathematical_techniques]
aliases: [Mathematical Algorithms Index, Math Techniques for Algorithms]
---

# Mathematical Techniques in Algorithms

This section covers algorithmic concepts and problem-solving patterns rooted in mathematical principles, number theory, probability, and specialized arithmetic operations.

## Core Concepts:
- [[Interview/Concept/Algorithms/Mathematical Techniques/00 - Efficient Prime Number Generation|Efficient Prime Number Generation (Sieve of Eratosthenes)]]
- [[Interview/Concept/Algorithms/Mathematical Techniques/01 - Counterintuitive Probability Problems|Counterintuitive Probability Problems]]
- [[Interview/Concept/Algorithms/Mathematical Techniques/02 - Modular Exponentiation|Modular Exponentiation]]
- [[Interview/Concept/Algorithms/Mathematical Techniques/03 - Factorial Algorithm Problems|Factorial Algorithm Problems]]
- [[Interview/Concept/Algorithms/Mathematical Techniques/04 - Brain Teaser Algorithm Problems|Brain Teaser Algorithm Problems]]
- [[Interview/Concept/Algorithms/Mathematical Techniques/05 - Finding Missing and Duplicate Elements|Finding Missing and Duplicate Elements]]

## Related Topics (Often Intersect):
- [[Interview/Concept/Algorithms/Bit Manipulation/index|Bit Manipulation Techniques]] (many bit tricks have mathematical underpinnings)
- [[Interview/Concept/Algorithms/Randomized Algorithms/index|Randomized Algorithms]] (often rely on probability)

## Visualization
```mermaid
graph TD
    MathTech["Mathematical Techniques"] --> Primes["[[Interview/Concept/Algorithms/Mathematical Techniques/00 - Efficient Prime Number Generation|Prime Generation (Sieve)]]"]
    MathTech --> Probability["[[Interview/Concept/Algorithms/Mathematical Techniques/01 - Counterintuitive Probability Problems|Probability Puzzles]]"]
    MathTech --> ModExpo["[[Interview/Concept/Algorithms/Mathematical Techniques/02 - Modular Exponentiation|Modular Exponentiation]]"]
    MathTech --> FactorialProbs["[[Interview/Concept/Algorithms/Mathematical Techniques/03 - Factorial Algorithm Problems|Factorial Problems]]"]
    MathTech --> BrainTeasers["[[Interview/Concept/Algorithms/Mathematical Techniques/04 - Brain Teaser Algorithm Problems|Brain Teasers]]"]
    MathTech --> MissingDup["[[Interview/Concept/Algorithms/Mathematical Techniques/05 - Finding Missing and Duplicate Elements|Missing/Duplicate Elements]]"]

    MathTech --> BitManipLink["(Related: [[Interview/Concept/Algorithms/Bit Manipulation/index|Bit Manipulation]])"]
    MathTech --> RandAlgoLink["(Related: [[Interview/Concept/Algorithms/Randomized Algorithms/index|Randomized Algos]])"]

    classDef main fill:#fff5e6,stroke:#ffb84d,stroke-width:2px;
    class MathTech main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
