---
tags: [concept/algorithms, concept/mathematical_techniques, topic/array, topic/number_theory, pattern/math_properties, course/labuladong]
aliases: [Find Missing Duplicate, Set Mismatch Logic, 寻找缺失重复元素]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/数学运算技巧/如何同时寻找缺失和重复的元素.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/数学运算技巧/如何同时寻找缺失和重复的元素.md|如何同时寻找缺失和重复的元素 by Labuladong]].
> This note discusses techniques for finding a duplicated number and a missing number in an array that originally contained numbers from 1 to N.

# Finding Missing and Duplicate Elements (Set Mismatch)

The "Set Mismatch" problem (e.g., [[Interview/Practice/LeetCode/LC645 - Set Mismatch|LC645]]) involves an array `nums` that originally contained numbers from 1 to `N`. Due to an error, one number is duplicated, and another number is missing. The task is to find both the duplicated and the missing number.

## Common Approaches

### 1. Using a Hash Map / Frequency Count
- Iterate through `nums`, count frequencies of each number using a hash map.
- Iterate from 1 to `N`:
    - The number with frequency 2 is the duplicate.
    - The number with frequency 0 (not found in map or count is 0) is the missing one.
- **Time:** $O(N)$, **Space:** $O(N)$.

### 2. Sorting
- Sort `nums`.
- Iterate through sorted `nums`:
    - Duplicate: If `nums[i] == nums[i-1]`.
    - Missing: If `nums[i] != nums[i-1] + 1` (and `nums[i-1]` wasn't the duplicate leading to this gap). This needs careful gap analysis combined with duplicate finding. Or, after finding duplicate, sum of `1..N` vs sum of `nums` after correcting for duplicate can find missing.
- **Time:** $O(N \log N)$, **Space:** $O(1)$ or $O(N)$ depending on sort.

### 3. Mathematical Approach (Sum and Sum of Squares)
Let `dup` be the duplicated number and `miss` be the missing number.
- Let $S = \sum_{i=1}^{N} i = N(N+1)/2$.
- Let $S_{nums} = \sum_{x \in nums} x$.
- Then, $S_{nums} - S = \text{dup} - \text{miss}$. (Equation 1)

- Let $S_2 = \sum_{i=1}^{N} i^2 = N(N+1)(2N+1)/6$.
- Let $S_{2,nums} = \sum_{x \in nums} x^2$.
- Then, $S_{2,nums} - S_2 = \text{dup}^2 - \text{miss}^2 = (\text{dup} - \text{miss})(\text{dup} + \text{miss})$. (Equation 2)

From Eq 1, let $A = \text{dup} - \text{miss}$.
From Eq 2, let $B = \text{dup} + \text{miss} = (S_{2,nums} - S_2) / A$. (Ensure $A \neq 0$. If $A=0$, then dup=miss, which is impossible in this problem setup).
Now we have a system of two linear equations:
`dup - miss = A`
`dup + miss = B`
Solving this:
`2 * dup = A + B`  => `dup = (A + B) / 2`
`2 * miss = B - A`  => `miss = (B - A) / 2`
- **Time:** $O(N)$ for sums, **Space:** $O(1)$.
- **Caution:** Sums of squares can overflow standard integer types if `N` is large.

### 4. In-place Modification / Cyclic Sort like idea (Labuladong's focus in some similar problems)
- Try to place each number `x` at index `x-1`.
- Iterate through `nums`. For each `nums[i]`:
    - While `nums[i]` is not in its correct place (`nums[i] != i+1`) AND `nums[i]` is not already equal to the number at its correct target spot (`nums[i] != nums[nums[i]-1]` to avoid infinite loop on duplicate):
        - Swap `nums[i]` with `nums[nums[i]-1]`.
- After this rearrangement, iterate again:
    - If `nums[i] != i+1`, then `nums[i]` is the duplicate, and `i+1` is the missing number.
This works because the duplicate will end up in the missing number's slot, or the missing number's slot will contain the duplicate.

**Example (In-place for LC645 like):** `nums = [1,2,2,4]` (N=4. Expected dup=2, miss=3)
Initial: `[1,2,2,4]`
- `i=0, nums[0]=1`. Correct.
- `i=1, nums[1]=2`. Correct.
- `i=2, nums[2]=2`. `nums[2] != 2+1`. `target_idx_for_2 = 2-1 = 1`. `nums[2](2) == nums[nums[2]-1](nums[1]=2)`. Condition `nums[i] != nums[nums[i]-1]` is false. Don't swap. (This means `nums[1]` already holds a 2. The current `nums[2]` is the duplicate.)
- `i=3, nums[3]=4`. Correct.
Final scan:
- `nums[0]=1`. `0+1=1`. Match.
- `nums[1]=2`. `1+1=2`. Match.
- `nums[2]=2`. `2+1=3`. Mismatch! `nums[2]` (which is 2) is duplicate. `2+1` (which is 3) is missing.
- `dup=2, miss=3`.
- **Time:** $O(N)$ (each number swapped at most once to its correct place). **Space:** $O(1)$.

Labuladong's article for LC645 `如何同时寻找缺失和重复的元素.md` focuses more on the XOR method or bit manipulation for related problems where the numbers are 0 to N-1 or similar. The method described above is a general cyclic sort based approach. The article specifically suggests using XOR similar to finding two unique numbers when others appear twice, but adapted.

**LC645 Specific Solution from Labuladong (conceptual using XOR idea for a variation, not direct):**
The source article's title is about finding missing AND duplicate. The "常用位操作" article handles finding one missing OR one duplicate among pairs. A direct XOR for LC645 is more complex if values are 1..N.
The mathematical sum approach or the in-place modification (cyclic sort variant) are standard for LC645.

The source article for "如何同时寻找缺失和重复的元素" leads to LC645.
The solution mentioned there is likely the in-place modification or mathematical approach.

## 总结 (Summary)
- Finding a single duplicate and a single missing number in a 1-to-N array can be solved in various ways.
- Hash Map: $O(N)$ time, $O(N)$ space.
- Sorting: $O(N \log N)$ time, $O(1)$ or $O(N)$ space.
- Mathematical (Sums): $O(N)$ time, $O(1)$ space, watch for overflow.
- In-place Modification (Cyclic Sort variant): $O(N)$ time, $O(1)$ space. This is often the most optimal.

---
Parent: [[Interview/Concept/Algorithms/Mathematical Techniques/index|Mathematical Techniques]]
Related: [[Interview/Practice/LeetCode/LC645 - Set Mismatch|LC645]], [[Interview/Concept/Algorithms/Array Manipulation/Cyclic Sort Pattern|Cyclic Sort]] (related in-place idea)
