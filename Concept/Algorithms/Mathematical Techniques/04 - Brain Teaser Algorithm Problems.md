---
tags: [concept/algorithms, concept/mathematical_techniques, topic/brain_teaser, topic/game_theory, topic/combinatorics, course/labuladong]
aliases: [Brain Teaser Algorithms, One-Liner Algorithm Puzzles, 脑筋急转弯算法题]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/数学运算技巧/一行代码就能解决的算法题.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/数学运算技巧/一行代码就能解决的算法题.md|一行代码就能解决的算法题 by Labuladong]].
> This note discusses problems that appear complex but have surprisingly simple solutions based on identifying a core mathematical or logical insight, often reducible to a "one-liner".

# Brain Teaser Algorithm Problems

Some algorithm problems, often encountered in interviews or as puzzles, seem to require complex logic (like dynamic programming or game theory) but actually have very simple, elegant solutions once a key insight is discovered. These are often referred to as "brain teasers" where the main challenge is finding that "aha!" moment.

Labuladong highlights three such problems: Nim Game, Stone Game, and Bulb Switcher.

## 1. Nim Game (LC292)
[[Interview/Practice/LeetCode/LC292 - Nim Game|LC292 - Nim Game]]
**Problem:** You and a friend take turns removing 1 to 3 stones from a pile. The one who takes the last stone wins. You go first. Given `n` stones, can you win?
**Insight:** You lose if and only if the number of stones `n` is a multiple of 4 when it's your turn.
- If `n` is not a multiple of 4, you can always make a move (take 1, 2, or 3 stones) such that the remaining number of stones *is* a multiple of 4 for your opponent.
- If `n` *is* a multiple of 4, whatever you take (1, 2, or 3), the remainder will not be a multiple of 4 for your opponent. They can then make it a multiple of 4 for you.
**Solution:** `return n % 4 != 0;`

## 2. Stone Game (LC877)
[[Interview/Practice/LeetCode/LC877 - Stone Game|LC877 - Stone Game]]
**Problem:** Players Alex and Lee take turns choosing a pile of stones from either end of a row of piles. Total stones are odd, number of piles is even. Player with more stones wins. Alex goes first.
**Insight:** Alex (the first player) can always win if they play optimally.
- Consider the piles indexed $p_0, p_1, ..., p_{N-1}$ (where $N$ is even).
- Alex can choose to take all odd-indexed piles or all even-indexed piles.
    - If Alex takes $p_0$, Lee is left with $p_1, ..., p_{N-1}$. Lee must pick $p_1$ or $p_{N-1}$.
        - If Lee picks $p_1$, Alex can pick $p_2$ (original even index).
        - If Lee picks $p_{N-1}$, Alex can pick $p_{N-2}$ (original even index).
    - No matter what Lee does, Alex can always ensure they get all piles of a certain parity (either all original evens, or all original odds by starting with $p_0$ or $p_{N-1}$ respectively).
- Since total stones is odd, `sum(odd_indexed_piles)` cannot equal `sum(even_indexed_piles)`. Alex chooses the larger sum.
**Solution:** `return true;` (Alex always wins).

## 3. Bulb Switcher (LC319)
[[Interview/Practice/LeetCode/LC319 - Bulb Switcher|LC319 - Bulb Switcher]]
**Problem:** `n` bulbs, initially off. `n` rounds.
- Round 1: Toggle all bulbs.
- Round 2: Toggle every 2nd bulb (2, 4, 6,...).
- Round `i`: Toggle every `i`-th bulb (`i, 2i, 3i,...`).
- After `n` rounds, how many bulbs are on?
**Insight:** A bulb `k` is toggled in round `i` if `i` is a divisor of `k`.
- Bulb `k`'s final state (on/off) depends on how many times it was toggled (odd times $\rightarrow$ on, even times $\rightarrow$ off).
- The number of times bulb `k` is toggled is equal to the number of its divisors.
- Only **perfect square numbers** have an odd number of divisors. (e.g., divisors of 9 are 1, 3, 9 (3 divisors); divisors of 6 are 1, 2, 3, 6 (4 divisors)).
- So, we need to count how many perfect squares are there from 1 to `n`. This is `floor(sqrt(n))`.
**Solution:** `return (int)Math.sqrt(n);`

## Problem-Solving Approach for Brain Teasers
1.  **Don't Rush to Code:** These problems often test insight more than complex coding.
2.  **Small Examples:** Work through small `n` values manually (e.g., Nim game for n=1 to 8). Look for patterns.
3.  **Consider Invariants or Symmetries:** What properties always hold? (e.g., Stone Game parities).
4.  **Reverse Thinking:** Think about losing conditions or what the opponent would do (e.g., Nim game).
5.  **Mathematical Properties:** Connect the problem to number theory, combinatorics, or simple arithmetic (e.g., Bulb Switcher divisors).

While some problems (like Stone Game) can be solved with complex DP, the brain teaser insight provides a much simpler solution.

---
Parent: [[Interview/Concept/Algorithms/Mathematical Techniques/index|Mathematical Techniques]]
Related: [[Interview/Practice/LeetCode/LC292 - Nim Game|LC292]], [[Interview/Practice/LeetCode/LC319 - Bulb Switcher|LC319]], [[Interview/Practice/LeetCode/LC877 - Stone Game|LC877]]
