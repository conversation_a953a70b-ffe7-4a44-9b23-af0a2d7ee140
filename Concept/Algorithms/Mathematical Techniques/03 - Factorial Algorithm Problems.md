---
tags: [concept/algorithms, concept/mathematical_techniques, topic/number_theory, topic/factorial, course/labuladong]
aliases: [Factorial Problems, Trailing Zeroes in Factorial, K Zeroes in Factorial]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/数学运算技巧/讲两道常考的阶乘算法题.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/数学运算技巧/讲两道常考的阶乘算法题.md|讲两道常考的阶乘算法题 by Labuladong]].
> This note covers common problems related to factorials: counting trailing zeros and finding numbers `n` such that `n!` has `K` trailing zeros.

# Factorial Algorithm Problems

Problems involving factorials ($n!$) often appear in algorithmic challenges. Two common questions are:
1.  Counting the number of trailing zeros in $n!$.
2.  Given `K`, finding how many non-negative integers `n` exist such that `n!` has exactly `K` trailing zeros.

## 1. Counting Trailing Zeros in $n!$ (LC172)
[[Interview/Practice/LeetCode/LC172 - Factorial Trailing Zeroes|LC172 - Factorial Trailing Zeroes]]

**Core Idea:**
Trailing zeros in $n!$ are formed by factors of 10. Since $10 = 2 \times 5$, we need to count pairs of (2, 5) in the prime factorization of $n!$.
The number of factors of 5 will always be less than or equal to the number of factors of 2 in $n!$. Therefore, the number of trailing zeros is determined by the number of factors of 5.

**How to count factors of 5 in $n!$:**
-   Numbers contributing one factor of 5: 5, 10, 15, 20, ... (These are `n / 5`).
-   Numbers contributing an additional factor of 5 (i.e., two factors of 5 total): 25, 50, 75, ... (These are `n / 25`).
-   Numbers contributing a third factor of 5: 125, 250, ... (These are `n / 125`).
-   And so on.
The total number of factors of 5 is `floor(n/5) + floor(n/25) + floor(n/125) + ...`.

**Algorithm:**
```python
# def trailingZeroes(n: int) -> int:
#     count = 0
#     divisor = 5
#     while divisor <= n:
#         count += n // divisor
#         # Check for overflow if divisor can become very large before n/divisor becomes 0
#         if divisor > n // 5: # Prevents divisor from overflowing if n is large
#             break            # e.g. if n=100, divisor=25. Next divisor=125. 125 > 100/5=20.
#                              # If n=MAX_INT, divisor could overflow.
#         divisor *= 5
#     return count
# A safer way for divisor update if n is huge:
# while n // divisor > 0:
#    count += n // divisor
#    divisor *= 5 # This could overflow if divisor starts small and n is huge
# Better:
# count = 0
# power_of_5 = 5
# while n >= power_of_5:
#    count += n // power_of_5
#    if power_of_5 > n // 5: # Check before multiplying to prevent overflow
#        break
#    power_of_5 *= 5
# return count
# Simplest form often seen that handles large n if Python's integers are arbitrary precision:
# count = 0
# while n > 0:
#    n //= 5
#    count += n
# return count
# Let n=25. count = 25//5 = 5. n=5.
#            count = 5 + 5//5 = 6. n=1.
#            count = 6 + 1//5 = 6. n=0. Returns 6. Correct (25 contributes two 5s).
```

## 2. Number of `n` such that `n!` has `K` Trailing Zeros (LC793)
[[Interview/Practice/LeetCode/LC793 - Preimage Size of Factorial Zeroes Function|LC793 - Preimage Size of Factorial Zeroes Function]]

Let `zeta(n)` be the function that counts trailing zeros in `n!`.
We are looking for the number of `n` such that `zeta(n) = K`.

**Key Property of `zeta(n)`:**
- `zeta(n)` is a monotonically non-decreasing function. As `n` increases, `zeta(n)` either stays the same or increases.
- `zeta(n)` increases in steps. For example, `zeta(4!)=0`, `zeta(5!)=1`, `zeta(9!)=1`, `zeta(10!)=2`.
- This step-like increase means that for some `K`, there might be **zero** values of `n` such that `zeta(n)=K`, or there might be **five** such values of `n`.
    - If `zeta(n)` jumps from `K-1` to `K+x` (where `x > 0`), then no `n` gives `K` zeros.
    - If `zeta(n)` stays at `K` for `n_start, n_start+1, ..., n_start+4`, then there are 5 such `n`s. This happens when `n_start+4` is not a multiple of 5, but `n_start+5` is a multiple of 5 (e.g. `zeta(24!) = 4`, `zeta(25!) = 6`. No `n` gives 5 zeros).
Labuladong's image `![](/algo/images/factorial/1.png)` shows `zeta(n)` as a step function.

**Algorithm:**
1.  The problem asks for the *count* of such `n`. This count will always be 0 or 5.
2.  We need to find if there is *any* `n` such that `zeta(n) = K`.
3.  Since `zeta(n)` is monotonic, we can use [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|binary search]] to find an `n` such that `zeta(n) = K`.
    - More precisely, we want to find the smallest `n_left` such that `zeta(n_left) >= K`.
    - And the smallest `n_right` such that `zeta(n_right) > K`.
    - Or, find smallest `n` where `zeta(n) == K`. If it exists, result is 5. Else 0.
    - This is equivalent to finding the `left_bound` for `K` in the "image" of `zeta`.
4.  Binary search for `n` in a sufficiently large range (e.g., `0` to `5K` or `Long.MAX_VALUE`, since `zeta(n)` grows roughly as `n/4`). The upper bound can be $5K$ because $K \approx N/5 + N/25 + ... < N/4$, so $N > 4K$. A loose upper bound $N \approx 5K$ is safe for binary search. If $K=0$, $N$ can be $0,1,2,3,4$.
5.  Let `left_n_for_K = binary_search_left_bound(K)` (smallest `n` s.t. `zeta(n) >= K`).
6.  Let `left_n_for_K_plus_1 = binary_search_left_bound(K+1)` (smallest `n` s.t. `zeta(n) >= K+1`).
7.  The number of `n`s such that `zeta(n) = K` is `left_n_for_K_plus_1 - left_n_for_K`. This will be 0 or 5.

**Binary Search Helper `num_zeros(n)` (same as `trailingZeroes`):**
```python
# def num_zeros(n: int) -> int:
#     res = 0
#     divisor = 5
#     while divisor <= n:
#         res += n // divisor
#         if divisor > n // 5: break # Overflow check
#         divisor *= 5
#     return res
# Simpler version for Python's arbitrary precision integers:
# def num_zeros(n: int) -> int:
#     if n < 0: return -1 # Or based on problem for invalid input
#     res = 0
#     while n > 0:
#         n //= 5
#         res += n
#     return res
```
**Binary Search for `left_bound_n_for_target_zeros(K)`:**
Find smallest `n` such that `num_zeros(n) >= K`.
```python
# def left_bound_n(K, zero_counter_func):
#     low, high = 0, 5 * K # A sufficiently large upper bound, or MaxInt
#     ans_n = high + 1 
#     while low <= high:
#         mid_n = low + (high - low) // 2
#         zeros_for_mid_n = zero_counter_func(mid_n)
#         if zeros_for_mid_n >= K:
#             ans_n = mid_n
#             high = mid_n - 1
#         else: # zeros_for_mid_n < K
#             low = mid_n + 1
#     return ans_n
#
# count_K = left_bound_n(K, num_zeros)
# count_K_plus_1 = left_bound_n(K + 1, num_zeros)
# result = count_K_plus_1 - count_K
# This gives 0 or 5.
```
Labuladong's approach is simpler: Find *any* `n` such that `zeta(n) = K` using binary search. If found, return 5, else 0.
This means finding `n` such that `num_zeros(n) == K`.
The `binary_search_left_bound(target_zeros)` finds smallest `n` for `num_zeros(n) >= target_zeros`.
If `num_zeros(left_bound_n(K)) == K`, then such `n` exists, so answer is 5. Else 0.

## Complexity
- **LC172 (Trailing Zeros):** $O(\log_5 N)$ time.
- **LC793 (K Zeros):**
    - `num_zeros(n)` is $O(\log n)$.
    - Binary search performs $O(\log (\text{SearchRange}))$ calls to `num_zeros`. Search range is up to $O(K)$.
    - Total time: $O(\log K \cdot \log K) = O((\log K)^2)$.

## 总结 (Summary)
- **Trailing Zeros in $n!$:** Count factors of 5 in $n!$. Sum `n/5, n/25, n/125, ...`. Time $O(\log N)$.
- **`n` for `K` Zeros:**
    - The number of `n`s is always 0 or 5.
    - Use binary search to find if an `n` exists such that `zeta(n) = K`.
    - `zeta(n)` (count of trailing zeros) is monotonic.
    - The binary search for `n` would itself call the `zeta(n)` function.
    - Time $O((\log K)^2)$.

---
Parent: [[Interview/Concept/Algorithms/Mathematical Techniques/index|Mathematical Techniques]]
Related: [[Interview/Practice/LeetCode/LC172 - Factorial Trailing Zeroes|LC172]], [[Interview/Practice/LeetCode/LC793 - Preimage Size of Factorial Zeroes Function|LC793]]
