---
tags: [concept/algorithms, concept/backtracking, type/problem_solving_framework, pattern/recursion, course/labuladong]
aliases: [Ball-Box Model Backtracking, Two Perspectives of Backtracking Exhaustion, 球盒模型回溯, 回溯穷举视角]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/球盒模型：回溯算法穷举的两种视角.md
---

> [!NOTE] Source Annotation
> Content conceptualized from Labuladong's article "球盒模型：回溯算法穷举的两种视角" (which is marked as "loading..." in the provided source files).
> This note will outline the anticipated core ideas of viewing backtracking through "ball" and "box" perspectives.

# Backtracking: Two Exhaustion Perspectives (Ball-Box Model)

Labuladong often emphasizes that backtracking is a systematic way to explore a decision tree. The "Ball-Box Model" (a term likely coined for intuitive explanation) provides two distinct perspectives for structuring this exhaustive search, leading to different, yet often equivalent, recursive formulations.

Understanding these two perspectives can help in:
- Choosing a more natural or efficient way to structure the backtracking for a given problem.
- Recognizing why different valid backtracking solutions for the same problem (e.g., permutations using `used` array vs. `swap` method) exist.

## 🎯 The Core Idea: Two Perspectives for Exhaustive Search

Many backtracking problems can be framed as assigning "balls" (e.g., elements from an input array `nums`) to "boxes" (e.g., positions in a permutation, or subsets being formed), or making choices related to balls/boxes.

### Perspective 1: Iterate Through "Boxes", Choose "Balls" (盒子的视角)
- **Focus:** For each "box" (e.g., each position in a permutation to be filled), decide which "ball" (available element) to place in it.
- **Structure:** The recursion depth often corresponds to the number of boxes/positions. The `for` loop inside the `backtrack` function iterates through available balls/choices for the current box.
- **Example (Permutations - [[Interview/Practice/LeetCode/LC46 - Permutations|LC46]]):**
    - Box: The $k$-th position in the permutation being built.
    - Ball: An unused number from `nums`.
    - `backtrack(k_th_position, current_path)`:
        - If `k_th_position` is filled: if path complete, add to results.
        - For each `num` in `nums`:
            - If `num` is not used:
                - Place `num` in `current_path[k_th_position]`. Mark `num` used.
                - `backtrack(k_th_position + 1, current_path)`.
                - Unmark `num`.
    - This is the standard approach taught in [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking Core Framework]].

### Perspective 2: Iterate Through "Balls", Choose "Boxes" (球的视角)
- **Focus:** For each "ball" (e.g., each element in `nums`), decide which "box" (e.g., which position in the permutation, or which subset) it should go into, or if it should be used at all.
- **Structure:** The recursion depth often corresponds to processing each ball/element. The `for` loop inside `backtrack` might iterate through available boxes/options for the current ball.
- **Example (Permutations - `swap` based):**
    - Ball: `nums[k]` (the $k$-th element of the input array).
    - Box: A position in the permutation `0...N-1`.
    - `backtrack(k_th_element_index)`:
        - If `k_th_element_index == N`: A full permutation `nums[0...N-1]` is formed. Add copy of `nums` to results.
        - For `j` from `k_th_element_index` to `N-1`:
            - Swap `nums[k_th_element_index]` with `nums[j]` (placing `nums[j]` at the $k$-th position).
            - `backtrack(k_th_element_index + 1)` (fix the $k$-th position, recurse for $(k+1)$-th).
            - Swap back `nums[k_th_element_index]` with `nums[j]` (undo choice).
    - This "swap" method for permutations iterates through which element (`nums[j]`) should occupy the current position (`k_th_element_index`).

- **Example (Subsets - [[Interview/Practice/LeetCode/LC78 - Subsets|LC78]]):**
    - Ball: `nums[k]` (the $k$-th element).
    - Box: Either "in the current subset" or "not in the current subset".
    - `backtrack(k_th_element_index, current_subset)`:
        - Base case: If `k_th_element_index == N`: Add `current_subset` to results.
        - Choice 1 (Exclude `nums[k]`): `backtrack(k_th_element_index + 1, current_subset)`.
        - Choice 2 (Include `nums[k]`):
            - Add `nums[k]` to `current_subset`.
            - `backtrack(k_th_element_index + 1, current_subset)`.
            - Remove `nums[k]` from `current_subset` (backtrack).
    - This version for subsets doesn't have an explicit `for` loop for choices; the choices are implicit in the two recursive calls.

## ⚖️ Equivalence and Efficiency

- **Theoretical Equivalence:** Both perspectives, if implemented correctly, will explore the same solution space and find all valid solutions. They are just different ways to traverse the decision tree.
- **Practical Differences:**
    - One perspective might lead to a more intuitive or simpler implementation for a specific problem.
    - Pruning strategies or duplicate handling might be easier to implement or more effective with one perspective over the other.
    - Complexity: While asymptotically similar for exploring the whole tree, constant factors or specific pruning opportunities might make one slightly faster in practice. For instance, the swap-based permutation might avoid the overhead of a `used` array.

## 总结 (Summary)
- The "Ball-Box Model" provides two vantage points for designing backtracking algorithms:
    1.  **Box-centric:** For each position/container ("box"), decide which item ("ball") goes into it.
    2.  **Ball-centric:** For each item ("ball"), decide which position/container ("box") it belongs to, or what its fate is (e.g., included/excluded).
- Different problem structures might lend themselves more naturally to one perspective.
- Recognizing both perspectives can lead to alternative solution strategies and a deeper understanding of how backtracking exhaustively explores the solution space.
- The choice of perspective can impact implementation complexity and minor efficiency aspects.

---
Parent: [[Interview/Concept/Algorithms/Backtracking/index|Backtracking Algorithms Index]]
Related: [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking Core Framework]], [[Interview/Concept/Algorithms/Backtracking/Patterns/01 - Backtracking for Permutations, Combinations, Subsets|P/C/S Patterns]]
