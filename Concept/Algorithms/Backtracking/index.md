---
tags: [index, concept/algorithms, concept/backtracking, pattern/recursion]
aliases: [Backtracking Index, Backtracking Algorithms]
---

# Backtracking Algorithms

This section covers the backtracking algorithmic paradigm, a refined approach to brute-force search, often implemented recursively. It's used for problems that involve making a sequence of choices to find all (or some) solutions satisfying certain constraints.

## Core Concepts & Framework:
- [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking - Core Framework]]
  - Decision Tree Perspective
  - Path, Choices List, End Condition
  - Make Choice & Undo Choice (Backtrack)
- [[Interview/Concept/Algorithms/Backtracking/02 - Backtracking - Two Exhaustion Perspectives (Ball-Box Model)|Backtracking - Two Exhaustion Perspectives (Ball-Box Model)]] (Placeholder - from loading source)

## Common Patterns & Applications:
- [[Interview/Concept/Algorithms/Backtracking/Patterns/01 - Backtracking for Permutations, Combinations, Subsets|Backtracking for Permutations, Combinations, and Subsets]]
  - Subsets (e.g., [[Interview/Practice/LeetCode/LC78 - Subsets|LC78]], [[Interview/Practice/LeetCode/LC90 - Subsets II|LC90]])
  - Combinations (e.g., [[Interview/Practice/LeetCode/LC77 - Combinations|LC77]], [[Interview/Practice/LeetCode/LC39 - Combination Sum|LC39]], [[Interview/Practice/LeetCode/LC40 - Combination Sum II|LC40]])
  - Permutations (e.g., [[Interview/Practice/LeetCode/LC46 - Permutations|LC46]], [[Interview/Practice/LeetCode/LC47 - Permutations II|LC47]])
- Solving Puzzles (e.g., [[Interview/Practice/LeetCode/LC51 - N-Queens|N-Queens]], [[Interview/Practice/LeetCode/LC37 - Sudoku Solver|Sudoku]])
- Pathfinding in graphs/matrices (e.g., finding all paths)

## Visualization
```mermaid
graph TD
    BTConcept["Backtracking"] --> Framework["[[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Core Framework]]"]
    Framework --> DecisionTree["Decision Tree Model"]
    Framework --> PathChoicesEnd["Path, Choices, End Condition"]
    Framework --> MakeUndo["Make/Undo Choice"]

    BTConcept --> BallBox["[[Interview/Concept/Algorithms/Backtracking/02 - Backtracking - Two Exhaustion Perspectives (Ball-Box Model)|Ball-Box Model]]"]

    BTConcept --> Patterns["Common Patterns"]
    Patterns --> PermCombSub["[[Interview/Concept/Algorithms/Backtracking/Patterns/01 - Backtracking for Permutations, Combinations, Subsets|Permutations, Combinations, Subsets]]"]
    Patterns --> Puzzles["Solving Puzzles (N-Queens, Sudoku)"]
    Patterns --> Pathfinding["Pathfinding"]
    
    PermCombSub --> LC46_ex["[[Interview/Practice/LeetCode/LC46 - Permutations|LC46 - Permutations]] (Example)"]

    classDef main fill:#fce8b2,stroke:#f39c12,stroke-width:2px;
    class BTConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
Related: [[Interview/Concept/Algorithms/Recursion/index|Recursion Strategies]], [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|Graph DFS]] (as DFS is closely related)
