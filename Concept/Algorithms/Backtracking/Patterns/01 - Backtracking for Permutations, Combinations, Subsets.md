---
tags: [concept/algorithms, concept/backtracking, pattern/permutations, pattern/combinations, pattern/subsets, type/problem_solving_pattern, course/labuladong]
aliases: [Permutations Combinations Subsets using Backtracking, 排列组合子集问题回溯法, Backtracking P/C/S]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/回溯算法秒杀所有排列_组合_子集问题.md
---

> [!NOTE] Source Annotation
> Content conceptualized from Labuladong's article "回溯算法秒杀所有排列/组合/子集问题" (which is marked as "loading..." in the provided source files, so this note will be a high-level summary based on the title and common patterns).
> This note details how to solve permutation, combination, and subset problems using the [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|backtracking framework]].

# Backtracking for Permutations, Combinations, and Subsets

Problems involving permutations, combinations, and subsets are common in interviews and are classic applications of the backtracking algorithm. These problems require generating all possible arrangements or selections of elements from a given set, often with specific constraints.

## 🌲 Core Idea: Decision Tree Exploration

All these problems can be solved by systematically exploring a decision tree where:
- Each level of the tree represents a decision point (e.g., choosing an element).
- Paths from the root to a node represent a partial construction (e.g., a partial permutation).
- Leaf nodes represent complete, valid solutions.

The [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|backtracking framework]] is used to traverse this decision tree. The main variations lie in how "choices" are made and how "paths" (current selections) are managed.

## Key Problem Variations (Anticipated from Title)

Labuladong's title implies coverage of these main categories and their variations:

1.  **Subsets (子集):** Find all possible subsets of a given set.
    -   Example: `nums = [1,2,3]` $\rightarrow$ `[[], [1], [2], [3], [1,2], [1,3], [2,3], [1,2,3]]`
    -   [[Interview/Practice/LeetCode/LC78 - Subsets|LC78 - Subsets]] (unique elements)
    -   [[Interview/Practice/LeetCode/LC90 - Subsets II|LC90 - Subsets II]] (duplicate elements)

2.  **Combinations (组合):** Find all combinations of `k` numbers from `n` numbers (or elements from a set). Order does not matter.
    -   Example: `nums = [1,2,3,4], k=2` $\rightarrow$ `[[1,2], [1,3], [1,4], [2,3], [2,4], [3,4]]`
    -   [[Interview/Practice/LeetCode/LC77 - Combinations|LC77 - Combinations]]
    -   [[Interview/Practice/LeetCode/LC39 - Combination Sum|LC39 - Combination Sum]] (elements reusable, target sum)
    -   [[Interview/Practice/LeetCode/LC40 - Combination Sum II|LC40 - Combination Sum II]] (duplicate elements, no reuse, target sum)
    -   [[Interview/Practice/LeetCode/LC216 - Combination Sum III|LC216 - Combination Sum III]] (fixed number of elements `k`, target sum, unique elements 1-9)

3.  **Permutations (排列):** Find all possible orderings of a given set of numbers. Order matters.
    -   Example: `nums = [1,2,3]` $\rightarrow$ `[[1,2,3], [1,3,2], [2,1,3], [2,3,1], [3,1,2], [3,2,1]]`
    -   [[Interview/Practice/LeetCode/LC46 - Permutations|LC46 - Permutations]] (unique elements)
    -   [[Interview/Practice/LeetCode/LC47 - Permutations II|LC47 - Permutations II]] (duplicate elements)

**Common Constraints/Variations across P/C/S problems:**
-   **Uniqueness of input elements:** Are elements in `nums` unique or can they have duplicates?
-   **Reusability of elements:** Can an element be chosen multiple times in a single solution?
-   **Specific constraints:** Target sum, fixed size `k`, etc.

## General Backtracking Structure Notes
*(This would be populated with detailed templates and logic for each category once the "loading..." source is available. The current content in [[Interview/Concept/Algorithms/Backtracking/Patterns/01 - Backtracking for Permutations, Combinations, Subsets.md]] (from previous run) already covers this well. This generation will ensure that file exists and points to the right concepts).*

The core [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|backtracking framework]] involves:
- A recursive helper function.
- Parameters tracking the current `path` (or `track`), the `choices_list` (often implicit via iteration start index or a `used` array), and other problem-specifics like `target_sum`.
- Base cases to add valid solutions to a result list.
- A loop to iterate through choices:
    - Make a choice (add to path, mark as used).
    - Recurse.
    - Undo the choice (remove from path, unmark as used).
- Sorting the input `nums` is often crucial when handling duplicate input elements to avoid duplicate solutions.

## 总结 (Summary)
- Subsets, Combinations, and Permutations are fundamental backtracking problems.
- The core backtracking framework can be adapted by:
    - Modifying how choices are generated (e.g., using a `start_index` for combinations/subsets to avoid re-picking and ensure order, or a `used` array for permutations).
    - Adjusting recursive calls (e.g., `i+1` vs `i` for combinations with/without reuse).
    - Handling duplicate inputs by sorting and skipping.
- Visualizing the decision tree for each problem type helps in designing the backtracking logic.

---
Parent: [[Interview/Concept/Algorithms/Backtracking/index|Backtracking Algorithms Index]]
Related: [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking Core Framework]]
