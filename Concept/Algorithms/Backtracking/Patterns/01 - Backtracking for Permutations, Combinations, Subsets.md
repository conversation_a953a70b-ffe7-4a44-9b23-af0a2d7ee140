---
tags: [concept/algorithms, concept/backtracking, pattern/permutations, pattern/combinations, pattern/subsets, type/problem_solving_pattern]
aliases: [Permutations Combinations Subsets using Backtracking, 排列组合子集问题回溯法]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/回溯算法秒杀所有排列_组合_子集问题.md]] and related problem pages.
> This note details how to solve permutation, combination, and subset problems using the [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|backtracking framework]].

# Backtracking for Permutations, Combinations, and Subsets

Problems involving permutations, combinations, and subsets are common in interviews and are classic applications of the backtracking algorithm. These problems require generating all possible arrangements or selections of elements from a given set, often with specific constraints.

## 🌲 Core Idea: Decision Tree Exploration

All these problems can be solved by systematically exploring a decision tree where:
- Each level of the tree represents a decision point (e.g., choosing an element).
- Paths from the root to a node represent a partial construction (e.g., a partial permutation).
- Leaf nodes represent complete, valid solutions.

The [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|backtracking framework]] is used to traverse this decision tree. The main variations lie in how "choices" are made and how "paths" (current selections) are managed.

## Key Problem Variations

Labuladong categorizes these problems based on two main factors:
1.  **Uniqueness of elements in input `nums`**:
    - Elements are unique.
    - Elements can have duplicates.
2.  **Reusability of elements**:
    - Each element can be used at most once (no re-selection of the same element instance).
    - Each element can be used multiple times (re-selection allowed).

This leads to several common forms. We'll focus on adapting the backtracking framework for each.

### Visualization of Decision Trees

**1. Subsets / Combinations Tree (Conceptual for `nums=[1,2,3]`):**
   Each element can either be included or excluded.
   ![](/algo/images/permutation/1.jpeg)
   *(Source: Labuladong. This tree structure is typical for subset and combination problems. The path from root to any node represents a subset/combination. Ordering of elements in the input `nums` matters for how the tree is built if we want to avoid duplicate combinations.)*

**2. Permutations Tree (Conceptual for `nums=[1,2,3]`):**
   The order of chosen elements matters.
   ![](/algo/images/permutation/2.jpeg)
   *(Source: Labuladong. This tree structure is for permutation problems. Each path from root to a leaf is a full permutation.)*

## General Backtracking Structure for These Problems

```python
def solve(nums, ...):
    results = []
    current_path = []

    def backtrack(start_index, current_path, ...other_params): # start_index is common for combinations/subsets
        # 1. End Condition / Add to results
        if should_add_to_results(current_path, ...):
            results.append(current_path.copy())
            # For some problems (like subsets), every path is a solution.
            # For others (like k-combinations), only paths of length k.
            # For permutations, only full-length paths.
            # May or may not return here depending on problem (e.g. permutations require full length)

        if should_stop_exploring_this_branch(current_path, ...):
            return

        # 2. Iterate through choices
        # The 'choices' depend on the problem type (permutations vs combinations/subsets)
        # and constraints (uniqueness, reusability).
        for i in range(start_index, len(nums)): # Common for combinations/subsets to avoid duplicates
                                                # For permutations, usually iterate 0 to len(nums) and use 'used' array.
            # Optional: Pruning for duplicates in input if elements are sorted
            # if i > start_index and nums[i] == nums[i-1] and ...:
            #     continue 

            # Make choice
            current_path.append(nums[i])
            # For permutations with 'used' array: used[i] = True

            # Recurse
            # Next start_index for combinations: i + 1 (cannot reuse current element)
            # Next start_index for combinations with reusability: i (can reuse current element)
            # For permutations, pass down the full nums and updated 'used' array.
            backtrack(next_start_index_or_same, current_path, ...) 

            # Undo choice
            current_path.pop()
            # For permutations with 'used' array: used[i] = False
            
    # Initial call
    # Sort nums first if dealing with duplicates to simplify pruning.
    # if has_duplicates_and_no_reuse: nums.sort()
    backtrack(0, current_path, ...)
    return results
```

Let's break down specifics for each type.

---
### 🧩 I. Subsets (子集)

**Problem:** Find all possible subsets of a given set of numbers.
**Example:** `nums = [1,2,3]` $\rightarrow$ `[[], [1], [2], [3], [1,2], [1,3], [2,3], [1,2,3]]`

#### A. Elements Unique, No Reuse (LC78. Subsets)

- Each element can either be in a subset or not.
- The decision tree branches on "include `nums[i]`" or "exclude `nums[i]`".
- A common way to implement is to iterate, and for each element, decide whether to add it to the current subset. To avoid duplicate subsets (like `[1,2]` and `[2,1]` if order didn't matter), we process elements in order.

```python
# [[Interview/Practice/LeetCode/LC78 - Subsets|LC78 - Subsets]]
class Solution_LC78:
    def subsets(self, nums: list[int]) -> list[list[int]]:
        results = []
        current_subset = []

        def backtrack(start_index):
            # Add the subset formed by the current path
            results.append(current_subset.copy())

            # Choices: elements from start_index to end
            for i in range(start_index, len(nums)):
                # Make choice: include nums[i]
                current_subset.append(nums[i])
                # Recurse: explore subsets starting with nums[i]
                # Next element to consider is nums[i+1]
                backtrack(i + 1)
                # Undo choice: exclude nums[i] (backtrack)
                current_subset.pop()
        
        backtrack(0)
        return results
```
- `start_index` ensures that we only consider elements at or after `start_index`, preventing re-selection of earlier elements in a different order, thus generating unique subsets.
- Every node in the decision tree represents a valid subset.

#### B. Elements Can Have Duplicates, No Reuse (LC90. Subsets II)

- If `nums = [1,2,2]`, subsets are `[], [1], [2], [1,2], [2,2], [1,2,2]`. `[2]` from first '2' is same as `[2]` from second '2'.
- **Strategy:** Sort `nums` first. Then, in the `for` loop for choices, if `nums[i]` is the same as `nums[i-1]` and we *didn't* pick `nums[i-1]` in the *previous level of decision* (i.e., `i > start_index`), skip `nums[i]`. This avoids duplicate subsets caused by identical elements.

```python
# [[Interview/Practice/LeetCode/LC90 - Subsets II|LC90 - Subsets II]]
class Solution_LC90:
    def subsetsWithDup(self, nums: list[int]) -> list[list[int]]:
        results = []
        current_subset = []
        nums.sort() # Crucial for duplicate handling

        def backtrack(start_index):
            results.append(current_subset.copy())

            for i in range(start_index, len(nums)):
                # Pruning: if current element is same as previous,
                # and previous was NOT chosen in this path (i > start_index means
                # we are considering a new element at the current depth, not recursing deeper),
                # skip to avoid duplicate subsets.
                if i > start_index and nums[i] == nums[i-1]:
                    continue
                
                current_subset.append(nums[i])
                backtrack(i + 1)
                current_subset.pop()
        
        backtrack(0)
        return results
```

---
### 🔄 II. Combinations (组合)

**Problem:** Find all combinations of `k` numbers from `n` numbers (e.g., `nums = [1,2,3,4]`, `k=2`).
**Example:** `nums = [1,2,3,4], k=2` $\rightarrow$ `[[1,2], [1,3], [1,4], [2,3], [2,4], [3,4]]`
- Combinations are essentially subsets of a fixed size `k`. The logic is very similar to subsets.

#### A. Elements Unique, No Reuse, Fixed Size `k` (LC77. Combinations)

```python
# [[Interview/Practice/LeetCode/LC77 - Combinations|LC77 - Combinations]]
class Solution_LC77:
    def combine(self, n: int, k: int) -> list[list[int]]:
        # nums would be [1, 2, ..., n]
        nums = list(range(1, n + 1))
        results = []
        current_combination = []

        def backtrack(start_index):
            # End condition: combination has reached size k
            if len(current_combination) == k:
                results.append(current_combination.copy())
                return

            # Pruning: if remaining elements are not enough to form a k-sized combination
            # (len(nums) - i) < (k - len(current_combination))
            # i.e., nums_to_pick_from < nums_needed
            # if (len(nums) - start_index) < (k - len(current_combination)):
            #     return

            for i in range(start_index, len(nums)):
                # Optimization: if we pick nums[i], we need k - len(current_combination) - 1 more elements.
                # We must have enough elements left: len(nums) - 1 - i >= k - len(current_combination) - 1
                # This means: len(nums) - i >= k - len(current_combination)
                if (len(nums) - i) < (k - len(current_combination)):
                    break # Further elements won't make it either.

                current_combination.append(nums[i])
                backtrack(i + 1) # Next element must be > current, so i + 1
                current_combination.pop()
        
        backtrack(0)
        return results
```
- The end condition checks `len(current_combination) == k`.

#### B. Elements Can Have Duplicates, No Reuse, Target Sum (LC40. Combination Sum II)

- Given `candidates` (may have duplicates) and `target`. Find unique combinations summing to `target`. Each number used once.
- **Strategy:** Sort `candidates`. Use `start_index` for `backtrack`. Skip duplicates as in Subsets II.

```python
# [[Interview/Practice/LeetCode/LC40 - Combination Sum II|LC40 - Combination Sum II]]
class Solution_LC40:
    def combinationSum2(self, candidates: list[int], target: int) -> list[list[int]]:
        results = []
        current_combination = []
        candidates.sort()

        def backtrack(start_index, current_sum):
            if current_sum == target:
                results.append(current_combination.copy())
                return
            if current_sum > target: # Pruning
                return

            for i in range(start_index, len(candidates)):
                # Skip duplicates: if current element is same as previous,
                # and we are not at the very first element of this level's choices (i > start_index)
                if i > start_index and candidates[i] == candidates[i-1]:
                    continue
                
                # Pruning: if current candidate makes sum exceed target
                if current_sum + candidates[i] > target:
                    break # Since candidates is sorted, further elements will also exceed

                current_combination.append(candidates[i])
                backtrack(i + 1, current_sum + candidates[i]) # i + 1: cannot reuse same element instance
                current_combination.pop()

        backtrack(0, 0)
        return results
```

#### C. Elements Unique, Can Reuse, Target Sum (LC39. Combination Sum)

- Each number can be used multiple times.
- **Strategy:** When recursing, `backtrack(i, ...)` instead of `backtrack(i + 1, ...)`. This allows `candidates[i]` to be chosen again.

```python
# [[Interview/Practice/LeetCode/LC39 - Combination Sum|LC39 - Combination Sum]]
class Solution_LC39:
    def combinationSum(self, candidates: list[int], target: int) -> list[list[int]]:
        results = []
        current_combination = []
        # No need to sort if elements are unique and can be reused, 
        # but sorting can help with pruning if done carefully.
        # For this standard version, sorting isn't strictly necessary but often done.
        # nums.sort() 

        def backtrack(start_index, current_sum):
            if current_sum == target:
                results.append(current_combination.copy())
                return
            if current_sum > target: # Pruning
                return

            for i in range(start_index, len(candidates)):
                # No duplicate skipping needed if elements are unique.
                # If candidates could have duplicates AND reuse, sorting + skipping would be needed.
                
                # Pruning: if current candidate makes sum exceed target
                # This pruning is more effective if candidates are sorted.
                # if current_sum + candidates[i] > target:
                #     if candidates_are_sorted: break 
                #     else: continue 

                current_combination.append(candidates[i])
                # Key difference: backtrack(i, ...) allows reusing candidates[i]
                backtrack(i, current_sum + candidates[i]) 
                current_combination.pop()
        
        backtrack(0, 0)
        return results
```

---
### 🔢 III. Permutations (排列)

**Problem:** Find all possible orderings of a given set of numbers.
**Example:** `nums = [1,2,3]` $\rightarrow$ `[[1,2,3], [1,3,2], [2,1,3], [2,3,1], [3,1,2], [3,2,1]]`

#### A. Elements Unique, No Reuse (LC46. Permutations)

- **Strategy:** Use a `used` boolean array to keep track of which elements from `nums` have been included in the `current_permutation`. Iterate `0..len(nums)-1` for choices at each step.

```python
# [[Interview/Practice/LeetCode/LC46 - Permutations|LC46 - Permutations]]
class Solution_LC46:
    def permute(self, nums: list[int]) -> list[list[int]]:
        results = []
        current_permutation = []
        used = [False] * len(nums)

        def backtrack():
            if len(current_permutation) == len(nums):
                results.append(current_permutation.copy())
                return

            for i in range(len(nums)):
                if used[i]:
                    continue # Element already used
                
                current_permutation.append(nums[i])
                used[i] = True
                
                backtrack()
                
                used[i] = False
                current_permutation.pop()
        
        backtrack()
        return results
```

#### B. Elements Can Have Duplicates, No Reuse (LC47. Permutations II)

- If `nums = [1,1,2]`, permutations: `[1,1,2], [1,2,1], [2,1,1]`.
- **Strategy:** Sort `nums` first. Use a `used` array. Add a condition to skip duplicates: if `nums[i] == nums[i-1]` and `used[i-1]` is `False` (meaning `nums[i-1]` was chosen, used in a permutation, and then *backtracked* / unused for the current exploration path at this level), then skip `nums[i]`. This ensures that for a set of identical numbers, they are picked in a fixed relative order within the permutation generation process to avoid duplicates.

```python
# [[Interview/Practice/LeetCode/LC47 - Permutations II|LC47 - Permutations II]]
class Solution_LC47:
    def permuteUnique(self, nums: list[int]) -> list[list[int]]:
        results = []
        current_permutation = []
        nums.sort() # Crucial for duplicate handling
        used = [False] * len(nums)

        def backtrack():
            if len(current_permutation) == len(nums):
                results.append(current_permutation.copy())
                return

            for i in range(len(nums)):
                if used[i]:
                    continue
                
                # Pruning for duplicates:
                # If current num is same as previous, AND previous num is NOT used yet (used[i-1] == False),
                # it means the permutation starting with previous num (which is same as current) has already
                # been generated and fully explored (that's why used[i-1] is false now).
                # So, we skip current num to avoid duplicate permutations.
                if i > 0 and nums[i] == nums[i-1] and not used[i-1]: # Important: not used[i-1]
                    continue
                
                current_permutation.append(nums[i])
                used[i] = True
                
                backtrack()
                
                used[i] = False
                current_permutation.pop()
                
        backtrack()
        return results
```
**Explanation of `not used[i-1]` for Permutations II:**
When `nums[i] == nums[i-1]`:
- If `used[i-1]` is `True`, it means `nums[i-1]` is part of the *current* `current_permutation` being built. We are allowed to pick `nums[i]` (the second identical number).
- If `used[i-1]` is `False`, it means `nums[i-1]` was considered at this level of decision (or an earlier identical number), included in some permutations, and then backtracked (marked as unused). Now, if we consider `nums[i]` (which is identical to `nums[i-1]`), we would generate permutations that are duplicates of those already generated starting with `nums[i-1]`. So we skip. This ensures identical elements are picked in their original sorted order.

## 总结 (Summary)

- **Subsets & Combinations:** Generally use a `start_index` parameter in `backtrack(start_index, ...)` to ensure elements are picked in order and avoid duplicates (for combinations) or redundant explorations.
    - To allow reuse of elements (e.g., Combination Sum): recurse with `backtrack(i, ...)` (same index).
    - To not allow reuse: recurse with `backtrack(i + 1, ...)` (next index).
- **Permutations:** Generally iterate through all elements `0..len(nums)-1` at each step and use a `used` array to track elements already in the current path.
- **Handling Duplicates in Input (`nums`):**
    - **Always sort `nums` first.**
    - **Subsets/Combinations (no reuse):** If `i > start_index and nums[i] == nums[i-1]`, skip `nums[i]`.
    - **Permutations (no reuse):** If `i > 0 and nums[i] == nums[i-1] and not used[i-1]`, skip `nums[i]`.

Mastering these distinctions and the core backtracking template allows for a unified approach to this entire class of problems. The key is careful management of the "choices list" (often implicit via `start_index` or `used` array) and the conditions for pruning duplicate solution branches.

---
Parent: [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking - Core Framework]]
Related LeetCode Problems:
- [[Interview/Practice/LeetCode/LC78 - Subsets|LC78 - Subsets]]
- [[Interview/Practice/LeetCode/LC90 - Subsets II|LC90 - Subsets II]] (Placeholder)
- [[Interview/Practice/LeetCode/LC77 - Combinations|LC77 - Combinations]] (Placeholder)
- [[Interview/Practice/LeetCode/LC39 - Combination Sum|LC39 - Combination Sum]] (Placeholder)
- [[Interview/Practice/LeetCode/LC40 - Combination Sum II|LC40 - Combination Sum II]] (Placeholder)
- [[Interview/Practice/LeetCode/LC216 - Combination Sum III|LC216 - Combination Sum III]] (Placeholder)
- [[Interview/Practice/LeetCode/LC46 - Permutations|LC46 - Permutations]]
- [[Interview/Practice/LeetCode/LC47 - Permutations II|LC47 - Permutations II]] (Placeholder)
