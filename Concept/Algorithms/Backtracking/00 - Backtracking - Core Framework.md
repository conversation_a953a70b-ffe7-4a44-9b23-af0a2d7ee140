---
tags: [concept/algorithms, concept/backtracking, concept/dfs, type/framework, pattern/recursion, course/labuladong]
aliases: [Backtracking Algorithm Framework, 回溯算法框架, DFS for Backtracking]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/回溯算法解题套路框架.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/回溯算法解题套路框架.md|回溯算法解题套路框架 by Labuladong]].
> This note outlines the general framework for solving problems using the backtracking algorithm.
> For deeper distinctions and other perspectives, see [[Interview/Concept/Algorithms/Recursion/01 - DFS vs Backtracking - Clarifications|DFS vs Backtracking - Clarifications]] and [[Interview/Concept/Algorithms/Backtracking/02 - Backtracking - Two Exhaustion Perspectives (Ball-Box Model)|Ball-Box Model for Backtracking]].

# Backtracking: Core Framework

Backtracking is an algorithmic technique for solving problems recursively by trying to build a solution incrementally, one piece at a time, removing those solutions that fail to satisfy the constraints of the problem at any point in time (this "removing" is the "backtracking").

Abstractly, solving a backtracking problem is like traversing a **decision tree**. Each leaf node of this tree represents a potential complete solution. The goal is to explore this tree to find all (or some) valid solutions.

## 🌲 The Decision Tree Perspective

When you are at a node in the decision tree, you need to consider three things:

1.  **Path (路径):** The sequence of choices you have made so far to reach the current node.
2.  **Choices List (选择列表):** The set of available choices you can make from the current node to move to the next level of the tree.
3.  **End Condition (结束条件):** The condition that signifies you've reached a leaf node (i.e., a decision path is complete) and can no longer make choices.

## 🛠️ The Generic Backtracking Algorithm Pseudocode

The core of a backtracking algorithm is a recursive function. The general structure is:

```python
# result = [] # To store all valid solutions
#
# def backtrack(path, choices_list):
#     if is_solution(path): # End condition met
#         result.append(copy(path)) # Add a copy of the current path to results
#         return
#
#     for choice in choices_list:
#         # 1. Make a choice
#         path.add(choice)
#         # (Update choices_list for the next step, e.g., remove 'choice')
#         
#         # 2. Recurse with the new path and updated choices
#         backtrack(path, updated_choices_list)
#         
#         # 3. Undo the choice (Backtrack)
#         path.remove(choice)
#         # (Restore choices_list if necessary, e.g., add 'choice' back)
```

**Key Idea:** The magic happens in the `for` loop. Before the recursive call (`backtrack(...)`), we "make a choice." After the recursive call returns, we "undo the choice." This allows the algorithm to explore different branches of the decision tree.

### Understanding "Make Choice" and "Undo Choice"

These actions are performed at specific times during the traversal of the (conceptual) decision tree, analogous to pre-order and post-order traversal in a standard tree DFS.

```tikz
\begin{tikzpicture}[
    treenode/.style={{circle, draw, minimum size=1cm, font=\sffamily}},
    edge_label/.style={{font=\sffamily\scriptsize, midway}},
    action_box/.style={{rectangle, draw, rounded corners, fill=yellow!20, font=\sffamily\tiny, align=center}}
]

\node[treenode] (root) at (0,0) {{Root}};
\node[treenode] (child1) at (-2,-2) {{Child 1}};
\node[treenode] (child2) at (2,-2) {{Child 2}};

\draw[->] (root) -- (child1) node[edge_label, left] {{Choice A}};
\draw[->] (root) -- (child2) node[edge_label, right] {{Choice B}};

% Actions for Child 1 branch
\node[action_box, below left=0.1cm and -0.5cm of child1, text width=2.5cm] (make_A) {{
    \textbf{{Make Choice A:}}\\
    `path.add(A)`\\
    `used[A] = true` (if applicable)
}};
\draw[->, dashed, blue] (root.south west) -- (make_A.north);

\node[action_box, below right=0.1cm and -0.5cm of child1, text width=2.5cm] (undo_A) {{
    \textbf{{Undo Choice A (Backtrack):}}\\
    `path.remove(A)`\\
    `used[A] = false` (if applicable)
}};
\node at ($(child1.south) + (0,-0.5)$) {{`backtrack(path_with_A, ...)`}};
\draw[->, dashed, red] ($(child1.south) + (0,-1)$) -- (undo_A.north);


\node at (0, -4.5) [text width=8cm, align=center, draw, fill=gray!10, rounded corners]
    {{
        When `backtrack` is called for a child node (e.g., after choosing 'A'), the `path` reflects this choice.
        After the recursive call returns (meaning the entire subtree under 'A' has been explored),
        'A' is removed from `path` to allow exploration of other choices (like 'B').
    }};

\end{tikzpicture}
```
- **Pre-order position (Make Choice):** Before recursively exploring a child, update the `path` and any state variables (like `used` elements) to reflect the choice made to reach that child.
- **Post-order position (Undo Choice):** After the recursive call for a child returns (meaning that entire branch of the decision tree has been explored), revert the `path` and state variables to what they were *before* making that choice. This is crucial for exploring other branches correctly.

## Example: Permutations (LC46)

Let's illustrate with LeetCode 46: [[Interview/Practice/LeetCode/LC46 - Permutations|Permutations]]. Given `nums = [1,2,3]`, find all permutations.

The decision tree looks like this:
![Decision Tree for Permutations of [1,2,3]](Interview/labuladong%20的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/回溯算法解题套路框架.md_Files/image-20240526021029760.png)
*(Source: Labuladong's article - Visual representation of the decision tree for permuting [1,2,3])*

- **Path:** The current permutation being built (e.g., `[1, 2]`).
- **Choices List:** Numbers from `nums` not yet in the `path`.
- **End Condition:** `len(path) == len(nums)`.

**Python Solution (Illustrative, for full solution see [[Interview/Practice/LeetCode/LC46 - Permutations|LC46]]):**
```python
class Solution_LC46_Permutations: # Renamed to avoid conflict if multiple Solution classes
    def __init__(self):
        self.res = []
        self.track = [] # Current path
        self.used = []  # To keep track of used numbers

    def permute(self, nums: list[int]) -> list[list[int]]:
        self.res = []
        self.track = []
        self.used = [False] * len(nums)
        self._backtrack_permute(nums)
        return self.res

    def _backtrack_permute(self, nums: list[int]): # Renamed to avoid potential global conflicts
        if len(self.track) == len(nums): # End condition
            self.res.append(self.track.copy()) # Add a copy of the path
            return

        for i in range(len(nums)):
            if self.used[i]:
                # nums[i] is already in track, skip
                # Visual cue for pruning: ![](/algo/images/backtracking/6.jpg)
                continue
            
            # Make choice
            self.track.append(nums[i])
            self.used[i] = True
            
            self._backtrack_permute(nums) # Recurse
            
            # Undo choice
            self.track.pop()
            self.used[i] = False
```
In this permutation example:
- "Making a choice" means adding `nums[i]` to `track` and marking `used[i] = True`.
- "Undoing a choice" means removing `nums[i]` from `track` (via `pop()`) and setting `used[i] = False`.
The image `![](/algo/images/backtracking/6.jpg)` from the source shows a crossed-out branch, symbolizing pruning/skipping an already used element.

## 总结 (Summary)

1.  **Decision Tree:** Visualize the problem as exploring a decision tree.
2.  **Identify:**
    *   **Path:** What represents the choices made so far?
    *   **Choices List:** What are the available options at the current step?
    *   **End Condition:** When is a solution complete?
3.  **Framework:**
    ```python
    # def backtrack(path, choices_list):
    #     if is_solution(path):
    #         add_to_results(path)
    #         return
    #     for choice in choices_list:
    #         make_choice(path, choice)
    #         backtrack(new_path, updated_choices_list)
    #         undo_choice(path, choice)
    ```
4.  **Efficiency:** Backtracking often involves exploring many possibilities, so its time complexity can be high (e.g., $O(N!)$ for permutations). Pruning (intelligently avoiding exploration of unpromising branches) can sometimes optimize it, but the core is exhaustive search.

Understanding this framework allows you to tackle a wide range of problems involving permutations, combinations, subsets, and other search-based puzzles.
For more detailed patterns on permutations, combinations, and subsets, see [[Interview/Concept/Algorithms/Backtracking/Patterns/01 - Backtracking for Permutations, Combinations, Subsets|Backtracking for Permutations, Combinations, Subsets]].
It's also important to understand the [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|two fundamental thinking modes of recursion]], as backtracking is a form of "traversal" thinking.

---
Parent: [[Interview/Concept/Algorithms/Backtracking/index|Backtracking Algorithms Index]]
Next: [[Interview/Concept/Algorithms/Backtracking/Patterns/01 - Backtracking for Permutations, Combinations, Subsets|Backtracking for Permutations, Combinations, Subsets]]
