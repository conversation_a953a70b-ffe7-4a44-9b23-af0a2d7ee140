---
tags: [concept/algorithms, concept/complexity_analysis, type/introduction, concept/big_o_notation]
aliases: [Time Complexity Introduction, Space Complexity Introduction, Big O Notation, 算法复杂度分析指南, 算法时空复杂度分析实用指南]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/算法时空复杂度分析实用指南.md]].
> This note covers the basics of Big O notation and how to approach complexity analysis, incorporating practical advice.

# Algorithm Time and Space Complexity: A Practical Guide

Understanding how to analyze the time and space complexity of algorithms is a fundamental skill for software engineers. It helps in choosing efficient solutions and predicting performance. This guide focuses on practical aspects, especially using Big O notation.

## 💡 Why Complexity Analysis Matters

- **Predict Performance:** Estimate how an algorithm will perform as input size grows.
- **Compare Algorithms:** Objectively compare different solutions to the same problem.
- **Identify Bottlenecks:** Pinpoint parts of code that might be slow or consume too much memory.
- **Reverse Engineering Problems (Labuladong's Insight):** The constraints on input size (e.g., `N <= 10^5`) can strongly hint at the required time complexity (e.g., $O(N \log N)$ or $O(N)$ for $N=10^5$), guiding your choice of algorithm and helping avoid approaches that would be too slow.
    - If `N` is very small (e.g., `N <= 20`), an exponential solution like $O(2^N)$ or $O(N!)$ (often [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|backtracking]]) might be acceptable.
    - If `N` is around $10^3$, $O(N^2)$ might pass.
    - If `N` is $10^5$ to $10^6$, $O(N \log N)$ or $O(N)$ is usually required. For such inputs, $O(N^2)$ would likely result in a Time Limit Exceeded (TLE) error.

## Big O Notation: The Language of Complexity

Big O notation describes the **asymptotic upper bound** of an algorithm's resource usage (time or space) as the input size ($N$) approaches infinity. It focuses on the growth rate, abstracting away machine-specific constants and lower-order terms.

### Key Principles of Big O

1.  **Ignore Constant Factors:**
    - $O(2N)$ is simplified to $O(N)$.
    - $O(100)$ (a constant number of operations) is $O(1)$.
    - $O(N/2)$ is $O(N)$.
    The constant `c` in the formal definition $f(n) \le c \cdot g(n)$ accounts for these.

2.  **Keep Only the Fastest-Growing Term (Dominant Term):**
    - $O(N^2 + N + 100)$ becomes $O(N^2)$.
    - $O(N + \log N)$ becomes $O(N)$.
    - $O(2^N + N^3)$ becomes $O(2^N)$.
    As $N$ grows large, the term with the highest growth rate dominates the overall complexity.

3.  **Logarithms:**
    - The base of the logarithm usually doesn't matter in Big O notation: $O(\log_2 N) = O(\log_{10} N) = O(\ln N)$, all commonly written as $O(\log N)$. This is because $\log_a N = \frac{\log_b N}{\log_b a}$, and $1/\log_b a$ is a constant factor.
    - Logarithmic complexity often arises in algorithms that repeatedly divide the problem size (e.g., [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|binary search]], balanced tree operations, [[Interview/Concept/Algorithms/Sorting/Merge Sort|merge sort]]).

4.  **Multiple Variables:** If complexity depends on multiple input sizes (e.g., $M$ and $N$), include all relevant variables: $O(M+N)$, $O(M \cdot N)$.

### Common Complexity Classes (Growth Order)

Slowest $\rightarrow$ Fastest Growth (Higher means less efficient for large inputs):
- **$O(1)$ (Constant):** Execution time is independent of input size. E.g., accessing an array element by index, arithmetic operations.
- **$O(\log N)$ (Logarithmic):** Time increases logarithmically with input size. Very efficient. E.g., binary search, operations on balanced BSTs.
- **$O(N)$ (Linear):** Time increases linearly with input size. E.g., iterating through an array once, finding max element.
- **$O(N \log N)$ (Log-linear or Linearithmic):** Common for efficient sorting algorithms. E.g., merge sort, heap sort, quick sort (average).
- **$O(N^2)$ (Quadratic):** Time increases with the square of input size. E.g., nested loops iterating $N$ times each, simple sorting like bubble sort/selection sort.
- **$O(N^3)$ (Cubic):** E.g., triple nested loops.
- **$O(2^N)$ (Exponential):** Time grows very rapidly. Often seen in brute-force solutions to combinatorial problems (e.g., generating all subsets without optimization).
- **$O(N!)$ (Factorial):** Even faster growth. E.g., generating all permutations by brute force.

```tikz
\begin{tikzpicture}[font=\sffamily\small]
\begin{axis}[
    title={Common Complexity Growth Rates},
    xlabel={Input Size (N)},
    ylabel={Operations (Time)},
    xmin=1, xmax=15, % Adjusted xmax for better visualization of faster growing functions
    ymin=0, ymax=60, % Adjusted ymax
    legend pos=north west,
    grid=major,
    axis lines=left,
    xtick={1,5,10,15},
    ytick={0,10,20,30,40,50,60},
    no markers, 
    samples=100, % Increased samples for smoother curves
]

\addplot [domain=1:15, blue, thick, very thick] {5} node[right, pos=0.8, font=\tiny] {$O(1)$}; % Constant, scaled for visibility
\addplot [domain=1:15, red, thick] {ln(x)/ln(2) * 2.5} node[above, pos=0.7, font=\tiny] {$O(\log N)$}; % Scaled log N
\addplot [domain=1:15, green, thick] {x} node[above right, pos=0.9, font=\tiny] {$O(N)$};
\addplot [domain=1:15, orange, thick] {x*ln(x)/ln(2)} node[above, pos=0.8, font=\tiny] {$O(N \log N)$};
\addplot [domain=1:15, purple, thick, samples=16, smooth] {x^2 / 4} node[above right, pos=0.9, font=\tiny] {$O(N^2)$}; % Scaled N^2

% Note: Exponential O(2^N) and Factorial O(N!) grow too fast for this N range and ymax.
% To show O(2^N), one might plot for N up to 5 or 6 with a much larger ymax.
% Example for O(2^N): \addplot [domain=1:6, brown, thick, samples=6, smooth] {2^x};
\end{axis}
\node at (current bounding box.south) [below=5pt, text width=10cm, align=center, font=\sffamily\scriptsize]
    {Illustrative graph. Actual function shapes and crossing points depend on constant factors hidden by Big O notation. The key is the relative growth rate as N increases.};
\end{tikzpicture}
```

### Big O is an Upper Bound (Asymptotic Upper Bound)
Technically, $O(g(N))$ means the algorithm's complexity is *no worse than* $c \cdot g(N)$ for sufficiently large $N$. So, an $O(N)$ algorithm is also correctly described as $O(N^2)$, but we always strive to find the "tightest" reasonable upper bound. For example, a brute-force recursive solution to Fibonacci is $O(2^N)$. While $O(N!)$ is also an upper bound, $O(2^N)$ is a tighter and more informative one.

## 🛠️ Analyzing Non-Recursive Algorithms

### Time Complexity
1.  **Sequence of Statements:** The total time is the sum of the times for individual statements. In Big O, this becomes the maximum complexity among them. $O(A) + O(B) = O(\max(A, B))$.
2.  **Loops:**
    - Complexity of the loop body $\times$ Number of iterations.
    - Example: `for i in range(N): # O(1) operation` is $O(1) \times N = O(N)$.
    - Example: Nested loops
      ```python
      # for i in range(N):       # N iterations
      #     for j in range(M):   # M iterations
      #         # O(1) operation
      ```
      This is $N \times M \times O(1) = O(NM)$. If $M=N$, it's $O(N^2)$.
    - Example: Dependent nested loops
      ```python
      # for i in range(N):       # Outer loop N times
      #     for j in range(i): # Inner loop runs 0, 1, ..., N-1 times
      #         # O(1) operation
      ```
      Total operations: $0 + 1 + 2 + ... + (N-1) = \frac{N(N-1)}{2} = O(N^2)$.
3.  **Conditional Statements (`if-else`):** Take the complexity of the branch with the worst-case (highest) complexity.

### Space Complexity
Refers to the *auxiliary* (or extra) space used by the algorithm, not including the space taken by the input itself.
- Simple variables (integers, booleans, pointers): Usually $O(1)$ each.
- Data Structures: Space used by lists, hash maps, sets, etc., created by the algorithm.
    - E.g., creating a hash map that stores $N$ items is $O(N)$ space.
    - E.g., creating a copy of an input array of size $N$ is $O(N)$ space.

## Common Pitfalls Leading to Incorrect Complexity (Labuladong's Reminders)

1.  **Ignoring Cost of Standard Library Functions:**
    - Python `list.insert(0, val)` or `list.pop(0)` (removing from beginning) is $O(N)$, not $O(1)$.
    - Python string concatenation `s += char` in a loop can be $O(N^2)$ because strings are immutable. Each concatenation creates a new string and copies content. Prefer `"".join(list_of_chars)` for $O(N)$ construction.
    - Slicing `my_list[a:b]` creates a new list of size $b-a$, taking $O(b-a)$ time and space.
2.  **Misunderstanding Parameter Passing (especially in C++/Java):**
    - In C++, passing large objects (like `std::vector`) by value causes a full copy ($O(N)$ time and space). Pass by reference (`&`) or const reference (`const &`) if modification is not needed.
    - Python passes object references. Assigning an argument to a new large object inside a function creates that new object. Modifying a mutable object passed as an argument modifies the original.
3.  **Ignoring Standard Output Time (Print Statements):**
    - `print()` or `console.log()` are I/O operations and can be relatively slow. Excessive printing, especially in loops, can cause TLE even if the core algorithm is efficient. Remove debug prints before final submission.
4.  **Incorrect Interface Implementation Assumptions (Java specific example by Labuladong):**
    - If a Java function takes `List<Integer> list`, and your code assumes `list.get(i)` is $O(1)$ (like for `ArrayList`), but the actual passed object is a `LinkedList` (where `get(i)` is $O(N)$), your complexity analysis will be wrong. If performance on `get(i)` is critical, it might be safer to copy the input `List` into an `ArrayList`.

## Analyzing Recursive Algorithms (Brief Introduction)

Recursive algorithm complexity analysis is often more involved and typically uses one of these methods:

1.  **Recurrence Relation:** Express $T(N)$ (time for input size $N$) in terms of $T(\text{smaller } N_k)$ (time for subproblems of smaller sizes).
    - Example: [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]] has $T(N) = 2T(N/2) + O(N)$ (two subproblems of size $N/2$, plus $O(N)$ work to merge).
    - Example: Naive Fibonacci $T(N) = T(N-1) + T(N-2) + O(1)$.
2.  **Solving the Recurrence:**
    - **Master Theorem:** A "cookbook" method for solving recurrences of the form $T(N) = aT(N/b) + f(N)$. (See [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer Framework]]).
    - **Recursion Tree Method:** Visualize the recursive calls as a tree. Sum the work done at each level and across all levels.
        - Example for $T(N) = 2T(N/2) + N$: Tree has $\log N$ levels. Each level does $N$ work. Total $O(N \log N)$.
        - Example for naive Fibonacci: Tree is exponential.
    - **Substitution Method:** Guess a solution form and prove it by mathematical induction.

**Space Complexity of Recursion:**
- Primarily determined by the **maximum depth of the recursion stack**.
- If a recursive call for input $N$ makes a call for $N-1$ (e.g., factorial, some DFS), stack depth can be $O(N)$.
- If it divides the problem by a constant factor (e.g., $N/2$ in merge sort, binary search), stack depth is $O(\log N)$.
- Additional space for local variables within each recursive call also contributes, but stack depth is often the dominant factor for auxiliary space.

These are covered in more detail in notes related to specific recursive paradigms like [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming]], [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking]], and [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer]].

## Amortized Analysis

Amortized analysis considers the average cost of an operation over a sequence of operations, even if some individual operations in the sequence are expensive.
- **Example:** [[Interview/Concept/Data Structures/Array/01 - Dynamic Array - Principles and Amortized Analysis|Dynamic Array]] `append`. Most appends are $O(1)$. When the array is full, a resize operation occurs ($O(N)$ to copy elements to a new, larger array). However, resizing happens infrequently enough that the *average* cost of append over many operations is $O(1)$.
- This is important for understanding the true practical efficiency of data structures like Python's `list` or Java's `ArrayList`.

## 总结 (Summary)
- Complexity analysis, particularly using Big O notation, is crucial for evaluating algorithm efficiency and scalability.
- Big O focuses on the asymptotic growth rate, ignoring constant factors and lower-order terms.
- When analyzing code, be mindful of the cost of loops, nested structures, library function calls, and I/O.
- For non-recursive algorithms, sum complexities for sequential parts and multiply for loops.
- Recursive algorithm analysis typically involves setting up and solving recurrence relations (e.g., via recursion tree or Master Theorem) for time complexity, and considering maximum stack depth for space complexity.
- Amortized analysis provides a more realistic average-case performance for operations with occasional high costs.
- Always consider input constraints to guide your choice of algorithm complexity.

---
Parent: [[Interview/Concept/Algorithms/Complexity Analysis/index|Complexity Analysis Index]]
Next: [[Interview/Concept/Algorithms/Complexity Analysis/01 - Asymptotic Analysis Guide|Asymptotic Analysis Guide]] (for more formal details, Master Theorem, etc.)
Related: [[Interview/Practice/LeetCode/00 - LeetCode Platform Guide and Tips|LeetCode Guide]] (for context on performance limits in online judges)
