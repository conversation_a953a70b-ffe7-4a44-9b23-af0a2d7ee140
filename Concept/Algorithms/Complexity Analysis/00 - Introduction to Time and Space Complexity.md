---
tags: [concept/algorithms, concept/complexity_analysis, type/introduction, concept/big_o_notation]
aliases: [Time Complexity, Space Complexity, Big O Notation Basics, 算法复杂度入门]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/时间空间复杂度入门.md]].
> This note provides a simplified introduction for beginners. A more detailed guide is planned for later in Labuladong's tutorial structure ([[算法时空复杂度分析实用指南.md]]).

# Introduction to Time and Space Complexity

Understanding time and space complexity is fundamental to analyzing algorithms. This note provides a beginner-friendly overview.

## 📌 Core Ideas for Beginners

1.  **Big O Notation:** Complexity is expressed using Big O notation (e.g., $O(1)$, $O(N)$, $O(N^2)$, $O(\log N)$). This notation describes the growth rate of an algorithm's resource usage (time or space) as the input size increases.
    - **Approximation:** It's an estimation. Constant factors and lower-order terms are ignored.
        - $O(2N^2 + 3N + 1)$ is simplified to $O(N^2)$.
        - $O(1000N + 1000)$ is simplified to $O(N)$.

2.  **Worst-Case Analysis:** Typically, we analyze the worst-case complexity, which gives an upper bound on the resource usage.

3.  **Meaning:**
    - **Time Complexity:** Measures how the execution time of an algorithm scales with the input size. Lower is better (faster).
    - **Space Complexity:** Measures how the amount of memory an algorithm uses scales with the input size. Lower is better (less memory).
    - Generally, $N$ represents the size of the input (e.g., length of an array, number of nodes in a tree).

4.  **Simplified Estimation (for now):**
    - **Time Complexity:** Often roughly corresponds to the maximum nesting level of loops that depend on the input size. (This is a simplification; not always true, especially with recursion or complex loop conditions).
    - **Space Complexity:** Refers to the *additional* space used by the algorithm beyond the input storage. It's about how much new memory is allocated.

> [!WARNING] Simplification Caveats
> - Counting loop nests is a heuristic, not a formal definition. Recursive calls, specific loop increment patterns, or operations within loops with their own complexities (like string concatenation or library function calls) can affect the actual complexity.
> - While worst-case is common for algorithm analysis, *average-case* complexity is often important for data structure API performance (e.g., hash map operations).
> A more rigorous approach will be covered in [[Interview/Concept/Algorithms/Complexity Analysis/01 - Asymptotic Analysis Guide|Asymptotic Analysis Guide]].

## ⏱️ Time/Space Complexity Examples

### Example 1: Sum of Array Elements
```python
# Input: List of integers `nums`
# Output: Sum of all elements
def get_sum(nums: list[int]) -> int:
    s = 0 # O(1) space for 's'
    for x in nums: # Loop runs N times, where N = len(nums)
        s += x # O(1) operation inside loop
    return s
```
-   **Time Complexity: $O(N)$**
    - The loop iterates $N$ times (where $N$ is `len(nums)`).
    - Operations inside the loop are $O(1)$.
-   **Space Complexity: $O(1)$**
    - The variable `s` takes constant space. The input `nums` is not counted as *extra* space for the algorithm itself.

### Example 2: Conditional Loop
```python
# Input: Integer `n`
# Output: Sum 0 to n if n is multiple of 10, else -1
def conditional_sum(n: int) -> int:
    if n % 10 != 0: # O(1)
        return -1   # O(1)
    
    s = 0 # O(1) space
    for i in range(n + 1): # Loop runs ~N times in worst case
        s += i
    return s
```
-   **Time Complexity: $O(N)$**
    - Even though the loop only runs if `n % 10 == 0`, complexity analysis considers the worst-case scenario where the loop *does* run. The loop iterates $N+1$ times.
-   **Space Complexity: $O(1)$**
    - Uses a constant number of variables (`n`, `s`, `i`).

### Example 3: Nested Loops (Find Pair with Target Sum - Brute Force)
```python
# Input: List `nums`, integer `target`
# Output: True if any two distinct numbers sum to target, else False
def has_target_sum_pair(nums: list[int], target: int) -> bool:
    n = len(nums)
    for i in range(n): # Outer loop: N iterations
        for j in range(i + 1, n): # Inner loop: up to N-1 iterations
            if nums[i] + nums[j] == target: # O(1)
                return True
    return False
```
-   **Time Complexity: $O(N^2)$**
    - The outer loop runs $N$ times.
    - The inner loop runs approximately $N/2$ times on average for each outer loop iteration in the worst case (if no pair is found early). Total operations are roughly proportional to $N \times N = N^2$.
-   **Space Complexity: $O(1)$**
    - Uses a few variables (`n`, `i`, `j`).

### Example 4: Array Allocation
```python
# Input: Integer `n`
# Output: None (creates an array)
def create_array(n: int):
    nums = [0] * n # Allocate and initialize an array of size N
```
-   **Time Complexity: $O(N)$**
    - In Python, `[0] * n` involves allocating memory for $N$ elements and initializing them (typically to 0 for integers). This initialization step is effectively an $O(N)$ operation.
-   **Space Complexity: $O(N)$**
    - An array of size $N$ is allocated.

### Example 5: Creating a New Array from Input
```python
# Input: List `nums`
# Output: New list where each element is square of original
def square_array(nums: list[int]) -> list[int]:
    n = len(nums)
    res = [0] * n # O(N) time for allocation/init, O(N) space
    for i in range(n): # O(N) time
        res[i] = nums[i] * nums[i] # O(1)
    return res
```
-   **Time Complexity: $O(N)$**
    - $O(N)$ for `res` allocation/initialization + $O(N)$ for the loop = $O(N)$.
-   **Space Complexity: $O(N)$**
    - The `res` array of size $N$ is created.

## 总结 (Summary)
- Time and space complexity help estimate an algorithm's performance and memory usage as input size grows.
- Big O notation provides an upper bound, ignoring constants and lower-order terms.
- For beginners, time complexity can often be gauged by loop nesting, and space complexity by new data structures allocated.
- Always clarify what $N$ (or other variables in $O()$) refers to.
- These are simplified guidelines; a deeper understanding involves analyzing operations more precisely and considering different cases (worst, average, best).

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
Next: [[Interview/Concept/Data Structures/Array/00 - Array - Introduction (Static vs Dynamic)|Array - Introduction]] (as per Labuladong's flow)
Further Reading: [[Interview/Concept/Algorithms/Complexity Analysis/01 - Asymptotic Analysis Guide|Asymptotic Analysis Guide]]
