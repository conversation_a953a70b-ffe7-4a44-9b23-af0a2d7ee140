---
tags: [index, concept/algorithms, concept/complexity_analysis]
aliases: [Complexity Analysis Index, Big O Index]
---

# Complexity Analysis

This section covers concepts related to analyzing the time and space complexity of algorithms.

## Core Concepts:
- [[Interview/Concept/Algorithms/Complexity Analysis/00 - Introduction to Time and Space Complexity|Introduction to Time and Space Complexity]]
  - Big O Notation Basics
- `[[Interview/Concept/Algorithms/Complexity Analysis/01 - Asymptotic Analysis Guide|Asymptotic Analysis Guide]]` (Placeholder for detailed guide)
  - Formal Definitions (Big O, Big Omega, Big Theta)
  - Analyzing Loops and Recursion
  - Amortized Analysis

## Visualization
```mermaid
graph TD
    CA["Complexity Analysis"] --> Intro["[[Interview/Concept/Algorithms/Complexity Analysis/00 - Introduction to Time and Space Complexity|Introduction]]"]
    CA --> Guide["(Detailed Asymptotic Analysis Guide)"]
    
    Intro --> BigO["Big O Notation"]
    Guide --> FormalDefs["(Formal Definitions O, Ω, Θ)"]
    Guide --> LoopRec["(Loop/Recursion Analysis)"]
    Guide --> Amortized["(Amortized Analysis)"]

    classDef main fill:#fcf3cf,stroke:#f39c12,stroke-width:2px;
    class CA main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
