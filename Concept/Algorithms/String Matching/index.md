---
tags: [index, concept/algorithms, concept/string_matching]
aliases: [String Matching Index, String Search Algorithms]
---

# String Matching Algorithms

This section covers algorithms designed for finding occurrences of a pattern string within a larger text string.

## Core Algorithms:
- [[Interview/Concept/Algorithms/String Matching/00 - <PERSON>bin-<PERSON>rp Algorithm|Rabin-Karp Algorithm]] (Hashing-based)
- `[[Interview/Concept/Algorithms/String Matching/01 - KMP Algorithm|<PERSON><PERSON><PERSON><PERSON><PERSON> (KMP) Algorithm]]` (Placeholder)
- `[[Interview/Concept/Algorithms/String Matching/02 - <PERSON>-<PERSON> Algorithm|<PERSON>-<PERSON> Algorithm]]` (Placeholder)

## Visualization
```mermaid
graph TD
    SM["String Matching"] --> RK["[[Interview/Concept/Algorithms/String Matching/00 - Rabin-Karp Algorithm|Rabin-Karp]]"]
    SM --> KMP["(KMP Algorithm)"]
    SM --> BM["(<PERSON><PERSON>)"]

    classDef main fill:#e6ffe6,stroke:#006400,stroke-width:2px;
    class SM main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
