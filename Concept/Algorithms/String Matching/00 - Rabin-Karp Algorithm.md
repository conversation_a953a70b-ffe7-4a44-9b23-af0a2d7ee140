---
tags: [concept/algorithms, concept/string_matching, pattern/hashing, pattern/sliding_window, course/labuladong]
aliases: [<PERSON><PERSON><PERSON><PERSON><PERSON> Algorithm, Rolling Hash, 字符串匹配算法, RK算法]
summary: |
  The <PERSON>bin-Karp algorithm is a string-searching algorithm that uses hashing to find occurrences of a pattern string within a main text string. 
  It employs a "rolling hash" to efficiently calculate hash values for substrings, often in conjunction with a sliding window approach.
created: 2025-05-26T18:00:00.000-07:00
modified: 2025-05-26T18:00:00.000-07:00
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷数组算法/滑动窗口延伸：Rabin Karp 字符匹配算法.md|滑动窗口延伸：Rabin Karp 字符匹配算法]].
> Labuladong explains Rabin-Karp as an extension of sliding window ideas, using hashing for efficient string comparison.

# Rabin-Karp Algorithm for String Matching

The Rabin-Karp algorithm is a string-searching algorithm that uses hashing to find occurrences of a pattern string within a main text string. It's particularly known for its use of a "rolling hash" technique, which allows for efficient calculation of hash values for substrings as a window slides across the text.

## 🎯 Core Idea: Hashing Substrings

Instead of directly comparing substrings (which can be $O(M)$ for a pattern of length $M$), Rabin-Karp compares hash values.
1.  Calculate the hash of the `pattern` string.
2.  Slide a window of the same length as `pattern` across the `text` string.
3.  For each window in `text`, calculate its hash.
4.  If `hash(window) == hash(pattern)`, then it's a *potential* match. A direct character-by-character comparison is then performed to confirm (to handle hash collisions).

## 🔢 String to Number Conversion (Foundation)

A string can be treated as a number in a base-`R` system, where `R` is the size of the alphabet (e.g., 26 for lowercase English letters, 256 for ASCII).
For a string `s = s_1 s_2 ... s_M`:
`hash(s) = (s_1 \cdot R^{M-1} + s_2 \cdot R^{M-2} + ... + s_M \cdot R^0) \pmod Q`
- `s_i`: Numeric value of the character (e.g., `ord(char) - ord('a')`).
- `R`: Base, often a prime larger than alphabet size.
- `M`: Length of the string.
- `Q`: A large prime modulus to keep hash values manageable and reduce collisions.

**Example: "abc" with R=26, a=0, b=1, c=2**
`hash("abc") = (0 * 26^2 + 1 * 26^1 + 2 * 26^0) = 0 + 26 + 2 = 28` (modulo Q if needed)

This conversion is similar to converting a string representation of a number (e.g., "8264") to an integer, as shown by Labuladong:
```python
# s = "8264" (base 10)
# number = 0
# for char_digit in s:
#     number = 10 * number + (ord(char_digit) - ord('0'))
# Iterations:
# num = 0*10 + 8 = 8
# num = 8*10 + 2 = 82
# num = 82*10 + 6 = 826
# num = 826*10 + 4 = 8264
```

## 🔄 Rolling Hash Technique

The power of Rabin-Karp comes from efficiently updating the hash of the window as it slides.
If `hash(s[i..i+M-1])` is known, `hash(s[i+1..i+M])` can be calculated in $O(1)$ (amortized, considering modulo arithmetic).

Let `h_window = hash(s[i..i+M-1])`.
To get `hash(s[i+1..i+M])`:
1.  **Subtract** the contribution of the leftmost character `s[i]`: `h_window = (h_window - s_i \cdot R^{M-1}) \pmod Q`.
2.  **Shift** the hash value: `h_window = (h_window \cdot R) \pmod Q`.
3.  **Add** the contribution of the new rightmost character `s[i+M]`: `h_window = (h_window + s_{i+M}) \pmod Q`.

Care must be taken with modulo operations, especially subtraction (ensure result is non-negative before modulo). `R^{M-1} \pmod Q` (often called `RM` or `H`) needs to be precomputed.

**Formula for Rolling Hash:**
`hash_next = ((hash_current - val(char_out) * R^{M-1}) * R + val(char_in)) \pmod Q`
Make sure subtractions `(X - Y) \pmod Q` are handled as `(X - Y + Q) \pmod Q` to stay positive.

## 🛠️ Algorithm Steps (Conceptual)
Assume `txt` (length `N`) and `pat` (length `M`).

1.  Choose `R` (base) and `Q` (modulus). Precompute `RM = R^{M-1} \pmod Q`.
2.  Calculate `hash_pat = hash(pat[0..M-1])`.
3.  Calculate `hash_txt_window = hash(txt[0..M-1])`.
4.  Loop `i` from `0` to `N-M`:
    a.  If `hash_txt_window == hash_pat`:
        i.  Perform a character-by-character comparison of `pat` and `txt[i..i+M-1]`.
        ii. If they match, `i` is an occurrence. Add to results.
    b.  If `i < N-M` (i.e., window can slide further):
        i.  Update `hash_txt_window` using the rolling hash formula to represent `txt[i+1..i+M]`.

## Example: LC187 - Repeated DNA Sequences
[[Interview/Practice/LeetCode/LC187 - Repeated DNA Sequences|LC187 - Repeated DNA Sequences]] asks to find all 10-letter-long sequences (substrings) that occur more than once in a DNA molecule.
- Here, the "pattern" length is fixed (10).
- We can slide a window of length 10, calculate the hash of each window.
- Store hashes (or actual substrings if collision) in a set or map to detect repeats.
- Labuladong suggests mapping 'A'->0, 'C'->1, 'G'->2, 'T'->3, so `R=4`.

**Simplified approach for LC187 (without explicit rolling hash, but uses hashing on substrings):**
```python
# Conceptual for LC187
# def findRepeatedDnaSequences(s: str) -> list[str]:
#     L = 10
#     n = len(s)
#     if n <= L:
#         return []
#
#     seen_substrings = set()
#     repeated_substrings = set()
#
#     for i in range(n - L + 1):
#         substring = s[i : i+L]
#         if substring in seen_substrings:
#             repeated_substrings.add(substring)
#         else:
#             seen_substrings.add(substring)
#     return list(repeated_substrings)
```
This uses Python's string hashing implicitly. A full Rabin-Karp would implement custom rolling hash for these substrings.

## Example: LC28 - Find the Index of the First Occurrence in a String
[[Interview/Practice/LeetCode/LC28 - Find the Index of the First Occurrence in a String|LC28 - Find Index]] (implement `strStr()`). This is a direct application of string matching.
- `pattern` is `needle`. `text` is `haystack`.

**Python's `str.find()` or KMP are often faster for this specific problem due to highly optimized C implementations, but Rabin-Karp is a valid approach.**

```python
# Conceptual Rabin-Karp for LC28 (Python-like, actual implementation needs care with modulo)
# class Solution:
#     def strStr(self, haystack: str, needle: str) -> int:
#         N, M = len(haystack), len(needle)
#         if M == 0: return 0
#         if N < M: return -1

#         R = 256 # Alphabet size (ASCII)
#         Q = 101 # A small prime for example, usually larger (e.g., 10^9 + 7)
        
#         # Precompute R^(M-1) % Q
#         RM = 1
#         for _ in range(M - 1):
#             RM = (RM * R) % Q

#         hash_needle = 0
#         hash_haystack_window = 0
#         for i in range(M):
#             hash_needle = (R * hash_needle + ord(needle[i])) % Q
#             hash_haystack_window = (R * hash_haystack_window + ord(haystack[i])) % Q
        
#         if hash_needle == hash_haystack_window:
#             if haystack[0:M] == needle: # Verify
#                 return 0
        
#         for i in range(1, N - M + 1):
#             # Rolling hash for haystack window
#             # hash_haystack_window = (hash_haystack_window - ord(haystack[i-1]) * RM) % Q # Subtract outgoing
#             # hash_haystack_window = (hash_haystack_window + Q) % Q # Ensure positive
#             # hash_haystack_window = (hash_haystack_window * R) % Q # Shift
#             # hash_haystack_window = (hash_haystack_window + ord(haystack[i+M-1])) % Q # Add incoming

#             # Corrected rolling hash for base R polynomial
#             val_out = ord(haystack[i-1])
#             val_in = ord(haystack[i+M-1])
#             hash_haystack_window = ( (hash_haystack_window - val_out * RM) * R + val_in ) % Q
#             hash_haystack_window = (hash_haystack_window + Q) % Q # Ensure positive
            
#             if hash_needle == hash_haystack_window:
#                 if haystack[i : i+M] == needle: # Verify
#                     return i
        
#         return -1
```
*Note: The modulo arithmetic for rolling hash, especially subtraction, needs to be handled carefully to keep results positive and correct.*

## Complexity
- **Preprocessing (hash of pattern, first window, RM):** $O(M)$.
- **Main Loop (Rolling Hash & Comparison):**
    - Each roll: $O(1)$ (amortized for large numbers, or if arithmetic is on fixed-size integers).
    - Number of windows: $N-M+1$. Total $O(N-M)$.
    - If hash matches, character verification: $O(M)$.
- **Worst Case (e.g., many hash collisions, "aaaaa" in "aaaaaaaaaa"):** $O((N-M+1) \cdot M) = O(NM)$.
- **Average Case (good hash function, few collisions):** $O(N+M)$.

## 🚀 Advantages
- Conceptually extends sliding window.
- Can be very fast on average.
- Used in practice for multiple pattern matching (Aho-Corasick variant).

## 👎 Disadvantages
- Worst-case performance can be poor ($O(NM)$).
- Relies on good hash function and prime modulus `Q` to minimize collisions.
- Arithmetic overflow can be an issue if `Q` and `R` are not chosen carefully relative to integer sizes.

## 总结 (Summary)
- Rabin-Karp is a string matching algorithm using hashing and a rolling hash technique.
- It slides a window over the text, comparing hash of window with hash of pattern.
- Rolling hash allows $O(1)$ update of window hash.
- Average time complexity $O(N+M)$, worst-case $O(NM)$.
- Proper choice of base `R` and modulus `Q`, and careful handling of modulo arithmetic (especially for subtraction in rolling hash) are crucial for correctness and performance.

---
Parent: [[Interview/Concept/Algorithms/String Matching/index|String Matching Algorithms]] (Create this index if it doesn't exist)
Related Concepts: [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window]], [[Interview/Concept/Algorithms/Hashing/index|Hashing]]
Related Problems: [[Interview/Practice/LeetCode/LC187 - Repeated DNA Sequences|LC187]], [[Interview/Practice/LeetCode/LC28 - Find the Index of the First Occurrence in a String|LC28]]
