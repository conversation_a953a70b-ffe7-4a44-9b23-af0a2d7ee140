---
tags: [concept/algorithms, concept/dynamic_programming, type/problem_solving_pattern, pattern/stock_trading, course/labuladong]
aliases: [Stock Trading DP, 股票买卖问题模式]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/一个方法团灭 LeetCode 股票买卖问题.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/一个方法团灭 LeetCode 股票买卖问题.md|一个方法团灭 LeetCode 股票买卖问题 by Labuladong]].
> This pattern provides a unified framework for solving various stock trading problems with different constraints (number of transactions, cooldown, fees).

# Dynamic Programming: Stock Trading Pattern

The "Stock Trading" series of problems on LeetCode involves finding the maximum profit from buying and selling stocks under various constraints. Labuladong proposes a unified dynamic programming framework based on states and choices to tackle these problems systematically.

## 核心思想 (Core Idea): State Machine DP

At any given day `i`, the state can be described by:
1.  The current day `i`.
2.  The number of transactions `k` already completed (or allowed remaining).
3.  Whether we are currently holding a stock (1) or not (0).

The `dp` table would generally be `dp[i][k][holding_status]`.

**Choices on day `i`:**
- **Buy:** If not holding, can buy. Profit decreases by `prices[i]`.
- **Sell:** If holding, can sell. Profit increases by `prices[i]`.
- **Rest:** Do nothing. Profit doesn't change from this action.

![](/algo/images/stock/1.png) *(Source: Labuladong - State machine for holding status)*

## General State Transition Equations

Let `dp[i][k][0]` be max profit on day `i` with `k` transactions allowed/completed, and not holding stock.
Let `dp[i][k][1]` be max profit on day `i` with `k` transactions allowed/completed, and holding stock.

- **`dp[i][k][0] = max(dp[i-1][k][0], dp[i-1][k][1] + prices[i])`**
    - `dp[i-1][k][0]`: Rested, was not holding yesterday.
    - `dp[i-1][k][1] + prices[i]`: Sold today, was holding yesterday. (Selling completes a transaction for some definitions, but Labuladong defines buying as start of k-th transaction).

- **`dp[i][k][1] = max(dp[i-1][k][1], dp[i-1][k-1][0] - prices[i])`**
    - `dp[i-1][k][1]`: Rested, was holding yesterday.
    - `dp[i-1][k-1][0] - prices[i]`: Bought today, was not holding yesterday. This uses one transaction allowance `k-1` -> `k`.

**Base Cases:**
- `dp[-1][...][0] = 0` (Before day 0, no profit, not holding)
- `dp[-1][...][1] = -infinity` (Before day 0, cannot be holding)
- `dp[...][0][0] = 0` (0 transactions allowed, not holding, profit 0)
- `dp[...][0][1] = -infinity` (0 transactions allowed, cannot be holding)

## Problem Variations and Adaptations

### 1. Best Time to Buy and Sell Stock I (LC121)
[[Interview/Practice/LeetCode/LC121 - Best Time to Buy and Sell Stock|LC121]]
- Constraint: `k = 1` transaction.
- Simplified equations:
  `dp[i][0] = max(dp[i-1][0], dp[i-1][1] + prices[i])`
  `dp[i][1] = max(dp[i-1][1], -prices[i])` (since `dp[i-1][0][0]` (0 transactions, not holding) is 0)
- Space optimized: `dp_i_0`, `dp_i_1`.

### 2. Best Time to Buy and Sell Stock II (LC122)
[[Interview/Practice/LeetCode/LC122 - Best Time to Buy and Sell Stock II|LC122]]
- Constraint: `k = +infinity` (unlimited transactions).
- `k` effectively becomes irrelevant (`k` and `k-1` are same state).
- Simplified equations:
  `dp[i][0] = max(dp[i-1][0], dp[i-1][1] + prices[i])`
  `dp[i][1] = max(dp[i-1][1], dp[i-1][0] - prices[i])`
- Space optimized: `dp_i_0`, `dp_i_1`.

### 3. Best Time to Buy and Sell Stock with Cooldown (LC309)
[[Interview/Practice/LeetCode/LC309 - Best Time to Buy and Sell Stock with Cooldown|LC309]]
- Constraint: `k = +infinity`, but 1-day cooldown after selling.
- `dp[i][k][1] = max(dp[i-1][k][1], dp[i-2][k][0] - prices[i])`
  (If buying today, must have rested on `i-1`, so prev non-holding state was `i-2`).
- Space optimized: `dp_i_0`, `dp_i_1`, `dp_pre_0` (for `dp[i-2][0]`).

### 4. Best Time to Buy and Sell Stock with Transaction Fee (LC714)
[[Interview/Practice/LeetCode/LC714 - Best Time to Buy and Sell Stock with Transaction Fee|LC714]]
- Constraint: `k = +infinity`, but `fee` per transaction.
- `dp[i][k][1] = max(dp[i-1][k][1], dp[i-1][k][0] - prices[i] - fee)` (Fee paid on buy)
  OR `dp[i][k][0] = max(dp[i-1][k][0], dp[i-1][k][1] + prices[i] - fee)` (Fee paid on sell).
  Labuladong usually puts fee on buy: `dp[i][1] = max(dp[i-1][1], dp[i-1][0] - prices[i] - fee)`.
- Space optimized: `dp_i_0`, `dp_i_1`.

### 5. Best Time to Buy and Sell Stock III (LC123)
[[Interview/Practice/LeetCode/LC123 - Best Time to Buy and Sell Stock III|LC123]]
- Constraint: `k = 2` transactions.
- Use the full `dp[i][k][status]` framework, iterating `k` from `max_k` down to `1`.
- Can be space-optimized by noticing `dp[i][k]` only depends on `dp[i-1][k]` and `dp[i-1][k-1]`.
  This leads to variables like `dp_i10, dp_i11, dp_i20, dp_i21`.

### 6. Best Time to Buy and Sell Stock IV (LC188)
[[Interview/Practice/LeetCode/LC188 - Best Time to Buy and Sell Stock IV|LC188]]
- Constraint: `k` is a given parameter.
- Use full `dp[i][k][status]` framework.
- Optimization: If `k > n/2`, it's equivalent to `k = +infinity` (LC122).

## General Python Code Structure (for `k` transactions)
```python
# General structure for max_k transactions
# def maxProfit_k_transactions(prices: list[int], max_k: int):
#     n = len(prices)
#     if n == 0: return 0

#     if max_k > n // 2: # Equivalent to k = +infinity
#         # Use LC122 logic (k_inf version)
#         dp_i0 = 0
#         dp_i1 = float('-inf')
#         for price in prices:
#             temp = dp_i0
#             dp_i0 = max(dp_i0, dp_i1 + price)
#             dp_i1 = max(dp_i1, temp - price)
#         return dp_i0

#     # dp[day_idx][transactions_used][0_not_holding_1_holding]
#     dp = [[[0] * 2 for _ in range(max_k + 1)] for _ in range(n)]

#     for k_val in range(max_k + 1):
#         dp[0][k_val][0] = 0
#         dp[0][k_val][1] = -prices[0] # Cost of buying on day 0

#     # Base case for k=0 transactions:
#     for i in range(n):
#         dp[i][0][0] = 0
#         dp[i][0][1] = float('-inf') # Cannot hold stock if 0 transactions allowed

#     for i in range(1, n):
#         for k_val in range(1, max_k + 1):
#             # Not holding stock today
#             dp[i][k_val][0] = max(dp[i-1][k_val][0],          # Rested
#                                    dp[i-1][k_val][1] + prices[i]) # Sold
#             # Holding stock today
#             dp[i][k_val][1] = max(dp[i-1][k_val][1],          # Rested
#                                    dp[i-1][k_val-1][0] - prices[i]) # Bought (uses one k)

#     return dp[n-1][max_k][0]
```
The base cases for `i=0` (first day) within the `dp` table are critical.
Labuladong's iterative solution often sets up `dp[i-1]` states with `dp[-1]` style base cases before the loop.
A common way to initialize the base cases for `dp[0][k][state]`:
- `dp[0][k][0] = 0` for all `k` (on day 0, if not holding, profit is 0).
- `dp[0][k][1] = -prices[0]` for all `k >= 1` (on day 0, if holding, must have bought, profit is `-prices[0]`). `dp[0][0][1]` would be `-infinity`.

## 总结 (Summary)
- The stock trading problems can be solved using a unified DP framework based on `(day, transactions_k, holding_status)`.
- Different problem constraints (max `k` transactions, cooldown, fees) modify the state transitions or the meaning/range of `k`.
- Space optimization is often possible by noticing dependencies on only the previous day's states.
- This state machine approach provides a systematic way to derive solutions for all variations.

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Concepts]]
Related: LC121, LC122, LC123, LC188, LC309, LC714 (links in main content)
