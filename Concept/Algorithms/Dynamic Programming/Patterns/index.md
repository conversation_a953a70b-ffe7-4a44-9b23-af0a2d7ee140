---
tags: [index, concept/algorithms, concept/dynamic_programming, pattern/dp]
aliases: [DP Patterns Index, Dynamic Programming Patterns]
---

# Dynamic Programming Problem Patterns

This section lists common problem patterns that can be solved using Dynamic Programming.

- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/House Robber Pattern|House Robber Pattern]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Stock Trading Pattern|Stock Trading Pattern]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Game DP/Burst Balloons|Game DP - Burst Balloons]]
- (More patterns will be added here)

- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Game DP/Super Egg Drop|Game DP - Super Egg Drop]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Game DP/Regular Expression Matching|Regex Matching DP]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Game DP/Travel Min Cost K Stops|Shortest Path DP (K Stops)]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Grid DP/Minimum Path Sum|Grid DP - Minimum Path Sum]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Game DP/index|Game DP Patterns]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Grid DP/index|Grid DP Patterns]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/index|Sequence DP Patterns]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Kadane Algorithm (Maximum Subarray)|Kadane's Algorithm (Maximum Subarray)]]
---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Index]]
