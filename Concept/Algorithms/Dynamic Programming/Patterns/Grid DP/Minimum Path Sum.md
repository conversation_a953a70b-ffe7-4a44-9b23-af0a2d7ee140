---
tags: [concept/algorithms, concept/dynamic_programming, type/problem_solving_pattern, pattern/grid_dp, course/labuladong]
aliases: [Grid Minimum Path Sum, 最小路径和DP]
source_file_path: "Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/动态规划之最小路径和.md"
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/动态规划之最小路径和|动态规划之最小路径和 by Labuladong]].
> This problem, [[Interview/Practice/LeetCode/LC64 - Minimum Path Sum|LC64 - Minimum Path Sum]], involves finding the minimum sum path in a grid from top-left to bottom-right.

# Dynamic Programming: Minimum Path Sum in Grid (LC64)

The Minimum Path Sum problem on a grid asks to find a path from the top-left corner to the bottom-right corner such that the sum of numbers along the path is minimized. You can only move right or down.

## 核心思想 (Core Idea): DP Table

Let `dp[i][j]` be the minimum path sum to reach cell `(i, j)` from `(0, 0)`.

**State Transition Equation:**
To reach cell `(i, j)`, you can only come from `(i-1, j)` (from above) or `(i, j-1)` (from left).
So, `dp[i][j] = grid[i][j] + min(dp[i-1][j], dp[i][j-1])`.

**Base Cases:**
- `dp[0][0] = grid[0][0]`.
- First row: `dp[0][j] = grid[0][j] + dp[0][j-1]` (can only come from left).
- First column: `dp[i][0] = grid[i][0] + dp[i-1][0]` (can only come from above).

The final answer is `dp[m-1][n-1]`.

## Python Solution for LC64
```python
class Solution:
    def minPathSum(self, grid: list[list[int]]) -> int:
        m = len(grid)
        n = len(grid[0])

        # dp[i][j] = min path sum to reach (i,j)
        dp = [[0] * n for _ in range(m)]

        # Base case for dp[0][0]
        dp[0][0] = grid[0][0]

        # Fill first column
        for i in range(1, m):
            dp[i][0] = dp[i-1][0] + grid[i][0]

        # Fill first row
        for j in range(1, n):
            dp[0][j] = dp[0][j-1] + grid[0][j]

        # Fill the rest of the dp table
        for i in range(1, m):
            for j in range(1, n):
                dp[i][j] = grid[i][j] + min(dp[i-1][j], dp[i][j-1])

        return dp[m-1][n-1]
```

## Complexity
- **Time:** $O(M \cdot N)$ for iterating through the grid.
- **Space:** $O(M \cdot N)$ for the `dp` table. Can be optimized to $O(N)$ or $O(M)$ by only keeping the previous row/column.

## 总结 (Summary)
- Minimum Path Sum in a grid is a standard DP problem.
- `dp[i][j]` stores the min sum to reach cell `(i,j)`.
- Transitions consider moves from top `dp[i-1][j]` or left `dp[i][j-1]`.
- Base cases handle the first row and first column.

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Concepts]]
Related: [[Interview/Practice/LeetCode/LC64 - Minimum Path Sum|LC64 - Minimum Path Sum]]
