---
tags: [concept/algorithms, concept/dynamic_programming, type/problem_solving_pattern, pattern/house_robber, course/labuladong]
aliases: [House Robber DP, 打家劫舍问题模式]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/一个方法团灭 LeetCode 打家劫舍问题.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/一个方法团灭 LeetCode 打家劫舍问题.md|一个方法团灭 LeetCode 打家劫舍问题 by Labuladong]].
> This pattern covers variants of the "House Robber" problem, typically involving choices of taking or skipping items with adjacency constraints.

# Dynamic Programming: House Robber Pattern

The "House Robber" series of problems are classic examples of dynamic programming where the core constraint is not robbing adjacent houses. The goal is to maximize the total amount robbed.

## 核心思想 (Core Idea)
At each house `i`, the robber has two choices:
1.  **Rob house `i`:** If they rob house `i`, they cannot rob house `i-1`. The total amount would be `nums[i] + dp[i-2]` (amount from house `i` + max amount from non-adjacent previous houses).
2.  **Don't rob house `i`:** If they don't rob house `i`, the total amount is `dp[i-1]` (max amount up to the previous house).

The decision maximizes these two choices: `dp[i] = max(nums[i] + dp[i-2], dp[i-1])`.

## 1. House Robber I (LC198)
[[Interview/Practice/LeetCode/LC198 - House Robber|LC198 - House Robber]]: Basic linear arrangement of houses.

### DP State Definition
`dp[i]` = maximum amount of money that can be robbed from houses `0` to `i`.

### State Transition
`dp[i] = max(dp[i-1], nums[i] + dp[i-2])`
- `dp[i-1]`: Max amount if house `i` is *not* robbed.
- `nums[i] + dp[i-2]`: Max amount if house `i` *is* robbed (cannot rob `i-1`).

### Base Cases
- `dp[0] = nums[0]`
- `dp[1] = max(nums[0], nums[1])`

### Python Solution (LC198)
```python
class Solution:
    def rob(self, nums: list[int]) -> int:
        n = len(nums)
        if n == 0:
            return 0
        if n == 1:
            return nums[0]

        # dp[i] = max money robbed up to house i
        dp = [0] * n
        dp[0] = nums[0]
        dp[1] = max(nums[0], nums[1])

        for i in range(2, n):
            # Option 1: Rob house i (cannot rob i-1)
            rob_i = nums[i] + dp[i-2]
            # Option 2: Don't rob house i
            not_rob_i = dp[i-1]
            dp[i] = max(rob_i, not_rob_i)

        return dp[n-1]
```
This can be space-optimized to $O(1)$ since `dp[i]` only depends on `dp[i-1]` and `dp[i-2]`.

## 2. House Robber II (LC213)
[[Interview/Practice/LeetCode/LC213 - House Robber II|LC213 - House Robber II]]: Houses are arranged in a circle (first and last are adjacent).

### Core Idea
Since the first and last houses are adjacent, we cannot rob both. This means the problem breaks down into two subproblems:
1. Rob houses from `0` to `n-2` (excluding the last house).
2. Rob houses from `1` to `n-1` (excluding the first house).
The answer is the maximum of these two subproblems. Each subproblem is a standard House Robber I case.

### Python Solution (LC213)
```python
class Solution:
    def _rob_linear(self, nums_segment: list[int]) -> int:
        n = len(nums_segment)
        if n == 0:
            return 0
        if n == 1:
            return nums_segment[0]

        dp = [0] * n
        dp[0] = nums_segment[0]
        dp[1] = max(nums_segment[0], nums_segment[1])

        for i in range(2, n):
            dp[i] = max(nums_segment[i] + dp[i-2], dp[i-1])
        return dp[n-1]

    def rob(self, nums: list[int]) -> int:
        n = len(nums)
        if n == 0:
            return 0
        if n == 1:
            return nums[0]

        # Case 1: Don't rob the last house (rob from 0 to n-2)
        max1 = self._rob_linear(nums[:-1])
        # Case 2: Don't rob the first house (rob from 1 to n-1)
        max2 = self._rob_linear(nums[1:])

        return max(max1, max2)
```

## 3. House Robber III (LC337)
[[Interview/Practice/LeetCode/LC337 - House Robber III|LC337 - House Robber III]]: Houses are arranged in a binary tree. Cannot rob directly connected nodes (parent-child).

### Core Idea (Recursive DP / Post-Order Traversal)
For each node `node`, we need to decide whether to rob it or not.
Let `rob_node(node)` be a function that returns a pair `[rob_this_node_max, not_rob_this_node_max]`:
- `rob_this_node_max`: Max money if `node` is robbed.
- `not_rob_this_node_max`: Max money if `node` is not robbed.

**Recursive Step for `rob_node(node)`:**
1. Get results from left child: `[rob_left, not_rob_left] = rob_node(node.left)`.
2. Get results from right child: `[rob_right, not_rob_right] = rob_node(node.right)`.
3. Calculate for `node`:
   - If `node` is robbed: `rob_this_node_max = node.val + not_rob_left + not_rob_right` (cannot rob children).
   - If `node` is not robbed: `not_rob_this_node_max = max(rob_left, not_rob_left) + max(rob_right, not_rob_right)` (can choose to rob or not rob children independently).
The final answer for the tree rooted at `node` is `max(rob_this_node_max, not_rob_this_node_max)`.

### Python Solution (LC337)
```python
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None):
#         self.val = val; self.left = left; self.right = right

class Solution:
    def rob(self, root: TreeNode) -> int:

        # Returns a pair: [money_if_rob_root, money_if_not_rob_root]
        def dp_rob(node: TreeNode) -> list[int]:
            if not node:
                return [0, 0] # [rob_this, not_rob_this]

            left_res = dp_rob(node.left)
            right_res = dp_rob(node.right)

            # If we rob the current node `node`:
            # We cannot rob its children.
            rob_current_node_val = node.val + left_res[1] + right_res[1] 
                                      # (node.val + not_rob_left + not_rob_right)

            # If we do NOT rob the current node `node`:
            # We can choose to rob or not rob its children. Take max for each child.
            not_rob_current_node_val = max(left_res[0], left_res[1]) + \
                                       max(right_res[0], right_res[1])

            return [rob_current_node_val, not_rob_current_node_val]

        result_pair = dp_rob(root)
        return max(result_pair[0], result_pair[1])
```
This uses post-order traversal logic from [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]].

## 总结 (Summary)
- The House Robber pattern deals with maximizing a sum with an adjacency constraint.
- **Linear (LC198):** `dp[i] = max(nums[i] + dp[i-2], dp[i-1])`.
- **Circular (LC213):** Reduce to two linear problems.
- **Tree (LC337):** Use recursive DP. Each node's decision considers results from children (rob/not_rob states), calculated in post-order.

This pattern demonstrates how a core DP idea can be adapted to different structural constraints.
---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Concepts]]
Related: [[Interview/Practice/LeetCode/LC198 - House Robber|LC198]], [[Interview/Practice/LeetCode/LC213 - House Robber II|LC213]], [[Interview/Practice/LeetCode/LC337 - House Robber III|LC337]]
