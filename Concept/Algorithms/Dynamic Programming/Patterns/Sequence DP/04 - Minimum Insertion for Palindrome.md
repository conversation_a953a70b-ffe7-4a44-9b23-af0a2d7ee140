---
tags: [concept/algorithms, concept/dynamic_programming, type/problem_solving_pattern, pattern/sequence_dp, pattern/lps, course/labuladong]
aliases: [Minimum Insertions to Make Palindrome, 让字符串成为回文串的最少插入次数]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/子序列类型问题/动态规划之子序列问题解题模板.md
---

> [!NOTE] Source Annotation
> Content conceptualized from discussion within [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/子序列类型问题/动态规划之子序列问题解题模板.md|DP Subsequence Problems Template by Labuladong]].
> Primary LeetCode problem: [[Interview/Practice/LeetCode/LC1312 - Minimum Insertion Steps to Make a String Palindrome|LC1312]].

# Dynamic Programming: Minimum Insertion Steps to Make a String Palindrome

The problem asks for the minimum number of characters to insert into a given string `s` to make it a palindrome.

## 🎯 Problem Definition (LC1312)
Given a string `s`, find the minimum number of insertions needed to make `s` a palindrome.

**Example:** `s = "mbadm"`
Output: 2. Insert 'd' at the start and 'b' at the end: `"dmbadmb"` (or other variations). Or "mbdadbm".
Actually, "mbadm" -> "mbdadbm" by inserting 'd' and 'b'. Or "madam" + 'b' -> "mbadamb".
The example in LC1312 is "mbadm" -> "mbdadbm", insertions 'd' before 'm' (pos 2) and 'b' after 'm' (pos 4). Result "mbdadbm" needs 2 insertions.

## 💡 Core Idea: Relate to Longest Palindromic Subsequence (LPS)

The key insight is that the characters already forming a palindromic subsequence within `s` do not need to be "mirrored" by insertions. The characters that are *not* part of the Longest Palindromic Subsequence (LPS) are the ones that need a corresponding character inserted to make the whole string a palindrome.

- Let `n = len(s)`.
- Let `len_lps = length_of_LPS(s)`.
- The characters in the LPS already have their "palindrome partners" within the subsequence.
- The `n - len_lps` characters are "unmatched" within the context of the LPS. Each of these `n - len_lps` characters needs one insertion to create its mirror image.
- Therefore, **Minimum Insertions = `n - len_lps`**.

**Example:** `s = "mbadm"` (`n=5`)
- LPS of "mbadm" is "mam" (or "mdm", "mbm"). Length = 3.
- `len_lps = 3`.
- Minimum Insertions = `5 - 3 = 2`.
  - If LPS is "mam": `s = "mbadm"`. 'b' and 'd' are not part of this LPS.
    - To make "mbadm" from "mam": `m (b_ins) a (d_ins) m`.
    - String "mam". Insert 'd' to match 'd'. Insert 'b' to match 'b'. E.g., `mb(d)ad(b)m`.

## 🛠️ Algorithm Steps
1.  Calculate the length of the Longest Palindromic Subsequence (LPS) of `s`. (See [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/02 - Longest Palindromic Subsequence (LPS)|LPS Algorithm]]). This is typically $O(N^2)$.
2.  The result is `len(s) - length_of_LPS(s)`.

## 🐍 Python Solution (LC1312)
```python
class Solution:
    def _longestPalindromeSubseq_dp(self, s: str) -> int:
        n = len(s)
        if n == 0: return 0
        dp = [[0] * n for _ in range(n)]
        for i in range(n):
            dp[i][i] = 1

        for i in range(n - 2, -1, -1):
            for j in range(i + 1, n):
                if s[i] == s[j]:
                    dp[i][j] = 2 + dp[i+1][j-1] if i+1 <= j-1 else 2
                else:
                    dp[i][j] = max(dp[i+1][j], dp[i][j-1])
        return dp[0][n-1]

    def minInsertions(self, s: str) -> int:
        n = len(s)
        len_lps = self._longestPalindromeSubseq_dp(s)
        return n - len_lps
```

## Complexity
- **Time Complexity:** $O(N^2)$ due to LPS calculation.
- **Space Complexity:** $O(N^2)$ for LPS DP table (can be optimized to $O(N)$ for LPS calculation).

## 总结 (Summary)
- The minimum insertions to make a string a palindrome is `len(s) - length_of_LPS(s)`.
- This reduces the problem to finding the LPS, which is a standard DP problem.
- This approach elegantly connects two subsequence problems.

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/index|Sequence DP Patterns]]
Related Problems: [[Interview/Practice/LeetCode/LC1312 - Minimum Insertion Steps to Make a String Palindrome|LC1312]], [[Interview/Practice/LeetCode/LC516 - Longest Palindromic Subsequence|LC516]]
