---
tags: [index, concept/algorithms, concept/dynamic_programming, pattern/sequence_dp]
aliases: [Sequence DP Index, DP on Sequences]
---

# Sequence Dynamic Programming Patterns

This section covers dynamic programming patterns specifically applied to sequence-based problems (e.g., strings, arrays).

## Core Patterns:
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/00 - DP Subsequence Problems Template|DP Subsequence Problems Template]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/01 - Longest Common Subsequence (LCS)|Longest Common Subsequence (LCS)]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/02 - Longest Palindromic Subsequence (LPS)|Longest Palindromic Subsequence (LPS)]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/03 - Edit Distance|Edit Distance]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/04 - Minimum Insertion for Palindrome|Minimum Insertion for Palindrome]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Ka<PERSON>e Algorithm (Maximum Subarray)|<PERSON><PERSON><PERSON>'s Algorithm (Maximum Subarray)]]
- (Add more specific sequence DP patterns here)

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Index]]
