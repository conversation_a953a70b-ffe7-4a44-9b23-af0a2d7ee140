---
tags: [concept/algorithms, concept/dynamic_programming, type/problem_solving_pattern, pattern/sequence_dp, pattern/lps, course/labuladong]
aliases: [Longest Palindromic Subsequence, LPS Algorithm, 最长回文子序列]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/子序列类型问题/动态规划之子序列问题解题模板.md 
---

> [!NOTE] Source Annotation
> Content primarily adapted from discussion within [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/子序列类型问题/动态规划之子序列问题解题模板.md|DP Subsequence Problems Template by Labuladong]], which uses LPS as an example.
> Primary LeetCode problem: [[Interview/Practice/LeetCode/LC516 - Longest Palindromic Subsequence|LC516]].

# Dynamic Programming: Longest Palindromic Subsequence (LPS)

The Longest Palindromic Subsequence (LPS) problem asks for the length of the longest subsequence of a given string that is also a palindrome. A subsequence is formed by deleting zero or more characters from the original string, maintaining relative order.

## 🎯 Problem Definition (LC516)
Given a string `s`, find the longest palindromic subsequence's length.

**Example:** `s = "bbbab"`
LPS is `"bbbb"` (or `"babab"` if we consider non-contiguous but original problem asks for subsequence), length 4.
If `s = "cbbd"`, LPS is `"bb"`, length 2.

Wait, "babab" from "bbbab". b_0, b_1, a_2, b_3, b_4. Pick b_0, b_1, a_2, b_3 -> "bbab" no.  b_0, a_2, b_3 -> "bab". b_0,b_1,b_3,b_4 -> "bbbb"
The subsequence definition is key. "babab" is not a subsequence of "bbbab". "bbbb" is, also "bab".
Correct LPS for "bbbab" is "bbbb", length 4.

## Approach 1: Reduction to LCS (Longest Common Subsequence)

A clever way to solve LPS is to reduce it to the [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/01 - Longest Common Subsequence (LCS)|LCS problem]].
- Let `s_rev` be the reverse of string `s`.
- The LPS of `s` is equivalent to the LCS of `s` and `s_rev`.

**Why?**
A palindrome reads the same forwards and backwards. If a subsequence is palindromic, it must appear in `s` in its original order, and in `s_rev` in its original order (which corresponds to reverse order in `s`). The longest such common subsequence is the LPS.

**Example:** `s = "bbbab"`
`s_rev = "babbb"`
LCS(`"bbbab"`, `"babbb"`) is `"bbbb"`, length 4.

**Implementation:** Use the standard LCS algorithm.
- Time: $O(N^2)$
- Space: $O(N^2)$ (or $O(N)$ if optimized for LCS)

```python
# Using the LCS function from the LCS concept note
# class SolutionLCS:
#     def longestCommonSubsequence(self, text1: str, text2: str) -> int:
#         # ... (implementation from LCS note) ...

class SolutionLPS_via_LCS:
    def longestPalindromeSubseq(self, s: str) -> int:
        s_rev = s[::-1]
        # return self.longestCommonSubsequence(s, s_rev) # Assuming LCS is available

        # Inline LCS for completeness:
        m, n = len(s), len(s_rev)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if s[i-1] == s_rev[j-1]:
                    dp[i][j] = 1 + dp[i-1][j-1]
                else:
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])
        return dp[m][n]
```

## Approach 2: Direct Dynamic Programming for LPS

This approach directly defines a DP state for the LPS problem.
- **DP State:** `dp[i][j]` = length of the LPS of the substring `s[i...j]` (inclusive).
- **Goal:** Find `dp[0][n-1]`, where `n = len(s)`.

**State Transition Equation:**
Consider `dp[i][j]`:
1.  **If `s[i] == s[j]`:**
    The characters at the ends of the current substring match. They must be part of the LPS for `s[i...j]`.
    - If `i == j` (single character substring): `dp[i][i] = 1`.
    - If `i < j`: `dp[i][j] = 2 + dp[i+1][j-1]` (2 for `s[i]` and `s[j]`, plus LPS of inner string).
      (Base case for `dp[i+1][j-1]` when `i+1 > j-1` (empty/invalid inner string) would be 0).

2.  **If `s[i] != s[j]`:**
    The characters at the ends do not match. So, `s[i]` and `s[j]` cannot *both* be part of the *same* palindromic subsequence's outermost pair. The LPS of `s[i...j]` must be either:
    - The LPS of `s[i+1...j]` (excluding `s[i]`) which is `dp[i+1][j]`.
    - The LPS of `s[i...j-1]` (excluding `s[j]`) which is `dp[i][j-1]`.
    So, `dp[i][j] = max(dp[i+1][j], dp[i][j-1])`.

**Base Cases:**
- `dp[i][i] = 1` for all `i` (a single character is a palindrome of length 1).
- If `i > j`, `dp[i][j] = 0` (empty substring has LPS length 0). This is implicitly handled by loop bounds or conditions like `i+1 > j-1` in the `s[i]==s[j]` case.

**Iteration Order:**
Since `dp[i][j]` depends on `dp[i+1][j-1]`, `dp[i+1][j]`, and `dp[i][j-1]`, we need to fill the `dp` table such that these subproblems are solved first. This means iterating by increasing substring length, or iterating `i` from `n-1` down to `0` and `j` from `i` up to `n-1`.

```python
class SolutionLPS_Direct:
    def longestPalindromeSubseq(self, s: str) -> int:
        n = len(s)
        if n == 0: return 0

        # dp[i][j] = LPS length for s[i..j]
        dp = [[0] * n for _ in range(n)]

        # Base case: all single characters are palindromes of length 1
        for i in range(n):
            dp[i][i] = 1

        # Fill table for lengths from 2 to n
        # Iterate i downwards from n-2 to 0
        # Iterate j upwards from i+1 to n-1
        for i in range(n - 2, -1, -1): # Start index i
            for j in range(i + 1, n):   # End index j
                if s[i] == s[j]:
                    # If i+1 == j (e.g., "bb"), dp[i+1][j-1] for "empty" middle is 0. Length is 2.
                    # dp[i+1][j-1] is for s[i+1 ... j-1]
                    # If i+1 > j-1 (length of inner part < 0), dp[i+1][j-1] should be 0.
                    # This occurs when j = i+1. Then i+1 > (i+1)-1 = i.
                    # The base case dp[i][i]=1 handles single char.
                    # if j == i + 1: dp[i][j] = 2
                    # else: dp[i][j] = 2 + dp[i+1][j-1]
                    # This can be simplified:
                    dp[i][j] = 2 + dp[i+1][j-1] if i+1 <= j-1 else 2 # if i+1 > j-1, inner length 0, means length 2 string like "aa"
                                                                    # if i+1 > j-1, implies j = i+1 (inner is empty)
                                                                    # then dp[i+1][j-1] = dp[i+1][i] which is 0.
                                                                    # so 2 + 0 works.
                else:
                    dp[i][j] = max(dp[i+1][j], dp[i][j-1])

        return dp[0][n-1]
```
Labuladong's visualization of direct DP for LPS: `![](/algo/images/LPS/2.png)` (DP table) and `![](/algo/images/LPS/3.jpg)` (state transitions).

## Complexity (Direct DP)
- **Time Complexity:** $O(N^2)$ for filling the $N \times N$ DP table.
- **Space Complexity:** $O(N^2)$ for the DP table. (Can be optimized to $O(N)$ space because `dp[i]` row only depends on `dp[i+1]` row and previous values in `dp[i]` row).

## 总结 (Summary)
- Longest Palindromic Subsequence can be solved using DP.
- **Method 1 (Reduction to LCS):** Find LCS of `s` and `reverse(s)`. $O(N^2)$ time and space.
- **Method 2 (Direct DP):** Define `dp[i][j]` as LPS length for `s[i...j]`. Iterate based on substring length or in a specific row/column order. $O(N^2)$ time and space.
- Both are standard DP approaches for this type of problem.

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/index|Sequence DP Patterns]]
Related Problems: [[Interview/Practice/LeetCode/LC516 - Longest Palindromic Subsequence|LC516]], [[Interview/Practice/LeetCode/LC1312 - Minimum Insertion Steps to Make a String Palindrome|LC1312]] (uses LPS length)
