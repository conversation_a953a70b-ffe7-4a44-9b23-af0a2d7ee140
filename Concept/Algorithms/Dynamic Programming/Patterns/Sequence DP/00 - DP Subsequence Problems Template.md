---
tags: [concept/algorithms, concept/dynamic_programming, type/problem_solving_pattern, pattern/sequence_dp, course/labuladong]
aliases: [DP Subsequence Template, 子序列动态规划模板]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/子序列类型问题/动态规划之子序列问题解题模板.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/子序列类型问题/动态规划之子序列问题解题模板.md|动态规划之子序列问题解题模板 by Labuladong]].
> This note outlines common templates for solving subsequence-related dynamic programming problems.

# Dynamic Programming: Subsequence Problem Templates

Subsequence problems are a common category in dynamic programming. Unlike substring or subarray problems where elements must be contiguous, subsequences allow for deletion of elements while maintaining relative order. This non-contiguity often leads to $O(N^2)$ solutions using DP. Labuladong highlights two primary DP definition templates for these types of problems.

## 🎯 Core Challenge of Subsequences
The main difficulty with subsequences is their potentially exponential number, making brute-force infeasible. DP helps by breaking the problem into manageable, overlapping subproblems.

## Template 1: `dp[i]` defines a property ending at `nums[i]`

- **Definition:** `dp[i]` typically represents the optimal value (e.g., length of LIS, max sum) of a subsequence that **ends with the element `nums[i]`**.
- **State Transition:** To calculate `dp[i]`, we usually iterate through all `j < i` and consider `nums[j]` as a potential predecessor.
  `dp[i] = optimal_combination(dp[j] + effect_of_nums[i])` for valid `j`.
- **Base Case:** Often `dp[i] = 1` (or `nums[i]`) if each element can form a subsequence of its own.
- **Final Result:** The overall answer is often the maximum (or minimum) value in the `dp` array, as the optimal subsequence could end at any `nums[i]`.

**Example Application:** [[Interview/Concept/Algorithms/Dynamic Programming/DP Design Longest Increasing Subsequence|Longest Increasing Subsequence (LIS)]]
  - `dp[i]` = length of LIS ending at `nums[i]`.
  - `dp[i] = 1 + max(dp[j])` for all `j < i` where `nums[j] < nums[i]`.

```python
# Conceptual LIS using Template 1
# def lengthOfLIS_template1(nums):
#     if not nums: return 0
#     dp = [1] * len(nums) # dp[i] = LIS length ending at nums[i]
#     for i in range(len(nums)):
#         for j in range(i):
#             if nums[i] > nums[j]:
#                 dp[i] = max(dp[i], dp[j] + 1)
#     return max(dp) if dp else 0
```

## Template 2: `dp[i][j]` defines a property involving `s1[0...i-1]` and `s2[0...j-1]`

This template is common for problems involving two strings or sequences.
- **Definition:** `dp[i][j]` represents the optimal value concerning the prefix `s1[0...i-1]` (of length `i`) and `s2[0...j-1]` (of length `j`).
- **State Transition:** Often depends on comparing `s1[i-1]` and `s2[j-1]`.
    - If `s1[i-1] == s2[j-1]`: `dp[i][j]` might depend on `dp[i-1][j-1]`.
    - If `s1[i-1] != s2[j-1]`: `dp[i][j]` might depend on `dp[i-1][j]`, `dp[i][j-1]`, or `dp[i-1][j-1]` (representing choices like delete from s1, delete from s2, or replace).
- **Base Cases:** Typically involve empty string prefixes, e.g., `dp[0][j]` (s1 is empty) or `dp[i][0]` (s2 is empty).
- **Final Result:** Usually `dp[len(s1)][len(s2)]`.

**Example Applications:**
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/01 - Longest Common Subsequence (LCS)|Longest Common Subsequence (LCS)]]
  - `dp[i][j]` = length of LCS of `s1[0..i-1]` and `s2[0..j-1]`.
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/03 - Edit Distance|Edit Distance]]
  - `dp[i][j]` = min operations to convert `s1[0..i-1]` to `s2[0..j-1]`.

```python
# Conceptual LCS using Template 2
# def longestCommonSubsequence_template2(s1, s2):
#     m, n = len(s1), len(s2)
#     dp = [[0] * (n + 1) for _ in range(m + 1)]
#     for i in range(1, m + 1):
#         for j in range(1, n + 1):
#             if s1[i-1] == s2[j-1]:
#                 dp[i][j] = 1 + dp[i-1][j-1]
#             else:
#                 dp[i][j] = max(dp[i-1][j], dp[i][j-1])
#     return dp[m][n]
```

## Problems Using These Templates (Examples from Source)

Labuladong's article "动态规划之子序列问题解题模板" primarily focuses on variations of these templates and hints at a unified approach for certain types of palindrome-related subsequence problems by cleverly transforming them.

- **[[Interview/Practice/LeetCode/LC516 - Longest Palindromic Subsequence|LC516 - Longest Palindromic Subsequence]]**:
  - This can be solved by finding the LCS of the string `s` and its reverse `s_rev`. The LCS of `s` and `s_rev` is the longest palindromic subsequence of `s`. This uses Template 2.
  - A direct DP solution (Template 1-like for 2D) is also possible: `dp[i][j]` = length of LPS in `s[i...j]`.

- **[[Interview/Practice/LeetCode/LC1312 - Minimum Insertion Steps to Make a String Palindrome|LC1312 - Minimum Insertion Steps to Make a String Palindrome]]**:
  - Find the length of the Longest Palindromic Subsequence (LPS) of the given string `s`. Let this be `len_lps`.
  - The minimum number of insertions needed is `len(s) - len_lps`. Each character not part of the LPS needs a corresponding character inserted to make it a palindrome.

## 总结 (Summary)
- Subsequence DP problems often fall into two main categories of `dp` array definitions:
    1.  1D `dp[i]` representing an optimal subsequence ending at `nums[i]`.
    2.  2D `dp[i][j]` representing an optimal relation between prefixes `s1[0..i-1]` and `s2[0..j-1]`.
- Recognizing which template fits or how to adapt them is key to solving these problems.
- Many complex subsequence problems can be reduced to simpler, known ones (like LPS to LCS).
- The time complexity is usually $O(N^2)$ for 1D input arrays/strings or $O(MN)$ for 2D inputs.

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/index|Sequence DP Patterns]]
Related Concepts: [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|DP Core Framework]]
