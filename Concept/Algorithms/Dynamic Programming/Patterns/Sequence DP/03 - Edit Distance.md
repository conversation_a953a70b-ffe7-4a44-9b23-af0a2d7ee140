---
tags: [concept/algorithms, concept/dynamic_programming, type/problem_solving_pattern, pattern/sequence_dp, pattern/edit_distance, course/labuladong]
aliases: [Edit Distance Algorithm, Levenshtein Distance, 编辑距离]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/子序列类型问题/经典动态规划：编辑距离.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/子序列类型问题/经典动态规划：编辑距离.md|经典动态规划：编辑距离 by Labuladong]].
> Primary LeetCode problem: [[Interview/Practice/LeetCode/LC72 - Edit Distance|LC72]].

# Dynamic Programming: Edit Distance

The Edit Distance problem (also known as Levenshtein distance) calculates the minimum number of single-character edits (insertions, deletions, or substitutions) required to change one word/string into another. It's a classic dynamic programming problem.

## 🎯 Problem Definition (LC72)
Given two strings `word1` and `word2`, return the minimum number of operations required to convert `word1` to `word2`.
Allowed operations:
1. Insert a character
2. Delete a character
3. Replace a character

**Example:** `word1 = "horse"`, `word2 = "ros"`
Output: 3
- horse -> rorse (replace 'h' with 'r')
- rorse -> rose (delete 'r')
- rose -> ros (delete 'e')

## 💡 DP State Definition
Let `dp[i][j]` be the minimum number of operations (edit distance) to convert the prefix `word1[0...i-1]` (first `i` characters of `word1`) into the prefix `word2[0...j-1]` (first `j` characters of `word2`).
The table size will be `(len(word1)+1) x (len(word2)+1)`.

## 🔡 State Transition Equation

Consider `dp[i][j]`, which involves `word1[i-1]` and `word2[j-1]`:

1.  **If `word1[i-1] == word2[j-1]`:**
    The current characters match. No operation is needed for these characters.
    `dp[i][j] = dp[i-1][j-1]` (edit distance of preceding substrings).
    Labuladong's GIF for this "skip" case: `![](/algo/images/editDistance/2.jpg)`

2.  **If `word1[i-1] != word2[j-1]`:**
    The current characters do not match. We must perform one operation. We choose the operation that results in the minimum total edits:
    -   **Insert:** Insert `word2[j-1]` into `word1`. Now `word1[0...i-1]` needs to become `word2[0...j-2]`.
        Cost: `1 + dp[i][j-1]`.
        Labuladong's GIF: `![](/algo/images/editDistance/insert.gif)` (conceptually, after insertion, `s2[j]` is matched, so `j` moves back relative to `s1[i]`).
    -   **Delete:** Delete `word1[i-1]` from `word1`. Now `word1[0...i-2]` needs to become `word2[0...j-1]`.
        Cost: `1 + dp[i-1][j]`.
        Labuladong's GIF: `![](/algo/images/editDistance/delete.gif)` (conceptually, `s1[i]` is removed, so `i` moves back relative to `s2[j]`).
    -   **Replace:** Replace `word1[i-1]` with `word2[j-1]`. Now `word1[0...i-2]` needs to become `word2[0...j-2]`.
        Cost: `1 + dp[i-1][j-1]`.
        Labuladong's GIF: `![](/algo/images/editDistance/replace.gif)` (conceptually, both `s1[i]` and `s2[j]` are matched/handled, so both `i` and `j` move back).
    So, `dp[i][j] = 1 + min(dp[i][j-1], dp[i-1][j], dp[i-1][j-1])`.

## 🕋 Base Cases
-   `dp[0][0] = 0`: Edit distance between two empty strings is 0.
-   `dp[i][0] = i` for `i > 0`: To convert `word1[0...i-1]` to an empty string, `i` deletions are needed.
-   `dp[0][j] = j` for `j > 0`: To convert an empty string to `word2[0...j-1]`, `j` insertions are needed.
Labuladong's GIF for base cases: `![](/algo/images/editDistance/3.jpg)`

## 🐍 Python Solution (LC72)
```python
class Solution:
    def minDistance(self, word1: str, word2: str) -> int:
        m, n = len(word1), len(word2)

        # dp[i][j] = min operations for word1[0..i-1] to word2[0..j-1]
        dp = [[0] * (n + 1) for _ in range(m + 1)]

        # Base cases
        for i in range(m + 1):
            dp[i][0] = i # Cost of deleting i chars from word1 to get empty string
        for j in range(n + 1):
            dp[0][j] = j # Cost of inserting j chars to empty word1 to get word2 prefix

        # Fill DP table
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if word1[i-1] == word2[j-1]:
                    dp[i][j] = dp[i-1][j-1] # No operation needed for these chars
                else:
                    dp[i][j] = 1 + min(
                        dp[i][j-1],    # Insert word2[j-1] into word1
                        dp[i-1][j],    # Delete word1[i-1] from word1
                        dp[i-1][j-1]   # Replace word1[i-1] with word2[j-1]
                    )

        return dp[m][n]
```
Labuladong's visualization for DP table: `![](/algo/images/editDistance/dp.jpg)`.
Visualizer panel in article: `div_edit-distance`.

## Complexity
- **Time Complexity:** $O(M \cdot N)$, where $M$ and $N$ are the lengths of `word1` and `word2`.
- **Space Complexity:** $O(M \cdot N)$ for the DP table. Can be optimized to $O(\min(M,N))$.

## Path Reconstruction
Labuladong's article also discusses how to reconstruct the actual sequence of operations by storing choices in the DP table (see `Node` class in his Java/Python example). This involves backtracking from `dp[m][n]` using the stored choices. Visualization: `![](/algo/images/editDistance/5.jpg)`, `![](/algo/images/editDistance/6.jpg)`.

## 总结 (Summary)
- Edit Distance (Levenshtein Distance) is a classic DP problem measuring string similarity.
- `dp[i][j]` stores the min operations for prefixes `word1[0..i-1]` and `word2[0..j-1]`.
- State transitions consider matching characters (cost 0, use `dp[i-1][j-1]`) or mismatching characters (cost 1 + min of insert, delete, replace operations on subproblems).
- Base cases handle empty string scenarios.

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/index|Sequence DP Patterns]]
Related Problems: [[Interview/Practice/LeetCode/LC72 - Edit Distance|LC72]], [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/01 - Longest Common Subsequence (LCS)|LCS]] (related 2D DP structure)
