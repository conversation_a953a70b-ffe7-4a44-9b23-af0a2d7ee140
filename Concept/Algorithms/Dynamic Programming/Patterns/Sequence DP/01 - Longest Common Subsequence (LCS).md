---
tags: [concept/algorithms, concept/dynamic_programming, type/problem_solving_pattern, pattern/sequence_dp, pattern/lcs, course/labuladong]
aliases: [Longest Common Subsequence, LCS Algorithm, 最长公共子序列]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/子序列类型问题/经典动态规划：最长公共子序列.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/子序列类型问题/经典动态规划：最长公共子序列.md|经典动态规划：最长公共子序列 by Labuladong]].
> Primary LeetCode problem: [[Interview/Practice/LeetCode/LC1143 - Longest Common Subsequence|LC1143]].

# Dynamic Programming: Longest Common Subsequence (LCS)

The Longest Common Subsequence (LCS) problem is a classic dynamic programming challenge. Given two sequences (typically strings), the goal is to find the longest subsequence common to both. A subsequence is derived from another sequence by deleting zero or more elements without changing the order of the remaining elements.

## 🎯 Problem Definition (LC1143)
Given two strings `text1` and `text2`, return the length of their longest common subsequence. If there is no common subsequence, return 0.

**Example:** `text1 = "abcde"`, `text2 = "ace"`
LCS is `"ace"`, length 3.

## 💡 DP State Definition
Let `dp[i][j]` be the length of the LCS of `text1[0...i-1]` (first `i` characters of `text1`) and `text2[0...j-1]` (first `j` characters of `text2`).
The table size will be `(len(text1)+1) x (len(text2)+1)`.

## 🔡 State Transition Equation

Consider `dp[i][j]`, which depends on `text1[i-1]` and `text2[j-1]`:

1.  **If `text1[i-1] == text2[j-1]`:**
    The current characters match. This character must be part of the LCS.
    So, `dp[i][j] = 1 + dp[i-1][j-1]` (1 for the current matching character + LCS of preceding substrings).
    Labuladong's visualization for this: `![](/algo/images/LCS/1.jpg)`

2.  **If `text1[i-1] != text2[j-1]`:**
    The current characters do not match. The LCS of `text1[0..i-1]` and `text2[0..j-1]` must be the LCS of one of these pairs:
    - `text1[0..i-2]` and `text2[0..j-1]` (i.e., `dp[i-1][j]`)
    - `text1[0..i-1]` and `text2[0..j-2]` (i.e., `dp[i][j-1]`)
    We take the maximum of these two: `dp[i][j] = max(dp[i-1][j], dp[i][j-1])`.
    This implies that at least one of `text1[i-1]` or `text2[j-1]` is not part of the LCS.
    Labuladong's visualization for this: `![](/algo/images/LCS/2.jpg)`

## 🕋 Base Cases
- `dp[0][j] = 0` for all `j`: If `text1` is empty, LCS length is 0.
- `dp[i][0] = 0` for all `i`: If `text2` is empty, LCS length is 0.
These are typically handled by initializing the DP table with zeros.

## 🐍 Python Solution (LC1143)
```python
class Solution:
    def longestCommonSubsequence(self, text1: str, text2: str) -> int:
        m, n = len(text1), len(text2)

        # dp[i][j] = LCS of text1[0...i-1] and text2[0...j-1]
        dp = [[0] * (n + 1) for _ in range(m + 1)]

        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if text1[i-1] == text2[j-1]:
                    dp[i][j] = 1 + dp[i-1][j-1]
                else:
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])

        return dp[m][n]
```
Labuladong's visualization of the DP table construction: `![](/algo/images/LCS/dp-table.jpg)` and `![](/algo/images/LCS/result.jpg)`.

## Complexity
- **Time Complexity:** $O(M \cdot N)$, where $M$ and $N$ are the lengths of `text1` and `text2`. We fill an $M \times N$ table.
- **Space Complexity:** $O(M \cdot N)$ for the DP table. Can be optimized to $O(\min(M,N))$ since `dp[i][j]` only depends on the previous row/column.

## Variations and Applications

The LCS core logic can solve related problems:

### 1. [[Interview/Practice/LeetCode/LC583 - Delete Operation for Two Strings|LC583 - Delete Operation for Two Strings]]
Given `word1` and `word2`, find the minimum number of steps (deletions) required to make `word1` and `word2` the same.
- **Solution:** The parts that remain must form an LCS.
  - `lcs_len = longestCommonSubsequence(word1, word2)`
  - Deletions from `word1`: `len(word1) - lcs_len`
  - Deletions from `word2`: `len(word2) - lcs_len`
  - Total deletions: `len(word1) + len(word2) - 2 * lcs_len`.

### 2. [[Interview/Practice/LeetCode/LC712 - Minimum ASCII Delete Sum for Two Strings|LC712 - Minimum ASCII Delete Sum for Two Strings]]
Given `s1` and `s2`, find the lowest ASCII sum of deleted characters to make two strings equal.
- **DP State:** `dp[i][j]` = minimum ASCII delete sum to make `s1[0..i-1]` and `s2[0..j-1]` equal.
- **Transition:**
    - If `s1[i-1] == s2[j-1]`: No deletion needed for these chars. `dp[i][j] = dp[i-1][j-1]`.
    - If `s1[i-1] != s2[j-1]`:
        - Option 1: Delete `s1[i-1]`. Cost: `ord(s1[i-1]) + dp[i-1][j]`.
        - Option 2: Delete `s2[j-1]`. Cost: `ord(s2[j-1]) + dp[i][j-1]`.
        - (A third option, deleting both, is covered by these two. E.g., deleting `s1[i-1]` leaves `s1[0..i-2]` and `s2[0..j-1]`. If `s2[j-1]` also needs to be deleted for that subproblem, `dp[i-1][j]` will handle it.)
        - `dp[i][j] = min(ord(s1[i-1]) + dp[i-1][j], ord(s2[j-1]) + dp[i][j-1])`.
- **Base Cases:**
    - `dp[0][0] = 0`.
    - `dp[i][0] = sum(ord(s1[k]) for k in 0..i-1)`. (Delete all of `s1` prefix).
    - `dp[0][j] = sum(ord(s2[k]) for k in 0..j-1)`. (Delete all of `s2` prefix).

```python
# Conceptual for LC712
# class SolutionLC712:
#     def minimumDeleteSum(self, s1: str, s2: str) -> int:
#         m, n = len(s1), len(s2)
#         dp = [[0] * (n + 1) for _ in range(m + 1)]

#         # Base cases
#         for i in range(1, m + 1):
#             dp[i][0] = dp[i-1][0] + ord(s1[i-1])
#         for j in range(1, n + 1):
#             dp[0][j] = dp[0][j-1] + ord(s2[j-1])

#         for i in range(1, m + 1):
#             for j in range(1, n + 1):
#                 if s1[i-1] == s2[j-1]:
#                     dp[i][j] = dp[i-1][j-1]
#                 else:
#                     dp[i][j] = min(dp[i-1][j] + ord(s1[i-1]), 
#                                    dp[i][j-1] + ord(s2[j-1]))
#         return dp[m][n]
```

## 总结 (Summary)
- LCS is a fundamental DP problem for comparing two sequences.
- The state `dp[i][j]` typically represents the LCS length for prefixes.
- Transitions depend on whether `s1[i-1]` equals `s2[j-1]`.
- This pattern is adaptable for variations like minimum deletions or ASCII sum deletions.

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/index|Sequence DP Patterns]]
Related Problems: [[Interview/Practice/LeetCode/LC1143 - Longest Common Subsequence|LC1143]], [[Interview/Practice/LeetCode/LC583 - Delete Operation for Two Strings|LC583]], [[Interview/Practice/LeetCode/LC712 - Minimum ASCII Delete Sum for Two Strings|LC712]]
