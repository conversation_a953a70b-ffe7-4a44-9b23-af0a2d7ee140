---
tags: [concept/algorithms, concept/dynamic_programming, type/problem_solving_pattern, pattern/sequence_dp, pattern/kadane, course/labuladong]
aliases: [<PERSON><PERSON><PERSON>'s Algorithm, Maximum Subarray Sum, 最大子数组和]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/子序列类型问题/动态规划设计：最大子数组.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/子序列类型问题/动态规划设计：最大子数组.md|动态规划设计：最大子数组 by Labuladong]].
> Primary LeetCode problem: [[Interview/Practice/LeetCode/LC53 - Maximum Subarray|LC53]].

# Dynamic Programming: Ka<PERSON><PERSON>'s Algorithm (Maximum Subarray Problem)

The Maximum Subarray problem asks to find the contiguous subarray within a one-dimensional array of numbers which has the largest sum. <PERSON><PERSON><PERSON>'s Algorithm is an efficient dynamic programming approach to solve this.

## 🎯 Problem Definition (LC53)
Given an integer array `nums`, find the contiguous subarray (containing at least one number) which has the largest sum and return its sum.

**Example:** `nums = [-2,1,-3,4,-1,2,1,-5,4]`
Output: 6. Subarray `[4,-1,2,1]` has the largest sum = 6.

## 💡 DP State Definition (Kadane's Logic)
Let `dp[i]` be the maximum sum of a subarray **ending at index `i`**.
(This is a common DP pattern for array problems: define state based on "ending at `i`").

## ➕ State Transition Equation
To calculate `dp[i]`:
The maximum subarray ending at `nums[i]` can either be:
1.  `nums[i]` itself (if `dp[i-1]` is negative, starting a new subarray from `nums[i]` is better).
2.  `nums[i] + dp[i-1]` (if `dp[i-1]` is positive, extend the previous maximum subarray).

So, `dp[i] = nums[i] + max(0, dp[i-1])`.
Or, equivalently: `dp[i] = max(nums[i], nums[i] + dp[i-1])`. (Labuladong uses this form).

**Base Case:** `dp[0] = nums[0]`.

**Final Result:** The answer is not necessarily `dp[n-1]`, because the overall maximum subarray might not end at the last element. The answer is the maximum value in the entire `dp` array: `max(dp)`.

## 🐍 Python Solution (LC53 - Kadane's Algorithm)

### Full DP Table Version
```python
class Solution:
    def maxSubArray_dp_table(self, nums: list[int]) -> int:
        n = len(nums)
        if n == 0:
            return 0 # Or based on constraints for empty array

        dp = [0] * n
        dp[0] = nums[0]
        max_so_far = dp[0] # Track overall maximum

        for i in range(1, n):
            # dp[i] = max sum of subarray ending at index i
            dp[i] = max(nums[i], nums[i] + dp[i-1])
            max_so_far = max(max_so_far, dp[i])

        return max_so_far
```

### Space-Optimized Version ($O(1)$ space)
Since `dp[i]` only depends on `dp[i-1]`, we can optimize space.
We only need to keep track of `current_max_ending_here` (equivalent to `dp[i]`) and `global_max_so_far`.

```python
class Solution:
    def maxSubArray(self, nums: list[int]) -> int: # Kadane's optimized
        n = len(nums)
        if n == 0:
            # Constraints usually ensure nums is non-empty, 
            # but good to handle. Problem says "at least one number".
            # If empty allowed and sum is 0, return 0. If sum for empty is neg inf, handle.
            # LeetCode constraints: nums.length >= 1.
            pass

        current_max_ending_here = nums[0]
        global_max_so_far = nums[0]

        for i in range(1, n):
            # Calculate max sum of subarray ending at current index i
            current_max_ending_here = max(nums[i], nums[i] + current_max_ending_here)

            # Update overall maximum sum found so far
            global_max_so_far = max(global_max_so_far, current_max_ending_here)

        return global_max_so_far
```
Labuladong's visualization: `![](/algo/images/maxSubArray/1.gif)` shows this iterative process.

## Complexity
- **Time Complexity:** $O(N)$ for a single pass through the array.
- **Space Complexity:**
    - $O(N)$ for the full DP table version.
    - $O(1)$ for the space-optimized (Kadane's) version.

## Alternative Perspective: Prefix Sums (from Labuladong's Article)
Labuladong also discusses solving this using prefix sums, though it's less direct than Kadane's for this specific "maximum subarray sum" problem.
- Precompute `prefix_sum[i] = sum(nums[0...i-1])`.
- The sum of subarray `nums[i...j]` is `prefix_sum[j+1] - prefix_sum[i]`.
- The problem becomes finding `max(prefix_sum[j+1] - prefix_sum[i])` over all `0 <= i <= j < n`.
- This is equivalent to, for each `j`, finding `max(prefix_sum[j+1] - min(prefix_sum[0...j]))`.
- Iterate `j`, maintain `min_prefix_sum_so_far`.
  `max_subarray_sum = max(max_subarray_sum, current_prefix_sum - min_prefix_sum_so_far)`.

```python
# Conceptual prefix sum approach
# def maxSubArray_prefix_sum(nums: list[int]) -> int:
#     min_prefix_sum = 0
#     current_prefix_sum = 0
#     max_sum = float('-inf')

#     if not nums: return 0 # Or handle as per constraints
#     if all(x < 0 for x in nums): return max(nums) # Kadane handles this naturally

#     for num in nums:
#         current_prefix_sum += num
#         max_sum = max(max_sum, current_prefix_sum - min_prefix_sum)
#         min_prefix_sum = min(min_prefix_sum, current_prefix_sum)
#     return max_sum
```
This prefix sum based approach is very similar to Kadane's. If `current_max_ending_here` becomes negative in Kadane's, it's reset by `max(nums[i], ...)`, effectively like starting a new subarray or considering `nums[i]` alone. This corresponds to `current_prefix_sum - min_prefix_sum` where `min_prefix_sum` was the sum *just before* the current optimal subarray started.

## 总结 (Summary)
- Kadane's Algorithm provides an elegant $O(N)$ time, $O(1)$ space solution to the Maximum Subarray Sum problem.
- It uses a DP approach where `dp[i]` (or `current_max_ending_here`) is the max sum of a subarray ending at index `i`.
- The overall maximum is tracked separately (`global_max_so_far`).
- This is a fundamental DP pattern for array/sequence problems.

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/index|Sequence DP Patterns]]
Related Problems: [[Interview/Practice/LeetCode/LC53 - Maximum Subarray|LC53]]
