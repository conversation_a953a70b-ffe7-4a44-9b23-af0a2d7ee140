---
tags: [index, concept/algorithms, concept/dynamic_programming, pattern/game_dp]
aliases: [Game DP Index, DP in Games]
---
# Game Dynamic Programming Patterns

This section covers DP patterns applied to game theory or game-like problems.

- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Game DP/Burst Balloons|Burst Balloons]]

- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Game DP/Super Egg Drop|Super Egg Drop]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Game DP/Regular Expression Matching|Regular Expression Matching]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Game DP/Travel Min Cost K Stops|Travel Min Cost K Stops]]
---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/index|DP Patterns Index]]
