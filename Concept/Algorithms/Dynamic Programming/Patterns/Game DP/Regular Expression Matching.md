---
tags: [concept/algorithms, concept/dynamic_programming, type/problem_solving_pattern, pattern/sequence_dp, pattern/regex, course/labuladong]
aliases: [Regex Matching DP, 正则表达式匹配DP]
source_file_path: "Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/经典动态规划：正则表达式.md"
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/经典动态规划：正则表达式|经典动态规划：正则表达式 by Labuladong]].
> This problem, [[Interview/Practice/LeetCode/LC10 - Regular Expression Matching|LC10 - Regular Expression Matching]], involves implementing regex matching with '.' and '*' wildcards.

# Dynamic Programming: Regular Expression Matching (LC10)

Implementing regular expression matching for patterns involving `.` (matches any single character) and `*` (matches zero or more of the preceding element) is a classic dynamic programming problem.

## 核心思想 (Core Idea): DP Table for Matching

Let `dp[i][j]` be `True` if the first `i` characters of the text `s` match the first `j` characters of the pattern `p`, and `False` otherwise.

**State Transition Logic:**
Consider `s[i-1]` and `p[j-1]`.
1.  **If `p[j-1]` is a normal character or `.` (dot):**
    -   If `s[i-1]` matches `p[j-1]` (either `s[i-1] == p[j-1]` or `p[j-1] == '.'`), then `dp[i][j] = dp[i-1][j-1]`.
    -   Else, `dp[i][j] = False`.

2.  **If `p[j-1]` is `*`:**
    This `*` applies to the character `p[j-2]`. Let `char_before_star = p[j-2]`.
    The `char_before_star*` part can match zero, one, or more occurrences of `char_before_star` in `s`.
    -   **Option 1: `char_before_star*` matches zero occurrences of `char_before_star`.**
        -   This means `char_before_star*` effectively disappears. The match depends on `dp[i][j-2]` (text `s[0..i-1]` vs pattern `p[0..j-3]`).
    -   **Option 2: `char_before_star*` matches one or more occurrences of `char_before_star`.**
        -   This is possible only if `s[i-1]` matches `char_before_star` (i.e., `s[i-1] == char_before_star` or `char_before_star == '.'`).
        -   If they match, `char_before_star*` consumes `s[i-1]`. The problem reduces to matching `s[0..i-2]` with `p[0..j-1]` (pattern `char_before_star*` can still match more). This depends on `dp[i-1][j]`.
    -   So, `dp[i][j] = dp[i][j-2] OR (match(s[i-1], p[j-2]) AND dp[i-1][j])`.

**Base Cases:**
- `dp[0][0] = True` (empty text matches empty pattern).
- `dp[i][0] = False` for `i > 0` (non-empty text cannot match empty pattern).
- `dp[0][j]`: For an empty text `s`, pattern `p[0..j-1]` can match only if it consists of `X*Y*Z*...` forms. E.g., `dp[0][j] = dp[0][j-2]` if `p[j-1] == '*'`.

## Python Solution for LC10
```python
class Solution:
    def isMatch(self, s: str, p: str) -> bool:
        m, n = len(s), len(p)

        # dp[i][j] = True if s[0...i-1] matches p[0...j-1]
        dp = [[False] * (n + 1) for _ in range(m + 1)]

        # Base cases
        dp[0][0] = True # Empty text, empty pattern

        # For p like "a*", ".*", "a*b*", etc. matching empty s
        for j in range(1, n + 1):
            if p[j-1] == '*':
                # '*' can match zero of preceding element
                if j >= 2: # Need p[j-2] to exist
                    dp[0][j] = dp[0][j-2]

        # Fill dp table
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                char_s = s[i-1]
                char_p = p[j-1]

                if char_p == '.' or char_p == char_s:
                    # Direct match or wildcard '.'
                    dp[i][j] = dp[i-1][j-1]
                elif char_p == '*':
                    # '*' case
                    # Preceding char in pattern is p[j-2]
                    char_before_star = p[j-2]

                    # Option 1: '*' matches zero occurrences of p[j-2]
                    # This means p[0...j-1] reduces to p[0...j-3]
                    match_zero = dp[i][j-2] # Relies on j >= 2 handled by loop bounds or initial base for j=1
                                           # (if j=1, p[j-1]='*', p[j-2] is invalid, so char_before_star fails)
                                           # The problem statement says p is valid regex.

                    # Option 2: '*' matches one or more occurrences of p[j-2]
                    # This requires s[i-1] to match p[j-2]
                    match_one_or_more = False
                    if char_s == char_before_star or char_before_star == '.':
                        match_one_or_more = dp[i-1][j] # s[0...i-2] vs p[0...j-1] (* can still match more)

                    dp[i][j] = match_zero or match_one_or_more
                else:
                    # No match, not '.', not '*'
                    dp[i][j] = False

        return dp[m][n]
```

## Complexity
- **Time:** $O(M \cdot N)$, where $M=\text{len}(s), N=\text{len}(p)$.
- **Space:** $O(M \cdot N)$ for the DP table.

## 总结 (Summary)
- Regex matching with `.` and `*` can be solved with 2D DP.
- `dp[i][j]` represents if `s` prefix matches `p` prefix.
- Transitions depend on whether `p[j-1]` is a normal char, `.`, or `*`.
- The `*` case considers matching zero or more of the preceding character.

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Concepts]]
Related: [[Interview/Practice/LeetCode/LC10 - Regular Expression Matching|LC10 - Regular Expression Matching]]
