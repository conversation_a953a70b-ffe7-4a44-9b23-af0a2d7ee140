---
tags: [concept/algorithms, concept/dynamic_programming, type/problem_solving_pattern, pattern/game_dp, pattern/interval_dp, course/labuladong]
aliases: [Burst Balloons DP, 戳气球问题]
source_file_path: "Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/经典动态规划：戳气球.md"
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/经典动态规划：戳气球|经典动态规划：戳气球 by Labuladong]].
> This problem requires finding the maximum coins by bursting balloons, where bursting balloon `i` gives `nums[left] * nums[i] * nums[right]` coins.

# Dynamic Programming: Burst Balloons Pattern (LC312)

The "Burst Balloons" problem ([[Interview/Practice/LeetCode/LC312 - Burst Balloons|LC312]]) is a challenging dynamic programming problem. The key is to correctly define the DP state and identify the optimal substructure by considering the *last* balloon to be burst in an interval.

## 核心思想 (Core Idea): Reverse Thinking - Last Balloon to <PERSON>urst

A naive approach of choosing which balloon to burst *first* is difficult because bursting a balloon changes the "adjacency" for future calculations.
The crucial insight is to think about which balloon is **burst last** in a given interval `(i, j)` (exclusive of `i` and `j`).

Let `dp[i][j]` be the maximum coins obtainable by bursting all balloons in the open interval `(i, j)`, i.e., `nums[i+1...j-1]`.
To compute `dp[i][j]`:
- Assume balloons `i` and `j` are fixed (not burst yet, acting as boundaries).
- Consider bursting balloon `k` (where `i < k < j`) *last* within the interval `(i,j)`.
- When balloon `k` is burst last, all balloons in `(i, k)` and `(k, j)` must have already been burst.
- The coins from bursting `k` at this stage are `points[i] * points[k] * points[j]`. (Here `points` is `nums` padded with 1s at ends).
- The total coins for this choice of `k` are:
  `dp[i][k] + dp[k][j] + points[i] * points[k] * points[j]`
- We iterate `k` from `i+1` to `j-1` and choose the `k` that maximizes this sum.

**State Transition Equation:**
`dp[i][j] = max_{i<k<j} (dp[i][k] + dp[k][j] + points[i] * points[k] * points[j])`

**Padded Array:**
Add `1` to both ends of the `nums` array to handle boundary conditions easily. Let this be `points`.
If `nums = [3,1,5,8]`, then `points = [1,3,1,5,8,1]`.
The problem becomes finding `dp[0][n+1]` where `n` is original length.

**Iteration Order:**
`dp[i][j]` depends on sub-intervals `dp[i][k]` and `dp[k][j]`. The length of the interval `len = j - i + 1` must increase.
- Loop `len` from `3` to `N` (where `N` is length of `points`).
- Loop `i` from `0` to `N - len`.
- `j = i + len - 1`.
- Loop `k` from `i+1` to `j-1`.

## Python Solution for LC312
```python
class Solution:
    def maxCoins(self, nums: list[int]) -> int:
        n_orig = len(nums)
        if n_orig == 0:
            return 0

        # Add 1 at both ends
        points = [1] * (n_orig + 2)
        for i in range(n_orig):
            points[i+1] = nums[i]

        n_padded = len(points) # n_padded = n_orig + 2

        # dp[i][j] = max coins from bursting balloons in (i, j)
        dp = [[0] * n_padded for _ in range(n_padded)]

        # len is the length of the interval [i, ..., j] considered
        # We are interested in interval (i,j), so actual balloons are points[i+1...j-1]
        # The number of balloons in (i,j) is j - i - 1.
        # Smallest meaningful interval to consider bursting is (i, k, j) where k is one balloon.
        # This means j - i + 1 (length of [i..j]) must be at least 3.
        for length_of_points_segment in range(3, n_padded + 1): # Length of points[i...j]
            for i in range(n_padded - length_of_points_segment + 1):
                j = i + length_of_points_segment - 1
                # Now we are considering bursting balloons in (i, j)
                # Iterate through k as the *last* balloon to burst in (i, j)
                for k in range(i + 1, j): # i < k < j
                    # Coins from bursting k last:
                    coins_from_k = points[i] * points[k] * points[j]
                    # Coins from subproblems (balloons in (i,k) and (k,j) already burst)
                    dp[i][j] = max(dp[i][j], dp[i][k] + dp[k][j] + coins_from_k)

        return dp[0][n_padded - 1]
```

## Complexity
- **Time:** $O(N^3)$ due to three nested loops (length, i, k). $N$ is padded length.
- **Space:** $O(N^2)$ for the `dp` table.

## 总结 (Summary)
- The Burst Balloons problem is solved by DP, thinking about the *last* balloon burst in an interval `(i, j)`.
- Padding the input `nums` array with `1`s simplifies boundary calculations.
- The state `dp[i][j]` represents max coins from interval `(i, j)`.
- Iteration is by increasing length of intervals.

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Concepts]]
Related: [[Interview/Practice/LeetCode/LC312 - Burst Balloons|LC312 - Burst Balloons]]
