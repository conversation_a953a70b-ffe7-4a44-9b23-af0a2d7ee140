---
tags: [concept/algorithms, concept/dynamic_programming, type/problem_solving_pattern, pattern/shortest_path_dp, pattern/graph, course/labuladong]
aliases: [Cheapest Flights K Stops DP, 加权图带限制最短路]
source_file_path: "Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/旅游省钱大法：加权最短路径.md"
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/旅游省钱大法：加权最短路径|旅游省钱大法：加权最短路径 by Labuladong]].
> This problem, [[Interview/Practice/LeetCode/LC787 - Cheapest Flights Within K Stops|LC787 - Cheapest Flights Within K Stops]], finds the cheapest flight path with at most K stops, often solved with a Bellman-Ford like DP or modified Dijkstra.

# Dynamic Programming: Cheapest Flights Within K Stops (LC787)

Finding the cheapest flight route from a source `src` to a destination `dst` with at most `K` stops is a variation of the shortest path problem on a weighted directed graph. This can be solved using dynamic programming, with a structure similar to the Bellman-Ford algorithm.

## 核心思想 (Core Idea): DP State Definition

Let `dp[k][v]` be the minimum cost to reach city `v` from `src` using at most `k` stops (which means at most `k+1` flights/edges).

**State Transition Equation:**
To calculate `dp[k][v]`:
We consider all flights `(u, v, price)` that land in city `v`.
If we take a flight from `u` to `v`, this flight is one leg of the journey.
The cost to reach `u` using `k-1` stops (i.e., `k` flights) must have been `dp[k-1][u]`.
So, `dp[k][v] = min(dp[k][v], dp[k-1][u] + price_of_flight(u,v))` for all incoming flights to `v`.

The number of stops `k` here means intermediate cities.
- `k=0` stops means direct flight (1 edge).
- `k=1` stop means 2 flights (2 edges).
- Max `K` stops means at most `K+1` flights/edges.

Let `dp[i][j]` = minimum cost to reach city `j` using exactly `i` flights.
The number of flights `i` can range from 1 to `K+1`.

**Refined DP State:**
`dp[flights_taken][city_v]` = minimum cost to reach `city_v` from `src` using exactly `flights_taken` flights.

**State Transition:**
`dp[i][v] = min(dp[i][v], dp[i-1][u] + price_of_flight(u,v))`
Iterate `i` from 1 to `K+1` (number of flights).
For each flight `(u, v, price)`:
  `dp[i][v] = min(dp[i][v], dp[i-1][u] + price)`

**Base Case:**
- `dp[0][src] = 0` (cost to reach source with 0 flights is 0).
- All other `dp[0][v] = infinity`.

The final answer is `min(dp[i][dst])` for `1 <= i <= K+1`.

## Python Solution for LC787
```python
class Solution:
    def findCheapestPrice(self, n: int, flights: list[list[int]], src: int, dst: int, k_stops: int) -> int:
        # dp[i][j] = min cost to reach city j using at most i edges (flights)
        # k_stops means at most k_stops+1 edges
        max_edges = k_stops + 1

        # Initialize dp table with infinity
        # dp[num_edges][city_id]
        dp = [[float('inf')] * n for _ in range(max_edges + 1)]

        # Base case: cost to reach src with 0 edges is 0
        # This loop will correctly set dp[i][src] = 0 for all i
        # if we assume you can "stay" at src with 0 cost.
        # Or more precisely:
        dp[0][src] = 0
        # For Bellman-Ford like DP, we iterate edges.
        # dp[i][j] = cost to reach j using EXACTLY i flights.

        # Let dp_prev store results for i-1 flights
        # Let dp_curr store results for i flights

        # dp[city] = min cost to reach city with current number of flights
        # We iterate k+1 times for number of flights.

        # dp[j] stores min cost to reach city j
        # prev_dp[j] stores min cost to reach city j in previous iteration (fewer flights)

        # Correct DP state for Bellman-Ford like approach:
        # dp[i][v] = min cost to reach city v using up to i flights
        # Initialize costs to infinity, source to 0
        costs = [float('inf')] * n
        costs[src] = 0

        # Iterate for number of flights allowed (up to k_stops + 1)
        for num_flights_allowed in range(1, k_stops + 2): # 1 to K+1 flights
            new_costs = list(costs) # Costs for current iteration
            for u, v, price in flights:
                if costs[u] != float('inf'): # If source 'u' of flight is reachable
                    new_costs[v] = min(new_costs[v], costs[u] + price)
            costs = new_costs

        return costs[dst] if costs[dst] != float('inf') else -1

```
This is a Bellman-Ford like relaxation. For `i` iterations, it finds shortest paths using at most `i` edges.

## Complexity
- **Time:** $O((K+1) \cdot E)$, where $E$ is number of flights. We iterate $K+1$ times, and in each iteration, we iterate through all flights.
- **Space:** $O(N)$ for `costs` array (if optimized). Or $O((K+1) \cdot N)$ if storing all `dp[i][v]` states.

## 总结 (Summary)
- Cheapest flights with at most K stops is a shortest path problem with an edge constraint.
- Solvable with a DP approach similar to Bellman-Ford.
- `dp[i][v]` stores min cost to `v` using `i` flights. Iterate `i` from 1 to `K+1`.
- Each iteration relaxes edges based on costs from the previous iteration.

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Concepts]]
Related: [[Interview/Practice/LeetCode/LC787 - Cheapest Flights Within K Stops|LC787 - Cheapest Flights Within K Stops]]
