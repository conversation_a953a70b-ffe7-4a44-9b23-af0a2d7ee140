---
tags: [concept/algorithms, concept/dynamic_programming, type/problem_solving_pattern, pattern/game_dp, pattern/minimax, course/labuladong]
aliases: [Super Egg Drop DP, 高楼扔鸡蛋问题]
source_file_path: "Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/经典动态规划：高楼扔鸡蛋.md"
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第三章、经典动态规划算法/用动态规划玩游戏/经典动态规划：高楼扔鸡蛋|经典动态规划：高楼扔鸡蛋 by Labuladong]].
> This problem, [[Interview/Practice/LeetCode/LC887 - Super Egg Drop|LC887 - Super Egg Drop]], involves finding the minimum number of moves in the worst case to determine the critical floor `F`.

# Dynamic Programming: Super Egg Drop Pattern (LC887)

The Super Egg Drop problem asks for the minimum number of drops required in the worst case to determine the critical floor `F` (from which an egg will break), given `K` eggs and `N` floors.

## 核心思想 (Core Idea): Minimax DP

This is a minimax problem: we want to *minimize* the number of drops, considering the *worst-case* outcome of each drop.

**DP State Definition (Original Formulation):**
`dp(k, n)` = minimum number of drops required to find `F` with `k` eggs and `n` floors.

**Choices at each step:**
If we drop an egg from floor `x` (where `1 <= x <= n`):
1.  **Egg breaks:** We lose an egg. Problem reduces to finding `F` in `x-1` floors below (`0` to `x-1`) with `k-1` eggs. Number of drops: `dp(k-1, x-1)`.
2.  **Egg doesn't break:** We keep the egg. Problem reduces to finding `F` in `n-x` floors above (`x` to `n-1`, effectively `n-x` floors) with `k` eggs. Number of drops: `dp(k, n-x)`.

Since we need to plan for the **worst case**, the number of drops from choosing floor `x` is `1 + max(dp(k-1, x-1), dp(k, n-x))`.
We want to choose `x` such that this worst-case value is **minimized**.

**State Transition Equation (Original):**
`dp(k, n) = 1 + min_{1 <= x <= n} ( max(dp(k-1, x-1), dp(k, n-x)) )`

**Base Cases:**
- `dp(k, 0) = 0` (0 floors, 0 drops needed).
- `dp(1, n) = n` (1 egg, `n` floors, must drop linearly from floor 1 up to `n`, taking `n` drops in worst case).
- `dp(k, 1) = 1` (1 floor, 1 drop needed, for any `k >= 1`).

**Complexity of Original DP:** $O(K \cdot N^2)$ (Outer loops for K, N. Inner loop for x). This is too slow for typical constraints ($K \approx 100, N \approx 10000$).

## Optimization 1: Monotonicity of `max` terms
The terms `dp(k-1, x-1)` (egg breaks, search lower) and `dp(k, n-x)` (egg survives, search higher) are monotonic with respect to `x`.
- `dp(k-1, x-1)` is increasing in `x`.
- `dp(k, n-x)` is decreasing in `x`.
The `min` of these `max` values occurs where these two terms are closest. This allows using binary search for `x` instead of linear scan, reducing the inner loop to $O(\log N)$.
**Complexity with Binary Search for `x`:** $O(K \cdot N \log N)$. Still potentially too slow.

## Optimization 2: Alternative DP State Definition
Labuladong highlights a more clever DP state:
`dp[k][m]` = maximum number of floors `N` we can check given `k` eggs and `m` moves (drops).

**State Transition Equation (Alternative):**
If we make one drop with `k` eggs and `m` moves allowed:
- Drop from some floor `x`.
  - **Egg breaks:** We used 1 egg, 1 move. Remaining: `k-1` eggs, `m-1` moves. We can check `dp[k-1][m-1]` floors *below* `x`.
  - **Egg doesn't break:** We keep the egg, used 1 move. Remaining: `k` eggs, `m-1` moves. We can check `dp[k][m-1]` floors *above* `x`.
- Total floors checked: `dp[k-1][m-1]` (below) + `1` (floor `x` itself) + `dp[k][m-1]` (above).
So, `dp[k][m] = dp[k-1][m-1] + dp[k][m-1] + 1`.

**Base Cases:**
- `dp[k][0] = 0` (0 moves, 0 floors checked).
- `dp[0][m] = 0` (0 eggs, 0 floors checked).
- `dp[k][1] = 1` (1 move, can check 1 floor).
- `dp[1][m] = m` (1 egg, `m` moves, can check `m` floors linearly).

**Goal:** Find the smallest `m` such that `dp[K_orig][m] >= N_orig`.

**Complexity of Alternative DP:**
- Iterate `k` from `1` to `K_orig`.
- Iterate `m` from `1` until `dp[k][m] >= N_orig`.
- Max `m` is $O(N)$ (worst case with 1 egg).
- Total: $O(K \cdot N)$ to fill the table up to the point where `dp[K][m] >= N`.
- This approach computes the answer `m`.

### Python Solution (Alternative DP - $O(KN)$)
```python
class Solution:
    def superEggDrop(self, k_eggs: int, n_floors: int) -> int:
        # dp[k][m] = max floors checkable with k eggs and m moves
        # We want smallest m such that dp[k_eggs][m] >= n_floors

        # dp table size (k_eggs+1) x (n_floors+1) is too large if m can be n_floors
        # dp[m] will store dp[k_eggs][m] for current k_eggs.
        # dp[m] stores the max number of floors checkable with current number of eggs and m moves.

        # Let dp[m] be the max floors testable with current number of eggs and m moves.
        # For k eggs, dp_k[m] = dp_k-1[m-1] (broken) + dp_k[m-1] (not broken) + 1

        # dp[i] = max floors checkable with current k and i moves
        dp = [0] * (n_floors + 1) 

        # Iterate for number of eggs
        for current_k in range(1, k_eggs + 1):
            # dp_prev_k_m_minus_1 represents dp[current_k-1][m-1]
            # dp_current_k_m_minus_1 represents dp[current_k][m-1]
            # These need to be carefully managed in a 1D array for m.
            # Let's use 2D dp[k][m] for clarity first, then optimize.
            # dp_table[num_eggs][num_moves]
            pass # Actual O(KN) uses a 1D array for `m` and updates it per `k`.
                 # Labuladong's full optimized solution for LC887 uses this.

        # The actual O(KN) solution in Labuladong's article:
        # dp[j] means: with current number of eggs, using j moves, max floors I can test
        # prev_dp[j] means: with (current-1) number of eggs, using j moves, max floors I can test

        # dp[m] = max floors we can check with k eggs and m moves
        dp_kn = [0] * (n_floors + 1) # dp_kn[m] = max floors for k_eggs using m moves
                                     # this is effectively dp[k_eggs][m] from a 2D table.
                                     # We only need one row for k, iteratively build m.

        # The standard O(KN) solution:
        # dp[k][moves] = max floors checkable
        # Initialize a 1D dp array for moves, this represents results for current_k eggs.
        # Iterate k from 1 to K (eggs)
        #   Iterate m from 1 to N (moves)
        #     dp_k[m] = dp_k-1[m-1] + dp_k[m-1] + 1
        # This requires storing the previous k's results.

        dp_table_k_m = [[0] * (n_floors + 1) for _ in range(k_eggs + 1)]
        # dp_table_k_m[k][m] = max floors checkable with k eggs and m moves.

        moves = 0
        while dp_table_k_m[k_eggs][moves] < n_floors:
            moves += 1
            if moves > n_floors: # Safety break, practically N moves is max for 1 egg
                break 
            for k_current_egg_count in range(1, k_eggs + 1):
                # dp[k][m] = dp[k-1][m-1] + dp[k][m-1] + 1
                dp_table_k_m[k_current_egg_count][moves] = \
                    dp_table_k_m[k_current_egg_count - 1][moves - 1] + \
                    dp_table_k_m[k_current_egg_count][moves - 1] + 1
        return moves

```
Labuladong's article also covers the $O(K \log N)$ solution using binary search on the original `dp(k,n)` formulation, and the optimized $O(K \cdot N)$ solution for the alternative `dp[k][m]` state.
The final Python solution in the article for $O(K \cdot N)$ space and time is indeed as above. The even more optimized $O(N)$ space version would use only two rows for `dp_table_k_m`.

## Complexity
- **Original Recursive (with memo):** $O(K N^2)$ time.
- **Optimized with Binary Search for `x`:** $O(K N \log N)$ time.
- **Alternative DP `dp[k][m]`:** $O(K \cdot M_{ans})$ time, where $M_{ans}$ is the answer (min moves). $M_{ans}$ can be up to $N$. So, $O(KN)$. Space $O(KN)$ or $O(N)$ if space-optimized for `k` dimension.

## 总结 (Summary)
- Super Egg Drop is a minimax DP problem.
- The standard DP `dp(k,n)` can be optimized using binary search for the choice of floor `x`.
- A more efficient formulation `dp[k][m]` (max floors checkable with `k` eggs, `m` moves) solves it in $O(KN)$ time.
- This problem is a great example of how reformulating the DP state can lead to significant performance improvements.

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Concepts]]
Related: [[Interview/Practice/LeetCode/LC887 - Super Egg Drop|LC887 - Super Egg Drop]]
