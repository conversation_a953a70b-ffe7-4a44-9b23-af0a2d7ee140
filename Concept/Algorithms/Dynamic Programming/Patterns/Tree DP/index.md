---
tags: [index, concept/algorithms, concept/dynamic_programming, pattern/tree_dp]
aliases: [Tree DP Index, DP on Trees]
---

# Dynamic Programming on Trees Patterns

This section covers dynamic programming patterns applied to tree structures.

- [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Tree DP/00 - Unique Binary Search Trees (LC95 LC96)|Unique Binary Search Trees (LC95, LC96)]]
- (Placeholder for other Tree DP patterns like House Robber III)

---
Parent: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming Index]]
