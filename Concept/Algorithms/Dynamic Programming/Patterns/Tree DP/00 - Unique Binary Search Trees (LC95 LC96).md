---
tags: [concept/algorithms, concept/dynamic_programming, pattern/tree_dp, pattern/catalan_numbers, topic/bst, course/labuladong]
aliases: [Unique BSTs, Number of BSTs, Generating All BSTs, 不同的二叉搜索树]
---
> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷二叉树算法/二叉搜索树心法（构造篇）.md]].
> This note covers problems related to counting and generating all unique Binary Search Trees (BSTs) for a given number of nodes, often involving dynamic programming or recursive construction with memoization.

| [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Tree DP/index|Back to Tree DP Index]] | (Consider linking to general DP or BST concepts) |

# Unique Binary Search Trees (LC95 & LC96)

Problems involving counting or generating all unique Binary Search Trees (BSTs) given a set of distinct values (e.g., 1 to `n`) are classic examples that can be solved using dynamic programming and recursive construction.

## 1. Count Unique BSTs (LC96)
[[Interview/Practice/LeetCode/LC96 - Unique Binary Search Trees|LC96 - Unique Binary Search Trees]]
Given an integer `n`, return the number of structurally unique BST's which has exactly `n` nodes of unique values from 1 to `n`. This is equivalent to finding the n-th Catalan number.

**Recursive Decomposition Approach (with Memoization for DP):**
Let `G(n)` be the number of unique BSTs that can be formed with `n` nodes (values 1 to `n`).
To form a BST with `n` nodes, we can choose any `i` (from 1 to `n`) as the root.
- If `i` is the root:
    - The left subtree will be formed by `i-1` nodes (values 1 to `i-1`). Number of ways: `G(i-1)`.
    - The right subtree will be formed by `n-i` nodes (values `i+1` to `n`, which can be mapped to 1 to `n-i` for structure). Number of ways: `G(n-i)`.
- The total number of BSTs with `i` as root is `G(i-1) * G(n-i)`.
- Sum this over all possible roots `i`: $G(n) = \sum_{i=1}^{n} G(i-1) \cdot G(n-i)$
- Base cases: `G(0) = 1` (one way to form an empty tree), `G(1) = 1` (one way for a single node tree).

```python
class SolutionLC96:
    def numTrees(self, n: int) -> int:
        # memo[i] will store G(i)
        memo = {} 

        def count_unique_bsts(num_nodes: int) -> int:
            if num_nodes == 0 or num_nodes == 1:
                return 1
            if num_nodes in memo:
                return memo[num_nodes]
            
            total_trees = 0
            # Iterate through all possible root values (or rather, number of nodes in left/right)
            # For 'num_nodes' total nodes, if 'i' nodes are in the left subtree,
            # then 1 node is root, and 'num_nodes - 1 - i' nodes are in right subtree.
            for nodes_in_left_subtree in range(num_nodes): # 0 to num_nodes-1
                nodes_in_right_subtree = num_nodes - 1 - nodes_in_left_subtree
                
                total_trees += count_unique_bsts(nodes_in_left_subtree) * \
                               count_unique_bsts(nodes_in_right_subtree)
            
            memo[num_nodes] = total_trees
            return total_trees

        return count_unique_bsts(n)

    # Tabulation (Bottom-up DP) approach for LC96
    def numTrees_tabulation(self, n: int) -> int:
        # G[i] stores the number of unique BST's for i nodes.
        G = [0] * (n + 1)
        G[0] = 1 # Empty tree
        G[1] = 1 # Single node tree
        
        # Calculate G[i] for i from 2 to n
        for num_total_nodes in range(2, n + 1):
            for num_left_nodes in range(num_total_nodes): # Number of nodes in left subtree (0 to num_total_nodes-1)
                # Root takes 1 node.
                # num_right_nodes = num_total_nodes - 1 - num_left_nodes
                num_right_nodes = num_total_nodes - 1 - num_left_nodes
                G[num_total_nodes] += G[num_left_nodes] * G[num_right_nodes]
        
        return G[n]
```

## 2. Generate All Unique BSTs (LC95)
[[Interview/Practice/LeetCode/LC95 - Unique Binary Search Trees II|LC95 - Unique Binary Search Trees II]]
Given an integer `n`, return all the structurally unique BST's which has exactly `n` nodes of unique values from 1 to `n`. Return the answer in any order.

**Recursive Decomposition Approach:**
Define `build(low, high)`: returns a list of all unique BSTs that can be formed using values from `low` to `high` inclusive.
1.  Base Case: If `low > high`, no nodes in this range. Return `[None]` (representing an empty subtree choice for a parent).
2.  Iterate `i` from `low` to `high` (current root value):
    a.  Recursively get all possible left subtrees: `left_subtrees = build(low, i - 1)`.
    b.  Recursively get all possible right subtrees: `right_subtrees = build(i + 1, high)`.
    c.  For each `left_tree` in `left_subtrees` and each `right_tree` in `right_subtrees`:
        i.  Create `root = TreeNode(i)`.
        ii. `root.left = left_tree`.
        iii. `root.right = right_tree`.
        iv. Add `root` to the list of results for the current `(low, high)` range.
3.  Return the list of constructed root nodes.
Memoization can be used if `build(low, high)` calls overlap significantly, mapping `(low, high)` tuples to lists of tree roots.

```python
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None): self.val = val; self.left = left; self.right = right

class SolutionLC95:
    def generateTrees(self, n: int) -> list[TreeNode | None]:
        if n == 0:
            return []
        
        memo = {} # For memoization: (low, high) -> list_of_roots

        def _build_unique_bsts(low: int, high: int) -> list[TreeNode | None]:
            if low > high:
                return [None] # Base case: represents an empty spot for a child
            
            if (low, high) in memo:
                return memo[(low, high)]

            all_trees_for_range = []
            # Iterate through all possible values for the root in the range [low, high]
            for root_val in range(low, high + 1):
                # Recursively generate all possible left subtrees
                left_subtrees = _build_unique_bsts(low, root_val - 1)
                # Recursively generate all possible right subtrees
                right_subtrees = _build_unique_bsts(root_val + 1, high)
                
                # Combine left and right subtrees with the current root
                for left_tree_root in left_subtrees:
                    for right_tree_root in right_subtrees:
                        current_root = TreeNode(root_val)
                        current_root.left = left_tree_root
                        current_root.right = right_tree_root
                        all_trees_for_range.append(current_root)
            
            memo[(low, high)] = all_trees_for_range
            return all_trees_for_range

        return _build_unique_bsts(1, n)
```

## 总结 (Summary)
- Counting unique BSTs (LC96) is a DP problem solvable by finding a recurrence relation based on choosing each element as a root and summing up combinations of left/right subtrees. This is related to Catalan numbers.
- Generating all unique BSTs (LC95) uses a recursive construction approach. For a given range of values `[low, high]`, iterate through each possible root `i` in this range. Then, recursively generate all possible left subtrees (from `[low, i-1]`) and right subtrees (from `[i+1, high]`) and combine them. Memoization is used to store and reuse lists of generated subtrees for specific ranges.
- Both problems demonstrate the "decomposition" thinking where the problem for `n` items (or a range `[low,high]`) is broken down by considering each item as a potential root and solving for sub-ranges.

---
| [[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Tree DP/index|Back to Tree DP Index]] | (Consider linking to general DP or BST concepts) |
