---
tags: [index, concept/algorithms, concept/dynamic_programming]
aliases: [Dynamic Programming Index, DP Index]
---

# Dynamic Programming (DP)

This section covers the principles, techniques, and problem-solving frameworks for Dynamic Programming.

## Core Concepts & Framework:
- [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming - Introduction and Framework]]
  - Three Pillars: Optimal Substructure, Overlapping Subproblems, State Transition Equation
  - Labuladong's Thinking Framework: States, Choices, DP Definition
  - Top-Down (Memoization) vs. Bottom-Up (Tabulation)
- `[[Interview/Concept/Algorithms/Dynamic Programming/01 - DP - Optimal Substructure|DP - Optimal Substructure]]` (Placeholder)
- `[[Interview/Concept/Algorithms/Dynamic Programming/02 - DP - Overlapping Subproblems|DP - Overlapping Subproblems]]` (Placeholder)
- [[Interview/Concept/Algorithms/Dynamic Programming/Memoization|Memoization (Top-Down DP)]]
- [[Interview/Concept/Algorithms/Dynamic Programming/Tabulation|Tabulation (Bottom-Up DP)]]
- `[[Interview/Concept/Algorithms/Dynamic Programming/05 - DP - Space Optimization|DP - Space Optimization Techniques]]` (Placeholder)

## Problem Patterns & Examples:
- **Sequence DP:**
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/Longest Increasing Subsequence|Longest Increasing Subsequence (LIS)]]` (Placeholder)
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/Longest Common Subsequence|Longest Common Subsequence (LCS)]]` (Placeholder)
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Sequence DP/Edit Distance|Edit Distance]]` (Placeholder)
- **Knapsack Problems:**
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Knapsack/0-1 Knapsack|0/1 Knapsack]]` (Placeholder)
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Knapsack/Unbounded Knapsack|Unbounded Knapsack (Complete Knapsack)]]` (Placeholder)
- **Interval DP / Game DP:**
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Interval DP/Burst Balloons|Burst Balloons (戳气球)]]` (Placeholder)
- **Pathfinding on Grids:**
  - `[[Interview/Concept/Algorithms/Dynamic Programming/Patterns/Grid DP/Unique Paths|Unique Paths]]` (Placeholder)

## LeetCode Examples Discussed:
- [[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 - Fibonacci Number]] (Illustrates overlapping subproblems & DP approaches)
- [[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 - Coin Change]] (Classic DP optimization problem)

## Visualization
```mermaid
graph TD
    DPConcept["Dynamic Programming"] --> IntroFramework["[[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Introduction & Framework]]"]
    IntroFramework --> Pillars["(Optimal Substructure, Overlapping Subproblems, State Transition)"]
    IntroFramework --> Approaches["(Memoization vs Tabulation)"]

    DPConcept --> Techniques["Core Techniques"]
    Techniques --> Memo["[[Interview/Concept/Algorithms/Dynamic Programming/Memoization|Memoization]]"]
    Techniques --> Tab["[[Interview/Concept/Algorithms/Dynamic Programming/Tabulation|Tabulation]]"]
    Techniques --> SpaceOpt["(Space Optimization)"]

    DPConcept --> Patterns["Problem Patterns"]
    Patterns --> SeqDP["Sequence DP (LIS, LCS, Edit Distance)"]
    Patterns --> KnapsackDP["Knapsack (0/1, Unbounded)"]
    Patterns --> IntervalDP["Interval/Game DP"]
    Patterns --> GridDP["Grid DP (Unique Paths)"]

    DPConcept --> ExamplesLC["LeetCode Examples"]
    ExamplesLC --> LC509["[[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 Fibonacci]]"]
    ExamplesLC --> LC322["[[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 Coin Change]]"]
    
    classDef main fill:#e6ccff,stroke:#9933ff,stroke-width:2px;
    class DPConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
