---
tags: [concept/algorithms, concept/dynamic_programming, type/framework, pattern/recursion, pattern/memoization, pattern/tabulation]
aliases: [DP Framework, Dynamic Programming Basics, 动态规划框架]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/动态规划解题套路框架.md]].
> Labuladong emphasizes that DP is essentially optimized brute-force search (窮举) through the identification of optimal substructure and overlapping subproblems.

# Dynamic Programming: Introduction and Framework

Dynamic Programming (DP) is a powerful algorithmic technique for solving optimization problems, typically those asking for a "maximum" or "minimum" value. At its core, DP is about intelligently exploring all possible solutions (brute-force or "穷举") and finding the best one.

## 🎯 What is Dynamic Programming?

DP problems usually involve finding an optimal solution (e.g., minimum cost, maximum profit, longest sequence). The key idea is to break down a complex problem into simpler, overlapping subproblems, solve each subproblem only once, and store their solutions to avoid redundant computations.

**Labuladong's Core Idea:** "Dynamic programming is essentially brute-force search with clever optimizations."

### The Three Pillars of Dynamic Programming:

1.  **Optimal Substructure:** An optimal solution to the overall problem can be constructed from optimal solutions to its subproblems. This means if you solve the subproblems optimally, you can combine these solutions to get the overall optimal solution.
    - *Example (Coin Change):* The minimum coins to make amount `A` can be found by taking one coin `c` and then finding the minimum coins for `A-c`. The subproblem `min_coins(A-c)` must be solved optimally.
    - Subproblems must be independent.

2.  **Overlapping Subproblems:** The recursive solution to the problem involves solving the same subproblems multiple times.
    - *Example (Fibonacci):* `fib(5)` calls `fib(4)` and `fib(3)`. `fib(4)` also calls `fib(3)`. `fib(3)` is an overlapping subproblem.
    - DP addresses this by storing the results of subproblems (memoization or tabulation) so they are computed only once.

3.  **State Transition Equation (状态转移方程):** A mathematical recurrence relation that defines how the solution to a problem (or state) can be derived from solutions to its subproblems (or previous/smaller states). This is the heart of a DP solution.

## 💡 Labuladong's Thinking Framework for DP

To derive the state transition equation and solve a DP problem, Labuladong suggests a three-step thinking process:

1.  **Clearly Define "States" (明确「状态」):**
    - Identify the variables that change as the problem is broken down into subproblems. These variables define the parameters of your `dp` function or the dimensions of your `dp` table.
    - *Example (Fibonacci):* The state is `n` (the n-th Fibonacci number).
    - *Example (Coin Change):* The state is `amount` (the target sum).

2.  **Identify "Choices" (明确「选择」):**
    - For each state, what are the decisions or actions you can take that lead to a transition to other states (subproblems)?
    - *Example (Coin Change):* For a given `amount`, the choices are which coin `c` from `coins` to use next. This leads to the subproblem of making `amount - c`.

3.  **Define the `dp` function/array's meaning (定义 `dp` 数组/函数的含义):**
    - Specify precisely what `dp(state_variables...)` computes or what `dp_table[state_indices...]` stores.
    - This definition is crucial for correctly formulating the recursive calls or iterative updates.
    - *Example (Fibonacci):* `dp(n)` returns the n-th Fibonacci number.
    - *Example (Coin Change):* `dp(amount)` returns the minimum number of coins to make up `amount`.

## 🛠️ Two Main Implementation Approaches

Once the state transition equation is formulated, DP problems can be solved using two general approaches:

### 1. Top-Down with Memoization (自顶向下带备忘录的递归)
   - This is a direct translation of the recursive state transition equation.
   - A "memo" (e.g., array or hash map) is used to store the results of already computed subproblems.
   - Before computing a subproblem, check the memo. If the result exists, return it. Otherwise, compute, store in memo, and then return.

   **General Structure (Recursive with Memoization):**
   ```python
   # memo = {} # or array initialized with a special value

   # def dp(state1, state2, ...):
   #     if (state1, state2, ...) in memo:
   #         return memo[(state1, state2, ...)]
       
   #     if base_case(state1, state2, ...):
   #         return base_case_value
       
   #     result = initial_value_for_optimization (e.g., infinity for min, -infinity for max)
   #     for choice in all_possible_choices_for_current_state:
   #         # Transition to new_state based on choice
   #         sub_problem_result = dp(new_state1, new_state2, ...)
   #         # Combine/update result
   #         result = optimize(result, combine(choice_effect, sub_problem_result))
           
   #     memo[(state1, state2, ...)] = result
   #     return result
   ```

### 2. Bottom-Up with Tabulation (自底向上 DP table迭代)
   - This involves building a `dp` table iteratively, starting from the base cases and progressively computing solutions for larger subproblems.
   - The `dp` table dimensions correspond to the states.
   - The iteration order must ensure that when `dp[state_indices...]` is computed, the values of subproblems it depends on (e.g., `dp[smaller_state_indices...]`) have already been computed.

   **General Structure (Iterative with DP Table):**
   ```python
   # dp_table = initialize_table_with_base_cases_or_default_values(...)

   # # Iterate through all states
   # for state1 in all_values_of_state1:
   #     for state2 in all_values_of_state2:
   #         # ...
   #         for choice in all_possible_choices_for_current_state:
   #             # Update dp_table[state1][state2]... based on previous states and choice
   #             dp_table[state1][state2]... = optimize(dp_table[state1][state2]..., 
   #                                                   combine(choice_effect, dp_table[prev_state1]...))
   # return dp_table[final_state_indices...]
   ```
Both approaches solve the same subproblems and generally have the same time complexity after optimization. The choice often comes down to conceptual clarity or ease of implementation for a specific problem.

## Example 1: Fibonacci Number (LeetCode 509)
[[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 - Fibonacci Number]]
- **Strictly speaking, not a DP problem** as it doesn't involve求最值 (optimization). However, it perfectly illustrates **overlapping subproblems** and the two DP solution approaches.
- **State:** `n` (the index of the Fibonacci number).
- **Choices:** Not applicable in the typical DP "choice" sense; the recurrence is fixed.
- **`dp(n)` definition:** Returns the n-th Fibonacci number.
- **State Transition:** $fib(n) = fib(n-1) + fib(n-2)$.
- **Base Cases:** $fib(0)=0, fib(1)=1$.

Labuladong's article shows:
1.  **Brute-force recursion:** $O(2^N)$ time due to recomputing subproblems. (Visualized in `div_mydata-fib`)
2.  **Top-down with memoization:** $O(N)$ time, $O(N)$ space for memo and recursion stack. (Visualized in `div_mydata-fib2`)
3.  **Bottom-up with DP table:** $O(N)$ time, $O(N)$ space for DP table. (Visualized in `div_mydata-fib3`)
4.  **Bottom-up with space optimization:** $O(N)$ time, $O(1)$ space (only need last two values). (Visualized in `div_fibonacci-number`)

## Example 2: Coin Change (LeetCode 322)
[[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 - Coin Change]]
Minimum coins to make amount `A` using `coins` array.
- **State:** `amount` (the current amount to make change for).
- **Choices:** For each `coin` in `coins`, choose to use it (if `amount >= coin`).
- **`dp(n)` definition:** Minimum coins to make amount `n`.
- **State Transition:** $dp(n) = \min_{c \in coins, n \ge c} \{1 + dp(n-c)\}$
- **Base Cases:** $dp(0)=0$. $dp(k) = \infty$ (or -1) if $k < 0$.

Labuladong's article shows:
1.  **Brute-force recursion:** Exponential time. (Visualized in `div_coin-change-brute-force`)
2.  **Top-down with memoization:** $O(A \cdot C)$ time (Amount * NumCoins), $O(A)$ space. (Visualized in `div_coin-change`)
3.  **Bottom-up with DP table:** $O(A \cdot C)$ time, $O(A)$ space.

## 总结 (Summary)
- Dynamic Programming solves optimization problems by breaking them into overlapping subproblems with optimal substructure.
- **Core Framework:**
    1.  Define **States**.
    2.  Identify **Choices** for each state.
    3.  Define the **`dp` function/array meaning**.
    4.  Formulate the **State Transition Equation**.
- **Implementation Methods:**
    - **Top-Down (Memoized Recursion):** Natural translation of recurrence.
    - **Bottom-Up (Tabulation):** Iterative filling of DP table.
- Key to DP is first writing the (often exponential) brute-force recursive solution based on the state transition, then optimizing it with memoization or by converting to tabulation.

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
Previous: [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer Framework]]
Next: [[Interview/Concept/Algorithms/Dynamic Programming/01 - DP - Optimal Substructure|DP - Optimal Substructure]] (Placeholder for more detailed pillar explanations)
Related Problems: [[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 - Fibonacci Number]], [[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 - Coin Change]]
Further Reading: [[Interview/Concept/Algorithms/Dynamic Programming/Memoization|Memoization]], [[Interview/Concept/Algorithms/Dynamic Programming/Tabulation|Tabulation]]
