---
tags: [concept/algorithms, concept/recursion, concept/dfs, concept/divide_and_conquer, type/problem_solving_framework]
aliases: [Recursive Thinking, Tree Perspective of Recursion, Traverse vs Decompose in Recursion, 递归思维模式]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/一个视角 _ 两种思维模式搞定递归.md]] and [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/学习数据结构和算法的框架思维.md]].
> Labuladong emphasizes that understanding recursion through the "tree" perspective and applying "traversal" or "decomposition" thinking modes are key to mastering recursive algorithms.

# Recursion: One Perspective, Two Thinking Modes

Recursion is a powerful problem-solving technique where a function calls itself to solve smaller instances of the same problem. The most effective way to understand and design recursive algorithms is through the **tree perspective**. This approach, combined with two primary thinking modes, forms a robust framework for tackling recursive challenges.

## 🌲 The Tree Perspective of Recursion

Any recursive algorithm can be visualized as operations on a **recursion tree**.
- The initial call is the root of this tree.
- Each recursive call made by a function becomes a child node.
- The `base cases` of the recursion are the leaf nodes of this tree.

The execution of a recursive function is like a traversal (often DFS-like) of this implicit recursion tree. Understanding this tree helps in debugging, analyzing complexity, and ensuring correctness.

### Example 1: Fibonacci Sequence
The Fibonacci sequence defined as $F(n) = F(n-1) + F(n-2)$ (with $F(0)=0, F(1)=1$) naturally forms a binary recursion tree.
- `fib(5)` calls `fib(4)` and `fib(3)`.
- `fib(4)` calls `fib(3)` and `fib(2)`, and so on.
This tree clearly shows overlapping subproblems, motivating dynamic programming. Labuladong's visualizer (`div_mydata-fib`) for this illustrates the concept well.

### Example 2: Permutations
Generating all permutations of a set (e.g., `[1,2,3]`) also forms a recursion tree where each path from root to leaf is a permutation. This is a core concept in [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking]].

## 🧠 Two Thinking Modes for Designing Recursive Algorithms

Labuladong proposes two primary ways to approach the design of recursive algorithms, based on the insights from "学习数据结构和算法的框架思维":

### 1. Decomposition Thinking Mode (Divide and Conquer Style - "分解问题思维")

- **Core Idea:** Define the recursive function based on what it **returns**. The function solves a subproblem and returns its result. The solution to the original problem is constructed from the results of these subproblems.
- **Function Signature:** The function usually has a meaningful return value representing the solution to `problem(input_parameters)`.
- **Process:**
    1.  **Define the function's meaning:** What does `recursive_function(sub_problem_input)` compute and return? This is the most crucial step.
    2.  **Base Case(s):** Identify the simplest versions of the problem that can be solved directly without further recursion.
    3.  **Recursive Step:** Assume `recursive_function` correctly solves smaller, self-similar subproblems. Use the results of these subproblem calls to construct the solution for the current problem. This often involves logic in the "post-order" position of a conceptual tree traversal.
- **Analogy:** This aligns with the [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer paradigm]] and is foundational to many [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming]] solutions.
- **Examples:**
    - **Fibonacci:** `fib(n)` is defined as returning the n-th Fibonacci number. It uses `fib(n-1)` and `fib(n-2)` (solutions to subproblems) to compute its result.
    - **Max Depth of Binary Tree (LC104):** `maxDepth(root)` returns the max depth of the tree rooted at `root`. It's computed as `1 + max(maxDepth(root.left), maxDepth(root.right))`. The problem is *decomposed* into finding max depths of subtrees.
        - Labuladong's visualizer `div_maximum-depth-of-binary-tree-divide` shows this.

```python
# Conceptual: Max Depth using Decomposition
# def maxDepth_decompose(root):
#     if root is None: return 0 # Base case
#     # Assume recursive calls work for subproblems
#     left_depth = maxDepth_decompose(root.left)
#     right_depth = maxDepth_decompose(root.right)
#     # Combine results to solve current problem
#     return 1 + max(left_depth, right_depth)
```

### 2. Traversal Thinking Mode (Iterative Exploration Style - "遍历思维")

- **Core Idea:** The recursive function's primary role is to **traverse** the implicit recursion tree or state space. The "answer" is often accumulated in external variables (e.g., instance variables, global variables) or passed by reference and modified as a side effect during the traversal.
- **Function Signature:** The function often has a `void` return type (or returns `None` explicitly in Python). It takes parameters that represent the current state of the traversal (e.g., current path, current depth).
- **Process:**
    1.  **Define state parameters:** What information needs to be carried along during the traversal?
    2.  **Base Case(s):** When to stop a particular path of exploration (e.g., reached a leaf, found a solution, invalid state). This is where results might be recorded or updated.
    3.  **Recursive Step (Choices/Transitions):** For each possible "next step" or "choice" from the current state:
        a.  Make the choice (update state).
        b.  Recursively call the function for the new state.
        c.  *Undo* the choice (backtrack) if necessary to explore other options. This "undo" step is characteristic of [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking]].
- **Analogy:** This is akin to a Depth-First Search (DFS) exploring a graph or tree.
- **Examples:**
    - **Permutations/Combinations/Subsets:** The `backtrack` function explores choices (which element to include/exclude). When a full solution (path to a leaf) is formed, it's added to a result list.
    - **Max Depth of Binary Tree (LC104) - Traversal Approach:**
        - Maintain a `current_depth` variable.
        - Pre-order: Increment `current_depth`. If leaf, update `max_overall_depth`. Recurse.
        - Post-order: Decrement `current_depth` (backtracking the depth).
        - Labuladong's visualizer `div_maximum-depth-of-binary-tree-traverse` illustrates this.

```python
# Conceptual: Max Depth using Traversal
# class Solution:
#     def __init__(self):
#         self.max_overall_depth = 0
#         self.current_depth = 0 # State carried through traversal (implicitly via stack or explicitly)
#
#     def maxDepth_traverse_entry(self, root):
#         self._traverse(root, 0) # Initial call with depth 0 or 1 depending on convention
#         return self.max_overall_depth
#
#     def _traverse(self, node, current_node_depth):
#         if node is None:
#             self.max_overall_depth = max(self.max_overall_depth, current_node_depth)
#             return
#
#         # current_node_depth passed along, or use instance var and increment/decrement
#         self._traverse(node.left, current_node_depth + 1)
#         self._traverse(node.right, current_node_depth + 1)
```
A more common traversal approach for max depth using instance variables:
```python
# class SolutionMaxDepthTraversal:
#     max_depth_val = 0
#     current_path_depth = 0
#
#     def maxDepth(self, root) -> int:
#         self.max_depth_val = 0
#         self.current_path_depth = 0
#         self.traverse(root)
#         return self.max_depth_val
#
#     def traverse(self, root):
#         if root is None:
#             # Reached beyond a leaf node
#             self.max_depth_val = max(self.max_depth_val, self.current_path_depth)
#             return
#
#         # Pre-order: entering a node, increment depth
#         self.current_path_depth += 1
#         self.traverse(root.left)
#         self.traverse(root.right)
#         # Post-order: leaving a node, decrement depth (backtrack)
#         self.current_path_depth -= 1
```

## 🤔 Choosing the Right Mode

- **Decomposition ("Define and Conquer"):**
    - Suitable when the problem has optimal substructure where the solution can be directly computed from sub-results (e.g., tree property calculations like height, sum, LCA).
    - Leads naturally to [[Interview/Concept/Algorithms/Dynamic Programming/Memoization|memoized recursion]] if subproblems overlap.
    - Often feels more "mathematical" or definitional.
- **Traversal ("Iterate and Accumulate"):**
    - Suitable for problems requiring exploration of a state space, finding all solutions, or when intermediate path information is critical.
    - Backtracking is a specialized form of this (e.g., N-Queens, Sudoku, permutations, combinations).
    - Logic often involves "doing" an action before recursion and "undoing" it after.

Sometimes, a problem can be solved using either mode, as shown with the "Max Depth of Binary Tree" example. The choice might depend on clarity, ease of implementation, or efficiency considerations (e.g., avoiding global state if decomposition is cleaner).

## 总结 (Summary)
1.  **Tree Perspective:** Always visualize recursion as traversing a tree. This helps in understanding control flow, base cases, and complexity.
2.  **Two Thinking Modes:**
    - **Decomposition (分解问题):** Define what `recursive_func(input)` returns. Use results of `recursive_func(sub_input)` to solve for `input`. This is typical for problems where the result for a node depends on results from its children (often using post-order logic).
    - **Traversal (遍历):** `recursive_func(state_params)` explores possibilities. Results are often accumulated as side effects or in external variables. This is typical for [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|backtracking]] or general [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|DFS]] state-space search.
3.  **Application:**
    - Decomposition often maps to [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming]] or [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer]].
    - Traversal often maps to [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking]] or general [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|DFS]] search.

Mastering these two modes and the tree perspective provides a powerful framework for tackling a wide range of recursive problems.

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
Previous: [[Interview/Concept/Algorithms/Tree Traversal/04 - DFS vs BFS - When to Use Which|DFS vs BFS - When to Use Which]] (Example of tree traversals)
Next: [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking Framework]] (as an application of traversal thinking)
Related: [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Binary Tree Recursive Traversal]], [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]]
