---
tags: [concept/algorithms, concept/recursion, concept/dfs, concept/divide_and_conquer, type/problem_solving_framework]
aliases: [Recursive Thinking, Tree Perspective of Recursion, Traverse vs Decompose in Recursion, 递归思维模式]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/一个视角 _ 两种思维模式搞定递归.md]].
> Labuladong emphasizes that understanding recursion through the "tree" perspective and applying "traversal" or "decomposition" thinking modes are key to mastering recursive algorithms.

# Recursion: One Perspective, Two Thinking Modes

Recursion is a powerful problem-solving technique where a function calls itself to solve smaller instances of the same problem. The most effective way to understand and design recursive algorithms is through the **tree perspective**.

## 🌲 The Tree Perspective of Recursion

Any recursive algorithm can be visualized as operations on a **recursion tree**.
- The initial call is the root of this tree.
- Each recursive call made by a function becomes a child node.
- The `base cases` of the recursion are the leaf nodes of this tree.

The execution of a recursive function is like a traversal (often DFS-like) of this implicit recursion tree.

### Example 1: Fibonacci Sequence
The Fibonacci sequence defined as $fib(n) = fib(n-1) + fib(n-2)$ (with $fib(0)=0, fib(1)=1$) naturally forms a binary recursion tree.
- `fib(5)` calls `fib(4)` and `fib(3)`.
- `fib(4)` calls `fib(3)` and `fib(2)`, and so on.
Labuladong's visualizer (`div_mydata-fib`) clearly shows this tree and how the values propagate up from the leaves (base cases) to the root.

### Example 2: Permutations
Generating all permutations of a set of numbers (e.g., `[1,2,3]`) also forms a recursion tree.
- The root represents the initial state (no numbers chosen).
- Children of a node represent choosing one of the remaining available numbers.
- Paths from the root to a leaf represent a complete permutation.
Labuladong's visualizer (`div_permutations`) shows this process.

## 🧠 Two Thinking Modes for Designing Recursive Algorithms

When faced with a problem that seems suitable for recursion (i.e., can be broken down into smaller, self-similar subproblems, or involves exhaustive search), there are two primary ways to approach the design:

### 1. Decomposition Thinking Mode (Divide and Conquer Style)

- **Core Idea:** Define the recursive function based on what it **returns**. The function solves a subproblem and returns its result. The solution to the original problem is constructed from the results of these subproblems.
- **Function Signature:** The function usually has a meaningful return value representing the solution to `problem(input_parameters)`.
- **Process:**
    1.  **Define the function's meaning:** What does `recursive_function(sub_problem_input)` compute and return?
    2.  **Base Case(s):** Identify the simplest versions of the problem that can be solved directly.
    3.  **Recursive Step:** Assume `recursive_function` correctly solves smaller subproblems. Use the results of these subproblem calls to construct the solution for the current problem.
- **Examples:**
    - **Fibonacci:** `fib(n)` is defined as returning the n-th Fibonacci number. It uses `fib(n-1)` and `fib(n-2)` (solutions to subproblems) to compute its result.
    - **Max Depth of Binary Tree (LeetCode 104):** `maxDepth(root)` returns the max depth of the tree rooted at `root`. It's computed as `1 + max(maxDepth(root.left), maxDepth(root.right))`. The problem is *decomposed* into finding max depths of subtrees.
        - Labuladong's visualizer `div_maximum-depth-of-binary-tree-divide` shows this.

```python
# Conceptual: Max Depth using Decomposition
# def maxDepth_decompose(root):
#     if root is None: return 0
#     left_depth = maxDepth_decompose(root.left)   # Result from left subproblem
#     right_depth = maxDepth_decompose(root.right) # Result from right subproblem
#     return 1 + max(left_depth, right_depth)      # Combine results
```

### 2. Traversal Thinking Mode (Iterative Exploration Style)

- **Core Idea:** The recursive function's primary role is to **traverse** the implicit recursion tree or state space. The "answer" is often accumulated in global variables, passed by reference, or built up as a side effect during the traversal.
- **Function Signature:** The function often has a `void` return type (or returns nothing explicitly in Python). It takes parameters that represent the current state of the traversal (e.g., current path, current depth).
- **Process:**
    1.  **Define state parameters:** What information needs to be carried along during the traversal?
    2.  **Base Case(s):** When to stop a particular path of exploration (e.g., reached a leaf, found a solution, invalid state).
    3.  **Recursive Step (Choices/Transitions):** For each possible "next step" or "choice" from the current state, make the choice, recursively call the function for the new state, and then *undo* the choice (backtrack) if necessary to explore other options.
- **Examples:**
    - **Permutations:** The `backtrack` function explores choices (which number to pick next). When a full permutation (path to a leaf) is formed, it's added to a global result list.
    - **Max Depth of Binary Tree (LeetCode 104) - Traversal Approach:**
        - Maintain a `current_depth` variable.
        - Pre-order: Increment `current_depth`. If leaf, update `max_overall_depth`. Recurse.
        - Post-order: Decrement `current_depth`.
        - Labuladong's visualizer `div_maximum-depth-of-binary-tree-traverse` illustrates this.

```python
# Conceptual: Max Depth using Traversal
# class Solution:
#     def __init__(self):
#         self.max_overall_depth = 0
#         self.current_depth = 0
#
#     def maxDepth_traverse(self, root):
#         self._traverse(root)
#         return self.max_overall_depth
#
#     def _traverse(self, node):
#         if node is None:
#             # Optional: update max_overall_depth here if path ends just before null
#             # self.max_overall_depth = max(self.max_overall_depth, self.current_depth)
#             return
#
#         self.current_depth += 1 # Pre-order action
#         if node.left is None and node.right is None: # Leaf node
#             self.max_overall_depth = max(self.max_overall_depth, self.current_depth)
#
#         self._traverse(node.left)
#         self._traverse(node.right)
#
#         self.current_depth -= 1 # Post-order action (backtrack depth)
```

## 🤔 Choosing the Right Mode

- **Decomposition ("Define and Conquer"):**
    - Suitable when the problem has optimal substructure and overlapping subproblems (can lead to dynamic programming).
    - The definition of what the recursive function *returns* is key.
    - Often more elegant for problems where the result can be directly computed from sub-results (e.g., tree traversals that build a list, tree property calculations like height/sum).
- **Traversal ("Iterate and Accumulate"):**
    - Suitable for problems requiring exploration of a state space, finding all solutions, or when intermediate path information is critical.
    - The parameters passed into the recursive function to maintain state are key.
    - Backtracking is a specialized form of this (e.g., N-Queens, Sudoku, permutations, combinations).

Sometimes, a problem can be solved using either mode, as shown with the "Max Depth of Binary Tree" example. The choice might depend on clarity or ease of implementation.

## 总结 (Summary)
1.  **Tree Perspective:** Always visualize recursion as traversing a tree. This helps in understanding control flow and base cases.
2.  **Two Thinking Modes:**
    - **Decomposition:** Define what `recursive_func(input)` returns. Use results of `recursive_func(sub_input)` to solve for `input`.
    - **Traversal:** `recursive_func(state_params)` explores possibilities. Results are often accumulated as side effects or in global/passed-by-reference variables. Backtracking is common here.
3.  **Application:**
    - Decomposition often maps to [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]] or [[Interview/Concept/Algorithms/Divide and Conquer/index|Divide and Conquer]].
    - Traversal often maps to [[Interview/Concept/Algorithms/Backtracking/index|Backtracking]] or general [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|DFS]] state-space search.

Mastering these two modes and the tree perspective provides a powerful framework for tackling a wide range of recursive problems.

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
Previous: [[Interview/Concept/Algorithms/Tree Traversal/04 - DFS vs BFS - When to Use Which|DFS vs BFS - When to Use Which]] (Example of tree traversals)
Next: [[Interview/Concept/Algorithms/Backtracking/index|Backtracking Framework]] (as an application of traversal thinking)
Related: [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Binary Tree Recursive Traversal]]
