---
tags: [concept/algorithms, concept/recursion, concept/dfs, concept/backtracking, type/clarification, course/labuladong]
aliases: [DFS vs Backtracking, Backtracking vs DFS Difference, 回溯与DFS辨析]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第二章、经典暴力搜索算法/DFS_回溯算法/解答回溯算法_DFS算法的若干疑问.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第二章、经典暴力搜索算法/DFS_回溯算法/解答回溯算法_DFS算法的若干疑问.md|解答回溯算法/DFS算法的若干疑问 by Labuladong]].
> This note clarifies the relationship and differences between Depth-First Search (DFS) and Backtracking algorithms, and addresses common questions about their implementation.

# DFS vs. Backtracking: Clarifications and Nuances

Depth-First Search (DFS) and Backtracking are closely related algorithmic paradigms, often used interchangeably, but with subtle distinctions in their typical application and structure. This note aims to clarify these points.

## 1. DFS vs. Backtracking: What's the Difference?

-   **DFS (Depth-First Search):**
    -   A general graph/tree traversal algorithm that explores as far as possible along each branch before backtracking.
    -   **Goal:** Typically to visit all nodes, find a path, detect cycles, or compute properties of the entire structure (e.g., connected components).
    -   **Structure:** The core logic often involves marking nodes as `visited` to avoid re-processing. The "processing" of a node can happen in pre-order, in-order (for binary trees), or post-order positions relative to recursive calls to its neighbors/children.
    -   See [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Binary Tree DFS]] or [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|Graph DFS]].

-   **Backtracking Algorithm:**
    -   A specialized form of DFS tailored for finding **all (or some) solutions** to a computational problem, by incrementally building candidates and abandoning a candidate ("backtracking") as soon as it's determined that it cannot possibly lead to a valid solution.
    -   **Goal:** To explore a "decision tree" or "state-space tree" where leaf nodes represent potential solutions.
    -   **Structure:** Characterized by "making a choice," recursively exploring consequences, and then "undoing the choice." This "undo" step is the essence of backtracking.
    -   The [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking Framework]] highlights this choice-recursion-undo pattern.

> [!TIP] Relationship
> Backtracking algorithms **use** a DFS-like exploration strategy to navigate the decision space. You can think of backtracking as DFS applied to an implicit state-space tree, with a strong emphasis on constructing solutions and pruning search paths.
> **All backtracking is DFS, but not all DFS is backtracking.** A simple DFS to, say, count nodes in a tree, doesn't necessarily involve the "choice-undo choice" characteristic of backtracking that's about constructing candidate solutions.

## 2. Placement of "Make Choice" / "Undo Choice"

The standard [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking Framework]] usually shows:
```python
# def backtrack(path, choices_list):
#     if end_condition_met:
#         add_solution(path)
#         return
#     for choice in choices_list:
#         # === Pre-order position relative to this choice ===
#         path.add(choice)          # Make choice
#         # Update choices_list for next level (e.g., mark choice as used)
#
#         backtrack(path, updated_choices_list) # Recurse
#
#         # === Post-order position relative to this choice ===
#         path.remove(choice)       # Undo choice
#         # Restore choices_list (e.g., unmark choice)
```
Here, "make choice" and "undo choice" are *inside* the `for` loop, tightly coupled with the specific `choice` being explored.

**Alternative Placement Question (from prompt):**
Sometimes, one might see a structure like:
```python
# def backtrack_alternative(current_node_state, ...):
#     if end_condition_met:
#         add_solution(current_node_state_path)
#         return
#
#     # === General "Make Choice" for current_node_state ===
#     # E.g., mark current_node_state as visited or part of path (if not done by caller)
#     # current_node_state.addToPath()
#
#     for next_possible_state from current_node_state:
#         # (Potentially some choice-specific setup here if not fully covered by next_possible_state)
#         backtrack_alternative(next_possible_state, ...)
#         # (Potentially choice-specific undo here)
#
#     # === General "Undo Choice" for current_node_state ===
#     # E.g., unmark current_node_state or remove from path
#     # current_node_state.removeFromPath()
```
**Clarification:**
- The standard backtracking template (`for choice in ...: make_choice(); backtrack(); undo_choice();`) is about the decision **at each step of forming the path**. The `make_choice/undo_choice` applies to adding/removing *one element* of the path.
- If "make choice" is outside the `for` loop, it usually means that the `backtrack(current_node_state, ...)` function itself represents exploring from `current_node_state`, and this state is already considered "chosen" when the function is called. The "undo choice" outside the loop would then correspond to backtracking *from* this `current_node_state` when all its subsequent options have been explored.
- **This is often a matter of how the "path" and "choices" are defined and managed.**
    - If `backtrack(index, current_path)` explores filling `current_path[index]`:
        - The `for` loop inside might iterate through possible values for `current_path[index]`. "Make choice" sets `current_path[index]`, "undo choice" resets it.
    - If `backtrack(current_node)` explores paths starting from `current_node` in a graph:
        - "Make choice" (entering `current_node`): `path.add(current_node); visited.add(current_node)`.
        - `for neighbor in current_node.neighbors`: `backtrack(neighbor)`.
        - "Undo choice" (leaving `current_node`): `path.remove(current_node); visited.remove(current_node)` (if needed, e.g., for paths, not for simple traversal).

**The key is consistency:** The "undo" must correspond to the "make." The core framework's "make/undo choice" *within* the loop is the most common for constructing permutations, combinations, subsets where each iteration of the loop picks *one more element* for the current solution candidate.

## 3. Can `backtrack/dfs/traverse` Functions Have Return Values?

**Yes, absolutely.**
- **Traversal Thinking (from [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Principles]]):** Functions often return `void` (or `None` in Python) and accumulate results via side effects (e.g., modifying a global list `self.results`).
- **Decomposition Thinking (Divide and Conquer):** Functions *must* return a meaningful value that represents the solution to the subproblem they are handling. This is common in dynamic programming on trees or many structural tree problems.
    - Example: `max_depth(node)` returns the depth of the subtree at `node`.
    - Example: `is_bst(node)` returns `True/False`.

**For Backtracking:**
- If the goal is to find **all** solutions, the `backtrack` function typically has a `void` return and adds valid solutions to a global list.
- If the goal is to find **if a solution exists** (e.g., Sudoku solver finding *one* solution), `backtrack` can return a boolean:
    ```python
    # def backtrack_sudoku_exists(...) -> bool:
    #     if solution_found_at_leaf:
    #         return True # Found a solution
    #     for choice ...:
    #         make_choice()
    #         if backtrack_sudoku_exists(...): # If recursive call finds a solution
    #             return True # Propagate success upwards
    #         undo_choice()
    #     return False # No choice from here led to a solution
    ```
    This allows for early termination once one solution is found.

## 4. Placement of Base Case / Pruning Logic

Should base cases or pruning conditions be checked:
(a) At the beginning of the recursive function?
(b) Before making a recursive call?

```python
# Option A: Check at the beginning
# def dfs_A(state):
#     if is_invalid(state) or is_base_case(state): # Pruning or base case
#         handle_base_case_or_return
#         return
#     # ...
#     for next_state in generate_next_states(state):
#         dfs_A(next_state)
#     # ...

# Option B: Check before recursive call
# def dfs_B(state):
#     # ... (process current state)
#     for next_state in generate_next_states(state):
#         if is_valid(next_state) and not_base_case(next_state): # Check validity before call
#             dfs_B(next_state)
#         else:
#             handle_base_case_for_next_state_if_needed
#     # ...
```
- **Labuladong's General Tree Traversal Framework:** The base case (`if root is None: return`) is at the beginning.
- **Backtracking Framework:** The "end condition" (which is a type of base case) is typically checked at the beginning. Pruning (e.g., `if used[i]: continue`) is usually done *before* making the choice and recursing for that specific choice (i.e., inside the loop, before the `backtrack` call).

**General Recommendation:**
- **Base cases defining termination of recursion (e.g., `node is None`, `index == len(array)`) are almost always best at the beginning of the function.** This defines the "bottom" of the recursion.
- **Pruning conditions that prevent exploring an invalid or unpromising branch:**
    - If the condition depends only on the *current state* (parameters passed to the function), it can be checked at the start.
    - If the condition depends on a *specific choice* about to be made (inside the `for` loop), it's naturally checked just before making that choice and recursing. Example: "if this number makes the sum exceed target, don't choose it."
- **Consistency:** Choose one style for base cases and stick to it for clarity. The most common and often clearest is to handle base cases (like null nodes, out-of-bounds indices, or found solutions) at the very start of the recursive function.

## 总结 (Summary)
1.  **Backtracking is a form of DFS** specialized for finding solutions by exploring a decision tree, characterized by "make choice - recurse - undo choice."
2.  The "make/undo choice" logic typically applies to each option *within* the loop exploring choices for the current step of solution construction.
3.  Recursive functions (DFS, backtrack, traverse) **can and often do have return values**, especially in "decomposition" style problems. For "find all solutions" backtracking, they are often `void`.
4.  **Base cases are best placed at the beginning** of the recursive function for clarity and correctness. Pruning specific to a choice is done before making that choice.

---
Parent: [[Interview/Concept/Algorithms/Backtracking/index|Backtracking Algorithms Index]] (or [[Interview/Concept/Algorithms/Recursion/index|Recursion Concepts Index]])
Related: [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking Framework]], [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Binary Tree DFS]]
