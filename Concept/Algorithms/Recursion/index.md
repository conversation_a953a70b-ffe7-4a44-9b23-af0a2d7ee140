---
tags: [index, concept/algorithms, concept/recursion]
aliases: [Recursion Index]
---

# Recursion Concepts

This section covers core ideas and frameworks for understanding and applying recursion in algorithms.

## Core Concepts:
- [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursion - One Perspective, Two Thinking Modes]]
  - The Tree Perspective of Recursion
  - Decomposition Thinking Mode (Divide and Conquer)
  - Traversal Thinking Mode (Iterative Exploration / Backtracking)
- [[Interview/Concept/Algorithms/Recursion/01 - DFS vs Backtracking - Clarifications|DFS vs Backtracking - Clarifications]]

## Related Algorithmic Paradigms:
- [[Interview/Concept/Algorithms/Backtracking/index|Backtracking]] (often implemented recursively)
- [[Interview/Concept/Algorithms/Divide and Conquer/index|Divide and Conquer]]
- [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]] (recursive solutions with memoization)
- [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal Algorithms]] (classic examples of recursion)

## Visualization
```mermaid
graph TD
    RecConcept["Recursion Concepts"] --> CoreRec["[[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|One Perspective, Two Modes]]"]
    CoreRec --> TreePersp["Tree Perspective"]
    CoreRec --> DecompMode["Decomposition Mode"]
    CoreRec --> TravMode["Traversal Mode"]

    RecConcept --> Clarify["[[Interview/Concept/Algorithms/Recursion/01 - DFS vs Backtracking - Clarifications|DFS vs. Backtracking Clarifications]]"]

    RecConcept --> RelatedParadigm["Related Paradigms"]
    RelatedParadigm --> BacktrackL["[[Interview/Concept/Algorithms/Backtracking/index|Backtracking]]"]
    RelatedParadigm --> DCL["[[Interview/Concept/Algorithms/Divide and Conquer/index|Divide & Conquer]]"]
    RelatedParadigm --> DPL["[[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]]"]
    RelatedParadigm --> TTL["[[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal]]"]

    classDef main fill:#ffe6cc,stroke:#ff8000,stroke-width:2px;
    class RecConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
