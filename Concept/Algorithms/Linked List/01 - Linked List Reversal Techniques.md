---
tags: [concept/algorithms, topic/linked_list, type/technique, pattern/list_reversal, course/labuladong]
aliases: [Linked List Reversal, Reverse Linked List Methods, 翻转链表技巧]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷链表算法/单链表的花式反转方法汇总.md]].
> This note covers common techniques for reversing linked lists: full reversal, partial reversal.

# Linked List: Reversal Techniques

Reversing a linked list or parts of it is a fundamental linked list manipulation skill. Labuladong's article covers several variations, including iterative and recursive approaches.

## 1. Reverse Entire Single Linked List ([[Interview/Practice/LeetCode/LC206 - Reverse Linked List|LC206]])

### a. Iterative Approach
This is the most common way. It involves three pointers: `prev`, `curr`, and `next_temp`.
- `prev`: Points to the previous node in the reversed part (initially `None`).
- `curr`: Points to the current node being processed (initially `head`).
- `next_temp`: Temporarily stores `curr.next` before `curr.next` is changed.

**Steps:**
1. Initialize `prev = None`, `curr = head`.
2. Loop while `curr` is not `None`:
   a. `next_temp = curr.next` (Save next node).
   b. `curr.next = prev` (Reverse current node's pointer).
   c. `prev = curr` (Move `prev` one step forward).
   d. `curr = next_temp` (Move `curr` one step forward).
3. After the loop, `prev` points to the new head of the reversed list. Return `prev`.

Labuladong's visualization panel `div_reverse-linked-list-iter` shows this.

### b. Recursive Approach
Define `reverseList(head)` to return the new head of the reversed list starting at `head`.
1.  **Base Case:** If `head` is `None` or `head.next` is `None` (list is empty or has one node), it's already "reversed". Return `head`.
2.  **Recursive Step:**
    a.  `last_node_of_reversed_sublist = reverseList(head.next)`: Recursively reverse the rest of the list (`head.next` onwards). `last_node_of_reversed_sublist` will be the new head of this reversed sublist (which is the original tail of the list).
    b.  Now, `head.next` (which was the original second node) is the *tail* of the reversed sublist. Make it point back to `head`: `head.next.next = head`.
    c.  Set `head.next = None` because `head` is now the new tail of the fully reversed list.
    d.  Return `last_node_of_reversed_sublist` (this is the overall new head).

Labuladong's visualization panel `div_reverse-linked-list` explains this with diagrams `![](/algo/images/reverse-linked-list/3.jpg)` to `![](/algo/images/reverse-linked-list/5.jpg)`.

```tikz
\begin{tikzpicture}[
    lnode/.style={draw, rectangle, minimum height=0.7cm, minimum width=1cm, font=\sffamily\small},
    arrow/.style={->, thick},
    rev_arrow/.style={->, thick, red, dashed}
]
    \node[lnode] (n1) at (0,0) {1}; \node[lnode] (n2) at (1.5,0) {2};
    \node[lnode] (n3) at (3,0) {3}; \node[lnode] (n4) at (4.5,0) {null};
    \draw[arrow] (n1) -- (n2); \draw[arrow] (n2) -- (n3); \draw[arrow] (n3) -- (n4);
    \node at (1.5, -0.7) {Original: 1 -> 2 -> 3};

    % After reverseList(2) returns 3 (new head of 3->2)
    % head is 1, head.next is 2.
    % head.next.next = head means 2.next = 1
    \node at (1.5, -1.5) {Recursive call `reverseList(1)`:};
    \node at (1.5, -2) {`last = reverseList(2)` returns Node(3) where `3 -> 2 -> null`};
    \node[lnode] (r_n3) at (0,-3) {3}; \node[lnode] (r_n2) at (1.5,-3) {2};
    \node[lnode] (r_n1) at (3,-3) {1}; \node[lnode] (r_nN) at (4.5,-3) {null};
    \draw[arrow] (r_n3) -- (r_n2);
    \node[anchor=south] at (r_n1.north) {head=1};
    \node[anchor=south] at (r_n2.north) {head.next=2};
    \node at (1.5, -3.7) {State before `head.next.next = head` in `reverseList(1)` call};
    \node at (1.5, -4.2) {Reversed sublist: `3 -> 2`. `head` is `1`, `head.next` is `2` (tail of sublist).};

    % Make 2 point to 1
    \draw[rev_arrow] (r_n2.east) to[bend left=40] node[midway,below] {`2.next = 1`} (r_n1.west);
    % Make 1 point to null
    \draw[rev_arrow] (r_n1) -- (r_nN);

    \node at (1.5, -5) {Final: 3 -> 2 -> 1 -> null};
\end{tikzpicture}
```

## 2. Reverse First N Nodes of a List
A helper function often used for more complex reversals.
`reverseN(head, n)`: Reverses the first `n` nodes of the list starting at `head` and returns the new head.
- **Base Case:** If `n == 1`, no reversal needed for the first node, return `head`. (The (n+1)th node becomes the successor).
- **Recursive Step:**
    - `last = reverseN(head.next, n - 1)`: Reverse first `n-1` nodes of sublist.
    - `successor_node = head.next.next` (store before modification if needed, tricky part not explicitly in Labuladong's simplified `reverseN`).
    - `head.next.next = head`.
    - `head.next = successor_node_from_original_nth_plus_1` (this needs to be passed back or handled carefully).
Labuladong's `reverseN` implementation simplifies this by returning the `successor` as well, or by connecting `head.next` to the $(N+1)^{th}$ node *after* the recursive call returns. The key is to link the original `head` (which becomes the $N^{th}$ node after partial reversal) to the original $(N+1)^{th}$ node.

## 3. Reverse List Between `m` and `n` (LC92)
[[Interview/Practice/LeetCode/LC92 - Reverse Linked List II|LC92 - Reverse Linked List II]]
- Use recursion. If `m == 1`, it's `reverseN(head, n)`.
- If `m > 1`, `head.next = reverseBetween(head.next, m - 1, n - 1)`. This reduces `m` until the subproblem starts at the first node to be reversed.

## 4. Reverse Nodes in k-Group (LC25)
[[Interview/Practice/LeetCode/LC25 - Reverse Nodes in k-Group|LC25 - Reverse Nodes in k-Group]]
- Iteratively or recursively reverse groups of `k` nodes.
- **Iterative:** Find `k` nodes, reverse them using standard iterative reversal, connect to previous group's tail and next group's head.
- **Recursive `reverseKGroup(head, k)`:**
    - Check if there are at least `k` nodes. If not, return `head`.
    - Find the `k`-th node (`b`). The `(k+1)`-th node is `b.next`.
    - Reverse the `k` nodes from `head` to `b`. `new_head = reverse_first_k_nodes(head, b)`.
    - The original `head` is now the tail of this reversed group.
    - `head.next = reverseKGroup(original_b_next, k)` (Recursively reverse rest).
    - Return `new_head`.

## 总结 (Summary)
- Linked list reversal is a core skill. Iterative approach uses `prev, curr, next_temp`. Recursive approach uses post-order logic.
- Partial reversals (first N, or between m and n) build upon the full reversal logic, often by carefully managing connections to the un-reversed parts of the list.
- k-Group reversal combines these ideas, typically processing the list in chunks of `k`.
- Recursive solutions for list reversal often benefit from helper functions that define clear contracts for what sub-problem they solve (e.g., "reverse first N nodes and return new head AND (N+1)th node").

---
Parent: [[Interview/Concept/Algorithms/Linked List/index|Linked List Concepts]]
Related: [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]]
