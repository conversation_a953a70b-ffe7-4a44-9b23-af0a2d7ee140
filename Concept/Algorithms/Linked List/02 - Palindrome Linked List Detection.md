---
tags: [concept/algorithms, topic/linked_list, type/technique, pattern/palindrome_check, pattern/fast_slow_pointers, pattern/list_reversal, course/labuladong]
aliases: [Palindrome Linked List, 回文链表判断]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷链表算法/如何判断回文链表.md]].
> This note explains the common optimized approach to check if a linked list is a palindrome.

# Linked List: Palindrome Detection

Determining if a singly linked list is a palindrome (reads the same forwards and backwards) requires comparing the first half of the list with the reversed second half.

## 🎯 Core Idea & Algorithm
The standard $O(N)$ time and $O(1)$ space (excluding recursion stack for reversal if recursive) approach involves:
1.  **Find the Middle Node:** Use the [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|fast and slow pointer technique]] to find the middle of the linked list.
    - If the list has an odd number of nodes, `slow` will point to the exact middle.
    - If even, `slow` points to the start of the second half (or the first of two middle nodes, depending on specific implementation).
2.  **Reverse the Second Half:** Reverse the portion of the list starting from `slow.next` (or `slow` itself if adjusting for odd/even length). See [[Interview/Concept/Algorithms/Linked List/01 - Linked List Reversal Techniques|Linked List Reversal Techniques]].
3.  **Compare Halves:** Iterate with one pointer from `head` (start of first half) and another from the new head of the reversed second half. Compare their values node by node.
    - If any mismatch, it's not a palindrome.
    - If they match until one half is exhausted, it's a palindrome.
4.  **(Optional) Restore List:** If the original list structure must be preserved, reverse the second half again to link it back. This step is crucial if modifications are not allowed permanently.

**Labuladong's Visualization Analogy for Palindrome Strings:**
`![](/algo/images/palindrome-linkedlist/1.png)` (Shows left/right pointers for string palindrome)
`![](/algo/images/palindrome-linkedlist/2.png)` (Extends this to linked list idea)

## 🛠️ Step-by-Step Implementation Details

```python
# class ListNode:
#     def __init__(self, val=0, next=None): self.val = val; self.next = next

class SolutionPalindromeLL:
    def isPalindrome(self, head: ListNode) -> bool:
        if not head or not head.next:
            return True

        # 1. Find the middle of the list (slow will point to middle or first of two middles)
        slow, fast = head, head
        while fast.next and fast.next.next: # Ensures fast stops correctly for odd/even
            slow = slow.next
            fast = fast.next.next

        # `slow` is now at the end of the first half.
        # `slow.next` is the start of the second half.

        # 2. Reverse the second half of the list
        head_second_half_reversed = self._reverse_list(slow.next)
        slow.next = None # Disconnect first half from second for comparison (optional but clean)

        # 3. Compare the first half with the reversed second half
        p1 = head
        p2 = head_second_half_reversed
        is_palindrome_result = True
        while p1 and p2: # Or just while p2, as reversed second half might be shorter for odd length
            if p1.val != p2.val:
                is_palindrome_result = False
                break
            p1 = p1.next
            p2 = p2.next

        # 4. (Optional) Restore the list by reversing the second half again
        # and connecting slow.next back to it.
        # slow.next = self._reverse_list(head_second_half_reversed) # Restore

        return is_palindrome_result

    def _reverse_list(self, node: ListNode) -> ListNode:
        prev = None
        curr = node
        while curr:
            next_temp = curr.next
            curr.next = prev
            prev = curr
            curr = next_temp
        return prev # New head of the reversed list
```

### Detailed Breakdown from Labuladong's article:
- **`middleNode(head)`:** Finds middle (implementation might vary slightly if middle of first half or second half is desired).
  `![](/algo/images/palindrome-linkedlist/3.gif)`
- **`reverse(head)`:** Reverses a list.
- **Main logic:**
  `slow = middleNode(head)`
  `head2 = reverse(slow.next)` (if middle means end of first half)
  `slow.next = None`
  Compare `head` with `head2`.
  `slow.next = reverse(head2)` (Restore)

## Complexity Analysis
- **Time Complexity:** $O(N)$.
    - Finding middle: $O(N)$.
    - Reversing second half: $O(N/2) = O(N)$.
    - Comparing halves: $O(N/2) = O(N)$.
    - (Optional) Restoring list: $O(N/2) = O(N)$.
- **Space Complexity:** $O(1)$ if reversal is iterative. If recursive reversal is used for the second half, then $O(N)$ for recursion stack in worst case (skewed list, though list length here is N/2). The provided solution uses iterative reversal.

## 总结 (Summary)
- Palindrome linked list detection typically involves:
    1. Finding the middle node using fast/slow pointers.
    2. Reversing the second half of the list.
    3. Comparing the first half with the reversed second half.
    4. (Optionally) Restoring the list by re-reversing the second half.
- This achieves $O(N)$ time complexity and $O(1)$ auxiliary space (for iterative reversal).
- This technique combines several fundamental linked list operations.

---
Parent: [[Interview/Concept/Algorithms/Linked List/index|Linked List Concepts]]
Related: [[Interview/Practice/LeetCode/LC234 - Palindrome Linked List|LC234 - Palindrome Linked List]]
