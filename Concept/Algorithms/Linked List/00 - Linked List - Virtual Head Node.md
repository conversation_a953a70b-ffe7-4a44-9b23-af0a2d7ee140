---
tags: [concept/data_structures, topic/linked_list, type/technique, pattern/dummy_node, course/labuladong]
aliases: [Dummy Head Node, Sentinel Node Linked List, 虚拟头节点]
---

> [!NOTE] Source Annotation
> This technique is frequently mentioned by Labuladong, for example, in [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道链表题目.md]] for problems like "Merge Two Sorted Lists" and "Remove Nth Node From End of List".

# Linked List: Virtual Head Node Technique

The Virtual Head Node (also known as a Dummy Head Node or Sentinel Node) is a common technique used in linked list problems to simplify edge cases, particularly those involving modifications at the beginning of the list or constructing a new list.

## 🎯 Core Idea
A virtual head node is an auxiliary node placed at the very beginning of a linked list. It does not store any meaningful data related to the problem but acts as a placeholder.

- **Original List:** `head -> node1 -> node2 -> ...`
- **With Virtual Head:** `dummy_head -> head -> node1 -> node2 -> ...`

```tikz
\begin{tikzpicture}[
    lnode/.style={draw, rectangle, minimum height=0.7cm, minimum width=1cm, font=\sffamily\small},
    dnode/.style={lnode, fill=gray!30, dashed},
    arrow/.style={->, thick}
]
    % Original
    \node[lnode] (h1) at (0,1) {H}; \node[lnode] (n1_1) at (1.5,1) {N1}; \node[lnode] (n1_2) at (3,1) {N2};
    \draw[arrow] (h1) -- (n1_1); \draw[arrow] (n1_1) -- (n1_2); \node at (4,1) {...};
    \node at (-1,1) {Original:};

    % With Dummy
    \node[dnode] (d0) at (0,0) {Dummy};
    \node[lnode] (h0) at (1.5,0) {H}; \node[lnode] (n0_1) at (3,0) {N1}; \node[lnode] (n0_2) at (4.5,0) {N2};
    \draw[arrow] (d0) -- (h0); \draw[arrow] (h0) -- (n0_1); \draw[arrow] (n0_1) -- (n0_2); \node at (5.5,0) {...};
    \node at (-1,0) {With Dummy:};

\end{tikzpicture}
```

## ✨ Advantages

1.  **Simplifies Head Operations:**
    - When inserting or deleting the actual head node of a list, the `head` pointer itself needs to change. This often requires special conditional logic.
    - With a dummy head, the actual head node is `dummy_head.next`. Operations on the "first real node" become operations on `dummy_head.next`, which are handled like operations on any other node (modifying `prev.next`).
    - Example: Deleting the first node. Without dummy: `head = head.next`. With dummy: `dummy.next = dummy.next.next`. The `dummy` pointer itself never changes.

2.  **Uniform Node Handling:**
    - All nodes (including the first actual node) have a preceding node (the dummy head for the first real node, or a real node for subsequent ones). This can eliminate special `if (prev == null)` checks in loops.

3.  **Simplified List Construction:**
    - When building a new list (e.g., in merging two sorted lists), start with a dummy head. Use a `current` pointer initialized to this dummy head. Append new nodes to `current.next` and advance `current = current.next`.
    - The final constructed list is `dummy_head.next`. This avoids needing to handle the "first node insertion" as a special case.

## 🚀 Common Use Cases

-   **[[Interview/Practice/LeetCode/LC21 - Merge Two Sorted Lists|LC21 - Merge Two Sorted Lists]]:** A dummy head is used to start the merged list. `p_merged = dummy_head; while(l1 && l2) { ... p_merged.next = ...; p_merged = p_merged.next; } return dummy_head.next;`
-   **[[Interview/Practice/LeetCode/LC19 - Remove Nth Node From End of List|LC19 - Remove Nth Node From End of List]]:** A dummy head makes it easier to remove the actual head of the list if it's the Nth node from the end. The pointer `p2` (which finds the node *before* the one to delete) can start at `dummy`.
-   **[[Interview/Practice/LeetCode/LC206 - Reverse Linked List|LC206 - Reverse Linked List]] (Iterative):** While not strictly necessary, a dummy node could be imagined as the initial `prev = null`.
-   **[[Interview/Practice/LeetCode/LC86 - Partition List|LC86 - Partition List]]:** Two dummy heads are used to build two separate lists.

## 🛠️ Implementation Detail
Typically, initialize `dummy = ListNode(-1)` (or any arbitrary value, as it's not part of the data). `dummy.next = original_head`. After operations, the new list starts at `dummy.next`.

```python
# class ListNode:
#     def __init__(self, val=0, next=None):
#         self.val = val
#         self.next = next

# Example: Removing head if value is X
def remove_head_if_val_is_x(head, x_val):
    dummy = ListNode(-1, head)
    prev = dummy
    curr = head

    # This loop now handles removing the actual head uniformly
    if curr and curr.val == x_val:
        prev.next = curr.next # 'prev' is dummy, so dummy.next is updated
        # No need to reassign 'head' variable explicitly.

    return dummy.next # The new head of the list
```

## 总结 (Summary)
- A virtual head node (dummy node) is a placeholder node added to the beginning of a linked list.
- It simplifies list manipulations, especially at the head of the list, by ensuring every actual node has a predecessor.
- Useful for constructing new lists iteratively and for deletion operations near the start.
- The final result is typically `dummy_head.next`.

---
Parent: [[Interview/Concept/Data Structures/Linked List/index|Linked List Concepts]]
Related: [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]]
