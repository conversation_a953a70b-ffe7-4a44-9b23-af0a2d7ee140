---
tags: [index, concept/algorithms, topic/linked_list]
aliases: [Linked List Algorithm Concepts, Linked List Techniques]
---

# Linked List Algorithmic Concepts

This section covers core algorithmic techniques and patterns related to linked lists. For basic linked list data structure implementation, see [[Interview/Concept/Data Structures/Linked List/index|Linked List Data Structure]].

## Core Techniques & Patterns:
- [[Interview/Concept/Algorithms/Linked List/00 - Linked List - Virtual Head Node|Virtual Head Node Technique]]
- [[Interview/Concept/Algorithms/Linked List/01 - Linked List Reversal Techniques|Linked List Reversal Techniques]]
  - Iterative Reversal
  - Recursive Reversal
  - Partial Reversal (N nodes, range m-n)
  - K-Group Reversal
- [[Interview/Concept/Algorithms/Linked List/02 - Palindrome Linked List Detection|Palindrome Linked List Detection]]

## Related General Patterns:
- [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]] (Fast/Slow, etc.)

## Visualization
```mermaid
graph TD
    LLAlgo["Linked List Algorithms"] --> VirtualNode["[[Interview/Concept/Algorithms/Linked List/00 - Linked List - Virtual Head Node|Virtual Head Node]]"]
    LLAlgo --> Reversal["[[Interview/Concept/Algorithms/Linked List/01 - Linked List Reversal Techniques|Reversal Techniques]]"]
    LLAlgo --> Palindrome["[[Interview/Concept/Algorithms/Linked List/02 - Palindrome Linked List Detection|Palindrome Detection]]"]

    Reversal --> IterRev["(Iterative Full Reverse)"]
    Reversal --> RecRev["(Recursive Full Reverse)"]
    Reversal --> PartialRev["(Partial Reverse - N, M-N)"]
    Reversal --> KGroupRev["(K-Group Reverse)"]

    LLAlgo --> TwoPtrsLink["(See [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for LL]])"]

    classDef main fill:#e0ffff,stroke:#008b8b,stroke-width:2px;
    class LLAlgo main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
