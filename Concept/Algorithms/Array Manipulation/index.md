---
tags: [index, concept/algorithms, concept/array_manipulation]
aliases: [Array Manipulation Index, Array Techniques]
---

# Array Manipulation Techniques

This section covers various techniques for efficient array manipulation.

## Core Techniques:
- [[Interview/Concept/Algorithms/Array Manipulation/00 - 2D Array Fancy Traversal|2D Array Fancy Traversal]]
- [[Interview/Concept/Algorithms/Array Manipulation/01 - Difference Array Technique|Difference Array Technique]]
- [[Interview/Concept/Algorithms/Array Manipulation/02 - Prefix Sum Array Technique|Prefix Sum Array Technique]]

- [[Interview/Concept/Algorithms/Array Manipulation/Trapping Rain Water Pattern|Trapping Rain Water Pattern]]
## Visualization
```mermaid
graph TD
    ArrayManip["Array Manipulation"] --> Trav2D["[[Interview/Concept/Algorithms/Array Manipulation/00 - 2D Array Fancy Traversal|2D Traversal]]"]
    ArrayManip --> DiffArr["[[Interview/Concept/Algorithms/Array Manipulation/01 - Difference Array Technique|Difference Array]]"]
    ArrayManip --> PrefixSumArr["[[Interview/Concept/Algorithms/Array Manipulation/02 - Prefix Sum Array Technique|Prefix Sum Array]]"]

    classDef main fill:#f0fff0,stroke:#2e8b57,stroke-width:2px;
    class ArrayManip main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
