---
tags: [concept/algorithms, pattern/two_pointers, pattern/dynamic_programming, pattern/array_manipulation, topic/array, course/labuladong]
aliases: [Trapping Rain Water, 接雨水问题思路]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/如何高效解决接雨水问题.md
---

> [!NOTE] Source Annotation
> Content inspired by [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/如何高效解决接雨水问题.md|如何高效解决接雨水问题 by Labuladong]]. This problem is [[Interview/Practice/LeetCode/LC42 - Trapping Rain Water|LC42 - Trapping Rain Water]].

# Trapping Rain Water: Solution Patterns

The "Trapping Rain Water" problem (LC42) asks to calculate how much rainwater can be trapped between bars of varying heights represented by an array. The core insight is that the water trapped at any position `i` depends on the height of the walls to its left and right.

## 💧 Core Insight

For any bar `height[i]`, the amount of water it can trap above it is determined by:
`water_at_i = min(max_height_to_left[i], max_height_to_right[i]) - height[i]`
If this value is negative (i.e., `height[i]` is taller than one of the "walls"), no water is trapped at `i`, so it's 0.

```tikz
\begin{tikzpicture}[
    bar/.style={rectangle, draw, fill=blue!30, minimum width=0.5cm, anchor=south},
    water/.style={rectangle, fill=cyan!50, minimum width=0.5cm, anchor=south},
    label_style/.style={font=\sffamily\small}
]
    % Bars: height = [0,1,0,2,1,0,1,3,2,1,2,1]
    \node[bar, minimum height=0cm] at (0,0) {}; \node[label_style, below=0cm of {(0,0)}] {$h_0=0$};
    \node[bar, minimum height=1cm] at (1,0) {}; \node[label_style, below=0cm of {(1,0)}] {$h_1=1$};
    \node[bar, minimum height=0cm] (bar_i) at (2,0) {}; \node[label_style, below=0cm of {(2,0)}] {$h_2=0$};
    \node[bar, minimum height=2cm] (l_max_for_i) at (3,0) {}; \node[label_style, below=0cm of {(3,0)}] {$h_3=2$};
    \node[bar, minimum height=1cm] at (4,0) {}; \node[label_style, below=0cm of {(4,0)}] {$h_4=1$};
    \node[bar, minimum height=0cm] at (5,0) {}; \node[label_style, below=0cm of {(5,0)}] {$h_5=0$};
    \node[bar, minimum height=1cm] at (6,0) {}; \node[label_style, below=0cm of {(6,0)}] {$h_6=1$};
    \node[bar, minimum height=3cm] (r_max_for_i) at (7,0) {}; \node[label_style, below=0cm of {(7,0)}] {$h_7=3$};
    % ... rest of bars

    % Focus on index i=2 (h_2=0)
    \node[label_style, above=1cm of bar_i] (focus_label) {Focus on $i=2$};
    \draw[->, red, thick, dashed] (focus_label) -- (bar_i.north);

    % L_max for i=2 (height[1]=1 is max left for h[0..1])
    % More accurately, for h[2], max_left includes h[0..1]
    % No, for index i, l_max is max(height[0...i-1]), r_max is max(height[i+1...n-1])
    % Labuladong uses max(height[0...i]) and max(height[i...n-1]) for l_max[i] and r_max[i] respectively.
    % Let's use Labuladong's: l_max[i] is max height on left *including* i.
    % water[i] = min(max_left_of_i, max_right_of_i) - height[i]
    % For i=2 (height=0):
    % True L_max (max of elements to its strict left, for wall): max(height[0], height[1]) = max(0,1) = 1.
    % True R_max (max of elements to its strict right, for wall): max(height[3]...height[end]) = 3.
    % Water level at i=2 = min(1,3) = 1. Water trapped = 1 - 0 = 1.

    % The article's diagram uses l_max as highest bar to left of i (exclusive), and r_max as highest bar to right of i (exclusive)
    % If we take the context of article's diagram for calculation at `height[i]`:
    % l_max_val = max(height[0...i-1])
    % r_max_val = max(height[i+1...n-1])
    % water_level_at_i = min(l_max_val, r_max_val)
    % water_trapped_at_i = max(0, water_level_at_i - height[i])

    % For the diagram in the problem, for index i=2 (height[2]=0):
    % Max bar to its left (up to index 1) is height[1]=1.
    % Max bar to its right (from index 3 onwards) is height[7]=3.
    % Water level at index 2 is min(1,3)=1. Water trapped = 1-0=1.
    \node[water, minimum height=1cm] at (2,0) {};
    \draw[<->, green, thick] (l_max_for_i.north east) ++(0.1, -0.2) -- ++(0.1, -1.8) node[midway, right, label_style]{$L_{max}[i]$ (wall)};
    \draw[<->, green, thick] (r_max_for_i.north west) ++(-0.1, -0.2) -- ++(-0.1, -2.8) node[midway, left, label_style]{$R_{max}[i]$ (wall)};
    \node[label_style, text width=6cm] at (3.5, -2) {Water at $i = \min(L_{max}, R_{max}) - height[i]$ \\ (where $L_{max}$ is max height in $0..i$, $R_{max}$ is max height in $i..N-1$ in one DP approach, or strict left/right for another view)};

\end{tikzpicture}
```

## Solution Approaches

### 1. Brute Force $O(N^2)$
For each bar `i` from `1` to `N-2`:
- Find `max_left = max(height[0...i-1])`.
- Find `max_right = max(height[i+1...N-1])`.
- `water_level = min(max_left, max_right)`.
- If `water_level > height[i]`, add `water_level - height[i]` to total.
Time: $O(N)$ for each `i` to find maxes, total $O(N^2)$. Space: $O(1)$.

### 2. Using Precomputed Maxes (DP-like) $O(N)$
Precompute `left_max[i]` (max height from `0` to `i`) and `right_max[i]` (max height from `i` to `N-1`).
- `left_max[i] = max(left_max[i-1], height[i])`.
- `right_max[i] = max(right_max[i+1], height[i])` (iterate from right to left for this).
Then, for each `i`:
- The actual left wall for `height[i]` is `left_max[i-1]`.
- The actual right wall for `height[i]` is `right_max[i+1]`.
- Or, if `left_max[i]` includes `height[i]`: water at `i` is `min(left_max[i], right_max[i]) - height[i]`. This is Labuladong's formulation.
Labuladong's article shows arrays `l_max` and `r_max`.
```python
# class SolutionLC42_DP:
#     def trap(self, height: list[int]) -> int:
#         n = len(height)
#         if n <= 2: return 0
#         ans = 0
#         l_max = [0] * n
#         r_max = [0] * n

#         l_max[0] = height[0]
#         for i in range(1, n):
#             l_max[i] = max(height[i], l_max[i-1])

#         r_max[n-1] = height[n-1]
#         for i in range(n-2, -1, -1):
#             r_max[i] = max(height[i], r_max[i+1])

#         for i in range(1, n - 1): # Exclude ends as they can't trap water
#             water_level = min(l_max[i], r_max[i]) 
#             # If l_max[i] means max up to AND INCLUDING i from left, this is right.
#             # The water level is limited by the shorter of the two "accumulated maxes" at that point.
#             trapped_water = water_level - height[i]
#             if trapped_water > 0:
#                 ans += trapped_water
#         return ans
```
Time: $O(N)$ for 3 passes. Space: $O(N)$ for `l_max` and `r_max` arrays.

### 3. Two Pointers $O(N)$ Time, $O(1)$ Space (Optimized)
This is the most optimal approach.
- Maintain `left = 0`, `right = n-1`.
- Maintain `l_max_height = 0`, `r_max_height = 0`. (Max heights encountered so far from left and right ends respectively).
- `ans = 0`.
- Loop while `left <= right`:
    - `l_max_height = max(l_max_height, height[left])`
    - `r_max_height = max(r_max_height, height[right])`
    - **If `l_max_height < r_max_height`:**
        - The water level at `left` is determined by `l_max_height` (because `r_max_height` is even taller and won't be the bottleneck).
        - `ans += l_max_height - height[left]`
        - `left += 1`
    - **Else (`l_max_height >= r_max_height`):**
        - The water level at `right` is determined by `r_max_height`.
        - `ans += r_max_height - height[right]`
        - `right -= 1`
Return `ans`.

```python
class SolutionLC42_TwoPointers:
    def trap(self, height: list[int]) -> int:
        n = len(height)
        if n <= 2: return 0

        left, right = 0, n - 1
        l_max, r_max = 0, 0 # Max height seen from left and right so far
        ans = 0

        while left < right : # Stop when pointers meet/cross
            l_max = max(l_max, height[left])
            r_max = max(r_max, height[right])

            if l_max < r_max:
                # Water at 'left' is determined by l_max
                ans += l_max - height[left]
                left += 1
            else:
                # Water at 'right' is determined by r_max
                ans += r_max - height[right]
                right -= 1
        return ans
```
Labuladong's GIF `![](/algo/images/rain-water/2.gif)` shows this two-pointer movement.

## Complexity
- **DP with precomputed maxes:** Time $O(N)$, Space $O(N)$.
- **Two Pointers:** Time $O(N)$, Space $O(1)$.

## 总结 (Summary)
- Trapping rain water at a position `i` depends on `min(max_left_wall, max_right_wall) - height[i]`.
- Naive solution is $O(N^2)$.
- Precomputing max arrays (`l_max`, `r_max`) reduces time to $O(N)$ but uses $O(N)$ space.
- The optimized two-pointer approach achieves $O(N)$ time and $O(1)$ space by cleverly processing from both ends.

---
Parent: [[Interview/Concept/Algorithms/Array Manipulation/index|Array Manipulation]]
Related Problem: [[Interview/Practice/LeetCode/LC42 - Trapping Rain Water|LC42 - Trapping Rain Water]]
Related Concepts: [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]]
