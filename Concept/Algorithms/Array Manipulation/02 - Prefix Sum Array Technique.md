---
tags: [concept/algorithms, concept/array_manipulation, type/technique, pattern/prefix_sum, course/labuladong]
aliases: [Prefix Sum Array, 前缀和数组]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷数组算法/小而美的算法技巧：前缀和数组.md]].
> This note explains the Prefix Sum Array technique for efficient range sum queries.

| [[Interview/Concept/Algorithms/Array Manipulation/01 - Difference Array Technique|Prev: Difference Array]] | [[Interview/Concept/Algorithms/Array Manipulation/index|Back to Array Manipulation Index]] |

# Prefix Sum Array Technique

The Prefix Sum Array (or cumulative sum array) is a technique used to quickly calculate the sum of elements within a given range `[i, j]` of an array. After an $O(N)$ preprocessing step to compute the prefix sum array, any range sum query can be answered in $O(1)$ time. This is highly efficient if the original array is immutable and range sum queries are frequent.

## 💡 Core Idea

1.  **Definition:** Given an array `nums`, its prefix sum array `preSum` is defined such that `preSum[k]` stores the sum of the first `k` elements of `nums`.
    - A common convention (used by Labuladong) is to make `preSum` one element longer than `nums`, with `preSum[0] = 0`.
    - `preSum[i]` then stores `nums[0] + nums[1] + ... + nums[i-1]`.
    - So, `preSum[k] = sum(nums[0...k-1])`.

2.  **Construction:**
    - `preSum[0] = 0`
    - `preSum[i] = preSum[i-1] + nums[i-1]` for `i > 0`.

3.  **Range Sum Query:** To find the sum of `nums[left...right]` (inclusive):
    - `sum(nums[left...right]) = nums[left] + ... + nums[right]`
    - This is equivalent to `(nums[0] + ... + nums[right]) - (nums[0] + ... + nums[left-1])`
    - Using the prefix sum array: `sum(nums[left...right]) = preSum[right+1] - preSum[left]`.

**Visualization (from Labuladong `![](/algo/images/difference/1.jpeg)`):**
`nums = [-2, 0, 3, -5, 2, -1]`
`preSum` construction:
- `preSum[0] = 0`
- `preSum[1] = preSum[0] + nums[0] = 0 + (-2) = -2`
- `preSum[2] = preSum[1] + nums[1] = -2 + 0 = -2`
- `preSum[3] = preSum[2] + nums[2] = -2 + 3 = 1`
- `preSum[4] = preSum[3] + nums[3] = 1 + (-5) = -4`
- `preSum[5] = preSum[4] + nums[4] = -4 + 2 = -2`
- `preSum[6] = preSum[5] + nums[5] = -2 + (-1) = -3`
So, `preSum = [0, -2, -2, 1, -4, -2, -3]`.

Query `sumRange(0, 2)` (sum of `nums[0..2]` = `-2 + 0 + 3 = 1`):
`preSum[2+1] - preSum[0] = preSum[3] - preSum[0] = 1 - 0 = 1`.

Query `sumRange(2, 5)` (sum of `nums[2..5]` = `3 + (-5) + 2 + (-1) = -1`):
`preSum[5+1] - preSum[2] = preSum[6] - preSum[2] = -3 - (-2) = -1`.

## 🛠️ Implementation: 1D Prefix Sum (Labuladong's `NumArray` for LC303)

```python
class NumArray:
    # Prefix sum array
    def __init__(self, nums: list[int]):
        # preSum[0] = 0, to simplify sumRange calculation
        self.preSum = [0] * (len(nums) + 1)
        # Calculate prefix sums
        for i in range(len(nums)):
            self.preSum[i+1] = self.preSum[i] + nums[i]
            # Or, using nums[i-1] convention from article:
            # self.preSum[i] = self.preSum[i-1] + nums[i-1] for i=1 to len(preSum)-1

    # Query sum of closed interval [left, right]
    def sumRange(self, left: int, right: int) -> int:
        # sum(nums[left...right]) = preSum[right+1] - preSum[left]
        return self.preSum[right + 1] - self.preSum[left]

# Example Usage:
# numArray = NumArray([-2, 0, 3, -5, 2, -1])
# print(numArray.sumRange(0, 2)) # Output: 1
# print(numArray.sumRange(2, 5)) # Output: -1
```
Labuladong's visualizer: `div_range-sum-query-immutable`.

## 🧱 2D Prefix Sum (for Matrices)

The concept extends to 2D arrays (matrices) for calculating sum of sub-rectangles.
Problem: [[Interview/Practice/LeetCode/LC304 - Range Sum Query 2D - Immutable|LC304 - Range Sum Query 2D - Immutable]].

- **Definition:** `preSum[r][c]` stores the sum of the rectangle from `matrix[0][0]` to `matrix[r-1][c-1]`.
- **Construction:**
  `preSum[r][c] = matrix[r-1][c-1] + preSum[r-1][c] + preSum[r][c-1] - preSum[r-1][c-1]`.
  This uses the principle of inclusion-exclusion.
  ```tikz
  \begin{tikzpicture}[
      rect_style/.style={draw, minimum width=1cm, minimum height=1cm, font=\sffamily\tiny},
      label_style/.style={font=\sffamily\scriptsize}
  ]
  % Grid for preSum[r-1][c-1], matrix[r-1][c-1], etc.
  \node[rect_style, fill=blue!10] (A) at (0,1) {$preSum[r-1][c-1]$}; % Area A
  \node[rect_style, fill=green!10] (B) at (1,1) {$col_{c-1} \text{ up to } row_{r-2}$}; % Area B (part of preSum[r][c-1] excluding A)
  \node[rect_style, fill=yellow!10] (C) at (0,0) {$row_{r-1} \text{ up to } col_{c-2}$}; % Area C (part of preSum[r-1][c] excluding A)
  \node[rect_style, fill=red!20] (D) at (1,0) {$matrix[r-1][c-1]$}; % Area D

  \node[label_style] at (3,1.5) {$preSum[r-1][c] = A+C$};
  \node[label_style] at (3,0.5) {$preSum[r][c-1] = A+B$};

  \node[label_style, text width=5cm, align=center] at (3,-1) {
    $preSum[r][c] = (A+B+C+D)$ \\
    $= D + (A+C) + (A+B) - A$ \\
    $= matrix[r-1][c-1] + preSum[r-1][c] + preSum[r][c-1] - preSum[r-1][c-1]$
  };
  \end{tikzpicture}
  ```

- **Range Sum Query `sumRegion(row1, col1, row2, col2)`:**
  Sum of rectangle defined by top-left `(row1, col1)` and bottom-right `(row2, col2)`.
  `sum = preSum[row2+1][col2+1] - preSum[row1][col2+1] - preSum[row2+1][col1] + preSum[row1][col1]`.
  This also uses inclusion-exclusion.
  Visual: `![](/algo/images/presum/5.jpeg)` (from Labuladong)

### Implementation: 2D Prefix Sum (`NumMatrix` for LC304)
```python
class NumMatrix:
    def __init__(self, matrix: list[list[int]]):
        if not matrix or not matrix[0]:
            # Handle empty matrix case if necessary, though problem constraints usually ensure non-empty.
            # For this template, let's assume valid matrix.
            self.preSum = None # Or raise error
            return

        rows, cols = len(matrix), len(matrix[0])
        # preSum[r][c] stores sum of rectangle from origin to matrix[r-1][c-1]
        self.preSum = [[0] * (cols + 1) for _ in range(rows + 1)]

        for r in range(1, rows + 1):
            for c in range(1, cols + 1):
                self.preSum[r][c] = matrix[r-1][c-1] + \
                                   self.preSum[r-1][c] + \
                                   self.preSum[r][c-1] - \
                                   self.preSum[r-1][c-1]

    def sumRegion(self, row1: int, col1: int, row2: int, col2: int) -> int:
        if not self.preSum: return 0 # Or handle error

        # Sum of matrix[row1..row2][col1..col2]
        return self.preSum[row2+1][col2+1] - \
               self.preSum[row1][col2+1] - \
               self.preSum[row2+1][col1] + \
               self.preSum[row1][col1]

# Example Usage:
# matrix = [[3,0,1,4,2],[5,6,3,2,1],[1,2,0,1,5],[4,1,0,1,7],[1,0,3,0,5]]
# numMatrix = NumMatrix(matrix)
# print(numMatrix.sumRegion(2,1,4,3)) # Expected: 8
```
Labuladong's visualizer: `div_range-sum-query-2d-immutable`.

## Complexity Analysis
- **1D Prefix Sum:**
    - Constructor: $O(N)$ time, $O(N)$ space.
    - `sumRange`: $O(1)$ time.
- **2D Prefix Sum:**
    - Constructor: $O(R \times C)$ time, $O(R \times C)$ space (where R, C are rows/cols).
    - `sumRegion`: $O(1)$ time.

## Limitations
1.  **Immutable Source Array:** Prefix sum technique assumes the original `nums` array (or `matrix`) does not change after `preSum` is computed. If `nums` changes, `preSum` must be recomputed.
2.  **Operation Must Have Inverse:** To find `sum(nums[left...right])` using `preSum[right+1] - preSum[left]`, subtraction is the inverse of addition. This works for sums, products (with division as inverse, careful with zeros). It doesn't work for operations like min/max if only prefix min/max is stored (e.g., `max(A[i..j])` is not `prefix_max[j] ??? prefix_max[i-1]`). For range min/max queries, [[Interview/Concept/Data Structures/Tree/Segment Tree/00 - Segment Tree - Principles and Visualization|Segment Trees]] or Sparse Tables are used.

## 总结 (Summary)
- Prefix Sum Array allows $O(1)$ range sum queries after $O(N)$ (or $O(RC)$ for 2D) preprocessing.
- A common trick is to make the `preSum` array 1-indexed or have `preSum[0]=0` to simplify range query formulas.
- Extends to 2D for sum of sub-rectangles.
- Ideal for scenarios with frequent range sum queries on a static array.
- Complements [[Interview/Concept/Algorithms/Array Manipulation/01 - Difference Array Technique|Difference Array]], which is for fast range updates.

---
| [[Interview/Concept/Algorithms/Array Manipulation/01 - Difference Array Technique|Prev: Difference Array]] | [[Interview/Concept/Algorithms/Array Manipulation/index|Back to Array Manipulation Index]] |

