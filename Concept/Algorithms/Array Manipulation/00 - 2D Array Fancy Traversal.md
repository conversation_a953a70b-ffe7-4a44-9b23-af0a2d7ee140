---
tags: [concept/algorithms, topic/array, topic/matrix, pattern/matrix_traversal]
aliases: [2D Array Traversal, Matrix Traversal Techniques, 二维数组的花式遍历]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷数组算法/二维数组的花式遍历技巧.md]].
> This note covers techniques for rotating and spirally traversing 2D arrays (matrices).

# 2D Array: Fancy Traversal Techniques

Working with 2D arrays (matrices) often involves specific traversal patterns beyond simple row-by-row or column-by-column iteration. Labuladong highlights two common "fancy" techniques: matrix rotation and spiral traversal.

## I.順/逆時針旋轉矩陣 (Clockwise/Counter-Clockwise Matrix Rotation)

Rotating an $N \times N$ matrix by 90 degrees in-place is a common problem ([[Interview/Practice/LeetCode/LC48 - Rotate Image|LC48 - Rotate Image]]).

**Core Idea for 90-degree Clockwise Rotation:**
The trick is to decompose the rotation into two simpler steps:
1.  **Transpose the matrix:** Swap `matrix[i][j]` with `matrix[j][i]`. This flips the matrix along its main diagonal.
2.  **Reverse each row:** For each row, reverse its elements.

**Example:** `matrix = [[1,2,3],[4,5,6],[7,8,9]]`

1.  **Transpose:**
    ```
    1 4 7
    2 5 8
    3 6 9
    ```
2.  **Reverse each row:**
    ```
    7 4 1  (Row 1 reversed)
    8 5 2  (Row 2 reversed)
    9 6 3  (Row 3 reversed)
    ```
    This is the 90-degree clockwise rotated matrix.

**Visualization (from Labuladong):**
`![](/algo/images/2d-array/2.gif)` (Shows transpose)
`![](/algo/images/2d-array/3.gif)` (Shows row reversal)

**For 90-degree Counter-Clockwise Rotation:**
1.  **Transpose the matrix.**
2.  **Reverse each column.** (Alternatively: Reverse each row, then transpose. Or, transpose along anti-diagonal then reverse rows/cols).
    A simpler two-step for counter-clockwise:
    1. Reverse each row.
    2. Transpose the matrix.
    OR
    1. Reverse each column. (Harder to do in place if thinking row-major)
    2. Transpose the matrix.
    OR (often easiest)
    1. Transpose along the *anti-diagonal* (swap `matrix[i][j]` with `matrix[N-1-j][N-1-i]`).
    2. Reverse each row.
    OR (even simpler analogy to clockwise)
    1. Reverse columns (flip horizontally).
    2. Transpose matrix.

Let's stick to Labuladong's likely implication which builds on the clockwise method:
Clockwise: Transpose $\rightarrow$ Reverse Rows
Counter-Clockwise: Transpose $\rightarrow$ Reverse Columns (conceptually)
Or, more practically for implementation:
Counter-Clockwise: Reverse Rows $\rightarrow$ Transpose
   Example: `[[1,2,3],[4,5,6],[7,8,9]]`
   1. Reverse Rows: `[[3,2,1],[6,5,4],[9,8,7]]`
   2. Transpose: `[[3,6,9],[2,5,8],[1,4,7]]` (This is counter-clockwise)

Labuladong also mentions that string reversal ([[Interview/Practice/LeetCode/LC151 - Reverse Words in a String|LC151]]) and list rotation ([[Interview/Practice/LeetCode/LC61 - Rotate List|LC61]]) share a similar "multi-step reversal" thinking pattern.

## II. 螺旋遍历二维数组 (Spiral Matrix Traversal)

Traversing a matrix in a spiral pattern is another common task ([[Interview/Practice/LeetCode/LC54 - Spiral Matrix|LC54]], [[Interview/Practice/LeetCode/LC59 - Spiral Matrix II|LC59]]).

**Core Idea:** Simulate the spiral path by defining boundaries (top, bottom, left, right) and iteratively traversing the outermost layer, then shrinking the boundaries inwards.

1.  Initialize boundaries: `top = 0`, `bottom = num_rows - 1`, `left = 0`, `right = num_cols - 1`.
2.  Initialize result list and count of elements to visit (`total_elements = num_rows * num_cols`).
3.  Loop while `len(result) < total_elements`:
    a.  **Traverse Right (top row):** From `left` to `right` along `matrix[top]`. Add elements to result. Increment `top`.
    b.  **Traverse Down (rightmost column):** From `top` to `bottom` along `matrix[col=right]`. Add elements. Decrement `right`.
    c.  **Traverse Left (bottom row):** From `right` to `left` (descending) along `matrix[bottom]`. Add elements. Decrement `bottom`. (Check `top <= bottom` before this step if not square).
    d.  **Traverse Up (leftmost column):** From `bottom` to `top` (descending) along `matrix[col=left]`. Add elements. Increment `left`. (Check `left <= right` before this step).
4.  Handle edge cases like single row/column matrices carefully within the boundary checks.

**Visualization (from Labuladong):**
`![](/algo/images/2d-array/4.gif)` (Shows the spiral path and shrinking boundaries)

The implementation needs careful boundary condition checks, especially after shrinking a boundary (e.g., after traversing right and incrementing `top`, check if `top <= bottom` before traversing down).

## 总结 (Summary)
- **Matrix Rotation (90-degree clockwise):** Achieved by transposing the matrix and then reversing each row. Counter-clockwise can be done by reversing rows then transposing.
- **Spiral Traversal:** Simulate the path by maintaining `top, bottom, left, right` boundaries. Traverse one layer at a time (right, down, left, up) and shrink the boundaries.
- These techniques often appear in coding interviews and require careful implementation of loops and boundary conditions.

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]] (Or a new `Interview/Concept/Algorithms/Array Manipulation/index.md`)
Related Problems:
- [[Interview/Practice/LeetCode/LC48 - Rotate Image|LC48 - Rotate Image]]
- [[Interview/Practice/LeetCode/LC54 - Spiral Matrix|LC54 - Spiral Matrix]]
- [[Interview/Practice/LeetCode/LC59 - Spiral Matrix II|LC59 - Spiral Matrix II]]
