---
tags: [concept/algorithms, concept/array_manipulation, type/technique, pattern/difference_array, course/labuladong]
aliases: [Difference Array, 差分数组]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷数组算法/小而美的算法技巧：差分数组.md]].
> This note explains the Difference Array technique for efficient range updates on an array.

| [[Interview/Concept/Algorithms/Array Manipulation/index|Back to Array Manipulation Index]] | [[Interview/Concept/Algorithms/Array Manipulation/02 - Prefix Sum Array Technique|Next: Prefix Sum Array]] |

# Difference Array Technique

The Difference Array is a clever technique used to efficiently perform range update operations on an array. If you need to frequently add (or subtract) a value to all elements within a specific range `[i, j]` of an array, using a difference array can reduce the complexity of each range update to $O(1)$.

## ❓ The Problem with Naive Range Updates
Given an array `nums`, if we need to add `val` to `nums[i...j]`, a naive approach would be:
```python
# for k in range(i, j + 1):
#     nums[k] += val
```
This takes $O(N)$ time for each update in the worst case (if the range covers most of the array). If there are many such updates, the total time can be prohibitive.

## 💡 Core Idea of Difference Array

1.  **Definition:** A difference array `diff` is constructed such that `diff[i] = nums[i] - nums[i-1]` for `i > 0`, and `diff[0] = nums[0]`.
    ```
    nums: [1, 3, 7, 4, 6]
    diff: [1, 2, 4, -3, 2]  (1=1; 2=3-1; 4=7-3; -3=4-7; 2=6-4)
    ```

2.  **Reconstruction:** The original array `nums` can be reconstructed from `diff` using prefix sums:
    `nums[0] = diff[0]`
    `nums[i] = diff[i] + nums[i-1]` for `i > 0`.
    This is equivalent to `nums[i] = prefix_sum(diff, i)`.

3.  **Range Update Magic:** To add `val` to `nums[i...j]`:
    - Add `val` to `diff[i]`. This affects `nums[i]` and all subsequent elements `nums[k]` for `k > i` during reconstruction (because `nums[k]` calculation includes `diff[i]` via the cascading sum).
    - Subtract `val` from `diff[j+1]` (if `j+1` is within bounds). This counteracts the effect of adding `val` to `diff[i]` for all elements `nums[k]` where `k > j`.
    - This pair of operations takes $O(1)$ time.

**Visualizing the Update:**
`![](/algo/images/difference/3.jpeg)` (Source: Labuladong)
- If `diff[i] += val`, then `res[i], res[i+1], ...` all increase by `val`.
- If `diff[j+1] -= val`, then `res[j+1], res[j+2], ...` all decrease by `val`.
- The net effect is that only `res[i...j]` are increased by `val`.

## 🛠️ Implementation: `Difference` Class (Labuladong)

Labuladong provides a helper class for this:
```python
class Difference:
    # Difference array
    def __init__(self, nums: list[int]):
        assert len(nums) > 0
        self.diff = [0] * len(nums)
        # Construct difference array
        self.diff[0] = nums[0]
        for i in range(1, len(nums)):
            self.diff[i] = nums[i] - nums[i-1]

    # Increment elements in closed interval [i, j] by val
    def increment(self, i: int, j: int, val: int) -> None:
        self.diff[i] += val
        if j + 1 < len(self.diff):
            self.diff[j + 1] -= val
        # If j+1 is out of bounds, it means the increment affects all elements from i to the end.
        # No counteraction is needed beyond the array.

    # Return the result array after all increments
    def result(self) -> list[int]:
        res = [0] * len(self.diff)
        # Reconstruct result array from difference array
        res[0] = self.diff[0]
        for i in range(1, len(self.diff)):
            res[i] = res[i-1] + self.diff[i]
        return res

# Example Usage:
# nums = [0, 0, 0, 0, 0] # Initial array (e.g., for LC370 Range Addition)
# df = Difference(nums)
# df.increment(1, 3, 2)  # Add 2 to nums[1..3]
# # diff becomes: [0, 2, 0, 0, -2] if len=5. For nums=[0,0,0,0,0], diff=[0,0,0,0,0] initially.
# # after increment(1,3,2) on nums=[0,0,0,0,0]:
# # Initial diff from [0,0,0,0,0] is [0,0,0,0,0]
# # diff[1]+=2 -> [0,2,0,0,0]
# # diff[3+1]-=2 -> diff[4]-=2 -> [0,2,0,0,-2]
# final_nums = df.result() # [0, 2, 2, 2, 0]
```
Labuladong's visualizer `div_diff-array-example` for an array `[8,2,6,3,1]` and `increment(1,3,3)`:
Initial `nums = [8,2,6,3,1]`
`diff = [8, -6, 4, -3, -2]`
`increment(1,3,3)`:
- `diff[1] += 3`: `diff[1]` becomes `-6 + 3 = -3`. `diff = [8, -3, 4, -3, -2]`.
- `diff[3+1] -= 3`: `diff[4]` becomes `-2 - 3 = -5`. `diff = [8, -3, 4, -3, -5]`.
Resulting `nums`:
- `res[0] = 8`
- `res[1] = 8 + (-3) = 5`
- `res[2] = 5 + 4 = 9`
- `res[3] = 9 + (-3) = 6`
- `res[4] = 6 + (-5) = 1`
So, `nums` becomes `[8, 5, 9, 6, 1]`.
Original elements `nums[1..3]` were `[2,6,3]`. After adding 3: `[5,9,6]`. This matches.

##Complexity Analysis
- **Constructor `__init__`**: $O(N)$ to build the `diff` array.
- **`increment(i, j, val)`**: $O(1)$.
- **`result()`**: $O(N)$ to reconstruct the `nums` array.

If there are $M$ range updates and we need the final array:
- Naive: $M \times O(N) = O(MN)$.
- Difference Array: $O(N)$ (initial construction) + $M \times O(1)$ (updates) + $O(N)$ (final reconstruction) = $O(N+M)$.
This is a significant improvement if $M$ is large.

## 🚀 Applications

- **[[Interview/Practice/LeetCode/LC370 - Range Addition|LC370 - Range Addition]] (🔒):** Direct application.
- **[[Interview/Practice/LeetCode/LC1109 - Corporate Flight Bookings|LC1109 - Corporate Flight Bookings]]:** Problem describes flight bookings which are range updates on seats for flight numbers.
- **[[Interview/Practice/LeetCode/LC1094 - Car Pooling|LC1094 - Car Pooling]]:** Passenger trips define ranges (stations) and capacity changes (value). After all "updates" (passengers getting on/off), check if any station's passenger count exceeds capacity.

## ⚖️ Comparison with Prefix Sum
- **[[Interview/Concept/Algorithms/Array Manipulation/02 - Prefix Sum Array Technique|Prefix Sum Array]]**: Optimizes range *query* (sum) to $O(1)$ after $O(N)$ preprocessing, assuming array is immutable.
- **Difference Array**: Optimizes range *update* (add/subtract) to $O(1)$, with $O(N)$ for initial setup and final reconstruction.

They address opposite problems: prefix sums for fast queries on static arrays, difference arrays for fast updates with a final query.

## 拓展延伸 (Further Extensions - Labuladong)
Labuladong mentions that for problems requiring both frequent range updates AND frequent range queries, more advanced data structures like [[Interview/Concept/Data Structures/Tree/Segment Tree/00 - Segment Tree - Principles and Visualization|Segment Trees]] or Binary Indexed Trees (BITs) are needed.

## 总结 (Summary)
- The Difference Array technique allows for $O(1)$ updates to a range `[i, j]` by modifying only `diff[i]` and `diff[j+1]`.
- The original array can be reconstructed from the difference array in $O(N)$ time.
- It's highly effective when there are many range updates and only a final view of the array is needed, or when properties of the final array (like max value in Car Pooling) are required.
- It's a complementary technique to Prefix Sums.

---
| [[Interview/Concept/Algorithms/Array Manipulation/index|Back to Array Manipulation Index]] | [[Interview/Concept/Algorithms/Array Manipulation/02 - Prefix Sum Array Technique|Next: Prefix Sum Array]] |

