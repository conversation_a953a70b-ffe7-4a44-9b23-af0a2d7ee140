---
tags: [concept/algorithms, pattern/two_pointers, pattern/n_sum, topic/array, pattern/recursion]
aliases: [N-Sum Problem, K-Sum Problem, N数之和]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷数组算法/一个方法团灭 nSum 问题.md]].
> This note details a general framework for solving N-Sum problems, typically by reducing them to a 2-Sum base case.

# N-Sum Problem: General Framework

The N-Sum problem asks to find `N` numbers in a given array `nums` that sum up to a specific `target` value. Variations include returning the numbers themselves, their indices, or all unique combinations. Labuladong proposes a general recursive framework that relies on sorting the input array and using two pointers for the 2-Sum base case.

## Core Idea: Sort + Recursive Reduction + Two Pointers

1.  **Sort the Input Array:** Sorting `nums` is crucial. It allows:
    - Efficiently finding pairs using two pointers (for 2-Sum).
    - Skipping duplicate numbers to avoid duplicate results in the final N-tuples.
2.  **Recursive `nSumTarget` Function:**
    - Define a helper function, say `nSumTarget(nums, n, start_index, target)`, which aims to find `n` numbers in `nums` (starting from `start_index`) that sum to `target`.
    - **Base Case (n=2):** If `n == 2`, reduce to the 2-Sum problem. Use two pointers (`lo` and `hi`) on the subarray `nums[start_index...]` to find pairs summing to `target`.
        - Handle duplicates carefully if required by the problem (e.g., for 3Sum, 4Sum problems on LeetCode that ask for unique quadruplets/triplets).
    - **Recursive Step (n > 2):**
        - Iterate through `nums` from `start_index`. For each `nums[i]`:
            - Recursively call `nSumTarget(nums, n - 1, i + 1, target - nums[i])` to find `n-1` numbers in the rest of the array that sum to the adjusted target.
            - If the recursive call returns valid (n-1)-tuples, prepend `nums[i]` to each of them to form n-tuples.
            - **Skip Duplicates:** To avoid generating duplicate n-tuples due to identical `nums[i]` values, if `nums[i] == nums[i-1]` (and `i > start_index`), skip the current `nums[i]`.

## 2-Sum Base Case (with Two Pointers for Sorted Array)

This is the foundation for the N-Sum framework. Given a sorted array segment `nums[start_index...]` and a `target`, find pairs that sum to `target`.

```python
# Conceptual twoSumTarget for the base case n=2
# (Adapted from Labuladong's nSum article structure)
def twoSumTarget_sorted(nums: list[int], start_index: int, target: int) -> list[list[int]]:
    lo, hi = start_index, len(nums) - 1
    results = []
    while lo < hi:
        current_sum = nums[lo] + nums[hi]
        left_val, right_val = nums[lo], nums[hi] # Store for adding to results

        if current_sum < target:
            while lo < hi and nums[lo] == left_val: # Skip duplicates for lo
                lo += 1
        elif current_sum > target:
            while lo < hi and nums[hi] == right_val: # Skip duplicates for hi
                hi -= 1
        else: # current_sum == target
            results.append([left_val, right_val])
            # Skip duplicates for both lo and hi to find next unique pair
            while lo < hi and nums[lo] == left_val: lo += 1
            while lo < hi and nums[hi] == right_val: hi -= 1

    return results
```
**Note:** The duplicate skipping logic (`while lo < hi and nums[lo] == left_val: lo += 1`) is crucial for problems like 3Sum/4Sum that require unique tuples. If any pair is fine, this can be simplified to just `lo += 1` or `hi -= 1`.

## General `nSumTarget` Recursive Function (Python Pseudocode)

```python
# class Solution:
#     def nSumTarget(self, nums: list[int], n: int, start: int, target: int) -> list[list[int]]:
#         arr_len = len(nums)
#         results = []

#         # Base case: n < 2 or array too short
#         if n < 2 or arr_len < n:
#             return results

#         # Base case: 2-Sum problem
#         if n == 2:
#             lo, hi = start, arr_len - 1
#             while lo < hi:
#                 current_sum = nums[lo] + nums[hi]
#                 left_val, right_val = nums[lo], nums[hi]
#                 if current_sum < target:
#                     while lo < hi and nums[lo] == left_val: lo += 1
#                 elif current_sum > target:
#                     while lo < hi and nums[hi] == right_val: hi -= 1
#                 else: # Found a pair
#                     results.append([left_val, right_val])
#                     while lo < hi and nums[lo] == left_val: lo += 1
#                     while lo < hi and nums[hi] == right_val: hi -= 1
#             return results

#         # Recursive case: n > 2
#         for i in range(start, arr_len):
#             # Skip duplicate numbers for the first element of the n-tuple
#             if i > start and nums[i] == nums[i-1]:
#                 continue

#             # Recursively find (n-1)Sum for the remaining part
#             sub_results = self.nSumTarget(nums, n - 1, i + 1, target - nums[i])

#             for sub_res_tuple in sub_results:
#                 results.append([nums[i]] + sub_res_tuple)

#         return results

#     # Main entry point for problems like 4Sum (LC18)
#     def fourSum(self, nums: list[int], target: int) -> list[list[int]]:
#         nums.sort()
#         return self.nSumTarget(nums, 4, 0, target)
```
Labuladong's article `div_n-sum` provides a visualizer for this recursive process.

## Pruning Optimizations (Early Exit)
In the recursive step for `nSumTarget(nums, n, start, target)`:
- If `nums[start] * n > target` (smallest `n` elements already too large), no solution possible.
- If `nums[arr_len - 1] * n < target` (largest `n` elements already too small), no solution possible.
These pruning steps can significantly speed up the process for certain inputs.

## Complexity
- Sorting: $O(N \log N)$.
- The N-Sum recursion: For K-Sum, it's roughly $O(N^{K-1})$ in the worst case without effective pruning or if many solutions exist. E.g., 3Sum is $O(N^2)$ after sort, 4Sum is $O(N^3)$ after sort. The two-pointer part for 2-Sum is $O(N)$.

## 总结 (Summary)
- The N-Sum problem can be solved with a general recursive approach.
- **Sort** the input array first.
- Implement a recursive function `nSumTarget(nums, n, start, target)`.
    - **Base Case (n=2):** Solve using the two-pointer technique on `nums[start...]`. Handle duplicates to ensure unique pairs if needed.
    - **Recursive Step (n>2):** Iterate `nums[i]` (from `start`), and recursively call `nSumTarget` for `n-1` elements with `target - nums[i]`, starting search from `i+1`. Skip duplicate `nums[i]` values.
- This framework is adaptable for 2Sum, 3Sum ([[Interview/Practice/LeetCode/LC15 - 3Sum|LC15]]), 4Sum ([[Interview/Practice/LeetCode/LC18 - 4Sum|LC18]]), etc.

---
Parent: [[Interview/Concept/Algorithms/Two Pointers/index|Two Pointers Index]]
Related Problems:
- [[Interview/Practice/LeetCode/LC1 - Two Sum|LC1 - Two Sum]] (Hashmap is often preferred if original indices needed / unsorted)
- [[Interview/Practice/LeetCode/LC167 - Two Sum II - Input Array Is Sorted|LC167 - Two Sum II]] (Direct 2-Sum on sorted array)
- [[Interview/Practice/LeetCode/LC15 - 3Sum|LC15 - 3Sum]]
- [[Interview/Practice/LeetCode/LC18 - 4Sum|LC18 - 4Sum]]
