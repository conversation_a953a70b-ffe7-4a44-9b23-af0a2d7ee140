---
tags: [index, concept/algorithms, pattern/two_pointers]
aliases: [Two Pointers Index, Two Pointer Algorithms]
---

# Two Pointer Techniques

This section covers the "Two Pointers" algorithmic pattern, a versatile technique for solving problems involving arrays and linked lists by efficiently managing and moving two pointers (or indices) through the data structure.

## Core Concepts & Types:
- [[Interview/Concept/Algorithms/Two Pointers/00 - Two Pointers - Introduction|Two Pointers - Introduction and Core Ideas]]
  - Fast and Slow Pointers
  - Left and Right Pointers

## Applications by Data Structure:
- **Arrays:**
  - [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]]
    - In-place Modification (e.g., Remove Duplicates [[Interview/Practice/LeetCode/LC26 - Remove Duplicates from Sorted Array|LC26]], Move Zeroes [[Interview/Practice/LeetCode/LC283 - Move Zeroes|LC283]])
    - Searching in Sorted Arrays (e.g., Two Sum II [[Interview/Practice/LeetCode/LC167 - Two Sum II - Input Array Is Sorted|LC167]])
    - Reversal ([[Interview/Practice/LeetCode/LC344 - Reverse String|LC344]])
    - Palindrome Finding ([[Interview/Practice/LeetCode/LC5 - Longest Palindromic Substring|LC5]])
- **Linked Lists:**
  - [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]]
    - Cycle Detection ([[Interview/Practice/LeetCode/LC141 - Linked List Cycle|LC141]], [[Interview/Practice/LeetCode/LC142 - Linked List Cycle II|LC142]])
    - Finding Middle Element ([[Interview/Practice/LeetCode/LC876 - Middle of the Linked List|LC876]])
    - Finding Nth Node from End ([[Interview/Practice/LeetCode/LC19 - Remove Nth Node From End of List|LC19]])
    - Merging/Partitioning ([[Interview/Practice/LeetCode/LC21 - Merge Two Sorted Lists|LC21]], [[Interview/Practice/LeetCode/LC86 - Partition List|LC86]])
    - Intersection of Lists ([[Interview/Practice/LeetCode/LC160 - Intersection of Two Linked Lists|LC160]])

## Visualization
```mermaid
graph TD
    TPConcept["Two Pointers"] --> Intro["[[Interview/Concept/Algorithms/Two Pointers/00 - Two Pointers - Introduction|Introduction]]"]
    Intro --> FastSlow["Fast & Slow Pointers"]
    Intro --> LeftRight["Left & Right Pointers"]

    TPConcept --> Applications["Applications"]
    Applications --> ForArrays["[[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|For Arrays]]"]
    ForArrays --> LC167["[[Interview/Practice/LeetCode/LC167 - Two Sum II - Input Array Is Sorted|LC167 - Two Sum II]] (Example)"]
    
    Applications --> ForLists["[[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|For Linked Lists]]"]
    ForLists --> LC141["[[Interview/Practice/LeetCode/LC141 - Linked List Cycle|LC141 - Cycle Detection]] (Example)"]

    classDef main fill:#e6fff2,stroke:#00994d,stroke-width:2px;
    class TPConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
