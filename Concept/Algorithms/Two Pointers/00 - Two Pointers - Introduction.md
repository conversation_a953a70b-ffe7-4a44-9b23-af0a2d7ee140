---
tags: [concept/algorithms, pattern/two_pointers, type/introduction, type/framework]
aliases: [Two Pointer Technique, 双指针技巧]
---

> [!NOTE] Source Annotation
> Content adapted from discussions in [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道数组题目.md]] and [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道链表题目.md]].

# Two Pointers: Introduction and Core Ideas

The **Two Pointers** technique is a common algorithmic pattern used primarily with arrays and linked lists. It involves using two integer indices (or pointers for linked lists) that traverse the data structure in a coordinated way to solve problems efficiently, often reducing time complexity compared to naive approaches.

## 🎯 Core Idea
Instead of using a single pointer to iterate, two pointers are employed to manage different parts of the data structure or to compare elements from different positions simultaneously. Labuladong emphasizes that many problems involving arrays or linked lists can be elegantly solved by identifying the correct two-pointer strategy.

## Types of Two Pointer Techniques

Labuladong categorizes two-pointer techniques mainly into:

1.  **Fast and Slow Pointers (快慢指针):**
    -   Both pointers typically start at or near the beginning of the data structure and move in the **same direction**, but at different speeds.
    -   **Common Uses:**
        -   Detecting cycles in linked lists ([[Interview/Practice/LeetCode/LC141 - Linked List Cycle|LC141]]).
        -   Finding the middle of a linked list ([[Interview/Practice/LeetCode/LC876 - Middle of the Linked List|LC876]]).
        -   In-place modification of arrays, like removing duplicates from a sorted array ([[Interview/Practice/LeetCode/LC26 - Remove Duplicates from Sorted Array|LC26]]) or moving zeros ([[Interview/Practice/LeetCode/LC283 - Move Zeroes|LC283]]). In these array cases, one pointer (`slow`) tracks the position for valid elements, while the other (`fast`) scouts ahead.
        -   [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding window]] problems are considered by Labuladong as a type of fast/slow pointer application where `left` is the slow pointer and `right` is the fast pointer defining the window.

2.  **Left and Right Pointers (左右指针):**
    -   One pointer starts at the beginning of the data structure (e.g., index 0 for an array), and the other starts at the end (e.g., index `n-1`).
    -   They typically move **towards each other** (相向而行) or, in some cases (like finding the longest palindromic substring), expand **outwards from a center** (相背而行).
    -   **Common Uses:**
        -   Searching for pairs in a sorted array (e.g., Two Sum II - [[Interview/Practice/LeetCode/LC167 - Two Sum II - Input Array Is Sorted|LC167]]).
        -   Reversing an array or string in-place ([[Interview/Practice/LeetCode/LC344 - Reverse String|LC344]]).
        -   [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary search]] is conceptually related, as it narrows down a range defined by left and right boundaries.
        -   Finding palindromes by checking symmetry from ends inwards or expanding from center outwards ([[Interview/Practice/LeetCode/LC5 - Longest Palindromic Substring|LC5]]).

```mermaid
graph TD
    TP["Two Pointer Techniques"] --> FSP["Fast & Slow Pointers (Same Direction)"];
    TP --> LRP["Left & Right Pointers (Opposite/Expanding)"];

    FSP --> App1FSP["Cycle Detection (Lists)"];
    FSP --> App2FSP["Middle of List"];
    FSP --> App3FSP["In-place Array Modification (e.g., Remove Duplicates, Move Zeros)"];
    FSP --> App4FSP["[[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window]]"];

    LRP --> App1LRP["Pair Sum in Sorted Array"];
    LRP --> App2LRP["Reverse Array/String"];
    LRP --> App3LRP["Palindrome Checking/Finding"];
    LRP --> App4LRP["[[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary Search]] (Conceptual)"];

    classDef category fill:#e6f2ff,stroke:#aaccff,stroke-width:2px;
    class TP category;
```

## ✨ Advantages
- **Efficiency:** Often reduces time complexity from $O(N^2)$ (e.g., nested loops for pair finding) to $O(N)$ (single pass with two pointers).
- **Space Efficiency:** Many two-pointer algorithms operate in-place, requiring $O(1)$ auxiliary space.

## 🤔 When to Consider Two Pointers
- **Sorted Arrays/Lists:** Problems involving sorted data are prime candidates for left/right pointers (e.g., finding pairs, searching).
- **Sequence Problems:** When needing to compare or process elements at different positions within a sequence.
- **In-place Modifications:** For tasks like reversing, partitioning, or removing elements without using extra space.
- **Finding Subsequences/Subarrays with Properties:** Sliding window, which often uses two pointers to define the window, is a common pattern.
- **Linked List Manipulations:** Fast/slow pointers are essential for cycle detection, finding middle, Nth from end, etc.

## 总结 (Summary)
- The Two Pointers technique uses two indices/pointers to traverse data structures (arrays, linked lists) efficiently.
- **Fast/Slow Pointers:** Move in the same direction at different speeds. Useful for cycle detection, middle element, some in-place array modifications, and sliding windows.
- **Left/Right Pointers:** Move towards each other or expand outwards. Useful for sorted array searches, reversals, and palindrome checks.
- This pattern often leads to $O(N)$ time and $O(1)$ space solutions.

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
Next:
- [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]]
- [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]]
