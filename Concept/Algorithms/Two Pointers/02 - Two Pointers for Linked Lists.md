---
tags: [concept/algorithms, pattern/two_pointers, topic/linked_list, type/framework, course/labuladong]
aliases: [Linked List Two Pointers, 双指针链表技巧, Two Pointer Techniques for Linked Lists]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道链表题目.md]].
> This note summarizes common two-pointer techniques for solving linked list problems.

# Linked List: Two Pointer Techniques

The [[Interview/Concept/Algorithms/Two Pointers/00 - Two Pointers - Introduction|Two Pointers technique]] is highly effective for solving a variety of linked list problems. This note summarizes several common patterns and their applications, primarily focusing on Fast-Slow Pointers and general iterative two-pointer strategies.

## 🔗 Core Patterns for Linked Lists

### 1. Merging Two Sorted Lists
- **Problem:** [[Interview/Practice/LeetCode/LC21 - Merge Two Sorted Lists|LC21 - Merge Two Sorted Lists]]
- **Technique:** Use three pointers: `p1` for list1, `p2` for list2, and `p_current` to build the new merged list. Compare `p1.val` and `p2.val`, append the smaller node to `p_current`, and advance the corresponding pointer.
- **Key Idea:** Similar to the merge step in [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]]. Often utilizes a [[Interview/Concept/Algorithms/Linked List/00 - Linked List - Virtual Head Node|Virtual Head Node]] to simplify list construction.
- **Visualization (Labuladong's "拉拉链" - Zipping Analogy):**
  Imagine two zipper tracks (`l1`, `l2`) and a zipper pull (`p`) that interleaves them based on value.
  ```tikz
  \begin{tikzpicture}[
      lnode/.style={draw, rectangle, minimum height=0.7cm, minimum width=1cm, font=\sffamily\small},
      ptr/.style={font=\sffamily\bfseries\tiny, above=1pt},
      merged_link/.style={->, thick, green!60!black},
      list_link/.style={->, thick}
  ]
  \node[lnode] (l1_1) at (0,2) {1}; \node[lnode] (l1_2) at (1.5,2) {4}; \node[lnode] (l1_3) at (3,2) {5};
  \draw[list_link] (l1_1) -- (l1_2); \draw[list_link] (l1_2) -- (l1_3);
  \node[ptr, red] at (l1_1.north) {p1};
  \node at (-1,2) {L1:};

  \node[lnode] (l2_1) at (0,1) {1}; \node[lnode] (l2_2) at (1.5,1) {3}; \node[lnode] (l2_3) at (3,1) {4};
  \draw[list_link] (l2_1) -- (l2_2); \draw[list_link] (l2_2) -- (l2_3);
  \node[ptr, blue] at (l2_1.north) {p2};
  \node at (-1,1) {L2:};

  \node[lnode, fill=gray!20] (dummy) at (0,0) {dummy}; \node[ptr, green!60!black] at (dummy.north) {p};
  \node[lnode, fill=green!10] (m1) at (1.5,0) {};
  \node[lnode, fill=green!10] (m2) at (3,0) {};
  \node[lnode, fill=green!10] (m3) at (4.5,0) {};
  \node at (-1,0) {Merged:};

  \draw[merged_link, dashed] (dummy) -- (m1); % Conceptual start
  % Example after a few steps: Merged: dummy -> 1 (from l1) -> 1 (from l2) -> 3 (from l2)...
  % p1 points to l1_2(4), p2 points to l2_3(4)
  % p points to the last added node in merged list.
  \end{tikzpicture}
  ```

### 2. Partitioning a List
- **Problem:** [[Interview/Practice/LeetCode/LC86 - Partition List|LC86 - Partition List]]
- **Technique:** Use two dummy head nodes and two current pointers (`p1`, `p2`) to build two separate lists: one for elements less than a pivot `x`, and another for elements greater than or equal to `x`. Finally, connect these two lists.
- **Key Idea:** Decomposes the original list into two, then reassembles. Essential to null-terminate the second list before connecting to avoid cycles if the last node of the original list was part of the "less than x" partition.
- Also see: [[Interview/Concept/Algorithms/Linked List/00 - Linked List - Virtual Head Node|Virtual Head Node]].

### 3. Merging k Sorted Lists
- **Problem:** [[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Sorted Lists]]
- **Technique (Primary):** Use a Min-Heap ([[Interview/Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization|Priority Queue]]) to efficiently find the smallest among the current heads of all `k` lists.
- **Connection to Two Pointers:** The heap helps manage `k` pointers. The underlying "merge" logic if done pairwise would use the two-pointer merging. A divide-and-conquer approach for merging `k` lists also boils down to repeatedly merging two lists.

### 4. Finding Nth Node From End / Middle Node
These use **Fast and Slow Pointers**.
- **Middle Node ([[Interview/Practice/LeetCode/LC876 - Middle of the Linked List|LC876]]):** `slow` moves 1 step, `fast` moves 2 steps. When `fast` reaches end, `slow` is at middle.
- **Nth Node From End ([[Interview/Practice/LeetCode/LC19 - Remove Nth Node From End of List|LC19]]):** `p1` moves `N` steps ahead. Then `p1` and `p2` move together. When `p1` reaches end, `p2` is at Nth from end (or N+1th if deleting).
  ```tikz
  \begin{tikzpicture}[
      node_style/.style={draw, rectangle, minimum size=0.7cm, font=\sffamily\small},
      ptr_label/.style={font=\sffamily\bfseries\tiny, above=0.1cm},
      arrow/.style={->, thick}
  ]
      \node[node_style] (n1) at (0,0) {1};
      \node[node_style] (n2) at (1.5,0) {2};
      \node[node_style] (n3) at (3,0) {3};
      \node[node_style] (n4) at (4.5,0) {4};
      \node[node_style] (n5) at (6,0) {5};
      \node (null) at (7.5,0) {null};
      \draw[arrow] (n1) -- (n2); \draw[arrow] (n2) -- (n3); \draw[arrow] (n3) -- (n4); \draw[arrow] (n4) -- (n5); \draw[arrow] (n5) -- (null);

      \node at (3,-0.8) {Find 2nd from end (N=2):};
      % After p1 moves N steps
      \node[ptr_label, red] at (n3.north) {p1};
      \node[ptr_label, blue] at (n1.north) {p2};
      \node at (3,-1.5) {1. p1 moves N steps.};

      % Both move until p1 hits null
      \begin{scope}[yshift=-2.5cm]
          \node[node_style] (nn1) at (0,0) {1}; \node[node_style] (nn2) at (1.5,0) {2}; \node[node_style] (nn3) at (3,0) {3};
          \node[node_style, fill=yellow!30] (nn4) at (4.5,0) {4}; \node[ptr_label, blue] at (nn4.north) {p2 (Target)};
          \node[node_style] (nn5) at (6,0) {5}; 
          \node (nnull) at (7.5,0) {null}; \node[ptr_label, red] at (nnull.north east) {p1};
          \draw[arrow] (nn1) -- (nn2); \draw[arrow] (nn2) -- (nn3); \draw[arrow] (nn3) -- (nn4); \draw[arrow] (nn4) -- (nn5); \draw[arrow] (nn5) -- (nnull);
          \node at (3,-0.8) {2. p1, p2 move together. When p1 is null, p2 is at Nth from end.};
      \end{scope}
  \end{tikzpicture}
  ```

### 5. Detecting Cycles & Finding Cycle Start
- **Detect Cycle ([[Interview/Practice/LeetCode/LC141 - Linked List Cycle|LC141]]):** `slow` moves 1 step, `fast` moves 2 steps. If they meet, cycle exists.
- **Find Cycle Start ([[Interview/Practice/LeetCode/LC142 - Linked List Cycle II|LC142]]):** After `slow` and `fast` meet, reset `slow` to `head`. Move both `slow` and `fast` one step at a time. Their next meeting point is the cycle start.
  - The mathematical proof relies on distances: `X + Y = kL` where `X` is head-to-cycle-start, `Y` is cycle-start-to-meeting-point, `L` is cycle length.

### 6. Finding Intersection of Two Lists
- **Problem:** [[Interview/Practice/LeetCode/LC160 - Intersection of Two Linked Lists|LC160 - Intersection of Two Linked Lists]]
- **Technique 1 (Length Alignment):** Calculate lengths of `listA` and `listB`. Advance the pointer of the longer list by the difference in lengths. Then, move both pointers one step at a time until they meet at the intersection or reach `null`.
- **Technique 2 (Concatenation Logic):**
  - `p1` traverses `A` then `B`. `p2` traverses `B` then `A`.
  - `p1 = headA, p2 = headB`
  - Loop `while p1 != p2`:
    - `p1 = p1.next if p1 else headB`
    - `p2 = p2.next if p2 else headA`
  - If they meet, it's the intersection. If no intersection, they both become `null` simultaneously.
  - This ensures both pointers travel `len(A) + len(B)` total distance before meeting (if an intersection exists) or both becoming `null`.
  ```tikz
  \begin{tikzpicture}[
      lnode/.style={draw, rectangle, minimum size=0.6cm, font=\sffamily\tiny},
      ptr/.style={font=\sffamily\bfseries\tiny, above=1pt, red},
      path_arrow/.style={->, thick, blue, dashed, shorten >=1pt, shorten <=1pt}
  ]
  % List A: a1 -> a2 -> c1 -> c2 -> c3
  \node[lnode] (a1) at (0,1) {a1}; \node[lnode] (a2) at (1,1) {a2};
  % List B: b1 -> b2 -> b3 -> c1 -> c2 -> c3
  \node[lnode] (b1) at (0,0) {b1}; \node[lnode] (b2) at (1,0) {b2}; \node[lnode] (b3) at (2,0) {b3};
  % Common part
  \node[lnode, fill=green!20] (c1) at (2,1) {c1};
  \node[lnode, fill=green!20] (c2) at (3,1) {c2};
  \node[lnode, fill=green!20] (c3) at (4,1) {c3};

  \draw[->] (a1) -- (a2); \draw[->] (a2) -- (c1);
  \draw[->] (b1) -- (b2); \draw[->] (b2) -- (b3); \draw[->] (b3) -- (c1);
  \draw[->] (c1) -- (c2); \draw[->] (c2) -- (c3);

  \node[ptr] at (a1.north) {p1 starts};
  \node[ptr, blue] at (b1.north) {p2 starts};

  \node[text width=5cm,align=center] at (2.5,-1) {
  Path of p1: a1-a2-c1-c2-c3-null $\rightarrow$ (switches to headB) b1-b2-b3-c1...\\
  Path of p2: b1-b2-b3-c1-c2-c3-null $\rightarrow$ (switches to headA) a1-a2-c1...\\
  Both meet at c1.
  };
  \end{tikzpicture}
  ```

## 总结 (Summary)
- Two-pointer techniques are versatile for linked list problems, offering efficient solutions.
- **Fast-Slow Pointers:** Ideal for finding middle, Nth from end, detecting cycles, and finding cycle start points.
- **General Two/Three Pointers:** Useful for merging, partitioning, and finding intersections.
- Understanding the specific movement logic (e.g., fixed gap, different speeds, list-switching) is key to applying these patterns correctly.
- [[Interview/Concept/Algorithms/Linked List/00 - Linked List - Virtual Head Node|Virtual head nodes]] are often helpful for simplifying edge cases in list modification problems.

---
Parent: [[Interview/Concept/Algorithms/Two Pointers/00 - Two Pointers - Introduction|Two Pointers - Introduction]]
Next: (Could be other Two Pointer applications or specific linked list problem notes)
Related Problems: (See list in "Core Patterns" section above)
