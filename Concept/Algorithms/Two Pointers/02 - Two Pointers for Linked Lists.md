---
tags: [concept/algorithms, pattern/two_pointers, topic/linked_list, type/technique]
aliases: [Linked List Two Pointers, Fast-Slow Pointers in Linked Lists]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道链表题目.md]].
> This note focuses on applying the [[Interview/Concept/Algorithms/Two Pointers/00 - Two Pointers - Introduction|Two Pointers technique]] specifically to linked list problems.

# Two Pointers for Linked Lists

The two-pointer technique is exceptionally useful for solving a variety of linked list problems, often providing elegant and efficient solutions. Unlike arrays, linked lists don't offer $O(1)$ random access, making pointers (references to nodes) the primary way to navigate.

## Common Linked List Two-Pointer Patterns

### 1. Fast and Slow Pointers (快慢指针)
   Both pointers start at or near the head and move in the same direction but at different speeds.

   **a. Finding the Middle of a Linked List (LC876)**
      - See [[Interview/Practice/LeetCode/LC876 - Middle of the Linked List|LC876 Solution]]
      - `slow` pointer moves one step at a time.
      - `fast` pointer moves two steps at a time.
      - When `fast` reaches the end of the list (or `fast.next` is null), `slow` will be at the middle.
      - If even number of nodes, `slow` points to the second middle node.

   **b. Detecting a Cycle in a Linked List (LC141)**
      - See [[Interview/Practice/LeetCode/LC141 - Linked List Cycle|LC141 Solution]]
      - `slow` moves one step, `fast` moves two steps.
      - If there's a cycle, `fast` will eventually "lap" `slow`, and they will meet.
      - If `fast` reaches `null`, there's no cycle.

   **c. Finding the Start of a Cycle in a Linked List (LC142)**
      - See [[Interview/Practice/LeetCode/LC142 - Linked List Cycle II|LC142 Solution]]
      - First, detect the cycle and find the meeting point using fast/slow pointers (as in LC141).
      - Then, reset one pointer (e.g., `slow`) to the `head` of the list.
      - Move both `slow` (from `head`) and `fast` (from meeting point) one step at a time.
      - The point where they meet again is the start of the cycle.

      **Visualization (Cycle Start Logic):**
      Let distance from head to cycle start be $X$.
      Let distance from cycle start to meeting point be $Y$.
      Let length of cycle be $L$.
      When they meet:
      `slow` traveled: $X + Y$
      `fast` traveled: $X + Y + kL$ (for some integer $k \ge 1$, fast went around cycle $k$ times more)
      Since `fast` moves twice as fast: $2 \times \text{dist(slow)} = \text{dist(fast)}$
      $2(X+Y) = X+Y+kL \implies X+Y = kL$
      This means $X = kL - Y$.
      Distance from head to cycle start is $X$.
      Distance from meeting point to cycle start (going around cycle) is $L-Y$.
      If $k=1$, then $X=L-Y$. So, pointer from head and pointer from meeting point will meet at cycle start.
      If $k>1$, $X = (k-1)L + (L-Y)$. Still holds, one pointer effectively makes extra laps.
      ![](/algo/images/linked-two-pointer/2.jpeg)
      *(Source: Labuladong. Shows `slow` reset to head, then both move one step to find cycle start)*

   **d. Finding the Nth Node from the End of a List (LC19)**
      - See [[Interview/Practice/LeetCode/LC19 - Remove Nth Node From End of List|LC19 Solution]]
      - This uses two pointers, `p1` and `p2`, both starting at `head` (or a dummy head for easier deletion).
      - First, move `p1` forward `N` (or `N+1` if using dummy for deletion) steps.
      - Then, move `p1` and `p2` together one step at a time.
      - When `p1` reaches the end of the list, `p2` will be at the (N+1)th node from the end, allowing deletion of the Nth node from the end.
      - This can be seen as maintaining a "gap" of N nodes between `p1` and `p2`.

      **Visualization for Finding Nth from End (e.g., N=2):**
  ```tikz
  \begin{tikzpicture}[
	  node_style/.style={draw, rectangle, minimum size=0.7cm, font=\sffamily\small},
	  ptr_label/.style={font=\sffamily\bfseries\tiny, above=0.1cm},
	  arrow/.style={->, thick}
  ]
	  \node[node_style] (n1) at (0,0) {1};
	  \node[node_style] (n2) at (1.5,0) {2};
	  \node[node_style] (n3) at (3,0) {3};
	  \node[node_style] (n4) at (4.5,0) {4};
	  \node[node_style] (n5) at (6,0) {5};
	  \node (null) at (7.5,0) {null};
	  \draw[arrow] (n1) -- (n2); \draw[arrow] (n2) -- (n3); \draw[arrow] (n3) -- (n4); \draw[arrow] (n4) -- (n5); \draw[arrow] (n5) -- (null);

	  % Initial (p1 moves N steps)
	  \node[ptr_label, red] at (n3.north) {p1};
	  \node[ptr_label, blue] at (n1.north) {p2};
	  \node at (3,-1) {1. p1 moves N=2 steps (assuming N is count, not steps for N+1 node)};
	  
	  % Both move
	  \begin{scope}[yshift=-2cm]
		  \node[node_style] (n1_2) at (0,0) {1}; \node[node_style] (n2_2) at (1.5,0) {2}; \node[node_style] (n3_2) at (3,0) {3};
		  \node[node_style] (n4_2) at (4.5,0) {4}; \node[node_style] (n5_2) at (6,0) {5}; \node (null_2) at (7.5,0) {null};
		  \draw[arrow] (n1_2) -- (n2_2); \draw[arrow] (n2_2) -- (n3_2); \draw[arrow] (n3_2) -- (n4_2); \draw[arrow] (n4_2) -- (n5_2); \draw[arrow] (n5_2) -- (null_2);
		  
		  \node[ptr_label, red] at (n5_2.north) {p1};
		  \node[ptr_label, blue, fill=yellow!30] at (n3_2.north) {p2}; % p2 is at 3rd from end (Nth from end)
		  \node at (3,-1) {2. p1, p2 move together. When p1 at end, p2 at Nth from end.};
	  \end{scope}
  \end{tikzpicture}
  ```

### 2. General Two Pointers (often `p1`, `p2` or similar)
   These might not fit the strict "fast/slow" or "left/right" array mold but use two pointers to achieve a task.

   **a. Merging Two Sorted Lists (LC21)**
      - See [[Interview/Practice/LeetCode/LC21 - Merge Two Sorted Lists|LC21 Solution]]
      - Uses three pointers: `p1` for list1, `p2` for list2, and `p_current` for building the new merged list.
      - Compares `p1.val` and `p2.val`, appends the smaller to `p_current`, and advances the corresponding pointer (`p1` or `p2`).

   **b. Partitioning a List (LC86)**
      - See [[Interview/Practice/LeetCode/LC86 - Partition List|LC86 Solution]]
      - Uses two "dummy head" pointers (`dummy1`, `dummy2`) and two current pointers (`p1`, `p2`) to build two separate lists: one for elements less than `x`, and one for elements greater than or equal to `x`.
      - Then, connects these two lists.

   **c. Merging k Sorted Lists (LC23)**
      - See [[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 Solution]]
      - While the optimal solution uses a min-heap, a divide-and-conquer approach repeatedly uses the "merge two sorted lists" logic, which itself employs two pointers.

   **d. Finding Intersection of Two Linked Lists (LC160)**
      - See [[Interview/Practice/LeetCode/LC160 - Intersection of Two Linked Lists|LC160 Solution]]
      - Pointers `p1` and `p2` start at `headA` and `headB`.
      - When `p1` reaches the end of list A, it's redirected to `headB`.
      - When `p2` reaches the end of list B, it's redirected to `headA`.
      - If they intersect, they will meet at the intersection point after traversing `lenA + lenB_non_intersecting` and `lenB + lenA_non_intersecting` respectively. If no intersection, they both reach `null` simultaneously.
      - Alternative: Calculate lengths `lenA`, `lenB`. Advance the pointer of the longer list by `abs(lenA - lenB)`. Then move both pointers one step at a time until they meet.

## 总结 (Summary)
- **Fast-Slow Pointers for Linked Lists:**
    - Essential for detecting cycles, finding the middle element, and problems reducible to these (like finding Nth from end, cycle start).
    - `fast` usually moves two steps for every one step of `slow`.
- **General Two/Three Pointers:**
    - Used for merging, partitioning, or comparing elements across two lists or within one list while constructing new lists.
- The key is often to manage how the pointers advance relative to each other to maintain a certain invariant or to reach a desired state simultaneously.
- Dummy head nodes are frequently useful in linked list problems to simplify edge cases (e.g., operations involving the head of the list).

---
Parent: [[Interview/Concept/Algorithms/Two Pointers/00 - Two Pointers - Introduction|Two Pointers - Introduction]]
Previous: [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]]
Related Problems:
- [[Interview/Practice/LeetCode/LC141 - Linked List Cycle|LC141]]
- [[Interview/Practice/LeetCode/LC142 - Linked List Cycle II|LC142]]
- [[Interview/Practice/LeetCode/LC160 - Intersection of Two Linked Lists|LC160]]
- [[Interview/Practice/LeetCode/LC19 - Remove Nth Node From End of List|LC19]]
- [[Interview/Practice/LeetCode/LC21 - Merge Two Sorted Lists|LC21]]
- [[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23]]
- [[Interview/Practice/LeetCode/LC86 - Partition List|LC86]]
- [[Interview/Practice/LeetCode/LC876 - Middle of the Linked List|LC876]]
