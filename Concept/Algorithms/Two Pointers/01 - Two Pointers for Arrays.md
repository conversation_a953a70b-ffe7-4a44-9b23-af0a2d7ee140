---
tags: [concept/algorithms, pattern/two_pointers, topic/array, type/technique]
aliases: [Array Two Pointers, Fast-Slow Pointers in Arrays, Left-Right Pointers in Arrays]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道数组题目.md]].
> This note focuses on applying the [[Interview/Concept/Algorithms/Two Pointers/00 - Two Pointers - Introduction|Two Pointers technique]] specifically to array problems.

# Two Pointers for Arrays

The two-pointer technique is highly effective for solving a variety of array-based problems. The pointers are typically array indices. We primarily see two patterns: Fast-Slow Pointers and Left-Right Pointers.

## I. Fast-Slow Pointers (快慢指针)

In this pattern, both pointers usually start at the beginning of the array and move in the same direction, but the `fast` pointer explores ahead while the `slow` pointer maintains a certain condition or marks a boundary.

### A. In-place Modification / "Compacting" an Array

This is common for problems requiring removal of certain elements or duplicates, where the goal is to overwrite the initial part of the array with the desired elements and return the new effective length.

**General Idea:**
- `slow` pointer: Tracks the end of the processed, valid part of the array (i.e., `nums[0...slow]` contains the elements to keep).
- `fast` pointer: Iterates through the array to examine each element.
- When `nums[fast]` is an element to be kept, it's copied to `nums[slow]`, and `slow` is incremented.

**1. Remove Duplicates from Sorted Array (LC26)**
   - See [[Interview/Practice/LeetCode/LC26 - Remove Duplicates from Sorted Array|LC26 Solution]]
   - `slow` points to the last unique element found.
   - `fast` iterates. If `nums[fast] != nums[slow]`, it's a new unique element. Copy `nums[fast]` to `nums[slow+1]` and increment `slow`.

**Visualization for LC26 (`nums = [0,0,1,1,1,2,2]`):**
```tikz
\begin{tikzpicture}[
    arr_cell/.style={draw, rectangle, minimum size=0.7cm, font=\sffamily\small},
    ptr_label/.style={font=\sffamily\bfseries\tiny, below=0.1cm},
    highlight_valid/.style={fill=green!20}
]
    \foreach \val/\idx in {0/0, 0/1, 1/2, 1/3, 1/4, 2/5, 2/6} {
        \node[arr_cell] (c\idx) at (\idx*1, 0) {\val};
    }
    
    % Initial: slow=0, fast=0 (or 1 depending on implementation)
    \node[ptr_label, red] at (c0.south) {s,f};
    \node at (3.5, -1) {Initial: `nums[slow]` is `0`. `nums[0...0]` is valid.};

    % fast=1, nums[1]==nums[0] (0==0). fast++
    \begin{scope}[yshift=-2cm]
        \foreach \val/\idx in {0/0, 0/1, 1/2, 1/3, 1/4, 2/5, 2/6} {
            \node[arr_cell, highlight_valid/.condition=\idx<=0] (c\idx) at (\idx*1, 0) {\val};
        }
        \node[ptr_label, red] at (c0.south) {s};
        \node[ptr_label, blue] at (c1.south) {f};
        \node at (3.5, -1) {`nums[1]==nums[0]`. `fast` moves. `slow` stays.};
    \end{scope}
    
    % fast=2, nums[2]!=nums[0] (1!=0). slow++, nums[slow]=nums[fast]. fast++
    \begin{scope}[yshift=-4cm]
         \node[arr_cell, highlight_valid] (c0_mod) at (0*1, 0) {0};
         \node[arr_cell, highlight_valid] (c1_mod) at (1*1, 0) {1}; % nums[1] becomes 1
         \node[arr_cell] (c2_orig) at (2*1, 0) {1}; % original
         \foreach \val/\idx in {1/3, 1/4, 2/5, 2/6} {
            \node[arr_cell] (c\idx) at (\idx*1, 0) {\val};
        }
        \node[ptr_label, red] at (c1_mod.south) {s};
        \node[ptr_label, blue] at (c2_orig.south) {f};
        \node at (3.5, -1) {`nums[2]!=nums[0]`. `slow++`, `nums[1]=nums[2]`. `fast` moves. Valid: `[0,1]`};
    \end{scope}
    % ... process continues ...
\end{tikzpicture}
```

**2. Remove Element (LC27)**
   - See [[Interview/Practice/LeetCode/LC27 - Remove Element|LC27 Solution]]
   - `slow` tracks the position where the next non-`val` element should be placed.
   - `fast` iterates. If `nums[fast] != val`, copy `nums[fast]` to `nums[slow]` and increment `slow`.

**3. Move Zeroes (LC283)**
   - See [[Interview/Practice/LeetCode/LC283 - Move Zeroes|LC283 Solution]]
   - This can be seen as removing all non-zero elements to the front (like `removeElement(nums, 0)` but keeping non-zeros), then filling the rest with zeros.
   - The `slow` pointer indicates the position for the next non-zero element. `fast` finds non-zero elements. If `nums[fast]` is non-zero, it's swapped with `nums[slow]` (if `slow != fast`) or just placed at `nums[slow]`, and `slow` increments.
   - Alternative from Labuladong: first, `removeElement(nums, 0)` to bring all non-zeros to the front and get the count `p`. Then fill `nums[p:]` with zeros.

### B. Sliding Window
   - This is a major application of fast-slow pointers, where `left` (slow) and `right` (fast) define a "window" over the array.
   - `right` expands the window, `left` shrinks it based on certain conditions.
   - Covered in detail in [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window Framework]].

## II. Left-Right Pointers (左右指针)

One pointer (`left`) starts from the beginning of the array, and the other (`right`) starts from the end. They move towards each other, processing elements or narrowing a search range.

### A. Binary Search
   - While not always explicitly "two pointers moving", binary search operates on a range `[left, right]`, adjusting `left` or `right` to shrink the search space.
   - Covered in [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary Search Core Template]].

### B. Two Sum in a Sorted Array (LC167)
   - See [[Interview/Practice/LeetCode/LC167 - Two Sum II - Input Array Is Sorted|LC167 Solution]]
   - `left` starts at index 0, `right` at `len(nums)-1`.
   - `current_sum = nums[left] + nums[right]`.
     - If `current_sum == target`, found.
     - If `current_sum < target`, `left++` to increase sum.
     - If `current_sum > target`, `right--` to decrease sum.

**Visualization for LC167 (`nums = [2,7,11,15], target = 9`):**
```tikz
\begin{tikzpicture}[
    arr_cell/.style={draw, rectangle, minimum size=0.8cm, font=\sffamily\small},
    ptr_label/.style={font=\sffamily\bfseries\tiny, below=0.1cm},
    highlight_pair/.style={fill=green!20}
]
    \node[arr_cell] (c0) at (0*1.2, 0) {2};
    \node[arr_cell] (c1) at (1*1.2, 0) {7};
    \node[arr_cell] (c2) at (2*1.2, 0) {11};
    \node[arr_cell] (c3) at (3*1.2, 0) {15};
    
    % Initial: left=0, right=3
    \node[ptr_label, red] at (c0.south) {L};
    \node[ptr_label, blue] at (c3.south) {R};
    \node at (1.8*1.2, -1) {L=0, R=3. Sum=2+15=17. $17 > 9 \implies R--$};

    % R moves to 2
    \begin{scope}[yshift=-2cm]
        \node[arr_cell] (c0_2) at (0*1.2, 0) {2};
        \node[arr_cell] (c1_2) at (1*1.2, 0) {7};
        \node[arr_cell] (c2_2) at (2*1.2, 0) {11};
        \node[arr_cell] (c3_2) at (3*1.2, 0) {15};
        \node[ptr_label, red] at (c0_2.south) {L};
        \node[ptr_label, blue] at (c2_2.south) {R};
        \node at (1.8*1.2, -1) {L=0, R=2. Sum=2+11=13. $13 > 9 \implies R--$};
    \end{scope}

    % R moves to 1
    \begin{scope}[yshift=-4cm]
        \node[arr_cell, highlight_pair] (c0_3) at (0*1.2, 0) {2};
        \node[arr_cell, highlight_pair] (c1_3) at (1*1.2, 0) {7};
        \node[arr_cell] (c2_3) at (2*1.2, 0) {11};
        \node[arr_cell] (c3_3) at (3*1.2, 0) {15};
        \node[ptr_label, red] at (c0_3.south) {L};
        \node[ptr_label, blue] at (c1_3.south) {R};
        \node at (1.8*1.2, -1) {L=0, R=1. Sum=2+7=9. Found!};
    \end{scope}
\end{tikzpicture}
```

### C. Reverse Array (LC344)
   - See [[Interview/Practice/LeetCode/LC344 - Reverse String|LC344 Solution]] (applies to char array or general array)
   - `left` starts at 0, `right` at `len(nums)-1`.
   - While `left < right`, swap `nums[left]` and `nums[right]`, then `left++`, `right--`.

### D. Palindrome Checking / Finding

   - **Checking if a string/array is a palindrome:**
     - `left` at start, `right` at end. Move inwards, checking `s[left] == s[right]`.
   - **Finding the Longest Palindromic Substring (LC5):**
     - See [[Interview/Practice/LeetCode/LC5 - Longest Palindromic Substring|LC5 Solution]]
     - This uses a variation where pointers `left` and `right` *expand outwards* from a center (or two centers for even-length palindromes).
     - For each `i` from `0` to `n-1`:
       - Find longest palindrome centered at `i` (odd length): `palindrome(s, i, i)`
       - Find longest palindrome centered at `i, i+1` (even length): `palindrome(s, i, i+1)`
     - The `palindrome(s, l, r)` helper function has `l` moving left and `r` moving right while `s[l] == s[r]`.

## 总结 (Summary)
- **Fast-Slow Pointers for Arrays:**
    - Useful for in-place modifications like removing elements/duplicates or reordering (e.g., move zeros).
    - `slow` pointer typically defines the boundary of the "processed" or "valid" section.
    - `fast` pointer scouts ahead for elements to process or include.
- **Left-Right Pointers for Arrays:**
    - Often used with sorted arrays for searching or finding pairs summing to a target.
    - Common for reversal operations.
    - Can expand outwards from a center for palindrome detection.
- Both patterns provide efficient $O(N)$ time solutions for many array problems, often with $O(1)$ space.

---
Parent: [[Interview/Concept/Algorithms/Two Pointers/00 - Two Pointers - Introduction|Two Pointers - Introduction]]
Next: [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]]
Related Problems:
- [[Interview/Practice/LeetCode/LC26 - Remove Duplicates from Sorted Array|LC26]]
- [[Interview/Practice/LeetCode/LC27 - Remove Element|LC27]]
- [[Interview/Practice/LeetCode/LC283 - Move Zeroes|LC283]]
- [[Interview/Practice/LeetCode/LC167 - Two Sum II - Input Array Is Sorted|LC167]]
- [[Interview/Practice/LeetCode/LC344 - Reverse String|LC344]]
- [[Interview/Practice/LeetCode/LC5 - Longest Palindromic Substring|LC5]]
