---
tags: [concept/algorithms, pattern/two_pointers, topic/array, type/technique]
aliases: [Array Two Pointers, Fast-Slow Pointers in Arrays, Left-Right Pointers in Arrays, 原地修改数组, 数组左右指针]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/双指针技巧秒杀七道数组题目.md]].
> This note focuses on applying the [[Interview/Concept/Algorithms/Two Pointers/00 - Two Pointers - Introduction|Two Pointers technique]] specifically to array problems.

# Two Pointers for Arrays

The two-pointer technique is highly effective for solving a variety of array-based problems. The pointers are typically array indices. Labuladong highlights two main patterns for arrays: Fast-Slow Pointers for in-place modifications and Left-Right Pointers for searching, reversing, or palindrome checks.

## I. Fast-Slow Pointers (快慢指针) for In-place Modification (原地修改)

In this pattern, both pointers usually start at or near the beginning of the array and move in the same direction. The `slow` pointer typically marks the boundary of the processed, valid part of the array, while the `fast` pointer iterates through the array to examine each element. This is particularly useful for problems requiring "原地修改" (in-place modification) of the array.

**General Idea:**
- `slow` pointer: `nums[0...slow]` contains the elements that satisfy the condition and should be kept.
- `fast` pointer: Iterates through the array `nums[0...n-1]` to find elements that should be moved/kept.
- When `nums[fast]` is an element to be kept, it's copied to `nums[slow]` (or `nums[slow+1]` depending on initialization), and `slow` is incremented.

### 1. Remove Duplicates from Sorted Array (LC26)
   - See [[Interview/Practice/LeetCode/LC26 - Remove Duplicates from Sorted Array|LC26 Solution]]
   - `slow` points to the last unique element written to the "valid" part.
   - `fast` iterates. If `nums[fast] != nums[slow]`, it means `nums[fast]` is a new unique element. It's then placed at `nums[slow+1]`, and `slow` is incremented.
   - Labuladong's visualizer `div_remove-duplicates-from-sorted-array` shows this well.

**Labuladong's Python Code for LC26:**
```python
# class Solution:
#    def removeDuplicates(self, nums: list[int]) -> int:
#        if not nums:
#            return 0
#        slow = 0 
#        fast = 1 # In Labuladong's Python, fast starts at 1
#        while fast < len(nums):
#            if nums[fast] != nums[slow]:
#                slow += 1
#                nums[slow] = nums[fast]
#            fast += 1
#        return slow + 1 # New length
```
> Note: Labuladong's Java/C++ versions for LC26 often initialize `slow = 0, fast = 0` and update `nums[slow]` when `nums[fast] != nums[slow]` after incrementing `slow`. The Python version shown in the article for LC26 has `slow=0, fast=1`. Both achieve the same goal. The key is `nums[0...slow]` remains unique.

### 2. Remove Duplicates from Sorted List (LC83)
   - See [[Interview/Practice/LeetCode/LC83 - Remove Duplicates from Sorted List|LC83 Solution]]
   - Though a linked list problem, Labuladong includes it to show the same fast-slow principle.
   - `slow` points to the last unique node. `fast` iterates. If `fast.val != slow.val`, then `slow.next = fast` and `slow = slow.next`.
   - Labuladong's visualizer `div_remove-duplicates-from-sorted-list` illustrates this.

### 3. Remove Element (LC27)
   - See [[Interview/Practice/LeetCode/LC27 - Remove Element|LC27 Solution]]
   - `slow` tracks the position where the next non-`val` element should be placed.
   - `fast` iterates. If `nums[fast] != val`, then `nums[slow] = nums[fast]` and `slow++`.
   - Labuladong's visualizer `div_remove-element`.

### 4. Move Zeroes (LC283)
   - See [[Interview/Practice/LeetCode/LC283 - Move Zeroes|LC283 Solution]]
   - Labuladong's approach: first, use the `removeElement` logic to move all non-zero elements to the front. Let `p` be the index after the last non-zero element. Then, fill `nums[p:]` with zeros.
   - Labuladong's visualizer `div_move-zeroes`.

### 5. Sliding Window
   - Labuladong considers [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window]] as another major category of fast-slow pointers. The `left` pointer is `slow` and the `right` pointer is `fast`. This framework is detailed separately.

## II. Left-Right Pointers (左右指针) for Arrays

One pointer (`left`) starts from the beginning of the array, and the other (`right`) starts from the end. They typically move towards each other (相向而行), or in some cases, expand outwards from a center (相背而行).

### A. Binary Search
   - While not always termed "two pointers" in the same vein, Labuladong mentions it as related because it uses `left` and `right` indices that move towards each other to narrow a search range.
   - Covered in [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary Search Core Template]].

### B. Two Sum in a Sorted Array (LC167)
   - See [[Interview/Practice/LeetCode/LC167 - Two Sum II - Input Array Is Sorted|LC167 Solution]]
   - `left` starts at index 0, `right` at `len(nums)-1`.
   - `current_sum = nums[left] + nums[right]`.
     - If `current_sum == target`, pair found.
     - If `current_sum < target`, `left++` to increase sum.
     - If `current_sum > target`, `right--` to decrease sum.

### C. Reverse Array (LC344)
   - See [[Interview/Practice/LeetCode/LC344 - Reverse String|LC344 Solution]] (applies to char array or general array)
   - `left` at 0, `right` at `len(nums)-1`.
   - While `left < right`, swap `nums[left]` and `nums[right]`, then `left++`, `right--`.

### D. Palindrome Checking / Finding Longest Palindromic Substring (LC5)
   - See [[Interview/Practice/LeetCode/LC5 - Longest Palindromic Substring|LC5 Solution]]
   - For palindrome checking, `left` and `right` start at ends and move inwards comparing characters.
   - For finding the longest palindromic substring (LC5), Labuladong's approach uses two pointers `l` and `r` that **expand outwards** (相背而行) from a center (or two centers for even-length palindromes).
     - For each `i` from `0` to `n-1`:
       - `palindrome(s, i, i)`: find longest odd-length palindrome centered at `i`.
       - `palindrome(s, i, i+1)`: find longest even-length palindrome centered at `i, i+1`.
     - The helper `palindrome(s, l, r)` expands `l` to the left and `r` to the right as long as `s[l] == s[r]` and bounds are valid.
     - Labuladong's visualizer `div_longest-palindromic-substring` shows this outward expansion.

## 总结 (Summary)
- **Fast-Slow Pointers for Arrays:**
    - Effective for in-place modifications like removing elements/duplicates or reordering (e.g., move zeros).
    - `slow` pointer usually marks the end of the valid/processed segment.
    - `fast` pointer explores the array.
- **Left-Right Pointers for Arrays:**
    - Commonly used with sorted arrays for pair-finding or search tasks.
    - Standard for in-place reversal.
    - Can expand outwards from a center for palindrome detection or finding.
- These patterns are fundamental for $O(N)$ time and often $O(1)$ space solutions to array problems.

---
Parent: [[Interview/Concept/Algorithms/Two Pointers/00 - Two Pointers - Introduction|Two Pointers - Introduction]]
Next: [[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|Two Pointers for Linked Lists]]
Related Problems (covered by Labuladong's article for arrays):
- [[Interview/Practice/LeetCode/LC26 - Remove Duplicates from Sorted Array|LC26]]
- [[Interview/Practice/LeetCode/LC27 - Remove Element|LC27]]
- [[Interview/Practice/LeetCode/LC283 - Move Zeroes|LC283]]
- [[Interview/Practice/LeetCode/LC167 - Two Sum II - Input Array Is Sorted|LC167]]
- [[Interview/Practice/LeetCode/LC344 - Reverse String|LC344]]
- [[Interview/Practice/LeetCode/LC5 - Longest Palindromic Substring|LC5]]
