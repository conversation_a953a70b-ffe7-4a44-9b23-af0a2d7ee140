---
tags: [index, concept/algorithms]
aliases: [Algorithms Index, Algorithm Concepts]
---

# Algorithm Concepts

This index covers various algorithm design paradigms, specific algorithms, and analytical techniques relevant to coding interviews.

## Algorithm Categories / Patterns

- [[Interview/Concept/Algorithms/Complexity Analysis/index|Complexity Analysis]]
- [[Interview/Concept/Algorithms/Hashing/index|Hashing Patterns & Techniques]]
- [[Interview/Concept/Algorithms/Recursion/index|Recursion Strategies]]
- [[Interview/Concept/Algorithms/Sliding Window/index|Sliding Window]]
- [[Interview/Concept/Algorithms/00 - Framework Thinking (Labuladong Philosophy)|Framework Thinking (Labuladong Philosophy)]]
- [[Interview/Concept/Algorithms/Greedy Algorithms/index|Greedy Algorithms]]
- [[Interview/Concept/Algorithms/Sorting/index|Sorting Algorithms]]
- `[[Interview/Concept/Algorithms/Sorting/index|Sorting Algorithms]]` (Placeholder)
- `[[Interview/Concept/Algorithms/Searching/index|Searching Algorithms]]` (Placeholder)
- [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal Algorithms]]
- [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]]
- [[Interview/Concept/Algorithms/Divide and Conquer/index|Divide and Conquer]]
- `[[Interview/Concept/Algorithms/Greedy Algorithms/index|Greedy Algorithms]]` (Placeholder)
- `[[Interview/Concept/Algorithms/Bit Manipulation/index|Bit Manipulation Techniques]]` (Placeholder)
- [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal Algorithms]]

- [[Interview/Concept/Algorithms/Backtracking/index|Backtracking]]
- [[Interview/Concept/Algorithms/Two Pointers/index|Two Pointers]]
## Visualization of Algorithm Areas
```mermaid
    FrameworkThink["[[Interview/Concept/Algorithms/00 - Framework Thinking (Labuladong Philosophy)|Framework Thinking]]"]
    Greedy["[[Interview/Concept/Algorithms/Greedy Algorithms/index|Greedy Algorithms]]"]
    SlidingWindow["[[Interview/Concept/Algorithms/Sliding Window/index|Sliding Window]]"]
    Sorting["[[Interview/Concept/Algorithms/Sorting/index|Sorting]]"]
    Algo --> FrameworkThink
    Algo --> Greedy
    Algo --> SlidingWindow
    Algo --> Sorting
```mermaid
Algo --> Backtracking["[[Interview/Concept/Algorithms/Backtracking/index|Backtracking]]"]
    Greedy["[[Interview/Concept/Algorithms/Greedy Algorithms/index|Greedy Algorithms]]"]
    SlidingWindow["[[Interview/Concept/Algorithms/Sliding Window/index|Sliding Window]]"]
    FrameworkThink["[[Interview/Concept/Algorithms/00 - Framework Thinking (Labuladong Philosophy)|Framework Thinking]]"]
    Algo --> TwoPointers["[[Interview/Concept/Algorithms/Two Pointers/index|Two Pointers]]"]
    Algo --> Greedy
    Algo --> SlidingWindow
    Algo --> FrameworkThink
    Backtracking --> BT_Framework["[[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Framework]]"]
    Backtracking --> BT_PermCombSub["[[Interview/Concept/Algorithms/Backtracking/Patterns/01 - Backtracking for Permutations, Combinations, Subsets|Perm/Comb/Subset Patterns]]"]
    TwoPointers --> TP_Intro["[[Interview/Concept/Algorithms/Two Pointers/00 - Two Pointers - Introduction|Introduction]]"]
    TwoPointers --> TP_Arrays["[[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|For Arrays]]"]
    TwoPointers --> TP_Lists["[[Interview/Concept/Algorithms/Two Pointers/02 - Two Pointers for Linked Lists|For Linked Lists]]"]
```mermaid
graph TD
    Algo["Algorithms"] --> Hashing["[[Interview/Concept/Algorithms/Hashing/index|Hashing]]"]
    Algo --> Sorting["(Sorting)"]
    Algo --> Searching["(Searching)"]
    Algo --> GraphT["[[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal]]"]
    Algo --> DP["[[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]]"]
    Algo --> Greedy["(Greedy)"]
    Algo --> BitManip["(Bit Manipulation)"]
    Algo --> Complexity["[[Interview/Concept/Algorithms/Complexity Analysis/index|Complexity Analysis]]"]
    Algo --> Recursion["[[Interview/Concept/Algorithms/Recursion/index|Recursion]]"]
    Algo --> DnC["[[Interview/Concept/Algorithms/Divide and Conquer/index|Divide & Conquer]]"]
    Algo --> TreeT["[[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal]]"]

    Hashing --> TwoSumP["[[Interview/Concept/Algorithms/Hashing/Two Sum Pattern|Two Sum]]"]
    Hashing --> DetectDupP["[[Interview/Concept/Algorithms/Hashing/Detecting Duplicates with Sets|Detect Duplicates]]"]
    Hashing --> FreqCountP["[[Interview/Concept/Algorithms/Hashing/Frequency Counting with Hash Maps|Frequency Count]]"]

    DP --> FibLC["[[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 Fibonacci]]"]
    DP --> CoinLC["[[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 CoinChange]]"]

    classDef category fill:#e0ffff,stroke:#008b8b,stroke-width:2px;
    class Algo, Hashing, Sorting, Searching, TreeT, GraphT, Recursion, DP, Greedy, BitManip, Complexity, DnC category;
```

An important purpose of this folder is to build educaitonal philosophical algorhtm concepts along with robust framework thinking, step by step, motivation, intuition, etc. to help ppl truly learn the algorithm efficiently and effectively.
