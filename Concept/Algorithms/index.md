This index covers various algorithm design paradigms, specific algorithms, and analytical techniques relevant to coding interviews.

An important purpose of this folder is to build educaitonal philosophical algorhtm concepts along with robust framework thinking, step by step, motivation, intuition, etc. to help ppl truly learn the algorithm efficiently and effectively.

## Algorithm Categories / Patterns
- [[Interview/Concept/Algorithms/Complexity Analysis/index|Complexity Analysis]]
- [[Interview/Concept/Algorithms/Hashing/index|Hashing Patterns & Techniques]]
- [[Interview/Concept/Algorithms/Recursion/index|Recursion Strategies]]
- `[[Interview/Concept/Algorithms/Sorting/index|Sorting Algorithms]]` (Placeholder)
- `[[Interview/Concept/Algorithms/Searching/index|Searching Algorithms]]` (Placeholder)
- [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal Algorithms]]
- `[[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]]` (Placeholder)
- [[Interview/Concept/Algorithms/Divide and Conquer/index|Divide and Conquer]]
- [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]]
- `[[Interview/Concept/Algorithms/Greedy Algorithms/index|Greedy Algorithms]]` (Placeholder)
- `[[Interview/Concept/Algorithms/Divide and Conquer/index|Divide and Conquer]]` (Placeholder)
- `[[Interview/Concept/Algorithms/Backtracking/index|Backtracking]]` (Placeholder)
- `[[Interview/Concept/Algorithms/Bit Manipulation/index|Bit Manipulation Techniques]]` (Placeholder)


- [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal Algorithms]]
## Visualization of Algorithm Areas
```mermaid
class Algo, Hashing, Sorting, Searching, TreeT, DP, Greedy, BitManip category;
    Algo --> Recursion["[[Interview/Concept/Algorithms/Recursion/index|Recursion]]"]
    Algo --> DnC["[[Interview/Concept/Algorithms/Divide and Conquer/index|Divide & Conquer]]"]
    Algo --> DP["[[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]]"]
    classDef category fill:#e0ffff,stroke:#008b8b,stroke-width:2px;
    class Algo, Hashing, Sorting, Searching, TreeT, GraphT, Recursion, DP, Greedy, BitManip category;
    DP --> FibLC["[[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 Fibonacci]]"]
    DP --> CoinLC["[[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 CoinChange]]"]
```mermaid
graph TD
    Algo["Algorithms"] --> Hashing["[[Interview/Concept/Algorithms/Hashing/index|Hashing]]"]
    Algo --> Sorting["(Sorting)"]
    Algo --> Searching["(Searching)"]
    Algo --> GraphT["[[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal]]"]
    Algo --> DP["(Dynamic Programming)"]
    Algo --> Greedy["(Greedy)"]
    Algo --> BitManip["(Bit Manipulation)"]
    Algo --> Complexity["[[Interview/Concept/Algorithms/Complexity Analysis/index|Complexity Analysis]]"]

    Hashing --> TwoSumP["[[Interview/Concept/Algorithms/Hashing/Two Sum Pattern|Two Sum]]"]
    Hashing --> DetectDupP["[[Interview/Concept/Algorithms/Hashing/Detecting Duplicates with Sets|Detect Duplicates]]"]
    Hashing --> FreqCountP["[[Interview/Concept/Algorithms/Hashing/Frequency Counting with Hash Maps|Frequency Count]]"]

    classDef category fill:#e0ffff,stroke:#008b8b,stroke-width:2px;
    class Algo, Hashing, Sorting, Searching, GraphT, DP, Greedy, BitManip category;
```



An important purpose of this folder is to build educaitonal philosophical algorhtm concepts along with robust framework thinking, step by step, motivation, intuition, etc. to help ppl truly learn the algorithm efficiently and effectively.