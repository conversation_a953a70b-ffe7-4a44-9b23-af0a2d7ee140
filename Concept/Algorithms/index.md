---
tags: [index, concept/algorithms]
aliases: [Algorithms Index, Algorithm Concepts]
---

# Algorithm Concepts

This index covers various algorithm design paradigms, specific algorithms, and analytical techniques relevant to coding interviews.

## Algorithm Categories / Patterns
- [[Interview/Concept/Algorithms/Caching/index|Caching Algorithms]]
- [[Interview/Concept/Algorithms/String Matching/index|String Matching]]
- [[Interview/Concept/Algorithms/Complexity Analysis/index|Complexity Analysis]]
- [[Interview/Concept/Algorithms/Hashing/index|Hashing Patterns & Techniques]]
- [[Interview/Concept/Algorithms/Recursion/index|Recursion Strategies]]
- [[Interview/Concept/Algorithms/Sliding Window/index|Sliding Window]]
- [[Interview/Concept/Algorithms/00 - Framework Thinking (Labuladong Philosophy)|Framework Thinking (Labuladong Philosophy)]]
- [[Interview/Concept/Algorithms/Greedy Algorithms/index|Greedy Algorithms]]
- [[Interview/Concept/Algorithms/Sorting/index|Sorting Algorithms]]
- [[Interview/Concept/Algorithms/Searching/index|Searching Algorithms]]
- [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal Algorithms]]
- [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]]
- [[Interview/Concept/Algorithms/Divide and Conquer/index|Divide and Conquer]]
- `[[Interview/Concept/Algorithms/Bit Manipulation/index|Bit Manipulation Techniques]]` (Placeholder)
- [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal Algorithms]]
- [[Interview/Concept/Algorithms/Backtracking/index|Backtracking Algorithms]]
- [[Interview/Concept/Algorithms/Two Pointers/index|Two Pointers]]
- [[Interview/Concept/Algorithms/Randomized Algorithms/index|Randomized Algorithms]]
- [[Interview/Concept/Algorithms/Array Manipulation/index|Array Manipulation]]

- [[Interview/Concept/Algorithms/Geometric Problems/index|Geometric Problems]]
- [[Interview/Concept/Algorithms/Interval Problems/index|Interval Problems]]
## Visualization of Algorithm Areas
```mermaid
graph TD
    Algo["Algorithms"] --> Cache["[[Interview/Concept/Algorithms/Caching/index|Caching]]"]
    Algo --> StrMatch["[[Interview/Concept/Algorithms/String Matching/index|String Matching]]"]
    Algo --> Complexity["[[Interview/Concept/Algorithms/Complexity Analysis/index|Complexity Analysis]]"]
    Algo --> Hashing["[[Interview/Concept/Algorithms/Hashing/index|Hashing]]"]
    Algo --> Recursion["[[Interview/Concept/Algorithms/Recursion/index|Recursion]]"]
    Algo --> SlidingWindow["[[Interview/Concept/Algorithms/Sliding Window/index|Sliding Window]]"]
    Algo --> FrameworkThink["[[Interview/Concept/Algorithms/00 - Framework Thinking (Labuladong Philosophy)|Framework Thinking]]"]
    Algo --> Greedy["[[Interview/Concept/Algorithms/Greedy Algorithms/index|Greedy]]"]
    Algo --> Sorting["[[Interview/Concept/Algorithms/Sorting/index|Sorting]]"]
    Algo --> Searching["[[Interview/Concept/Algorithms/Searching/index|Searching]]"]
    Algo --> GraphT["[[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal]]"]
    Algo --> DP["[[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]]"]
    Algo --> DnC["[[Interview/Concept/Algorithms/Divide and Conquer/index|Divide & Conquer]]"]
    Algo --> TreeT["[[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal]]"]
    Algo --> Backtracking["[[Interview/Concept/Algorithms/Backtracking/index|Backtracking]]"]
    Algo --> TwoPointers["[[Interview/Concept/Algorithms/Two Pointers/index|Two Pointers]]"]
    Algo --> RandAlgo["[[Interview/Concept/Algorithms/Randomized Algorithms/index|Randomized Algos]]"]
    Algo --> ArrayManip["[[Interview/Concept/Algorithms/Array Manipulation/index|Array Manipulation]]"]
    Algo --> BitManip["(Bit Manipulation)"]


    Hashing --> TwoSumP["[[Interview/Concept/Algorithms/Hashing/Two Sum Pattern|Two Sum]]"]
    Hashing --> DetectDupP["[[Interview/Concept/Algorithms/Hashing/Detecting Duplicates with Sets|Detect Duplicates]]"]
    Hashing --> FreqCountP["[[Interview/Concept/Algorithms/Hashing/Frequency Counting with Hash Maps|Frequency Count]]"]

    DP --> FibLC["[[Interview/Practice/LeetCode/LC509 - Fibonacci Number|LC509 Fibonacci]]"]
    DP --> CoinLC["[[Interview/Practice/LeetCode/LC322 - Coin Change|LC322 CoinChange]]"]

    Backtracking --> BT_Framework["[[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Framework]]"]
    Backtracking --> BT_PermCombSub["[[Interview/Concept/Algorithms/Backtracking/Patterns/01 - Backtracking for Permutations, Combinations, Subsets|P/C/S Patterns]]"]
    Backtracking --> BT_BallBox["[[Interview/Concept/Algorithms/Backtracking/02 - Backtracking - Two Exhaustion Perspectives (Ball-Box Model)|Ball-Box Model]]"]

    GraphT --> GT_Island["[[Interview/Concept/Algorithms/Graph Traversal/Island Problems Framework (DFS BFS)|Island Problems Framework]]"]

    Recursion --> Rec_Clarify["[[Interview/Concept/Algorithms/Recursion/01 - DFS vs Backtracking - Clarifications|DFS vs Backtracking Clarifications]]"]

    classDef category fill:#e0ffff,stroke:#008b8b,stroke-width:2px;
    class Algo, Cache, StrMatch, Complexity, Hashing, Recursion, SlidingWindow, FrameworkThink, Greedy, Sorting, Searching, GraphT, DP, DnC, TreeT, Backtracking, TwoPointers, RandAlgo, ArrayManip, BitManip category;
```

An important purpose of this folder is to build educational philosophical algorithm concepts along with robust framework thinking, step by step, motivation, intuition, etc. to help ppl truly learn the algorithm efficiently and effectively.
