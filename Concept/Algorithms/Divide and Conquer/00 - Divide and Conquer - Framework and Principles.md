---
tags: [concept/algorithms, concept/divide_and_conquer, type/framework, concept/recursion]
aliases: [<PERSON><PERSON> and Conquer Algorithm, 分治算法框架, 分治思想]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/分治算法解题套路框架.md]].
> Labuladong distinguishes between the general "divide and conquer *thinking*" (分解问题思路) and specific "divide and conquer *algorithms*".

# Divide and Conquer: Framework and Principles

Divide and Conquer is a powerful algorithmic paradigm. It's important to distinguish between the broad "divide and conquer *thinking*" (which Labuladong calls "decomposition thinking" - 分解问题思路) and specific "divide and conquer *algorithms*".

## 🧠 Divide and Conquer Thinking (Decomposition)

This is a general approach to problem-solving, especially with [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|recursion]].
- **Core Idea:** Break down a large problem into smaller, self-similar subproblems. Solve the subproblems recursively. Combine their solutions to solve the original problem.
- **Examples:**
    - <PERSON><PERSON><PERSON><PERSON>: `fib(n) = fib(n-1) + fib(n-2)`.
    - Counting nodes in a binary tree: `count(root) = count(root.left) + count(root.right) + 1`.
    - [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]] fundamentally uses this decomposition thinking, adding optimizations like memoization or tabulation.

Many recursive algorithms employ this decompositional thinking. However, not all of them are strictly termed "Divide and Conquer algorithms."

## 🎯 Divide and Conquer Algorithms (Narrow Definition)

A "Divide and Conquer algorithm" specifically refers to algorithms where **decomposing the problem and then solving leads to a better time complexity** than solving the problem directly without decomposition.

- **Key Characteristic:** The "divide" and "combine" steps, along with solving subproblems, result in overall efficiency gains.
- **Non-Example (Fibonacci):** The naive recursive Fibonacci `fib(n) = fib(n-1) + fib(n-2)` uses decomposition thinking but is *not* an efficient divide and conquer algorithm in this form because it has overlapping subproblems leading to exponential time complexity. [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]] techniques are needed to make it efficient.
- **Classic Examples:**
    - **[[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]]:** Divides array, sorts subarrays, merges sorted subarrays. $O(N \log N)$. Direct sorting methods like insertion sort are $O(N^2)$.
    - **[[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]]:** Partitions array, sorts subarrays. Average $O(N \log N)$.
    - **Binary Search:** Though simple, it divides the search space in half at each step.
    - **Closest Pair of Points:** A more advanced geometric algorithm.
    - **Strassen's Matrix Multiplication.**

Labuladong uses Bucket Sort as an example: directly using insertion sort is $O(N^2)$, but by dividing into buckets and then using insertion sort on smaller buckets, overall complexity can approach $O(N)$.

## 📜 General Steps of a Divide and Conquer Algorithm

1.  **Divide:** Break the problem into several smaller subproblems that are themselves smaller instances of the original problem.
2.  **Conquer:** Solve the subproblems recursively. If the subproblems are small enough (base case), solve them directly.
3.  **Combine:** Combine the solutions to the subproblems into the solution for the original problem.

```mermaid
graph TD
    P["Problem P"] --> D{"Divide"}
    D --> SP1["Subproblem P1"]
    D --> SP2["Subproblem P2"]
    D --> SPN["...Subproblem Pn"]

    SP1 --> C1["Conquer P1 (Recursive Call)"]
    SP2 --> C2["Conquer P2 (Recursive Call)"]
    SPN --> CN["Conquer Pn (Recursive Call)"]
    
    C1 --> S1["Solution S1"]
    C2 --> S2["Solution S2"]
    CN --> SN["Solution Sn"]

    S1 --> M{"Combine Solutions"}
    S2 --> M
    SN --> M
    M --> FinalS["Final Solution for P"]

    classDef step fill:#e6f2ff,stroke:#3366cc,stroke-width:2px;
    class D,C1,C2,CN,M step;
    classDef problem fill:#fff0b3,stroke:#ffcc00,stroke-width:2px;
    class P,SP1,SP2,SPN problem;
    classDef solution fill:#d6ffd6,stroke:#006400,stroke-width:2px;
    class S1,S2,SN,FinalS solution;
```

## Why Can Divide and Conquer Be More Efficient?

The efficiency gain often comes from how the problem size reduces and how the "combine" step is performed.
- If a problem of size $N$ is divided into $a$ subproblems of size $N/b$, and the divide/combine step takes $f(N)$ time, the recurrence relation is often of the form $T(N) = aT(N/b) + f(N)$. The Master Theorem can be used to solve such recurrences.
- If $f(N)$ is relatively small (e.g., $O(N)$ for Merge Sort where $a=2, b=2$), the overall complexity can be significantly better than a naive $O(N^2)$ approach.

## Example: Merge K Sorted Lists (LeetCode 23)

[[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Sorted Lists]] is a good example where divide and conquer provides an efficient solution.
- **Problem:** Merge $k$ sorted linked lists into one sorted linked list.
- **Naive approach:** Merge lists one by one. If average length is $L$, this is roughly $(L+L) + (2L+L) + ... + ((k-1)L+L) \approx O(k^2 L)$.
- **Divide and Conquer Approach:**
    1.  **Divide:** Split the $k$ lists into two halves: $k/2$ lists and $k/2$ lists.
    2.  **Conquer:** Recursively merge the lists in each half. This results in two sorted lists.
    3.  **Combine:** Merge the two resulting sorted lists (this takes $O(N_{total})$ where $N_{total}$ is total elements).
    - This approach is similar to Merge Sort and results in $O(N_{total} \log k)$ time.

```python
# Conceptual Python for Merge K Sorted Lists (Divide and Conquer)
# class ListNode: ...
# def merge_two_lists(l1, l2): ... (standard merge for two sorted lists)

# def merge_k_lists_dc(lists: list[ListNode], low: int, high: int) -> ListNode:
#     if low > high:
#         return None
#     if low == high: # Base case: single list
#         return lists[low]
    
#     mid = low + (high - low) // 2
    
#     # Conquer: recursively merge halves
#     left_merged = merge_k_lists_dc(lists, low, mid)
#     right_merged = merge_k_lists_dc(lists, mid + 1, high)
    
#     # Combine: merge the two sorted lists from subproblems
#     return merge_two_lists(left_merged, right_merged)

# # Initial call:
# # merged_list_head = merge_k_lists_dc(all_lists, 0, len(all_lists) - 1)
```
This structure is analogous to building a merge plan up a [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|binary tree]], where leaves are original lists and internal nodes are merged lists.

## 总结 (Summary)
- **Divide and Conquer Thinking (Decomposition):** A broad recursive strategy to break problems into subproblems and combine their solutions.
- **Divide and Conquer Algorithms:** Specific algorithms where this decomposition leads to a demonstrable improvement in time complexity compared to direct approaches.
- **Common Steps:** Divide, Conquer (recurse), Combine.
- Efficiency often analyzed using recurrence relations (e.g., Master Theorem).
- Classic examples include Merge Sort, Quick Sort, and problems like Merge K Sorted Lists.

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
Previous: [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursion Thinking Modes]]
Next: [[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Sorted Lists (Example)]]
Related: [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]], [[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]]
