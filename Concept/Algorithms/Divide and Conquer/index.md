---
tags: [index, concept/algorithms, concept/divide_and_conquer]
aliases: [Divide and Conquer Index, D&C Algorithms]
---

# Divide and Conquer Algorithms

This section covers the Divide and Conquer algorithmic paradigm, including its core principles and example applications.

## Core Concepts:
- [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework and Principles|Divide and Conquer - Framework and Principles]]
  - Distinction from general "decomposition thinking"
  - Steps: Divide, Conquer, Combine
  - Recurrence Relations and Master Theorem (brief mention)

## Classic Examples:
- [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]]
- [[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]]
- [[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|Merge K Sorted Lists]]

## Visualization
```mermaid
graph TD
    DCConcept["Divide & Conquer"] --> Framework["[[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework and Principles|Framework & Principles]]"]
    
    DCConcept --> Examples["Classic Examples"]
    Examples --> MergeSortL["[[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]]"]
    Examples --> QuickSortL["[[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]]"]
    Examples --> LC23L["[[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Lists]]"]

    classDef main fill:#d5e8d4,stroke:#82b366,stroke-width:2px;
    class DCConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
