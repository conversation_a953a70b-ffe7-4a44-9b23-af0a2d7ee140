---
tags: [index, concept/algorithms, concept/divide_and_conquer]
aliases: [Divide and Conquer Index]
---

# Divide and Conquer Algorithms

This section covers the Divide and Conquer algorithmic paradigm.

## Core Concepts:
- [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer - Framework]]
  - General Steps (Divide, Conquer, Combine)
  - Relation to Tree Traversals
  - Complexity Analysis (Master Theorem)

## Classic Examples:
- [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]]
- [[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]]
- [[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Sorted Lists]] (application of D&C)
- Karatsuba Algorithm (Fast Multiplication) - Placeholder
- Strassen's Algorithm (Matrix Multiplication) - Placeholder

## Visualization
```mermaid
graph TD
    DnCConcept["Divide & Conquer"] --> Framework["[[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Framework]]"]
    Framework --> Steps["(Divide, Conquer, Combine)"]
    Framework --> MasterTh["(Master Theorem)"]

    DnCConcept --> Examples["Classic Examples"]
    Examples --> MergeS["[[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]]"]
    Examples --> QuickS["[[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]]"]
    Examples --> MergeKL["[[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|Merge K Lists]]"]
    Examples --> Others["(Karatsuba, Strassen...)"]
    
    classDef main fill:#d5e8d4,stroke:#82b366,stroke-width:2px;
    class DnCConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
