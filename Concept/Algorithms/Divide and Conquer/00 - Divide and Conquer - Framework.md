---
tags: [concept/algorithms, concept/divide_and_conquer, type/framework, pattern/recursion]
aliases: [<PERSON><PERSON> and Conquer Paradigm, 分治算法框架]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/分治算法解题套路框架.md]].
> Labuladong distinguishes "Divide and Conquer思想 (thought/idea)" from "Divide and Conquer算法 (algorithm)". The latter implies improved complexity through division.

# Divide and Conquer: Algorithmic Framework

The Divide and Conquer paradigm is a powerful problem-solving strategy that involves breaking down a problem into smaller, more manageable subproblems, solving these subproblems (often recursively), and then combining their solutions to form the solution to the original problem.

## 💭 Distinguishing "Divide and Conquer Idea" from "Divide and Conquer Algorithm"

- **Divide and Conquer Idea (Decomposition Thinking):** This is a broad concept where a problem is broken into subproblems. Many recursive algorithms use this idea, such as simple tree traversals that calculate properties (e.g., node count = left_count + right_count + 1) or basic recursive Fibonacci. This is one of the "Two Thinking Modes" described in [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursion - One Perspective Two Thinking Modes]].
- **Divide and Conquer *Algorithm* (Strict Sense):** Refers to algorithms where breaking the problem down and solving subproblems leads to a *more efficient solution (lower time complexity)* than solving the problem directly without decomposition.
    - Examples: [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]], [[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]], Karatsuba algorithm for fast multiplication, Strassen's algorithm for matrix multiplication.
    - If decomposition doesn't yield a better complexity, it's just "recursive decomposition" or "using the divide and Conquer idea," not strictly a "Divide and Conquer Algorithm" in this specialized sense.

This note focuses on the general recursive structure common to algorithms that employ the divide and conquer strategy, especially those leading to efficiency gains.

## 📜 General Steps of Divide and Conquer

A typical Divide and Conquer algorithm involves three main steps:

1.  **Divide:** Break the given problem into a number of subproblems that are smaller instances of the same problem.
2.  **Conquer:** Solve the subproblems recursively. If the subproblem sizes are small enough (base case), solve them directly.
3.  **Combine:** Combine the solutions to the subproblems into the solution for the original problem.

## 🌲 Recursive Structure (Often Tree-Like)

The process of a Divide and Conquer algorithm can often be visualized as a recursion tree.

```python
def divide_and_conquer_template(problem_input):
    # 1. Base Case: If the problem is small enough, solve it directly.
    if is_small_enough(problem_input):
        return solve_directly(problem_input)

    # 2. Divide: Break problem_input into subproblems: sub1, sub2, ...
    subproblems = divide_problem(problem_input)
    
    sub_solutions = []
    # 3. Conquer: Solve subproblems recursively.
    for sub_problem in subproblems:
        sub_solutions.append(divide_and_conquer_template(sub_problem))
        
    # 4. Combine: Combine sub_solutions to solve the original problem_input.
    result = combine_solutions(sub_solutions, problem_input) # problem_input might be needed for context
    
    return result

# Helper functions (to be defined based on the specific problem)
# def is_small_enough(p): ...
# def solve_directly(p): ...
# def divide_problem(p): ...
# def combine_solutions(sols, p_orig): ...
```

## 🔗 Connection to Binary Tree Traversals

Labuladong often draws parallels between Divide and Conquer algorithms and binary tree traversals:
- **[[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]]:**
    - `sort(array)`:
        - Divide: Split `array` into `left_half` and `right_half`.
        - Conquer: `sorted_left = sort(left_half)`, `sorted_right = sort(right_half)`.
        - Combine: `merge(sorted_left, sorted_right)`.
    - This structure is analogous to a **post-order traversal**. The "work" (merging) is done after the recursive calls (conquering subproblems) return.
- **[[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]]:**
    - `sort(array, low, high)`:
        - Divide: Partition `array` around a pivot `p` such that elements `< p` are left, `> p` are right. (This is the main "work" or "processing").
        - Conquer: `sort(array, low, p-1)`, `sort(array, p+1, high)`.
        - Combine: Trivial (already sorted in place).
    - This structure is analogous to a **pre-order traversal**. The main "work" (partitioning) is done before the recursive calls.

## ⏳ Complexity Analysis (Master Theorem)

The time complexity of Divide and Conquer algorithms is often analyzed using recurrence relations. The Master Theorem provides a way to solve many common recurrences of the form:
$T(N) = aT(N/b) + f(N)$
- $N$: size of the problem.
- $a$: number of subproblems.
- $N/b$: size of each subproblem (assuming all subproblems are roughly equal size).
- $f(N)$: cost of dividing the problem and combining the solutions.

For example:
- **Merge Sort:** $T(N) = 2T(N/2) + O(N)$ (2 subproblems of size N/2, $O(N)$ to merge). Solution: $O(N \log N)$.
- Some tree algorithms: $T(N) = 2T(N/2) + O(1)$ (e.g., counting nodes in a balanced tree). Solution: $O(N)$.

## Example: Merge K Sorted Lists (LeetCode 23)
[[Interview/Practice/LeetCode/LC23 - Merge K Sorted Lists|LC23 - Merge K Sorted Lists]]

This problem asks to merge $k$ sorted linked lists into one sorted linked list.
A divide and conquer approach:
1.  **Base Cases:**
    - If $k=0$ lists, return `null`.
    - If $k=1$ list, return that list.
2.  **Divide:** Split the $k$ lists into two halves: first $k/2$ lists and remaining $k - k/2$ lists.
3.  **Conquer:** Recursively merge the first $k/2$ lists into `list1`. Recursively merge the other half into `list2`.
4.  **Combine:** Merge `list1` and `list2` (standard two-list merge).

```python
# Conceptual structure for merging k lists
# def mergeKLists(lists):
#     if not lists: return None
#     if len(lists) == 1: return lists[0]
#
#     mid = len(lists) // 2
#     left_half = lists[:mid]
#     right_half = lists[mid:]
#
#     merged_left = mergeKLists(left_half)   # Conquer
#     merged_right = mergeKLists(right_half) # Conquer
#
#     return merge_two_lists(merged_left, merged_right) # Combine
#
# def merge_two_lists(l1, l2): ... (standard 2-list merge)
```
This approach effectively builds a "merge tree." If there are $k$ lists, the height of this recursion tree is $O(\log k)$. At each level of the tree, roughly $N$ total elements are processed during merges (where $N$ is total number of nodes across all lists). So, complexity is $O(N \log k)$.

## When is Divide and Conquer Effective?

- **Independent Subproblems:** The subproblems should ideally be solvable independently.
- **Efficiency Gain:** The cost of dividing and combining, plus solving subproblems, should be less than solving the original problem monolithically. This often happens when $f(N)$ is relatively small compared to the work saved by reducing problem size, or when subproblems significantly overlap and can be memoized (leading to dynamic programming, a special case of D&C).
- **Balanced Partitions (Often Ideal):** Algorithms like Merge Sort perform best when subproblems are of roughly equal size. Quick Sort's worst-case $O(N^2)$ occurs with unbalanced partitions.

## 总结 (Summary)
- **Divide and Conquer** is a strategy that breaks a problem into smaller subproblems, solves them recursively, and combines their solutions.
- It's a form of [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|recursive decomposition thinking]].
- Strictly speaking, a "Divide and Conquer Algorithm" implies that this decomposition leads to a more efficient solution (lower time complexity) than direct approaches.
- Key examples include Merge Sort, Quick Sort, and many tree algorithms.
- The Master Theorem is often used to analyze the complexity of D&C algorithms.
- The effectiveness relies on the cost of division/combination relative to the reduction in problem size and the independence of subproblems.

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
Previous: [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursion Thinking Modes]]
Next: [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]] (as a related paradigm)
Related: [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]], [[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]]
