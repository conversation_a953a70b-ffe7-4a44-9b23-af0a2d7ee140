---
tags: [concept/algorithms, concept/geometric, pattern/area_counting, pattern/vertex_counting, pattern/hashing, topic/geometry]
aliases: [Perfect Rectangle Problem, Rectangle Cover Algorithm, 完美矩形判定]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/如何判定完美矩形.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/如何判定完美矩形.md|如何判定完美矩形 by Labuladong]].
> This pattern is key to solving [[Interview/Practice/LeetCode/LC391 - Perfect Rectangle|LC391 - Perfect Rectangle]].

# Perfect Rectangle Detection Pattern

The problem of determining if a set of smaller rectangles perfectly covers a larger, single rectangle without gaps or overlaps is a fascinating geometric puzzle. Labuladong's approach provides an elegant solution using two main criteria: area matching and vertex counting.

## 🎯 Core Idea

To confirm that a collection of small rectangles `rectangles[i] = [x1_i, y1_i, x2_i, y2_i]` forms a single "perfect" large rectangle, two conditions must simultaneously hold true:

1.  **Area Conservation:** The sum of the areas of all small rectangles must be equal to the area of the encompassing bounding box formed by these small rectangles.
2.  **Vertex Property:** In a perfect covering:
    *   The four corner points of the encompassing bounding box must each appear exactly once as a vertex among all small rectangles.
    *   All other internal vertices formed by the corners of small rectangles must appear an even number of times (typically 2 or 4 times, as they are shared by adjacent small rectangles).

If both these conditions are met, the small rectangles form a perfect rectangle.

## 🛠️ Algorithm Steps

1.  **Calculate Bounding Box and Total Area of Small Rectangles:**
    *   Initialize `min_x1 = float('inf')`, `min_y1 = float('inf')`, `max_x2 = float('-inf')`, `max_y2 = float('-inf')`.
    *   Initialize `total_small_rects_area = 0`.
    *   Iterate through each small rectangle `(x1, y1, x2, y2)`:
        *   Update `min_x1 = min(min_x1, x1)`, `min_y1 = min(min_y1, y1)`.
        *   Update `max_x2 = max(max_x2, x2)`, `max_y2 = max(max_y2, y2)`.
        *   Add its area to `total_small_rects_area`: `(x2 - x1) * (y2 - y1)`.

2.  **Check Area Conservation:**
    *   Calculate the area of the determined bounding box: `bounding_box_area = (max_x2 - min_x1) * (max_y2 - min_y1)`.
    *   If `total_small_rects_area != bounding_box_area`, then it's not a perfect rectangle. Return `False`. This condition alone handles cases with gaps or overlaps that alter the total area.
    ```tikz
    \begin{tikzpicture}[
        rect/.style={draw, minimum width=1cm, minimum height=1cm},
        bounding_box/.style={rect, dashed, red, thick},
        small_rect_A/.style={rect, fill=blue!20},
        small_rect_B/.style={rect, fill=green!20},
        label_style/.style={font=\sffamily\small}
    ]
        % Scenario 1: Gap
        \node[label_style] at (1, 2.5) {Scenario 1: Gap};
        \node[small_rect_A] (s1a) at (0,1) {A};
        \node[small_rect_A] (s1b) at (2,1) {B};
        \node[bounding_box] (bb1) at (1,1) [minimum width=3cm, minimum height=1cm] {};
        \node[label_style, below=0.2cm of bb1] {Area(A)+Area(B) < Area(BB)};

        % Scenario 2: Overlap
        \node[label_style] at (6, 2.5) {Scenario 2: Overlap};
        \node[small_rect_A] (s2a) at (5,1) {A};
        \node[small_rect_B, minimum width=1.5cm] (s2b) at (6,1) {B}; % B overlaps A
        \node[bounding_box] (bb2) at (5.75,1) [minimum width=2.5cm, minimum height=1cm] {};
        \node[label_style, below=0.2cm of bb2] {Area(A)+Area(B) > Area(BB)};

        % Correct fill implies this check must pass.
    \end{tikzpicture}
    ```

3.  **Count Vertex Occurrences:**
    *   Use a hash set (`points_count_set`) to track the occurrences of each vertex from the small rectangles. The vertex coordinates `(x, y)` can be stored as strings `"x,y"` or tuples `(x,y)` to be hashable.
    *   For each small rectangle `(x1, y1, x2, y2)`, consider its four corner points: `(x1,y1), (x1,y2), (x2,y1), (x2,y2)`.
    *   For each corner point:
        *   If the point is already in `points_count_set`, remove it (it has appeared an even number of times so far).
        *   If the point is not in `points_count_set`, add it (it has appeared an odd number of times so far).

4.  **Check Vertex Property:**
    *   After processing all small rectangles, `points_count_set` should contain exactly 4 points. If `len(points_count_set) != 4`, return `False`.
    *   These 4 points must be the corners of the bounding box: `(min_x1, min_y1)`, `(min_x1, max_y2)`, `(max_x2, min_y1)`, and `(max_x2, max_y2)`. Check if all four of these are present in `points_count_set`. If not, return `False`.
    ```tikz
    \begin{tikzpicture}[
        rect/.style={draw, fill=gray!10},
        point_highlight/.style={circle, draw, fill=red, inner sep=1.5pt},
        label_style/.style={font=\sffamily\small}
    ]
        % Perfect Rectangle Case
        \node[label_style] at (1.5, 3) {Perfect Rectangle};
        \draw[rect] (0,0) rectangle (1,1); \draw[rect] (1,0) rectangle (2,1);
        \draw[rect] (0,1) rectangle (1,2); \draw[rect] (1,1) rectangle (2,2);

        \node[point_highlight, label=below left:{(0,0)}] at (0,0) {};
        \node[point_highlight, label=above left:{(0,2)}] at (0,2) {};
        \node[point_highlight, label=below right:{(2,0)}] at (2,0) {};
        \node[point_highlight, label=above right:{(2,2)}] at (2,2) {};
        % Internal points (e.g., (1,1) appears 4 times, (0,1) 2 times, (1,0) 2 times, (1,2) 2 times, (2,1) 2 times)
        % are removed from the set.
        \node[label_style, below=1cm of current bounding box.south] {Only 4 bounding box corners remain in set.};

        % Imperfect Rectangle Case (e.g., an L-shape from 3 squares)
        \begin{scope}[xshift=5cm]
            \node[label_style] at (1, 3) {Imperfect (L-shape)};
            \draw[rect] (0,0) rectangle (1,1); 
            \draw[rect] (0,1) rectangle (1,2);
            \draw[rect] (1,1) rectangle (2,2);
            % Odd-occurrence points: (0,0), (1,0), (0,2), (2,2), (2,1), (1,1) - 6 points
            % (1,1) appears 3 times. (0,1) 2 times. (1,2) 2 times.
            \node[point_highlight, label=below left:{(0,0)}] at (0,0) {};
            \node[point_highlight, label=above left:{(0,2)}] at (0,2) {};
            \node[point_highlight, label=above right:{(2,2)}] at (2,2) {};
            \node[point_highlight, label=right:{(2,1)}] at (2,1) {};
            \node[point_highlight, label=below:{(1,1)}] at (1,1) {};
            \node[point_highlight, label=below:{(1,0)}] at (1,0) {};
            \node[label_style, below=1cm of current bounding box.south] {More than 4 points remain in set.};
        \end{scope}
    \end{tikzpicture}
    ```

5.  If all checks pass, return `True`.

## Python Implementation Sketch
```python
class SolutionPerfectRectangle:
    def isRectangleCover(self, rectangles: list[list[int]]) -> bool:
        min_x1, min_y1 = float('inf'), float('inf')
        max_x2, max_y2 = float('-inf'), float('-inf')

        actual_area_sum = 0
        points_set = set()

        for x1, y1, x2, y2 in rectangles:
            min_x1 = min(min_x1, x1)
            min_y1 = min(min_y1, y1)
            max_x2 = max(max_x2, x2)
            max_y2 = max(max_y2, y2)

            actual_area_sum += (x2 - x1) * (y2 - y1)

            # Vertices for current rectangle
            p1 = (x1, y1)
            p2 = (x1, y2)
            p3 = (x2, y1)
            p4 = (x2, y2)

            for point in [p1, p2, p3, p4]:
                if point in points_set:
                    points_set.remove(point)
                else:
                    points_set.add(point)

        # Check Area
        expected_bounding_box_area = (max_x2 - min_x1) * (max_y2 - min_y1)
        if actual_area_sum != expected_bounding_box_area:
            return False

        # Check Vertex Property
        if len(points_set) != 4:
            return False

        # Check if the 4 points in set are the corners of the bounding box
        if (min_x1, min_y1) not in points_set: return False
        if (min_x1, max_y2) not in points_set: return False
        if (max_x2, min_y1) not in points_set: return False
        if (max_x2, max_y2) not in points_set: return False

        return True
```

## Complexity Analysis
- **Time Complexity:** $O(N)$, where $N$ is the number of small rectangles.
    - Iterating through rectangles: $O(N)$.
    - Set operations (add, remove, check): Average $O(1)$ for hashable tuples. Each rectangle adds/removes 4 points. Total $O(N)$ for point processing.
- **Space Complexity:** $O(N)$ in the worst case for `points_set` if many distinct vertices appear an odd number of times (e.g., a "checkerboard" of non-touching squares).

## 总结 (Summary)
- Detecting a perfect rectangle formed by smaller rectangles involves two main checks:
    1.  The sum of areas of small rectangles must equal the area of their overall bounding box.
    2.  Exactly four unique vertices (the corners of the bounding box) must result from the "XOR" logic of counting vertex appearances. All internal shared vertices must appear an even number of times.
- This approach uses basic geometry and [[Interview/Concept/Algorithms/Hashing/Detecting Duplicates with Sets|hashing (via sets)]] to efficiently verify these properties.

---
Parent: [[Interview/Concept/Algorithms/Geometric Problems/index|Geometric Problem Patterns]]
Related Problem: [[Interview/Practice/LeetCode/LC391 - Perfect Rectangle|LC391 - Perfect Rectangle]]
