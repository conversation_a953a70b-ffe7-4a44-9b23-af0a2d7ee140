---
tags: [index, concept/algorithms, concept/bit_manipulation]
aliases: [Bit Manipulation Index, Bitwise Algorithms]
---

# Bit Manipulation Techniques

This section covers algorithms and techniques that involve direct manipulation of bits within integers.

## Core Concepts:
- [[Interview/Concept/Algorithms/Bit Manipulation/00 - Common Bitwise Operations|Common Bitwise Operations]]
  - Basic Operators (`&, |, ^, ~, <<, >>`)
  - Useful Tricks (e.g., `n & (n-1)`, `n & (-n)`)
- [[Interview/Concept/Algorithms/Bit Manipulation/XOR for Finding Unique Element|XOR for Finding Unique Element]] (Placeholder, concept embedded in Common Ops for now)

## LeetCode Examples:
- [[Interview/Practice/LeetCode/LC136 - Single Number|LC136 - Single Number]]
- [[Interview/Practice/LeetCode/LC191 - Number of 1 Bits|LC191 - Number of 1 Bits (Hamming Weight)]]
- [[Interview/Practice/LeetCode/LC231 - Power of Two|LC231 - Power of Two]]
- [[Interview/Practice/LeetCode/LC268 - Missing Number|LC268 - Missing Number]] (can be solved with XOR)

## Visualization
```mermaid
graph TD
    BitManip["Bit Manipulation"] --> CommonOps["[[Interview/Concept/Algorithms/Bit Manipulation/00 - Common Bitwise Operations|Common Operations & Tricks]]"]

    CommonOps --> BasicOps["(&, |, ^, ~, <<, >>)"]
    CommonOps --> NAndNMinus1["(n & (n-1))"]
    CommonOps --> NAndNegN["(n & (-n))"]
    CommonOps --> XORExamples["(XOR for Unique/Missing)"]

    BitManip --> ExamplesLC["LeetCode Examples"]
    ExamplesLC --> LC136Bit["[[Interview/Practice/LeetCode/LC136 - Single Number|LC136]]"]
    ExamplesLC --> LC191Bit["[[Interview/Practice/LeetCode/LC191 - Number of 1 Bits|LC191]]"]
    ExamplesLC --> LC231Bit["[[Interview/Practice/LeetCode/LC231 - Power of Two|LC231]]"]
    ExamplesLC --> LC268Bit["[[Interview/Practice/LeetCode/LC268 - Missing Number|LC268]]"]

    classDef main fill:#e6fff2,stroke:#00994d,stroke-width:2px;
    class BitManip main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
