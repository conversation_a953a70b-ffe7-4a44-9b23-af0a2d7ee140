---
tags: [concept/algorithms, concept/bit_manipulation, type/technique, course/labuladong]
aliases: [Common Bitwise Operations, 位操作技巧, Bit Twiddling]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/数学运算技巧/常用的位操作.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/数学运算技巧/常用的位操作.md|常用的位操作 by Labuladong]].
> This note covers fundamental and commonly used bitwise operations and tricks.

# Common Bitwise Operations

Bitwise operations manipulate integers at the level of their individual bits. They are powerful tools for optimization, low-level control, and solving certain types of algorithmic problems efficiently.

## ⚙️ Basic Bitwise Operators

Most languages support these common operators:
- **AND (`&`):** `1 & 1 = 1`, `1 & 0 = 0`, `0 & 0 = 0`. Sets a bit if it exists in both operands.
- **OR (`|`):** `1 | 1 = 1`, `1 | 0 = 1`, `0 | 0 = 0`. Sets a bit if it exists in either operand.
- **XOR (`^`):** `1 ^ 1 = 0`, `1 ^ 0 = 1`, `0 ^ 0 = 0`. Sets a bit if it's set in one operand but not both.
- **NOT (`~`):** Flips all bits of an operand. (e.g., `~0...0101` becomes `1...1010`). Behavior with signed integers depends on representation (e.g., two's complement).
- **Left Shift (`<<`):** `x << k` shifts bits of `x` to the left by `k` positions; equivalent to multiplying by $2^k$.
- **Right Shift (`>>`):** `x >> k` shifts bits of `x` to the right by `k` positions; equivalent to dividing by $2^k$.
    - **Arithmetic Right Shift:** Preserves the sign bit (fills with sign bit). Common for signed integers.
    - **Logical Right Shift (`>>>` in Java/JavaScript):** Fills with zeros. Python's `>>` on positive integers is logical; on negative integers, it behaves like arithmetic shift (due to Python's arbitrary-precision integers).

## ✨ Interesting Bitwise Tricks (from Labuladong)

Labuladong highlights several "有趣但没卵用" (interesting but not very useful) tricks, and some useful ones:

1.  **Lowercase Conversion:** `('a' | ' ')` gives `'a'`, `('A' | ' ')` gives `'a'`.
    - Relies on ASCII: space is `0b00100000`. Uppercase letters `A-Z` are `0b010xxxxx`, lowercase `a-z` are `0b011xxxxx`. ORing with space sets the 6th bit (from right, 0-indexed), converting uppercase to lowercase.
2.  **Uppercase Conversion:** `('b' & '_')` gives `'B'`, `('B' & '_')` gives `'B'`.
    - Relies on ASCII: underscore `_` is `0b01011111`. ANDing with `_` effectively unsets the 6th bit, converting lowercase to uppercase.
3.  **Case Toggle:** `('d' ^ ' ')` gives `'D'`, `('D' ^ ' ')` gives `'d'`.
    - XORing with space flips the 6th bit, toggling case.
4.  **Swap Two Integers (without temp variable):**
    ```python
    # a = 1, b = 2
    # a = a ^ b  # a = 1^2
    # b = a ^ b  # b = (1^2)^2 = 1
    # a = a ^ b  # a = (1^2)^1 = 2
    ```
5.  **Add One:** `n = -~n`
    - `~n` is bitwise NOT. `-x` in two's complement is `~x + 1`.
    - So, `-~n = ~(~n) + 1 = n + 1`.
6.  **Subtract One:** `n = ~-n`
    - `-n = ~n + 1`.
    - `~-n = ~(~n + 1)`. This is not directly `n-1`.
    - Labuladong's text shows this. Let's verify: If `n=2 (0b10)`. `-n` is `~0b01+1 = 0b111...110`. `~-n` is `0b000...001 = 1`. It works.
7.  **Check if Integers Have Opposite Signs:**
    - `(x ^ y) < 0`
    - If `x` and `y` have different signs, their most significant bits (sign bits) will be different. `x^y` will have its MSB set to 1, making the result negative (in two's complement).
    - If same sign, MSB of `x^y` will be 0, result non-negative.
    - This avoids overflow issues that `x * y < 0` might have.

```tikz
\begin{tikzpicture}[
    op_box/.style={rectangle, draw, rounded corners, fill=blue!10, font=\sffamily\small, align=center, text width=3.5cm},
    arrow/.style={->, thick, red}
]
    \node[op_box] (and_op) at (0,2) {\textbf{AND (\&)}\\ Bit set if in BOTH};
    \node[op_box] (or_op) at (4,2) {\textbf{OR (|)}\\ Bit set if in EITHER};
    \node[op_box] (xor_op) at (8,2) {\textbf{XOR (^)}\\ Bit set if in ONE, not BOTH};

    \node[op_box] (not_op) at (0,0) {\textbf{NOT (~)}\\ Flips all bits};
    \node[op_box] (lshift) at (4,0) {\textbf{Left Shift (<< k)}\\ Multiply by $2^k$};
    \node[op_box] (rshift) at (8,0) {\textbf{Right Shift (>> k)}\\ Divide by $2^k$};

    \node at (4, -1.5) [draw, fill=yellow!20, text width=8cm, align=center, font=\sffamily\small]
        {Key applications arise from properties like $x \text{\textasciicircum} x = 0$ and $x \text{\textasciicircum} 0 = x$, and specific bit manipulations.};
\end{tikzpicture}
```

## 🚀 Important Bit Manipulation Techniques

### 1. `n & (n - 1)`: Clear a_variableb_example the LSB (Least Significant Bit that is 1)
- **Property:** `n - 1` flips the LSB of `n` to 0 and all subsequent bits (to its right) to 1.
- `n & (n - 1)` then clears the LSB of `n` and leaves higher bits unchanged.
- **Example:** `n = 6 (0b0110)`. `n-1 = 5 (0b0101)`. `n & (n-1) = 0b0100 = 4`. The LSB '1' at position 1 was cleared.
- **Applications:**
    - Counting set bits (Hamming weight): Repeatedly apply `n = n & (n-1)` until `n` is 0. The number of operations is the number of set bits. (See [[Interview/Practice/LeetCode/LC191 - Number of 1 Bits|LC191]]).
    - Checking if `n` is a power of two: A power of two has only one bit set (e.g., `0b1000`). So, if `n > 0` and `(n & (n-1)) == 0`, then `n` is a power of two. (See [[Interview/Practice/LeetCode/LC231 - Power of Two|LC231]]).

### 2. `n & (-n)` or `n & (~n + 1)`: Isolate the LSB (Least Significant Bit that is 1)
- **Property:** In two's complement, `-n` is `~n + 1`. This operation isolates the rightmost '1' bit.
- **Example:** `n = 6 (0b...0110)`. `-n` (in 8-bit two's complement) would be `0b...1010`.
  `n & (-n) = 0b...0110 & 0b...1010 = 0b...0010 = 2`. The rightmost '1' of 6 is isolated.
- **Applications:**
    - Fenwick Tree (Binary Indexed Tree) updates and queries.
    - Finding specific bits.

### 3. XOR Properties for Finding Unique/Missing Elements
- `a ^ a = 0`
- `a ^ 0 = a`
- `a ^ b ^ a = b`
- **Applications:**
    - Finding the single element that appears once while all others appear twice: XOR all elements. Pairs cancel, single remains. (See [[Interview/Practice/LeetCode/LC136 - Single Number|LC136]]).
    - Finding a missing number from `0..n` given `n-1` numbers: XOR all numbers from `0..n` with all numbers in the given array. (See [[Interview/Practice/LeetCode/LC268 - Missing Number|LC268]]).

### 4. Indexing with `index & (arr.length - 1)` for Power-of-Two Length Arrays
- If `arr.length` is a power of two (e.g., $2^k$), then `arr.length - 1` is a bitmask of `k` ones (e.g., if length is 8 (0b1000), length-1 is 7 (0b0111)).
- `index & (arr.length - 1)` is equivalent to `index % arr.length`.
- Useful for implementing hash maps with power-of-two table sizes or circular arrays.

## 总结 (Summary)
- Bitwise operations provide low-level control and can lead to highly efficient solutions.
- `&, |, ^, ~, <<, >>` are fundamental.
- `n & (n-1)` clears the LSB '1'. Useful for counting bits and power-of-two checks.
- `n & (-n)` isolates the LSB '1'. Used in Fenwick trees.
- XOR properties (`x^x=0, x^0=x`) are excellent for problems involving finding unique or missing elements among duplicates.

Mastering these techniques is valuable for specific algorithmic optimizations and understanding low-level data structure implementations.
---
Parent: [[Interview/Concept/Algorithms/Bit Manipulation/index|Bit Manipulation Index]]
Related: [[Interview/Practice/LeetCode/LC136 - Single Number|LC136]], [[Interview/Practice/LeetCode/LC191 - Number of 1 Bits|LC191]], [[Interview/Practice/LeetCode/LC231 - Power of Two|LC231]], [[Interview/Practice/LeetCode/LC268 - Missing Number|LC268]]
