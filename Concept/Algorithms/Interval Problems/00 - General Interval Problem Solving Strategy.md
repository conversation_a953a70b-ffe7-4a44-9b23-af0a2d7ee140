---
tags: [concept/algorithms, pattern/interval_problems, topic/array, topic/sorting, topic/greedy, course/labuladong]
aliases: [Interval Problem Strategy, Solving Interval Questions, 区间问题解题方法]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/一个方法解决三道区间问题.md
---

> [!NOTE] Source Annotation
> Content conceptualized from Labuladong's "一个方法解决三道区间问题" ([[Interview/labuladong 的算法笔记/markdown_export_本站简介/第四章、其他常见算法技巧/经典面试题/一个方法解决三道区间问题.md|Source]]), which discusses problems like [[Interview/Practice/LeetCode/LC1288 - Remove Covered Intervals|LC1288]], [[Interview/Practice/LeetCode/LC56 - Merge Intervals|LC56]], and [[Interview/Practice/LeetCode/LC986 - Interval List Intersections|LC986]]. The source file is "loading..." for main content, so this outlines general strategies.

# General Strategy for Solving Interval Problems

Interval problems are common in coding interviews. They typically involve a list of intervals, where each interval is represented by a start and end point `[start, end]`. The goal is often to merge overlapping intervals, find intersections, identify non-overlapping sets, etc.

## 🔑 Core Techniques

Labuladong usually emphasizes two key techniques for interval problems:

1.  **Sorting:**
    *   The most crucial first step is almost always to sort the intervals.
    *   **Common Sorting Criteria:**
        *   Sort by **start points** in ascending order.
        *   If start points are equal, sort by **end points** (either ascending or descending, depending on the problem logic). For example, in "Remove Covered Intervals" (LC1288), if starts are equal, sorting by end points descending helps identify covered intervals more easily.
    *   Sorting brings order to the intervals, allowing for efficient linear scan or merging logic.

2.  **Visualizing / Drawing:**
    *   Manually draw out examples of interval relationships on a number line. This helps to understand all possible cases of overlap, containment, or disjointness.
    *   Consider cases like:
        *   Interval A completely before Interval B.
        *   Interval A completely after Interval B.
        *   Interval A overlaps with Interval B (various ways: A starts first, B starts first, A contains B, B contains A).
        *   Interval A adjacent to Interval B.

```tikz
\begin{tikzpicture}[
    interval/.style={draw, very thick, -latex}, % Use -latex for interval appearance
    line_axis/.style={->, gray, thin},
    label_style/.style={font=\sffamily\small, above=2pt}
]
    % Axis
    \draw[line_axis] (-1,0) -- (11,0) node[right] {Timeline};
    \foreach \x in {0,...,10} { \draw (\x,0.1) -- (\x,-0.1) node[below, font=\tiny] {\x}; }

    % Case 1: A before B, no overlap
    \node[label_style] at (1.5, 1.5) {A: [0,2], B: [3,5] (No Overlap)};
    \draw[interval, blue] (0,1) -- (2,1) node[midway, label_style, black] {A};
    \draw[interval, red] (3,1) -- (5,1) node[midway, label_style, black] {B};

    % Case 2: A overlaps B (A starts first)
    \node[label_style] at (1.5, -0.5) {A: [1,4], B: [3,6] (Overlap)};
    \draw[interval, blue] (1,-1) -- (4,-1) node[midway, label_style, black] {A};
    \draw[interval, red] (3,-1.5) -- (6,-1.5) node[midway, label_style, black] {B};

    % Case 3: A contains B
    \node[label_style] at (7.5, 1.5) {A: [6,10], B: [7,8] (A contains B)};
    \draw[interval, blue] (6,1) -- (10,1) node[midway, label_style, black] {A};
    \draw[interval, red, yshift=-0.2cm] (7,1) -- (8,1) node[midway, label_style, black, yshift=-0.1cm] {B};

    % Case 4: B contains A (similar to above)
    \node[label_style] at (7.5, -0.5) {A: [7,8], B: [6,10] (B contains A)};
    \draw[interval, blue] (7,-1.5) -- (8,-1.5) node[midway, label_style, black] {A};
    \draw[interval, red, yshift=0.2cm] (6,-1.5) -- (10,-1.5) node[midway, label_style, black, yshift=0.1cm] {B};

\end{tikzpicture}
```

## Common Interval Problem Patterns

### 1. Merging Overlapping Intervals (e.g., [[Interview/Practice/LeetCode/LC56 - Merge Intervals|LC56]])
-   **Strategy:**
    1.  Sort intervals by start points.
    2.  Initialize `merged_intervals` with the first interval.
    3.  Iterate through the rest of the sorted intervals:
        -   If the current interval overlaps with the last interval in `merged_intervals`, merge them (update the end point of the last merged interval).
        -   Else (no overlap), add the current interval to `merged_intervals`.
-   **Overlap Condition:** `current_interval.start <= last_merged_interval.end`.
-   **Merge Logic:** `last_merged_interval.end = max(last_merged_interval.end, current_interval.end)`.

### 2. Removing Covered Intervals (e.g., [[Interview/Practice/LeetCode/LC1288 - Remove Covered Intervals|LC1288]])
-   An interval `[a,b]` is covered by `[c,d]` if `c <= a` and `b <= d`.
-   **Strategy:**
    1.  Sort intervals by start points (ascending). If start points are equal, sort by end points (descending). This places larger covering intervals first when starts are the same.
    2.  Iterate through sorted intervals, keeping track of the `previous_interval`. If the `current_interval` is covered by `previous_interval`, increment cover count. Otherwise, update `previous_interval = current_interval`.

### 3. Interval List Intersections (e.g., [[Interview/Practice/LeetCode/LC986 - Interval List Intersections|LC986]])
-   Given two sorted lists of disjoint intervals, find their intersections.
-   **Strategy (Two Pointers):**
    1.  Use two pointers, `i` for `listA` and `j` for `listB`.
    2.  While `i < len(listA)` and `j < len(listB)`:
        -   Calculate potential intersection: `intersect_start = max(A[i].start, B[j].start)`, `intersect_end = min(A[i].end, B[j].end)`.
        -   If `intersect_start <= intersect_end`, an intersection `[intersect_start, intersect_end]` exists; add it to results.
        -   Advance the pointer of the interval that finishes earlier:
            -   If `A[i].end < B[j].end`, increment `i`.
            -   Else, increment `j`.

## 总结 (Summary)
- Solving interval problems usually starts with **sorting** the intervals, typically by their start points.
- **Visualizing** the different ways intervals can relate to each other (overlap, containment, disjoint) is crucial for deriving the correct logic.
- Common patterns involve merging overlapping intervals, finding intersections using two pointers, or identifying covered/non-overlapping intervals.
- Many interval problems can be solved with greedy approaches after sorting, often in $O(N \log N)$ time (dominated by sort) and $O(N)$ or $O(1)$ space (for output or in-place modifications).

---
Parent: [[Interview/Concept/Algorithms/Interval Problems/index|Interval Problem Patterns Index]]
Related: [[Interview/Concept/Algorithms/Greedy Algorithms/Interval Scheduling Pattern|Interval Scheduling Pattern]] (a specific type of interval problem)
