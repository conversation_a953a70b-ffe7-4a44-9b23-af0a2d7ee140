---
tags: [concept/algorithms, concept/randomized_algorithms, concept/prefix_sum, concept/binary_search, type/algorithm, course/labuladong]
aliases: [Weighted Random Choice, 带权重的随机选择, Prefix Sum for Weighted Random]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷数组算法/带权重的随机选择算法.md]].
> This note explains how to implement weighted random selection using prefix sums and binary search.

| [[Interview/Concept/Algorithms/Randomized Algorithms/index|Back to Randomized Algorithms Index]] |

# Weighted Random Selection Algorithm

The problem of selecting an element randomly from a collection, where each element has a different weight (and thus a different probability of being chosen), is a common one. A naive approach might be complex or inefficient. A more elegant solution involves [[Interview/Concept/Algorithms/Array Manipulation/02 - Prefix Sum Array Technique|Prefix Sums]] and [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary Search]].

## 🎯 Problem Statement
Given an array of positive weights `w`, where `w[i]` is the weight of index `i`, implement a function `pickIndex()` that randomly picks an index `i` with probability `w[i] / sum(w)`.

**Example:** `w = [1, 3]`
- Probability of picking index 0: `1 / (1+3) = 0.25`
- Probability of picking index 1: `3 / (1+3) = 0.75`

## 💡 Core Idea: Mapping to Ranges

1.  **Prefix Sums:** Calculate the prefix sum of the weights. Let this be `prefix_sum`. `prefix_sum[i]` will store the sum of weights from `w[0]` to `w[i-1]`. (Using `prefix_sum[0] = 0` for convenience).
    - `w = [1, 3]` $\implies$ `prefix_sum = [0, 1, 4]` (conceptually `[w_0, w_0+w_1]`, so intervals `[0,1)` for index 0, `[1,4)` for index 1).
    - `w = [w_0, w_1, ..., w_{n-1}]`
    - `prefix_sum[0] = 0`
    - `prefix_sum[1] = w_0`
    - `prefix_sum[2] = w_0 + w_1`
    - ...
    - `prefix_sum[n] = w_0 + w_1 + ... + w_{n-1}` (This is `total_weight`)

2.  **Mapping to Intervals:** Each original index `i` corresponds to a range of values in the prefix sum:
    - Index 0: `[prefix_sum[0], prefix_sum[1]-1]` which is `[0, w_0-1]`
    - Index 1: `[prefix_sum[1], prefix_sum[2]-1]` which is `[w_0, w_0+w_1-1]`
    - ...
    - Index `i`: `[prefix_sum[i], prefix_sum[i+1]-1]`
    The length of each interval `prefix_sum[i+1] - prefix_sum[i]` is exactly `w[i]`.

3.  **Random Number Generation:**
    - Generate a random integer `rand_target` between `0` and `total_weight - 1` (inclusive).  (Labuladong's method uses `1` to `total_weight` for 1-based indexing into prefix sums where `prefix_sum[0]` isn't a dummy zero, or targets within ranges like `[1, w_0]`, `[w_0+1, w_0+w_1]`, etc.  If we use 0-indexed random numbers `0` to `total-1` and our prefix sums `ps` are `[w0, w0+w1, ...]` , we search for the first `ps[i] > rand_target`.)

    Let's stick to Labuladong's example `w = [1,3]`. `prefix_sum = [1, 4]` (meaning `ps[0]=w[0]`, `ps[1]=w[0]+w[1]`).
    `total_sum = 4`.
    Generate random `target` in `[1, 4]`.
    - If `target` is 1 (prob 1/4), pick index 0. (Range `[1,1]`)
    - If `target` is 2, 3, or 4 (prob 3/4), pick index 1. (Range `[2,4]`)

4.  **Binary Search for Index:**
    - Use binary search (specifically, a "left-bound" search) on the `prefix_sum` array to find the smallest index `j` such that `rand_target <= prefix_sum[j]`. This index `j` is the randomly picked index.
    - This works because `rand_target` will fall into one of the intervals defined by the prefix sums, and the length of these intervals is proportional to the original weights.

## Example Walkthrough
`w = [1, 3, 2]`
- Weights: `w_0=1, w_1=3, w_2=2`. Total weight = 6.
- Prefix Sums (Labuladong's style, 1-based logic):
    - `ps[0] = w[0] = 1`
    - `ps[1] = w[0] + w[1] = 1 + 3 = 4`
    - `ps[2] = w[0] + w[1] + w[2] = 1 + 3 + 2 = 6`
    So, `prefix_sums_array = [1, 4, 6]`.
- Ranges:
    - Index 0 (weight 1): Corresponds to random target in `[1, 1]`. Length 1.
    - Index 1 (weight 3): Corresponds to random target in `[2, 4]`. Length 3.
    - Index 2 (weight 2): Corresponds to random target in `[5, 6]`. Length 2.

- **Random Pick:**
    - Generate `rand_num` uniformly from `1` to `total_weight (6)`.
    - Say `rand_num = 3`.
    - Binary search for `3` in `[1, 4, 6]` (find left bound for target, or first element >= target):
        - `left_bound([1,4,6], 3)` returns index 1 (because `prefix_sums_array[1] = 4` is the first element $\ge 3$).
        - So, pick original index 1.
    - Say `rand_num = 5`.
        - `left_bound([1,4,6], 5)` returns index 2 (because `prefix_sums_array[2] = 6` is the first element $\ge 5$).
        - So, pick original index 2.

The `left_bound` function detailed in [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary Search Core Template]] is used.

## Python Implementation ([[Interview/Practice/LeetCode/LC528 - Random Pick with Weight|LC528]])
```python
import random
import bisect # Python's built-in binary search module

class Solution:
    def __init__(self, w: list[int]):
        self.prefix_sums = []
        current_sum = 0
        for weight in w:
            current_sum += weight
            self.prefix_sums.append(current_sum)
        self.total_sum = current_sum

    def pickIndex(self) -> int:
        # Generate a random number between 1 and total_sum (inclusive)
        # Labuladong's reasoning: imagine weights as line segments.
        # w=[1,3] -> segment1 (len 1), segment2 (len 3). Total length 4.
        # Pick a point from 1 to 4.
        # If 1 -> pick index 0.
        # If 2,3,4 -> pick index 1.
        target = random.randint(1, self.total_sum)

        # Find the index of the first prefix sum that is >= target
        # This is a left-bound binary search.
        # bisect_left(a, x) returns an insertion point which comes before (to the left of) any existing entries of x in a and after (to the right of) any existing entries of x in a.
        # This is exactly what we need for finding the first prefix_sum >= target.

        # Example: w = [1,3,2] -> prefix_sums = [1,4,6]
        # target = 1 -> bisect_left([1,4,6], 1) -> index 0
        # target = 2 -> bisect_left([1,4,6], 2) -> index 1
        # target = 3 -> bisect_left([1,4,6], 3) -> index 1
        # target = 4 -> bisect_left([1,4,6], 4) -> index 1
        # target = 5 -> bisect_left([1,4,6], 5) -> index 2
        # target = 6 -> bisect_left([1,4,6], 6) -> index 2

        return bisect.bisect_left(self.prefix_sums, target)

    # Manual left_bound implementation (for understanding)
    # def _left_bound_search(self, arr: list[int], target_val: int) -> int:
    #     left, right = 0, len(arr) - 1 
    #     # Search for target_val in arr, return index of first element >= target_val
    #     # If standard left_bound from Labuladong:
    #     # left_idx, right_idx = 0, len(arr)
    #     # while left_idx < right_idx:
    #     #    mid_idx = left_idx + (right_idx - left_idx) // 2
    #     #    if arr[mid_idx] < target_val:
    #     #        left_idx = mid_idx + 1
    #     #    else: # arr[mid_idx] >= target_val
    #     #        right_idx = mid_idx
    #     # return left_idx 
    #     # This matches bisect_left's behavior.

# Your Solution object will be instantiated and called as such:
# obj = Solution(w)
# param_1 = obj.pickIndex()
```
Labuladong's visualization panel `div_random-pick-with-weight` shows this:
- Weights `w` are converted to prefix sums.
- A random number `p` is generated within the total sum range.
- Binary search (`left_bound`) is used on prefix sums to find the index corresponding to `p`.

## Complexity
- **Constructor (`__init__`)**:
    - Time: $O(N)$ to compute prefix sums, where $N$ is the number of weights.
    - Space: $O(N)$ to store prefix sums.
- **`pickIndex()`**:
    - Time: $O(\log N)$ for binary search (`bisect_left`). Random number generation is $O(1)$.
    - Space: $O(1)$ (if binary search is iterative or `bisect_left` is optimized).

## 总结 (Summary)
- Weighted random selection can be implemented efficiently by mapping weights to cumulative ranges using prefix sums.
- A random number is generated within the total sum of weights.
- Binary search (specifically, finding the left bound or using `bisect_left`) on the prefix sum array identifies which original index corresponds to the generated random number.
- This approach offers $O(N)$ preprocessing time and $O(\log N)$ time per pick.

---
| [[Interview/Concept/Algorithms/Randomized Algorithms/index|Back to Randomized Algorithms Index]] |

Related Concepts:
- [[Interview/Concept/Algorithms/Array Manipulation/02 - Prefix Sum Array Technique|Prefix Sum Array]]
- [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary Search (Left Bound)]]
- [[Interview/Practice/LeetCode/LC528 - Random Pick with Weight|LC528 - Random Pick with Weight]]

