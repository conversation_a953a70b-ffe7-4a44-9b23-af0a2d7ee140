---
tags: [index, concept/algorithms, concept/randomized_algorithms]
aliases: [Randomized Algorithms Index]
---

# Randomized Algorithms

This section covers algorithms that incorporate randomness as part of their logic.

## Core Concepts:
- [[Interview/Concept/Algorithms/Randomized Algorithms/00 - Weighted Random Selection|Weighted Random Selection]]
- [[Interview/Concept/Algorithms/Randomized Algorithms/01 - Concepts in Game Random Algorithms|Concepts in Game Random Algorithms]]

## Visualization
```mermaid
graph TD
    RandAlgos["Randomized Algorithms"] --> WRS["[[Interview/Concept/Algorithms/Randomized Algorithms/00 - Weighted Random Selection|Weighted Random Selection]]"]

    classDef main fill:#fff0e6,stroke:#ff8c00,stroke-width:2px;
    class <PERSON>A<PERSON>gos main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
