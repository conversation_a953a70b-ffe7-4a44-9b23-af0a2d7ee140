---
tags: [concept/algorithms, pattern/constraint_satisfaction, topic/problem_solving, topic/optimization]
aliases: [Constraint Satisfaction, CSP, Constraint Analysis, Feasibility Analysis]
---

# Constraint Satisfaction

## 🎯 Core Concept

**Constraint Satisfaction** is a problem-solving pattern where we systematically identify and work within the constraints of a problem to find valid solutions. The key insight is to use constraints to **reduce the search space** dramatically.

```tikz
\begin{tikzpicture}[
    space_box/.style={rectangle, draw, fill=red!20, font=\sffamily\small, text width=3cm, align=center, minimum height=2cm},
    constraint_box/.style={rectangle, draw, fill=blue!20, font=\sffamily\small, text width=2.5cm, align=center},
    solution_box/.style={rectangle, draw, fill=green!30, font=\sffamily\small, text width=2cm, align=center},
    arrow/.style={->, thick, blue}
]

\node[space_box] (original) at (0, 2) {
    \textbf{Original Problem}\\[0.5em]
    Huge search space\\
    Many possibilities\\
    Exponential complexity
};

\node[constraint_box] (constraints) at (4, 3.5) {
    \textbf{Constraint 1}\\
    Eliminate invalid\\
    candidates
};

\node[constraint_box] (constraints2) at (4, 0.5) {
    \textbf{Constraint 2}\\
    Further reduce\\
    search space
};

\node[solution_box] (solution) at (8, 2) {
    \textbf{Reduced Space}\\[0.5em]
    Few candidates\\
    Polynomial or\\
    constant time
};

\draw[arrow] (original) -- (constraints);
\draw[arrow] (original) -- (constraints2);
\draw[arrow] (constraints) -- (solution);
\draw[arrow] (constraints2) -- (solution);

\end{tikzpicture}
```

## 🔍 Pattern Recognition

### When to Use Constraint Satisfaction

1. **Large search space** that seems intractable
2. **Clear constraints** that must be satisfied
3. **Optimization problems** with feasibility requirements
4. **Combinatorial problems** with restrictions

### Key Characteristics

- **Constraint identification**: What must be true for a solution to exist?
- **Feasibility analysis**: Which candidates can possibly work?
- **Search space reduction**: Eliminate impossible cases early
- **Systematic enumeration**: Check remaining candidates efficiently

## 💡 Problem-Solving Framework

```tikz
\begin{tikzpicture}[
    step_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4cm, align=left, minimum height=1.5cm},
    arrow/.style={->, thick, blue}
]

\node[step_box] (step1) at (0, 4) {
    \textbf{1. Identify Constraints}\\
    • What conditions must hold?\\
    • What makes a solution invalid?
};

\node[step_box] (step2) at (5, 4) {
    \textbf{2. Analyze Implications}\\
    • What do constraints tell us?\\
    • How do they limit possibilities?
};

\node[step_box] (step3) at (0, 2) {
    \textbf{3. Reduce Candidates}\\
    • Eliminate impossible cases\\
    • Focus on viable options only
};

\node[step_box] (step4) at (5, 2) {
    \textbf{4. Enumerate & Check}\\
    • Systematically test candidates\\
    • Find optimal among valid ones
};

\draw[arrow] (step1) -- (step2);
\draw[arrow] (step2) -- (step4);
\draw[arrow] (step4) -- (step3);
\draw[arrow] (step3) -- (step1);

\end{tikzpicture}
```

## 🎲 Example: Domino Rotations

**Problem**: Make all dominoes show the same value on one side.

**Constraint Analysis**:
1. **Constraint**: Target value must appear in every domino
2. **Implication**: Target ∈ {tops[0], bottoms[0]} (must appear in position 0)
3. **Reduction**: Only 2 candidates instead of 6 possible values
4. **Enumeration**: Check both candidates, return minimum

```python
def constraint_satisfaction_example():
    # Instead of checking all 6 possible values (1-6)
    # Constraint analysis tells us only 2 can work
    candidates = [tops[0], bottoms[0]]  # Massive reduction!
    
    for candidate in candidates:
        if is_feasible(candidate):
            return min_cost(candidate)
    return -1
```

## 🌟 Applications

### Classic Problems
- **[[LC1007 - Minimum Domino Rotations For Equal Row]]**: Constraint reduces 6 candidates to 2
- **N-Queens**: Constraint propagation eliminates invalid placements
- **Sudoku Solver**: Constraints guide valid number placement
- **Graph Coloring**: Constraints determine valid color assignments

### Problem Types
1. **Optimization with constraints**: Find best solution within limits
2. **Feasibility checking**: Determine if solution exists
3. **Combinatorial search**: Explore valid combinations only
4. **Resource allocation**: Distribute limited resources optimally

## 🧠 Key Insights

### Why It Works
- **Pruning**: Eliminate large portions of search space early
- **Focus**: Concentrate effort on promising candidates
- **Efficiency**: Transform exponential problems to polynomial
- **Clarity**: Make problem structure more apparent

### Common Patterns
1. **Necessary conditions**: What must be true for any solution?
2. **Sufficient conditions**: What guarantees a valid solution?
3. **Invariants**: What properties are preserved throughout?
4. **Bounds**: What limits exist on valid solutions?

## 🔗 Related Concepts

- **[[Greedy Algorithms]]**: Often combined with constraint satisfaction
- **[[Case Analysis]]**: Systematic enumeration of constrained cases
- **[[Dynamic Programming]]**: Constraints define state transitions
- **[[Backtracking]]**: Constraint checking guides search pruning

Constraint satisfaction is a **meta-strategy** that can dramatically improve the efficiency of many algorithmic approaches!
