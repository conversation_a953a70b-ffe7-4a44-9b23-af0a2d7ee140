---
tags: [concept/algorithms, pattern/state_transition, topic/dynamic_programming, topic/simulation, topic/state_machine]
aliases: [State Transition, State Machine, Finite State Automaton, State Evolution]
---

# State Transition

## 🎯 Core Concept

**State Transition** is a problem-solving pattern where we model a system as a sequence of states, with well-defined rules for how the system moves from one state to another. This approach is particularly powerful for problems involving step-by-step evolution or transformation.

```tikz
\begin{tikzpicture}[
    state/.style={circle, draw, minimum size=1.2cm, font=\sffamily\small, fill=blue!30},
    transition/.style={->, thick, blue},
    label/.style={font=\tiny, above}
]

% State diagram example
\node[state] (s0) at (0, 2) {S₀};
\node[state] (s1) at (3, 3) {S₁};
\node[state] (s2) at (6, 2) {S₂};
\node[state] (s3) at (3, 1) {S₃};

% Transitions
\draw[transition] (s0) to[bend left] node[label] {action₁} (s1);
\draw[transition] (s1) to[bend left] node[label] {action₂} (s2);
\draw[transition] (s2) to[bend left] node[label] {action₃} (s3);
\draw[transition] (s3) to[bend left] node[label] {action₄} (s0);
\draw[transition] (s1) to[bend right] node[label] {action₅} (s3);

\node at (3, 4.5) {\bfseries State Transition System};
\node at (3, -0.5) {\small States evolve according to transition rules};

\end{tikzpicture}
```

## 🔍 Key Components

### 1. **State Representation**
- **State Space**: All possible states the system can be in
- **State Variables**: Data that defines the current state
- **State Encoding**: How to represent states efficiently

### 2. **Transition Rules**
- **Deterministic**: Given state and input, next state is uniquely determined
- **Non-deterministic**: Multiple possible next states
- **Conditional**: Transitions depend on additional conditions

### 3. **Evolution Process**
- **Time Steps**: Discrete moments when transitions occur
- **Input/Events**: What triggers state changes
- **Output**: What the system produces in each state

## 💡 Common State Transition Patterns

### Pattern 1: **Linear State Evolution**

```tikz
\begin{tikzpicture}[
    state_box/.style={rectangle, draw, minimum width=1.5cm, minimum height=0.8cm, font=\sffamily\small, fill=green!30},
    arrow/.style={->, thick, blue}
]

\node[state_box] (s1) at (0, 1) {State 1};
\node[state_box] (s2) at (3, 1) {State 2};
\node[state_box] (s3) at (6, 1) {State 3};
\node[state_box] (s4) at (9, 1) {State 4};

\draw[arrow] (s1) -- (s2);
\draw[arrow] (s2) -- (s3);
\draw[arrow] (s3) -- (s4);

\node at (4.5, 2) {\bfseries Linear Progression};
\node at (4.5, 0) {\small Each state leads to exactly one next state};

\end{tikzpicture}
```

**Examples:**
- **Fibonacci sequence**: F(n) = F(n-1) + F(n-2)
- **String transformations**: Each character evolves independently
- **Game levels**: Progress through sequential stages

### Pattern 2: **Branching State Evolution**

```tikz
\begin{tikzpicture}[
    state_box/.style={rectangle, draw, minimum width=1.2cm, minimum height=0.6cm, font=\tiny, fill=yellow!30},
    arrow/.style={->, thick, red}
]

\node[state_box] (root) at (4, 3) {Initial};
\node[state_box] (l1) at (2, 2) {Branch A};
\node[state_box] (r1) at (6, 2) {Branch B};
\node[state_box] (ll) at (1, 1) {A1};
\node[state_box] (lr) at (3, 1) {A2};
\node[state_box] (rl) at (5, 1) {B1};
\node[state_box] (rr) at (7, 1) {B2};

\draw[arrow] (root) -- (l1);
\draw[arrow] (root) -- (r1);
\draw[arrow] (l1) -- (ll);
\draw[arrow] (l1) -- (lr);
\draw[arrow] (r1) -- (rl);
\draw[arrow] (r1) -- (rr);

\node at (4, 4) {\bfseries Branching Evolution};
\node at (4, 0) {\small Multiple possible next states};

\end{tikzpicture}
```

**Examples:**
- **Decision trees**: Different choices lead to different outcomes
- **Game states**: Player actions create different game states
- **Parsing**: Different grammar rules create different parse states

### Pattern 3: **Cyclic State Evolution**

```tikz
\begin{tikzpicture}[
    state/.style={circle, draw, minimum size=1cm, font=\sffamily\small, fill=orange!30},
    arrow/.style={->, thick, green}
]

\node[state] (a) at (0, 2) {A};
\node[state] (b) at (2, 3) {B};
\node[state] (c) at (4, 2) {C};
\node[state] (d) at (2, 1) {D};

\draw[arrow] (a) -- (b);
\draw[arrow] (b) -- (c);
\draw[arrow] (c) -- (d);
\draw[arrow] (d) -- (a);

\node at (2, 4.5) {\bfseries Cyclic Evolution};
\node at (2, -0.5) {\small States form cycles};

\end{tikzpicture}
```

**Examples:**
- **Modular arithmetic**: States cycle through remainders
- **Periodic processes**: Day/night cycles, seasons
- **State machines**: Finite automata with loops

## 🌟 Applications in Problem Solving

### 1. **Dynamic Programming Problems**

State transitions naturally model DP recurrence relations:

```python
# Example: Climbing stairs with state transition
def climb_stairs(n):
    # State: current step
    # Transition: step i can come from step i-1 or i-2
    if n <= 2:
        return n
    
    prev2, prev1 = 1, 2
    for i in range(3, n + 1):
        current = prev1 + prev2  # Transition rule
        prev2, prev1 = prev1, current
    
    return prev1
```

### 2. **String/Array Transformation Problems**

Model transformations as state evolution:

```python
# Example: Character frequency evolution
def transform_string(s, t):
    # State: frequency count of each character
    # Transition: apply transformation rules
    count = [0] * 26
    for char in s:
        count[ord(char) - ord('a')] += 1
    
    for _ in range(t):
        new_count = [0] * 26
        # Apply transition rules
        for i in range(25):
            new_count[i + 1] += count[i]  # Regular transition
        new_count[0] += count[25]  # Special transition
        new_count[1] += count[25]
        count = new_count
    
    return sum(count)
```

### 3. **Game State Problems**

Model game progression as state transitions:

```python
# Example: Game with multiple states
class GameState:
    def __init__(self, health, score, level):
        self.health = health
        self.score = score
        self.level = level
    
    def take_action(self, action):
        # Transition to new state based on action
        if action == "attack":
            return GameState(self.health - 1, self.score + 10, self.level)
        elif action == "defend":
            return GameState(self.health, self.score + 5, self.level)
        # ... more transitions
```

## 🧠 Advanced Techniques

### Matrix Exponentiation for Linear Transitions

For linear state transitions, we can use matrix exponentiation:

```python
def matrix_power(matrix, n):
    """Compute matrix^n efficiently"""
    size = len(matrix)
    result = [[1 if i == j else 0 for j in range(size)] for i in range(size)]
    
    while n > 0:
        if n & 1:
            result = matrix_multiply(result, matrix)
        matrix = matrix_multiply(matrix, matrix)
        n >>= 1
    
    return result

# Apply to state transition
def fast_state_evolution(initial_state, transition_matrix, steps):
    """Evolve state using matrix exponentiation"""
    evolved_matrix = matrix_power(transition_matrix, steps)
    return matrix_vector_multiply(evolved_matrix, initial_state)
```

### Memoization for Complex Transitions

Cache computed states to avoid recomputation:

```python
from functools import lru_cache

@lru_cache(maxsize=None)
def evolve_state(state, steps_remaining):
    """Memoized state evolution"""
    if steps_remaining == 0:
        return state
    
    # Apply transition and recurse
    next_state = apply_transition(state)
    return evolve_state(next_state, steps_remaining - 1)
```

## 🎯 Problem-Solving Framework

### Step 1: **Identify State Space**
- What are all possible states?
- How can we represent states efficiently?
- What are the initial and final states?

### Step 2: **Define Transition Rules**
- What actions/events cause state changes?
- Are transitions deterministic or probabilistic?
- Are there any constraints on transitions?

### Step 3: **Choose Evolution Strategy**
- **Simulation**: Step-by-step evolution
- **Mathematical**: Closed-form solutions
- **Optimization**: Matrix exponentiation, memoization

### Step 4: **Implement and Optimize**
- Start with simple simulation
- Identify bottlenecks and optimize
- Consider space-time trade-offs

## 🔗 Related Concepts

- **[[Dynamic Programming]]**: State transitions model DP recurrences
- **[[Finite State Automata]]**: Formal model of state transitions
- **[[Matrix Exponentiation]]**: Fast computation for linear transitions
- **[[Frequency Counting]]**: States often represent frequency distributions
- **[[Simulation]]**: Step-by-step state evolution

## 🌟 Key Insights

### When to Use State Transition
- **Sequential processes**: Problems involving step-by-step evolution
- **System modeling**: When you can identify distinct system states
- **Optimization problems**: When optimal substructure exists
- **Counting problems**: When you need to count configurations

### Advantages
- **Clear modeling**: Makes problem structure explicit
- **Systematic approach**: Provides framework for solution
- **Optimization opportunities**: Enables mathematical optimizations
- **Debugging**: Easy to trace state evolution

### Common Pitfalls
- **State explosion**: Too many states to handle efficiently
- **Complex transitions**: Overly complicated transition rules
- **Missing states**: Incomplete state space definition
- **Inefficient representation**: Poor choice of state encoding

State transition is a powerful abstraction that transforms complex sequential problems into manageable, systematic solutions!
