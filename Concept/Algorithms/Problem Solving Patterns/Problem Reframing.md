---
tags: [concept/problem_solving, pattern/reframing, topic/algorithmic_thinking, topic/optimization]
aliases: [Problem Reframing, Problem Transformation, Perspective Shift, Reformulation]
---

# Problem Reframing

## 🎯 Core Concept

**Problem Reframing** is the art of transforming how we think about a problem to reveal simpler, more efficient solutions. Instead of solving the problem as originally stated, we find an equivalent but easier formulation.

```tikz
\begin{tikzpicture}[
    problem_box/.style={rectangle, draw, fill=red!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2.5cm},
    reframe_box/.style={rectangle, draw, fill=blue!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2.5cm},
    solution_box/.style={rectangle, draw, fill=green!30, font=\sffamily\small, text width=4cm, align=center, minimum height=2.5cm},
    arrow/.style={->, thick, red, line width=2pt}
]

\node[problem_box] (original) at (0, 2) {
    \textbf{Original Problem}\\[0.5em]
    Complex formulation\\
    Difficult constraints\\
    Unclear approach\\
    High complexity
};

\node[reframe_box] (reframed) at (5, 2) {
    \textbf{Reframed Problem}\\[0.5em]
    Simpler formulation\\
    Clear structure\\
    Obvious approach\\
    Lower complexity
};

\node[solution_box] (solution) at (10, 2) {
    \textbf{Elegant Solution}\\[0.5em]
    Efficient algorithm\\
    Clean implementation\\
    Easy to understand\\
    Optimal performance
};

\draw[arrow] (original) -- (reframed);
\draw[arrow] (reframed) -- (solution);

\node at (2.5, 0.5) {\small Reframe};
\node at (7.5, 0.5) {\small Solve};

\end{tikzpicture}
```

## 🔍 When to Use Problem Reframing

### Recognition Patterns
1. **Complex constraints** that seem to make the problem intractable
2. **Multiple objectives** that conflict with each other
3. **Exponential search spaces** that need to be reduced
4. **Indirect relationships** between input and desired output

### Key Indicators
- The "obvious" solution is too slow or complex
- Problem seems harder than similar problems you've solved
- Constraints appear to make the problem impossible
- Multiple approaches all lead to the same complexity wall

## 💡 Reframing Techniques

### 1. **Constraint Relaxation/Addition**

```tikz
\begin{tikzpicture}[
    technique_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=5cm, align=left, minimum height=2cm},
    example_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\tiny, text width=4cm, align=left}
]

\node[technique_box] (relax) at (0, 3) {
    \textbf{Constraint Relaxation}\\[0.3em]
    • Remove restrictive constraints\\
    • Solve easier version first\\
    • Add constraints back gradually\\
    • Find patterns in relaxed solution
};

\node[technique_box] (add) at (6, 3) {
    \textbf{Constraint Addition}\\[0.3em]
    • Add helpful constraints\\
    • Restrict search space\\
    • Create structure in problem\\
    • Enable specialized algorithms
};

\node[example_box] at (0, 0.5) {
    \textbf{Example:}\\
    Original: Find shortest path\\
    with complex restrictions\\
    \\
    Relaxed: Find any path first,\\
    then optimize
};

\node[example_box] at (6, 0.5) {
    \textbf{Example:}\\
    Original: Optimize over all\\
    possible configurations\\
    \\
    Added: Only consider\\
    configurations with property X
};

\end{tikzpicture}
```

### 2. **Perspective Shift**

Transform what you're optimizing or how you view the problem:

```python
# Original: Find best path from A to B
def find_best_path(start, end):
    # Complex pathfinding with many constraints
    pass

# Reframed: Find worst edge that must be used
def find_bottleneck_edge(start, end):
    # Much simpler - just find connected component
    # and return minimum edge weight
    pass
```

### 3. **Dual Problem Formulation**

Sometimes the "opposite" problem is easier:

```tikz
\begin{tikzpicture}[
    dual_box/.style={rectangle, draw, fill=orange!20, font=\sffamily\small, text width=4.5cm, align=center, minimum height=2cm},
    arrow/.style={<->, thick, blue}
]

\node[dual_box] (primal) at (0, 2) {
    \textbf{Primal Problem}\\[0.5em]
    "What's the maximum\\
    we can achieve?"\\[0.5em]
    Often complex optimization
};

\node[dual_box] (dual) at (6, 2) {
    \textbf{Dual Problem}\\[0.5em]
    "What's the minimum\\
    constraint violation?"\\[0.5em]
    Often simpler analysis
};

\draw[arrow] (primal) -- (dual);

\end{tikzpicture}
```

### 4. **Abstraction Level Change**

Move to higher or lower level of abstraction:

- **Higher Level**: Focus on properties rather than details
- **Lower Level**: Break into smaller, manageable pieces
- **Different Domain**: Transform to familiar problem space

## 🎲 Classic Examples

### Example 1: [[LC2492 - Minimum Score of a Path Between Two Cities]]

**Original Framing**: "Find the path from 1 to n that maximizes the minimum edge weight"
- Seems like complex pathfinding
- Need to explore all possible paths
- Exponential complexity

**Reframed**: "Find the minimum edge weight in the connected component containing both 1 and n"
- Simple connectivity problem
- Single traversal needed
- Linear complexity

### Example 2: Two Sum Problem

**Original Framing**: "Find two numbers that sum to target"
- Nested loops approach O(n²)
- Check all pairs

**Reframed**: "For each number x, check if (target - x) exists"
- Hash table approach O(n)
- Transform search into lookup

### Example 3: Longest Increasing Subsequence

**Original Framing**: "Find longest increasing subsequence"
- Dynamic programming O(n²)
- Check all previous elements

**Reframed**: "Maintain smallest tail for each length"
- Binary search approach O(n log n)
- Transform to patience sorting

## 🧠 Reframing Strategies

### 1. **Change the Question**
```python
# Instead of: "How do I solve this directly?"
# Ask: "What would make this problem trivial?"
# Or: "What's the simplest version of this problem?"
```

### 2. **Invert the Problem**
```python
# Instead of: "Find the best solution"
# Ask: "What makes a solution bad?"
# Or: "How do I avoid the worst case?"
```

### 3. **Change the Data Structure**
```python
# Instead of: Working with arrays
# Consider: Graphs, trees, hash tables, heaps
# Each structure reveals different properties
```

### 4. **Change the Algorithm Paradigm**
```python
# Instead of: Brute force search
# Consider: Greedy, DP, divide-and-conquer
# Each paradigm suggests different framings
```

## 🌟 Benefits of Problem Reframing

### Complexity Reduction
- **Exponential → Polynomial**: Better algorithm class
- **Polynomial → Linear**: Practical performance gains
- **Complex → Simple**: Easier implementation and debugging

### Insight Generation
- **Pattern Recognition**: See connections to known problems
- **Generalization**: Solution applies to broader class
- **Optimization**: Find multiple improvement opportunities

### Problem-Solving Skills
- **Flexibility**: Approach problems from multiple angles
- **Creativity**: Generate novel solution approaches
- **Efficiency**: Avoid getting stuck on hard formulations

## 🎯 Practice Framework

### Step 1: Understand Deeply
- What is the problem really asking?
- What are the essential constraints?
- What would an ideal solution look like?

### Step 2: Question Assumptions
- Do I need to solve it exactly as stated?
- What if I relaxed constraint X?
- What if I optimized for Y instead of Z?

### Step 3: Explore Alternatives
- How would I explain this to someone else?
- What's the simplest version of this problem?
- What problems does this remind me of?

### Step 4: Test Reframings
- Does the reframed version capture the essence?
- Is it actually easier to solve?
- Does it suggest a clear algorithm?

## 🔗 Related Concepts

- **[[Constraint Satisfaction]]**: Working within problem constraints
- **[[Algorithm Design Paradigms]]**: Different approaches suggest different framings
- **[[Optimization Theory]]**: Dual problems and reformulations
- **[[Abstract Thinking]]**: Moving between levels of abstraction

Problem reframing is often the difference between an intractable problem and an elegant solution!
