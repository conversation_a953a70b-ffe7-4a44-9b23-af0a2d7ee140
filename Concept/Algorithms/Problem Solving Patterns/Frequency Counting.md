---
tags: [concept/algorithms, pattern/frequency_counting, topic/counting, topic/hash_table, topic/array]
aliases: [Frequency Counting, Frequency Analysis, Count Tracking, Histogram]
---

# Frequency Counting

## 🎯 Core Concept

**Frequency Counting** is a fundamental problem-solving pattern where we track how often each element appears in a dataset. Instead of working with the actual data, we work with counts, which often simplifies the problem and reduces space/time complexity.

```tikz
\begin{tikzpicture}[
    data_box/.style={rectangle, draw, minimum size=0.8cm, font=\sffamily\small, fill=blue!30},
    count_box/.style={rectangle, draw, minimum size=0.8cm, font=\sffamily\small, fill=green!30},
    arrow/.style={->, thick, red},
    label_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=3cm, align=center}
]

% Original data
\node at (2, 5) {\bfseries Original Data};
\node[data_box] at (0, 4) {a};
\node[data_box] at (0.8, 4) {b};
\node[data_box] at (1.6, 4) {a};
\node[data_box] at (2.4, 4) {c};
\node[data_box] at (3.2, 4) {a};
\node[data_box] at (4, 4) {b};

\draw[arrow] (2, 3.5) -- (2, 2.5);

% Frequency representation
\node at (2, 2) {\bfseries Frequency Count};
\node at (0.5, 1.5) {\tiny a};
\node at (1.5, 1.5) {\tiny b};
\node at (2.5, 1.5) {\tiny c};
\node[count_box] at (0.5, 1) {3};
\node[count_box] at (1.5, 1) {2};
\node[count_box] at (2.5, 1) {1};

\node[label_box] at (6, 2.5) {
    \textbf{Benefits:}\\
    • Compact representation\\
    • Fast operations\\
    • Pattern recognition
};

\end{tikzpicture}
```

## 🔍 Key Characteristics

### Why Frequency Counting Works
1. **Abstraction**: Focus on "how many" rather than "what exactly"
2. **Compression**: Reduce data size when many duplicates exist
3. **Pattern Recognition**: Frequencies reveal important patterns
4. **Efficiency**: Many operations become O(k) instead of O(n), where k << n

### Common Data Structures
- **Arrays**: When elements have small, known range (e.g., characters a-z)
- **Hash Tables**: When elements can be arbitrary
- **Counters**: Specialized data structures for counting

## 💡 Frequency Counting Patterns

### Pattern 1: **Character Frequency**

```python
def count_characters(s):
    """Count frequency of each character"""
    # Array approach (for a-z)
    count = [0] * 26
    for char in s:
        count[ord(char) - ord('a')] += 1
    return count

    # Hash table approach (for any characters)
    from collections import Counter
    return Counter(s)
```

### Pattern 2: **Element Frequency with Operations**

```python
def frequency_operations(arr):
    """Perform operations based on frequencies"""
    freq = {}
    for num in arr:
        freq[num] = freq.get(num, 0) + 1
    
    # Find most frequent element
    max_freq = max(freq.values())
    most_frequent = [k for k, v in freq.items() if v == max_freq]
    
    return most_frequent
```

### Pattern 3: **Frequency Evolution**

```python
def evolve_frequencies(initial_freq, transformations):
    """Update frequencies through transformations"""
    current_freq = initial_freq.copy()
    
    for transform in transformations:
        new_freq = {}
        for element, count in current_freq.items():
            # Apply transformation rule
            new_element = transform(element)
            new_freq[new_element] = new_freq.get(new_element, 0) + count
        current_freq = new_freq
    
    return current_freq
```

## 🌟 Applications and Use Cases

### 1. **String Problems**

```tikz
\begin{tikzpicture}[
    problem_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4cm, align=center, minimum height=2cm},
    solution_box/.style={rectangle, draw, fill=green!20, font=\sffamily\small, text width=3.5cm, align=center}
]

\node[problem_box] (anagram) at (0, 3) {
    \textbf{Anagram Detection}\\[0.5em]
    Check if two strings\\
    are anagrams of\\
    each other
};

\node[problem_box] (substring) at (5, 3) {
    \textbf{Substring Matching}\\[0.5em]
    Find all anagrams\\
    of pattern in text\\
    (sliding window)
};

\node[solution_box] at (0, 0.5) {
    Compare character\\
    frequency counts\\
    of both strings
};

\node[solution_box] at (5, 0.5) {
    Maintain frequency\\
    window and compare\\
    with pattern frequency
};

\end{tikzpicture}
```

**Example: Anagram Detection**
```python
def are_anagrams(s1, s2):
    if len(s1) != len(s2):
        return False
    
    count1 = [0] * 26
    count2 = [0] * 26
    
    for i in range(len(s1)):
        count1[ord(s1[i]) - ord('a')] += 1
        count2[ord(s2[i]) - ord('a')] += 1
    
    return count1 == count2
```

### 2. **Array Problems**

**Example: Find All Duplicates**
```python
def find_duplicates(nums):
    freq = {}
    for num in nums:
        freq[num] = freq.get(num, 0) + 1
    
    return [num for num, count in freq.items() if count > 1]
```

### 3. **Transformation Problems**

**Example: Character Evolution ([[LC3335 - Total Characters in String After Transformations I]])**
```python
def transform_string_length(s, t):
    # Track character frequencies instead of actual string
    count = [0] * 26
    for char in s:
        count[ord(char) - ord('a')] += 1
    
    for _ in range(t):
        new_count = [0] * 26
        # Apply transformation rules to frequencies
        for i in range(25):
            new_count[i + 1] += count[i]  # Regular: i -> i+1
        new_count[0] += count[25]  # Special: z -> a
        new_count[1] += count[25]  # Special: z -> b
        count = new_count
    
    return sum(count)
```

## 🧠 Advanced Techniques

### 1. **Frequency Difference Tracking**

Instead of maintaining absolute frequencies, track differences:

```python
def frequency_difference_approach(s1, s2):
    """Check if s1 and s2 have same character frequencies"""
    diff = [0] * 26
    
    # Add frequencies from s1, subtract from s2
    for char in s1:
        diff[ord(char) - ord('a')] += 1
    for char in s2:
        diff[ord(char) - ord('a')] -= 1
    
    # If all differences are 0, frequencies match
    return all(d == 0 for d in diff)
```

### 2. **Sliding Window with Frequency**

Maintain frequency count in a sliding window:

```python
def sliding_window_frequency(text, pattern):
    """Find all anagrams of pattern in text"""
    if len(pattern) > len(text):
        return []
    
    pattern_freq = [0] * 26
    window_freq = [0] * 26
    
    # Initialize pattern frequency
    for char in pattern:
        pattern_freq[ord(char) - ord('a')] += 1
    
    result = []
    window_size = len(pattern)
    
    for i in range(len(text)):
        # Add current character to window
        window_freq[ord(text[i]) - ord('a')] += 1
        
        # Remove character that's out of window
        if i >= window_size:
            window_freq[ord(text[i - window_size]) - ord('a')] -= 1
        
        # Check if current window matches pattern
        if i >= window_size - 1 and window_freq == pattern_freq:
            result.append(i - window_size + 1)
    
    return result
```

### 3. **Frequency-Based State Machines**

Use frequencies to represent states in state machines:

```python
def frequency_state_machine(initial_state, transitions, steps):
    """Evolve frequency state through transitions"""
    current_state = initial_state.copy()
    
    for _ in range(steps):
        next_state = {}
        for element, count in current_state.items():
            # Apply transition rules
            for next_element in transitions.get(element, [element]):
                next_state[next_element] = next_state.get(next_element, 0) + count
        current_state = next_state
    
    return current_state
```

## 🎯 Problem-Solving Framework

### Step 1: **Identify Frequency Opportunity**
- Are you working with collections of similar elements?
- Do you need to track "how many" rather than "which ones"?
- Can the problem be solved by comparing counts?

### Step 2: **Choose Data Structure**
- **Fixed range**: Use arrays (e.g., a-z characters)
- **Unknown range**: Use hash tables/dictionaries
- **Special requirements**: Consider specialized counters

### Step 3: **Design Frequency Operations**
- **Initialization**: How to build initial frequency count
- **Updates**: How frequencies change over time
- **Queries**: What information to extract from frequencies

### Step 4: **Optimize**
- **Space**: Can you use difference tracking?
- **Time**: Can you avoid rebuilding frequencies?
- **Memory**: Can you reuse frequency arrays?

## 🔗 Related Concepts

- **[[Hash Tables]]**: Primary data structure for frequency counting
- **[[Sliding Window]]**: Often combined with frequency tracking
- **[[State Transition]]**: Frequencies can represent states
- **[[Two Pointers]]**: Sometimes used with frequency arrays
- **[[Modular Arithmetic]]**: For handling large frequency values

## 🌟 Key Insights

### When to Use Frequency Counting
- **Duplicate detection**: Finding repeated elements
- **Pattern matching**: Comparing element distributions
- **Transformation tracking**: Following element evolution
- **Optimization**: When actual order doesn't matter

### Advantages
- **Space efficiency**: Compact representation of large datasets
- **Fast operations**: O(k) operations where k is unique elements
- **Pattern recognition**: Reveals distribution patterns
- **Simplification**: Reduces complex problems to counting

### Common Pitfalls
- **Order matters**: When sequence is important, frequencies aren't enough
- **Overflow**: Large counts may exceed integer limits
- **Hash collisions**: Poor hash function performance
- **Memory usage**: Hash tables can have overhead

### Optimization Tips
- **Use arrays when possible**: Faster than hash tables for small, known ranges
- **Difference tracking**: More efficient for comparison problems
- **Lazy evaluation**: Don't compute frequencies until needed
- **Reuse structures**: Avoid repeated allocation/deallocation

Frequency counting is a powerful abstraction that transforms many complex problems into simple counting operations!
