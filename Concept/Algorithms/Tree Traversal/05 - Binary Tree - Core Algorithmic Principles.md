---
tags: [concept/algorithms, concept/tree, concept/binary_tree, concept/recursion, type/problem_solving_framework, pattern/divide_and_conquer, pattern/dfs]
aliases: [Binary Tree Algorithm Framework, 二叉树算法纲领, Tree Problem Solving Strategies]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/二叉树系列算法核心纲领.md]].
> Labuladong emphasizes that most binary tree problems can be solved by one of two main thinking modes applied to the standard tree traversal framework.

# Binary Tree Algorithms: Core Principles and Problem-Solving Framework

Binary tree problems are a cornerstone of algorithm interviews. Understanding their structure and common solution patterns is crucial. This note outlines a general framework for approaching binary tree problems, focusing on two primary thinking modes.

## 🌲 The Importance of Binary Trees in Algorithms

Binary trees are not just data structures; they embody recursive thinking. Many algorithms, even those not directly operating on explicit tree structures, can be conceptualized as processes on trees.
- **Sorting Algorithms:**
    - [[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]] can be seen as a pre-order traversal. The partitioning step (processing the "root" or pivot) happens before recursively sorting subarrays (child "subtrees").
    - [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]] can be seen as a post-order traversal. Subarrays (child "subtrees") are sorted recursively, and then the results are merged (processing the "root" or current array segment).
- **General Recursive Algorithms:** Many problems solved with [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|recursion]], [[Interview/Concept/Algorithms/Dynamic Programming/index|dynamic programming]], [[Interview/Concept/Algorithms/Backtracking/index|backtracking]], and [[Interview/Concept/Algorithms/Divide and Conquer/index|divide and conquer]] can be mapped to operations on an implicit or explicit tree structure.

## 🧠 Two Core Thinking Modes for Solving Binary Tree Problems

Most binary tree problems can be solved by applying one of two fundamental thinking modes to the standard [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|recursive tree traversal framework]].

### Mode 1: "Traversal" - Iterating Through the Tree
- **Core Idea:** Traverse the entire tree (or relevant parts) once, accumulating information or performing actions at each node. The final answer is often built up in an external variable or by side effects during the traversal.
- **Function Signature:** Typically a `void` function (or returns `None` in Python) like `traverse(node, state_variables...)`.
- **Process:**
    1. Define what information needs to be passed down or updated globally during traversal (e.g., current path, current depth, result accumulator).
    2. The recursive `traverse` function visits nodes.
    3. Logic is injected at **pre-order, in-order, or post-order** positions within the traversal framework to process the current node or update state.
- **Key Question:** "What does each node need to *do* as it's visited, and when (pre/in/post) should it do it?"

### Mode 2: "Decomposition" - Divide and Conquer
- **Core Idea:** Define what the recursive function should compute and **return** for a given subtree. The solution for a node's subtree is derived from the solutions of its children's subtrees.
- **Function Signature:** Typically returns a meaningful value, e.g., `solve_subtree(node) -> result_for_this_subtree`.
- **Process:**
    1. Clearly define what `recursive_function(node)` means: What problem does it solve for the subtree rooted at `node`, and what does it return?
    2. Identify base cases (e.g., a `null` node).
    3. Recursively call the function for the left and right children: `left_result = recursive_function(node.left)`, `right_result = recursive_function(node.right)`.
    4. Combine `left_result`, `right_result`, and information from the `current_node` to compute the result for the subtree rooted at `current_node`. This combination logic is often placed in the **post-order position**.
- **Key Question:** "If I know the answers for the left and right subtrees, how can I compute the answer for the current tree rooted at this node?"

**Labuladong's Naming Convention:**
- Traversal mode functions: often named `traverse(...)` and may not have a return value (use side effects).
- Decomposition mode functions: named descriptively (e.g., `maxDepth(...)`, `countNodes(...)`) and usually have a return value.

## 🧭 Understanding Pre-order, In-order, and Post-order Positions

These are not just about the sequence of visiting nodes to produce a list; they are **critical junctures in the recursive processing of each node**:

```python
def traverse(root):
    if root is None:
        return

    # --- Pre-order position ---
    # Code here executes when a node is first "entered".
    # Information from parent/ancestors is available.
    # Decisions/actions can be made before exploring children.
    # Example: print(root.val) for pre-order traversal list.

    traverse(root.left)

    # --- In-order position ---
    # Code here executes after the entire left subtree has been processed,
    # but before the right subtree is processed.
    # Information from parent and left_subtree_result (if decomposition) is available.
    # Example: print(root.val) for in-order traversal list (sorted for BST).

    traverse(root.right)

    # --- Post-order position ---
    # Code here executes after both left and right subtrees have been fully processed.
    # Information from parent, left_subtree_result, AND right_subtree_result (if decomposition) is available.
    # This is powerful for "bottom-up" computations.
    # Example: print(root.val) for post-order traversal list.
    # Example: Calculate height: 1 + max(height(left), height(right)).
```

**Key Insights about Positions:**
- **Pre-order:** "Root, Left, Right" processing. Good for top-down operations.
- **In-order:** "Left, Root, Right" processing. Crucial for [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Binary Search Trees (BSTs)]] as it yields sorted elements. Less uniquely defined for N-ary trees.
- **Post-order:** "Left, Right, Root" processing. Powerful because when code at the post-order position for `node` executes, `node.left` and `node.right` subproblems have already been solved and their results (if any) are available. This is the natural fit for many "Decomposition" / "Divide and Conquer" solutions.

![](/algo/images/binary-tree-summary/2.jpeg)
*(Image from Labuladong showing the conceptual timing of pre/in/post order positions)*

##  अप्लाईकेशन उदाहरण (Application Examples)

### Example 1: Max Depth of Binary Tree (LeetCode 104)
[[Interview/Practice/LeetCode/LC104 - Maximum Depth of Binary Tree|LC104 - Maximum Depth of Binary Tree]]

**Traversal Mode Solution:**
```python
# self.max_overall_depth = 0
# self.current_depth = 0
# def _traverse_max_depth(node):
#     if node is None: return
#     self.current_depth += 1 # Pre-order: entering a level
#     if node.left is None and node.right is None: # Leaf
#         self.max_overall_depth = max(self.max_overall_depth, self.current_depth)
#     _traverse_max_depth(node.left)
#     _traverse_max_depth(node.right)
#     self.current_depth -= 1 # Post-order: leaving a level
```
- Updates an external variable `max_overall_depth`.
- `current_depth` is maintained during traversal.

**Decomposition Mode Solution:**
```python
# def maxDepth_decompose(root):
#     if root is None: return 0
#     left_depth = maxDepth_decompose(root.left)
#     right_depth = maxDepth_decompose(root.right)
#     # Post-order logic: combine children's results
#     return 1 + max(left_depth, right_depth)
```
- Clearly defines `maxDepth_decompose(node)` as returning the depth of the subtree rooted at `node`.
- Relies on returned values from subproblems.

### Example 2: Binary Tree Preorder Traversal (LeetCode 144)
[[Interview/Practice/LeetCode/LC144 - Binary Tree Preorder Traversal|LC144 - Binary Tree Preorder Traversal]]

**Traversal Mode Solution (Standard):**
```python
# self.result = []
# def _traverse_preorder(node):
#     if node is None: return
#     self.result.append(node.val) # Pre-order action
#     _traverse_preorder(node.left)
#     _traverse_preorder(node.right)
```

**Decomposition Mode Solution:**
```python
# def preorderTraversal_decompose(root):
#     if root is None: return []
#     # Definition: returns list of preorder traversal for subtree at root
#     res = [root.val] # Root first
#     res.extend(preorderTraversal_decompose(root.left)) # Then left subtree's preorder
#     res.extend(preorderTraversal_decompose(root.right)) # Then right subtree's preorder
#     return res
```
- While possible, this decomposition approach can be less efficient for simple traversals due to list concatenations, depending on the language (e.g., Python `extend` might be efficient, but repeated `+` for lists would be $O(N^2)$).

### Example 3: Diameter of Binary Tree (LeetCode 543)
[[Interview/Practice/LeetCode/LC543 - Diameter of Binary Tree|LC543 - Diameter of Binary Tree]]
The diameter is the length of the longest path between any two nodes. This path may or may not pass through the root.
A path's length through a node `X` is `maxDepth(X.left) + maxDepth(X.right)`.

**Initial (Less Efficient) Approach - Traversal + Decomposition:**
```python
# self.max_diameter = 0
# def diameterOfBinaryTree(root):
#     _traverse_diameter(root) # Traversal part
#     return self.max_diameter
#
# def _traverse_diameter(node): # Traversal thinking
#     if node is None: return
#     # For each node, calculate its diameter contribution
#     left_d = _max_depth_for_diameter(node.left) # Decomposition for depth
#     right_d = _max_depth_for_diameter(node.right)
#     self.max_diameter = max(self.max_diameter, left_d + right_d)
#     _traverse_diameter(node.left)
#     _traverse_diameter(node.right)
#
# def _max_depth_for_diameter(node): # Standard maxDepth (decomposition)
#     if node is None: return 0
#     return 1 + max(_max_depth_for_diameter(node.left), _max_depth_for_diameter(node.right))
```
- This is $O(N^2)$ because `_max_depth_for_diameter` is called for each node, and it re-traverses subtrees.

**Optimized Approach - Pure Decomposition (using Post-order):**
The key is that `maxDepth` calculation inherently uses post-order. We can "piggyback" the diameter calculation onto it.
```python
# self.max_diameter = 0
# def _max_depth_and_update_diameter(root): # Returns depth of this subtree
#     if root is None: return 0
#     
#     left_depth = _max_depth_and_update_diameter(root.left)
#     right_depth = _max_depth_and_update_diameter(root.right)
#     
#     # --- Post-order position ---
#     # Calculate diameter through current root
#     current_diameter = left_depth + right_depth
#     self.max_diameter = max(self.max_diameter, current_diameter)
#     
#     # Return depth of current subtree for parent
#     return 1 + max(left_depth, right_depth)
#
# def diameterOfBinaryTree(root):
#    _max_depth_and_update_diameter(root)
#    return self.max_diameter
```
- This is $O(N)$ because each node is visited once. The `max_diameter` is updated as a side effect, but the function's primary definition (returning depth) follows the decomposition pattern. The post-order position is crucial as it has access to `left_depth` and `right_depth`.

## 💡 Relation to Other Algorithmic Paradigms

- **Traversal Mode $\approx$ [[Interview/Concept/Algorithms/Backtracking/index|Backtracking]] / [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|DFS]]:** Focus is on exploring paths/states. Choices are made, explored, and undone.
    - Backtracking:关注「树枝」 (关注节点之间移动的过程) "Doing and undoing choices" logic is typically *inside* the loop over children/choices.
    - DFS for node processing (e.g., graph coloring, island problems): 关注「节点」 (关注单个节点) "Processing a node" logic is typically *outside* the loop over neighbors, often in pre-order.
- **Decomposition Mode $\approx$ [[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]] / [[Interview/Concept/Algorithms/Divide and Conquer/index|Divide and Conquer]]:** Focus is on solving subproblems and combining their results. The definition and return value of the recursive function are paramount. 关注「子树」 (关注整棵「子树」)

## 总结 (Summary)

1.  **Universal Approach:** View binary tree problems through the lens of recursive traversal.
2.  **Two Thinking Modes:**
    - **Traversal:** Use a `void traverse(...)` function, often with external variables, to iterate and collect results. Inject logic at pre/in/post-order positions.
    - **Decomposition (Divide and Conquer):** Define `return_type solve_subtree(...)`. Solve subproblems recursively and combine results, usually in post-order.
3.  **Node-Specific Logic:** For either mode, determine what each individual node needs to do and *when* (pre/in/post-order) it should do it.
4.  **Post-order Power:** The post-order position is especially powerful as it has access to results from both left and right subtrees, making it ideal for many decomposition-style solutions.

By consistently applying these thinking modes and understanding the roles of pre/in/post-order positions, most binary tree problems become approachable and solvable.

---
Parent: [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal Algorithms]]
Previous: [[Interview/Concept/Algorithms/Tree Traversal/04 - DFS vs BFS - When to Use Which|DFS vs BFS - When to Use Which]]
Next: [[Interview/Practice/LeetCode/LC104 - Maximum Depth of Binary Tree|LC104 - Maximum Depth of Binary Tree]] (as a primary example)
Related: [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursion Thinking Modes]]
