---
tags: [concept/algorithms, concept/tree, concept/binary_tree, concept/recursion, type/problem_solving_framework, pattern/divide_and_conquer, pattern/dfs]
aliases: [Binary Tree Algorithm Framework, 二叉树算法纲领, Tree Problem Solving Strategies, 二叉树解题思路]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/二叉树系列算法核心纲领.md]] and influenced by [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/学习数据结构和算法的框架思维.md]].
> Labuladong emphasizes that most binary tree problems can be solved by one of two main thinking modes applied to the standard tree traversal framework.

# Binary Tree Algorithms: Core Principles and Problem-Solving Framework

Binary tree problems are a cornerstone of algorithm interviews. Understanding their structure and common solution patterns is crucial. This note outlines a general framework for approaching binary tree problems, focusing on two primary thinking modes as described by Labuladong, which are specializations of the general [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursive Thinking Modes]].

## 🌲 The Importance of Binary Trees in Algorithms

Binary trees are not just data structures; they embody recursive thinking. Many algorithms, even those not directly operating on explicit tree structures, can be conceptualized as processes on trees.
- **Sorting Algorithms:**
    - [[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]] partition step can be seen as processing a "root" before recursively sorting "subtrees" (pre-order like).
    - [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]] recursively sorts "subtrees" then merges (post-order like).
- **General Recursive Algorithms:** Many problems solved with [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|recursion]], [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|dynamic programming]], [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|backtracking]], and [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|divide and conquer]] can be mapped to operations on an implicit or explicit tree structure.

## 🧠 Two Core Thinking Modes for Solving Binary Tree Problems

Most binary tree problems can be solved by applying one of two fundamental recursive thinking modes, which are essentially the "Traversal" and "Decomposition" modes discussed in [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursion - One Perspective, Two Thinking Modes]], tailored for trees.

### Mode 1: "Traversal" - Iterating Through the Tree ("遍历" 思维)
- **Core Idea:** Traverse the entire tree (or relevant parts) once, accumulating information or performing actions at each node. The final answer is often built up in an external variable (instance variable, global, or passed by reference) or by side effects during the traversal.
- **Function Signature:** Typically a `void` function (or returns `None` in Python) like `traverse(node, state_variables...)`.
- **Process:**
    1. Define what information needs to be passed down or updated globally during traversal (e.g., current path sum, current depth, result accumulator).
    2. The recursive `traverse` function visits nodes.
    3. Logic is injected at **pre-order, in-order, or post-order** positions within the standard [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|recursive tree traversal framework]] to process the current node or update state.
- **Key Question:** "What does each node need to *do* as it's visited, and *when* (pre/in/post) should it do it relative to visiting its children?"
- **Focus:** "关注节点" (Focus on individual nodes) and "关注树枝" (Focus on the transitions/process between nodes). The logic often involves "doing" an action (e.g., adding to path, incrementing depth) before recursive calls and "undoing" it (e.g., removing from path, decrementing depth) after – characteristic of [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking]].

### Mode 2: "Decomposition" - Divide and Conquer ("分解问题" 思维)
- **Core Idea:** Define what the recursive function should compute and **return** for a given subtree. The solution for a node's subtree is derived from the solutions of its children's subtrees.
- **Function Signature:** Typically returns a meaningful value, e.g., `solve_subtree(node) -> result_for_this_subtree`.
- **Process:**
    1. Clearly define what `recursive_function(node)` means: What problem does it solve for the subtree rooted at `node`, and what does it return?
    2. Identify base cases (e.g., a `null` node usually returns a value like 0, an empty list, etc., depending on the problem).
    3. Recursively call the function for the left and right children: `left_result = recursive_function(node.left)`, `right_result = recursive_function(node.right)`.
    4. Combine `left_result`, `right_result`, and information from the `current_node` to compute the result for the subtree rooted at `current_node`. This combination logic is almost always placed in the **post-order position** of the traversal, as it needs results from children before processing the parent.
- **Key Question:** "If I know the answers for the left and right subtrees, how can I compute the answer for the current tree rooted at this node?"
- **Focus:** "关注整棵子树" (Focus on the entire subtree) and its properties.

**Labuladong's Naming Convention Suggestion:**
- Traversal mode functions: often named `traverse(...)` and may not have a meaningful return value (use side effects).
- Decomposition mode functions: named descriptively (e.g., `maxDepth(...)`, `countNodes(...)`) and usually have a return value that is the solution for the subtree.

## 🧭 Understanding Pre-order, In-order, and Post-order Positions

These are not just about the sequence of visiting nodes to produce a list; they are **critical junctures in the recursive processing of each node** where specific logic can be executed:

```python
def tree_operation_framework(root):
    if root is None:
        # Base case for recursion: what to do or return for an empty subtree
        return # Or return a specific value (e.g., 0, True, [], etc.)

    # --- Pre-order position ---
    # Code here executes when a node is first "entered".
    # Information from parent/ancestors is available (if passed down).
    # Decisions/actions can be made *before* exploring children.
    # Useful for:
    #   - Processing root before subtrees (e.g., printing in pre-order list).
    #   - Passing information downwards to children.
    #   - Making a copy of the node itself.
    # Example: result_list.append(root.val) for pre-order traversal list.

    left_subtree_result = tree_operation_framework(root.left) # Recursive call for left child

    # --- In-order position ---
    # Code here executes *after* the entire left subtree has been processed,
    # but *before* the right subtree is processed.
    # Information from parent and left_subtree_result (if decomposition mode) is available.
    # Useful for:
    #   - Processing root between left and right subtrees (e.g., printing in-order list).
    #   - In BSTs, this is where elements are processed in sorted order.
    # Example: result_list.append(root.val) for in-order traversal list.

    right_subtree_result = tree_operation_framework(root.right) # Recursive call for right child

    # --- Post-order position ---
    # Code here executes *after* both left and right subtrees have been fully processed.
    # Information from parent, left_subtree_result, AND right_subtree_result (if decomposition mode) is available.
    # This is powerful for "bottom-up" computations.
    # Useful for:
    #   - Processing root after subtrees (e.g., printing post-order list).
    #   - Calculating properties that depend on children's properties (height, size, sum).
    #   - Cleaning up/deleting nodes.
    # Example: result_list.append(root.val) for post-order traversal list.
    # Example: return 1 + max(left_subtree_result, right_subtree_result) for height.

    # If in decomposition mode, combine results and return
    # return combine(root.val, left_subtree_result, right_subtree_result) 
```

**Key Insights about Positions:**
- **Pre-order:** "Root, Left, Right" processing. Good for top-down operations, making decisions before diving deeper.
- **In-order:** "Left, Root, Right" processing. Crucial for [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Binary Search Trees (BSTs)]] as it yields sorted elements.
- **Post-order:** "Left, Right, Root" processing. Powerful because when code at the post-order position for `node` executes, `node.left` and `node.right` subproblems have already been solved and their results are available. This is the natural fit for most "Decomposition" / "Divide and Conquer" solutions on trees.

Labuladong's image `![](/algo/images/binary-tree-ii/1.jpeg)` (for preorder) and `![](/algo/images/binary-tree-summary/2.jpeg)` (general pre/in/post timing) illustrate these concepts.

##  अप्लाईकेशन उदाहरण (Application Examples)

### Example 1: Max Depth of Binary Tree (LC104)
[[Interview/Practice/LeetCode/LC104 - Maximum Depth of Binary Tree|LC104 - Maximum Depth of Binary Tree]]

**Traversal Mode Solution (using instance variables for depth tracking):**
```python
# class SolutionMaxDepthTraversal:
#     max_depth_val = 0
#     current_path_depth = 0 # Represents depth of current node in traversal path
#
#     def maxDepth(self, root) -> int:
#         self.max_depth_val = 0
#         self.current_path_depth = 0
#         self._traverse_max_depth(root)
#         return self.max_depth_val
#
#     def _traverse_max_depth(self, node):
#         if node is None:
#             # Reached beyond a leaf node; current_path_depth is depth of this null path
#             self.max_depth_val = max(self.max_depth_val, self.current_path_depth)
#             return
#
#         self.current_path_depth += 1 # Pre-order: entering a new level
#         # Note: If a leaf is defined as having depth 1, initialize current_path_depth to 0
#         # and increment for actual nodes. If problem considers depth of path to leaf:
#         # if node.left is None and node.right is None: # At a leaf
#         #    self.max_depth_val = max(self.max_depth_val, self.current_path_depth)
#
#         self._traverse_max_depth(node.left)
#         self._traverse_max_depth(node.right)
#
#         self.current_path_depth -= 1 # Post-order: leaving this level (backtrack depth)
```
- Updates an external variable `max_depth_val`.
- `current_path_depth` is maintained during traversal.

**Decomposition Mode Solution (more common and often cleaner):**
```python
# def maxDepth_decompose(root):
#     if root is None: return 0 # Depth of an empty tree is 0
#     # Get depths of subtrees
#     left_depth = maxDepth_decompose(root.left)
#     right_depth = maxDepth_decompose(root.right)
#     # Post-order logic: combine children's results + 1 for current node
#     return 1 + max(left_depth, right_depth)
```
- Clearly defines `maxDepth_decompose(node)` as returning the depth of the subtree rooted at `node`.
- Relies on returned values from subproblems.

### Example 2: Binary Tree Preorder Traversal (LC144)
[[Interview/Practice/LeetCode/LC144 - Binary Tree Preorder Traversal|LC144 - Binary Tree Preorder Traversal]]

**Traversal Mode Solution (Standard):**
```python
# class SolutionPreorderTraversal:
#     def __init__(self):
#         self.result = []
#
#     def preorderTraversal(self, root) -> list[int]:
#         self.result = [] # Reset for multiple calls if Solution object is reused
#         self._traverse_preorder(root)
#         return self.result
#
#     def _traverse_preorder(self, node):
#         if node is None: return
#         self.result.append(node.val) # Pre-order action: Add current node's value
#         self._traverse_preorder(node.left)
#         self._traverse_preorder(node.right)
```

**Decomposition Mode Solution:**
```python
# def preorderTraversal_decompose(root):
#     # Definition: returns list of preorder traversal for subtree at root
#     if root is None: return []
#     
#     # Pre-order logic: Current node's value first
#     res = [root.val] 
#     # Then left subtree's preorder result
#     res.extend(preorderTraversal_decompose(root.left)) 
#     # Then right subtree's preorder result
#     res.extend(preorderTraversal_decompose(root.right)) 
#     return res
```
- While possible, this decomposition approach for simple list generation can be less efficient due to list creations and extensions in some languages if not optimized (Python's `extend` is generally efficient). The traversal mode with an accumulator list is often more direct for generating traversal sequences.

### Example 3: Diameter of Binary Tree (LC543)
[[Interview/Practice/LeetCode/LC543 - Diameter of Binary Tree|LC543 - Diameter of Binary Tree]]
The diameter is the length of the longest path between any two nodes. This path may or may not pass through the root of the current subtree.

**Optimized Approach - Decomposition (using Post-order):**
The key is that the function should return the *depth* of the current subtree (useful for parent's calculation) but *also* update a global/instance variable for the maximum diameter found so far. The diameter through a node `X` is `depth(X.left) + depth(X.right)`.
```python
# class SolutionDiameter:
#     max_diameter = 0
#
#     def _depth_and_update_diameter(self, root) -> int: # Returns depth of this subtree
#         if root is None: return 0
#         
#         left_depth = self._depth_and_update_diameter(root.left)
#         right_depth = self._depth_and_update_diameter(root.right)
#         
#         # --- Post-order position ---
#         # Calculate diameter passing through current root
#         current_node_diameter = left_depth + right_depth
#         self.max_diameter = max(self.max_diameter, current_node_diameter)
#         
#         # Return depth of current subtree for parent's calculation
#         return 1 + max(left_depth, right_depth)
#
#     def diameterOfBinaryTree(self, root) -> int:
#        self.max_diameter = 0 # Reset for multiple calls
#        self._depth_and_update_diameter(root)
#        return self.max_diameter
```
- This is $O(N)$ because each node is visited once. The `max_diameter` is updated as a side effect, but the function's primary recursive definition (returning depth) follows the decomposition pattern. The post-order position is crucial as it has access to `left_depth` and `right_depth`.

## 总结 (Summary)

1.  **Universal Approach:** View binary tree problems through the lens of recursive traversal. The core framework is always `traverse(left_child)`, `traverse(right_child)`, with logic placed in pre-order, in-order, or post-order positions relative to these calls.
2.  **Two Thinking Modes (Labuladong's perspective):**
    - **Traversal ("遍历"思维):** Use a `void traverse(...)` function (or returns `None`), often with external/instance variables, to iterate and collect results or perform actions. Logic is injected at pre/in/post-order positions based on *when* a node should be processed relative to its children.
    - **Decomposition ("分解问题"思维 / Divide and Conquer):** Define `return_type solve_subtree(...)`. Recursively solve for children, then combine these results (usually in post-order) with the current node's information to solve for the current subtree and return that result.
3.  **Node-Specific Logic:** For either mode, determine what each individual node needs to do and *when* (pre/in/post-order) it should do it. The "when" is critical and often dictates the choice of mode or where logic is placed.
4.  **Post-order Power:** The post-order position is especially powerful for decomposition-style solutions, as it naturally allows combining results from fully processed left and right subtrees.

By consistently applying these thinking modes and understanding the roles of pre/in/post-order positions, most binary tree problems become approachable and solvable in a structured manner.

---
Parent: [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal Algorithms]]
Previous: [[Interview/Concept/Algorithms/Tree Traversal/04 - DFS vs BFS - When to Use Which|DFS vs BFS - When to Use Which]]
Next: [[Interview/Practice/LeetCode/LC104 - Maximum Depth of Binary Tree|LC104 - Maximum Depth of Binary Tree]] (as a primary example)
Related: [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursion Thinking Modes]], [[Interview/Concept/Algorithms/00 - Framework Thinking (Labuladong Philosophy)|Framework Thinking (Labuladong Philosophy)]]
