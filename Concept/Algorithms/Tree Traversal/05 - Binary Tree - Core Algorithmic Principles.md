---
tags: [concept/algorithms, concept/tree, concept/binary_tree, concept/recursion, type/problem_solving_framework, pattern/divide_and_conquer, pattern/dfs, course/labuladong]
aliases: [Binary Tree Algorithm Framework, 二叉树算法纲领, Tree Problem Solving Strategies, 二叉树解题思路, Binary Tree Core Principles]
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/二叉树系列算法核心纲领.md]].
> Labuladong emphasizes that most binary tree problems can be solved by one of two main thinking modes applied to the standard tree traversal framework.

# Binary Tree Algorithms: Core Principles and Problem-Solving Framework

Binary tree problems are a cornerstone of algorithm interviews. Understanding their structure and common solution patterns is crucial. This note outlines a general framework for approaching binary tree problems, focusing on two primary thinking modes as described by Labuladong, which are specializations of the general [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursive Thinking Modes]].

## 🌲 The Importance of Binary Trees in Algorithms

Binary trees are not just data structures; they embody recursive thinking. Many algorithms, even those not directly operating on explicit tree structures, can be conceptualized as processes on trees.
- **Sorting Algorithms:**
    - [[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]] partition step can be seen as processing a "root" before recursively sorting "subtrees" (pre-order like). The Labuladong article provides the following C++ like pseudocode which can be adapted to Python:
      ```python
      # Quick Sort conceptual structure
      # def sort(nums: list[int], lo: int, hi: int):
      #     if lo >= hi:
      #         return
      #     # Pre-order position: Partition and place pivot
      #     p = partition(nums, lo, hi) # p is pivot's final index
      #     # Recursively sort sub-arrays
      #     sort(nums, lo, p - 1)
      #     sort(nums, p + 1, hi)
      ```
    - [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]] recursively sorts "subtrees" then merges (post-order like).
      ```python
      # Merge Sort conceptual structure
      # def sort(nums: list[int], lo: int, hi: int):
      #     if lo == hi: # Base case for recursion (single element is sorted)
      #         return
      #     mid = (lo + hi) // 2
      #     # Recursively sort left and right halves
      #     sort(nums, lo, mid)
      #     sort(nums, mid + 1, hi)
      #     # Post-order position: Merge sorted halves
      #     merge(nums, lo, mid, hi)
      ```
- **General Recursive Algorithms:** Many problems solved with [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|recursion]], [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|dynamic programming]], [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|backtracking]], and [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|divide and conquer]] can be mapped to operations on an implicit or explicit tree structure.

## 🧠 Two Core Thinking Modes for Solving Binary Tree Problems

Most binary tree problems can be solved by applying one of two fundamental recursive thinking modes:

### Mode 1: "Traversal" - Iterating Through the Tree ("遍历" 思维)
- **Core Idea:** Traverse the entire tree (or relevant parts) once, accumulating information or performing actions at each node. The final answer is often built up in an external variable (instance variable, global, or passed by reference) or by side effects during the traversal.
- **Function Signature:** Typically a `void` function (or returns `None` in Python) like `traverse(node, state_variables...)`.
- **Process:**
    1. Define what information needs to be passed down or updated globally during traversal (e.g., current path sum, current depth, result accumulator).
    2. The recursive `traverse` function visits nodes.
    3. Logic is injected at **pre-order, in-order, or post-order** positions within the standard [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|recursive tree traversal framework]] to process the current node or update state.
- **Key Question:** "What does each node need to *do* as it's visited, and *when* (pre/in/post) should it do it relative to visiting its children?"
- **Focus:** "关注节点" (Focus on individual nodes) and "关注树枝" (Focus on the transitions/process between nodes). The logic often involves "doing" an action (e.g., adding to path, incrementing depth) before recursive calls and "undoing" it (e.g., removing from path, decrementing depth) after – characteristic of [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking]].

### Mode 2: "Decomposition" - Divide and Conquer ("分解问题" 思维)
- **Core Idea:** Define what the recursive function should compute and **return** for a given subtree. The solution for a node's subtree is derived from the solutions of its children's subtrees.
- **Function Signature:** Typically returns a meaningful value, e.g., `solve_subtree(node) -> result_for_this_subtree`.
- **Process:**
    1. Clearly define what `recursive_function(node)` means: What problem does it solve for the subtree rooted at `node`, and what does it return?
    2. Identify base cases (e.g., a `None` node usually returns a value like 0, an empty list, etc., depending on the problem).
    3. Recursively call the function for the left and right children: `left_result = recursive_function(node.left)`, `right_result = recursive_function(node.right)`.
    4. Combine `left_result`, `right_result`, and information from the `current_node` to compute the result for the subtree rooted at `current_node`. This combination logic is almost always placed in the **post-order position** of the traversal, as it needs results from children before processing the parent.
- **Key Question:** "If I know the answers for the left and right subtrees, how can I compute the answer for the current tree rooted at this node?"
- **Focus:** "关注整棵子树" (Focus on the entire subtree) and its properties.

**Labuladong's Naming Convention Suggestion:**
- Traversal mode functions: often named `traverse(...)` and may not have a meaningful return value (use side effects).
- Decomposition mode functions: named descriptively (e.g., `maxDepth(...)`, `countNodes(...)`) and usually have a return value that is the solution for the subtree.

## 🧭 Understanding Pre-order, In-order, and Post-order Positions

These are not just about the sequence of visiting nodes to produce a list; they are **critical junctures in the recursive processing of each node** where specific logic can be executed.

```python
# Conceptual Python TreeNode definition
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None):
#         self.val = val
#         self.left = left
#         self.right = right

def tree_operation_framework(root): # 'root' is a TreeNode object
    if root is None:
        # Base case for recursion: what to do or return for an empty subtree
        return # Or return a specific value (e.g., 0, True, [], etc.)

    # --- Pre-order position ---
    # Code here executes when a node is first "entered".
    # Information from parent/ancestors is available (if passed down).
    # Decisions/actions can be made *before* exploring children.
    # Example: result_list.append(root.val) for pre-order traversal list.
    # print(f"Pre-order: {root.val}")

    left_subtree_result = tree_operation_framework(root.left) # Recursive call for left child

    # --- In-order position ---
    # Code here executes *after* the entire left subtree has been processed,
    # but *before* the right subtree is processed.
    # Information from parent and left_subtree_result (if decomposition mode) is available.
    # Example: result_list.append(root.val) for in-order traversal list.
    # print(f"In-order: {root.val}")

    right_subtree_result = tree_operation_framework(root.right) # Recursive call for right child

    # --- Post-order position ---
    # Code here executes *after* both left and right subtrees have been fully processed.
    # Information from parent, left_subtree_result, AND right_subtree_result (if decomposition mode) is available.
    # This is powerful for "bottom-up" computations.
    # Example: result_list.append(root.val) for post-order traversal list.
    # Example: return 1 + max(left_subtree_result, right_subtree_result) for height.
    # print(f"Post-order: {root.val}")
    
    # If in decomposition mode, combine results and return
    # For example, if tree_operation_framework was defined to return sum of nodes:
    # return root.val + left_subtree_result + right_subtree_result 
    # (assuming base case for None returns 0 for sum)
```

Labuladong's visualization `![](/algo/images/binary-tree-summary/2.jpeg)` shows these positions:
```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, font=\sffamily\bfseries, minimum size=8mm},
    pos_label/.style={font=\sffamily\scriptsize, text width=2.5cm, align=center},
    edge_style/.style={->, >=stealth, thick},
    level 1/.style={sibling distance=40mm, level distance=20mm},
    level 2/.style={sibling distance=20mm}
]

\node[treenode] (root) {root}
    child {node[treenode] (left_child) {left}
        child {node (ll_dummy) {} edge from parent[draw=none]} % for spacing
        child {node (lr_dummy) {} edge from parent[draw=none]}
    }
    child {node[treenode] (right_child) {right}
        child {node (rl_dummy) {} edge from parent[draw=none]}
        child {node (rr_dummy) {} edge from parent[draw=none]}
    };

% Pre-order position labels
\node[pos_label, above=0.1cm of root, fill=red!10, rounded corners] (pre_root) {Pre-order for `root`};
\node[pos_label, above left=0.1cm and -0.5cm of left_child, fill=red!10, rounded corners] {Pre-order for `left`};
\node[pos_label, above right=0.1cm and -0.5cm of right_child, fill=red!10, rounded corners] {Pre-order for `right`};

% In-order position labels
\node[pos_label, below left=0.1cm and -0.5cm of root, fill=blue!10, rounded corners] (in_root) {In-order for `root`};
% For children, it's after their left subtrees (which are null here for simplicity)
\node[pos_label, left=0.3cm of left_child, fill=blue!10, rounded corners] {In-order for `left`};
\node[pos_label, right=0.3cm of right_child, fill=blue!10, rounded corners] {In-order for `right`};


% Post-order position labels
\node[pos_label, below=0.1cm of root, fill=green!10, rounded corners] (post_root) {Post-order for `root`};
\node[pos_label, below left=0.1cm and -0.5cm of left_child, fill=green!10, rounded corners] {Post-order for `left`};
\node[pos_label, below right=0.1cm and -0.5cm of right_child, fill=green!10, rounded corners] {Post-order for `right`};

\node at (0,-4.5) [text width=10cm, align=center, draw, rounded corners, fill=gray!10] {
    \textbf{Conceptual Traversal Path & Processing Points:}\\
    Path "enters" node (Pre-order), then explores left subtree.\\
    Path returns from left subtree, "passes through" node (In-order), then explores right subtree.\\
    Path returns from right subtree, "exits" node (Post-order).
};
\end{tikzpicture}
```

##  अप्लाईकेशन उदाहरण (Application Examples)

### Example 1: Max Depth of Binary Tree (LC104)
[[Interview/Practice/LeetCode/LC104 - Maximum Depth of Binary Tree|LC104 - Maximum Depth of Binary Tree]]
**Traversal Mode Solution (using instance variables):**
```python
# (Conceptual class structure)
# class Solution:
#     max_d = 0
#     current_depth = 0
# 
#     def maxDepth(self, root) -> int:
#         self.max_d = 0
#         self.current_depth = 0
#         self._traverse_max_depth(root, 1) # Start root at depth 1
#         return self.max_d
#
#     def _traverse_max_depth(self, node, depth_at_node):
#         if node is None:
#             return
#         
#         # Pre-order action: Update max_d when we enter a node
#         self.max_d = max(self.max_d, depth_at_node)
#         
#         self._traverse_max_depth(node.left, depth_at_node + 1)
#         self._traverse_max_depth(node.right, depth_at_node + 1)
#         # No explicit post-order action needed for depth variable passed by value
```
Labuladong's article `二叉树系列算法核心纲领.md` presents a version where `depth` is an instance variable modified in pre-order (increment) and post-order (decrement). Visualizer: `div_mydata-maxdepth1`.

**Decomposition Mode Solution (more common):**
```python
# def maxDepth_decompose(root): # Returns depth of subtree at root
#     if root is None: return 0
#     left_depth = maxDepth_decompose(root.left)
#     right_depth = maxDepth_decompose(root.right)
#     # Post-order logic:
#     return 1 + max(left_depth, right_depth)
```
Visualizer: `div_mydata-maxdepth2`.

### Example 2: Binary Tree Preorder Traversal (LC144)
[[Interview/Practice/LeetCode/LC144 - Binary Tree Preorder Traversal|LC144 - Binary Tree Preorder Traversal]]
**Traversal Mode Solution:**
```python
# class Solution:
#     res_list = []
#     def preorderTraversal(self, root) -> list[int]:
#         self.res_list = []
#         self._traverse(root)
#         return self.res_list
#
#     def _traverse(self, node):
#         if node is None: return
#         self.res_list.append(node.val) # Pre-order action
#         self._traverse(node.left)
#         self._traverse(node.right)
```
**Decomposition Mode Solution:**
```python
# def preorderTraversal_decompose(root): # Returns preorder list for subtree at root
#     if root is None: return []
#     res = [root.val] # Pre-order action
#     res.extend(preorderTraversal_decompose(root.left))
#     res.extend(preorderTraversal_decompose(root.right))
#     return res
```
Labuladong's image `![](/algo/images/binary-tree-summary/3.jpeg)` illustrates this decomposition.

### Example 3: Diameter of Binary Tree (LC543)
[[Interview/Practice/LeetCode/LC543 - Diameter of Binary Tree|LC543 - Diameter of Binary Tree]]
Diameter is `max_depth(left_subtree) + max_depth(right_subtree)` for each node. Maximize this over all nodes.
**Naive Traversal (O(N^2)):**
```python
# class Solution:
#     max_diameter = 0
#     # maxDepth helper is the decomposition version from above
#
#     def diameterOfBinaryTree(self, root) -> int:
#         self.max_diameter = 0
#         self._traverse_for_diameter(root)
#         return self.max_diameter
#
#     def _traverse_for_diameter(self, node):
#         if node is None: return
#         # For each node, calculate its diameter
#         left_d = self.maxDepth(node.left) # O(N_left)
#         right_d = self.maxDepth(node.right) # O(N_right)
#         self.max_diameter = max(self.max_diameter, left_d + right_d)
#         
#         self._traverse_for_diameter(node.left)
#         self._traverse_for_diameter(node.right)
```
**Optimized Decomposition (O(N)):**
The `maxDepth` function can be modified to update `max_diameter` as a side effect during its post-order computation.
```python
# class SolutionOptimized:
#     max_diameter = 0
#
#     def diameterOfBinaryTree(self, root) -> int:
#         self.max_diameter = 0
#         self._depth_and_update_diameter(root) # Call helper
#         return self.max_diameter
#
#     def _depth_and_update_diameter(self, node) -> int: # Returns depth of this subtree
#         if node is None: return 0
#         
#         left_depth = self._depth_and_update_diameter(node.left)
#         right_depth = self._depth_and_update_diameter(node.right)
#         
#         # Post-order: Update global max_diameter
#         self.max_diameter = max(self.max_diameter, left_depth + right_depth)
#         
#         # Return depth for parent's calculation
#         return 1 + max(left_depth, right_depth)
```
Visualizer: `div_mydata-diameter-of-binary-tree`.

## 总结 (Summary)

1.  **Universal Approach:** View binary tree problems through the lens of recursive traversal. The core framework is always `traverse(left_child)`, `traverse(right_child)`, with logic placed in pre-order, in-order, or post-order positions relative to these calls.
2.  **Two Thinking Modes:**
    - **Traversal ("遍历"思维):** Use a `void traverse(...)` function (or returns `None`), often with external/instance variables, to iterate and collect results or perform actions.
    - **Decomposition ("分解问题"思维 / Divide and Conquer):** Define `return_type solve_subtree(...)`. Recursively solve for children, then combine these results (usually in post-order) with the current node's information to solve for the current subtree and return that result.
3.  **Node-Specific Logic:** For either mode, determine what each individual node needs to do and *when* (pre/in/post-order) it should do it.
4.  **Post-order Power:** The post-order position is especially powerful for decomposition-style solutions, as it naturally allows combining results from fully processed left and right subtrees.
5.  **DFS vs. Backtracking vs. DP on Trees:**
    - DFS (Traversal thinking, node-focused): e.g., `root.val++`.
    - Backtracking (Traversal thinking, edge/path-focused): e.g., exploring paths, making/undoing choices in `for child : children`.
    - DP (Decomposition thinking, subtree-focused): e.g., `count(root) = 1 + count(left) + count(right)`.

This framework provides a robust way to approach and solve a wide variety of binary tree problems.

---
Parent: [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal Algorithms Index]]
Related Problems: [[Interview/Practice/LeetCode/LC104 - Maximum Depth of Binary Tree|LC104]], [[Interview/Practice/LeetCode/LC144 - Binary Tree Preorder Traversal|LC144]], [[Interview/Practice/LeetCode/LC543 - Diameter of Binary Tree|LC543]]
Further Reading:
- [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/01 - Tree Problems - Traversal Thinking Part 1|Traversal Thinking Part 1]]
- [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/04 - Tree Problems - Decomposition Thinking Part 1|Decomposition Thinking Part 1]]
- [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/06 - Tree Problems - Post-Order Solutions Part 1|Post-Order Solutions Part 1]]
- [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/11 - Tree Problems - Level-Order (BFS) Part 1|Level-Order (BFS) Part 1]]
