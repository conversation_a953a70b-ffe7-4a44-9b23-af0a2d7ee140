---
tags: [concept/algorithms, concept/tree_traversal, concept/binary_tree, type/problem_solving_framework, pattern/lca]
aliases: [LCA Framework, Lowest Common Ancestor, 最近公共祖先框架]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树的拓展延伸/拓展：最近公共祖先系列解题框架.md]].
> This note outlines a general framework for solving Lowest Common Ancestor (LCA) problems on binary trees and binary search trees.

# Lowest Common Ancestor (LCA): Problem Solving Framework

The Lowest Common Ancestor (LCA) of two nodes `p` and `q` in a tree is defined as the lowest (i.e., deepest) node that has both `p` and `q` as descendants (where we allow a node to be a descendant of itself). Finding the LCA is a classic tree problem with applications in various domains, including version control systems like Git (as mentioned by Labuladong for `git rebase`).

## Case 1: LCA in a Binary Tree (Not necessarily a BST) - LC236

This is [[Interview/Practice/LeetCode/LC236 - Lowest Common Ancestor of a Binary Tree|LC236 - Lowest Common Ancestor of a Binary Tree]].
The core idea is recursive, using the "decomposition" thinking mode from [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]].

**Function Definition:** `find(root, p, q)`
- If `root` is `null`, or `root` is `p`, or `root` is `q`, then `root` itself is a candidate for LCA (or indicates presence of `p` or `q` in this subtree). Return `root`.
- Recursively search in the left subtree: `left_lca = find(root.left, p, q)`.
- Recursively search in the right subtree: `right_lca = find(root.right, p, q)`.

**Combining Results (Post-order logic):**
1.  If `left_lca` is not `null` AND `right_lca` is not `null`:
    This means `p` is found in one subtree and `q` in the other. So, the current `root` is the LCA. Return `root`.
2.  If `left_lca` is `null` AND `right_lca` is `null`:
    Neither `p` nor `q` were found in the subtrees of `root`. Return `null`.
3.  If `left_lca` is not `null` (and `right_lca` is `null`):
    Both `p` and `q` are in the left subtree (or one of them is `root.left` and the other is deeper in the left subtree). The LCA must be `left_lca`. Return `left_lca`.
4.  If `right_lca` is not `null` (and `left_lca` is `null`):
    Similar to case 3, but for the right subtree. Return `right_lca`.

```python
# Definition for a binary tree node.
# class TreeNode:
#     def __init__(self, x):
#         self.val = x
#         self.left = None
#         self.right = None

class SolutionLC236:
    def lowestCommonAncestor(self, root: 'TreeNode', p: 'TreeNode', q: 'TreeNode') -> 'TreeNode':
        # Base cases
        if not root:
            return None
        if root == p or root == q: # If root is p or q, it's a candidate for LCA
            return root

        # Recursively search in left and right subtrees
        left_lca = self.lowestCommonAncestor(root.left, p, q)
        right_lca = self.lowestCommonAncestor(root.right, p, q)

        # Post-order processing: combine results
        # Case 1: p and q are in different subtrees of root
        if left_lca and right_lca:
            return root

        # Case 2: p and q are not in subtrees of root (this case is covered by base cases
        #           or one of the following if p/q found deeper) - effectively handled by
        #           returning non-null if one is found.
        # if not left_lca and not right_lca: # Not strictly needed as written
        #     return None 

        # Case 3 & 4: p and q are in the same subtree (or one is an ancestor of other via this path)
        # If one subtree returned a node (p, q, or their LCA), and the other returned None,
        # then the non-None result is the LCA for this root's perspective.
        return left_lca if left_lca else right_lca
```

**Visualization for LC236:**
Tree: `root = [3,5,1,6,2,0,8,null,null,7,4]`, `p=5`, `q=1`
LCA should be `3`.

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\scriptsize},
    lca_node/.style={treenode, fill=green!30, double},
    target_node/.style={treenode, fill=yellow!40},
    level 1/.style={sibling distance=4cm, level distance=1.2cm},
    level 2/.style={sibling distance=2cm},
    level 3/.style={sibling distance=1cm},
    call_arrow/.style={->, dashed, blue, shorten >=1pt, shorten <=1pt}
]

\node[lca_node] (n3) at (0,0) {3}
    child { node[target_node] (n5) {5}
        child { node[treenode] (n6) {6} }
        child { node[treenode] (n2) {2} 
            child {node[treenode] (n7) {7}}
            child {node[treenode] (n4) {4}}
        }
    }
    child { node[target_node] (n1) {1}
        child { node[treenode] (n0) {0} }
        child { node[treenode] (n8) {8} }
    };

\node[text width=5cm, align=center, draw, fill=orange!10, rounded corners] at (5,0) {
    `find(3, p=5, q=1)`:\\
    `left_lca = find(5, 5, 1)` returns `5`.\\
    `right_lca = find(1, 5, 1)` returns `1`.\\
    Since `left_lca (5)` and `right_lca (1)` are both non-null, `root (3)` is the LCA.
};
\draw[call_arrow] (n3) to[bend left=20] (n5);
\draw[call_arrow] (n3) to[bend right=20] (n1);
\end{tikzpicture}
```

## Case 2: LCA in a Binary Search Tree (BST) - LC235

This is [[Interview/Practice/LeetCode/LC235 - Lowest Common Ancestor of a Binary Search Tree|LC235 - Lowest Common Ancestor of a BST]].
The BST property allows for a more direct, non-recursive (or simpler recursive) solution.

**Function Definition:** `find_bst(root, p, q)`
- If `root.val` is greater than both `p.val` and `q.val`, the LCA must be in the left subtree. Recurse: `find_bst(root.left, p, q)`.
- If `root.val` is less than both `p.val` and `q.val`, the LCA must be in the right subtree. Recurse: `find_bst(root.right, p, q)`.
- Otherwise (`root.val` is between `p.val` and `q.val`, or equal to one of them), `root` is the LCA (because `p` and `q` would diverge into different subtrees from this point, or one is `root` itself). Return `root`.

```python
class SolutionLC235:
    def lowestCommonAncestor(self, root: 'TreeNode', p: 'TreeNode', q: 'TreeNode') -> 'TreeNode':
        curr = root
        while curr:
            if p.val < curr.val and q.val < curr.val:
                # Both p and q are in the left subtree
                curr = curr.left
            elif p.val > curr.val and q.val > curr.val:
                # Both p and q are in the right subtree
                curr = curr.right
            else:
                # curr is the LCA. This happens when:
                # 1. p.val <= curr.val <= q.val (or q.val <= curr.val <= p.val)
                # 2. curr.val == p.val (p is LCA)
                # 3. curr.val == q.val (q is LCA)
                return curr
        return None # Should not be reached if p and q are in the tree
```

## Case 3: LCA Variations (from Labuladong's Article)

The article mentions several variations which are often LeetCode premium problems:
- **LC1644: LCA of a Binary Tree II:** Nodes `p` and `q` might *not* be present in the tree. The standard LC236 solution assumes they are present. To handle this, you'd need to modify the recursion to return not just the LCA candidate, but also boolean flags indicating if `p` was found and if `q` was found in the subtree. The final LCA is valid only if both `p` and `q` were found in the overall tree.
- **LC1650: LCA of a Binary Tree III:** Nodes have a `parent` pointer. This changes the problem significantly. One approach is to find the depth of `p` and `q`. Move the deeper node up until both are at the same depth. Then move both `p` and `q` upwards simultaneously until they meet. This meeting point is the LCA. (Similar to finding intersection of two linked lists).
- **LC1676: LCA of a Binary Tree IV:** Find LCA of a *set* of nodes. The recursive approach can be extended. `find(root, set_of_nodes)` would return `root` if `root` is one of the target nodes, or if `left_lca` and `right_lca` are both non-null (meaning target nodes are split), or if one child found multiple/all targets. A helper count of target nodes found in a subtree can be useful.

## 总结 (Summary)
- The Lowest Common Ancestor (LCA) is a fundamental tree problem.
- **For general Binary Trees (LC236):** A recursive DFS approach works effectively. A node is the LCA if `p` and `q` are found in its different subtrees, or if the node itself is `p` or `q` and the other is in one of its subtrees.
- **For Binary Search Trees (LC235):** The BST property allows a more efficient iterative (or recursive) search by comparing `root.val` with `p.val` and `q.val` to decide which subtree to explore.
- **Variations** may involve nodes not being present, parent pointers, or finding LCA for multiple nodes, requiring modifications to the base algorithms.
- The recursive "decomposition" thinking is key: `LCA(root, p, q)` depends on `LCA(root.left, p, q)` and `LCA(root.right, p, q)`.

---
Parent: [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal Algorithms]]
Related Problems: [[Interview/Practice/LeetCode/LC235 - Lowest Common Ancestor of a Binary Search Tree|LC235]], [[Interview/Practice/LeetCode/LC236 - Lowest Common Ancestor of a Binary Tree|LC236]]
Related Concepts: [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Principles]], [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursive Thinking Modes]]
