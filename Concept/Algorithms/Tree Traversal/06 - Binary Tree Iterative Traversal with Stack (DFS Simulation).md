---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, concept/stack, type/algorithm_variant]
aliases: [Iterative Tree Traversal, Stack Based Tree Traversal, DFS Simulation with Stack, 二叉树迭代遍历]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树的拓展延伸/拓展：用栈模拟递归迭代遍历二叉树.md]].
> This note discusses how to convert recursive DFS traversals (pre-order, in-order, post-order) of a binary tree into iterative versions using a stack, effectively simulating the recursion call stack. While [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|recursive traversal]] is often more intuitive, iterative versions can be useful for avoiding recursion depth limits or for finer control.

# Binary Tree: Iterative Traversal with Stack (DFS Simulation)

While recursive Depth-First Search (DFS) traversals ([[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|pre-order, in-order, post-order]]) are natural for binary trees, they can sometimes lead to stack overflow errors for very deep trees. Iterative versions using an explicit stack can mitigate this and offer a different way to think about the traversal process. These iterative methods essentially simulate the function call stack used by recursion.

Labuladong points out that while simpler iterative templates exist just for collecting node values in a specific order, the goal here is a *general* iterative framework where pre-order, in-order, and post-order logic can be placed, mimicking the recursive framework:

```python
# Recursive Framework Reference
# def traverse_recursive(root):
#     if root is None: return
#     # Pre-order position
#     # print(f"Pre: {root.val}")
#     traverse_recursive(root.left)
#     # In-order position
#     # print(f"In: {root.val}")
#     traverse_recursive(root.right)
#     # Post-order position
#     # print(f"Post: {root.val}")
```

## Iterative Pre-order Traversal

This is the most straightforward iterative DFS.
1. Initialize an empty stack and push the root node.
2. While the stack is not empty:
   a. Pop a node `curr` from the stack.
   b. Process `curr` (this is the pre-order position).
   c. Push `curr.right` child (if it exists) onto the stack.
   d. Push `curr.left` child (if it exists) onto the stack.
   (Note: Right child is pushed before left so that left is processed first, maintaining pre-order logic).

```python
class Solution:
    def preorderTraversal_iterative(self, root: TreeNode) -> list[int]:
        if not root:
            return []

        stack = [root]
        result = []

        while stack:
            node = stack.pop()
            # Pre-order action: process node
            result.append(node.val)

            # Push right child first, then left child (to process left before right)
            if node.right:
                stack.append(node.right)
            if node.left:
                stack.append(node.left)
        return result
```
This simple template is mainly for collecting nodes in pre-order. A more general template that allows placing arbitrary pre/in/post logic is more complex.

## General Iterative Framework (Simulating Recursion)

To create a general iterative framework that truly mimics the recursive pre-order, in-order, and post-order "slots," we need a way to know when a node is being visited for the first time (for pre-order), after its left subtree (for in-order), or after both subtrees (for post-order).

Labuladong's article "拓展：用栈模拟递归迭代遍历二叉树" likely details a universal iterative template. A common approach involves pushing nodes onto the stack and, for in-order and post-order, re-pushing them with a marker or using multiple stack pops to signify different processing stages.

### Simplified Universal Iterative Traversal (More Complex Example)

This is a more advanced concept often using a stack of `(node, state)` tuples or by adding nodes multiple times with special handling. Here's a conceptual structure often seen for a unified approach, particularly powerful for post-order:

**Conceptual Iterative Post-order (Can be adapted for others):**
One common way to achieve iterative post-order is to use two stacks or one stack with a clever trick. Another is to process nodes in Root-Right-Left order (a modified pre-order) and then reverse the result.

A more direct simulation might involve:
1. Push root onto stack.
2. Loop while stack is not empty:
   - `curr = stack.top()`
   - If `curr` is being visited for the first time (e.g., just pushed or its children haven't been processed):
     - If `curr.left` exists and not visited, push `curr.left`.
     - Else if `curr.right` exists and not visited, push `curr.right`.
     - Else (both children visited or null), pop `curr`, process it (post-order).
This requires a way to mark nodes as "visited" or to know which children have been processed.

**Labuladong's Universal Iterative Approach (Likely Focuses on In-order & Post-order as Pre-order is simpler):**
For **in-order traversal iteratively**:
1. Initialize an empty stack and `curr = root`.
2. While `curr` is not `None` OR stack is not empty:
   a. While `curr` is not `None`: Push `curr` onto stack, `curr = curr.left` (go as left as possible).
   b. `curr = stack.pop()`.
   c. Process `curr` (this is the in-order position).
   d. `curr = curr.right` (move to the right subtree).

```python
class Solution:
    def inorderTraversal_iterative(self, root: TreeNode) -> list[int]:
        if not root:
            return []

        stack = []
        curr = root
        result = []

        while curr or stack:
            # Reach the leftmost node of the curr node
            while curr:
                stack.append(curr)
                curr = curr.left

            # Current must be None at this point
            curr = stack.pop()
            # In-order action: process node
            result.append(curr.val)

            # Visit right subtree
            curr = curr.right

        return result
```

For **post-order traversal iteratively** (often trickier):
One common method:
1. Use a stack and start with `root`.
2. While stack is not empty:
   a. Pop `node`. Add `node.val` to the *beginning* of the result list.
   b. Push `node.left` (if exists).
   c. Push `node.right` (if exists).
   (The order of pushing left then right ensures that when popped, right is processed before left, and prepending to result list reverses to correct post-order).

```python
class Solution:
    def postorderTraversal_iterative(self, root: TreeNode) -> list[int]:
        if not root:
            return []

        stack = [root]
        result = [] # Or use collections.deque for efficient appendleft

        while stack:
            node = stack.pop()
            result.append(node.val) # Add to result

            # Push left child first, then right child
            # So that right child is popped (and processed into result) before left child
            if node.left:
                stack.append(node.left)
            if node.right:
                stack.append(node.right)

        return result[::-1] # Reverse the result to get actual post-order
        # If using deque.appendleft(node.val) for result, no final reverse needed.
```
This effectively computes a "Root-Right-Left" traversal, and reversing it gives "Left-Right-Root" (post-order).

**Labuladong's Goal: A Truly Universal Iterative Framework**
The text implies a desire for a single iterative loop structure where specific `if` blocks can house pre-order, in-order, and post-order logic directly, truly simulating the recursive call stack's behavior at those points. This is more advanced and might involve pushing nodes multiple times or using state flags on the stack.

Consider a stack that stores not just nodes, but `(node, visited_children_count)` or similar state.
- When a node is popped:
  - If `visited_children_count == 0`: This is like the **pre-order** position. Mark it, push right child, then left child (so left is processed first), then push the node back with `visited_children_count = 1`.
  - If `visited_children_count == 1`: This means left child has been processed. This is like the **in-order** position. Mark it, push right child (if any), then push node back with `visited_children_count = 2`.
  - If `visited_children_count == 2`: This means both children processed. This is the **post-order** position. Process the node.

This is more complex than the simpler iterative traversals above but offers the generality Labuladong seeks.

## 总结 (Summary)
- Iterative DFS traversals of binary trees simulate recursion using an explicit stack.
- **Iterative Pre-order:** Relatively simple; pop node, process, push right then left.
- **Iterative In-order:** Go left until null, pushing nodes; pop, process, go right.
- **Iterative Post-order:** Often done by a modified pre-order (Root-Right-Left) then reversing the result, or by more complex stack manipulations to ensure children are processed before the parent.
- A universal iterative template mimicking all three recursive "slots" (pre, in, post) requires careful state management on the stack, potentially pushing nodes multiple times or storing state flags.
- While educational for understanding stack mechanics and recursion simulation, standard recursive solutions are often more concise and easier to reason about for most binary tree problems in interviews, unless specific constraints (like avoiding recursion depth limits) are present.

---
Parent: [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal Algorithms]]
Next: [[Interview/Concept/Algorithms/Tree Traversal/07 - Lowest Common Ancestor (LCA) Framework|Lowest Common Ancestor (LCA) Framework]]
Related: [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Recursive Tree Traversal]], [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Principles]]
