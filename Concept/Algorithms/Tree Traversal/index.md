---
tags: [index, concept/algorithms, concept/tree_traversal, concept/dfs, concept/bfs]
aliases: [Tree Traversal Algorithm Index, Tree DFS BFS]
---

# Tree Traversal Algorithms

This section covers algorithms for traversing tree data structures, primarily focusing on Depth-First Search (DFS) and Breadth-First Search (BFS) strategies and their variants.

## Binary Tree Traversals
- [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Binary Tree Recursive Traversal (DFS)]]
  - Pre-order Traversal
  - In-order Traversal
  - Post-order Traversal
- [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Binary Tree Level-Order Traversal (BFS)]]

## N-ary Tree Traversals
- [[Interview/Concept/Algorithms/Tree Traversal/02 - N-ary Tree Recursive Traversal (DFS)|N-ary Tree Recursive Traversal (DFS)]]
  - Pre-order Traversal
  - Post-order Traversal
- [[Interview/Concept/Algorithms/Tree Traversal/03 - N-ary Tree Level-Order Traversal (BFS)|N-ary Tree Level-Order Traversal (BFS)]]

## Core Principles & Frameworks
- [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree - Core Algorithmic Principles]]

## Comparison and Use Cases
- [[Interview/Concept/Algorithms/Tree Traversal/04 - DFS vs BFS - When to Use Which|DFS vs. BFS - When to Use Which]]

## Problem-Solving Patterns (Labuladong Inspired)
- [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Binary Tree Problem-Solving Patterns]]

## Visualization
```mermaid
graph TD
    TT["Tree Traversal"] --> BT_T["Binary Tree Traversals"]
    TT --> NT_T["N-ary Tree Traversals"]
    TT --> CorePrinc["[[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Core Principles]]"]
    TT --> Compare["[[Interview/Concept/Algorithms/Tree Traversal/04 - DFS vs BFS - When to Use Which|DFS vs. BFS Comparison]]"]

    BT_T --> BT_DFS["[[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Recursive (DFS)]]"]
    BT_DFS --> PreO["Pre-order"]
    BT_DFS --> InO["In-order"]
    BT_DFS --> PostO["Post-order"]
    BT_T --> BT_BFS["[[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Level-Order (BFS)]]"]

    NT_T --> NT_DFS["[[Interview/Concept/Algorithms/Tree Traversal/02 - N-ary Tree Recursive Traversal (DFS)|Recursive (DFS)]]"]
    NT_DFS --> NT_PreO["Pre-order"]
    NT_DFS --> NT_PostO["Post-order"]
    NT_T --> NT_BFS["[[Interview/Concept/Algorithms/Tree Traversal/03 - N-ary Tree Level-Order Traversal (BFS)|Level-Order (BFS)]]"]


    PatternIndex["Problem Patterns"]
    TT --> PatternIndex["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Problem Patterns]]"]

    classDef main fill:#e0ffff,stroke:#008b8b,stroke-width:2px;
    class TT main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
