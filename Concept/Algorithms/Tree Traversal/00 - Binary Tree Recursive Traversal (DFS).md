---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, concept/recursion, pattern/divide_and_conquer, type/algorithm]
aliases: [Binary Tree DFS, Recursive Tree Traversal, Preorder, Inorder, Postorder, 前序遍历, 中序遍历, 后序遍历]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/二叉树结构及遍历/二叉树的递归_层序遍历.md]].
> Labuladong emphasizes that the recursive traversal framework is foundational for many tree algorithms and even extends to general DFS and backtracking.

# Binary Tree Recursive Traversal (DFS-style)

Recursive traversal is a natural way to visit all nodes in a binary tree, mirroring its recursive structure. This approach is a form of Depth-First Search (DFS). The core idea involves visiting a node and then recursively visiting its left and right subtrees. The order in which these three operations (visit node, traverse left, traverse right) are performed defines the specific type of recursive traversal.

## 🌲 The Core Recursive Traversal Framework

The fundamental structure of a recursive traversal function for a binary tree is:

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

def traverse(root: TreeNode):
    if root is None:
        return

    # (1) "Pre-order" position: Code here executes before traversing subtrees.
    # print(f"Pre-order: {root.val}") # Example: Visit node data

    traverse(root.left)

    # (2) "In-order" position: Code here executes after left subtree, before right.
    # print(f"In-order: {root.val}") # Example: Visit node data

    traverse(root.right)

    # (3) "Post-order" position: Code here executes after both subtrees.
    # print(f"Post-order: {root.val}") # Example: Visit node data
```

**Understanding the Traversal Path:**
The `traverse` function can be visualized as a "pointer" moving through the tree. It always attempts to go left first. If it can't go left (hits `None` or already traversed left), it tries to go right. If it can't go right, it backtracks (returns from the recursive call). The actual "visiting" or processing of a node's data can occur at different points in this journey, leading to different traversal orders.

Labuladong provides interactive visualizers for this. Conceptually, `root` pointer moves:
1. Down the leftmost path.
2. When a null is hit, it backtracks.
3. Processes the node (depending on pre/in/post order logic).
4. Tries to go right.
5. Repeats from step 1 for the right child.

The recursive calls `traverse(root.left)` and `traverse(root.right)` define the fixed movement pattern. The placement of your custom logic (`# Your code here`) determines the traversal type.

## 🧭 The Three Key Positions and Traversal Types

The "magic" of recursive traversal lies in understanding what information is available and what tasks are appropriate at each of these three positions within the `traverse` function.

### 1. Pre-order Traversal (Root, Left, Right)
   - **Logic Placement:** Code is placed *before* the recursive calls to `traverse(root.left)` and `traverse(root.right)`.
   - **Timing:** The node is processed as soon as it's entered (visited for the first time by the conceptual pointer).
   - **Use Cases:**
     - Making a copy of the tree.
     - Path-finding problems where decisions are made at the current node before exploring children.
     - Constructing a prefix expression from an expression tree.
	- 
```python
def preorder_traversal(root: TreeNode, result: list):
    if root is None:
        return
    result.append(root.val)  # Process node (e.g., add to list)
    preorder_traversal(root.left, result)
    preorder_traversal(root.right, result)
```



### 2. In-order Traversal (Left, Root, Right)
   - **Logic Placement:** Code is placed *between* the `traverse(root.left)` call and the `traverse(root.right)` call.
   - **Timing:** The node is processed after its entire left subtree has been processed, but before its right subtree is processed.
   - **Use Cases:**
     - **Crucially for [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Binary Search Trees (BSTs)]]: In-order traversal of a BST visits nodes in ascending sorted order.** This is a very important property.
     - Flattening a BST into a sorted list.
 - 
```python
def inorder_traversal(root: TreeNode, result: list):
	if root is None:
		return
	inorder_traversal(root.left, result)
	result.append(root.val)  # Process node
	inorder_traversal(root.right, result)
```


### 3. Post-order Traversal (Left, Right, Root)
   - **Logic Placement:** Code is placed *after* both recursive calls (`traverse(root.left)` and `traverse(root.right)`).
   - **Timing:** The node is processed only after its entire left subtree AND its entire right subtree have been processed. This means you have information about the children available when processing the parent.
   - **Use Cases:**
     - Deleting or freeing nodes in a tree (children must be processed before the parent).
     - Calculating tree properties that depend on children's properties (e.g., height, size, checking if a tree is balanced). Many dynamic programming problems on trees use post-order traversal.
     - Constructing a postfix expression from an expression tree.
-
```python
def postorder_traversal(root: TreeNode, result: list):
    if root is None:
        return
    postorder_traversal(root.left, result)
    postorder_traversal(root.right, result)
    result.append(root.val)  # Process node
```

## Visualization of Traversal Orders

Consider the tree:
```
      F
     / \
    B   G
   / \   \
  A   D   I
     / \   /
    C   E H
```
Or Labuladong's example tree for visualization:
```
        1
       / \
      2   3
     /   / \
    4   5   6
```

The following diagram illustrates the "path" of the `traverse` function and when each node would be "processed" for pre, in, and post-order. The numbers indicate the sequence of processing for that specific order.

```tikz
\begin{tikzpicture}[
    tn/.style={circle, draw, font=\sffamily\bfseries, minimum size=8mm},
    tp/.style={font=\tiny\sffamily, red}, % Pre-order
    ti/.style={font=\tiny\sffamily, blue}, % In-order
    tpo/.style={font=\tiny\sffamily, green!50!black}, % Post-order
    level 1/.style={sibling distance=35mm, level distance=15mm},
    level 2/.style={sibling distance=20mm},
    level 3/.style={sibling distance=15mm}
]

\node[tn] (n1) {1}
    child {node[tn] (n2) {2}
        child {node[tn] (n4) {4}
            edge from parent node[left, pos=0.2, tp] {2} node[left, pos=0.8, ti] {2} node[left, pos=0.9, tpo] {1}
        }
        child[missing]
        edge from parent node[left, pos=0.2, tp] {1} node[left, pos=0.8, ti] {3} node[left, pos=0.9, tpo] {2}
    }
    child {node[tn] (n3) {3}
        child {node[tn] (n5) {5}
            edge from parent node[left, pos=0.2, tp] {4} node[left, pos=0.8, ti] {5} node[left, pos=0.9, tpo] {3}
        }
        child {node[tn] (n6) {6}
            edge from parent node[right, pos=0.2, tp] {5} node[right, pos=0.8, ti] {7} node[right, pos=0.9, tpo] {4}
        }
        edge from parent node[right, pos=0.2, tp] {3} node[right, pos=0.8, ti] {6} node[right, pos=0.9, tpo] {5}
    };

% Annotations for processing order
\node[tp, above left=1mm of n1] {P:1}; \node[ti, below left=1mm of n1] {I:4}; \node[tpo, below right=1mm of n1] {Po:6};
\node[tp, above left=1mm of n2] {P:2}; \node[ti, below left=1mm of n2] {I:2}; \node[tpo, below right=1mm of n2] {Po:2};
\node[tp, above left=1mm of n3] {P:4}; \node[ti, below left=1mm of n3] {I:6}; \node[tpo, below right=1mm of n3] {Po:5};
\node[tp, above left=1mm of n4] {P:3}; \node[ti, below left=1mm of n4] {I:1}; \node[tpo, below right=1mm of n4] {Po:1};
\node[tp, above left=1mm of n5] {P:5}; \node[ti, below left=1mm of n5] {I:5}; \node[tpo, below right=1mm of n5] {Po:3};
\node[tp, above left=1mm of n6] {P:6}; \node[ti, below left=1mm of n6] {I:7}; \node[tpo, below right=1mm of n6] {Po:4};

\node at (0, -5.5) [text width=10cm, align=center, draw, rounded corners, fill=yellow!10]
    {
        \textbf{Processing Order Example (Node Values)} \\
        Tree: 1 -> (2 -> (4, None), 3 -> (5, 6)) \\
        \textbf{\textcolor{red}{Pre-order (P):}} 1, 2, 4, 3, 5, 6 \\
        \textbf{\textcolor{blue}{In-order (I):}} 4, 2, 1, 5, 3, 6 \\
        \textbf{\textcolor{green!50!black}{Post-order (Po):}} 4, 2, 5, 6, 3, 1 \\
        (Numbers P:X, I:Y, Po:Z near nodes indicate the X-th node processed in Pre-order, etc.)
    };
\end{tikzpicture}
```

Labuladong's original visualization dynamically colors nodes. The TikZ diagram above tries to capture the sequence statically.

## 🤔 Why is this important?
Understanding these three positions is critical because many tree algorithms involve performing specific actions at these precise moments.
- If you need to process a node *before* its children (e.g. create a parent node before attaching children), use the pre-order position.
- If you need information from the left child *before* processing the current node, and then process the right child, use the in-order position.
- If you need information from *both* children before processing the current node (e.g. calculate size/height, check balance), use the post-order position. This is extremely common for "divide and conquer" style tree problems.

## 总结 (Summary)
- Recursive binary tree traversal is a form of DFS.
- The core framework involves a base case (null node) and recursive calls for left and right children.
- **Pre-order:** Process node, then left subtree, then right subtree.
- **In-order:** Process left subtree, then node, then right subtree. (Yields sorted data for BSTs).
- **Post-order:** Process left subtree, then right subtree, then node. (Useful for calculations depending on children).
- The choice of where to place processing logic (pre, in, or post recursive calls) determines the traversal type and is key to solving many tree problems.

---
Parent: [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal Algorithms]]
Previous: [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Binary Tree Introduction]]
Next: [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Binary Tree Level-Order Traversal (BFS)]]
Related: [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Binary Search Trees (BSTs)]] (especially in-order)
