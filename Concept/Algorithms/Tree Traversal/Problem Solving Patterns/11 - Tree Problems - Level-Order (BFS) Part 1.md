---
tags: [concept/algorithms, concept/tree_traversal, concept/bfs, pattern/tree_problem, course/labuladong]
aliases: [Tree BFS Applications, Level Order Views, 二叉树层序遍历应用一]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's discussion on using level-order traversal (BFS) for binary tree problems, particularly for tasks requiring a level-by-level view.
> Original source context: [[{source_file_path}|【练习】运用层序遍历解题 I]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/10 - BST Problems - Modification and Construction Part 2|Prev: BST Problems Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/12 - Tree Problems - Level-Order (BFS) Part 2|Next: Level-Order (BFS) Part 2]] |

# Tree Problems: Using Level-Order Traversal (BFS) Part 1 - Level-based Views

While most binary tree problems can be solved recursively (DFS), some are more naturally or efficiently solved using [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Level-Order Traversal (BFS)]]. BFS is ideal when the problem involves processing the tree layer by layer, or when information about nodes at the same depth is important.

## 核心概念 (Core Concept)
Level-order traversal uses a queue to explore the tree. Nodes are visited in order of their depth: all nodes at depth `d` are visited before any nodes at depth `d+1`. The standard "Pattern 2" from Labuladong's BFS explanation (see [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Binary Tree Level-Order Traversal (BFS)]]) is key here, as it processes nodes one full level at a time.

## 通用模板 (General Template - Collecting Nodes by Level)
This is the direct application of LeetCode 102. Binary Tree Level Order Traversal.

```python
from collections import deque

class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionLevelOrder:
    def levelOrder(self, root: TreeNode | None) -> list[list[int]]:
        if not root:
            return []

        result_per_level = []
        queue = deque([root])

        while queue:
            level_size = len(queue) # Number of nodes at the current level
            current_level_nodes = []

            for _ in range(level_size): # Process all nodes at this level
                node = queue.popleft()
                current_level_nodes.append(node.val)

                if node.left:
                    queue.append(node.left)
                if node.right:
                    queue.append(node.right)
            
            result_per_level.append(current_level_nodes)
            
        return result_per_level

# Example Usage:
# root = TreeNode(3, TreeNode(9), TreeNode(20, TreeNode(15), TreeNode(7)))
# solver = SolutionLevelOrder()
# print(solver.levelOrder(root)) # Expected: [[3], [9, 20], [15, 7]]
```
### 模板解析 (Template Explanation)
1.  **Queue Initialization:** Start with the `root` in the queue.
2.  **Outer Loop:** Continues as long as there are nodes to process (`while queue`).
3.  **Level Processing:**
    *   `level_size = len(queue)`: Determines how many nodes are in the current level.
    *   `current_level_nodes = []`: To store values of nodes at this level.
    *   **Inner Loop (`for _ in range(level_size)`):** Dequeues `level_size` nodes, processes them (adds value to `current_level_nodes`), and enqueues their children for the next level.
4.  **Store Level:** After processing all nodes for the current level, `current_level_nodes` is added to `result_per_level`.

## 示例图示 (Diagram Example: Level Order Grouping)
Tree: `[3,9,20,null,null,15,7]`
```
    3
   / \
  9  20
    /  \
   15   7
```
```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\small},
    queue_state/.style={rectangle, draw, fill=yellow!20, font=\sffamily\tiny, align=center, text width=4cm},
    level_label/.style={font=\sffamily\bfseries\small, blue},
    level 1/.style={sibling distance=3cm},
    level 2/.style={sibling distance=2cm}
]

% Tree
\node[treenode] (n3) at (0,0) {3};
\node[treenode] (n9) at (-1.5,-1.5) {9};
\node[treenode] (n20) at (1.5,-1.5) {20};
\node[treenode] (n15) at (0.75,-3) {15};
\node[treenode] (n7) at (2.25,-3) {7};
\draw (n3) -- (n9); \draw (n3) -- (n20);
\draw (n20) -- (n15); \draw (n20) -- (n7);

% Queue processing
\node[level_label] at (5,0) {Level 0 Processing};
\node[queue_state] (q1) at (5,-0.75) {Queue: `deque([Node(3)])`\\`level_size=1`. Dequeue 3. Add 3 to `lvl_nodes`.\\Enqueue 9, 20. Result: `[[3]]`};

\node[level_label] at (5,-2.5) {Level 1 Processing};
\node[queue_state] (q2) at (5,-3.25) {Queue: `deque([Node(9), Node(20)])`\\`level_size=2`. Dequeue 9, then 20.\\ Add 9, 20 to `lvl_nodes`. Enqueue 15, 7.\\Result: `[[3], [9,20]]`};

\node[level_label] at (5,-5) {Level 2 Processing};
\node[queue_state] (q3) at (5,-5.75) {Queue: `deque([Node(15), Node(7)])`\\`level_size=2`. Dequeue 15, then 7.\\Add 15, 7 to `lvl_nodes`. No children.\\Result: `[[3], [9,20], [15,7]]`};

\node at (5,-7) {Queue empty. Done.};
\end{tikzpicture}
```
This iterative, level-by-level processing is characteristic of BFS and is directly applicable to problems requiring such views (e.g., finding level sums, right side view, zigzag traversal with minor modifications).

## 总结 (Summary)
- Level-order traversal (BFS) is used when problems require processing a tree layer by layer.
- The key is using a queue and, for per-level grouping, processing `len(queue)` elements in an inner loop before moving to the "next level" of enqueued children.
- This pattern forms the basis for solutions to many "view" related tree problems (e.g., right side view, level averages) and finding shortest paths in terms of levels.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/10 - BST Problems - Modification and Construction Part 2|Prev: BST Problems Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/12 - Tree Problems - Level-Order (BFS) Part 2|Next: Level-Order (BFS) Part 2]] |
