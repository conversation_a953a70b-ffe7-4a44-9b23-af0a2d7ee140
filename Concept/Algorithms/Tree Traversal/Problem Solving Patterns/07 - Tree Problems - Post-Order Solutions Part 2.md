---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, concept/recursion, pattern/tree_problem, course/labuladong]
aliases: [Advanced Post-Order, Tree DP Post-Order, 二叉树后序遍历应用二]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's emphasis on leveraging the post-order traversal position for solving tree problems, particularly those with more complex dependencies on children's states.
> Original source context: [[{source_file_path}|【练习】利用后序位置解题 II]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/06 - Tree Problems - Post-Order Solutions Part 1|Prev: Post-Order Solutions Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/08 - Tree Problems - Post-Order Solutions Part 3|Next: Post-Order Solutions Part 3]] |

# Tree Problems: Leveraging Post-Order Traversal Part 2 - Complex Dependencies

Building upon [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/06 - Tree Problems - Post-Order Solutions Part 1|basic aggregations]], the post-order position is also critical for problems where the solution for a node depends on more nuanced information or multiple pieces of data returned from its children's subtrees. This often appears in dynamic programming on trees or problems requiring sophisticated state combination.

## 核心概念 (Core Concept)
The strength of post-order processing lies in its "bottom-up" nature. When the code at a node `curr`'s post-order slot executes, `curr` has full knowledge of what its children's subtrees "decided" or "computed". This allows `curr` to make an informed decision or computation for the subtree it roots.

## 通用模板 (General Template - Example: Lowest Common Ancestor LC236)
LeetCode 236. Lowest Common Ancestor of a Binary Tree ([[Interview/Practice/LeetCode/LC236 - Lowest Common Ancestor of a Binary Tree|LC236]]) is a prime example. The function `find_lca(root, p, q)` typically returns:
- `p` if `p` is found in `root`'s subtree (and `q` is not, or `p` is an ancestor of `q`).
- `q` if `q` is found in `root`'s subtree (and `p` is not, or `q` is an ancestor of `p`).
- The LCA node if `p` and `q` are found in different subtrees of `root`.
- `None` if neither `p` nor `q` are found in `root`'s subtree.

The logic to combine these possibilities happens in post-order.
```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionLCA:
    def lowestCommonAncestor(self, root: TreeNode, p: TreeNode, q: TreeNode) -> TreeNode | None:
        # Base cases for recursion
        if not root:
            return None
        if root == p or root == q: # If root is p or q, it's a candidate
            return root

        # Recursively search in left and right subtrees
        left_result = self.lowestCommonAncestor(root.left, p, q)
        right_result = self.lowestCommonAncestor(root.right, p, q)

        # === Post-order position ===
        # Now, left_result and right_result contain information from children
        
        # Case 1: p and q are found in different subtrees of current 'root'.
        # Then, current 'root' is the LCA.
        if left_result and right_result:
            return root
        
        # Case 2: p and q are both in the left subtree (or one is root.left).
        # Then left_result is the LCA.
        if left_result:
            return left_result
            
        # Case 3: p and q are both in the right subtree (or one is root.right).
        # Then right_result is the LCA.
        if right_result:
            return right_result
            
        # Case 4: Neither p nor q are in the subtrees of current 'root'.
        return None
```
The full logic and explanation can be found at [[Interview/Concept/Algorithms/Tree Traversal/07 - Lowest Common Ancestor (LCA) Framework|LCA Framework]].

### 模板解析 (Template Explanation)
1.  **Function Definition:** `lowestCommonAncestor(root, p, q)` returns the LCA of `p` and `q` if both are present in the subtree rooted at `root`, or returns `p` if only `p` is present, `q` if only `q` is present, or `None` if neither.
2.  **Base Cases:** If `root` is `None`, or `root` itself is `p` or `q`.
3.  **Recursive Calls:** Get results from left and right children.
4.  **Post-Order Logic:** Based on `left_result` and `right_result`:
    *   If both are non-null, `root` is the LCA.
    *   If only one is non-null, that non-null result is propagated upwards (it's either `p`, `q`, or their LCA found deeper).
    *   If both are null, neither `p` nor `q` were found below.

## 示例图示 (Diagram Example: LCA)
Tree: `root = [3,5,1,6,2,0,8,null,null,7,4]`, `p=5`, `q=1`. LCA is `3`.

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=8mm, font=\sffamily\scriptsize},
    targetnode/.style={treenode, fill=yellow!30},
    lcanode/.style={treenode, fill=green!30, double},
    call/.style={font=\sffamily\tiny\bfseries, align=center},
    arrow/.style={->, dashed},
    level 1/.style={sibling distance=5cm, level distance=1.8cm},
    level 2/.style={sibling distance=2.5cm},
    level 3/.style={sibling distance=1.2cm}
]

\node[lcanode] (r3) at (0,0) {3}
    child{ node[targetnode] (n5) {5} 
        child{ node[treenode] (n6) {6} }
        child{ node[treenode] (n2) {2}
            child{ node[treenode] (n7) {7} }
            child{ node[treenode] (n4) {4} }
        }
    }
    child{ node[targetnode] (n1) {1}
        child{ node[treenode] (n0) {0} }
        child{ node[treenode] (n8) {8} }
    };

% Annotations for LCA call at root (3)
\node[call, below left=0.5cm and -0.5cm of r3] (call_left_3) {Call LCA(5, p=5, q=1)\\$\rightarrow$ Returns 5};
\node[call, below right=0.5cm and -0.5cm of r3] (call_right_3) {Call LCA(1, p=5, q=1)\\$\rightarrow$ Returns 1};

\draw[arrow, blue] (r3.south) to[bend right=20] (call_left_3.north);
\draw[arrow, blue] (r3.south) to[bend left=20] (call_right_3.north);

\node[call, below=0.5cm of call_left_3, fill=green!10, draw, rounded corners, text width=6cm] (combine_3) at (0, -4.5) {
    Post-order at Node 3:\\
    `left_result = 5` (Node 5)\\
    `right_result = 1` (Node 1)\\
    Since both are non-null, Node 3 is the LCA. Return Node 3.
};
\draw[arrow, red, thick] (call_left_3.south) -- (combine_3.north west);
\draw[arrow, red, thick] (call_right_3.south) -- (combine_3.north east);

\end{tikzpicture}
```
The decision that node `3` is the LCA is made only after the recursive calls for its children (node `5` and node `1`) have returned their findings.

## 总结 (Summary)
- Post-order traversal is extremely powerful for problems where the solution for a node needs complete information from its subtrees.
- Many "find X in tree" or "property Y of tree" problems can be elegantly solved by defining what a recursive function should return for a subtree, and then combining these results in the post-order slot.
- This often forms the basis of dynamic programming solutions on trees.
- Key questions to ask for such problems:
    1. What information do I need from my children's subtrees to solve the problem for my current subtree?
    2. How do I combine this information with my own node's data?
    3. What should my recursive function return to its parent?

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/06 - Tree Problems - Post-Order Solutions Part 1|Prev: Post-Order Solutions Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/08 - Tree Problems - Post-Order Solutions Part 3|Next: Post-Order Solutions Part 3]] |
