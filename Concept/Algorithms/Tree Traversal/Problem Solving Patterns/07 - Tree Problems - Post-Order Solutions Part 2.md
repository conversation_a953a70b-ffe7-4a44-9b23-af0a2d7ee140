---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, concept/recursion, pattern/tree_problem, pattern/post_order, course/labuladong]
aliases: [Advanced Post-Order Applications, Tree DP Post-Order, 二叉树后序遍历应用二, Find Duplicate Subtrees]
---
> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷二叉树算法/二叉树心法（后序篇）.md]], focusing on LC652 Find Duplicate Subtrees.
> This note showcases using post-order traversal for problems requiring serialization or complex state aggregation from subtrees.

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/06 - Tree Problems - Post-Order Solutions Part 1|Prev: Post-Order Solutions Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/08 - Tree Problems - Post-Order Solutions Part 3|Next: Post-Order Solutions Part 3]] |

# Tree Problems: Leveraging Post-Order Traversal Part 2 - Subtree Identification & Complex Dependencies

This part continues exploring the power of post-order traversal. Here, we look at problems where identifying entire subtrees or their structural properties is key. The post-order position is ideal because a subtree's structure is fully defined only after its left and right children (and their subtrees) are processed.

## 核心概念 (Core Concept)
To identify or compare subtrees, we often need a way to represent a subtree uniquely, for example, by serializing it. This serialization (or unique representation) is best constructed in a post-order manner, as it depends on the serialized forms of its children.

## Example: Find Duplicate Subtrees (LC652)
[[Interview/Practice/LeetCode/LC652 - Find Duplicate Subtrees|LC652 - Find Duplicate Subtrees]]
The problem asks to find all duplicate subtrees. Two subtrees are duplicates if they have the same structure and node values. We need to return a list of root nodes of these duplicate subtrees (only one instance per duplicate type).

**Decomposition/Post-Order Approach:**
1.  **Serialize Subtrees:** Define a recursive function `traverse(node)` that returns a unique string representation (serialization) of the subtree rooted at `node`.
2.  **Store Serializations:** Use a hash map (`memo` or `subtree_counts`) to store the counts of each unique subtree serialization encountered.
3.  **Identify Duplicates:** If a serialization string is encountered and its count in the map becomes 2, it means this is the *first time* we've confirmed this subtree structure as a duplicate. Add the current `node` (which roots this duplicate subtree) to the result list.

**Serialization Strategy (Post-Order):**
A common way to serialize a subtree rooted at `node`:
`serialize(node) = str(node.val) + "," + serialize(node.left) + "," + serialize(node.right)`
- Use a special character (e.g., `#`) for null children to distinguish structures like `(1, #, 2)` from `(1, 2, #)`.
- The serialization is built in post-order implicitly by the recursive calls returning their serialized strings.

```python
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None): self.val = val; self.left = left; self.right = right
import collections

class SolutionDuplicateSubtrees:
    def findDuplicateSubtrees(self, root: TreeNode) -> list[TreeNode]:
        self.memo = collections.defaultdict(int) # Stores serialization -> count
        self.result_nodes = [] # Stores root nodes of duplicate subtrees

        self._serialize_and_count(root)
        return self.result_nodes

    def _serialize_and_count(self, node: TreeNode) -> str:
        if not node:
            return "#" # Represent null node with a special character

        # Recursively serialize left and right subtrees
        left_serialization = self._serialize_and_count(node.left)
        right_serialization = self._serialize_and_count(node.right)

        # === Post-order position ===
        # Construct serialization for the current subtree
        # Order matters: (node.val), (left_subtree_repr), (right_subtree_repr)
        # Using commas as separators
        current_subtree_serialization = str(node.val) + "," + left_serialization + "," + right_serialization
        
        # Count occurrences of this serialization
        self.memo[current_subtree_serialization] += 1
        
        # If count reaches 2, it's a duplicate. Add the node to results.
        # Only add it when count becomes 2 to avoid adding multiple instances of the same duplicate structure.
        if self.memo[current_subtree_serialization] == 2:
            self.result_nodes.append(node)
            
        return current_subtree_serialization
```
Labuladong's visualizations `![](/algo/images/binary-tree-iii/1.png)` to `![](/algo/images/binary-tree-iii/3.png)` show how different subtrees are identified and compared.

### 模板解析 (Template Explanation)
1.  **`_serialize_and_count(node)` Function:**
    *   **Definition:** This function serves two purposes:
        1.  It returns a string serialization of the subtree rooted at `node`.
        2.  As a side effect, it populates `self.memo` with counts of each encountered subtree serialization and `self.result_nodes` with duplicate subtree roots.
    *   **Base Case:** If `node` is `None`, return a representation for null (e.g., `"#"`).
    *   **Recursive Calls:** Get serializations for `left_child` and `right_child`.
    *   **Post-Order Logic:**
        *   Combine `node.val`, `left_serialization`, `right_serialization` to form `current_subtree_serialization`. The order and separators must be consistent to ensure unique representation.
        *   Increment its count in `self.memo`.
        *   If the count becomes 2, this is the first time this specific structure is identified as a duplicate, so add `node` to `self.result_nodes`.
    *   **Return Value:** The `current_subtree_serialization` string.

## 示例图示 (Diagram Example: Duplicate Subtrees)
Consider tree: `[1,2,3,4,null,2,4,null,null,4]`
Serialized forms that might appear:
- Leaf 4: `"4,#,#"`
- Subtree (2,4): `"2,4,#,#,#"` (assuming 4 is left child of 2, right is null)

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\small},
    dupnode/.style={treenode, fill=green!30, double},
    level 1/.style={sibling distance=3.5cm},
    level 2/.style={sibling distance=1.5cm},
    level 3/.style={sibling distance=1cm},
    note/.style={rectangle, draw, fill=yellow!10, font=\sffamily\tiny, text width=3.5cm, align=center}
]

\node[treenode] (n1) at (0,0) {1}
    child {node[treenode] (n2_L) {2} % Left child of 1
        child {node[dupnode] (n4_LL) {4}} % Left child of 2_L
    }
    child {node[treenode] (n3_R) {3} % Right child of 1
        child {node[treenode] (n2_RL) {2} % Left child of 3_R
            child {node[dupnode] (n4_RLL) {4}} % Left child of 2_RL
        }
        child {node[dupnode] (n4_RR) {4}} % Right child of 3_R
    };
\node[dupnode] (n4_RLL_R) at ($(n4_RLL)+(0.8,-1)$) {4}; % Adding a right child to 2_RL's left child 4 for a different example
% This last node makes the structure more complex than the simple LC example.
% Let's stick to Labuladong's example: [1,2,3,4,null,2,4,null,null,4]
% Re-drawing for LC example:
% 1 -> (2 -> (4, #), 3 -> (2 -> (4, #), 4 -> (#,#)))

\coordinate (root_lc) at (0,-4);
\node[treenode] (r_1) at (root_lc) {1};
\node[treenode] (r_2a) at ($(root_lc)+(-2,-1.5)$) {2};
\node[treenode] (r_4a) at ($(r_2a)+(-1,-1.5)$) {4};
\node[treenode] (r_3) at ($(root_lc)+(2,-1.5)$) {3};
\node[treenode] (r_2b) at ($(r_3)+(-1,-1.5)$) {2};
\node[treenode] (r_4b) at ($(r_2b)+(-0.7,-1.5)$) {4};
\node[treenode] (r_4c) at ($(r_3)+(1,-1.5)$) {4};

\draw (r_1) -- (r_2a); \draw (r_1) -- (r_3);
\draw (r_2a) -- (r_4a);
\draw (r_3) -- (r_2b); \draw (r_3) -- (r_4c);
\draw (r_2b) -- (r_4b);

\node[note] at (4, -6) {
    1. At node `r_4a` (leaf 4): Serialize "4,#,#". memo["4,#,#"]=1.
    2. At node `r_2a`: Serialize "2,4,#,#,#". memo["2,4,#,#,#"]=1.
    3. At node `r_4b` (leaf 4): Serialize "4,#,#". memo["4,#,#"]=2. Add `r_4b` to results.
    4. At node `r_2b`: Serialize "2,4,#,#,#". memo["2,4,#,#,#"]=2. Add `r_2b` to results.
    5. At node `r_4c` (leaf 4): Serialize "4,#,#". memo["4,#,#"]=3. (Already added for count 2).
};
\node at (0, -9) [text width=10cm, align=center] {Results: Roots of subtrees [4] and [2,4] (e.g., `r_4b` and `r_2b`).};

\end{tikzpicture}
```
The key is that the serialization string acts as a unique identifier for a subtree's structure and values. The hash map `memo` tracks how many times each unique structure has been seen.

## 总结 (Summary)
- Post-order traversal is crucial for problems involving the identification or aggregation of properties of entire subtrees because it processes a node only after its children's subtrees are fully processed.
- Serializing subtrees into a canonical string form allows them to be used as keys in a hash map to detect duplicates or count occurrences.
- This pattern combines "decomposition" (the recursive serialization relies on children's serializations) with "traversal" (visiting all nodes to perform this). The core logic for combining information and making decisions happens in the post-order position.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/06 - Tree Problems - Post-Order Solutions Part 1|Prev: Post-Order Solutions Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/08 - Tree Problems - Post-Order Solutions Part 3|Next: Post-Order Solutions Part 3]] |
