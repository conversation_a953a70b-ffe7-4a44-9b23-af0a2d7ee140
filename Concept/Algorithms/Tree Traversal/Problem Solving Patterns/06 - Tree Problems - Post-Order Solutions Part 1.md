---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, concept/recursion, pattern/tree_problem, course/labuladong]
aliases: [Post-Order Traversal Benefits, Tree Aggregation Post-Order, 二叉树后序遍历应用一]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's emphasis on leveraging the post-order traversal position for solving tree problems, particularly for basic aggregations and property calculations.
> Original source context: [[{source_file_path}|【练习】利用后序位置解题 I]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/05 - Tree Problems - Decomposition Thinking Part 2|Prev: Decomposition Thinking Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/07 - Tree Problems - Post-Order Solutions Part 2|Next: Post-Order Solutions Part 2]] |

# Tree Problems: Leveraging Post-Order Traversal Part 1 - Basic Aggregations & Properties

The post-order position in a Depth-First Search (DFS) traversal of a binary tree ([[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Recursive DFS]]) is uniquely powerful. Code in the post-order position executes *after* both the left and right subtrees have been fully processed. This means that when processing a node `curr` in its post-order slot, the results or properties of `curr.left` and `curr.right` subtrees are available (typically via the return values of the recursive calls).

## 核心概念 (Core Concept)
Post-order traversal naturally fits the "decomposition" or "divide and conquer" thinking mode ([[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]]). If a problem's solution for a tree/subtree can be derived from the solutions of its left and right subtrees, post-order is the place to combine these sub-solutions.

**Why Post-Order is Special:**
When `dfs(node)` is about to execute its post-order logic for `node`:
- `dfs(node.left)` has completed and returned the result for the left subtree.
- `dfs(node.right)` has completed and returned the result for the right subtree.
- Thus, at `node`, we have all necessary information from its children to compute the result for the subtree rooted at `node`.

## 通用模板 (General Template - Calculate Tree Height/Max Depth)
A classic example is calculating the maximum depth (or height) of a binary tree (LeetCode 104. Maximum Depth of Binary Tree).

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionMaxDepth:
    def maxDepth(self, root: TreeNode) -> int:
        # Define what the recursive function returns: the depth of the subtree rooted at 'node'.
        if not root:
            return 0 # Base case: depth of an empty tree is 0

        # Recursively get depths of left and right subtrees
        left_depth = self.maxDepth(root.left)
        right_depth = self.maxDepth(root.right)

        # === Post-order position ===
        # The depth of the current tree is 1 (for the current root node)
        # plus the maximum of the depths of its left and right subtrees.
        current_depth = 1 + max(left_depth, right_depth)
        
        return current_depth

# Example Usage:
# root = TreeNode(3, TreeNode(9), TreeNode(20, TreeNode(15), TreeNode(7)))
# solver = SolutionMaxDepth()
# print(solver.maxDepth(root)) # Expected: 3
```
### 模板解析 (Template Explanation)
1.  **Function Definition:** `maxDepth(node)` is defined to return the maximum depth of the subtree rooted at `node`.
2.  **Base Case:** If `node` is `None`, its depth is 0.
3.  **Recursive Calls:** `left_depth = maxDepth(node.left)` and `right_depth = maxDepth(node.right)` compute the depths of the children's subtrees.
4.  **Post-Order Logic:** After `left_depth` and `right_depth` are known, the depth of the current `node`'s subtree is `1 + max(left_depth, right_depth)`. This calculation happens *after* the recursive calls return, hence it's in the post-order position.

## 示例图示 (Diagram Example: Max Depth Calculation)
Tree:
```
    A
   / \
  B   C
     /
    D
```
`maxDepth(A)`:
1. Calls `maxDepth(B)` and `maxDepth(C)`.
2. `maxDepth(B)`:
   - Calls `maxDepth(B.left=None)` -> returns 0.
   - Calls `maxDepth(B.right=None)` -> returns 0.
   - Post-order for B: `1 + max(0,0) = 1`. Returns 1 (depth of subtree B).
3. `maxDepth(C)`:
   - Calls `maxDepth(D)`.
     - `maxDepth(D)` calls `maxDepth(None)` (for left) and `maxDepth(None)` (for right), both return 0.
     - Post-order for D: `1 + max(0,0) = 1`. Returns 1.
   - `left_depth_C = 1` (from D). `right_depth_C = maxDepth(C.right=None)` -> returns 0.
   - Post-order for C: `1 + max(1,0) = 2`. Returns 2 (depth of subtree C).
4. Post-order for A:
   - `left_depth_A = 1` (from B).
   - `right_depth_A = 2` (from C).
   - `1 + max(1,2) = 3`. Returns 3.

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=1cm, font=\sffamily\small},
    retval/.style={font=\sffamily\tiny, blue, fill=yellow!10, draw, rounded corners},
    level 1/.style={sibling distance=3cm},
    level 2/.style={sibling distance=2cm},
]
\node[treenode] (A) at (0,0) {A};
\node[treenode] (B) at (-1.5,-1.5) {B};
\node[treenode] (C) at (1.5,-1.5) {C};
\node[treenode] (D) at (0.75,-3) {D}; % Child of C

\draw (A) -- (B); \draw (A) -- (C);
\draw (C) -- (D);

% Return values conceptually
\node[retval, above right=0.1cm of B] {returns 1};
\node[retval, above right=0.1cm of D] {returns 1};
\node[retval, above right=0.1cm of C] {returns 2};
\node[retval, above right=0.1cm of A] {returns 3};

\node at (0,-4) [text width=6cm, align=center, draw, fill=gray!10, rounded corners]
    {Post-order: Each node calculates its depth based on children's returned depths.
    D: $1+max(0,0)=1$. B: $1+max(0,0)=1$. C: $1+max(depth(D)=1, depth(null)=0)=2$. A: $1+max(depth(B)=1, depth(C)=2)=3$.};
\end{tikzpicture}
```

## 总结 (Summary)
- The post-order traversal position is ideal for problems where a node's property or the solution for its subtree depends on the already computed properties/solutions of its children's subtrees.
- This pattern naturally aligns with the "decomposition" (divide and conquer) approach to solving tree problems.
- Common applications include calculating tree height/depth, tree size, checking for balance, finding lowest common ancestor (LCA, where decisions are based on results from left/right searches), and many dynamic programming problems on trees.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/05 - Tree Problems - Decomposition Thinking Part 2|Prev: Decomposition Thinking Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/07 - Tree Problems - Post-Order Solutions Part 2|Next: Post-Order Solutions Part 2]] |
