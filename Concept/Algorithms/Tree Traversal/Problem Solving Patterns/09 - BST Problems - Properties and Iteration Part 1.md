---
tags: [concept/algorithms, concept/tree_traversal, concept/bst, pattern/tree_problem, course/labuladong]
aliases: [BST Properties, In-order Traversal BST, 二叉搜索树特性应用一]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's approach to solving Binary Search Tree (BST) problems, focusing on leveraging BST properties, especially in-order traversal.
> Original source context: [[{source_file_path}|【练习】二叉搜索树经典例题 I]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/08 - Tree Problems - Post-Order Solutions Part 3|Prev: Post-Order Solutions Part 3]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/10 - BST Problems - Modification and Construction Part 2|Next: BST Problems Part 2]] |

# BST Problems: Leveraging Properties & In-Order Traversal (Part 1)

Binary Search Trees (BSTs) have unique properties that can be exploited to solve problems more efficiently than on general binary trees. The most fundamental property is that **an in-order traversal of a BST yields its elements in sorted order.** Many BST problems revolve around this or the definition: `node.left.val < node.val < node.right.val` for all nodes. See [[Interview/Concept/Data Structures/Tree/Binary Search Tree/00 - BST - Introduction and Operations|BST Introduction]].

## 核心概念 (Core Concept)
1.  **Sorted In-order Traversal:** This is the cornerstone. If a problem involves sorted data, order statistics (k-th smallest/largest), or ranges, in-order traversal is often key.
2.  **Local BST Property:** For any node, all values in its left subtree are smaller, and all values in its right subtree are larger. This allows for efficient searching and pruning of search space.

## 通用模板 (General Template - Kth Smallest Element in BST LC230)
LeetCode 230. Kth Smallest Element in a BST is a classic example.

**Approach 1: In-order traversal and store in a list**
Perform a full in-order traversal, store all elements in a list, then return the element at index `k-1`.
```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionKthSmallest_List:
    def kthSmallest(self, root: TreeNode, k: int) -> int:
        inorder_list = []
        self._inorder_traverse(root, inorder_list)
        if k > 0 and k <= len(inorder_list):
            return inorder_list[k-1]
        return -1 # Should not happen if k is valid

    def _inorder_traverse(self, node: TreeNode, lst: list):
        if not node:
            return
        self._inorder_traverse(node.left, lst)
        lst.append(node.val)
        self._inorder_traverse(node.right, lst)
```
- Time: $O(N)$ to traverse all nodes. Space: $O(N)$ for list and recursion stack.

**Approach 2: Optimized In-order traversal (Stop early)**
We can stop the in-order traversal once we've found the k-th element.
```python
class SolutionKthSmallest_Optimized:
    def __init__(self):
        self.k_val = 0
        self.count = 0
        self.result = -1

    def kthSmallest(self, root: TreeNode, k: int) -> int:
        self.k_val = k
        self.count = 0
        self.result = -1 # In case k is out of bounds or tree is empty
        self._inorder_stop_early(root)
        return self.result

    def _inorder_stop_early(self, node: TreeNode):
        if not node or self.count >= self.k_val: # Pruning if already found
            return

        # Traverse left
        self._inorder_stop_early(node.left)
        
        # Process current node (in-order position)
        if self.count < self.k_val: # Check again due to left recursion possibly finding it
            self.count += 1
            if self.count == self.k_val:
                self.result = node.val
                return # Found, can stop further traversal
        
        # Traverse right (only if not yet found)
        if self.count < self.k_val:
            self._inorder_stop_early(node.right)
```
- Time: $O(H+k)$ on average where $H$ is height, if balanced. Worst case $O(N)$. Space: $O(H)$ for recursion.

## 示例图示 (Diagram Example: Kth Smallest)
BST: `[3,1,4,null,2]`, `k=1`
In-order: `1, 2, 3, 4`. 1st smallest is `1`.

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\small},
    visitednode/.style={treenode, fill=yellow!30},
    kthnode/.style={treenode, fill=green!30, double},
    level 1/.style={sibling distance=2cm},
    level 2/.style={sibling distance=1.5cm},
    trav_note/.style={font=\sffamily\tiny, align=center, text width=3cm}
]

\node[treenode] (n3) at (0,0) {3};
\node[treenode] (n1) at (-1,-1.5) {1};
\node[treenode] (n4) at (1,-1.5) {4};
\node[treenode] (n2) at (-0.5,-3) {2}; % Child of 1

\draw (n3) -- (n1); \draw (n3) -- (n4);
\draw (n1) -- (n2); % 1's right child is 2

% Traversal Steps for k=1 (Optimized)
\node[trav_note] at (4, 0) {`_inorder(Node(3))`};
\node[trav_note, below=0.5cm of {(4,0)}] (call_1) {`_inorder(Node(1))` (left of 3)};
\node[trav_note, below=0.5cm of call_1] (call_1_left_null) {`_inorder(null)` (left of 1), returns};
\node[trav_note, below=0.5cm of call_1_left_null] (process_1) {Process Node(1) (in-order)\\`count`=0 $\rightarrow$ 1.\\`count==k` (1==1)? Yes!\\`result = 1`. Return.};

% Highlight nodes
\begin{scope}[on background layer]
    \node[visitednode, fit=(n1)] {}; % Node 1 is visited
    \node[kthnode, fit=(n1)] {}; % Node 1 is the k-th
\end{scope}

\node at (0,-4) [text width=6cm, align=center, draw, fill=gray!10, rounded corners]
    {For k=1: Traverse to Node(1). Process it. `count` becomes 1. Since `count == k`, `result = 1`. Traversal stops.};
\end{tikzpicture}
```

## 总结 (Summary)
- The sorted nature of BST in-order traversal is fundamental. Use it for problems involving order, rank, or ranges.
- The local BST property (`left < root < right`) allows efficient pruning in search-like traversals. If searching for a value `x`:
    - If `x < root.val`, go left.
    - If `x > root.val`, go right.
    - If `x == root.val`, found.
- Many "validation" problems (e.g., is this a valid BST?) also rely on checking these properties recursively or via in-order traversal to see if it produces a sorted sequence. See [[Interview/Practice/LeetCode/LC98 - Validate Binary Search Tree|LC98 - Validate BST]].

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/08 - Tree Problems - Post-Order Solutions Part 3|Prev: Post-Order Solutions Part 3]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/10 - BST Problems - Modification and Construction Part 2|Next: BST Problems Part 2]] |
