---
tags: [concept/algorithms, concept/tree_traversal, concept/bst, pattern/tree_problem, pattern/in_order_traversal, course/labuladong]
aliases: [BST Properties Traversal, In-order Traversal BST Applications, 二叉搜索树特性应用一, Kth Smallest BST, Convert BST to Greater Sum Tree]
---
> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷二叉树算法/二叉搜索树心法（特性篇）.md]].
> This note focuses on leveraging BST properties, especially the sorted nature of in-order traversal, and its variant for specific problems.

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/08 - Tree Problems - Post-Order Solutions Part 3|Prev: Post-Order Solutions Part 3]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/10 - BST Problems - Modification and Construction Part 2|Next: BST Problems Part 2]] |

# BST Problems: Leveraging Properties & In-Order Traversal (Part 1)

Binary Search Trees (BSTs) have unique properties that can be exploited to solve problems more efficiently than on general binary trees. The most fundamental property is that **an in-order traversal of a BST yields its elements in sorted order.** Many BST problems revolve around this or the definition: `node.left.val < node.val < node.right.val` for all nodes. For a general introduction to BSTs, see [[Interview/Concept/Data Structures/Tree/Binary Search Tree/00 - BST - Introduction and Operations|BST Introduction and Operations]].

## 核心概念 (Core Concept)
1.  **Sorted In-order Traversal:** This is the cornerstone. If a problem involves sorted data, order statistics (k-th smallest/largest), or ranges, in-order traversal is often key.
2.  **Local BST Property:** For any node, all values in its left subtree are smaller, and all values in its right subtree are larger. This allows for efficient searching and pruning of search space.

## Example 1: Kth Smallest Element in a BST (LC230)
[[Interview/Practice/LeetCode/LC230 - Kth Smallest Element in a BST|LC230 - Kth Smallest Element in a BST]]
Since in-order traversal visits nodes in ascending order, the k-th element visited during an in-order traversal is the k-th smallest element.

**Optimized In-order Traversal:**
We can perform an in-order traversal and stop once we've encountered `k` elements.
```python
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None): self.val = val; self.left = left; self.right = right

class SolutionKthSmallest:
    def __init__(self):
        self.k_count = 0
        self.result = -1

    def kthSmallest(self, root: TreeNode, k: int) -> int:
        self.k_target = k # Store k to compare with self.k_count
        self.k_count = 0
        self.result = -1 
        self._inorder_traverse(root)
        return self.result

    def _inorder_traverse(self, node: TreeNode):
        if not node or self.k_count >= self.k_target: # Stop if already found or node is null
            return

        # Traverse left subtree
        self._inorder_traverse(node.left)
        
        # Process current node (in-order position)
        if self.k_count < self.k_target: # Check again if left subtree already found it
            self.k_count += 1
            if self.k_count == self.k_target:
                self.result = node.val
                return # Found, can prune further traversal
        
        # Traverse right subtree (only if not yet found)
        if self.k_count < self.k_target:
            self._inorder_traverse(node.right)
```
Labuladong's visualizer `div_kth-smallest-element-in-a-bst` helps understand this. The key is to increment a rank/counter during the in-order visit.

> [!TIP] Follow-up for Kth Smallest (Frequent Operations)
> If BST is frequently modified and k-th smallest is needed often, augment `TreeNode` to store `size` (number of nodes in its subtree). Then, `select(k)` can be done in $O(H)$ time, similar to how selection works in order statistic trees. If `left_subtree.size == k-1`, current node is k-th. If `left_subtree.size > k-1`, go left. Else, go right for `k - left_subtree.size - 1`.

## Example 2: Convert BST to Greater Tree (LC538 / LC1038)
[[Interview/Practice/LeetCode/LC538 - Convert BST to Greater Tree|LC538 - Convert BST to Greater Tree]] / [[Interview/Practice/LeetCode/LC1038 - Binary Search Tree to Greater Sum Tree|LC1038 - Binary Search Tree to Greater Sum Tree]]
Convert a BST to a "Greater Sum Tree" where each node's new value is the sum of all original values greater than or equal to its original value.

**Insight:** This requires summing up values in decreasing order. A standard in-order traversal gives increasing order. A *reverse in-order traversal* (Right, Root, Left) visits nodes in decreasing order.

**Reverse In-order Traversal Approach:**
Maintain a running sum of values encountered (which will be values greater than or equal to the current node as we traverse Right-Root-Left).
```python
class SolutionConvertBST:
    def __init__(self):
        self.current_sum = 0 # Accumulates sum of nodes greater than current

    def convertBST(self, root: TreeNode) -> TreeNode:
        self.current_sum = 0 # Reset for multiple calls if instance is reused
        self._reverse_inorder_traverse(root)
        return root

    def _reverse_inorder_traverse(self, node: TreeNode):
        if not node:
            return

        # 1. Traverse right subtree (all nodes greater than current)
        self._reverse_inorder_traverse(node.right)
        
        # 2. Process current node (in "reverse in-order" position)
        # Add current node's original value to the running sum
        self.current_sum += node.val
        # Update current node's value to this new sum
        node.val = self.current_sum
        
        # 3. Traverse left subtree (all nodes smaller than current)
        # The self.current_sum now correctly includes current_node and all nodes greater than it.
        self._reverse_inorder_traverse(node.left)
```
Labuladong's visualizer `div_convert-bst-to-greater-tree` demonstrates this. The sum accumulates as we traverse from largest to smallest values.

## 总结 (Summary)
- The sorted nature of BST in-order traversal is a powerful property. For problems requiring sorted order or rank (like Kth smallest), in-order traversal is a natural fit.
- By reversing the in-order traversal (Right-Root-Left), we can process nodes in decreasing order. This is useful for problems like converting to a Greater Sum Tree, where aggregation involves larger elements.
- These "traversal thinking" approaches often use an instance variable (or pass state via parameters) to accumulate results or maintain context during the traversal.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/08 - Tree Problems - Post-Order Solutions Part 3|Prev: Post-Order Solutions Part 3]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/10 - BST Problems - Modification and Construction Part 2|Next: BST Problems Part 2]] |
