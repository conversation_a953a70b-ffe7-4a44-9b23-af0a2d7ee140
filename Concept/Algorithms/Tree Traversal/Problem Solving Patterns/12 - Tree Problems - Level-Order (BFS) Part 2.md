---
tags: [concept/algorithms, concept/tree_traversal, concept/bfs, pattern/tree_problem, course/labuladong]
aliases: [Advanced BFS Tree, Tree Width, Connect Nodes at Same Level, 二叉树层序遍历应用二]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's discussion on advanced applications of level-order traversal (BFS) for binary tree problems.
> Original source context: [[{source_file_path}|【练习】运用层序遍历解题 II]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/11 - Tree Problems - Level-Order (BFS) Part 1|Prev: Level-Order (BFS) Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/13 - Tree Problems - Combining Traversal and Decomposition|Next: Combining Modes]] |

# Tree Problems: Using Level-Order Traversal (BFS) Part 2 - Width & Connectivity

This part explores more specialized applications of [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Level-Order Traversal (BFS)]] for binary trees, often focusing on properties related to the "width" of the tree at different levels or connecting nodes within the same level.

## 核心概念 (Core Concept)
The standard BFS template that processes nodes level by level (Pattern 2 from [[Interview/Concept/Algorithms/Tree Traversal/01 - Binary Tree Level-Order Traversal (BFS)|Binary Tree Level-Order Traversal (BFS)]]) is the foundation. The specific logic within the inner loop (processing nodes of a single level) is adapted to the problem.

## 通用模板 (General Template - Example: Max Width of Binary Tree LC662)
LeetCode 662. Maximum Width of Binary Tree. The width of one level is defined as the length between the end-nodes (the leftmost and rightmost non-null nodes in the level, where the null nodes between the end-nodes are also counted into the length calculation).

To solve this, we need to assign an "index" or "position" to each node as if it were in a complete binary tree.
- If a node is at `pos`, its left child is at `2*pos` and right child at `2*pos + 1` (or `2*pos-1` and `2*pos` if 1-indexed).
- During BFS, store `(node, position)` tuples in the queue.
- For each level, the width is `max_pos_in_level - min_pos_in_level + 1`.

```python
from collections import deque

class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionMaxWidth:
    def widthOfBinaryTree(self, root: TreeNode | None) -> int:
        if not root:
            return 0

        max_width = 0
        # Queue stores tuples: (node, position_index)
        # Start root at position 0 (or 1, be consistent)
        queue = deque([(root, 0)]) 

        while queue:
            level_size = len(queue)
            level_start_pos = -1 # Position of the first node in this level
            level_end_pos = -1   # Position of the last node in this level
            
            for i in range(level_size):
                node, pos = queue.popleft()

                if i == 0: # First node of the level
                    level_start_pos = pos
                if i == level_size - 1: # Last node of the level
                    level_end_pos = pos
                
                # Enqueue children with their calculated positions
                # To prevent large position numbers, we can re-index relative to level_start_pos
                # For simplicity here, using absolute positions.
                # Relative indexing: child_pos = (pos - level_start_pos) * 2 for left.
                if node.left:
                    queue.append((node.left, 2 * pos)) 
                if node.right:
                    queue.append((node.right, 2 * pos + 1))
            
            current_level_width = level_end_pos - level_start_pos + 1
            max_width = max(max_width, current_level_width)
            
        return max_width

# Example Usage:
# root = TreeNode(1, TreeNode(3, TreeNode(5), TreeNode(3)), TreeNode(2, None, TreeNode(9)))
# solver = SolutionMaxWidth()
# print(solver.widthOfBinaryTree(root)) 
# Level 0: [1] (pos 0). Width 1.
# Level 1: [3,2] (pos 0,1 if re-indexed, or 0,1 from root=0). Width (1-0)+1=2.
# Level 2: [5,3,null,9] (pos 0,1,_,3 if re-indexed, or 0,1,_,3 from root=0). Width (3-0)+1=4.
# Max width should be 4.
# Careful with indexing: If root is 0, left is 2*0=0, right is 2*0+1=1. This is for segment tree like indexing.
# For heap-like indexing if root is 1: left=2*idx, right=2*idx+1.
# If root is 0: left child at 2*idx + 1, right child at 2*idx + 2.
# Let's use the root=0, left=2p+1, right=2p+2 common for BFS level indexing
# Corrected indexing for LC662:
class SolutionMaxWidth_LC662_Corrected:
    def widthOfBinaryTree(self, root: TreeNode | None) -> int:
        if not root: return 0
        max_w = 0
        queue = deque([(root, 0)]) # (node, position_index)

        while queue:
            level_length = len(queue)
            _, level_head_index = queue[0] # Position of the first node in this level
            
            for i in range(level_length):
                curr_node, curr_idx = queue.popleft()
                
                # Calculate width if this is the last node of the level
                if i == level_length - 1:
                    max_w = max(max_w, curr_idx - level_head_index + 1)
                
                if curr_node.left:
                    queue.append((curr_node.left, 2 * (curr_idx - level_head_index) + 1)) # Relative indexing
                if curr_node.right:
                    queue.append((curr_node.right, 2 * (curr_idx - level_head_index) + 2))
            # Corrected approach: Use absolute indexing, subtract start index of level at end of level
            # This avoids issues with large index values when re-indexing.

# Simpler LC662 approach with absolute indices normalized at each level.
# Each node stores (node, its_index_in_level). The actual value of index can be large.
# At each level, width is (last_node_index - first_node_index + 1).
class SolutionMaxWidth_LC662_Normalized:
    def widthOfBinaryTree(self, root: TreeNode | None) -> int:
        if not root: return 0
        max_width = 0
        queue = deque([(root, 0)]) # (node, index) - root is at index 0

        while queue:
            level_size = len(queue)
            # The first element's index in the queue for this level becomes the "offset"
            level_start_index = queue[0][1] 
            
            first_node_idx_in_level = 0
            last_node_idx_in_level = 0

            for i in range(level_size):
                node, original_idx = queue.popleft()
                
                # Normalized index for current level processing
                current_normalized_idx = original_idx - level_start_index

                if i == 0:
                    first_node_idx_in_level = current_normalized_idx # Will be 0
                if i == level_size - 1:
                    last_node_idx_in_level = current_normalized_idx
                
                if node.left:
                    # Children's original_idx calculated from parent's original_idx
                    queue.append((node.left, 2 * original_idx + 1))
                if node.right:
                    queue.append((node.right, 2 * original_idx + 2))
            
            max_width = max(max_width, last_node_idx_in_level - first_node_idx_in_level + 1)
            
        return max_width
```

## 示例图示 (Diagram Example: Populating Next Right Pointers - LC116/117)
LeetCode 116/117. Populating Next Right Pointers in Each Node.
This problem requires connecting nodes at the same level using a `next` pointer. BFS is ideal.

```python
class Node: # Definition for LC116/117
    def __init__(self, val: int = 0, left: 'Node' = None, right: 'Node' = None, next: 'Node' = None):
        self.val = val
        self.left = left
        self.right = right
        self.next = next

class SolutionConnectNodes:
    def connect(self, root: 'Node') -> 'Node':
        if not root:
            return None
        
        queue = deque([root])
        while queue:
            level_size = len(queue)
            prev_node_in_level = None
            for i in range(level_size):
                node = queue.popleft()
                
                # Connect to previous node in the same level
                if prev_node_in_level:
                    prev_node_in_level.next = node
                
                prev_node_in_level = node # Update for the next iteration

                if node.left:
                    queue.append(node.left)
                if node.right:
                    queue.append(node.right)
            # The last node in the level will have its 'next' pointer as None (default)
        return root
```
```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\small},
    nextptr/.style={->, thick, blue, dashed, bend left=10},
    level_conn/.style={font=\sffamily\tiny, align=center, text width=5cm},
    level 1/.style={sibling distance=3cm},
    level 2/.style={sibling distance=1.5cm}
]
% Tree
\node[treenode] (n1) at (0,0) {1};
\node[treenode] (n2) at (-1.5,-1.5) {2};
\node[treenode] (n3) at (1.5,-1.5) {3};
\node[treenode] (n4) at (-2.25,-3) {4};
\node[treenode] (n5) at (-0.75,-3) {5};
\node[treenode] (n6) at (0.75,-3) {6};
\node[treenode] (n7) at (2.25,-3) {7};

\draw (n1) -- (n2); \draw (n1) -- (n3);
\draw (n2) -- (n4); \draw (n2) -- (n5);
\draw (n3) -- (n6); \draw (n3) -- (n7);

% Next pointers
\draw[nextptr] (n2.east) to node[midway, below, font=\tiny] {next} (n3.west);
\draw[nextptr] (n4.east) to node[midway, below, font=\tiny] {next} (n5.west);
\draw[nextptr] (n5.east) to node[midway, below, font=\tiny] {next} (n6.west);
\draw[nextptr] (n6.east) to node[midway, below, font=\tiny] {next} (n7.west);

% Explanation
\node[level_conn, rectangle, draw, fill=yellow!10] at (0,-4.5) {
    Level 0: Node 1. `prev_node=null`. After loop, `prev_node=1`. `1.next=null`.
    Level 1: Nodes 2, 3.
    - Process 2: `prev_node=null`. `prev_node=2`.
    - Process 3: `prev_node=2`. `2.next=3`. `prev_node=3`. `3.next=null`.
    Level 2: Nodes 4, 5, 6, 7. (Similar logic)
};
\end{tikzpicture}
```

## 总结 (Summary)
- Advanced BFS applications on trees often involve processing nodes at each level with more specific logic than just collecting values.
- For problems like "Max Width," assigning positional indices to nodes (as if in a complete tree) is a common strategy within the BFS framework.
- For problems like "Populating Next Right Pointers," BFS allows easy iteration through nodes of the same level to establish connections.
- The core BFS template (process level by level using `queue` and `level_size`) remains highly adaptable.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/11 - Tree Problems - Level-Order (BFS) Part 1|Prev: Level-Order (BFS) Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/13 - Tree Problems - Combining Traversal and Decomposition|Next: Combining Modes]] |
