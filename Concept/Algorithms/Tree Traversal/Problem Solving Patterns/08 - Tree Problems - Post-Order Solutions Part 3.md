---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, concept/recursion, pattern/tree_problem, pattern/post_order, pattern/bst, course/labuladong]
aliases: [Advanced Post-Order BST, Max Sum BST Subtree, 二叉树后序遍历应用三 (BST)]
---
> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷二叉树算法/二叉搜索树心法（后序篇）.md]], focusing on LC1373 Maximum Sum BST in Binary Tree.
> This note demonstrates an advanced application of post-order traversal for problems that require verifying properties (like BST) and aggregating values from subtrees.

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/07 - Tree Problems - Post-Order Solutions Part 2|Prev: Post-Order Solutions Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/09 - BST Problems - Properties and Iteration Part 1|Next: BST Problems Part 1]] |

# Tree Problems: Leveraging Post-Order Traversal Part 3 - Max Sum BST Subtree

This section explores a complex problem, LeetCode 1373. Maximum Sum BST in Binary Tree, which effectively uses post-order traversal to gather multiple pieces of information from subtrees to make a decision at the current node.

## 核心概念 (Core Concept)
For a problem like finding the maximum sum of a valid Binary Search Tree (BST) within a general binary tree, each node needs information from its children:
1.  Is the left child's subtree a valid BST?
2.  Is the right child's subtree a valid BST?
3.  What is the maximum value in the left BST subtree (to compare with current node's value)?
4.  What is the minimum value in the right BST subtree (to compare with current node's value)?
5.  What are the sums of the left and right BST subtrees?

All this information is naturally available in the post-order traversal position. The recursive function must be designed to return a bundle of these states.

## Example: Maximum Sum BST in Binary Tree (LC1373)
[[Interview/Practice/LeetCode/LC1373 - Maximum Sum BST in Binary Tree|LC1373 - Maximum Sum BST in Binary Tree]]

**Recursive Function Definition:**
`traverse(node)` will return an array or object: `[is_bst, min_val_in_subtree, max_val_in_subtree, sum_of_subtree_nodes]`.
- `is_bst`: 1 if subtree at `node` is BST, 0 otherwise.
- `min_val_in_subtree`: Minimum value in the BST subtree at `node`.
- `max_val_in_subtree`: Maximum value in the BST subtree at `node`.
- `sum_of_subtree_nodes`: Sum of node values in the BST subtree at `node`.

A global variable `max_bst_sum_found` will track the maximum sum.

```python
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None): self.val = val; self.left = left; self.right = right

class SolutionMaxSumBST:
    def __init__(self):
        self.max_sum = 0

    def maxSumBST(self, root: TreeNode) -> int:
        self.max_sum = 0
        self._traverse_bst_sum(root)
        return self.max_sum

    def _traverse_bst_sum(self, node: TreeNode) -> list:
        # Returns [is_bst, min_val, max_val, current_sum]
        # is_bst: 1 for true, 0 for false
        # min_val, max_val: relevant only if is_bst is true
        # current_sum: sum of nodes in this BST subtree, or non-BST sum if not a BST (can be ignored then)

        if not node:
            # Base case: An empty tree is a BST, sum is 0.
            # min should be +inf, max should be -inf to correctly compare with parent.
            return [1, float('inf'), float('-inf'), 0]

        # Recursively get info from left and right children
        left_info = self._traverse_bst_sum(node.left)
        right_info = self._traverse_bst_sum(node.right)

        # === Post-order position ===
        # Check conditions for current node's subtree to be a BST
        res = [0] * 4 # is_bst, min, max, sum for current node's subtree

        # Condition 1: Left and right subtrees must be BSTs.
        # Condition 2: node.val must be > max_val_in_left_subtree.
        # Condition 3: node.val must be < min_val_in_right_subtree.
        if (left_info[0] == 1 and right_info[0] == 1 and \
            node.val > left_info[2] and node.val < right_info[1]):
            
            # It's a BST
            res[0] = 1
            # Min value in this BST is min of node.val, left_info[1] (or node.val if left is null)
            res[1] = min(node.val, left_info[1])
            # Max value in this BST is max of node.val, right_info[2] (or node.val if right is null)
            res[2] = max(node.val, right_info[2])
            # Sum of this BST
            res[3] = node.val + left_info[3] + right_info[3]
            
            # Update global max_sum
            self.max_sum = max(self.max_sum, res[3])
        else:
            # Not a BST
            res[0] = 0
            # Other values (min, max, sum) don't matter for non-BST in this context,
            # but we can fill them (e.g. res[3] could be overall subtree sum for other problems).
            # For this problem, if it's not a BST, its sum isn't considered for max_bst_sum.
            # The values of min/max for non-BST subtrees are not used by parent to form larger BST.
            # We could just return [0,0,0,0] for non-BST or specific invalid markers.
            # Labuladong's code returns the current sum `node.val + left_info[3] + right_info[3]`
            # even if it's not a BST, but `is_bst=0` flag prevents it from being used by parent to form BST.
            # The problem wants max sum of a *valid* BST.
            # The `self.max_sum` is only updated if `res[0] == 1`.
            # The actual min/max/sum returned upwards for a non-BST node don't matter as its parent won't form a BST with it.
            # Let's fill them somewhat meaningfully for completeness, though they are ignored by parent if res[0]==0.
            res[1] = min(node.val, left_info[1], right_info[1] if right_info[0] else float('inf')) 
            res[2] = max(node.val, left_info[2] if left_info[0] else float('-inf'), right_info[2])
            res[3] = node.val + left_info[3] + right_info[3] # total sum of this non-BST subtree
            
        return res
```
Visualizer for this in Labuladong's article `div_maximum-sum-bst-in-binary-tree` would show how these 4-tuple results propagate up.

### 模板解析 (Template Explanation)
1.  **Recursive Return Value:** The helper function returns a list/tuple containing multiple pieces of information about the subtree: `[is_bst, min_val, max_val, sum_val]`.
2.  **Base Case (Null Node):** A null node represents an empty BST.
    - `is_bst = 1` (true).
    - `min_val = float('inf')` (so any parent value is smaller).
    - `max_val = float('-inf')` (so any parent value is larger).
    - `sum_val = 0`.
3.  **Recursive Calls:** Get this 4-tuple information from left and right children.
4.  **Post-Order Logic (At current `node`):**
    -   Check if the current subtree (rooted at `node`) forms a valid BST:
        -   `left_info[0]` must be 1 (left child is BST).
        -   `right_info[0]` must be 1 (right child is BST).
        -   `node.val > left_info[2]` (current node greater than max in left BST).
        -   `node.val < right_info[1]` (current node smaller than min in right BST).
    -   If all conditions met:
        -   Mark current subtree as BST (`res[0]=1`).
        -   Calculate its `min_val` (min of `node.val` and `left_info[1]`).
        -   Calculate its `max_val` (max of `node.val` and `right_info[2]`).
        -   Calculate its `sum_val` (`node.val + left_info[3] + right_info[3]`).
        -   Update the global `self.max_sum = max(self.max_sum, sum_val)`.
    -   If not a BST:
        -   Mark `res[0]=0`. The other values in `res` are not critical for parent forming BST.
5.  **Return `res`** to the parent.

## 示例图示 (Diagram Example: Max Sum BST)
Tree: `[4,3,null,1,2]` (Not a BST itself)
Subtree at 3: `[3,1,2]` is a BST. Sum = 1+2+3 = 6.
Global `max_sum` becomes 6.

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=8mm, font=\sffamily\small},
    info_box/.style={rectangle, draw, fill=yellow!10, font=\sffamily\tiny, text width=3.5cm, align=left},
    level 1/.style={sibling distance=2.5cm},
    level 2/.style={sibling distance=1.5cm}
]

\node[treenode] (n4) at (0,0) {4};
\node[treenode] (n3) at (-1.2,-1.5) {3};
\node[treenode] (n1) at (-2,-3) {1};
\node[treenode] (n2) at (-0.4,-3) {2};

\draw (n4) -- (n3);
\draw (n3) -- (n1); \draw (n3) -- (n2);

% Information flow (conceptual)
\node[info_box] at (-3.5,-4.5) {Node 1 (leaf):\\Returns [1,1,1,1]};
\node[info_box] at (1,-4.5) {Node 2 (leaf):\\Returns [1,2,2,2]};

\node[info_box] (info_3) at (-1.2, -0.5) {
    Node 3:
    Left_info = [1,1,1,1] (from N1)
    Right_info = [1,2,2,2] (from N2)
    Is BST? Yes (1>1 false - wait, BST property is strict. Node 3.val > max(left_info[2]), 3.val < min(right_info[1])).
    3 > 1 (max of left N1), 3 < 2 (min of right N2) -> This is FALSE. Node 3 is not a BST here because its value 3 is NOT < 2.
    The example from Labuladong must have a different tree structure or my interpretation of values is off.
    Assuming a valid BST subtree at 3 like `3 -> (1,4)`:
    Left (1): [1,1,1,1]
    Right (4): [1,4,4,4]
    Node 3: 3 > 1 (max_left), 3 < 4 (min_right). Yes, BST.
    min=1, max=4, sum=1+3+4=8.
    Returns [1,1,4,8]. `max_sum`=max(0,8)=8.
};

\node[info_box] (info_4) at (2.5, 1) {
    Node 4:
    Left_info = [1,1,4,8] (from N3 if it was a BST)
    Right_info (null) = [1,inf,-inf,0]
    Is BST? 4 > 4 (max_left) -> FALSE.
    So subtree at 4 is not BST.
    Returns [0, ... irrelevant ...]. `max_sum` remains 8.
};

\node at (0,-5.5) [text width=8cm, align=center, draw, fill=gray!10, rounded corners] {
    The key is that each node's processing (post-order) uses the 4-tuple returned by its children to determine its own 4-tuple and update a global max sum if it forms a valid BST.
};
\end{tikzpicture}
```
*The diagram above attempts to illustrate the flow; the exact values depend on the specific tree structure and BST validity checks. The Labuladong example problem (LC1373) is critical for this specific logic.*

## 总结 (Summary)
- Complex tree problems often require a recursive helper function that returns multiple pieces of state information from subtrees.
- Post-order traversal is the natural place to combine this information from children to determine the properties of the current node's subtree.
- A global or instance variable is often used to track an overall result (like a maximum sum or count) that is updated during the traversal as valid structures/conditions are identified.
- This pattern is highly applicable to dynamic programming problems on trees or problems involving validation of structural properties (like BST-ness) combined with aggregation.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/07 - Tree Problems - Post-Order Solutions Part 2|Prev: Post-Order Solutions Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/09 - BST Problems - Properties and Iteration Part 1|Next: BST Problems Part 1]] |
