---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, concept/recursion, pattern/tree_problem, course/labuladong]
aliases: [Complex Post-Order, Tree DP with Parent Info, 二叉树后序遍历应用三]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's framework, illustrating advanced uses of post-order traversal where information might flow both up (from children) and down (from parent via parameters).
> Original source context: [[{source_file_path}|【练习】利用后序位置解题 III]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/07 - Tree Problems - Post-Order Solutions Part 2|Prev: Post-Order Solutions Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/09 - BST Problems - Properties and Iteration Part 1|Next: BST Problems Part 1]] |

# Tree Problems: Leveraging Post-Order Traversal Part 3 - Advanced State & Parent Interaction

This section explores advanced scenarios where post-order traversal is used, possibly in combination with information passed down from parent nodes (via function parameters) or by returning more complex state upwards. The core idea remains that decisions or computations at a node leverage fully processed information from its children.

## 核心概念 (Core Concept)
Labuladong highlights that some difficult tree problems require a blend:
1.  **Information from children (via return values):** This is the standard post-order strength.
2.  **Information from parent/ancestors (via parameters):** Sometimes, the decision at a node also depends on context from above.

When both are needed, the recursive function signature might become `dfs(node, parent_info...)` and it would still primarily use post-order logic to combine `parent_info` with `dfs(node.left, ...)` and `dfs(node.right, ...)`.

## 通用模板 (General Template - Example: Max Path Sum LC124)

LeetCode 124. Binary Tree Maximum Path Sum ([[Interview/Practice/LeetCode/LC124 - Binary Tree Maximum Path Sum|LC124]]) is a good example of a problem requiring sophisticated post-order logic.
A "path" can start and end at any node.

**Recursive Function Definition:** `max_gain(node)`
This helper function computes the maximum path sum starting at `node` and going downwards (into one of its subtrees, or just the node itself). It returns this "gain" to its parent.
Crucially, it also updates a global/instance variable `max_overall_sum` if a path through `node` (potentially using both left and right children, forming an "arch") is greater than the current overall maximum.

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionMaxPathSum:
    def __init__(self):
        self.max_so_far = float('-inf')

    def maxPathSum(self, root: TreeNode) -> int:
        self.max_so_far = float('-inf') # Reset for multiple calls if object is reused
        self._max_gain_from_subtree(root)
        return self.max_so_far

    def _max_gain_from_subtree(self, node: TreeNode) -> int:
        if not node:
            return 0 # Gain from a null subtree is 0

        # Recursively get max gain from left and right children
        # Important: if a child's gain is negative, we don't include it in a path *going upwards*.
        gain_left = max(self._max_gain_from_subtree(node.left), 0)
        gain_right = max(self._max_gain_from_subtree(node.right), 0)

        # === Post-order position ===
        # The price to start a new path (an "arch") with current 'node' as the highest point
        price_new_path_through_node = node.val + gain_left + gain_right
        
        # Update global maximum sum found so far
        self.max_so_far = max(self.max_so_far, price_new_path_through_node)
        
        # For recursion upwards: return the max gain from this node if it's part of a path
        # extending from its parent. A path can only go down one branch (left or right).
        return node.val + max(gain_left, gain_right)

# Example Usage:
# root = TreeNode(-10, TreeNode(9), TreeNode(20, TreeNode(15), TreeNode(7)))
# solver = SolutionMaxPathSum()
# print(solver.maxPathSum(root)) # Expected: 42 (path 15-20-7)
```
### 模板解析 (Template Explanation)
1.  **Global Maximum:** `self.max_so_far` stores the maximum path sum found anywhere in the tree.
2.  **Recursive Helper `_max_gain_from_subtree(node)`:**
    *   **Definition:** Returns the maximum sum of a path that *starts at `node` and goes downwards* into at most one of its subtrees. This is the "gain" this node can offer to its parent if the parent extends a path through it.
    *   **Base Case:** If `node` is `None`, gain is 0.
    *   **Recursive Calls:** Get `gain_left` and `gain_right`. If a child's gain is negative, we don't want to include it in a path extending upwards, so we take `max(child_gain, 0)`.
    *   **Post-Order Logic:**
        *   Calculate `price_new_path_through_node = node.val + gain_left + gain_right`. This is the sum of a path that forms an "arch" with `node` as its peak (it uses `node`, and potentially its left downward path and right downward path). This path *cannot* extend further upwards to `node`'s parent.
        *   Update `self.max_so_far = max(self.max_so_far, price_new_path_through_node)`.
        *   **Return Value:** For the parent, `node` can only contribute itself plus the *better* of its left or right downward paths. So, return `node.val + max(gain_left, gain_right)`.

## 示例图示 (Diagram Example: Max Path Sum)
Tree: `[-10, 9, 20, null, null, 15, 7]`
```
    -10
    / \
   9  20
     /  \
    15   7
```

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=8mm, font=\sffamily\small},
    callinfo/.style={font=\sffamily\tiny, align=center, text width=2.5cm, fill=yellow!10, draw, rounded corners},
    maxval/.style={font=\sffamily\bfseries\small, red},
    level 1/.style={sibling distance=3cm},
    level 2/.style={sibling distance=2cm}
]
\node[treenode] (n_10) at (0,0) {-10};
\node[treenode] (n9) at (-1.5,-1.5) {9};
\node[treenode] (n20) at (1.5,-1.5) {20};
\node[treenode] (n15) at (0.75,-3) {15};
\node[treenode] (n7) at (2.25,-3) {7};

\draw (n_10) -- (n9); \draw (n_10) -- (n20);
\draw (n20) -- (n15); \draw (n20) -- (n7);

% Annotations (conceptual trace)
\node[callinfo] at (-2.5, -3) {`_max_gain(9)`\\Returns: $9+max(0,0)=9$\\Arch sum: $9+0+0=9$\\`max_so_far` = max(-inf, 9) = 9};

\node[callinfo] at (-0.25, -4.2) {`_max_gain(15)`\\Returns: $15+max(0,0)=15$\\Arch sum: $15+0+0=15$\\`max_so_far` = max(9, 15) = 15};

\node[callinfo] at (3.25, -4.2) {`_max_gain(7)`\\Returns: $7+max(0,0)=7$\\Arch sum: $7+0+0=7$\\`max_so_far` = max(15, 7) = 15};

\node[callinfo] at (1.5, -0.75) {
    `_max_gain(20)`:
    `gain_left` (from 15) = 15
    `gain_right` (from 7) = 7
    Arch sum: $20+15+7 = 42$
    `max_so_far` = max(15, 42) = \maxval{42}
    Returns: $20+max(15,7) = 20+15 = 35$
};

\node[callinfo] at (0, 1.25) {
    `_max_gain(-10)`:
    `gain_left` (from 9) = 9
    `gain_right` (from 20) = 35
    Arch sum: $-10+9+35 = 34$
    `max_so_far` = max(42, 34) = \maxval{42}
    Returns: $-10+max(9,35) = -10+35 = 25$
};
\node at (0, -5.5) {Final `max_so_far` is \maxval{42}};
\end{tikzpicture}
```
This diagram traces how `_max_gain_from_subtree` returns values upwards while `max_so_far` captures the best "arch" path found. The post-order nature is critical: a node processes after its children have reported their maximum downward gains.

## 总结 (Summary)
- Complex tree problems solvable with post-order traversal often involve recursive helper functions that return rich state information about subtrees.
- Simultaneously, a global or instance variable might be updated to track an overall optimum that can be formed by combining information from left and right children at a common ancestor (forming an "arch" or a path that doesn't necessarily extend upwards).
- This pattern is common in "maximum path sum" type problems or other dynamic programming on trees where the optimal solution for a subtree contributes to, but is not always identical to, the part of it that propagates to the parent.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/07 - Tree Problems - Post-Order Solutions Part 2|Prev: Post-Order Solutions Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/09 - BST Problems - Properties and Iteration Part 1|Next: BST Problems Part 1]] |
