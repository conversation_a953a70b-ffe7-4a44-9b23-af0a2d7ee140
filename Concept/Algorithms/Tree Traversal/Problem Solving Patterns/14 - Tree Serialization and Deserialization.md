---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, concept/bfs, pattern/tree_problem, pattern/serialization, course/labuladong]
aliases: [Binary Tree Serialization, Binary Tree Deserialization, 二叉树序列化与反序列化]
---
> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷二叉树算法/二叉树心法（序列化篇）.md]].
> This note covers techniques for serializing a binary tree into a string and deserializing it back into a tree structure, focusing on LC297.

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/13 - Tree Problems - Combining Traversal and Decomposition|Prev: Combining Modes]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | (End of this series for now) |

# Tree Problems: Serialization and Deserialization (LC297)

Serializing a binary tree means converting it into a string representation. Deserialization is the reverse process: reconstructing the original tree from its string representation. This is crucial for storing trees or transmitting them. LeetCode 297. Serialize and Deserialize Binary Tree ([[Interview/Practice/LeetCode/LC297 - Serialize and Deserialize Binary Tree|LC297]]) is the classic problem.

## 核心概念 (Core Concept)
The key is to choose a traversal method that, when augmented with null pointer information, can uniquely represent the tree's structure and values.
- **Pre-order and Post-order traversals (with nulls)** can uniquely define a binary tree.
- **In-order traversal (even with nulls)** cannot uniquely define a binary tree (e.g., `#,1,#,1,#` can be ambiguous).
- **Level-order traversal (with nulls)** can also uniquely define a binary tree.

Labuladong's article explores pre-order, post-order, and level-order methods.

## Method 1: Pre-order Traversal for Serialization/Deserialization

### Serialization (Pre-order)
Traverse the tree in pre-order. Append node values to a string builder. Use a special marker (e.g., `#`) for null nodes and a separator (e.g., `,`).

```python
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None): self.val = val; self.left = left; self.right = right

class CodecPreOrder:
    SEP = ","
    NULL_MARKER = "#"

    def serialize(self, root: TreeNode) -> str:
        sb = []
        self._serialize_helper(root, sb)
        return "".join(sb) # Final string might have trailing separator, can be handled

    def _serialize_helper(self, node: TreeNode, sb: list[str]):
        if not node:
            sb.append(self.NULL_MARKER)
            sb.append(self.SEP)
            return
        
        # Pre-order: Root, Left, Right
        sb.append(str(node.val))
        sb.append(self.SEP)
        
        self._serialize_helper(node.left, sb)
        self._serialize_helper(node.right, sb)

    def deserialize(self, data: str) -> TreeNode | None:
        if not data: return None
        nodes_str_list = data.split(self.SEP)
        # Remove trailing empty string if data ends with SEP
        if nodes_str_list and not nodes_str_list[-1]:
            nodes_str_list.pop()
            
        # Use a list to act as a queue/iterator for node values
        self.nodes_iter = iter(nodes_str_list) 
        return self._deserialize_helper()

    def _deserialize_helper(self) -> TreeNode | None:
        try:
            val_str = next(self.nodes_iter)
        except StopIteration:
            return None # Should not happen if serialization is correct and data is not empty

        if val_str == self.NULL_MARKER:
            return None
        
        root_val = int(val_str)
        root = TreeNode(root_val)
        
        root.left = self._deserialize_helper()
        root.right = self._deserialize_helper()
        
        return root
```
Labuladong's visualization for pre-order serialization `![](/algo/images/binary-tree-serialization/1.jpeg)` shows: `1,2,#,4,#,#,3,#,#`.

### Deserialization (Pre-order)
Convert the string back to a list of values (splitting by separator). Then, use a recursive helper:
1.  Take the first value from the list; this is the current `root.val`.
2.  If it's the null marker, return `None`.
3.  Create `TreeNode(root_val)`.
4.  Recursively call for `root.left`.
5.  Recursively call for `root.right`.
A global index or an iterator over the list of values is needed to keep track of the current value being processed.

## Method 2: Post-order Traversal for Serialization/Deserialization

### Serialization (Post-order)
Similar to pre-order, but append `node.val` in the post-order position.
`_serialize_helper(node.left, sb)`, `_serialize_helper(node.right, sb)`, then `sb.append(str(node.val))`.
Example string: `#,#,4,#,2,#,#,3,1` for the tree `1,2,#,4,#,#,3,#,#` (pre-order).

### Deserialization (Post-order)
This is trickier. Since the root is at the *end* of the post-order list, we need to build from the end.
1. Pop the last element from list; this is `root.val`.
2. If null marker, return `None`.
3. Create `TreeNode(root_val)`.
4. **Crucially, build the *right* subtree first, then the *left* subtree**, because in post-order `(Left, Right, Root)`, the right subtree's elements appear immediately before the root in the reversed list.

```python
class CodecPostOrder:
    SEP = ","
    NULL_MARKER = "#"

    def serialize(self, root: TreeNode) -> str:
        sb = []
        self._serialize_helper(root, sb)
        return "".join(sb)

    def _serialize_helper(self, node: TreeNode, sb: list[str]):
        if not node:
            sb.append(self.NULL_MARKER)
            sb.append(self.SEP)
            return
        
        self._serialize_helper(node.left, sb)
        self._serialize_helper(node.right, sb)
        
        # Post-order: Left, Right, Root
        sb.append(str(node.val))
        sb.append(self.SEP)

    def deserialize(self, data: str) -> TreeNode | None:
        if not data: return None
        nodes_str_list = data.split(self.SEP)
        if nodes_str_list and not nodes_str_list[-1]: # Handle trailing separator
            nodes_str_list.pop()
        
        # For post-order, process from the end of the list
        # So, using pop() from a list acts like processing a reversed iterator
        self.nodes_list_for_pop = nodes_str_list 
        return self._deserialize_helper()

    def _deserialize_helper(self) -> TreeNode | None:
        if not self.nodes_list_for_pop:
            return None 
        
        val_str = self.nodes_list_for_pop.pop() # Get last element

        if val_str == self.NULL_MARKER:
            return None
        
        root_val = int(val_str)
        root = TreeNode(root_val)
        
        # IMPORTANT: For post-order deserialization (Root is last),
        # we build Right subtree first, then Left subtree from the reversed list.
        root.right = self._deserialize_helper()
        root.left = self._deserialize_helper()
        
        return root
```

## Method 3: Level-order Traversal for Serialization/Deserialization

### Serialization (Level-order)
Use BFS. Maintain a queue. Add `node.val` or null marker to string. Enqueue children.
Example string: `1,2,3,#,4,#,#,#,#` (for the tree `1,2,#,4,#,#,3,#,#` from pre-order). Note that level-order needs to represent trailing nulls properly if they are necessary to define structure (e.g., a right child of a node when left is null).

```python
import collections

class CodecLevelOrder:
    SEP = ","
    NULL_MARKER = "#"

    def serialize(self, root: TreeNode) -> str:
        if not root:
            return ""
        
        sb = []
        q = collections.deque([root])
        
        while q:
            node = q.popleft()
            if not node:
                sb.append(self.NULL_MARKER)
                sb.append(self.SEP)
                continue # Don't enqueue children of null
            
            sb.append(str(node.val))
            sb.append(self.SEP)
            
            q.append(node.left)  # Enqueue children, even if null
            q.append(node.right) # to maintain structure for deserialization
            
        return "".join(sb)

    def deserialize(self, data: str) -> TreeNode | None:
        if not data:
            return None
        
        nodes_str_list = data.split(self.SEP)
        if nodes_str_list and not nodes_str_list[-1]:
             nodes_str_list.pop()

        if not nodes_str_list or nodes_str_list[0] == self.NULL_MARKER:
            return None

        root = TreeNode(int(nodes_str_list[0]))
        q = collections.deque([root])
        i = 1 # Index for nodes_str_list

        while q and i < len(nodes_str_list):
            parent = q.popleft()
            
            # Left child
            if i < len(nodes_str_list) and nodes_str_list[i] != self.NULL_MARKER:
                left_child = TreeNode(int(nodes_str_list[i]))
                parent.left = left_child
                q.append(left_child)
            i += 1 # Advance index even if null
            
            # Right child
            if i < len(nodes_str_list) and nodes_str_list[i] != self.NULL_MARKER:
                right_child = TreeNode(int(nodes_str_list[i]))
                parent.right = right_child
                q.append(right_child)
            i += 1 # Advance index even if null
            
        return root
```
Labuladong's article also covers why in-order traversal (even with nulls) is problematic for unique reconstruction from a single traversal string.

## 总结 (Summary)
- Tree serialization converts a tree into a string, deserialization reconstructs it.
- **Pre-order Traversal:**
    - Serialize: "Root, Left, Right", represent nulls.
    - Deserialize: Recursively build root, then left subtree, then right subtree from a list/iterator of serialized values.
- **Post-order Traversal:**
    - Serialize: "Left, Right, Root", represent nulls.
    - Deserialize: Process serialized list from *end to beginning*. Build root, then *right* subtree, then *left* subtree.
- **Level-order Traversal (BFS):**
    - Serialize: Use a queue, add node values level by level, including nulls to maintain structure.
    - Deserialize: Use a queue to reconstruct. Read values for current parent's children, create nodes, and enqueue children.
- In-order traversal alone, even with nulls, is not sufficient for unique reconstruction.
- All these methods are typically $O(N)$ time and space, where $N$ is number of nodes.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/13 - Tree Problems - Combining Traversal and Decomposition|Prev: Combining Modes]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | (End of Labuladong Tree Pattern series for now) |
