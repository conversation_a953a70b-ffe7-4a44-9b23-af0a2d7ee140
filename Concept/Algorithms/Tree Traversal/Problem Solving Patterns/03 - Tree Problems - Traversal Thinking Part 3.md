---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, pattern/tree_problem, course/labuladong]
aliases: [Advanced Tree Traversal, DFS with Extra Logic, 二叉树遍历思维应用三]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's framework for solving tree problems using "traversal thinking", particularly when combined with other algorithmic techniques.
> Original source context: [[{source_file_path}|【练习】用「遍历」思维解题 III]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/02 - Tree Problems - Traversal Thinking Part 2|Prev: Traversal Thinking Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/04 - Tree Problems - Decomposition Thinking Part 1|Next: Decomposition Thinking Part 1]] |

# Tree Problems: Traversal Thinking Part 3 - Advanced Applications & Combinations

This part of "traversal thinking" explores more complex scenarios where simple DFS traversal is augmented with additional logic or data structures to solve sophisticated tree problems. Often, these problems require carrying more state during the traversal or combining traversal insights with other algorithmic patterns.

## 核心概念 (Core Concept)
The "traversal" framework ([[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|recursive DFS]]) remains the backbone. However, the actions performed at pre-order, in-order, or post-order positions become more involved. This might include:
-   Maintaining complex state variables passed through recursive calls.
-   Using auxiliary data structures (e.g., hash maps) updated during traversal.
-   Combining traversal with insights from other areas like [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|dynamic programming]] or [[Interview/Concept/Algorithms/Greedy Algorithms/00 - Greedy Algorithm - Core Framework|greedy algorithms]] at a local node level.

## 通用模板 (General Template - Example: Construct String from Binary Tree LC606)

LeetCode 606. Construct String from Binary Tree: "You need to construct a string consists of parenthesis and integers from a binary tree with the preorder traversal way."
Example: `Input: root = [1,2,3,4] Output: "1(2(4))(3)"`
This problem naturally fits pre-order traversal thinking.

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionConstructString:
    def tree2str(self, root: TreeNode) -> str:
        if not root:
            return ""

        # Pre-order: current node's value
        result_str = str(root.val)

        # Handle left child
        if root.left or root.right: # Need () for left child if right child exists or left exists
            result_str += "(" + self.tree2str(root.left) + ")"
        
        # Handle right child
        # Only add () for right child if it exists
        if root.right:
            result_str += "(" + self.tree2str(root.right) + ")"
            
        return result_str

# Example usage:
# root = TreeNode(1, TreeNode(2, TreeNode(4)), TreeNode(3))
# solver = SolutionConstructString()
# print(solver.tree2str(root)) # Output: "1(2(4))(3)"

# root = TreeNode(1, TreeNode(2, None, TreeNode(4)), TreeNode(3))
# print(solver.tree2str(root)) # Output: "1(2()(4))(3)" (LC606 specific rule: omit () for null left if right exists is tricky, this handles general case)
# LeetCode 606 has specific rules about omitting empty parentheses "()" for null children.
# The rule: "You need to omit all the empty parenthesis pairs that don't affect the one-to-one mapping relationship between the string and the original binary tree."
# - If a node has a left child but no right child, the right child's () can be omitted.
# - If a node has a right child but no left child, the left child's () `MUST` be included as "()" to show the right child belongs to current root.

# Refined LC606 solution using traversal thinking:
class SolutionLC606:
    def tree2str(self, root: TreeNode) -> str:
        res = []
        self._dfs_construct(root, res)
        return "".join(res)

    def _dfs_construct(self, node: TreeNode, res: list[str]):
        if not node:
            return

        res.append(str(node.val))

        if not node.left and not node.right: # Leaf node
            return

        # Left child
        res.append("(")
        self._dfs_construct(node.left, res)
        res.append(")")

        # Right child - only if it exists
        if node.right:
            res.append("(")
            self._dfs_construct(node.right, res)
            res.append(")")

# This second _dfs_construct still needs refinement for LC606's specific empty () rules.
# The first recursive `tree2str` is closer to a decomposition mindset.
# A pure traversal for LC606 would build a string list:

class SolutionLC606_Traversal:
    def tree2str(self, root: TreeNode) -> str:
        if not root: return ""
        
        # Current node (Pre-order position)
        s = str(root.val)
        
        # Left child
        if root.left or root.right: # Need parentheses for left if either child exists
            s += "(" + self.tree2str(root.left) + ")"
        
        # Right child
        if root.right: # Need parentheses for right only if right itself exists
            s += "(" + self.tree2str(root.right) + ")"
            
        return s
# The LC606 example is subtle and actually better solved with pure decomposition.
# The "traversal thinking" here applies if we imagine a string builder being passed around.
```
This example demonstrates that a problem might seem like simple traversal but the rules for constructing the output (handling `null` children specifically) can make it lean more towards decomposition logic for clarity. The key is that "traversal thinking" focuses on "what to do at *this* node" using information available *at* this node or passed down.

## 示例图示 (Diagram Example: Max Path Sum - LC124)
LeetCode 124. Binary Tree Maximum Path Sum. This problem is more complex and typically solved using a modified post-order traversal (which blends into decomposition thinking, as the function returns useful info from subtrees).

A true "traversal thinking" approach for a complex problem might involve:
- A DFS function `traverse(node, current_state_params...)`.
- `current_state_params` could include things like `max_sum_ending_at_parent_going_downwards`.
- A global variable `max_overall_path_sum` is updated whenever a new, larger path sum is discovered.

The processing at each node would involve:
1.  Considering paths that pass through the current node.
2.  Deciding whether to extend a path from a parent or start a new path at this node.
3.  Updating `max_overall_path_sum`.
4.  Passing relevant information down to children (e.g., "what's the max path sum ending at *you* if you connect to me?").

This can become very intricate with pure traversal thinking if the subproblem definition isn't clear. For LC124, the common solution defines a recursive helper that returns the "max path sum starting at this node and going downwards". This is fundamentally a decomposition approach.

## 总结 (Summary)
- Advanced applications of "traversal thinking" involve managing more complex state during DFS or combining traversal with other algorithmic ideas.
- The core remains visiting nodes and performing actions at pre/in/post-order positions.
- For highly complex problems where optimal substructure is clear, "decomposition thinking" (often leading to DP on trees) might be more structured, where the recursive function is defined by what it *returns* for a subtree, rather than primarily by side effects.
- Many hard tree problems require a blend: a traversal framework where the recursive calls themselves are designed with a decomposition mindset (i.e., they return meaningful values about their subtrees). This is the essence of [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]].

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/02 - Tree Problems - Traversal Thinking Part 2|Prev: Traversal Thinking Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/04 - Tree Problems - Decomposition Thinking Part 1|Next: Decomposition Thinking Part 1]] |
