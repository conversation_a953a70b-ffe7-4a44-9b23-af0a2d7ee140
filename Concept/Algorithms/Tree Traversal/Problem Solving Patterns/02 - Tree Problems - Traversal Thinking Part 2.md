---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, pattern/tree_problem, course/labuladong]
aliases: [Tree Node Operations DFS, DFS Traversal for Node Properties, 二叉树遍历思维应用二]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's framework for solving tree problems using "traversal thinking", specifically for operations or checks on individual nodes.
> Original source context: [[{source_file_path}|【练习】用「遍历」思维解题 II]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/01 - Tree Problems - Traversal Thinking Part 1|Prev: Traversal Thinking Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/03 - Tree Problems - Traversal Thinking Part 3|Next: Traversal Thinking Part 3]] |

# Tree Problems: Traversal Thinking Part 2 - Node-based Operations

This part continues exploring the "traversal" thinking mode for binary tree problems. Here, the focus is on performing operations or calculations related to individual nodes, often accumulating a global result or modifying nodes based on some criteria during a DFS traversal.

## 核心概念 (Core Concept)
When a problem requires examining each node and possibly updating a global state or the node itself based on its properties or properties passed down from its ancestors, the traversal framework is ideal. The logic is placed in the pre-order, in-order, or post-order position depending on when the node's processing should occur relative to its children.

## 通用模板 (General Template - Counting Nodes Satisfying a Condition)
Let's consider counting nodes in a BST that fall within a given range `[low, high]` (similar to LeetCode 938. Range Sum of BST, but counting instead of summing).

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionCountNodesInRange:
    def __init__(self):
        self.count = 0

    def count_nodes_in_range(self, root: TreeNode, low: int, high: int) -> int:
        self.count = 0 # Reset for multiple calls if object is reused
        self._traverse(root, low, high)
        return self.count

    def _traverse(self, node: TreeNode, low: int, high: int):
        if not node:
            return

        # Pre-order, in-order, or post-order?
        # For simple counting or summing where order doesn't strictly matter
        # for the correctness of the check itself, any order works.
        # However, for BSTs, leveraging the BST property can optimize.

        # Pre-order check:
        if low <= node.val <= high:
            self.count += 1
        
        # If it's a BST, we can prune branches:
        if node.val > low: # Or just node.val >= low if range is inclusive like low=node.val
            self._traverse(node.left, low, high) # Only go left if current node.val is not too small
        
        if node.val < high: # Or just node.val <= high if range is inclusive
            self._traverse(node.right, low, high) # Only go right if current node.val is not too large

# Example Usage (BST):
# root = TreeNode(10, TreeNode(5, TreeNode(3), TreeNode(7)), TreeNode(15, None, TreeNode(18)))
# solver = SolutionCountNodesInRange()
# node_count = solver.count_nodes_in_range(root, 7, 15) 
# print(node_count) # Expected: Nodes 7, 10, 15. Count = 3.

```
### 模板解析 (Template Explanation - for BST example)
1.  **Global Accumulator:** `self.count` stores the total count of nodes satisfying the condition.
2.  **Base Case:** If `node` is `None`, return.
3.  **Node Processing (Pre-order example):**
    *   Check if `node.val` is within the `[low, high]` range. If yes, increment `self.count`.
4.  **Optimized Recursive Calls (for BST):**
    *   If `node.val > low` (or `node.val >= low`), it's possible the left subtree contains nodes in range, so recurse left. If `node.val < low`, the entire left subtree is too small and can be skipped.
    *   If `node.val < high` (or `node.val <= high`), it's possible the right subtree contains nodes in range, so recurse right. If `node.val > high`, the entire right subtree is too large.
    *   For a non-BST tree, you'd unconditionally recurse on `node.left` and `node.right`.

The key is that "traversal thinking" involves a function that primarily *explores* the tree. The result is often accumulated outside the recursive function's direct return value (e.g., instance/global variable) or by modifying nodes directly.

## 示例图示 (Diagram Example: Invert Binary Tree - LC226)
LeetCode 226. Invert Binary Tree is a good example of node-based operation using traversal.
```python
class SolutionInvertTree:
    def invertTree(self, root: TreeNode) -> TreeNode:
        self._traverse_invert(root)
        return root

    def _traverse_invert(self, node: TreeNode):
        if not node:
            return

        # Pre-order, in-order, or post-order for swapping children?
        # Let's analyze:
        # Pre-order: Swap children, then recurse. The recursion will operate on already swapped subtrees.
        # Post-order: Recurse on children (inverting them), then swap the (now inverted) children.
        # In-order: Recurse left, swap, recurse right. Original right child becomes new left child for recursion.
        
        # All three could work with careful thought. Post-order is often intuitive:
        # "Invert my left child, invert my right child, then swap them."
        
        # Pre-order approach:
        # 1. Swap children of current node
        node.left, node.right = node.right, node.left
        # 2. Recurse on the (new) left child
        self._traverse_invert(node.left)
        # 3. Recurse on the (new) right child
        self._traverse_invert(node.right)

        # Post-order approach:
        # self._traverse_invert(node.left)
        # self._traverse_invert(node.right)
        # node.left, node.right = node.right, node.left 
        # Both yield correct result for simple inversion.
```
**Visualization (Inverting children at each node):**
Original: `A -> (B, C)`
Post-order step at A (after B, C subtrees are inverted):
- `A.left` (orig B), `A.right` (orig C)
- Swap them: `A.left` becomes (inverted) C, `A.right` becomes (inverted) B.
Result: `A -> (InvC, InvB)`

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\small},
    level 1/.style={sibling distance=2cm},
    level 2/.style={sibling distance=1.5cm}
]
\node[treenode] (A) {A};
\node[treenode, below left=1cm and 0.5cm of A] (B) {B};
\node[treenode, below right=1cm and 0.5cm of A] (C) {C};
\node[treenode, below left=1cm and 0.25cm of B] (D) {D};
\node[treenode, below right=1cm and 0.25cm of B] (E) {E};

\draw (A) -- (B); \draw (A) -- (C);
\draw (B) -- (D); \draw (B) -- (E);

\node at (3, -1.5) {Original Tree};

\begin{scope}[xshift=6cm]
    \node[treenode] (A2) {A};
    \node[treenode, below left=1cm and 0.5cm of A2] (C2) {C}; % Swapped
    \node[treenode, below right=1cm and 0.5cm of A2] (B2) {B}; % Swapped
    \node[treenode, below left=1cm and 0.25cm of B2] (E2) {E}; % Swapped
    \node[treenode, below right=1cm and 0.25cm of B2] (D2) {D}; % Swapped

    \draw (A2) -- (C2); \draw (A2) -- (B2);
    \draw (B2) -- (E2); \draw (B2) -- (D2); % Original B's children are D,E. Now B2's children are E2,D2.
    \node at (3, -1.5) {Inverted Tree};
\end{scope}
\end{tikzpicture}
```
The traversal visits each node. At each node (say, in pre-order), it performs the swap of its direct children. Then it recursively calls for the (new) left and right children.

## 总结 (Summary)
- "Traversal thinking" for node-based operations means designing a DFS function that visits nodes and performs actions based on the node's data or parameters passed down.
- Results are often accumulated in global/instance variables, or nodes are modified in-place.
- The choice of pre-order, in-order, or post-order logic depends on whether a node's processing needs to happen before, between, or after its children are processed.
- For BSTs, traversal can be optimized by pruning branches that cannot satisfy the condition.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/01 - Tree Problems - Traversal Thinking Part 1|Prev: Traversal Thinking Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/03 - Tree Problems - Traversal Thinking Part 3|Next: Traversal Thinking Part 3]] |
