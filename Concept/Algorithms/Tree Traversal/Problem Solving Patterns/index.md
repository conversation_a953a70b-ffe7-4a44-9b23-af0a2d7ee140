---
tags: [index, concept/algorithms, concept/tree_traversal, concept/problem_solving_pattern, course/labuladong]
aliases: [Binary Tree Problem Patterns Index, 二叉树算法习题模式索引, Labuladong Tree Problem Frameworks]
---

> [!NOTE] Source Annotation
> This index is based on the structure and intent of Labuladong's "二叉树算法习题汇总" chapter introduction.
> Original source: [[{source_file_path}|本章导读 - 二叉树算法习题汇总]]

# Binary Tree Problem-Solving Patterns (Labuladong Inspired)

This section aggregates various problem-solving patterns and thinking modes for tackling binary tree algorithm questions, drawing heavily from Labuladong's "二叉树算法习题汇总" collection. The focus is on understanding how to apply core traversal and decomposition strategies to a wide range of tree problems.

Mastering these patterns, particularly the distinction between "traversal" thinking and "decomposition" (divide and conquer) thinking as applied to binary trees, can significantly enhance one's ability to solve tree-based algorithmic challenges efficiently.

## Core Thinking Modes & Traversal Positions

These notes explore how to leverage different parts of the standard tree traversal framework ([[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|recursive DFS]]) and specific thinking modes ([[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Core Algorithmic Principles]]) to solve problems.

-   **Traversal Thinking Patterns (遍历思维模式):**
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/01 - Tree Problems - Traversal Thinking Part 1|Traversal Thinking Part 1: Path-based Problems]]
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/02 - Tree Problems - Traversal Thinking Part 2|Traversal Thinking Part 2: Node-based Operations]]
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/03 - Tree Problems - Traversal Thinking Part 3|Traversal Thinking Part 3: Advanced Traversal Applications]]

-   **Decomposition Thinking Patterns (分解问题思维模式 / Divide & Conquer):**
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/04 - Tree Problems - Decomposition Thinking Part 1|Decomposition Thinking Part 1: Tree Construction]]
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/05 - Tree Problems - Decomposition Thinking Part 2|Decomposition Thinking Part 2: Symmetric & Structural Problems]]

-   **Leveraging Post-Order Traversal (后序位置的妙用):**
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/06 - Tree Problems - Post-Order Solutions Part 1|Post-Order Solutions Part 1: Basic Aggregations & Properties]]
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/07 - Tree Problems - Post-Order Solutions Part 2|Post-Order Solutions Part 2: Complex Dependencies]]
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/08 - Tree Problems - Post-Order Solutions Part 3|Post-Order Solutions Part 3: Advanced Applications]]

-   **Binary Search Tree (BST) Specific Patterns (二叉搜索树特性应用):**
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/09 - BST Problems - Properties and Iteration Part 1|BST Problems Part 1: Leveraging In-order Traversal & Properties]]
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/10 - BST Problems - Modification and Construction Part 2|BST Problems Part 2: Modification & Construction]]

-   **Level-Order Traversal (BFS) Patterns (层序遍历应用):**
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/11 - Tree Problems - Level-Order (BFS) Part 1|Level-Order (BFS) Solutions Part 1: Level-based Views]]
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/12 - Tree Problems - Level-Order (BFS) Part 2|Level-Order (BFS) Solutions Part 2: Width & Connectivity]]

-   **Combining Thinking Modes (综合运用):**
    -   [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/13 - Tree Problems - Combining Traversal and Decomposition|Combining Traversal and Decomposition]]

## Visualization of Pattern Categories

```mermaid
graph TD
    Root["Binary Tree Problem Patterns"]

    subgraph "Traversal (DFS-like)"
        direction LR
        T1["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/01 - Tree Problems - Traversal Thinking Part 1|Paths]]"]
        T2["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/02 - Tree Problems - Traversal Thinking Part 2|Node Ops]]"]
        T3["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/03 - Tree Problems - Traversal Thinking Part 3|Advanced Apps]]"]
    end

    subgraph "Decomposition (D&C)"
        direction LR
        D1["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/04 - Tree Problems - Decomposition Thinking Part 1|Construction]]"]
        D2["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/05 - Tree Problems - Decomposition Thinking Part 2|Structure]]"]
    end
    
    subgraph "Post-Order Magic"
        direction LR
        P1["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/06 - Tree Problems - Post-Order Solutions Part 1|Aggregation]]"]
        P2["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/07 - Tree Problems - Post-Order Solutions Part 2|Dependencies]]"]
        P3["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/08 - Tree Problems - Post-Order Solutions Part 3|Advanced]]"]
    end

    subgraph "BST Specifics"
        direction LR
        BST1["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/09 - BST Problems - Properties and Iteration Part 1|Properties]]"]
        BST2["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/10 - BST Problems - Modification and Construction Part 2|Modification]]"]
    end

    subgraph "Level-Order (BFS)"
        direction LR
        L1["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/11 - Tree Problems - Level-Order (BFS) Part 1|Level Views]]"]
        L2["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/12 - Tree Problems - Level-Order (BFS) Part 2|Width/Connect]]"]
    end
    
    Root --> TravThinking["Traversal Thinking"]
    TravThinking --> T1
    TravThinking --> T2
    TravThinking --> T3

    Root --> DecompThinking["Decomposition Thinking"]
    DecompThinking --> D1
    DecompThinking --> D2

    Root --> PostOrder["Post-Order Applications"]
    PostOrder --> P1
    PostOrder --> P2
    PostOrder --> P3
    
    Root --> BSTSpecifics["BST Patterns"]
    BSTSpecifics --> BST1
    BSTSpecifics --> BST2

    Root --> LevelOrderBFS["Level-Order (BFS) Patterns"]
    LevelOrderBFS --> L1
    LevelOrderBFS --> L2

    Root --> Combined["[[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/13 - Tree Problems - Combining Traversal and Decomposition|Combining Modes]]"]

    classDef main fill:#cde4ff,stroke:#5a9ae5;
    classDef category fill:#e6ffe6,stroke:#006400;
    class Root main;
    class TravThinking, DecompThinking, PostOrder, BSTSpecifics, LevelOrderBFS, Combined category;
```

By studying these patterns, one can develop a more systematic approach to solving a wide variety of binary tree problems.

---
Parent: [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal Algorithms]]
