---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, concept/recursion, pattern/tree_problem, course/labuladong]
aliases: [Hybrid Tree Thinking, Dual Approach Tree Problems, 二叉树双思维模式解题, Binary Tree Thinking Modes]
---
> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷二叉树算法/二叉树心法（思路篇）.md]].
> This note explores problems where both "Traversal" and "Decomposition" thinking modes from [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]] can be applied, or where a blend is optimal.

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/12 - Tree Problems - Level-Order (BFS) Part 2|Prev: Level-Order (BFS) Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/14 - Tree Serialization and Deserialization|Next: Serialization]] |

# Tree Problems: Combining or Choosing Between Traversal & Decomposition Thinking

Many binary tree problems can be solved effectively using either the "traversal" (DFS with side effects) or "decomposition" (divide and conquer, function returns sub-result) thinking modes. Some problems are particularly illustrative of how both approaches can lead to a solution, or how one might be more intuitive or efficient than the other. Understanding when and how to apply each, or even combine them, is a sign of advanced understanding.

## 核心概念 (Core Concept Recap)
-   **Traversal Thinking:** Focuses on what each node *does* during a traversal (e.g., DFS). Results are often accumulated in external variables or by modifying node properties. Logic is placed in pre-order, in-order, or post-order positions.
-   **Decomposition Thinking:** Focuses on what a recursive function *returns* for a subtree. The solution for a node is built from the returned solutions of its children's subtrees (typically using post-order logic for combination).

## 示例问题 (Illustrative Problems)

### Example 1: Invert Binary Tree (LC226)
[[Interview/Practice/LeetCode/LC226 - Invert Binary Tree|LC226 - Invert Binary Tree]] asks to mirror a binary tree.

**Traversal Thinking Solution:**
```python
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None):
#         self.val = val; self.left = left; self.right = right

class SolutionInvert_Traversal:
    def invertTree(self, root: TreeNode) -> TreeNode:
        self._traverse_and_swap(root)
        return root

    def _traverse_and_swap(self, node: TreeNode):
        if not node:
            return
        
        # Pre-order action: Swap children
        node.left, node.right = node.right, node.left
        
        # Continue traversal
        self._traverse_and_swap(node.left) # Traverses the original right child
        self._traverse_and_swap(node.right) # Traverses the original left child
```
Here, each node performs an action (swapping its children). The traversal ensures all nodes are visited.

**Decomposition Thinking Solution:**
```python
class SolutionInvert_Decomposition:
    def invertTree(self, root: TreeNode) -> TreeNode:
        # Definition: invertTree(node) inverts the subtree at 'node' and returns its new root (which is 'node' itself).
        if not root:
            return None
        
        # Recursively invert left and right subtrees
        inverted_left_subtree_root = self.invertTree(root.left)
        inverted_right_subtree_root = self.invertTree(root.right)
        
        # Post-order action: Swap the (already inverted) children of the current root
        root.left = inverted_right_subtree_root
        root.right = inverted_left_subtree_root
        
        return root
```
Both are valid and achieve the same $O(N)$ time and $O(H)$ space (recursion stack). The decomposition approach explicitly uses the return values (the roots of the inverted subtrees), while the traversal approach modifies the tree in place as a side effect of visiting nodes.

### Example 2: Populating Next Right Pointers in Each Node (LC116)
[[Interview/Practice/LeetCode/LC116 - Populating Next Right Pointers in Each Node|LC116 - Populating Next Right Pointers in Each Node]] (for perfect binary trees).

**Traversal Thinking (Modified Traversal):**
This problem is trickier for a standard single-node traversal function. Labuladong suggests an ingenious approach: define a `traverse` function that takes *two* nodes as arguments, `node1` and `node2`, which are assumed to be adjacent at the same level. The function's job is to connect `node1.next = node2`.
```python
# class Node: # val, left, right, next
#     ...

class SolutionConnect_Traversal:
    def connect(self, root: 'Node') -> 'Node':
        if not root:
            return None
        self._traverse_connect(root.left, root.right)
        return root

    def _traverse_connect(self, node1: 'Node', node2: 'Node'):
        if not node1 or not node2: # Base case for recursion
            return

        # Pre-order action: Connect the two passed-in nodes
        node1.next = node2

        # Recursively connect children within the same parent
        self._traverse_connect(node1.left, node1.right)
        self._traverse_connect(node2.left, node2.right)
        
        # Recursively connect children across different parents (node1's right to node2's left)
        self._traverse_connect(node1.right, node2.left)
```
This "traversal" is over conceptual "pairs of adjacent nodes."

**Decomposition Thinking:** Is it applicable here? It's less direct. One might define `connect(root)` to fully connect the subtree at `root`. This would involve connecting `root.left` to `root.right`, and then ensuring `root.left.right` (if it exists) connects to `root.right.left` (if it exists and is the next node in level order). This becomes more like BFS logic or requires passing more contextual info (like the "next node outside this subtree at this level"). The two-node traversal approach is a clever adaptation of "traversal thinking."
For a BFS solution see [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/12 - Tree Problems - Level-Order (BFS) Part 2|Level-Order (BFS) Part 2]].

### Example 3: Flatten Binary Tree to Linked List (LC114)
[[Interview/Practice/LeetCode/LC114 - Flatten Binary Tree to Linked List|LC114 - Flatten Binary Tree to Linked List]]. The tree should be flattened in pre-order.

**Traversal Thinking (Attempt 1 - with external list, not in-place):**
One could do a pre-order traversal to collect all node values, then build a new linked-list-like tree. This is "traversal thinking" but doesn't meet the in-place requirement usually implied.

**Decomposition Thinking (In-place):**
Define `flatten(root)` to flatten the subtree at `root` and, crucially, it could return the *tail* of the flattened list to help connect to the next part. Or, modify in-place and rely on side effects.
Labuladong's provided solution for LC114 uses a post-order decomposition:
```python
# class SolutionFlatten_Decomposition:
#     def flatten(self, root: TreeNode) -> None:
#         if not root:
#             return
        
#         # Flatten left and right subtrees first
#         self.flatten(root.left)
#         self.flatten(root.right)
        
#         # Post-order actions:
#         # 1. Store original left and right children
#         left_subtree_flattened_head = root.left
#         right_subtree_flattened_head = root.right
        
#         # 2. Make the flattened left subtree the new right child of root
#         root.left = None
#         root.right = left_subtree_flattened_head
        
#         # 3. Find the tail of the (now right) flattened left subtree
#         p = root
#         while p.right:
#             p = p.right
        
#         # 4. Attach the flattened original right subtree to this tail
#         p.right = right_subtree_flattened_head
```
This fits decomposition: `flatten(root)` assumes `flatten(root.left)` and `flatten(root.right)` correctly do their job on subtrees. Then, it rearranges pointers at `root` using the now-flattened subtrees.

## 总结 (Summary)
- Many tree problems can be approached using either "traversal" or "decomposition" perspectives.
- **Traversal thinking** is often about iterating through nodes and performing actions, possibly with side effects or by maintaining state passed through parameters (e.g., current path, current depth).
- **Decomposition thinking** defines a recursive function by what it computes and returns for a subtree, combining results from children (often in post-order).
- For simple property calculations (height, size), decomposition is typically elegant.
- For path-finding or stateful exploration, traversal (with backtracking) is common.
- Complex problems might blend these: a traversal framework where the main recursive logic is decompositional (returns a value) but also updates a global/instance variable as a side effect (e.g., max diameter found so far while primarily calculating depth).
- Understanding both modes and being able to switch between them or recognize which is more suitable is key to mastering tree algorithms.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/12 - Tree Problems - Level-Order (BFS) Part 2|Prev: Level-Order (BFS) Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/14 - Tree Serialization and Deserialization|Next: Serialization]] |

