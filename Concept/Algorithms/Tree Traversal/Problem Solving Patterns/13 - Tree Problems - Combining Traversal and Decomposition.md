---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, concept/recursion, pattern/tree_problem, course/labuladong]
aliases: [Hybrid Tree Thinking, Dual Approach Tree Problems, 二叉树双思维模式解题]
---
> [!NOTE] Source Annotation
> Content inspired by <PERSON><PERSON><PERSON>'s philosophy that some tree problems can be approached using either "traversal" or "decomposition" thinking, and understanding both deepens mastery.
> Original source context: [[{source_file_path}|【练习】同时运用两种思维解题]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/12 - Tree Problems - Level-Order (BFS) Part 2|Prev: Level-Order (BFS) Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | (End of specific patterns, consider linking to Tree Traversal Index) |

# Tree Problems: Combining or Choosing Between Traversal & Decomposition Thinking

Many binary tree problems can be solved effectively using either the "traversal" (DFS with side effects) or "decomposition" (divide and conquer, function returns sub-result) thinking modes outlined in [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]]. Some problems are particularly illustrative of how both approaches can lead to a solution, or how one might be more intuitive or efficient than the other. Understanding when and how to apply each, or even combine them, is a sign of advanced understanding.

## 核心概念 (Core Concept)
-   **Traversal Thinking:** Focuses on what each node *does* during a traversal. Results are often accumulated in external variables. Pre-order, in-order, and post-order positions are key for injecting logic.
-   **Decomposition Thinking:** Focuses on what a recursive function *returns* for a subtree. The solution for a node is built from the returned solutions of its children's subtrees (typically post-order).

This note explores problems where either perspective is viable or where a blend is optimal.

## 示例问题 (Illustrative Problem: Max Depth of Binary Tree LC104)
LeetCode 104. Maximum Depth of Binary Tree ([[Interview/Practice/LeetCode/LC104 - Maximum Depth of Binary Tree|LC104]]) is a prime example where both modes apply clearly.

### Approach 1: Traversal Thinking
Maintain a `current_depth` during DFS. When a leaf (or null beyond leaf) is reached, update a global `max_overall_depth`.

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionMaxDepth_Traversal:
    def __init__(self):
        self.max_d = 0
    
    def _traverse(self, node: TreeNode, current_depth: int):
        if not node:
            # Reached end of a path, update max_d if current_depth is greater
            # Or, if depth is #nodes, process at leaf. If depth is #edges, this is fine.
            # Let's assume depth = number of nodes on path.
            # If leaf is base, update self.max_d = max(self.max_d, current_depth) here.
            return

        # Update max_d at each node, especially if definition of depth is number of nodes.
        # For path length to leaf, it's better to check at leaf or just beyond.
        # If a leaf is depth D, its null children path is D. Update at null.
        self.max_d = max(self.max_d, current_depth + 1) # +1 for current node

        self._traverse(node.left, current_depth + 1)
        self._traverse(node.right, current_depth + 1)

    # Alternative traversal for path length (edges):
    def _traverse_path_len(self, node: TreeNode, path_len: int):
        if not node: # Path ended before this node
            return
        
        if not node.left and not node.right: # This is a leaf
            self.max_d = max(self.max_d, path_len) # path_len is number of edges to this leaf
            return
            
        self._traverse_path_len(node.left, path_len + 1)
        self._traverse_path_len(node.right, path_len + 1)

    def maxDepth(self, root: TreeNode) -> int:
        if not root: return 0
        self.max_d = 0
        # Using path_len as number of edges to current node (root has path_len 0)
        # self._traverse_path_len(root, 0) 
        # return self.max_d + 1 # if max_d stores max edges, depth (nodes) is edges+1

        # If depth = number of nodes. Start root with depth 1.
        self._traverse_nodes_depth(root, 1)
        return self.max_d

    def _traverse_nodes_depth(self, node: TreeNode, current_node_depth: int):
        if not node:
            return
        
        self.max_d = max(self.max_d, current_node_depth)
        
        self._traverse_nodes_depth(node.left, current_node_depth + 1)
        self._traverse_nodes_depth(node.right, current_node_depth + 1)

```
This uses an instance variable `self.max_d` and passes `current_depth` down.

### Approach 2: Decomposition Thinking
The function `maxDepth(node)` returns the depth of the subtree rooted at `node`.
```python
class SolutionMaxDepth_Decomposition:
    def maxDepth(self, root: TreeNode) -> int:
        if not root:
            return 0 # Depth of an empty tree is 0
        
        left_subtree_depth = self.maxDepth(root.left)
        right_subtree_depth = self.maxDepth(root.right)
        
        # Post-order: combine results
        return 1 + max(left_subtree_depth, right_subtree_depth)
```
This is often considered more elegant and purely functional for this specific problem.

## 何时选择哪种思维 (When to Choose Which Mode)

-   **Decomposition:**
    -   **Pros:** Often leads to cleaner, more functional code, especially if the problem has clear optimal substructure (e.g., height, size, sum, isBalanced, isSymmetric). The function's return value directly contributes to solving the parent's problem. Avoids global/instance state for the result.
    -   **Cons:** May not be intuitive for problems requiring complex path tracking or "global" context beyond direct parent-child relationships.

-   **Traversal:**
    -   **Pros:** More natural for problems that involve "visiting" nodes sequentially and performing actions based on a path or accumulated state (e.g., path sum, serializing/deserializing, Kth smallest if iterating in order). Backtracking problems are a prime example of traversal thinking.
    -   **Cons:** Can lead to reliance on instance/global variables if not careful, making the function less "pure." Passing complex state down can make function signatures cumbersome.

## 示例图示 (Diagram: Conceptual Difference)

```tikz
\begin{tikzpicture}[
    mode_box/.style={rectangle, draw, rounded corners, fill=blue!10, text width=5cm, align=center, minimum height=2cm, font=\sffamily\small},
    arrow/.style={->, thick, >=stealth}
]

% Traversal Thinking
\node[mode_box] (traversal) at (0,0) {
    \textbf{Traversal Thinking (DFS)}\\
    `void traverse(node, path_state)`\\
    - Global/instance `result` variable.\\
    - Logic in Pre/In/Post order slots.\\
    - Example: Accumulate all root-to-leaf paths.
};
\node[below=0.5cm of traversal, font=\sffamily\tiny, text width=5cm, align=center] (trav_focus)
    {Focus: Action at each node during a fixed exploration sequence. Result built via side-effects.};

% Decomposition Thinking
\node[mode_box] (decomposition) at (7,0) {
    \textbf{Decomposition Thinking (D&C)}\\
    `ReturnType solve(node)`\\
    - Function returns solution for `node`'s subtree.\\
    - `res_L = solve(node.left)`\\
    - `res_R = solve(node.right)`\\
    - `return combine(node.val, res_L, res_R)` (Post-order).
};
\node[below=0.5cm of decomposition, font=\sffamily\tiny, text width=5cm, align=center] (decomp_focus)
    {Focus: Defining subproblem solution. Result built by combining sub-solutions.};

\node[cloud, draw, cloud puffs=10, cloud puff arc=120, aspect=2.5, fill=orange!20] at (3.5, -3) {
    Many problems can use either, but one might be more natural or efficient.
    Advanced problems might blend: e.g., a traversal function that calls a decomposition-style helper.
};
\end{tikzpicture}
```

## 总结 (Summary)
- Many tree problems allow solutions from both "traversal" and "decomposition" perspectives.
- **Traversal thinking** is like exploring the tree and taking notes or actions as you go, often using global/instance variables or passed-down state.
- **Decomposition thinking** is about defining what a function should compute for a subtree and how to build that from its children's results.
- Understanding both modes enhances problem-solving flexibility. For simple property calculations (height, size), decomposition is often cleaner. For path-finding or stateful exploration, traversal (with backtracking) is common.
- The "best" approach depends on the specific problem, clarity of implementation, and sometimes efficiency nuances (though both are typically DFS-based and $O(N)$ for visiting nodes).

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/12 - Tree Problems - Level-Order (BFS) Part 2|Prev: Level-Order (BFS) Part 2]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | Next: (Consider linking to [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Core Principles]] or main Tree Traversal Index) |
