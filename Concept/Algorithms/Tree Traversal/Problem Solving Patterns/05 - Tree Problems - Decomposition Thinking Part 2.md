---
tags: [concept/algorithms, concept/tree_traversal, concept/recursion, concept/divide_and_conquer, pattern/tree_problem, course/labuladong]
aliases: [Tree Structural Problems, Symmetric Tree, Invert Tree, 二叉树分解问题思维应用二]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's framework for solving tree problems using "decomposition thinking," focusing on structural or symmetric properties.
> Original source context: [[{source_file_path}|【练习】用「分解问题」思维解题 II]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/04 - Tree Problems - Decomposition Thinking Part 1|Prev: Decomposition Thinking Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/06 - Tree Problems - Post-Order Solutions Part 1|Next: Post-Order Solutions Part 1]] |

# Tree Problems: Decomposition Thinking Part 2 - Symmetric & Structural Problems

This part continues exploring "decomposition thinking" ([[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]]) for binary tree problems. The focus here is on problems that involve checking or manipulating the structure of the tree, often based on symmetry or relationships between subtrees.

## 核心概念 (Core Concept)
Decomposition thinking involves defining a recursive function that solves a problem for a subtree rooted at a given node, typically by:
1.  Solving the problem for its left and right children (recursive calls).
2.  Combining the results from the children with the current node's properties to solve the problem for the current node's subtree.
This inherently uses post-order logic for the combination step.

## 通用模板 (General Template - Symmetric Tree Example)
A classic example is LeetCode 101. Symmetric Tree, which asks if a tree is a mirror image of itself.

**Recursive Function Definition:** `is_mirror(node1, node2)`
This helper function checks if the subtree rooted at `node1` is a mirror image of the subtree rooted at `node2`.

1.  **Base Cases:**
    *   If both `node1` and `node2` are `None`, they are symmetric (empty subtrees). Return `True`.
    *   If one is `None` and the other is not, they are not symmetric. Return `False`.
    *   If `node1.val != node2.val`, they are not symmetric. Return `False`.
2.  **Recursive Step (Combine Results):**
    *   The tree is symmetric if `node1.left` is a mirror of `node2.right` AND `node1.right` is a mirror of `node2.left`.
    *   Return `is_mirror(node1.left, node2.right) and is_mirror(node1.right, node2.left)`.

The main function `isSymmetric(root)` calls `is_mirror(root.left, root.right)` if root is not None.

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionSymmetricTree:
    def isSymmetric(self, root: TreeNode) -> bool:
        if not root:
            return True
        return self._is_mirror(root.left, root.right)

    def _is_mirror(self, node1: TreeNode, node2: TreeNode) -> bool:
        # Both are None, symmetric
        if not node1 and not node2:
            return True
        # One is None, the other is not, not symmetric
        if not node1 or not node2:
            return False
        
        # Values must be equal
        if node1.val != node2.val:
            return False
            
        # Check if (node1's left subtree is mirror of node2's right subtree)
        # AND (node1's right subtree is mirror of node2's left subtree)
        return self._is_mirror(node1.left, node2.right) and \
               self._is_mirror(node1.right, node2.left)

# Example Usage:
# root_sym = TreeNode(1, TreeNode(2, TreeNode(3), TreeNode(4)), TreeNode(2, TreeNode(4), TreeNode(3)))
# solver = SolutionSymmetricTree()
# print(solver.isSymmetric(root_sym)) # True

# root_asym = TreeNode(1, TreeNode(2, None, TreeNode(3)), TreeNode(2, None, TreeNode(3)))
# print(solver.isSymmetric(root_asym)) # False (2's right 3 vs 2's right 3. Should be 2's left vs 2's right)
```

### 模板解析 (Template Explanation)
-   The function `_is_mirror` is defined by what it returns: a boolean indicating if two subtrees are mirror images.
-   The solution for the current pair `(node1, node2)` is built upon the solutions for sub-pairs `(node1.left, node2.right)` and `(node1.right, node2.left)`.
-   This is a classic example of "decomposition" where the problem is broken into smaller, self-similar structural comparisons.

## 示例图示 (Diagram Example: Symmetric Tree Check)
Consider `root = [1,2,2,3,4,4,3]`
```
      1
     / \
    2   2
   / \ / \
  3  4 4  3
```
`isSymmetric(root)` calls `_is_mirror(root.left, root.right)`, i.e., `_is_mirror(Node(2)_left, Node(2)_right)`.

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\small},
    call_box/.style={rectangle, draw, fill=blue!10, rounded corners, text width=5.5cm, align=center, font=\sffamily\scriptsize},
    level 1/.style={sibling distance=3cm, level distance=1.5cm},
    level 2/.style={sibling distance=1.5cm}
]

% Tree visualization
\node[treenode] (n1) at (0,0) {1}
    child {node[treenode] (n2L) {2}
        child {node[treenode] (n3L) {3}}
        child {node[treenode] (n4L) {4}}
    }
    child {node[treenode] (n2R) {2}
        child {node[treenode] (n4R) {4}}
        child {node[treenode] (n3R) {3}}
    };

% is_mirror call visualization
\node[call_box] (call1) at (0, -4) {
    `_is_mirror(Node(2)_L, Node(2)_R)`:\\
    Values match (2==2). Check children:\\
    1. `_is_mirror(Node(2)_L.left, Node(2)_R.right)` $\rightarrow$ `_is_mirror(Node(3)_L, Node(3)_R)`\\
    2. `_is_mirror(Node(2)_L.right, Node(2)_R.left)` $\rightarrow$ `_is_mirror(Node(4)_L, Node(4)_R)`
};

\node[call_box, fill=green!20] (call_3_3) at (-3, -6.5) {
    `_is_mirror(Node(3)_L, Node(3)_R)`:\\
    Values match (3==3).\\
    Children are null, returns True.
};
\node[call_box, fill=green!20] (call_4_4) at (3, -6.5) {
    `_is_mirror(Node(4)_L, Node(4)_R)`:\\
    Values match (4==4).\\
    Children are null, returns True.
};

\draw[->, dashed] (call1) -- (call_3_3);
\draw[->, dashed] (call1) -- (call_4_4);
\node at (0, -7.5) {Both sub-calls return True $\implies$ `_is_mirror(Node(2)_L, Node(2)_R)` returns True. Overall symmetric.};

\end{tikzpicture}
```
The problem is broken down by comparing `node1.left` with `node2.right` and `node1.right` with `node2.left` recursively. This ensures the "mirror" property is checked at each level.

## 总结 (Summary)
- "Decomposition thinking" is highly effective for problems involving structural properties of trees, like symmetry or equivalence.
- The recursive function is defined to solve the problem for a given node or pair of nodes, relying on results from recursive calls on their children/sub-components.
- Base cases are crucial for terminating recursion (e.g., null nodes, value mismatches).
- The combination logic (usually in post-order) synthesizes the overall solution from subproblem solutions.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/04 - Tree Problems - Decomposition Thinking Part 1|Prev: Decomposition Thinking Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/06 - Tree Problems - Post-Order Solutions Part 1|Next: Post-Order Solutions Part 1]] |
