---
tags: [concept/algorithms, concept/tree_traversal, concept/dfs, pattern/tree_problem, course/labuladong]
aliases: [Tree Path Problems DFS, DFS Traversal for Paths, 二叉树遍历思维应用一]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's framework for solving tree problems using "traversal thinking", particularly for path-related tasks.
> Original source context: [[{source_file_path}|【练习】用「遍历」思维解题 I]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/02 - Tree Problems - Traversal Thinking Part 2|Next: Traversal Thinking Part 2]] |

# Tree Problems: Traversal Thinking Part 1 - Path-based Problems

The "traversal" thinking mode, as outlined in [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]], involves using a standard tree traversal (usually DFS) and injecting logic at pre-order, in-order, or post-order positions. This part focuses on applying this to problems involving paths in a binary tree, such as finding specific paths or path properties.

## 核心概念 (Core Concept)
Many path-related problems require accumulating information along a path from the root to a node (often a leaf). This is naturally handled by passing a "current path" data structure (e.g., a list) down the recursion or by maintaining it as an instance variable that is updated during the DFS traversal. [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking]] is often essential: when a choice (going down a branch) is made, the path is updated; after the recursive call for that branch returns, the choice is undone to explore other branches.

## 通用模板 (General Template - Path Sum Example)
Let's consider a common problem: finding all root-to-leaf paths that sum to a target value (similar to LeetCode 113. Path Sum II).

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionPathSum:
    def __init__(self):
        self.result_paths = []
        self.current_path = []

    def find_paths(self, root: TreeNode, target_sum: int) -> list[list[int]]:
        self.result_paths = []
        self.current_path = []
        self._traverse(root, target_sum)
        return self.result_paths

    def _traverse(self, node: TreeNode, remaining_sum: int):
        if not node:
            return

        # Pre-order action: Add current node to path
        self.current_path.append(node.val)
        new_remaining_sum = remaining_sum - node.val

        # Check if it's a leaf node and if the path sum matches
        if not node.left and not node.right: # Leaf node
            if new_remaining_sum == 0:
                # Found a valid path, add a copy to results
                self.result_paths.append(list(self.current_path))
            # Backtrack will happen after this block
        else:
            # Continue traversal if not a leaf
            if node.left:
                self._traverse(node.left, new_remaining_sum)
            if node.right:
                self._traverse(node.right, new_remaining_sum)

        # Post-order action (Backtrack): Remove current node from path
        self.current_path.pop()

# Example usage:
# root = TreeNode(5, TreeNode(4, TreeNode(11, TreeNode(7), TreeNode(2))), TreeNode(8, TreeNode(13), TreeNode(4, TreeNode(5), TreeNode(1))))
# solver = SolutionPathSum()
# paths = solver.find_paths(root, 22)
# print(paths) # Expected: [[5, 4, 11, 2], [5, 8, 4, 5]]
```

### 模板解析 (Template Explanation)
1.  **State Variables:**
    *   `result_paths`: Stores all valid paths found.
    *   `current_path`: Stores the nodes in the path currently being explored.
    *   `remaining_sum`: The sum still needed to reach the target.
2.  **Base Case (`_traverse`):** If `node` is `None`, stop this branch.
3.  **Pre-order Logic:**
    *   Add `node.val` to `current_path`.
    *   Update `remaining_sum`.
4.  **Leaf Node Check:**
    *   If `node` is a leaf and `new_remaining_sum == 0`, a valid path is found. Add a *copy* of `current_path` to `result_paths`.
5.  **Recursive Calls:**
    *   Recursively call `_traverse` for left and right children.
6.  **Post-order Logic (Backtracking):**
    *   After returning from recursive calls for children (i.e., after exploring subtrees rooted at `node.left` and `node.right`), remove `node.val` from `current_path` using `pop()`. This "undoes" the choice of including `node` in the path, allowing exploration of other paths correctly.

## 示例图示 (Diagram Example: Path Sum)
Consider finding paths summing to 8 in a simple tree:
```
    5
   / \
  4   3
 /   /
1   2
 \
  -1
```
Target Sum = 8

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\small},
    pathtracker/.style={rectangle, draw, fill=yellow!20, font=\sffamily\tiny, align=left, text width=3.5cm},
    foundpath/.style={treenode, fill=green!30, double},
    level 1/.style={sibling distance=2.5cm},
    level 2/.style={sibling distance=1.5cm},
    level 3/.style={sibling distance=1cm}
]
\node[treenode] (n5) at (0,0) {5};
\node[treenode] (n4) at (-1.5,-1.5) {4};
\node[treenode] (n3) at (1.5,-1.5) {3};
\node[treenode] (n1) at (-2.25,-3) {1};
\node[treenode] (n2) at (0.75,-3) {2};
\node[treenode] (n_1) at (-2.25,-4.5) {-1};

\draw (n5) -- (n4); \draw (n5) -- (n3);
\draw (n4) -- (n1); \draw (n3) -- (n2);
\draw (n1) -- (n_1);

% Path tracking (conceptual)
\node[pathtracker] at (4,0) {
    Path: [5], Sum: 5, Rem: 3\\
    $\rightarrow$ Path: [5,4], Sum: 9, Rem: -1 (Too much)\\
    $\rightarrow$ Path: [5,4,1], Sum: 10, Rem: -2\\
    $\rightarrow$ Path: [5,4,1,-1], Sum: 9, Rem: -1 (Leaf, Sum!=8)
};
\node[pathtracker, fill=green!20] at (4,-3) {
    Backtrack to 5, try right child 3\\
    Path: [5,3], Sum: 8, Rem: 0\\
    $\rightarrow$ Path: [5,3,2], Sum: 10, Rem: -2 (Leaf, Sum!=8)
};
\node at (4,-4.5) {Actually, the sum is checked at leaf. For path [5,3], sum is 8. Is (3) a leaf? No.
Recurse for (2). Path [5,3,2], leaf. Sum is 10, not 8.
This example highlights that path sum is checked at leaves. If [5,3] was a path where node 3 was a leaf and sum = 8, then it would be a solution.
};

\end{tikzpicture}
```
The key is the "add to path -> recurse -> remove from path" sequence, which is characteristic of backtracking implemented with DFS.

## 总结 (Summary)
- "Traversal thinking" applied to path problems often involves maintaining the current path and updating it during DFS.
- Backtracking (undoing choices by removing elements from the path) is crucial after exploring a branch.
- This pattern is effective for problems like finding all paths with a certain sum, finding the path with max/min sum, or checking existence of specific paths.
- The logic for checking the condition (e.g., sum matches target) is usually done at leaf nodes or when a path segment is completed.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/02 - Tree Problems - Traversal Thinking Part 2|Next: Traversal Thinking Part 2]] |
