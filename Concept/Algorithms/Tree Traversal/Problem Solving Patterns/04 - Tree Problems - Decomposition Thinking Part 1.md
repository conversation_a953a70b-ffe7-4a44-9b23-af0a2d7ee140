---
tags: [concept/algorithms, concept/tree_traversal, concept/recursion, concept/divide_and_conquer, pattern/tree_problem, pattern/tree_construction, course/labuladong]
aliases: [Tree Decomposition Thinking Construction, Divide and Conquer Trees Construction, 二叉树分解问题思维应用一 (构造), Tree Construction from Traversal]
---
> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷二叉树算法/二叉树心法（构造篇）.md]].
> This note focuses on "decomposition thinking" ([[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]]) for tree construction problems.

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/03 - Tree Problems - Traversal Thinking Part 3|Prev: Traversal Thinking Part 3]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/05 - Tree Problems - Decomposition Thinking Part 2|Next: Decomposition Thinking Part 2]] |

# Tree Problems: Decomposition Thinking Part 1 - Tree Construction

"Decomposition thinking," also known as the divide and conquer approach for trees, is particularly powerful for problems involving the construction or reconstruction of binary trees. The core idea is to define what a recursive function should *return* for a given subproblem (e.g., "build and return the root of the subtree for this segment of an array/traversal sequence").

## 核心概念 (Core Concept)
The main principle is to identify how a larger tree can be constructed if its left and right subtrees are already constructed. The recursive function typically takes parameters defining the scope of the current subproblem (e.g., a sub-array or indices into pre-order/in-order traversals) and returns the root of the constructed subtree for that scope.

## Example 1: Construct Maximum Binary Tree (LC654)
[[Interview/Practice/LeetCode/LC654 - Maximum Binary Tree|LC654 - Maximum Binary Tree]]
Given an array of unique integers, construct a "maximum binary tree":
1. The root is the maximum element in the array.
2. The left subtree is the maximum binary tree constructed from the subarray to the left of the maximum element.
3. The right subtree is the maximum binary tree constructed from the subarray to the right of the maximum element.

**Decomposition Approach:**
Define `build(nums, lo, hi)`: constructs the maximum binary tree from `nums[lo...hi]` and returns its root.
1. Base Case: If `lo > hi`, return `None` (empty subarray).
2. Find Max: Find the maximum value `max_val` and its `index` in `nums[lo...hi]`.
3. Create Root: `root = TreeNode(max_val)`.
4. Recurse:
   - `root.left = build(nums, lo, index - 1)`
   - `root.right = build(nums, index + 1, hi)`
5. Return `root`.

```python
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None): self.val = val; self.left = left; self.right = right

class SolutionMaxBinaryTree:
    def constructMaximumBinaryTree(self, nums: list[int]) -> TreeNode | None:
        return self._build(nums, 0, len(nums) - 1)

    def _build(self, nums: list[int], lo: int, hi: int) -> TreeNode | None:
        if lo > hi:
            return None
        
        # Find the index of the maximum element in nums[lo..hi]
        max_val_idx = lo
        for i in range(lo + 1, hi + 1):
            if nums[i] > nums[max_val_idx]:
                max_val_idx = i
        
        root_val = nums[max_val_idx]
        root = TreeNode(root_val)
        
        # Recursively construct left and right subtrees
        root.left = self._build(nums, lo, max_val_idx - 1)
        root.right = self._build(nums, max_val_idx + 1, hi)
        
        return root
```
Visualizer: `div_maximum-binary-tree` (in Labuladong's article)

## Example 2: Construct Binary Tree from Preorder and Inorder Traversal (LC105)
[[Interview/Practice/LeetCode/LC105 - Construct Binary Tree from Preorder and Inorder Traversal|LC105 - Construct Binary Tree from Preorder and Inorder Traversal]]
Given `preorder = [3,9,20,15,7]` and `inorder = [9,3,15,20,7]`.

**Decomposition Approach:**
Define `build(pre_start, pre_end, in_start, in_end)`: constructs tree from `preorder[pre_start..pre_end]` and `inorder[in_start..in_end]`.
1. Base Case: If `pre_start > pre_end` (or `in_start > in_end`), return `None`.
2. Find Root: `root_val = preorder[pre_start]`.
3. Create Root: `root = TreeNode(root_val)`.
4. Partition Inorder: Find `root_val` in `inorder` at `in_root_idx`.
   - `left_subtree_size = in_root_idx - in_start`.
5. Recurse:
   - `root.left = build(pre_start + 1, pre_start + left_subtree_size, in_start, in_root_idx - 1)`
   - `root.right = build(pre_start + left_subtree_size + 1, pre_end, in_root_idx + 1, in_end)`
6. Return `root`.
(Use a hash map for $O(1)$ lookup of `in_root_idx` for optimization).

```python
class SolutionConstructFromPreIn:
    def buildTree(self, preorder: list[int], inorder: list[int]) -> TreeNode | None:
        self.inorder_map = {val: i for i, val in enumerate(inorder)}
        self.preorder_iter = iter(preorder) # Or pass preorder and manage indices
        
        # Optimized way using indices to avoid slicing arrays:
        # self.preorder_idx = 0 # If passing preorder array by reference
        # return self._build_optimized(preorder, 0, len(preorder) - 1, inorder, 0, len(inorder) - 1)

        # Labuladong's version usually uses index manipulation for subarrays
        return self._build_with_indices(preorder, 0, len(preorder) - 1,
                                      inorder, 0, len(inorder) - 1)

    def _build_with_indices(self, preorder, pre_start, pre_end,
                            inorder, in_start, in_end):
        if pre_start > pre_end:
            return None

        root_val = preorder[pre_start]
        root = TreeNode(root_val)

        in_root_idx = self.inorder_map[root_val]
        left_subtree_size = in_root_idx - in_start

        root.left = self._build_with_indices(
            preorder, pre_start + 1, pre_start + left_subtree_size,
            inorder, in_start, in_root_idx - 1
        )
        root.right = self._build_with_indices(
            preorder, pre_start + left_subtree_size + 1, pre_end,
            inorder, in_root_idx + 1, in_end
        )
        return root
```
Labuladong's visualization `![](/algo/images/binary-tree-ii/4.jpeg)` illustrates the partitioning.

## Example 3: Construct Binary Tree from Inorder and Postorder Traversal (LC106)
[[Interview/Practice/LeetCode/LC106 - Construct Binary Tree from Inorder and Postorder Traversal|LC106 - Construct Binary Tree from Inorder and Postorder Traversal]]
Similar to LC105, but the root is the *last* element of the `postorder` traversal.
`postorder = [9,15,7,20,3]`, `inorder = [9,3,15,20,7]`. Root is `3`.

**Decomposition Approach:**
Define `build(in_start, in_end, post_start, post_end)`
1. Base Case: If `post_start > post_end`, return `None`.
2. Find Root: `root_val = postorder[post_end]`.
3. Create Root & Partition Inorder as before (find `in_root_idx`).
4. `left_subtree_size = in_root_idx - in_start`.
5. Recurse:
   - `root.left = build(in_start, in_root_idx - 1, post_start, post_start + left_subtree_size - 1)`
   - `root.right = build(in_root_idx + 1, in_end, post_start + left_subtree_size, post_end - 1)`
6. Return `root`.

```python
class SolutionConstructFromInPost:
    def buildTree(self, inorder: list[int], postorder: list[int]) -> TreeNode | None:
        self.inorder_map = {val: i for i, val in enumerate(inorder)}
        # self.postorder_idx = len(postorder) - 1 # If popping from end of postorder
        return self._build_with_indices(inorder, 0, len(inorder) - 1,
                                      postorder, 0, len(postorder) - 1)

    def _build_with_indices(self, inorder, in_start, in_end,
                            postorder, post_start, post_end):
        if post_start > post_end: # or in_start > in_end
            return None

        root_val = postorder[post_end] # Root is last in postorder
        root = TreeNode(root_val)

        in_root_idx = self.inorder_map[root_val]
        left_subtree_size = in_root_idx - in_start

        root.left = self._build_with_indices(
            inorder, in_start, in_root_idx - 1,
            postorder, post_start, post_start + left_subtree_size - 1
        )
        root.right = self._build_with_indices(
            inorder, in_root_idx + 1, in_end,
            postorder, post_start + left_subtree_size, post_end - 1
        )
        return root
```
Labuladong's visualization `![](/algo/images/binary-tree-ii/6.jpeg)` shows this.

## Example 4: Construct Binary Tree from Preorder and Postorder Traversal (LC889)
[[Interview/Practice/LeetCode/LC889 - Construct Binary Tree from Preorder and Postorder Traversal|LC889 - Construct Binary Tree from Preorder and Postorder Traversal]]
This is trickier as Pre+Post does not uniquely define a tree. However, if a unique tree is guaranteed or any valid one is fine.
`preorder = [1,2,4,5,3,6,7]`, `postorder = [4,5,2,6,7,3,1]`. Root is `1`.

**Decomposition Approach:**
1. Root is `preorder[pre_start]`.
2. If `pre_start == pre_end` (single node), return `TreeNode(root_val)`.
3. Left child's root is `preorder[pre_start + 1]`. Let this be `left_root_val`.
4. Find `left_root_val` in `postorder` at `post_left_root_idx`.
5. `left_subtree_size = post_left_root_idx - post_start + 1`.
6. Recurse for left and right subtrees using calculated sizes to partition `preorder` and `postorder` arrays.

```python
class SolutionConstructFromPrePost:
    def constructFromPrePost(self, preorder: list[int], postorder: list[int]) -> TreeNode | None:
        self.postorder_map = {val: i for i, val in enumerate(postorder)}
        return self._build(preorder, 0, len(preorder) - 1,
                         postorder, 0, len(postorder) - 1)

    def _build(self, preorder, pre_start, pre_end,
               postorder, post_start, post_end):
        if pre_start > pre_end:
            return None
        
        root_val = preorder[pre_start]
        root = TreeNode(root_val)

        if pre_start == pre_end: # Single node tree
            return root
            
        # Left subtree's root value is the next element in preorder
        left_root_val = preorder[pre_start + 1]
        # Find this left_root_val in postorder to determine left subtree size
        post_left_root_idx = self.postorder_map[left_root_val]
        
        left_subtree_size = post_left_root_idx - post_start + 1
        
        root.left = self._build(
            preorder, pre_start + 1, pre_start + left_subtree_size,
            postorder, post_start, post_left_root_idx
        )
        root.right = self._build(
            preorder, pre_start + left_subtree_size + 1, pre_end,
            postorder, post_left_root_idx + 1, post_end - 1 # Exclude root from post_end
        )
        return root
```
Labuladong's visualization `![](/algo/images/binary-tree-ii/8.jpeg)` is key here.

## 总结 (Summary)
- "Decomposition thinking" is essential for tree construction problems. The recursive function is defined to build and return a subtree for a specific portion of the input data (e.g., segments of traversal arrays or a subarray).
- The core steps involve:
    1. Identifying the root of the current (sub)tree from the input.
    2. Creating the root node.
    3. Determining the scope of data for the left and right subtrees.
    4. Recursively calling the build function for left and right children.
    5. Linking the constructed left and right subtrees to the root.
- Using hash maps to store indices of values in inorder/postorder traversals optimizes finding partition points from $O(N)$ to $O(1)$ per recursive call.
- Passing array indices is more efficient than slicing arrays in each recursive call.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/03 - Tree Problems - Traversal Thinking Part 3|Prev: Traversal Thinking Part 3]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/05 - Tree Problems - Decomposition Thinking Part 2|Next: Decomposition Thinking Part 2]] |

