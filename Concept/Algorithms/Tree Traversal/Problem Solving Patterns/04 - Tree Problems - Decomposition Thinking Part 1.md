---
tags: [concept/algorithms, concept/tree_traversal, concept/recursion, concept/divide_and_conquer, pattern/tree_problem, course/labuladong]
aliases: [Tree Decomposition Thinking, Divide and Conquer Trees, 二叉树分解问题思维应用一, Tree Construction]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's framework for solving tree problems using "decomposition thinking" (divide and conquer), particularly for tree construction tasks.
> Original source context: [[{source_file_path}|【练习】用「分解问题」思维解题 I]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/03 - Tree Problems - Traversal Thinking Part 3|Prev: Traversal Thinking Part 3]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/05 - Tree Problems - Decomposition Thinking Part 2|Next: Decomposition Thinking Part 2]] |

# Tree Problems: Decomposition Thinking Part 1 - Tree Construction

"Decomposition thinking," also known as the divide and conquer approach for trees ([[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]]), is particularly powerful for problems involving the construction or reconstruction of binary trees. The core idea is to define what a recursive function should *return* for a given subproblem (e.g., "build and return the root of the subtree for this segment of an array").

## 核心概念 (Core Concept)
The main principle is to identify how a larger tree can be constructed if its left and right subtrees are already constructed. The recursive function typically takes parameters defining the scope of the current subproblem (e.g., a sub-array or indices into pre-order/in-order traversals) and returns the root of the constructed subtree for that scope.

## 通用模板 (General Template - Constructing from Preorder and Inorder Traversals)
A classic example is LeetCode 105. Construct Binary Tree from Preorder and Inorder Traversal.
Given `preorder = [3,9,20,15,7]` and `inorder = [9,3,15,20,7]`.

**Recursive Function Definition:** `build(preorder_sub, inorder_sub)` returns the root of the tree constructed from these sub-traversals.

1.  **Base Case:** If `preorder_sub` or `inorder_sub` is empty, return `None` (no tree to build).
2.  **Find Root:** The first element of `preorder_sub` is always the root of the current subtree. Let this be `root_val`.
3.  **Create Root Node:** `root_node = TreeNode(root_val)`.
4.  **Partition Inorder Traversal:** Find `root_val` in `inorder_sub`. This value divides `inorder_sub` into:
    *   `inorder_left_sub`: Elements to the left of `root_val` (all in the left subtree).
    *   `inorder_right_sub`: Elements to the right of `root_val` (all in the right subtree).
5.  **Partition Preorder Traversal:** Based on the sizes of `inorder_left_sub` and `inorder_right_sub`:
    *   `preorder_left_sub`: The next `len(inorder_left_sub)` elements in `preorder_sub` (after the initial `root_val`).
    *   `preorder_right_sub`: The remaining elements in `preorder_sub`.
6.  **Recursive Construction (Conquer & Combine):**
    *   `root_node.left = build(preorder_left_sub, inorder_left_sub)`
    *   `root_node.right = build(preorder_right_sub, inorder_right_sub)`
7.  **Return `root_node`**.

This logic naturally occurs in the **post-order position** of the conceptual `build` function's execution: the left and right children are fully constructed *before* they are assigned to the current `root_node`.

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionConstructFromPreIn:
    def buildTree(self, preorder: list[int], inorder: list[int]) -> TreeNode | None:
        if not preorder or not inorder:
            return None

        # Root is the first element in preorder traversal
        root_val = preorder[0]
        root = TreeNode(root_val)

        # Find root_val in inorder traversal to split into left/right subtrees
        # For efficiency, create a map of inorder values to their indices
        # This is usually done once at the beginning if passing indices instead of slices
        inorder_root_idx = -1
        for i in range(len(inorder)):
            if inorder[i] == root_val:
                inorder_root_idx = i
                break
        
        # Split inorder array
        inorder_left_subtree = inorder[0:inorder_root_idx]
        inorder_right_subtree = inorder[inorder_root_idx+1:]

        # Split preorder array
        # Left subtree in preorder will have len(inorder_left_subtree) elements
        # after the root element (preorder[0])
        num_left_nodes = len(inorder_left_subtree)
        preorder_left_subtree = preorder[1 : 1 + num_left_nodes]
        preorder_right_subtree = preorder[1 + num_left_nodes :]

        # Recursively build left and right subtrees
        root.left = self.buildTree(preorder_left_subtree, inorder_left_subtree)
        root.right = self.buildTree(preorder_right_subtree, inorder_right_subtree)
        
        return root

# Note: Passing slices like this creates new lists and can be inefficient (O(N^2) overall).
# A more optimized version passes indices (pointers) into the original arrays.
# See [[Interview/Practice/LeetCode/LC105 - Construct Binary Tree from Preorder and Inorder Traversal|LC105 solution]] for optimized approach.
```
The optimized approach using indices is a core part of Labuladong's teaching for these construction problems.

## 示例图示 (Diagram Example: Construction from Preorder & Inorder)
`preorder = [3,9,20,15,7]`, `inorder = [9,3,15,20,7]`

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\small},
    array_box/.style={rectangle, draw, fill=yellow!10, font=\sffamily\tiny, minimum height=0.5cm},
    call_box/.style={rectangle, draw, fill=blue!10, rounded corners, text width=4cm, align=center, font=\sffamily\scriptsize},
    level 1/.style={sibling distance=4cm, level distance=2cm},
    level 2/.style={sibling distance=2.5cm}
]

% Initial call
\node[call_box] (call_root) at (0,0) {
    `build([3,9,20,15,7], [9,3,15,20,7])`\\
    Root val = 3. Node(3) created.
};

% Left child call
\node[call_box] (call_left) at (-3,-3) {
    `build([9], [9])` (pre=[9], in=[9])\\
    Root val = 9. Node(9) created.\\
    Left/Right empty. Returns Node(9).
};

% Right child call
\node[call_box] (call_right) at (3,-3) {
    `build([20,15,7], [15,20,7])`\\
    Root val = 20. Node(20) created.
};

\draw[->] (call_root) -- (call_left) node[midway, above, sloped] {`root.left = ...`};
\draw[->] (call_root) -- (call_right) node[midway, above, sloped] {`root.right = ...`};

% Further decomposition of right child call
\node[call_box] (call_right_left) at (1,-6) {
    `build([15], [15])` (pre=[15], in=[15])\\
    Node(15) created & returned.
};
\node[call_box] (call_right_right) at (5,-6) {
    `build([7], [7])` (pre=[7], in=[7])\\
    Node(7) created & returned.
};
\draw[->] (call_right) -- (call_right_left) node[midway, above, sloped] {`Node(20).left = ...`};
\draw[->] (call_right) -- (call_right_right) node[midway, above, sloped] {`Node(20).right = ...`};


% Resulting tree structure (conceptual)
\node at (0,-8) {Resulting Tree:};
\node[treenode] (n3) at (0,-9) {3}
    child {node[treenode] (n9) {9}}
    child {node[treenode] (n20) {20}
        child {node[treenode] (n15) {15}}
        child {node[treenode] (n7) {7}}
    };
\end{tikzpicture}
```
The diagram shows the recursive calls. Each call processes its segment of preorder/inorder arrays, identifies its local root, and then delegates construction of left/right subtrees to further recursive calls. The assignment `root.left = ...` and `root.right = ...` happens after the recursive calls return, which is post-order logic.

## 总结 (Summary)
- "Decomposition thinking" is key for tree construction problems. Define a recursive function that builds and returns a subtree for a given part of the input (e.g., segments of traversal arrays).
- Identifying the root of the current subtree is usually the first step.
- The properties of different traversals (preorder, inorder, postorder) are used to partition the remaining input for recursive calls to build left and right subtrees.
- The actual linking of subtrees (`root.left = ..., root.right = ...`) happens in a post-order fashion relative to the processing of the current node's value.
- Optimizing by passing array indices instead of slicing arrays is crucial for achieving efficient $O(N)$ solutions for construction problems.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/03 - Tree Problems - Traversal Thinking Part 3|Prev: Traversal Thinking Part 3]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/05 - Tree Problems - Decomposition Thinking Part 2|Next: Decomposition Thinking Part 2]] |
