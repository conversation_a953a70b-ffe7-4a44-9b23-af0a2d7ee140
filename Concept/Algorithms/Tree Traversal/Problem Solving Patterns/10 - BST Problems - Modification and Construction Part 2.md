---
tags: [concept/algorithms, concept/tree_traversal, concept/bst, pattern/tree_problem, course/labuladong]
aliases: [BST Modification, BST Construction, 二叉搜索树特性应用二]
---
> [!NOTE] Source Annotation
> Content adapted from Labuladong's approach to solving Binary Search Tree (BST) problems, focusing on modification and construction tasks that preserve BST properties.
> Original source context: [[{source_file_path}|【练习】二叉搜索树经典例题 II]]

| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/09 - BST Problems - Properties and Iteration Part 1|Prev: BST Problems Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/11 - Tree Problems - Level-Order (BFS) Part 1|Next: Level-Order (BFS) Part 1]] |

# BST Problems: Modification & Construction (Part 2)

This part delves into Binary Search Tree (BST) problems that involve modifying an existing BST (like insertion or deletion) or constructing a BST from given data, while always maintaining the BST invariants. The core [[Interview/Concept/Data Structures/Tree/Binary Search Tree/00 - BST - Introduction and Operations|BST properties]] guide these operations.

## 核心概念 (Core Concept)
Operations that modify a BST (insert, delete) or construct one must ensure that for every node `n`, all values in `n.left` are less than `n.val`, and all values in `n.right` are greater than `n.val`. This often involves recursive "decomposition thinking."

## 通用模板 (General Template - Inserting into a BST LC701)
LeetCode 701. Insert into a Binary Search Tree is a good example. The goal is to insert a `val` into the BST and return the root of the modified BST.

**Recursive Function Definition:** `insertIntoBST(root, val)` returns the root of the (potentially modified) subtree after inserting `val`.

1.  **Base Case:** If `root` is `None`, the new `val` forms a new subtree. Return `TreeNode(val)`.
2.  **Recursive Step (Decomposition):**
    *   If `val < root.val`: The `val` belongs in the left subtree. Recursively call `insertIntoBST(root.left, val)`. The result of this call (the new root of the modified left subtree) becomes `root.left`.
    *   If `val > root.val`: The `val` belongs in the right subtree. Recursively call `insertIntoBST(root.right, val)`. The result becomes `root.right`.
    *   (If `val == root.val`, typically do nothing for BSTs if duplicates are not allowed, or handle as per problem spec. LC701 assumes unique values or doesn't require special handling for duplicates other than placing them.)
3.  **Return `root`** (the current node, possibly with a modified child pointer).

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

class SolutionInsertIntoBST:
    def insertIntoBST(self, root: TreeNode | None, val: int) -> TreeNode | None:
        if not root:
            return TreeNode(val) # Base case: found the spot, create new node

        if val < root.val:
            root.left = self.insertIntoBST(root.left, val)
        elif val > root.val:
            root.right = self.insertIntoBST(root.right, val)
        # If val == root.val, do nothing (standard BST behavior for duplicates often implies no insertion or specific handling)
        # For LC701, the problem statement implies val will be inserted if it's not already there following BST rules.
        
        return root # Return the (possibly modified) root of this subtree
```
### 模板解析 (Template Explanation)
- This is a prime example of "decomposition thinking." The function `insertIntoBST(node, val)` is defined to return the root of the subtree rooted at `node` *after* `val` has been inserted into it.
- The assignments `root.left = ...` and `root.right = ...` happen in a post-order fashion (after the recursive calls return), linking the modified subtrees back to the parent.

## 示例图示 (Diagram Example: Inserting 5 into BST `[4,2,7,1,3]`)
Original BST:
```
    4
   / \
  2   7
 / \
1   3
```
Call `insertIntoBST(root, 5)`:
1. `insertIntoBST(Node(4), 5)`: `5 > 4`, so `Node(4).right = insertIntoBST(Node(7), 5)`.
2. `insertIntoBST(Node(7), 5)`: `5 < 7`, so `Node(7).left = insertIntoBST(None, 5)`.
3. `insertIntoBST(None, 5)`: Base case, returns `TreeNode(5)`.
4. Back to call 2: `Node(7).left` becomes `Node(5)`. `insertIntoBST(Node(7), 5)` returns `Node(7)` (now with left child 5).
5. Back to call 1: `Node(4).right` becomes `Node(7)` (which now has Node(5) as left child). `insertIntoBST(Node(4), 5)` returns `Node(4)`.

Resulting BST:
```
    4
   / \
  2   7
 / \ /
1  3 5
```

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=7mm, font=\sffamily\small},
    newnode/.style={treenode, fill=green!30},
    callflow/.style={font=\sffamily\tiny, align=center, text width=4cm},
    level 1/.style={sibling distance=2.5cm},
    level 2/.style={sibling distance=1.5cm},
    level 3/.style={sibling distance=1cm}
]
% Initial Tree
\node[treenode] (n4_orig) at (-3,0) {4}
    child {node[treenode] (n2_orig) {2}
        child {node[treenode] (n1_orig) {1}}
        child {node[treenode] (n3_orig) {3}}
    }
    child {node[treenode] (n7_orig) {7}};
\node at (-3, -3) {Initial BST};

% Call Flow
\node[callflow, rectangle, draw, fill=yellow!10] (cf1) at (3, 1.5) {`insert(Node(4), 5)`\\`5>4` $\rightarrow$ go right};
\node[callflow, rectangle, draw, fill=yellow!10] (cf2) at (3, 0) {`insert(Node(7), 5)`\\`5<7` $\rightarrow$ go left};
\node[callflow, rectangle, draw, fill=yellow!10] (cf3) at (3, -1.5) {`insert(None, 5)`\\Returns `Node(5)`};

\draw[->, dashed] (cf1) -- (cf2);
\draw[->, dashed] (cf2) -- (cf3);
\draw[->, dashed, blue, bend left] (cf3.west) to node[midway,above,sloped] {`Node(7).left=Node(5)`} (cf2.west);
\draw[->, dashed, blue, bend left] (cf2.east) to node[midway,above,sloped] {`Node(4).right=Node(7)`} (cf1.east);


% Resulting Tree
\node[treenode] (n4_final) at (-3,-4.5) {4}
    child {node[treenode] (n2_final) {2}
        child {node[treenode] (n1_final) {1}}
        child {node[treenode] (n3_final) {3}}
    }
    child {node[treenode] (n7_final) {7}
        child {node[newnode] (n5_final) {5}} % Inserted node
    };
\node at (-3, -7.5) {BST after inserting 5};

\end{tikzpicture}
```

## 总结 (Summary)
- Modifying or constructing BSTs using decomposition thinking involves recursive functions that operate on subtrees and return the (potentially new) root of that modified/constructed subtree.
- BST properties guide the recursion: compare the target value with the current node's value to decide whether to go left or right.
- Base cases typically involve handling `None` nodes (e.g., finding an empty spot for insertion, or returning `None` for an empty sub-construction).
- The actual modification (like linking a new node or a reconstructed subtree) often happens in a post-order fashion after recursive calls return.

---
| [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/09 - BST Problems - Properties and Iteration Part 1|Prev: BST Problems Part 1]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/index|Back to Patterns Index]] | [[Interview/Concept/Algorithms/Tree Traversal/Problem Solving Patterns/11 - Tree Problems - Level-Order (BFS) Part 1|Next: Level-Order (BFS) Part 1]] |
