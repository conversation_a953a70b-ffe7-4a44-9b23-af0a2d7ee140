---
tags: [concept/algorithms, concept/string_manipulation, concept/stack, pattern/monotonic_stack, course/labuladong]
aliases: [Remove Duplicate Letters, Smallest Subsequence of Distinct Characters, 单调栈去重]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/拓展：数组去重问题（困难版）.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/拓展：数组去重问题（困难版）.md|拓展：数组去重问题（困难版） by Labuladong]].
> This note explains using a monotonic stack to find the lexicographically smallest subsequence of distinct characters while maintaining relative order.

# Remove Duplicate Letters / Smallest Subsequence (Monotonic Stack)

The problem of removing duplicate letters from a string to form the lexicographically smallest subsequence, while maintaining the relative order of the remaining characters, is a challenging task. This is tackled by [[Interview/Practice/LeetCode/LC316 - Remove Duplicate Letters|LC316 - Remove Duplicate Letters]] and its identical counterpart [[Interview/Practice/LeetCode/LC1081 - Smallest Subsequence of Distinct Characters|LC1081 - Smallest Subsequence of Distinct Characters]]. The core idea involves using a data structure similar to a [[Interview/Concept/Data Structures/Stack/Applications/Monotonic Stack Pattern|Monotonic Stack]].

## 🎯 Problem Requirements Recap

1.  **Distinct Characters:** Each character in the result must be unique.
2.  **Relative Order:** The relative order of characters from the original string must be preserved.
3.  **Lexicographically Smallest:** Among all valid subsequences satisfying 1 and 2, choose the one that is smallest alphabetically.

Example: `s = "bcabc"` $\rightarrow$ Result `"abc"`.
(`"bac"` is also valid for 1&2, but `"abc"` is smaller).

## 💡 Core Idea: Monotonic Stack with Lookahead

We process the input string character by character and build our result subsequence using a stack-like structure (let's call it `result_stack`).

For each character `c` in the input string `s`:
1.  **If `c` is already in `result_stack`:** We've already included this character optimally (or will skip it now if current `c` offers no better placement). Skip this `c`.
2.  **If `c` is NOT in `result_stack`:**
    This `c` must be included. To ensure lexicographical smallness, we check if `c` is smaller than the `top` of `result_stack`.
    -   **While `result_stack` is not empty, `c < result_stack.top()`, AND `result_stack.top()` appears again later in `s`:**
        -   Pop from `result_stack`. (This is the greedy choice: remove a larger character from the current subsequence if it's safe to re-add it later, to make way for the current smaller `c`).
        -   Mark the popped character as "not in stack".
    -   Push `c` onto `result_stack`.
    -   Mark `c` as "in stack".

**Data Structures Needed:**
-   `result_stack`: A list or deque used as a stack to build the result.
-   `in_stack_flags`: A boolean array (e.g., size 26 for lowercase English) to track if a character is currently in `result_stack` ($O(1)$ check).
-   `char_counts` (or `last_occurrence_index`): An array to store the frequency of each character in the input string `s`. This is used to determine if `result_stack.top()` can be safely popped (i.e., if it appears later). After processing a character `c` from `s`, decrement its count.

## 🛠️ Algorithm Steps

1.  Initialize `result_stack = []`.
2.  Initialize `in_stack_flags = [False] * 26`.
3.  Calculate `char_counts` for all characters in `s`.
4.  Iterate through each character `c` in `s`:
    a.  Decrement `char_counts[c]`.
    b.  If `in_stack_flags[c]` is `True` (char `c` already optimally in stack), `continue`.
    c.  While `result_stack` is not empty, `c < result_stack[-1]`, and `char_counts[result_stack[-1]] > 0` (meaning `stack.top()` will appear again):
        i.  `popped_char = result_stack.pop()`.
        ii. `in_stack_flags[popped_char] = False`.
    d.  `result_stack.append(c)`.
    e.  `in_stack_flags[c] = True`.
5.  Join characters in `result_stack` to form the final string.

## Visualization (Example: `s = "cbacdcbc"`)

`char_counts = {'c':4, 'b':2, 'a':1, 'd':1}`

| Char `c` | `char_counts` (after dec) | `in_stack[c]`? | Stack Condition (`c < top && count[top]>0`) | `result_stack` | `in_stack` updated |
| :------: | :-----------------------: | :------------: | :----------------------------------------: | :------------: | :----------------: |
| **c**    | `c:3`                     | No             | Stack empty                                | `[c]`          | `in_stack[c]=T`    |
| **b**    | `b:1`                     | No             | `b < c`, `count[c]=3>0`. Pop `c`. `in_stack[c]=F`. Stack empty. | `[b]`          | `in_stack[b]=T`    |
| **a**    | `a:0`                     | No             | `a < b`, `count[b]=1>0`. Pop `b`. `in_stack[b]=F`. Stack empty. | `[a]`          | `in_stack[a]=T`    |
| **c**    | `c:2`                     | No             | Stack: `[a]`. `c > a`.                     | `[a,c]`        | `in_stack[c]=T`    |
| **d**    | `d:0`                     | No             | Stack: `[a,c]`. `d > c`.                   | `[a,c,d]`      | `in_stack[d]=T`    |
| **c**    | `c:1`                     | `in_stack[c]=T` (is 'c' in `[a,c,d]`? Yes). Skip. | -                                          | `[a,c,d]`      | -                  |
| **b**    | `b:0`                     | No             | Stack: `[a,c,d]`. `b < d`, `count[d]=0`. Condition false (`count[d]` not >0). | `[a,c,d,b]`    | `in_stack[b]=T`    |
| **c**    | `c:0`                     | `in_stack[c]=T` (is 'c' in `[a,c,d,b]`? Yes). Skip. | -                                          | `[a,c,d,b]`    | -                  |

Result: `"".join(["a","c","d","b"])` = `"acdb"`

```tikz
\begin{tikzpicture}[
    char_box/.style={rectangle, draw, minimum size=0.7cm, font=\sffamily\small, fill=blue!10, align=center},
    stack_box/.style={rectangle, draw, minimum width=3cm, minimum height=0.8cm, label=above:Stack, align=center, font=\sffamily\small},
    step_label/.style={font=\sffamily\bfseries\small, align=left, text width=7cm}
]

\matrix[column sep=0.5cm, row sep=0.7cm] {
    % Headers
    \node[align=center, text width=1.5cm]{\textbf{Char}}; & \node[align=center, text width=2cm]{\textbf{Stack Op}}; & \node[stack_box, minimum height=1.5cm, label={[yshift=0.1cm]above:Stack (Bottom->Top)}] {}; & \node[align=center, text width=3cm]{\textbf{Counts (Relevant)}}; \\

    % s = "cbacdcbc"
    % Initial counts: c:4, b:2, a:1, d:1
    
    \node[char_box]{c}; & \node{Push c}; & \node{[c]}; & \node{c:3, b:2, a:1, d:1}; \\
    
    \node[char_box]{b}; & \node{Pop c (b<c, c left)\newline Push b}; & \node{[b]}; & \node{c:3, b:1, a:1, d:1}; \\
    
    \node[char_box]{a}; & \node{Pop b (a<b, b left)\newline Push a}; & \node{[a]}; & \node{c:3, b:1, a:0, d:1}; \\
    
    \node[char_box]{c}; & \node{Push c (c>a)}; & \node{[a, c]}; & \node{c:2, b:1, a:0, d:1}; \\
    
    \node[char_box]{d}; & \node{Push d (d>c)}; & \node{[a, c, d]}; & \node{c:2, b:1, a:0, d:0}; \\
    
    \node[char_box]{c}; & \node{Skip (c in stack)}; & \node{[a, c, d]}; & \node{c:1, b:1, a:0, d:0}; \\
    
    \node[char_box]{b}; & \node{Push b (b<d, but d count=0)}; & \node{[a, c, d, b]}; & \node{c:1, b:0, a:0, d:0}; \\

    \node[char_box]{c}; & \node{Skip (c in stack)}; & \node{[a, c, d, b]}; & \node{c:0, b:0, a:0, d:0}; \\
};
\node at (2.5, -11.5) {\textbf{Final Stack: [a, c, d, b] $\rightarrow$ Result: "acdb"}};
\end{tikzpicture}
```

## Complexity Analysis
-   **Time Complexity:** $O(N)$, where $N$ is the length of the string `s`. Each character is pushed onto and popped from the stack at most once. Operations like checking `in_stack` and `char_counts` are $O(1)$ (assuming alphabet size is constant, e.g., 26).
-   **Space Complexity:** $O(K)$ or $O(1)$ where $K$ is alphabet size (e.g., 26). The stack stores at most $K$ distinct characters. `in_stack_flags` and `char_counts` also take $O(K)$ space.

## 总结 (Summary)
- The problem of finding the lexicographically smallest subsequence with unique characters while maintaining relative order can be solved efficiently using a monotonic stack-like approach.
- Key elements:
    - A stack to build the result.
    - A way to track if a character is already in the stack (`in_stack_flags`).
    - A way to know if a character to be popped from the stack will appear again later (`char_counts`).
- The greedy strategy is to pop larger characters from the stack if a smaller character arrives and the popped character can be re-added later.

---
Parent: [[Interview/Concept/Algorithms/Stack Applications/index|Stack Applications]]
Related Problems: [[Interview/Practice/LeetCode/LC316 - Remove Duplicate Letters|LC316]], [[Interview/Practice/LeetCode/LC1081 - Smallest Subsequence of Distinct Characters|LC1081]]
Related Concepts: [[Interview/Concept/Data Structures/Stack/Applications/Monotonic Stack Pattern|Monotonic Stack]]
