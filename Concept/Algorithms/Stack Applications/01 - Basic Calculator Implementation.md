---
tags: [concept/algorithms, concept/stack, pattern/expression_evaluation, pattern/recursion, course/labuladong]
aliases: [Calculator Implementation, Expression Parsing, Shunting-Yard like, 计算器实现]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/拓展：如何实现一个计算器.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/拓展：如何实现一个计算器.md|拓展：如何实现一个计算器 by Labuladong]].
> This note explains the progressive approach to building a calculator that handles `+ - * /` and parentheses.

# Basic Calculator Implementation Framework

Implementing a calculator that can parse and evaluate arithmetic expressions involving numbers, operators (`+`, `-`, `*`, `/`), and parentheses is a common interview problem. Labuladong presents a step-by-step approach, gradually adding complexity. The core idea often involves using stacks or recursion.

This framework covers variations found in:
- [[Interview/Practice/LeetCode/LC224 - Basic Calculator|LC224 - Basic Calculator]] (`+`, `-`, `(`, `)`)
- [[Interview/Practice/LeetCode/LC227 - Basic Calculator II|LC227 - Basic Calculator II]] (`+`, `-`, `*`, `/`, no parentheses)
- [[Interview/Practice/LeetCode/LC772 - Basic Calculator III|LC772 - Basic Calculator III]] (all: `+`, `-`, `*`, `/`, `(`, `)`)

## 🧱 Foundational Steps (Labuladong's Approach)

### 1. String to Integer
Converting a numeric string segment to an integer.
```python
# s = "458"
# n = 0
# for char_digit in s:
#     n = 10 * n + (ord(char_digit) - ord('0'))
```
(Careful with `(c - '0')` to avoid overflow with `10 * n + c - '0'` if `c` is large, but Python handles large integers.)

### 2. Handling `+` and `-` Only (No Spaces, No Parentheses)
- Example: `s = "1-12+3"`
- Strategy:
    - Iterate through the string, parsing numbers.
    - Keep track of the `sign` preceding the current number (`+` by default for the first number).
    - When a new operator is met (or end of string), push the `current_number` (with its `sign`) onto a stack.
    - Sum all numbers in the stack.
- Visualization: `![](/algo/images/calculator/1.jpg)` and `![](/algo/images/calculator/2.jpg)` from the source.

```python
# Conceptual for + and -
# def calculate_plus_minus(s: str) -> int:
#     stack = []
#     num = 0
#     sign = '+' # Sign for the *upcoming* number
#     s_appended = s + "+" # Add a sentinel operator to process the last number
#
#     for char_val in s_appended:
#         if char_val.isdigit():
#             num = num * 10 + int(char_val)
#         elif char_val in "+-": # Found an operator
#             if sign == '+':
#                 stack.append(num)
#             elif sign == '-':
#                 stack.append(-num)
#             sign = char_val # Update sign for next number
#             num = 0        # Reset num
#     return sum(stack)
```

### 3. Adding `*` and `/` (No Parentheses, Handle Spaces)
- Example: `s = " 2-3 * 4 +5/ 2 "`
- Strategy (Extends step 2):
    - `sign` now tracks the operator for the number being parsed.
    - When an operator (or end of string) is encountered:
        - If `sign == '+'`: `stack.push(num)`
        - If `sign == '-'`: `stack.push(-num)`
        - If `sign == '*'` : `stack.push(stack.pop() * num)`
        - If `sign == '/'`: `stack.push(int(stack.pop() / num))` (integer division, truncate towards zero)
    - Spaces are ignored when parsing numbers or identifying operators.
- Visualization: `![](/algo/images/calculator/3.jpg)`
```python
# Conceptual for + - * /
# def calculate_all_ops_no_paren(s: str) -> int:
#     stack = []
#     num = 0
#     sign = '+'
#     
#     for i, char_val in enumerate(s):
#         if char_val.isdigit():
#             num = num * 10 + int(char_val)
#         
#         # An operator is found OR it's the last character
#         if (not char_val.isdigit() and char_val != ' ') or i == len(s) - 1:
#             if sign == '+':
#                 stack.append(num)
#             elif sign == '-':
#                 stack.append(-num)
#             elif sign == '*':
#                 stack.append(stack.pop() * num)
#             elif sign == '/':
#                 # Integer division, truncate towards zero
#                 stack.append(int(stack.pop() / num)) 
#             
#             sign = char_val # Update sign for next operation
#             num = 0        # Reset num
#             
#     return sum(stack)
```
This approach correctly handles precedence because `*` and `/` operate immediately on the stack top, while `+` and `-` defer their operation until later summation.

### 4. Handling Parentheses (Recursion)
- Example: `s = "3 * (2 - 6 / (3 - 7))"`
- Strategy: When a `(` is encountered, recursively call the `calculate` function on the substring enclosed by the matching `)`. The result of this recursive call is treated as a single number by the outer call.
- A [[Interview/Concept/Data Structures/Stack/Applications/Valid Parentheses Pattern|stack can be used to find matching parentheses]] or a counter `balance` for parentheses.

**Unified Recursive Approach for `+ - * / ( )` (LC772):**
The `calculate` function takes a string (or an iterator/queue of tokens) as input.
```python
# Conceptual for full calculator (LC772 like)
# (This Python version simplifies Labuladong's Java queue-based recursion)

class SolutionFullCalculator:
    def calculate(self, s: str) -> int:
        
        def helper(tokens: list) -> int: # tokens is a list of strings (numbers or operators)
            stack = []
            num = 0
            sign = '+' # Current operator
            
            while tokens:
                token = tokens.pop(0) # Process tokens one by one

                if token.isdigit() or (token.startswith('-') and token[1:].isdigit() and sign in '+-('): # Handle negative numbers if they are at start of sub-expression
                    num = int(token)
                elif token == '(':
                    # Recursive call for sub-expression
                    num = helper(tokens) # helper consumes tokens until matching ')'
                
                # Processing logic based on 'sign' when an operator or ')' or end of tokens is met
                # This is where the actual operation happens
                # The previous num is processed with previous sign
                if token in "+-*/)" or not tokens: # Operator, or closing paren, or end of expression
                    if sign == '+':
                        stack.append(num)
                    elif sign == '-':
                        stack.append(-num)
                    elif sign == '*':
                        stack.append(stack.pop() * num)
                    elif sign == '/':
                        stack.append(int(stack.pop() / num))
                    
                    sign = token # New operator
                    num = 0      # Reset num
                    
                    if token == ')': # End of sub-expression
                        break 
            
            return sum(stack)

        # Preprocess string s to handle multi-digit numbers, spaces, and create a token list
        # e.g., " (1 + 21 ) * 3" -> ['(', '1', '+', '21', ')', '*', '3']
        # This preprocessing is non-trivial. Labuladong's Java uses a queue.
        # A simpler way for Python might be to iterate with an index or use regex.
        
        # Simplified tokenization for the helper function:
        # Pass s to helper, and helper manages an index.
        self.idx = 0
        
        def parse_and_calc(s_str: str) -> int:
            stack = []
            num = 0
            current_sign = '+'

            while self.idx < len(s_str):
                char = s_str[self.idx]
                self.idx += 1

                if char.isdigit():
                    num = num * 10 + int(char)
                
                if char == '(':
                    num = parse_and_calc(s_str) # Recursive call

                # Condition to process the number with its sign
                if (not char.isdigit() and char != ' ') or self.idx == len(s_str):
                    if current_sign == '+':
                        stack.append(num)
                    elif current_sign == '-':
                        stack.append(-num)
                    elif current_sign == '*':
                        stack[-1] *= num # Modify stack top
                    elif current_sign == '/':
                        stack[-1] = int(stack[-1] / num) # Modify stack top
                    
                    current_sign = char # Update sign for next number
                    num = 0 # Reset num
                
                if char == ')': # End of this recursive call's scope
                    break
            
            return sum(stack)

        # Add a sentinel character to ensure last number is processed if expression doesn't end with ')'
        return parse_and_calc(s + '\0') # '\0' or any non-digit/non-space sentinel
```
Labuladong's provided Java code uses a `LinkedList<Character>` as a queue of characters for the recursive helper to consume. The Python adaptation above tries to capture the spirit. The main challenge is managing the shared index `self.idx` across recursive calls or properly segmenting the string/token stream for each recursive call.

## Complexity
- **Without Parentheses (LC227):**
    - Time: $O(N)$ - one pass through the string.
    - Space: $O(N)$ - for the stack in the worst case (e.g., "1+2+3+4").
- **With Parentheses (LC224, LC772):**
    - Time: $O(N)$ - each character is processed a constant number of times across all recursive calls.
    - Space: $O(N)$ - for recursion stack depth (e.g., "(((...)))") and operand stack.

## 总结 (Summary)
- Implementing a calculator involves careful parsing of numbers and handling operator precedence.
- A stack-based approach is common:
    - For expressions without parentheses, iterate and process numbers based on the *previous* operator. `*` and `/` take immediate precedence by operating on the stack top.
    - For expressions with parentheses, recursion is a natural way: a `(` triggers a recursive call to evaluate the sub-expression.
- Handling spaces, multi-digit numbers, and operator signs (+/- for numbers vs. operations) are key details.

---
Parent: [[Interview/Concept/Algorithms/Stack Applications/index|Stack Applications]]
Related Problems: [[Interview/Practice/LeetCode/LC224 - Basic Calculator|LC224]], [[Interview/Practice/LeetCode/LC227 - Basic Calculator II|LC227]], [[Interview/Practice/LeetCode/LC772 - Basic Calculator III|LC772]]
