---
tags: [concept/algorithms, concept/data_structures, type/problem_solving_framework, meta/learning_philosophy]
aliases: [Algorithm Framework Thinking, Data Structure Core Ideas, Labuladong Framework, 学习数据结构和算法的框架思维]
---

> [!NOTE] Source Annotation
> This note synthesizes core ideas from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/学习数据结构和算法的框架思维.md]].
> It provides a high-level perspective on understanding and mastering data structures and algorithms.

# Framework Thinking for Data Structures and Algorithms (Labuladong)

Labuladong emphasizes a "framework thinking" approach to learning data structures and algorithms, which involves understanding their fundamental building blocks and common operational patterns. This allows for a more structured and efficient way to tackle a wide range of problems.

## 🧱 Data Structures: Based on Arrays and Linked Lists

**Core Idea:** All common data structures are essentially variations or combinations of two fundamental storage mechanisms:
1.  **Arrays (Sequential Storage):** Elements are stored in contiguous memory locations.
    - **Pros:** $O(1)$ random access (by index), space-efficient for elements themselves.
    - **Cons:** Fixed size (requiring $O(N)$ resizing for [[Interview/Concept/Data Structures/Array/01 - Dynamic Array - Principles and Amortized Analysis|dynamic arrays]]), $O(N)$ for insertions/deletions in the middle.
    - Examples derived: [[Interview/Concept/Data Structures/Stack/index|Stacks]] (array-based), [[Interview/Concept/Data Structures/Queue/index|Queues]] (array-based, often [[Interview/Concept/Data Structures/Array/02 - Circular Array (Ring Buffer)|circular arrays]]), [[Interview/Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization|Heaps]] (array representation of complete binary tree), [[Interview/Concept/Data Structures/Hash Map/00 - Hash Map - Core Principles|Hash Maps]] (underlying array for buckets).
2.  **Linked Lists (Linked Storage):** Elements store data and pointers to next (and possibly previous) elements.
    - **Pros:** Flexible size, $O(1)$ insertion/deletion if node reference is known (and doubly linked for previous).
    - **Cons:** $O(N)$ for access by index (requires traversal), extra space for pointers.
    - Examples derived: Stacks (linked-list based), Queues (linked-list based), [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Trees]] (nodes with child pointers), [[Interview/Concept/Data Structures/Graph/01 - Graph - Representation (Adjacency List, Adjacency Matrix)|Graphs]] (adjacency lists).

Understanding this underlying duality helps in choosing the right base for custom data structures or analyzing trade-offs of existing ones.

## ⚙️ Data Structure Operations: Traversal and Access (CRUD)

**Core Idea:** The fundamental operations on any data structure are **Create, Read, Update, Delete (CRUD)**, which boil down to **traversal** (visiting elements) and **access** (interacting with specific elements).

Traversal patterns:
-   **Linear Iteration:** `for` loops, `while` loops (common for arrays, lists).
    ```python
    # Array traversal
    # for item in array: process(item)
    # Linked list traversal
    # curr = head; while curr: process(curr.val); curr = curr.next
    ```
-   **Non-linear Recursion:** Common for tree and graph structures.
    - [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Binary Tree DFS (Pre-order, In-order, Post-order)]]
    - [[Interview/Concept/Algorithms/Tree Traversal/02 - N-ary Tree Recursive Traversal (DFS)|N-ary Tree DFS]]
    - [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|Graph DFS]] (often with a `visited` set)

These traversal frameworks are the skeletons upon which specific algorithmic logic is built.

## 💡 Algorithms: Refined Exhaustive Search (穷举)

**Core Idea:** Many algorithms, at their heart, are about **exhaustive search (穷举)** – systematically exploring all possible solutions or states. The "art" of algorithm design lies in making this search efficient.

**Two Key Challenges in Exhaustive Search:**
1.  **No Omissions (无遗漏):** Ensure all valid possibilities are considered. This requires a robust traversal or generation framework (e.g., correct [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|backtracking framework]] for permutations).
2.  **No Redundancy (无冗余):** Avoid re-computing the same information or exploring identical states multiple times. This is where optimizations like memoization in [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming]], pruning in backtracking, or using appropriate data structures (like sets for visited states in BFS/DFS) come in.

**Thinking Dimensions for Algorithm Problems:**
1.  **How to exhaustively search?** (Define the search space, states, transitions).
2.  **How to search *smartly*?** (Identify and eliminate redundant computations, prune unpromising branches).

**Examples:**
-   **[[Interview/Concept/Algorithms/Backtracking/index|Backtracking]] (e.g., Permutations, Combinations, Subsets):** The difficulty is often in correctly formulating the recursive structure to explore all possibilities without missing any or generating duplicates. This is "how to exhaustively search."
-   **[[Interview/Concept/Algorithms/Dynamic Programming/index|Dynamic Programming]] (e.g., Fibonacci, Coin Change):** The initial brute-force recursive solution exhaustively explores subproblems. The "smart" part is adding memoization or using tabulation to avoid recomputing overlapping subproblems. The primary challenge is often finding the state transition equation (the "how to exhaustively search" part via recursion).
-   **[[Interview/Concept/Algorithms/Two Pointers/index|Two Pointers]], [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window]], [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary Search]]:** These are "smart search" techniques for array/list problems, often improving upon a naive $O(N^2)$ or $O(N)$ full scan.
-   **[[Interview/Concept/Algorithms/Greedy Algorithms/00 - Greedy Algorithm - Core Framework|Greedy Algorithms]]:** A special case of "smart search" where a specific local choice property allows us to avoid exploring many branches, directly leading to an optimum.

## 🌲 Two Thinking Modes for Tree Problems (and beyond)

Labuladong particularly emphasizes two modes of recursive thinking for tree-like problems, which also extend to general recursion and [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming]] / [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking]]:

1.  **"Traversal" Thinking (遍历思维):**
    - Focus: Iterating through the structure (e.g., nodes of a tree, states in a search space).
    - Mechanism: A recursive function (often `void` or returning `None`) that explores. Results are accumulated in external variables or via side effects.
    - Analogy: A "visitor" pattern. The function `traverse(node, state_params...)` carries information along.
    - Examples: [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Standard tree traversals (pre/in/post)]] for collecting node values, [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|backtracking algorithms]].
    - This mode is about "what to do at each node/state."

2.  **"Decomposition" Thinking (分解问题思维 / Divide and Conquer):**
    - Focus: Defining what the recursive function computes and *returns* for a given subproblem. The solution for a larger problem is built from solutions of smaller subproblems.
    - Mechanism: A recursive function that `returns` a meaningful result for the subproblem it solves.
    - Analogy: [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer]]. The function `solve(sub_problem_input)` returns the solution for that input.
    - Examples: Calculating tree height (`maxDepth(node) = 1 + max(maxDepth(left), maxDepth(right))`), many [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming]] solutions.
    - This mode is about "how to combine results from subproblems."

These two modes are further detailed in [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursion - One Perspective, Two Thinking Modes]] and applied extensively in [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree - Core Algorithmic Principles]].

## 总结 (Summary)
- Master data structures by understanding their array/linked-list foundations and traversal/access patterns.
- Approach algorithms by first considering how to exhaustively search, then how to optimize that search by eliminating redundancy and leveraging problem-specific information.
- For recursive problems (especially on trees or implicit graphs), employ "traversal" thinking or "decomposition" thinking to structure your solution.
- This framework-based approach aims to make algorithm design more systematic and less about "aha!" moments for every new problem.

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
Related: [[Interview/Concept/Algorithms/Recursion/00 - Recursion - One Perspective Two Thinking Modes|Recursion Thinking Modes]], [[Interview/Concept/Algorithms/Tree Traversal/05 - Binary Tree - Core Algorithmic Principles|Binary Tree Core Algorithmic Principles]], [[Interview/Concept/Algorithms/Dynamic Programming/00 - Dynamic Programming - Introduction and Framework|Dynamic Programming Framework]], [[Interview/Concept/Algorithms/Backtracking/00 - Backtracking - Core Framework|Backtracking Framework]]
