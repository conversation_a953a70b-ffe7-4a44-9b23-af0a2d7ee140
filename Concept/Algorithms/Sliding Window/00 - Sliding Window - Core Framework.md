---
tags: [concept/algorithms, pattern/two_pointers, pattern/sliding_window, type/framework, course/labuladong]
aliases: [Sliding Window Algorithm, 滑动窗口算法, 滑动窗口框架]
summary: |
  This note outlines the general framework for solving problems using the Sliding Window technique, 
  which is efficient for sub-array/sub-string problems. It typically achieves O(N) time complexity.
  Key aspects include managing window boundaries (left, right), updating window state, 
  and defining conditions for window expansion and contraction.
created: 2025-05-25T22:48:40.673-07:00
modified: 2025-05-26T18:00:00.000-07:00
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/滑动窗口算法核心代码模板.md|滑动窗口算法核心代码模板]].
> This note outlines the general framework for solving problems using the Sliding Window technique.

# Sliding Window: Core Framework

The Sliding Window technique is an algorithmic pattern often used for problems involving finding a sub-array or sub-string that satisfies certain conditions (e.g., longest, shortest, contains specific elements). It typically uses two pointers, `left` and `right`, to define a "window" `s[left...right)` (inclusive `left`, exclusive `right`) over the sequence. This approach can often reduce time complexity from $O(N^2)$ for brute-force approaches to $O(N)$.

## 🎯 Core Idea

The window `[left, right)` slides through the data:
1.  The `right` pointer expands the window by moving to the right, incorporating new elements.
2.  The `left` pointer shrinks the window from the left when a certain condition is met or violated, removing elements.
This process efficiently explores contiguous sub-sequences.

> [!TIP] Why $O(N)$ Complexity?
> Although the typical implementation involves a `while` loop for `right` and an inner `while` loop for `left`, each element of the input sequence `s` is processed by `right` at most once (when it enters the window) and by `left` at most once (when it leaves the window). Thus, the total number of pointer movements is at most $2N$, leading to $O(N)$ time complexity for the pointer operations. Operations on data structures inside the loops (like hash map updates) are usually average $O(1)$ per element.

## 🛠️ Generic Sliding Window Algorithm Pseudocode (Python)

```python
import collections

def sliding_window_template(s: str, t: str = None): # t is optional, for problems like minWindow
    # 1. Initialization of window data structures
    # For problems involving character counts (e.g., Min Window Substring, Permutation in String)
    needs = collections.Counter(t) if t else collections.defaultdict(int) # Characters and counts needed from t
    window_chars = collections.defaultdict(int) # Characters and counts in the current window s[left:right)

    left, right = 0, 0 # Window boundaries [left, right)

    # `valid_chars_count` tracks how many character types from `needs` 
    # have their counts fully satisfied in `window_chars`.
    # This is crucial for problems where specific character frequencies must be met.
    valid_chars_count = 0 

    # Result variables (example for min window substring)
    min_len = float('inf')
    result_start_index = 0
    # For other problems, this might be max_length, a list of indices, etc.
    # e.g. res_list = [] for Find All Anagrams
    # e.g. max_substring_len = 0 for Longest Substring Without Repeats

    # 2. Main Loop: Expand window with `right` pointer
    while right < len(s):
        char_in = s[right] # Character entering the window
        right += 1         # Expand window: right pointer moves, window becomes [left, right)
                           # The new character s[right-1] (which is char_in) is now in the window.

        # --- Part A: Update window data based on char_in ---
        # This logic is specific to the problem.
        # Example for problems with 'needs' map (like Min Window, Permutation in String):
        if t and char_in in needs:
            window_chars[char_in] += 1
            if window_chars[char_in] == needs[char_in]:
                valid_chars_count += 1
        # Example for problems like Longest Substring Without Repeating Chars:
        # window_chars[char_in] += 1 (if window_chars just counts all chars in window)
        # ---------------------------------------------------

        # Debug print (optional, remove for submission)
        # print(f"Window s[{left}:{right}]='{s[left:right]}', window_chars={dict(window_chars)}, valid={valid_chars_count if t else 'N/A'}")

        # 3. Shrink Window: While window meets the condition to shrink
        # The condition `window_needs_shrink` is problem-specific.
        while window_needs_shrink(s, t, left, right, window_chars, needs, valid_chars_count):
            # --- Part B: Update result (if applicable) BEFORE shrinking ---
            # For "minimum" type problems (e.g., Min Window Substring LC76), 
            # the window s[left:right] is currently valid and a candidate for the minimum.
            # Update result if this window is better than current best.
            # Example for Min Window Substring:
            # if t and (right - left) < min_len:
            #    min_len = (right - left)
            #    result_start_index = left
            # For "existence" problems (e.g., Permutation in String LC567), 
            # if condition is met here, might return True.
            # Example for Permutation in String:
            # if t and valid_chars_count == len(needs) and (right - left) == len(t): return True
            # For "all occurrences" (e.g. Find All Anagrams LC438):
            # if t and valid_chars_count == len(needs) and (right - left) == len(t): res_list.append(left)
            # -----------------------------------------------------------------

            char_out = s[left] # Character leaving the window
            left += 1          # Shrink window: left pointer moves

            # --- Part C: Update window data based on char_out ---
            # This logic is also specific to the problem and often mirrors Part A.
            # Example for problems with 'needs' map:
            if t and char_out in needs:
                if window_chars[char_out] == needs[char_out]:
                    valid_chars_count -= 1
                window_chars[char_out] -= 1
            # Example for problems like Longest Substring Without Repeating Chars:
            # window_chars[char_out] -= 1
            # if window_chars[char_out] == 0: del window_chars[char_out]
            # ----------------------------------------------------
            
            # Debug print (optional)
            # print(f"Shrunk to s[{left}:{right}]='{s[left:right]}', window_chars={dict(window_chars)}, valid={valid_chars_count if t else 'N/A'}")
        
        # --- Part D: Update result (if applicable) AFTER shrinking (common for "maximum" problems) ---
        # For "maximum" type problems (e.g. Longest Substring Without Repeats LC3),
        # the window s[left:right] is now guaranteed to be valid *after* any necessary shrinking.
        # So, update max_length here.
        # Example for Longest Substring Without Repeats:
        # max_substring_len = max(max_substring_len, right - left)
        # --------------------------------------------------------------------------------------


    # 4. Return final result
    # Example for Min Window Substring:
    if t:
        return "" if min_len == float('inf') else s[result_start_index : result_start_index + min_len]
    # Example for Longest Substring Without Repeats:
    # return max_substring_len
    # Example for Permutation in String (if loop finishes without returning True):
    # return False
    # Example for Find All Anagrams:
    # return res_list
    return "Define return based on problem" # Placeholder


def window_needs_shrink(s, t, left, right, window_chars, needs, valid_chars_count):
    # This function must be defined based on the specific problem
    
    # Example for Min Window Substring (LC76): Shrink if window is valid (contains all of t)
    if t: # Only if t is relevant
        return valid_chars_count == len(needs)

    # Example for Permutation in String (LC567) / Find All Anagrams (LC438):
    # Shrink when window size is >= length of t (or > length of t if we check for solution before shrink)
    # The template above suggests checking `right - left >= len(t)`
    # If checking for fixed size window of len(t):
    # if t:
    #    return (right - left) >= len(t)

    # Example for Longest Substring Without Repeating Chars (LC3):
    # Shrink if `window_chars[s[right-1]] > 1` (the char just added caused a repeat)
    # Or, more generally, if any char count in `window_chars` > 1.
    # This is specific and often the condition is tied to `char_in` from the expansion step.
    # For a generic check on window_chars:
    # for char_count in window_chars.values():
    #    if char_count > 1: return True # Found a repeating character
    # return False

    return False # Default: don't shrink, needs problem-specific logic
```

**Key Questions to Adapt the Framework (Labuladong's Method):**

1.  **When to expand `right`?** (Outer loop condition: `right < len(s)`).
    -   **What data to update (Part A)?** When `s[right]` (the character `char_in`) enters the window, how do `window_chars`, `valid_chars_count`, or other problem-specific state variables change?

2.  **When to shrink `left` (`window_needs_shrink` condition)?**
    -   This is the condition for the inner `while` loop. It's problem-specific.
        -   *Minimum Window (LC76):* `valid_chars_count == len(needs)` (window contains all required characters).
        -   *Permutation in String (LC567)/Find All Anagrams (LC438):* `(right - left) >= len(t)` (window has reached target pattern's length).
        -   *Longest Substring Without Repeats (LC3):* A character count in `window_chars` becomes `> 1` (a repeat occurred).
    -   **What data to update (Part C)?** When `s[left]` (the character `char_out`) leaves the window, how do `window_chars`, `valid_chars_count`, etc., change? This often mirrors the updates in Part A.

3.  **When/Where to update the result (Part B or Part D)?**
    -   **Minimum-seeking problems (e.g., LC76):** Update the result *inside* the shrinking loop (Part B), just after confirming the window is valid and before actually shrinking it in a way that might make it invalid or less optimal for the current `left`.
    -   **Maximum-seeking problems (e.g., LC3):** Update the result *after* the shrinking loop (Part D), ensuring the window `s[left:right]` is valid according to the problem's constraints before its length is considered for the maximum.
    -   **Existence/Count problems (e.g., LC567, LC438):** If the condition for a valid match is met (often inside the shrinking loop, e.g., window of correct size AND `valid_chars_count` matches), then update the result (return `True`, or add `left` to a list of start indices).

## Visualizing Window Movement (Example: LC76)
Imagine `s = "ADOBECODEBANC"`, `t = "ABC"`. Target: find minimum window in `s` containing all chars of `t`.
(For detailed step-by-step, see [[Interview/Practice/LeetCode/LC76 - Minimum Window Substring|LC76 Problem Note]])

The core dynamic:
1. `right` expands the window: `[A]`, `[A,D]`, `[A,D,O]`, `[A,D,O,B]`, `[A,D,O,B,E]`, `[A,D,O,B,E,C]`
   - Window: `"ADOBEC"` (`left=0, right=6`). `window_chars` contains A,B,C. `valid_chars_count` equals `len(needs)`.
   - `window_needs_shrink` condition (`valid_chars_count == len(needs)`) is met.

2. Inner `while` loop (shrinking `left`):
   - **Iteration 1 of inner loop:**
     - Current window `s[0:6]` ("ADOBEC") is valid. Update `min_len=6, result_start_index=0`.
     - `char_out = s[0] = 'A'`. `left` becomes 1.
     - Update `window_chars['A']--`. If this makes `window_chars['A'] < needs['A']`, then `valid_chars_count--`.
     - Now `window_chars` might not satisfy `needs`. Re-check `window_needs_shrink`. If false, inner loop exits.
   - (If window `s[1:6]` ("DOBEC") was still valid, repeat: update `min_len`, shrink `left` again.)

3. If inner loop exits (window no longer satisfies `needs`), outer loop continues expanding `right`.

Labuladong's article uses images `![](/algo/images/slidingwindow/1.png)` through `![](/algo/images/slidingwindow/4.png)` to effectively illustrate this expansion and contraction for LC76.

## 总结 (Summary)
- Sliding window is an $O(N)$ technique for sub-array/sub-string problems, significantly more efficient than $O(N^2)$ brute-force.
- It uses `left` and `right` pointers to define a dynamic window `[left, right)`.
- **Framework Steps:**
    1. Initialize `left`, `right`, `window_data_structures` (e.g., `needs`, `window_chars`, `valid_chars_count`), and `result_variables`.
    2. Loop with `right` to expand the window: Add `s[right]` to window, update `window_data` (Part A).
    3. Inner loop: While `window_needs_shrink` is true:
        a. Update `result_variables` if current window is a candidate (Part B - often for min-type problems).
        b. Remove `s[left]` from window, update `window_data` (Part C). Increment `left`.
    4. After inner loop (if applicable), update `result_variables` if current window is valid (Part D - often for max-type problems).
- This framework is highly adaptable by changing the `window_data` structure, the `window_needs_shrink` condition, and the logic for updating results.

---
Parent: [[Interview/Concept/Algorithms/Sliding Window/index|Sliding Window Index]]
Related Concepts: [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]] (as it's a form of fast/slow pointers)
Related Problems:
- [[Interview/Practice/LeetCode/LC76 - Minimum Window Substring|LC76 - Minimum Window Substring]]
- [[Interview/Practice/LeetCode/LC567 - Permutation in String|LC567 - Permutation in String]]
- [[Interview/Practice/LeetCode/LC438 - Find All Anagrams in a String|LC438 - Find All Anagrams in a String]]
- [[Interview/Practice/LeetCode/LC3 - Longest Substring Without Repeating Characters|LC3 - Longest Substring Without Repeating Characters]]
- [[Interview/Practice/LeetCode/LC1004 - Max Consecutive Ones III|LC1004 - Max Consecutive Ones III]]
