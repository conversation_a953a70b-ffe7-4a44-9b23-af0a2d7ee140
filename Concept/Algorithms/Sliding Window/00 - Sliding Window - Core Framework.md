---
tags: [concept/algorithms, pattern/two_pointers, pattern/sliding_window, type/framework]
aliases: [Sliding Window Algorithm, 滑动窗口算法, 滑动窗口框架]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/滑动窗口算法核心代码模板.md]].
> This note outlines the general framework for solving problems using the Sliding Window technique.

# Sliding Window: Core Framework

The Sliding Window technique is an algorithmic pattern often used for problems involving finding a sub-array or sub-string that satisfies certain conditions (e.g., longest, shortest, contains specific elements). It typically uses two pointers, `left` and `right`, to define a "window" `s[left...right-1]` over the sequence.

## 🎯 Core Idea

The window `[left, right)` (inclusive `left`, exclusive `right`) slides through the data.
1.  The `right` pointer expands the window by moving to the right, incorporating new elements.
2.  The `left` pointer shrinks the window from the left when a certain condition is met or violated, removing elements.
This process efficiently explores contiguous sub-sequences.

**Why $O(N)$ Complexity?**
Although the typical implementation involves a `while` loop for `right` and an inner `while` loop for `left`, each element of the input sequence `s` is processed by `right` at most once (when it enters the window) and by `left` at most once (when it leaves the window). Thus, the total number of pointer movements is at most $2N$, leading to $O(N)$ time complexity for the pointer operations. Operations on data structures inside the loops (like hash map updates) are usually average $O(1)$ per element.

Labuladong's visualization (`div_minimum-window-substring`) for LC76 effectively shows this sliding motion.

## 🛠️ Generic Sliding Window Algorithm Pseudocode (Python)

```python
import collections

def sliding_window_template(s: str, t: str = ""): # t is optional, for problems like minWindow
    # 1. Initialization
    needs = collections.Counter(t) # Characters and their counts needed from t
    window_chars = collections.defaultdict(int) # Characters and their counts in the current window s[left:right]

    left, right = 0, 0 # Window boundaries [left, right)

    valid_chars_count = 0 # Number of character types from t whose counts are fully met in window_chars
                          # More precisely, it's len(needs) if all conditions are met.
                          # Labuladong uses it as count of chars in 't' that window 'window_chars' currently satisfies.

    # Result variables (example for min window substring)
    min_len = float('inf')
    result_start_index = 0

    # 2. Main Loop: Expand window with `right` pointer
    while right < len(s):
        char_in = s[right] # Character entering the window
        right += 1         # Expand window: [left, right)

        # --- Update window data based on char_in ---
        if char_in in needs:
            window_chars[char_in] += 1
            if window_chars[char_in] == needs[char_in]:
                valid_chars_count += 1
        # For problems not involving a 't' string (e.g. longest substring without repeats):
        # window_chars[char_in] = window_chars.get(char_in, 0) + 1
        # -------------------------------------------

        # Debug print (optional, remove for submission)
        # print(f"Window s[{left}:{right}]='{s[left:right]}', window_chars={dict(window_chars)}, valid={valid_chars_count}")

        # 3. Shrink Window: While window meets the condition to shrink
        # Condition depends on the problem.
        # For Min Window Substring: valid_chars_count == len(needs)
        # For Longest Substring Without Repeating: window_chars[char_in_from_expansion_step] > 1
        while left < right and condition_to_shrink_window(window_chars, needs, valid_chars_count): # `left < right` is crucial
            # --- Update result based on current valid window s[left:right-1] ---
            # This is where you might record a candidate solution
            # For Min Window Substring:
            if (right - left) < min_len: # Current window is s[left:right]
                min_len = (right - left)
                result_start_index = left
            # For Longest Substring Without Repeating:
            # res = max(res, right - left) (This is updated after shrinking for this specific problem)
            # -----------------------------------------------------------------

            char_out = s[left] # Character leaving the window
            left += 1          # Shrink window: [left, right)

            # --- Update window data based on char_out ---
            if char_out in needs:
                if window_chars[char_out] == needs[char_out]: # Was fully satisfied, now potentially not
                    valid_chars_count -= 1
                window_chars[char_out] -= 1
            # For problems not involving 't':
            # window_chars[char_out] -= 1
            # if window_chars[char_out] == 0: del window_chars[char_out]
            # ----------------------------------------------

            # Debug print (optional)
            # print(f"Shrunk to s[{left}:{right}]='{s[left:right]}', window_chars={dict(window_chars)}, valid={valid_chars_count}")

    # 4. Return final result
    # For Min Window Substring:
    return "" if min_len == float('inf') else s[result_start_index : result_start_index + min_len]
    # For other problems, return the calculated result (e.g., max_length, list of start_indices)

def condition_to_shrink_window(window_chars, needs, valid_chars_count):
    # This function must be defined based on the specific problem
    # Example for Min Window Substring (LC76):
    if not needs: return False # If t is empty, no condition to shrink based on needs
    return valid_chars_count == len(needs)

    # Example for Longest Substring Without Repeating Chars (LC3):
    # Assume window_chars stores counts of chars in current window.
    # Shrink if the char just added (`char_in` from expansion) caused a duplicate.
    # This implies `condition_to_shrink` is checked with knowledge of `char_in` that caused expansion.
    # A common way for LC3 is `while window_chars[char_that_entered_and_caused_duplicate] > 1:`
    # Or, more generally for "no repeats": shrink if any char count in window > 1.
    # For a generic template, the check needs to be on the state of `window_chars`.
    # Let's say the goal is "window must not have char_X more than K times":
    # return window_chars.get('X', 0) > K
    pass # Placeholder
```

**Key Questions to Answer for Each Problem (as per Labuladong):**
1.  **When to expand `right`?** (This is the outer `while` loop condition: `right < len(s)`). What data to update when `s[right]` (the character `char_in`) enters the window?
    - *Typically involves updating counts in `window_chars` and checking if this update makes a character type `valid` with respect to `needs`.*
2.  **When to shrink `left`?** What is the condition (`condition_to_shrink_window`) that triggers the inner `while` loop? What data to update when `s[left]` (the character `char_out`) leaves the window?
    - *The condition is problem-specific. For example, if the window currently satisfies all criteria of `t` (in Min Window Substring), or if a character becomes repeated (in Longest Substring Without Repeats).*
    - *Updating data involves decrementing counts in `window_chars` and potentially updating `valid_chars_count` if a character type is no longer fully satisfied.*
3.  **When to update the result?**
    - *For "minimum" type problems (like LC76 Min Window Substring): Update when a valid window is found (i.e., inside the shrinking loop, *before* actually shrinking in a way that might invalidate it, or just after confirming its validity and current length).*
    - *For "maximum" type problems (like LC3 Longest Substring Without Repeats): Often updated after expanding `right` and *after* any necessary shrinking has occurred to ensure the window is valid before calculating its length for the result.* The exact placement (inside or outside shrinking loop) depends on whether the "valid" state for updating the result is achieved *before* shrinking or *after* shrinking ensures some property. Labuladong's LC3 solution updates `res = max(res, right - left)` *after* the inner shrinking loop.

## Visualizing Window Movement
Imagine `s = "ADOBECODEBANC"`, `t = "ABC"`. Target: find minimum window in `s` containing all chars of `t`. (Refer to [[Interview/Practice/LeetCode/LC76 - Minimum Window Substring|LC76]] for detailed visualization).

The core dynamic:
1. `right` expands the window: `[A]`, `[A,D]`, `[A,D,O]`, `[A,D,O,B]`, `[A,D,O,B,E]`, `[A,D,O,B,E,C]`
   - Window: `"ADOBEC"` (Indices 0-5, so `left=0, right=6`)
   - `window_chars` now contains 'A','B','C' with sufficient counts. `valid_chars_count` equals `len(needs)`.
   - This window is a candidate. Length is 6. `min_len = 6`, `result_start_index = 0`.
   - Condition to shrink is met (`valid_chars_count == len(needs)`).

2. `left` shrinks the window:
   - Remove `s[0]='A'`: Window is `"DOBEC"` (`left=1, right=6`). `window_chars['A']` decreases. If 'A' is still sufficiently represented (e.g., if `t="AABC"` and window was `"AADOBEC"`), `valid_chars_count` might not change. If `window_chars['A']` drops below `needs['A']`, then `valid_chars_count` decreases.
   - Assuming `t="ABC"`, removing 'A' makes `window_chars['A']==0`. So `valid_chars_count` (which was 3) becomes 2.
   - The inner `while` loop condition (`valid_chars_count == len(needs)`) becomes false. Shrinking stops.

3. `right` continues expanding from `right=6`, and the process repeats.

Labuladong's article uses images `![](/algo/images/slidingwindow/1.png)` through `![](/algo/images/slidingwindow/4.png)` to effectively illustrate this expansion and contraction for LC76. The logic of these specific images (showing `needs` and `window` maps) is best understood in the context of the [[Interview/Practice/LeetCode/LC76 - Minimum Window Substring|LC76 problem solution]].

## 总结 (Summary)
- Sliding window is an $O(N)$ technique for sub-array/sub-string problems, significantly more efficient than $O(N^2)$ brute-force.
- It uses `left` and `right` pointers to define a dynamic window `[left, right)`.
- `right` expands the window, incorporating `s[right]` and updating window state (e.g., character counts).
- `left` shrinks the window when a problem-specific condition is met (e.g., window is "valid" and we want to find minimum, or window is "invalid" due to repeats and we want to fix it). `s[left]` is removed, and window state is updated.
- Results (min/max length, count, start indices) are typically updated when the window either achieves a valid state (often during shrinking for "min" problems) or just after ensuring validity post-shrinking (often for "max" problems).
- This framework is highly adaptable by changing the `window_data` structure, the `condition_to_shrink_window`, and the logic for updating results.

---
Parent: [[Interview/Concept/Algorithms/Sliding Window/index|Sliding Window Index]]
Related Concepts: [[Interview/Concept/Algorithms/Two Pointers/01 - Two Pointers for Arrays|Two Pointers for Arrays]] (as it's a form of fast/slow pointers)
Related Problems:
- [[Interview/Practice/LeetCode/LC76 - Minimum Window Substring|LC76 - Minimum Window Substring]]
- [[Interview/Practice/LeetCode/LC567 - Permutation in String|LC567 - Permutation in String]]
- [[Interview/Practice/LeetCode/LC438 - Find All Anagrams in a String|LC438 - Find All Anagrams in a String]]
- [[Interview/Practice/LeetCode/LC3 - Longest Substring Without Repeating Characters|LC3 - Longest Substring Without Repeating Characters]]
