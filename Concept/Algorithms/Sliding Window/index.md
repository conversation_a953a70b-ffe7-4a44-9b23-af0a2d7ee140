---
tags: [index, concept/algorithms, pattern/sliding_window, pattern/two_pointers]
aliases: [Sliding Window Index]
---

# Sliding Window Algorithm

This section covers the Sliding Window algorithmic pattern.

## Core Concepts:
- [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window - Core Framework]]
  - Two Pointers (Left, Right)
  - Window Expansion and Shrinking
  - $O(N)$ Time Complexity

## LeetCode Examples:
- [[Interview/Practice/LeetCode/LC3 - Longest Substring Without Repeating Characters|LC3 - Longest Substring Without Repeating Characters]]
- [[Interview/Practice/LeetCode/LC76 - Minimum Window Substring|LC76 - Minimum Window Substring]]
- [[Interview/Practice/LeetCode/LC438 - Find All Anagrams in a String|LC438 - Find All Anagrams in a String]]
- [[Interview/Practice/LeetCode/LC567 - Permutation in String|LC567 - Permutation in String]]


## Visualization
```mermaid
graph TD
    SWConcept["Sliding Window"] --> Framework["[[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Framework]]"]
    Framework --> Pointers["(Left & Right Pointers)"]
    Framework --> ExpandShrink["(Window Expansion/Shrinking)"]

    SWConcept --> ExamplesLC["LeetCode Examples"]
    ExamplesLC --> LC3["[[Interview/Practice/LeetCode/LC3 - Longest Substring Without Repeating Characters|LC3]]"]
    ExamplesLC --> LC76["[[Interview/Practice/LeetCode/LC76 - Minimum Window Substring|LC76]]"]
    ExamplesLC --> LC438["[[Interview/Practice/LeetCode/LC438 - Find All Anagrams in a String|LC438]]"]
    ExamplesLC --> LC567["[[Interview/Practice/LeetCode/LC567 - Permutation in String|LC567]]"]

    classDef main fill:#fff2cc,stroke:#d6b656,stroke-width:2px;
    class SWConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
