- [[Interview/Concept/Algorithms/Searching/Binary Search/02 - Binary Search in 2D Matrix|Binary Search in 2D Matrix]]
---
tags: [index, concept/algorithms, concept/searching, concept/binary_search]
aliases: [Binary Search Index]
---

# Binary Search Algorithms

This section covers core templates and advanced applications of Binary Search.

## Core Concepts:
- [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary Search - Core Template]]
  - Basic Search
  - Left-Bound Search
  - Right-Bound Search
- [[Interview/Concept/Algorithms/Searching/Binary Search/01 - Binary Search - Advanced Application Framework|Binary Search - Advanced Application Framework]]
  - Binary Search on Answer

## Visualization
```mermaid
graph TD
    BSConcept["Binary Search"] --> CoreT["[[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Core Template]]"]
    BSConcept --> AdvApp["[[Interview/Concept/Algorithms/Searching/Binary Search/01 - Binary Search - Advanced Application Framework|Advanced Application Framework]]"]

    CoreT --> BasicS["(Basic Search)"]
    CoreT --> LeftBoundS["(Left-Bound)"]
    CoreT --> RightBoundS["(Right-Bound)"]

    AdvApp --> BSAnswer["(Binary Search on Answer)"]

    classDef main fill:#e6f2ff,stroke:#0073e6,stroke-width:2px;
    class BSConcept main;
```

---
Parent: [[Interview/Concept/Algorithms/Searching/index|Searching Algorithms Index]]
