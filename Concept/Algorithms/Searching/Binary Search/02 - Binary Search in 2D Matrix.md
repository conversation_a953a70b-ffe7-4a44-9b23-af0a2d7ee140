---
tags: [concept/algorithms, concept/searching, pattern/binary_search, topic/matrix, course/labuladong]
aliases: [Binary Search in 2D Array, Search a 2D Matrix, 二维数组二分搜索]
summary: |
  Explains how to apply binary search techniques to 2D matrices, typically by treating the matrix as a flattened 1D sorted array or by performing binary search on rows then columns (or vice-versa) if rows/columns are sorted.
created: 2025-05-26T18:00:00.000-07:00
modified: 2025-05-26T18:00:00.000-07:00
---

> [!NOTE] Source Annotation
> Content conceptualized from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷数组算法/【练习】二分搜索算法经典习题.md|【练习】二分搜索算法经典习题.md]] which mentions "二维数组中的二分搜索".
> This note details common strategies for applying binary search to 2D matrices.

# Binary Search in 2D Matrices

Applying binary search to a 2D matrix requires adapting the 1D binary search logic to the matrix's structure. The specific approach depends on the properties of the matrix (e.g., how it's sorted).

## Strategy 1: Treat Matrix as a Flattened 1D Sorted Array

This strategy is applicable if the matrix has the property that:
- Each row is sorted from left to right.
- The first integer of each row is greater than the last integer of the previous row.
(Example: LeetCode 74. Search a 2D Matrix)

If these properties hold, the entire matrix can be conceptually viewed as a single, large, sorted 1D array.

**Algorithm:**
1.  Get matrix dimensions: `rows`, `cols`.
2.  Perform a standard binary search on the index range `0` to `rows * cols - 1`.
3.  For a `mid_1d_index` obtained from the 1D binary search:
    -   Convert it back to 2D matrix coordinates:
        -   `row = mid_1d_index // cols`
        -   `col = mid_1d_index % cols`
    -   Compare `matrix[row][col]` with the `target`.
    -   Adjust `left_1d` and `right_1d` pointers of the 1D binary search accordingly.

```python
# Conceptual for LC74-like matrix
# class Solution:
#     def searchMatrix_flattened(self, matrix: list[list[int]], target: int) -> bool:
#         if not matrix or not matrix[0]:
#             return False
        
#         rows, cols = len(matrix), len(matrix[0])
#         left, right = 0, rows * cols - 1

#         while left <= right:
#             mid_1d = left + (right - left) // 2
#             mid_row = mid_1d // cols
#             mid_col = mid_1d % cols
#             val_at_mid = matrix[mid_row][mid_col]

#             if val_at_mid == target:
#                 return True
#             elif val_at_mid < target:
#                 left = mid_1d + 1
#             else:
#                 right = mid_1d - 1
#         return False
```
- **Time Complexity:** $O(\log(R \cdot C))$, where R is rows, C is columns.
- **Space Complexity:** $O(1)$.

## Strategy 2: Binary Search on Rows, then Binary Search on Chosen Row

Applicable if:
- Each row is sorted.
- (Optional, but helps for row selection) Rows are sorted with respect to their first/last elements.
  (Example: LeetCode 74 can also use this. Find the row where `target` might be, then BS in that row).

**Algorithm (for LC74-like matrix):**
1.  **Find Target Row:** Perform binary search on the first (or last) elements of each row to identify which row *could* contain the `target`.
    - E.g., find row `r` such that `matrix[r][0] <= target <= matrix[r][cols-1]`.
2.  **Search in Row:** Once a candidate row `r` is found, perform a standard 1D binary search on `matrix[r]`.

```python
# Conceptual for LC74-like matrix (alternative)
# class Solution:
#     def searchMatrix_row_then_col(self, matrix: list[list[int]], target: int) -> bool:
#         if not matrix or not matrix[0]: return False
#         rows, cols = len(matrix), len(matrix[0])

#         # Step 1: Find the target row using BS on first elements
#         top, bottom = 0, rows - 1
#         target_row_idx = -1
#         while top <= bottom:
#             row_mid = top + (bottom - top) // 2
#             if matrix[row_mid][0] <= target <= matrix[row_mid][cols-1]:
#                 target_row_idx = row_mid
#                 break
#             elif matrix[row_mid][0] > target:
#                 bottom = row_mid - 1
#             else: # matrix[row_mid][cols-1] < target
#                 top = row_mid + 1
        
#         if target_row_idx == -1: return False # Target cannot be in any row based on range

#         # Step 2: Binary search in the identified row
#         left, right = 0, cols - 1
#         while left <= right:
#             col_mid = left + (right - left) // 2
#             if matrix[target_row_idx][col_mid] == target:
#                 return True
#             elif matrix[target_row_idx][col_mid] < target:
#                 left = col_mid + 1
#             else:
#                 right = col_mid - 1
#         return False
```
- **Time Complexity:** $O(\log R + \log C)$.
- **Space Complexity:** $O(1)$.

## Strategy 3: Start from a Corner, Eliminate Rows/Columns

Applicable if:
- Rows are sorted left-to-right.
- Columns are sorted top-to-bottom.
(Example: LeetCode 240. Search a 2D Matrix II)

**Algorithm:**
1. Start at a specific corner, e.g., top-right `(row=0, col=cols-1)` or bottom-left `(row=rows-1, col=0)`.
2. Let current element be `matrix[row][col]`.
   - If `matrix[row][col] == target`, found.
   - If `matrix[row][col] > target`: The target cannot be in the current column (if starting top-right, because elements below are larger) or current row (if starting bottom-left, because elements to right are larger). So, eliminate current column (`col--` if top-right) or row (`row--` if bottom-left).
   - If `matrix[row][col] < target`: The target cannot be in the current row (if starting top-right) or current column (if starting bottom-left). So, eliminate current row (`row++` if top-right) or column (`col++` if bottom-left).
3. Repeat until target found or pointers go out of bounds.

```python
# Conceptual for LC240-like matrix
# class Solution:
#     def searchMatrix_corner_elimination(self, matrix: list[list[int]], target: int) -> bool:
#         if not matrix or not matrix[0]: return False
#         rows, cols = len(matrix), len(matrix[0])
#         row, col = 0, cols - 1 # Start at top-right corner

#         while row < rows and col >= 0:
#             current_val = matrix[row][col]
#             if current_val == target:
#                 return True
#             elif current_val > target:
#                 col -= 1 # Target must be to the left
#             else: # current_val < target
#                 row += 1 # Target must be downwards
#         return False
```
- **Time Complexity:** $O(R+C)$. In each step, either `row` increases or `col` decreases.
- **Space Complexity:** $O(1)$.

## 总结 (Summary)
- Binary search in 2D matrices depends on the matrix's sorting properties.
- **Flattened 1D Search ($O(\log(RC))$):** If rows are sorted and each row's start > previous row's end. (e.g., LC74)
- **Row BS then Column BS ($O(\log R + \log C)$):** If rows are sorted, and first/last elements of rows are also sorted. (e.g., LC74)
- **Corner Elimination ($O(R+C)$):** If rows sorted L-R and columns sorted T-B. This is not strictly binary search but a similar search space reduction technique. (e.g., LC240)
- Understanding the specific sorted structure of the matrix is key to choosing the right approach.

---
Parent: [[Interview/Concept/Algorithms/Searching/Binary Search/index|Binary Search Algorithms Index]]
Related Problems: [[Interview/Practice/LeetCode/LC74 - Search a 2D Matrix|LC74]], [[Interview/Practice/LeetCode/LC240 - Search a 2D Matrix II|LC240]]
