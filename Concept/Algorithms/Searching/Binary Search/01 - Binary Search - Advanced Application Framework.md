---
tags: [concept/algorithms, concept/searching, concept/binary_search, type/framework, pattern/divide_and_conquer, course/labuladong]
aliases: [Binary Search on Answer, Advanced Binary Search, 二分搜索答案, 二分答案]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷数组算法/实际运用二分搜索时的思维框架.md]].
> This note expands on the [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary Search Core Template]] by detailing how to apply binary search to problems where the search space is not an array, but a range of possible answers.

| [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Prev: Core Template]] | [[Interview/Concept/Algorithms/Searching/Binary Search/index|Back to Binary Search Index]] | 

# Binary Search: Advanced Application Framework (Binary Search on Answer)

While the basic [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Binary Search Core Template]] focuses on finding elements in sorted arrays, a powerful extension is "Binary Search on the Answer." This technique applies when you can determine if a *potential answer* `x` is feasible (or too large/small) and the range of possible answers exhibits monotonicity.

## 🎯 Core Idea: Searching the Answer Space

Many problems ask for a minimum value that satisfies a condition, or a maximum value that satisfies another. If we can define a function `check(x)` that tells us if a potential answer `x` is valid or leads to a certain outcome (e.g., "can we achieve the goal with capacity `x`?"), and this function exhibits monotonicity, we can binary search for the optimal `x`.

**Monotonicity Requirement:**
- If `check(x)` is true, then `check(x+1)` is also true (or `check(x-1)` is also true, depending on the problem).
- Or, if `check(x)` is true, then `check(x-1)` is false (for finding minimum true) or `check(x+1)` is false (for finding maximum true).

This allows us to narrow down the search space for the answer `x`.

## 🛠️ General Framework for Binary Search on Answer

1.  **Define the Search Space for the Answer:**
    - Determine the minimum (`low`) and maximum (`high`) possible values for the answer. These define your `[low, high]` search interval.
2.  **Implement a `check(potential_answer)` function:**
    - This function takes a `potential_answer` (let's call it `mid_val` from the binary search) and determines if it's possible to satisfy the problem's main constraint with this `mid_val`.
    - The `check` function's logic is problem-specific and often involves a greedy approach, simulation, or another algorithm. Its own complexity must be considered.
3.  **Apply Binary Search Template:**
    - Use a standard binary search loop (e.g., `while low <= high`).
    - `mid_val = low + (high - low) // 2`.
    - Based on the result of `check(mid_val)` and whether you're looking for a minimum or maximum feasible answer, adjust `low` and `high` using left-bound or right-bound logic.

**Template for finding the MINIMUM `x` such that `check(x)` is TRUE:**
(This is like a left-bound search on the answer space.)
```python
# def binary_search_on_answer_min_true(low_bound_ans, high_bound_ans, check_function, ...other_params):
#     ans = high_bound_ans + 1 # Initialize with a value outside the valid range (for min)
#     left, right = low_bound_ans, high_bound_ans

#     while left <= right:
#         mid_val = left + (right - left) // 2
#         if check_function(mid_val, ...other_params): # If mid_val is a feasible answer
#             ans = mid_val       # mid_val could be the minimum, store it
#             right = mid_val - 1 # Try to find an even smaller feasible answer
#         else: # mid_val is not feasible (e.g., too small)
#             left = mid_val + 1  # Need a larger potential answer
#     return ans # Returns the smallest mid_val for which check() was true, or initial if none
```
This template aligns with finding the **leftmost `true`** in a conceptual boolean array `[F,F,F,T,T,T]`.

**Template for finding the MAXIMUM `x` such that `check(x)` is TRUE:**
(This is like a right-bound search on the answer space.)
```python
# def binary_search_on_answer_max_true(low_bound_ans, high_bound_ans, check_function, ...other_params):
#     ans = low_bound_ans - 1 # Initialize with a value outside the valid range (for max)
#     left, right = low_bound_ans, high_bound_ans

#     while left <= right:
#         mid_val = left + (right - left) // 2
#         if check_function(mid_val, ...other_params): # If mid_val is a feasible answer
#             ans = mid_val       # mid_val could be the maximum, store it
#             left = mid_val + 1  # Try to find an even larger feasible answer
#         else: # mid_val is not feasible (e.g., too large)
#             right = mid_val - 1 # Need a smaller potential answer
#     return ans # Returns the largest mid_val for which check() was true, or initial if none
```
This template aligns with finding the **rightmost `true`** in a conceptual boolean array `[T,T,T,F,F,F]`.

## 💡 How to Identify if a Problem Fits "Binary Search on Answer"

Labuladong suggests looking for these cues:
- The problem asks for a "minimum value of `X` such that condition `P(X)` is met" or "maximum value of `Y` such that condition `Q(Y)` is met."
- The "condition" `P(X)` or `Q(Y)` (your `check` function) often has a monotonic property. If `X` works, any `X' > X` might also work (or not work, depending on min/max goal).
- It's often easier to *verify* if a given answer `X` works, than to directly compute the optimal `X`.

## Example Problem: [[Interview/Practice/LeetCode/LC1011 - Capacity To Ship Packages Within D Days|LC1011 - Capacity To Ship Packages Within D Days]]

**Problem:** Find the least weight capacity of a ship that can ship all packages within `D` days.
- **Answer Space:** The capacity `x` of the ship.
    - `low_bound_ans = max(weights)` (ship must carry at least the heaviest package).
    - `high_bound_ans = sum(weights)` (ship can carry all packages in one day).
- **`check(capacity_x)` function:** Given a ship capacity `capacity_x`, can all packages be shipped within `D` days?
    - This subproblem can be solved greedily: load packages onto the current ship as long as capacity isn't exceeded. If exceeded, start a new day with a new ship. Count the number of days required.
    - `check(capacity_x)` returns `True` if `days_required <= D`, `False` otherwise.
- **Monotonicity:** If a capacity `x` works (requires $\le D$ days), then any capacity `x' > x` will also work (requires $\le D$ days, possibly fewer). We are looking for the *minimum* `x` that works.
- **Binary Search:** Use the "minimum true" template.

```python
# Conceptual structure for LC1011
# def can_ship(weights, capacity, D_days):
#     days_needed = 1
#     current_load = 0
#     for w in weights:
#         if current_load + w <= capacity:
#             current_load += w
#         else:
#             days_needed += 1
#             current_load = w
#     return days_needed <= D_days

# def shipWithinDays(weights, D):
#     left_capacity = max(weights) # Smallest possible answer
#     right_capacity = sum(weights) # Largest possible answer
#     ans_capacity = right_capacity 

#     while left_capacity <= right_capacity:
#         mid_capacity = left_capacity + (right_capacity - left_capacity) // 2
#         if can_ship(weights, mid_capacity, D):
#             ans_capacity = mid_capacity
#             right_capacity = mid_capacity - 1 # Try for smaller capacity
#         else:
#             left_capacity = mid_capacity + 1 # Need larger capacity
#     return ans_capacity
```

## Visualization of Answer Space Search

Imagine `check(x)` results for a "minimum `x` for `check(x)==True`" problem:
Answer Space `x`:  `[low ... x1, x2, x3, x4, x5 ... high]`
`check(x)` results: `[F,  F,  F,  T,  T,  T,  T,  T]`
Binary search aims to find `x3` (the first `T`).

```tikz
\begin{tikzpicture}[
    scale_point/.style={circle, draw, minimum size=4mm, inner sep=1pt, font=\sffamily\scriptsize},
    result_label/.style={font=\sffamily\bfseries\tiny, above=2pt}
]
    % Axis for answer values
    \draw[->] (-1,0) -- (9,0) node[right] {$x$ (Potential Answer)};
    \foreach \x/\label/\color in {0/F/red, 1/F/red, 2/F/red, 3/T/green, 4/T/green, 5/T/green, 6/T/green, 7/T/green, 8/T/green} {
        \node[scale_point, fill=\color!20] (p\x) at (\x,0) {\x};
        \node[result_label, color=\color] at (p\x.north) {\label};
    }

    % Binary search steps (conceptual)
    \node at (4,-1) [text width=8cm, align=center, draw, fill=yellow!10, rounded corners] {
        Binary search on this `[F,F,F,T,T,T]` conceptual array.
        If `check(mid)` is `T`, it's a candidate, try `right = mid - 1`.
        If `check(mid)` is `F`, need larger `x`, so `left = mid + 1`.
        This finds the leftmost `T`.
    };
\end{tikzpicture}
```

## 总结 (Summary)
- "Binary Search on the Answer" is a powerful technique for optimization problems where the answer lies in a monotonic search space.
- **Key Steps:**
    1.  Define the range of possible answers (`low`, `high`).
    2.  Create a `check(potential_answer)` function to verify feasibility. This function itself might be a non-trivial algorithm (e.g., greedy).
    3.  Apply a binary search template (left-bound or right-bound type) to find the optimal answer.
- The overall time complexity will be $O(\text{ComplexityOfCheckFunction} \times \log(\text{RangeOfAnswers}))$.
- Look for problems asking for min/max values satisfying a condition where the condition exhibits monotonicity.

---
| [[Interview/Concept/Algorithms/Searching/Binary Search/00 - Binary Search - Core Template|Prev: Core Template]] | [[Interview/Concept/Algorithms/Searching/Binary Search/index|Back to Binary Search Index]] | 

Related Problems:
- [[Interview/Practice/LeetCode/LC1011 - Capacity To Ship Packages Within D Days|LC1011 - Capacity To Ship Packages]]
- [[Interview/Practice/LeetCode/LC875 - Koko Eating Bananas|LC875 - Koko Eating Bananas]]
- [[Interview/Practice/LeetCode/LC410 - Split Array Largest Sum|LC410 - Split Array Largest Sum]]
This framework is very common for "minimize the maximum" or "maximize the minimum" type problems.

