---
tags: [concept/algorithms, concept/searching, concept/binary_search, type/framework, pattern/divide_and_conquer]
aliases: [Binary Search Algorithm, 二分搜索框架, 二分查找]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第零章、核心刷题框架汇总/二分搜索算法核心代码模板.md]].
> This note details the core framework for binary search, including variations for finding exact values, left bounds, and right bounds.

# Binary Search: Core Templates and Logic

Binary search is an efficient algorithm for finding an item from a **sorted** list of items. It works by repeatedly dividing the search interval in half. Labuladong emphasizes that while the idea is simple, "细节是魔鬼" (details are the devil), and it's crucial to correctly handle `mid` calculation, loop conditions (`<=` vs `<`), and boundary updates (`left = mid + 1` vs `right = mid`).

## 零、二分查找框架 (Basic Binary Search Framework)

```python
def binary_search_framework(nums: list[int], target: int) -> int:
    left, right = 0, len(nums) - 1 # Default: search in [left, right] (closed interval)

    while left <= right: # Condition for closed interval
        mid = left + (right - left) // 2 # Prevents overflow, same as (left + right) // 2

        if nums[mid] == target:
            # Found target, handle accordingly (return mid, or adjust for bounds)
            return mid 
        elif nums[mid] < target:
            left = mid + 1 # Search in right half: [mid + 1, right]
        elif nums[mid] > target:
            right = mid - 1 # Search in left half: [left, mid - 1]

    return -1 # Target not found
```
**Key Analysis Points (Labuladong's advice):**
1.  **Use `else if` for clarity:** Avoid plain `else` to explicitly show all conditions.
2.  **`mid` calculation:** `left + (right - left) // 2` prevents potential overflow if `left + right` is too large.
3.  **Search Interval `[left, right]` (Closed-Closed):**
    -   `right` initialized to `len(nums) - 1`.
    -   `while` loop condition: `left <= right`. Terminates when `left = right + 1` (interval becomes empty, e.g., `[high+1, high]`).
    -   Boundary updates: `left = mid + 1` and `right = mid - 1` because `mid` has been checked and excluded from the next search interval.

## I. 寻找一个数 (Basic Binary Search - Finding an Exact Value)
This is the simplest scenario, as implemented in the framework above. [[Interview/Practice/LeetCode/LC704 - Binary Search|LC704 - Binary Search]] is a direct application.

**Visualization (Standard Binary Search):**
Labuladong's visualizer `div_binary-search` demonstrates this.

**Alternative: `while left < right` (Search interval `[left, right)`)**
If `right` is initialized to `len(nums)` (exclusive end), the loop condition becomes `while left < right`.
- When `left == right`, the interval `[left, left)` is empty, loop terminates.
- Boundary updates: `right = mid` (not `mid-1`) because `mid` itself could be the target in `[left, mid)`. If `nums[mid] < target`, then `left = mid + 1`.
- Final check: After loop, if `left == len(nums)` or `nums[left] != target`, then not found. Otherwise `left` is the index. This form is common for lower_bound/upper_bound implementations.

Labuladong's article focuses on the `[left, right]` closed interval for the initial basic search.

## II. 寻找左侧边界的二分搜索 (Left-Bound Binary Search)
Finds the index of the first occurrence of `target`. If `target` is not present, it finds the index where `target` *would be inserted* to maintain order (i.e., index of first element greater than `target`, or `len(nums)` if all elements are smaller).

### Version 1: Search Interval `[left, right)` (Labuladong's common example for bounds)
```python
def left_bound_v1(nums: list[int], target: int) -> int:
    left, right = 0, len(nums) # Search interval [left, right)

    while left < right: # Loop until left == right
        mid = left + (right - left) // 2
        if nums[mid] == target:
            right = mid # Shrink right boundary to find leftmost
        elif nums[mid] < target:
            left = mid + 1 # Target is in [mid+1, right)
        elif nums[mid] > target:
            right = mid # Target is in [left, mid)

    # `left` is the insertion point.
    # To return -1 if not found (exact match needed):
    # if left == len(nums) or nums[left] != target:
    #    return -1
    return left # Returns insertion point
```
- **Key logic `nums[mid] == target`:** `right = mid`. This is because `mid` *could* be the leftmost `target`, so we search in `[left, mid)`. The loop terminates when `left == right`, and `left` points to the potential leftmost target or insertion point.
- If `target` does not exist, `left_bound` returns the index of the first element *greater than* `target`, or `len(nums)`.
- This can be used for `floor(target)`: `left_bound(nums, target) - 1`.

### Version 2: Search Interval `[left, right]` (Unified with basic search)
```python
def left_bound_v2(nums: list[int], target: int) -> int:
    left, right = 0, len(nums) - 1 # Search interval [left, right]
    # candidate_left_bound = -1 # To store potential answer

    while left <= right:
        mid = left + (right - left) // 2
        if nums[mid] == target:
            # Found target, potential left bound. Keep searching left.
            # candidate_left_bound = mid 
            right = mid - 1 
        elif nums[mid] < target:
            left = mid + 1
        elif nums[mid] > target:
            right = mid - 1

    # After loop, `left` is the insertion point or first element >= target.
    # If target was found, `left` points to its first occurrence if condition was strictly `nums[mid] < target`.
    # The `right = mid - 1` when `nums[mid] == target` ensures `left` eventually points to first `target` or
    # `len(nums)` if `target` is larger than all, or index of first element > `target`.

    # Check if `left` is valid and `nums[left]` is indeed the target for "find first occurrence"
    if left < len(nums) and nums[left] == target:
        return left
    # This is for "find index of target", if not found return -1.
    # If we want insertion point, just return left (after checking bounds if it's out of array).
    # Labuladong's unified template's final check for left_bound:
    # if left < 0 or left >= len(nums): return -1
    # return left if nums[left] == target else -1

    # If looking for insertion point (first element >= target):
    # (Loop ends when left = right + 1)
    # `left` will be the correct index.
    return left
```
Labuladong's "unified" `left_bound` template (second Python example in article) checks `if left < 0 or left >= len(nums): return -1` followed by `return left if nums[left] == target else -1`. This is specifically for finding the first index of `target` or -1. If just the "bound" is needed, `left` is often the result. Visualizer: `div_binary-search-left-bound`.


## III. 寻找右侧边界的二分搜索 (Right-Bound Binary Search)
Finds the index of the last occurrence of `target`. If `target` is not present, or to find insertion point for elements *just greater than* target, it's slightly different. The common goal for "right_bound" is to find the index of the first element *strictly greater than* target, then subtract 1. Or, find the last element *equal to* target.

### Version 1: Search Interval `[left, right)`
To find the index of the *first element strictly greater than `target`*:
```python
def right_bound_v1_insertion_point(nums: list[int], target: int) -> int:
    left, right = 0, len(nums) # Search interval [left, right)

    while left < right:
        mid = left + (right - left) // 2
        if nums[mid] == target:
            left = mid + 1 # Shrink left boundary to find rightmost / element > target
        elif nums[mid] < target:
            left = mid + 1 # Target is in [mid+1, right)
        elif nums[mid] > target:
            right = mid # Target is in [left, mid)

    # `left` (or `right`) is the insertion point for elements > target.
    # So, `left - 1` is the index of the rightmost element <= target.
    # If `target` exists, `nums[left-1]` would be `target`.
    # If nums[left-1] == target: return left - 1. Else -1.
    # Labuladong's template for right_bound usually returns left-1 or right based on condition.
    # If `left` becomes 0 because target is smaller than all, `left-1` is -1.
    # if left == 0: return -1 # Target smaller than all elements
    # return left - 1 if nums[left-1] == target else -1 # This is for finding last occurrence
    return left # Returns insertion point for elements > target
```
- **Key logic `nums[mid] == target`:** `left = mid + 1`. This tries to find elements to the right of `mid`.
- If goal is index of last `target`: Result is `(returned_insertion_point_for_strictly_greater) - 1`. Must check if `nums[result]` is actually `target`.

### Version 2: Search Interval `[left, right]` (Unified with basic search)
To find the index of the last occurrence of `target`.
```python
def right_bound_v2(nums: list[int], target: int) -> int:
    left, right = 0, len(nums) - 1
    # candidate_right_bound = -1

    while left <= right:
        mid = left + (right - left) // 2
        if nums[mid] == target:
            # Found target, potential right bound. Keep searching right.
            # candidate_right_bound = mid
            left = mid + 1 
        elif nums[mid] < target:
            left = mid + 1
        elif nums[mid] > target:
            right = mid - 1

    # After loop, `right` is candidate for last occurrence.
    # (Loop ends when left = right + 1, so `right` is one less than `left`)
    # Check if `right` is valid and `nums[right]` is indeed the target.
    if right >= 0 and nums[right] == target:
        return right
    # return candidate_right_bound
    # Labuladong's unified template's final check for right_bound:
    # if right < 0 or right >= len(nums): return -1
    # return right if nums[right] == target else -1
    return -1 # For finding last occurrence and returning -1 if not found
```
Visualizer: `div_binary-search-right-bound`.

## IV. 逻辑统一 (Logical Unification - Labuladong's Summary)
Labuladong provides a unified template using `[left, right]` (closed interval) for all three scenarios:
```python
# 1. Basic Binary Search
def binary_search_unified(nums: list[int], target: int) -> int:
    left, right = 0, len(nums) - 1 
    while left <= right:
        mid = left + (right - left) // 2
        if nums[mid] < target:
            left = mid + 1
        elif nums[mid] > target:
            right = mid - 1 
        elif nums[mid] == target:
            return mid # Direct return
    return -1 # Direct return

# 2. Left-Bound Binary Search
def left_bound_unified(nums: list[int], target: int) -> int:
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = left + (right - left) // 2
        if nums[mid] < target:
            left = mid + 1
        elif nums[mid] > target:
            right = mid - 1
        elif nums[mid] == target:
            right = mid - 1 # Lock left boundary
    # Final checks for left_bound to return index or -1
    if left < 0 or left >= len(nums): return -1
    return left if nums[left] == target else -1

# 3. Right-Bound Binary Search
def right_bound_unified(nums: list[int], target: int) -> int:
    left, right = 0, len(nums) - 1
    while left <= right:
        mid = left + (right - left) // 2
        if nums[mid] < target:
            left = mid + 1
        elif nums[mid] > target:
            right = mid - 1
        elif nums[mid] == target:
            left = mid + 1 # Lock right boundary
    # Final checks for right_bound to return index or -1
    # Note: After loop, left = right + 1. `right` is the candidate.
    if right < 0 or right >= len(nums): return -1
    return right if nums[right] == target else -1
```
This unified template provides a consistent structure. The key difference lies in the `nums[mid] == target` condition and the final return logic.

**Key to "Locking" Boundaries:**
- To find **left bound**: when `nums[mid] == target`, try to find an even earlier occurrence by setting `right = mid - 1`.
- To find **right bound**: when `nums[mid] == target`, try to find an even later occurrence by setting `left = mid + 1`.

## 总结 (Summary)
- Binary search efficiently finds elements or bounds in sorted arrays.
- **Core Logic:** Repeatedly halve the search interval `[left, right]`.
- **Details Matter:** Correctly handling `left`, `right`, `mid`, loop conditions, and boundary updates is crucial.
- **Variations:**
    - Basic search: Find any occurrence of `target`.
    - Left-bound: Find first occurrence or insertion point.
    - Right-bound: Find last occurrence or insertion point for elements > `target`.
- Labuladong's unified template based on `[left, right]` closed interval provides a consistent way to implement these variations.

---
Parent: [[Interview/Concept/Algorithms/Searching/Binary Search/index|Binary Search Algorithms Index]]
Related Problems:
- [[Interview/Practice/LeetCode/LC704 - Binary Search|LC704 - Binary Search]]
- [[Interview/Practice/LeetCode/LC34 - Find First and Last Position of Element in Sorted Array|LC34 - Find First and Last Position]]
