---
tags: [concept/algorithms, concept/sorting, concept/divide_and_conquer, pattern/recursion, pattern/merge_sort]
aliases: [Merge Sort Algorithm, 归并排序]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树的拓展延伸/拓展：归并排序详解及应用.md]].
> Merge Sort is a classic divide-and-conquer sorting algorithm, often related to post-order traversal of a conceptual recursion tree.

# Merge Sort Algorithm

Merge Sort is an efficient, stable, comparison-based sorting algorithm. It follows the [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer]] paradigm.

## 🌀 Core Idea
1.  **Divide:** If the list has more than one element, divide it into two (approximately) equal halves.
2.  **Conquer:** Recursively sort each half using Merge Sort.
3.  **Combine:** Merge the two sorted halves into a single sorted list.

Labuladong emphasizes viewing this process through the lens of a [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|binary tree's post-order traversal]]: the "merge" step (processing the current "node") happens after the recursive calls to sort the left and right "subtrees" (sub-arrays) have completed.

**Conceptual Recursion Tree for Merge Sort on `[3,1,4,2]`:**
```mermaid
graph TD
    Sort_3142["Sort([3,1,4,2])"] --> Sort_31["Sort([3,1])"]
    Sort_3142 --> Sort_42["Sort([4,2])"]

    Sort_31 --> Sort_3["Sort([3]) -> [3]"]
    Sort_31 --> Sort_1["Sort([1]) -> [1]"]
    Sort_31 -- Merge --> Merge_13["Merge([3],[1]) -> [1,3]"]

    Sort_42 --> Sort_4["Sort([4]) -> [4]"]
    Sort_42 --> Sort_2["Sort([2]) -> [2]"]
    Sort_42 -- Merge --> Merge_24["Merge([4],[2]) -> [2,4]"]

    Sort_3142 -- Merge --> Merge_1234["Merge([1,3],[2,4]) -> [1,2,3,4]"]

    style Sort_3 fill:#lightgreen
    style Sort_1 fill:#lightgreen
    style Sort_4 fill:#lightgreen
    style Sort_2 fill:#lightgreen
    style Merge_13 fill:#lightblue
    style Merge_24 fill:#lightblue
    style Merge_1234 fill:#lightcoral
```
The base cases are single-element arrays, which are already sorted. The `Merge` operations happen as the recursion unwinds (post-order).

## 🛠️ Implementation Details

A temporary array is typically used during the merge step to hold the elements while they are being combined.

### Python Implementation
```python
class MergeSort:
    def sortArray(self, nums: list[int]) -> list[int]:
        self.temp = [0] * len(nums) # Pre-allocate temp array for merging
        self._merge_sort(nums, 0, len(nums) - 1)
        return nums

    def _merge_sort(self, nums: list[int], lo: int, hi: int):
        if lo >= hi: # Base case: subarray of size 0 or 1 is already sorted
            return

        mid = lo + (hi - lo) // 2

        # Sort left half
        self._merge_sort(nums, lo, mid)
        # Sort right half
        self._merge_sort(nums, mid + 1, hi)

        # Merge the two sorted halves
        # This is the "post-order" position logic
        self._merge(nums, lo, mid, hi)

    def _merge(self, nums: list[int], lo: int, mid: int, hi: int):
        # Copy nums[lo..hi] to temp array
        for i in range(lo, hi + 1):
            self.temp[i] = nums[i]

        # Merge back to nums[lo..hi]
        i, j = lo, mid + 1 # Pointers for left and right halves in temp

        for p in range(lo, hi + 1): # Pointer for nums array
            if i > mid: # Left half exhausted, copy from right
                nums[p] = self.temp[j]
                j += 1
            elif j > hi: # Right half exhausted, copy from left
                nums[p] = self.temp[i]
                i += 1
            elif self.temp[i] > self.temp[j]: # temp[j] is smaller
                nums[p] = self.temp[j]
                j += 1
            else: # temp[i] is smaller or equal
                nums[p] = self.temp[i]
                i += 1
```

## Complexity Analysis
-   **Time Complexity:** $O(N \log N)$ for all cases (worst, average, best).
    - The recurrence relation is $T(N) = 2T(N/2) + O(N)$ (for merging).
    - The recursion tree has $\log N$ levels.
    - At each level, $O(N)$ work is done in total for merging.
-   **Space Complexity:** $O(N)$ due to the temporary array used for merging. (Some in-place merge strategies exist but are complex and often less efficient in practice). If considering recursion stack, it's $O(\log N)$.

## Applications of Merge Sort Logic
The "merge" part of Merge Sort is powerful and can be adapted to solve other problems, particularly those involving "counting inversions" or related pair-counting tasks in an array. When merging two sorted subarrays, you can efficiently compare elements from both halves.

Examples mentioned by Labuladong (requiring a similar merge-while-counting logic):
-   [[Interview/Practice/LeetCode/LC315 - Count of Smaller Numbers After Self|LC315 - Count of Smaller Numbers After Self]]
-   [[Interview/Practice/LeetCode/LC327 - Count of Range Sum|LC327 - Count of Range Sum]]
-   [[Interview/Practice/LeetCode/LC493 - Reverse Pairs|LC493 - Reverse Pairs]]

These problems often use the merge sort structure: during the `_merge` step, as you compare elements from the left and right sorted halves, you can deduce counts or relationships.

## 总结 (Summary)
- Merge Sort is a stable $O(N \log N)$ sorting algorithm based on Divide and Conquer.
- It recursively divides the array, sorts subarrays, and then merges them.
- The merging process is key and takes $O(N)$ time for a combined size of $N$.
- Requires $O(N)$ auxiliary space for the temporary array used in merging.
- The divide-and-conquer structure and the merge step itself are adaptable for solving complex counting problems on arrays.

---
Parent: [[Interview/Concept/Algorithms/Sorting/index|Sorting Algorithms Index]]
Related Concepts: [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer]], [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Post-order Traversal]] (analogy)
Related Problems: [[Interview/Practice/LeetCode/LC912 - Sort an Array|LC912 - Sort an Array]], [[Interview/Practice/LeetCode/LC315 - Count of Smaller Numbers After Self|LC315]], [[Interview/Practice/LeetCode/LC327 - Count of Range Sum|LC327]], [[Interview/Practice/LeetCode/LC493 - Reverse Pairs|LC493]]
