---
tags: [concept/algorithms, concept/sorting, concept/divide_and_conquer, pattern/recursion, pattern/quick_sort]
aliases: [Quick Sort Algorithm, 快速排序]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/二叉树的拓展延伸/拓展：快速排序详解及应用.md]].
> Quick Sort is another divide-and-conquer sorting algorithm, often related to pre-order traversal of a conceptual recursion tree.

# Quick Sort Algorithm

Quick Sort is an efficient, in-place, comparison-based sorting algorithm that also uses the [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer]] strategy.

## 🌀 Core Idea
1.  **Choose a Pivot:** Select an element from the array as a pivot.
2.  **Partition:** Rearrange the array such that all elements smaller than the pivot come before it, and all elements greater than the pivot come after it. After partitioning, the pivot is in its final sorted position.
3.  **Conquer:** Recursively apply Quick Sort to the sub-array of elements smaller than the pivot and the sub-array of elements greater than the pivot.

Labuladong views this process through the lens of a [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|binary tree's pre-order traversal]]: the `partition` step (processing the current "node"/array segment by placing the pivot correctly) happens *before* the recursive calls to sort the left and right "subtrees" (sub-arrays).

**Labuladong's one-sentence summary:** "Quick sort first sorts one element (the pivot) correctly, then sorts the remaining elements."

**Conceptual Recursion Tree for Quick Sort:**
The `partition` function places `nums[p]` correctly. The `sort` calls then operate on `nums[lo..p-1]` and `nums[p+1..hi]`.

```mermaid
graph TD
    Sort_Main["sort(nums, lo, hi)"] -- "partition() -> p" --> PivotPlaced["Pivot nums[p] is in final sorted place"]
    PivotPlaced --> Sort_Left["sort(nums, lo, p-1)"]
    PivotPlaced --> Sort_Right["sort(nums, p+1, hi)"]

    Sort_Left -- ... --> Sorted_Left_Subarray
    Sort_Right -- ... --> Sorted_Right_Subarray

    style PivotPlaced fill:#lightgreen
```

## 🛠️ Implementation Details

The `partition` function is the heart of Quick Sort. A common scheme (like Hoare's or Lomuto's):

### Lomuto Partition Scheme (Example)
- Choose the last element `nums[hi]` as the pivot.
- `i` (initially `lo - 1`) tracks the index of the last element smaller than the pivot.
- Iterate with `j` from `lo` to `hi-1`:
    - If `nums[j] <= pivot`, increment `i` and swap `nums[i]` with `nums[j]`.
- Finally, swap `nums[i+1]` with `nums[hi]` (the pivot). Return `i+1` (pivot's final index).

### Python Implementation (using Lomuto partition)
```python
import random

class QuickSort:
    def sortArray(self, nums: list[int]) -> list[int]:
        self._shuffle(nums) # Shuffle to improve average-case performance, avoid worst-case on sorted/reverse-sorted
        self._quick_sort(nums, 0, len(nums) - 1)
        return nums

    def _shuffle(self, nums: list[int]):
        # Fisher-Yates shuffle
        n = len(nums)
        for i in range(n -1, 0, -1):
            j = random.randint(0, i)
            nums[i], nums[j] = nums[j], nums[i]

    def _quick_sort(self, nums: list[int], lo: int, hi: int):
        if lo >= hi: # Base case: subarray of size 0 or 1
            return

        # Partition nums[lo..hi] such that nums[lo..p-1] <= nums[p] < nums[p+1..hi]
        # This is the "pre-order" position logic
        p = self._partition(nums, lo, hi)

        # Recursively sort left and right subarrays
        self._quick_sort(nums, lo, p - 1)
        self._quick_sort(nums, p + 1, hi)

    def _partition(self, nums: list[int], lo: int, hi: int) -> int:
        # Lomuto partition scheme with nums[lo] as pivot
        # (To use nums[hi] as pivot, one would iterate j from lo to hi-1, 
        # and swap pivot with nums[i+1] at the end.
        # Here, using nums[lo] as pivot for simplicity in this example matching Labuladong's structure.)

        # To better handle already sorted or reverse sorted arrays, 
        # it's good practice to pick a random pivot or median-of-three, then swap it to nums[lo].
        # For this example, let's stick to nums[lo] as pivot directly.
        pivot = nums[lo]

        # This implementation of partition places elements <= pivot to the left
        # and elements > pivot to the right.
        # `i` points to the end of the <= pivot section.
        # `j` scans the array.
        # Example: two-pointer scan from both ends (Hoare-like, simplified)
        # Or, a more common Lomuto if pivot is nums[hi]:
        # pivot = nums[hi]
        # i = lo - 1 
        # for j in range(lo, hi):
        #    if nums[j] <= pivot:
        #        i += 1
        #        nums[i], nums[j] = nums[j], nums[i]
        # nums[i+1], nums[hi] = nums[hi], nums[i+1]
        # return i + 1

        # Labuladong's partition often uses two pointers `i` and `j` scanning from `lo+1` and `hi`
        # towards each other, swapping elements that are on the wrong side of the pivot `nums[lo]`.

        # Let's use a common in-place partition (Hoare-like variant):
        i, j = lo + 1, hi
        while True:
            while i <= hi and nums[i] < pivot: # Find item on left to swap
                i += 1
            while j >= lo + 1 and nums[j] > pivot: # Find item on right to swap
                j -= 1

            if i >= j: # Pointers crossed
                break

            # Swap nums[i] and nums[j]
            nums[i], nums[j] = nums[j], nums[i]
            i += 1 # Move pointers after swap to continue search
            j -= 1

        # Place pivot in its final position
        # Elements from lo+1 to j are <= pivot. Element at j is the last one that is <= pivot (or pivot itself if no smaller found)
        # Swap pivot (nums[lo]) with nums[j]
        nums[lo], nums[j] = nums[j], nums[lo]
        return j # Return pivot's final index
```
*Note: The `_partition` implementation above is a common Hoare-style partition. Labuladong's visual GIF (`![](/algo/images/quick-select/1.jpeg)`) might show a slightly different scheme (e.g., Lomuto or specific two-pointer movement). The core idea of placing a pivot correctly remains.*

## Complexity Analysis
-   **Time Complexity:**
    -   **Average Case:** $O(N \log N)$. This occurs when partitions are reasonably balanced. The recurrence is $T(N) = 2T(N/2) + O(N)$ (for partitioning).
    -   **Worst Case:** $O(N^2)$. This occurs when the pivot selection consistently leads to highly unbalanced partitions (e.g., picking the smallest or largest element as pivot in an already sorted or reverse-sorted array). The recurrence becomes $T(N) = T(N-1) + O(N)$.
    -   Shuffling the array initially or using a randomized pivot selection strategy (or median-of-three) helps make the worst-case highly unlikely.
-   **Space Complexity:** $O(\log N)$ on average for the recursion stack (due to balanced partitions). $O(N)$ in the worst case for the recursion stack (skewed partitions). Quick Sort is considered in-place as it doesn't require $O(N)$ auxiliary array like Merge Sort for the data itself.

## Applications of Partition Logic
The `partition` subroutine is very powerful and is the core of the **Quickselect** algorithm, used to find the k-th smallest (or largest) element in an unsorted array in $O(N)$ average time.
- Example: [[Interview/Practice/LeetCode/LC215 - Kth Largest Element in an Array|LC215 - Kth Largest Element in an Array]]

## 总结 (Summary)
- Quick Sort is an in-place $O(N \log N)$ average-case sorting algorithm using Divide and Conquer.
- It works by partitioning the array around a pivot, placing the pivot in its sorted position, and then recursively sorting the subarrays.
- Worst-case time complexity is $O(N^2)$, but this can be mitigated by good pivot selection strategies (e.g., randomization).
- The `partition` logic is a key component and is also used in algorithms like Quickselect.
- The recursive structure can be viewed as a pre-order traversal of the problem space.

---
Parent: [[Interview/Concept/Algorithms/Sorting/index|Sorting Algorithms Index]]
Related Concepts: [[Interview/Concept/Algorithms/Divide and Conquer/00 - Divide and Conquer - Framework|Divide and Conquer]], [[Interview/Concept/Algorithms/Tree Traversal/00 - Binary Tree Recursive Traversal (DFS)|Pre-order Traversal]] (analogy)
Related Problems: [[Interview/Practice/LeetCode/LC912 - Sort an Array|LC912 - Sort an Array]], [[Interview/Practice/LeetCode/LC215 - Kth Largest Element in an Array|LC215 - Kth Largest Element]]
