---
tags: [index, concept/algorithms, concept/sorting]
aliases: [Sorting Algorithms Index, Sort Algorithms]
---

# Sorting Algorithms

This section covers various sorting algorithms, their principles, and complexities.

## Comparison Sorts:
- [[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]] ($O(N \log N)$ time, $O(N)$ space)
- [[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]] ($O(N \log N)$ average time, $O(\log N)$ average space for stack)
- [[Interview/Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization|Heap Sort]] (uses [[Interview/Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization|Binary Heap]], $O(N \log N)$ time, $O(1)$ space for in-place version)
- `[[Interview/Concept/Algorithms/Sorting/Bubble Sort|Bubble Sort]]` (Placeholder, $O(N^2)$)
- `[[Interview/Concept/Algorithms/Sorting/Insertion Sort|Insertion Sort]]` (Placeholder, $O(N^2)$)
- `[[Interview/Concept/Algorithms/Sorting/Selection Sort|Selection Sort]]` (Placeholder, $O(N^2)$)

## Non-Comparison Sorts (Linear Time Potential):
- `[[Interview/Concept/Algorithms/Sorting/Counting Sort|Counting Sort]]` (Placeholder, $O(N+K)$)
- `[[Interview/Concept/Algorithms/Sorting/Radix Sort|Radix Sort]]` (Placeholder, $O(D(N+K))$)
- `[[Interview/Concept/Algorithms/Sorting/Bucket Sort|Bucket Sort]]` (Placeholder, $O(N+K)$ average)


## Visualization
```mermaid
graph TD
    SA["Sorting Algorithms"] --> CompS["Comparison Sorts"]
    SA --> NonCompS["Non-Comparison Sorts"]

    CompS --> MergeS["[[Interview/Concept/Algorithms/Sorting/Merge Sort|Merge Sort]]"]
    CompS --> QuickS["[[Interview/Concept/Algorithms/Sorting/Quick Sort|Quick Sort]]"]
    CompS --> HeapS["Heap Sort"]
    CompS --> OthersSimple["(Bubble, Insertion, Selection)"]

    NonCompS --> CountingS["(Counting Sort)"]
    NonCompS --> RadixS["(Radix Sort)"]
    NonCompS --> BucketS["(Bucket Sort)"]

    classDef main fill:#f0e6ff,stroke:#8a2be2,stroke-width:2px;
    class SA main;
```

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
