---
tags: [concept/algorithms, concept/caching, concept/data_structures, concept/hash_table, concept/linked_list, pattern/lru, course/labuladong]
aliases: [LRU Cache Algorithm, Least Recently Used Cache, LRU 原理]
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/算法就像搭乐高：手撸 LRU 算法.md|算法就像搭乐高：手撸 LRU 算法 by Labuladong]].
> This note explains the design and implementation of an LRU (Least Recently Used) Cache.

# LRU (Least Recently Used) Cache Algorithm

The LRU Cache is a common caching strategy that evicts the least recently used items first when the cache reaches its capacity. This requires efficient ways to:
1.  Access items (get).
2.  Add/update items (put).
3.  Track the recency of use.
4.  Evict the least recently used item when capacity is full.

All these operations, ideally `get` and `put`, should be performed in $O(1)$ time complexity.

## 🎯 Core Idea: HashMap + Doubly Linked List

To achieve $O(1)$ for both lookup (like a hash map) and ordered removal/update of recency (like a list), the LRU cache combines two data structures:
1.  **Hash Map (`map`):** Stores `key -> DoublyLinkedListNode` pairs. This allows $O(1)$ average time lookup of any node in the linked list by its key.
2.  **Doubly Linked List (`cache`):** Stores `DoublyLinkedListNode` objects. Each node contains `(key, value)`. The list maintains the order of usage:
    - Most recently used items are at one end (e.g., tail).
    - Least recently used items are at the other end (e.g., head).

```mermaid
graph LR
    subgraph "LRU Cache Internals"
        HashMap["Hash Map (map)\nkey -> Node_reference"]
        CacheList["Doubly Linked List (cache)\n[LRU] Node_A <-> Node_B <-> Node_C [MRU]"]

        HashMap -.-> CacheList
    end
    CacheList_Node["Node in List:\n- key\n- value\n- prev_ptr\n- next_ptr"]
```
Labuladong's visual for this: `![](/algo/images/lru/4.jpg)`

## 🛠️ Data Structures Implementation

### `Node` for Doubly Linked List
Each node in the doubly linked list stores the key, value, and pointers to the previous and next nodes.
```python
class Node:
    def __init__(self, key: int, val: int):
        self.key = key
        self.val = val
        self.next = None
        self.prev = None
```

### `DoubleList` Class
This class manages the doubly linked list with dummy head and tail nodes to simplify boundary conditions.
- `addLast(node)`: Adds a node to the tail (most recently used).
- `remove(node)`: Removes a node from its current position.
- `removeFirst()`: Removes the node from the head (least recently used).
- `size()`: Returns the current number of nodes.

```python
class DoubleList:
    def __init__(self):
        self.head = Node(0, 0) # Dummy head
        self.tail = Node(0, 0) # Dummy tail
        self.head.next = self.tail
        self.tail.prev = self.head
        self._size = 0

    def addLast(self, x: Node): # Add to tail
        x.prev = self.tail.prev
        x.next = self.tail
        self.tail.prev.next = x
        self.tail.prev = x
        self._size += 1

    def remove(self, x: Node):
        x.prev.next = x.next
        x.next.prev = x.prev
        self._size -= 1

    def removeFirst(self) -> Node | None: # Remove from head
        if self.head.next == self.tail: # Empty
            return None
        first = self.head.next
        self.remove(first)
        return first

    def size(self) -> int:
        return self._size
```
Using a dummy head and tail simplifies `addLast` and `removeFirst` as they don't need to handle null checks for `head` or `tail` themselves being the elements.

## `LRUCache` Class Implementation

The `LRUCache` class combines the hash map and the `DoubleList`.

```python
class LRUCache:
    def __init__(self, capacity: int):
        self.cap = capacity
        self.map = {}  # Stores key -> Node
        self.cache = DoubleList() # Stores Node objects

    # Helper: Make a key recently used by moving its node to the tail of the list
    def _make_recently(self, key: int):
        node = self.map[key]
        self.cache.remove(node)
        self.cache.addLast(node)

    # Helper: Add a new key-value pair as recently used
    def _add_recently(self, key: int, val: int):
        new_node = Node(key, val)
        self.cache.addLast(new_node)
        self.map[key] = new_node

    # Helper: Delete a key (and its node)
    def _delete_key(self, key: int):
        node_to_delete = self.map[key]
        self.cache.remove(node_to_delete)
        del self.map[key]

    # Helper: Remove the least recently used item
    def _remove_least_recently(self):
        deleted_node = self.cache.removeFirst()
        if deleted_node:
            del self.map[deleted_node.key]

    def get(self, key: int) -> int:
        if key not in self.map:
            return -1
        # Make key recently used
        self._make_recently(key)
        return self.map[key].val

    def put(self, key: int, value: int) -> None:
        if key in self.map:
            # Key exists, update value and make it recently used
            self._delete_key(key) # Delete old entry
            self._add_recently(key, value) # Add as new recent entry
            return

        # Key does not exist
        if self.cache.size() >= self.cap:
            # Cache is full, remove least recently used
            self._remove_least_recently()

        # Add new key-value as recently used
        self._add_recently(key, value)
```

**Logic Explanation:**
- **`get(key)`:**
    1. If `key` not in `map`, return -1.
    2. If `key` exists, retrieve its `Node` from `map`.
    3. Move this `Node` to the tail of `cache` (marking it as most recently used) using `_make_recently`.
    4. Return `Node.val`.
- **`put(key, value)`:** (Labuladong's diagram: `![](/algo/images/lru/put.jpg)`)
    1. If `key` exists in `map`:
        a. Update `Node.val`.
        b. Move this `Node` to the tail of `cache` using `_make_recently`. (Labuladong's provided code in the LRU article actually deletes and re-adds for `put` update, which also works and is slightly simpler to implement helpers for).
    2. If `key` does not exist:
        a. If `cache.size()` equals `capacity`:
            i. Remove the least recently used item (head of `cache`) using `_remove_least_recently`. This also removes it from `map`.
        b. Create a new `Node(key, value)`.
        c. Add this new `Node` to the tail of `cache` and to `map` using `_add_recently`.

**Why store `key` in `DoublyLinkedListNode`?**
When `_remove_least_recently()` is called, it removes a node from the `cache` (the doubly linked list). To also remove the corresponding entry from the `map`, we need the key associated with that list node. Storing the `key` within the `Node` object allows us to retrieve it and then delete `map[node.key]`.

## ⚙️ Using Java's `LinkedHashMap` (Conceptual Adaptation for Python)
Java's `LinkedHashMap` can be configured to maintain access order, making it a direct fit for LRU cache implementation.
```java
// Java example from Labuladong
// class LRUCache {
//     int cap;
//     LinkedHashMap<Integer, Integer> cache = new LinkedHashMap<>();
//     public LRUCache(int capacity) { this.cap = capacity; }

//     public int get(int key) {
//         if (!cache.containsKey(key)) { return -1; }
//         makeRecently(key); // Moves key to end of internal list
//         return cache.get(key);
//     }

//     public void put(int key, int val) {
//         if (cache.containsKey(key)) {
//             cache.put(key, val);
//             makeRecently(key);
//             return;
//         }
//         if (cache.size() >= this.cap) {
//             int oldestKey = cache.keySet().iterator().next(); // Oldest is at head
//             cache.remove(oldestKey);
//         }
//         cache.put(key, val); // Newest is at tail
//     }

//     private void makeRecently(int key) {
//         int val = cache.get(key);
//         cache.remove(key); // Remove and re-insert to move to tail
//         cache.put(key, val);
//     }
// }
```
Python's `collections.OrderedDict` can also be used similarly if you manage `move_to_end()` and popping from the beginning (`popitem(last=False)`). Python's standard `dict` (since 3.7) preserves insertion order, but moving items to reflect recent use requires `del` and re-insertion.

## Complexity Analysis
- **`get(key)`:** $O(1)$ on average. Hash map lookup is $O(1)$, list operations (`remove` and `addLast` on `DoubleList` given node reference) are $O(1)$.
- **`put(key, value)`:** $O(1)$ on average. Hash map operations are $O(1)$, `DoubleList` operations are $O(1)$.
- **Space Complexity:** $O(capacity)$ for storing `capacity` key-value pairs in both the hash map and the doubly linked list.

## 总结 (Summary)
- LRU Cache evicts the least recently used item.
- It's typically implemented using a hash map (for $O(1)$ key lookup) and a doubly linked list (for $O(1)$ addition/removal of items to track recency).
- Helper functions like `_make_recently`, `_add_recently`, `_delete_key`, `_remove_least_recently` encapsulate operations on both data structures, making the main `get` and `put` methods cleaner and less error-prone.
- All core operations (`get`, `put`) achieve $O(1)$ average time complexity.

---
Parent: [[Interview/Concept/Algorithms/Caching/index|Caching Algorithms]]
Related Problem: [[Interview/Practice/LeetCode/LC146 - LRU Cache|LC146 - LRU Cache]]
Related Concepts: [[Interview/Concept/Data Structures/Hash Map/00 - Hash Map - Core Principles|Hash Map]], [[Interview/Concept/Data Structures/Linked List/index|Doubly Linked List]], [[Interview/Concept/Data Structures/Hash Map/03 - LinkedHashMap - Maintaining Insertion Order|LinkedHashMap (Concept)]]
