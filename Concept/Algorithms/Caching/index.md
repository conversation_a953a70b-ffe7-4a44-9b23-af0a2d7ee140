---
tags: [index, concept/algorithms, concept/caching, pattern/caching]
aliases: [Caching Algorithms Index, Cache Replacement Policies]
---

# Caching Algorithms

This section covers common cache replacement algorithms and their implementations, often encountered in system design or advanced data structure problems.

## Core Caching Algorithms:
- [[Interview/Concept/Algorithms/Caching/LRU Cache|LRU (Least Recently Used) Cache]]
  - Evicts the least recently accessed item.
  - Typically implemented with a HashMap and a Doubly Linked List.
- [[Interview/Concept/Algorithms/Caching/LFU Cache|LFU (Least Frequently Used) Cache]]
  - Evicts the least frequently accessed item.
  - Ties broken by LRU (evict oldest among least frequent).
  - More complex implementation, often involving multiple HashMaps and potentially linked lists within frequency groups.

## Visualization
```mermaid
graph TD
    CacheAlgos["Caching Algorithms"] --> LRU["[[Interview/Concept/Algorithms/Caching/LRU Cache|LRU Cache]]"]
    CacheAlgos --> LFU["[[Interview/Concept/Algorithms/Caching/LFU Cache|LFU Cache]]"]

    LRU --> LRU_Impl["(HashMap + DoublyLinkedList)"]
    LFU --> LFU_Impl["(Multiple HashMaps + OrderedDicts/LinkedHashSets)"]

    classDef main fill:#e6ffe6,stroke:#006400,stroke-width:2px;
    class CacheAlgos main;
```

## Related LeetCode Problems:
- [[Interview/Practice/LeetCode/LC146 - LRU Cache|LC146 - LRU Cache]]
- [[Interview/Practice/LeetCode/LC460 - LFU Cache|LC460 - LFU Cache]]

---
Parent: [[Interview/Concept/Algorithms/index|Algorithms Concepts Index]]
