---
tags: [concept/algorithms, concept/caching, concept/data_structures, concept/hash_table, concept/linked_list, pattern/lfu, course/labuladong]
aliases: [LFU Cache Algorithm, Least Frequently Used Cache, LFU 原理]
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/算法就像搭乐高：手撸 LFU 算法.md|算法就像搭乐高：手撸 LFU 算法 by Labuladong]].
> This note explains the design and implementation of an LFU (Least Frequently Used) Cache.

# LFU (Least Frequently Used) Cache Algorithm

The LFU Cache is a caching strategy that evicts the least frequently used items first when the cache reaches its capacity. If multiple items have the same minimum frequency, the least recently used (oldest) among them is evicted. This makes LFU more complex than LRU.

Core operations (`get` and `put`) should ideally be $O(1)$ time complexity.

## 🎯 Core Idea: Tracking Frequency and Recency

To implement LFU, we need to efficiently:
1.  Track the frequency of use for each key.
2.  For a given frequency, track the recency of use of keys with that frequency (to break ties).
3.  Quickly find and evict the key with the minimum frequency (and oldest if tie).
4.  Update a key's frequency and its position within its frequency group upon access.

Labuladong's solution uses a combination of three data structures:
1.  **`keyToVal` (HashMap):** Stores `key -> value` mappings for quick value retrieval.
2.  **`keyToFreq` (HashMap):** Stores `key -> frequency` mappings.
3.  **`freqToKeys` (HashMap):** Stores `frequency -> LinkedHashSet<key>` mappings. A `LinkedHashSet` is used because:
    - It stores keys with a specific frequency.
    - It maintains insertion order, so we can easily identify the oldest key (first inserted) within this frequency group for tie-breaking.
    - It allows $O(1)$ average time for adding/removing keys.

Additionally, a variable `minFreq` tracks the current minimum frequency in the cache.

```mermaid
graph TD
    subgraph "LFU Cache Internals"
        KTV["keyToVal (HashMap)\nkey -> value"]
        KTF["keyToFreq (HashMap)\nkey -> frequency"]
        FTK["freqToKeys (HashMap)\nfrequency -> LinkedHashSet<key>"]
        MinF["minFreq (int)\nTracks minimum frequency"]
    end

    FTK_Example["Example freqToKeys[f]:\n{keyA (oldest), keyB, keyC (newest)}"]
    FTK --> FTK_Example
```

## 🛠️ Data Structures & Logic Details

### `keyToVal: HashMap<Integer, Integer>`
- Stores the actual key-value pairs.
- `map.get(key)` returns value.

### `keyToFreq: HashMap<Integer, Integer>`
- Stores the frequency of each key.
- `map.get(key)` returns frequency.

### `freqToKeys: HashMap<Integer, LinkedHashSet<Integer>>`
- Maps a frequency `f` to a set of keys that currently have frequency `f`.
- The `LinkedHashSet` ensures that:
    - We can get the "oldest" key (least recently used within this frequency) by iterating from the beginning.
    - Adding/removing keys from this set is $O(1)$ on average.
- `map.get(freq)` returns the `LinkedHashSet` of keys.

### `minFreq: int`
- Stores the minimum frequency currently present in the cache. This is crucial for eviction: when the cache is full, we look at `freqToKeys.get(minFreq)` and remove the oldest key from that set.

## Helper Function: `increaseFreq(key)`
This function is called whenever a key is accessed (`get` or `put`).
1. Get `freq = keyToFreq.get(key)`.
2. Update `keyToFreq.put(key, freq + 1)`.
3. Remove `key` from `freqToKeys.get(freq)`.
4. Add `key` to `freqToKeys.get(freq + 1)` (create this set if it doesn't exist).
5. If `freqToKeys.get(freq)` becomes empty, remove `freq` from `freqToKeys`.
6. If `freq == minFreq` AND `freqToKeys.get(freq)` is now empty, then `minFreq` must be updated to `freq + 1`.

## `LFUCache` Class Operations

### `get(key)`
1. If `key` not in `keyToVal`, return -1.
2. Call `increaseFreq(key)`.
3. Return `keyToVal.get(key)`.

### `put(key, value)`
1. If `capacity <= 0`, do nothing.
2. If `key` in `keyToVal`:
    a. Update `keyToVal.put(key, value)`.
    b. Call `increaseFreq(key)`.
    c. Return.
3. **If key does not exist (new key):**
    a. If cache size (`keyToVal.size()`) is already at `capacity`:
        i. Call `removeMinFreqKey()`.
    b. Add new key:
        i. `keyToVal.put(key, value)`.
        ii. `keyToFreq.put(key, 1)`.
        iii. Add `key` to `freqToKeys.get(1)` (create set for freq 1 if needed).
        iv. Set `minFreq = 1`.

### `removeMinFreqKey()`
1. Get `keyList = freqToKeys.get(minFreq)`.
2. Get the oldest key from `keyList` (e.g., `keyList.iterator().next()`). Let it be `key_to_delete`.
3. Remove `key_to_delete` from `keyList`.
4. If `keyList` becomes empty, remove `minFreq` from `freqToKeys` (no keys left with this min frequency).
   (Note: `minFreq` update logic is subtle. It might not need an update here if a new key is added with freq 1, which becomes the new `minFreq`. The `minFreq` typically updates when a key's freq increases from `minFreq` and that was the only key at `minFreq`, or when a new key is added at freq 1 making it the new `minFreq`.)
5. Remove `key_to_delete` from `keyToVal` and `keyToFreq`.

## Python Implementation Sketch
Python doesn't have a built-in `LinkedHashSet`. We can simulate it with `collections.OrderedDict` (using keys as a set, values as dummy) or a combination of a `dict` (for $O(1)$ presence check) and a `collections.deque` (for $O(1)$ add/remove from ends to maintain order). For LFU, `collections.OrderedDict` is more straightforward.
The `freqToKeys` would map `freq -> OrderedDict()`. The oldest item in an `OrderedDict` is `next(iter(od.keys()))` or `od.popitem(last=False)`.

```python
import collections

class LFUCache:
    def __init__(self, capacity: int):
        self.capacity = capacity
        self.key_to_val = {}
        self.key_to_freq = {}
        # freq_to_keys maps frequency to an OrderedDict of {key: None}
        # OrderedDict maintains insertion order, so keys are ordered by recency for that freq
        self.freq_to_keys = collections.defaultdict(collections.OrderedDict)
        self.min_freq = 0 # Tracks the current minimum frequency

    def _increase_freq(self, key: int):
        freq = self.key_to_freq[key]

        # Remove key from current frequency list
        del self.freq_to_keys[freq][key] 
        if not self.freq_to_keys[freq]: # If list becomes empty
            del self.freq_to_keys[freq]
            # If this was the min_freq, update min_freq
            if freq == self.min_freq:
                self.min_freq += 1 # This key moved to freq+1, so min_freq might increase
                                    # Needs careful update if other keys still at min_freq

        # Add key to new frequency list (freq + 1)
        new_freq = freq + 1
        self.freq_to_keys[new_freq][key] = None # Value in OrderedDict doesn't matter
        self.key_to_freq[key] = new_freq

        # After increasing frequency, if the old min_freq list is now empty,
        # and no other list with min_freq exists, then min_freq may need to be incremented.
        # More robust min_freq update: after deleting from freq_to_keys[freq],
        # if freq == self.min_freq and not self.freq_to_keys[freq]:
        #    self.min_freq = new_freq (if no other list at freq became the new min_freq)
        # This part is tricky. `self.min_freq` should be updated if the set for `self.min_freq` becomes empty.
        # If `freq == self.min_freq` and `not self.freq_to_keys[freq]`:
        #   `self.min_freq = new_freq` (this assumes no other freq lists exist between `freq` and `new_freq`)
        #   This is not quite right. The new min_freq could be something else if `freqToKeys` has other entries.
        #   A simpler way: if `freq == self.min_freq` and `not self.freq_to_keys.get(freq)`: `self.min_freq += 1`.
        #   This still assumes `freq+1` is the next candidate.
        #   Correct `min_freq` update: if `freq == self.min_freq` and `freq` is no longer in `self.freq_to_keys`,
        #   `self.min_freq` becomes `new_freq` IF `new_freq` is now the smallest freq.
        #   However, if other keys exist at frequency `freq+1` already, `min_freq` could already be `freq+1`.
        #   The key is: if the set for `self.min_freq` becomes empty, `self.min_freq` has to increase.
        #   If `freq == self.min_freq` and not `self.freq_to_keys.get(self.min_freq)`: then `self.min_freq` points to an empty category.
        #   It should then be `new_freq` *unless* there was another list that was previously `self.min_freq + 1`, which would now be the minimum.
        #   This suggests `min_freq` might not always just increment.
        #   Labuladong's original Java code has `if (freq == minFreq && freqList.isEmpty()) { minFreq++; }`
        #   This implies that if a key moves from `minFreq` to `minFreq + 1`, and `minFreq`'s list becomes empty,
        #   then `minFreq + 1` is the new candidate for minimum frequency. This works because keys only increase in frequency.

        # Refined min_freq update logic in _increase_freq:
        if freq == self.min_freq and not self.freq_to_keys.get(freq):
            self.min_freq = new_freq # Or more generally, find the new min_freq if new_freq isn't it.
                                     # But given freq only increases, if `min_freq` list is empty,
                                     # the elements moved to `min_freq+1`. So new `min_freq` is at least `min_freq+1`.


    def get(self, key: int) -> int:
        if key not in self.key_to_val:
            return -1

        val = self.key_to_val[key]
        self._increase_freq(key)
        return val

    def put(self, key: int, value: int) -> None:
        if self.capacity <= 0:
            return

        if key in self.key_to_val:
            self.key_to_val[key] = value # Update value
            self._increase_freq(key)    # Increase frequency
            return

        # New key
        if len(self.key_to_val) >= self.capacity:
            # Cache is full, remove LFU key
            # Get the list of keys with min_freq
            keys_at_min_freq = self.freq_to_keys[self.min_freq]
            # Remove the oldest (first inserted) key from this list
            oldest_key_at_min_freq, _ = keys_at_min_freq.popitem(last=False) # Pop first item (oldest)

            # If this was the last key in this frequency list, remove the list itself
            if not keys_at_min_freq:
                del self.freq_to_keys[self.min_freq]
                # min_freq update is implicitly handled when new key is added with freq 1, 
                # or if _increase_freq handles it when the min_freq list becomes empty.
                # If we just removed the only list at min_freq, the new min_freq is NOT YET SET.

            del self.key_to_val[oldest_key_at_min_freq]
            del self.key_to_freq[oldest_key_at_min_freq]

        # Add the new key
        self.key_to_val[key] = value
        self.key_to_freq[key] = 1
        self.freq_to_keys[1][key] = None # Add to OrderedDict for freq 1
        self.min_freq = 1 # A new key always starts with freq 1, becoming the new min_freq
```

**Refinement on `min_freq` logic during `_increase_freq`:**
If a key `k` is moved from `freqList[minFreq]` to `freqList[minFreq+1]`, and `freqList[minFreq]` becomes empty, then `minFreq` itself should become `minFreq+1`. This is because there are no more keys with the old `minFreq`.
```python
# (inside _increase_freq)
# ...
# if not self.freq_to_keys[freq]:
#     del self.freq_to_keys[freq]
#     if freq == self.min_freq: # If the list for min_freq became empty
#         self.min_freq += 1    # The new min_freq is at least min_freq + 1
#                               # (because all keys moved from min_freq to min_freq + 1)
# ...
```
This makes `_increase_freq` correctly update `min_freq` if needed.

## Complexity Analysis
- **`get(key)`:** $O(1)$ on average. Hash map lookups, `OrderedDict` operations (if used for `LinkedHashSet` simulation) are $O(1)$ average.
- **`put(key, value)`:** $O(1)$ on average. Similar reasoning. `popitem(last=False)` for `OrderedDict` is $O(1)$.
- **Space Complexity:** $O(capacity)$ for storing key-value pairs and frequency information. Each key appears in `keyToVal`, `keyToFreq`, and one of the `OrderedDict`s in `freqToKeys`.

## 总结 (Summary)
- LFU Cache evicts the least frequently used item. If ties in frequency, it evicts the least recently used (oldest) of those.
- Implementation uses:
    - `keyToVal` (HashMap): For $O(1)$ value retrieval.
    - `keyToFreq` (HashMap): For $O(1)$ frequency retrieval/update.
    - `freqToKeys` (HashMap of Freq -> `LinkedHashSet` or `OrderedDict`): To group keys by frequency and maintain their insertion (recency) order within that frequency group.
    - `minFreq`: An integer to track the current minimum frequency for quick eviction.
- Operations `get` and `put` are $O(1)$ on average.
- The complexity lies in correctly updating these structures, especially `freqToKeys` and `minFreq`, when a key's frequency changes or when eviction occurs.

---
Parent: [[Interview/Concept/Algorithms/Caching/index|Caching Algorithms]]
Related Problem: [[Interview/Practice/LeetCode/LC460 - LFU Cache|LC460 - LFU Cache]]
Related Concepts: [[Interview/Concept/Data Structures/Hash Map/00 - Hash Map - Core Principles|Hash Map]], [[Interview/Concept/Data Structures/Linked List/index|Doubly Linked List]] (underlying `LinkedHashSet`/`OrderedDict`), [[Interview/Concept/Data Structures/Hash Map/03 - LinkedHashMap - Maintaining Insertion Order|LinkedHashMap (Concept for Ordered Key Sets)]]
