---
tags: [concept/data_structures, concept/tree, concept/trie, concept/prefix_tree, type/introduction]
aliases: [<PERSON><PERSON>, Prefix Tree, Dictionary Tree, 字典树, 前缀树]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/二叉树结构的种种变换/Trie_字典树_前缀树原理及可视化.md]].
> Labuladong positions Trie as an extension of [[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|N-ary tree]] structures, specifically optimized for string operations.

# Trie (Prefix Tree / Dictionary Tree): Principles and Visualization

A Trie, also known as a prefix tree or dictionary tree, is a specialized tree-like data structure used for efficient retrieval of keys in a dataset of strings. Each node in a Trie represents a character (or part of a character/string), and paths from the root to a node represent prefixes.

## 🌲 Core Concept: An N-ary Tree for Strings

A Trie can be seen as a specific type of [[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|N-ary tree]] where:
- Each node (except possibly the root) corresponds to a character.
- The children of a node are indexed by characters. For example, a node might have children for 'a', 'b', 'c', etc.
- Paths from the root to a particular node represent a prefix.
- Some nodes might be marked as the end of a complete key (string) stored in the Trie, often also storing an associated value if implementing a TrieMap.

**Visualization Example: Building a Trie Step by Step**

Let's visualize inserting "app", "apple", "appl" into a TrieMap:

```tikz
\begin{tikzpicture}[
    trie_node/.style={circle, draw, minimum size=1.2cm, font=\sffamily\small},
    root_node/.style={trie_node, fill=pink!30, font=\sffamily\bfseries},
    end_node/.style={trie_node, fill=lightgreen!40, double},
    edge_label/.style={font=\sffamily\scriptsize, midway, above},
    step_title/.style={font=\sffamily\bfseries, align=center}
]

% Step 1: Insert "app" (value=1)
\node[step_title] at (2, 4.5) {Step 1: Insert "app" → value=1};
\node[root_node] (root1) at (0, 3) {root};
\node[trie_node] (a1) at (2, 3) {a};
\node[trie_node] (p1) at (4, 3) {p};
\node[end_node] (pp1) at (6, 3) {p\\val=1};

\draw[->] (root1) -- (a1) node[edge_label] {a};
\draw[->] (a1) -- (p1) node[edge_label] {p};
\draw[->] (p1) -- (pp1) node[edge_label] {p};

% Step 2: Insert "apple" (value=2)
\node[step_title] at (2, 1.5) {Step 2: Insert "apple" → value=2};
\node[root_node] (root2) at (0, 0) {root};
\node[trie_node] (a2) at (2, 0) {a};
\node[trie_node] (p2) at (4, 0) {p};
\node[end_node] (pp2) at (6, 0) {p\\val=1};
\node[trie_node] (l2) at (8, 0) {l};
\node[end_node] (e2) at (10, 0) {e\\val=2};

\draw[->] (root2) -- (a2) node[edge_label] {a};
\draw[->] (a2) -- (p2) node[edge_label] {p};
\draw[->] (p2) -- (pp2) node[edge_label] {p};
\draw[->] (pp2) -- (l2) node[edge_label] {l};
\draw[->] (l2) -- (e2) node[edge_label] {e};

% Step 3: Insert "appl" (value=3)
\node[step_title] at (2, -1.5) {Step 3: Insert "appl" → value=3};
\node[root_node] (root3) at (0, -3) {root};
\node[trie_node] (a3) at (2, -3) {a};
\node[trie_node] (p3) at (4, -3) {p};
\node[end_node] (pp3) at (6, -3) {p\\val=1};
\node[end_node] (l3) at (8, -3) {l\\val=3};
\node[end_node] (e3) at (10, -3) {e\\val=2};

\draw[->] (root3) -- (a3) node[edge_label] {a};
\draw[->] (a3) -- (p3) node[edge_label] {p};
\draw[->] (p3) -- (pp3) node[edge_label] {p};
\draw[->] (pp3) -- (l3) node[edge_label] {l};
\draw[->] (l3) -- (e3) node[edge_label] {e};

% Legend
\node[step_title] at (12, 2) {Legend:};
\node[root_node] at (11, 1) {root};
\node at (13, 1) {Root node};
\node[trie_node] at (11, 0.2) {};
\node at (13.5, 0.2) {Character node};
\node[end_node] at (11, -0.6) {val};
\node at (14, -0.6) {End-of-word + value};

\end{tikzpicture}
```

**Key Observations:**
- Each node represents a character (except root)
- Green double-circled nodes mark complete words and store values
- Shared prefixes ("app") are stored only once, saving space
- Path from root to any green node spells a complete key

## 🔑 Key Applications and Advantages

### 1. Efficient Prefix Operations
Tries excel at operations involving prefixes:
- **`startsWith(prefix)` / `hasKeyWithPrefix(prefix)`:** Check if any stored key begins with the given prefix. Complexity: $O(L)$, where $L$ is the length of the prefix.
- **`keysWithPrefix(prefix)`:** Retrieve all keys that start with the given prefix. Complexity depends on the number of matching keys and their lengths, but finding the prefix node is $O(L)$. This is ideal for auto-completion features.
- **`shortestPrefixOf(string)`:** Find the shortest stored key that is a prefix of a given string.
- **`longestPrefixOf(string)`:** Find the longest stored key that is a prefix of a given string (useful in routing tables, for example).

These operations are much less efficient with standard [[Interview/Concept/Programming Languages/Python/05 - Python Dict (Hash Map) for Interviews|hash maps]] or [[Interview/Concept/Data Structures/Tree/Binary Search Tree/index|binary search trees]].

### 2. Space Efficiency for Common Prefixes
If many strings share common prefixes (e.g., "application", "apply", "appeal"), the Trie stores the common prefix "app" only once, saving space compared to storing each full string separately in a hash map.

**Comparison: Hash Map vs. Trie Storage**

```tikz
\begin{tikzpicture}[
    hash_entry/.style={rectangle, draw, fill=blue!20, minimum width=2cm, minimum height=0.8cm, font=\sffamily\small},
    trie_node/.style={circle, draw, minimum size=0.8cm, font=\sffamily\tiny},
    root_node/.style={trie_node, fill=pink!30, font=\sffamily\bfseries\tiny},
    end_node/.style={trie_node, fill=lightgreen!40, double},
    title/.style={font=\sffamily\bfseries, align=center}
]

% Hash Map representation
\node[title] at (2, 4) {Hash Map Storage};
\node[hash_entry] at (0, 3) {"app"};
\node[hash_entry] at (0, 2) {"apple"};
\node[hash_entry] at (0, 1) {"appl"};
\node at (3, 2) {Total: 12 characters\\(3+5+4)};

% Trie representation
\node[title] at (8, 4) {Trie Storage};
\node[root_node] (root) at (6, 2.5) {root};
\node[trie_node] (a) at (7.5, 2.5) {a};
\node[trie_node] (p1) at (9, 2.5) {p};
\node[end_node] (p2) at (10.5, 3.5) {p};
\node[end_node] (l) at (10.5, 2.5) {l};
\node[end_node] (e) at (10.5, 1.5) {e};

\draw[->] (root) -- (a) node[midway, above, font=\tiny] {a};
\draw[->] (a) -- (p1) node[midway, above, font=\tiny] {p};
\draw[->] (p1) -- (p2) node[midway, above left, font=\tiny] {p};
\draw[->] (p2) -- (l) node[midway, right, font=\tiny] {l};
\draw[->] (l) -- (e) node[midway, right, font=\tiny] {e};

\node at (8, 0.5) {Total: 5 unique characters\\Shared prefix "app" stored once};

% Space efficiency annotation
\draw[dashed, red, thick] (4.5, 0) rectangle (11.5, 4.5);
\node[title, red] at (8, -0.5) {58\% Space Savings!};

\end{tikzpicture}
```

**Analysis:**
- **Hash Map:** Stores 3 distinct string objects: "apple" (5 chars), "app" (3 chars), "appl" (4 chars) = 12 characters total
- **Trie:** Stores characters along paths. The shared "app" prefix is stored once. Total: 5 unique character nodes
- **Space Savings:** Especially significant with longer common prefixes

### 3. String Sorting (Implicit)
Traversing a Trie in a specific order (e.g., visiting children in alphabetical order) can retrieve all stored strings in lexicographical order.

### 4. Full-text Search and Pattern Matching
Tries and their variations (like Suffix Trees or Aho-Corasick automata) are fundamental in algorithms for searching patterns in text.

## 🔧 Trie Node Structure (Conceptual)

A common way to implement a Trie node:
```python
class TrieNode:
    def __init__(self):
        self.children = {}  # Maps character to TrieNode
        self.is_end_of_word = False # Flag to mark end of a word
        self.value = None # Optional: To store value if it's a TrieMap

# For a fixed alphabet (e.g., lowercase English letters):
# self.children = [None] * 26
```
- **`children`:** A dictionary (or array for fixed alphabets) mapping a character to the child `TrieNode` representing that character.
- **`is_end_of_word` (or similar flag):** A boolean indicating if the path from the root to this node forms a complete key stored in the Trie.
- **`value`:** If implementing a `TrieMap`, this field stores the value associated with the key. For a `TrieSet`, only `is_end_of_word` is needed.

## TrieMap vs. TrieSet
- **`TrieMap<ValueType>`:** Stores key-value pairs. Keys are strings. `TrieNode` would have a `value` field.
- **`TrieSet`:** Stores a set of unique strings. `TrieNode` would have an `is_end_of_word` flag. It's essentially a `TrieMap` where the value is a boolean or not explicitly stored if `is_end_of_word` suffices.

## Complexity
For a Trie storing $N$ keys, where $L_{avg}$ is the average key length and $L_{max}$ is the maximum key length:
- **Insertion:** $O(L_{avg})$ or $O(L_{max})$ for one key.
- **Search (for a key):** $O(L)$ where $L$ is the length of the key being searched.
- **Deletion:** $O(L)$.
- **Prefix-based operations (e.g., `startsWith`):** $O(P)$ where $P$ is the length of the prefix.
- **Space Complexity:** In the worst case, if there are no shared prefixes, it can be $O(\sum L_i)$ (sum of lengths of all keys), similar to storing strings directly. However, with significant prefix sharing, it's much better. The number of nodes is at most $\sum L_i$.

## 🛠️ Basic Operations (Conceptual for TrieMap)

**Visual Example: Operations on Trie containing {"app"→1, "apple"→2, "appl"→3}**

```tikz
\begin{tikzpicture}[
    trie_node/.style={circle, draw, minimum size=1cm, font=\sffamily\tiny},
    root_node/.style={trie_node, fill=pink!30, font=\sffamily\bfseries\tiny},
    end_node/.style={trie_node, fill=lightgreen!40, double},
    search_path/.style={very thick, red},
    prefix_path/.style={very thick, blue},
    operation_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\scriptsize, align=left}
]

% Main Trie structure
\node[root_node] (root) at (0, 0) {root};
\node[trie_node] (a) at (2, 0) {a};
\node[trie_node] (p1) at (4, 0) {p};
\node[end_node] (p2) at (6, 1) {p\\val=1};
\node[end_node] (l) at (6, 0) {l\\val=3};
\node[end_node] (e) at (6, -1) {e\\val=2};

\draw[->] (root) -- (a) node[midway, above, font=\tiny] {a};
\draw[->] (a) -- (p1) node[midway, above, font=\tiny] {p};
\draw[->] (p1) -- (p2) node[midway, above left, font=\tiny] {p};
\draw[->] (p2) -- (l) node[midway, right, font=\tiny] {l};
\draw[->] (l) -- (e) node[midway, right, font=\tiny] {e};

% Operation 1: Search for "app"
\node[operation_box] at (8, 2) {Search("app"):\\1. Follow a→p→p\\2. Reach end node\\3. Return value=1};
\draw[search_path] (root) -- (a) -- (p1) -- (p2);

% Operation 2: Search for "ap" (not found)
\node[operation_box] at (8, 0) {Search("ap"):\\1. Follow a→p\\2. Not end node\\3. Return null\\(prefix exists, key doesn't)};

% Operation 3: startsWith("ap")
\node[operation_box] at (8, -2) {startsWith("ap"):\\1. Follow a→p\\2. Path exists\\3. Return True\\(multiple keys start with "ap")};
\draw[prefix_path] (root) -- (a) -- (p1);

\end{tikzpicture}
```

### Insert (`put(key, value)`)
1. Start from the root.
2. For each character in the `key`:
   a. If the character does not exist as a child of the current node, create a new child node for it.
   b. Move to that child node.
3. After processing all characters, mark the final node as `is_end_of_word = True` and set its `value`.

### Search (`get(key)`)
1. Start from the root.
2. For each character in the `key`:
   a. If the character does not exist as a child of the current node, the `key` is not in the Trie. Return `null` or raise an error.
   b. Move to that child node.
3. After processing all characters, if the final node is marked `is_end_of_word`, return its `value`. Otherwise, the `key` is a prefix of another key but not a key itself.

### Starts With (`startsWith(prefix)`)
1. Start from the root.
2. For each character in the `prefix`:
   a. If the character does not exist as a child of the current node, no key starts with this `prefix`. Return `False`.
   b. Move to that child node.
3. If all characters in `prefix` are processed successfully, return `True`.

Labuladong's course typically delves into the full implementation in later sections or exercises, focusing on these principles first.

## 总结 (Summary)
- Trie (Prefix Tree) is an N-ary tree optimized for string storage and prefix-based operations.
- Nodes represent characters, and paths from the root represent prefixes or full keys.
- Offers space savings for common prefixes and fast $O(L)$ prefix operations.
- Key methods include `insert`, `search`, `startsWith`, `keysWithPrefix`, etc.
- `TrieSet` is a simplified `TrieMap` for storing sets of strings.

---
Parent: [[Interview/Concept/Data Structures/Tree/index|Trees (Data Structure)]]
Next: [[Interview/Concept/Data Structures/Trie/01 - TrieMap and TrieSet Implementation|TrieMap and TrieSet Implementation]] (Placeholder for Labuladong's typical follow-up on implementation)
Related: [[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|N-ary Trees]], [[Interview/Concept/Programming Languages/Python/05 - Python Dict (Hash Map) for Interviews|Hash Maps]] (for comparison)
