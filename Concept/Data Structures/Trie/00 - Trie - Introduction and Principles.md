---
tags: [concept/data_structures, concept/tree, concept/trie, concept/prefix_tree, type/introduction]
aliases: [<PERSON><PERSON>, Prefix Tree, Dictionary Tree, 字典树, 前缀树]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/二叉树结构的种种变换/Trie_字典树_前缀树原理及可视化.md]].
> Labuladong positions Tri<PERSON> as an extension of [[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|N-ary tree]] structures, specifically optimized for string operations.

# Trie (Prefix Tree / Dictionary Tree): Principles and Visualization

A Trie, also known as a prefix tree or dictionary tree, is a specialized tree-like data structure used for efficient retrieval of keys in a dataset of strings. Each node in a Trie represents a character (or part of a character/string), and paths from the root to a node represent prefixes.

## 🌲 Core Concept: An N-ary Tree for Strings

A Trie can be seen as a specific type of [[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|N-ary tree]] where:
- Each node (except possibly the root) corresponds to a character.
- The children of a node are indexed by characters. For example, a node might have children for 'a', 'b', 'c', etc.
- Paths from the root to a particular node represent a prefix.
- Some nodes might be marked as the end of a complete key (string) stored in the Trie, often also storing an associated value if implementing a TrieMap.

**Visualization Example (Labuladong's Visualizer):**
Imagine inserting "apple", "app", "appl":
```mermaid
graph TD
    Root["(root)"]
    Root -- "a" --> A["a"]
    A -- "p" --> AP["ap"]
    AP -- "p" --> APP["app (val=2)"]
    APP -- "l" --> APPL["appl (val=3)"]
    APPL -- "e" --> APPLE["apple (val=1)"]

    style Root fill:#f9f,stroke:#333,stroke-width:2px
    style APP fill:#lightgreen,stroke:#333,stroke-width:2px
    style APPL fill:#lightgreen,stroke:#333,stroke-width:2px
    style APPLE fill:#lightgreen,stroke:#333,stroke-width:2px
```
> In this conceptual diagram based on Labuladong's visualization, nodes like "app", "appl", "apple" would be marked as end-of-word and could store associated values.

## 🔑 Key Applications and Advantages

### 1. Efficient Prefix Operations
Tries excel at operations involving prefixes:
- **`startsWith(prefix)` / `hasKeyWithPrefix(prefix)`:** Check if any stored key begins with the given prefix. Complexity: $O(L)$, where $L$ is the length of the prefix.
- **`keysWithPrefix(prefix)`:** Retrieve all keys that start with the given prefix. Complexity depends on the number of matching keys and their lengths, but finding the prefix node is $O(L)$. This is ideal for auto-completion features.
- **`shortestPrefixOf(string)`:** Find the shortest stored key that is a prefix of a given string.
- **`longestPrefixOf(string)`:** Find the longest stored key that is a prefix of a given string (useful in routing tables, for example).

These operations are much less efficient with standard [[Interview/Concept/Programming Languages/Python/05 - Python Dict (Hash Map) for Interviews|hash maps]] or [[Interview/Concept/Data Structures/Tree/Binary Search Tree/index|binary search trees]].

### 2. Space Efficiency for Common Prefixes
If many strings share common prefixes (e.g., "application", "apply", "appeal"), the Trie stores the common prefix "app" only once, saving space compared to storing each full string separately in a hash map.
- **Example:** Storing "apple", "app", "appl".
    - Hash Map: Stores 3 distinct string objects: "apple" (5 chars), "app" (3 chars), "appl" (4 chars) = 12 characters total for keys.
    - Trie: Stores characters along paths. The shared "app" prefix is stored once. Total characters for key structure: 'a', 'p', 'p', 'l', 'e' = 5 characters essentially define all keys.

### 3. String Sorting (Implicit)
Traversing a Trie in a specific order (e.g., visiting children in alphabetical order) can retrieve all stored strings in lexicographical order.

### 4. Full-text Search and Pattern Matching
Tries and their variations (like Suffix Trees or Aho-Corasick automata) are fundamental in algorithms for searching patterns in text.

## 🔧 Trie Node Structure (Conceptual)

A common way to implement a Trie node:
```python
class TrieNode:
    def __init__(self):
        self.children = {}  # Maps character to TrieNode
        self.is_end_of_word = False # Flag to mark end of a word
        self.value = None # Optional: To store value if it's a TrieMap

# For a fixed alphabet (e.g., lowercase English letters):
# self.children = [None] * 26 
```
- **`children`:** A dictionary (or array for fixed alphabets) mapping a character to the child `TrieNode` representing that character.
- **`is_end_of_word` (or similar flag):** A boolean indicating if the path from the root to this node forms a complete key stored in the Trie.
- **`value`:** If implementing a `TrieMap`, this field stores the value associated with the key. For a `TrieSet`, only `is_end_of_word` is needed.

## TrieMap vs. TrieSet
- **`TrieMap<ValueType>`:** Stores key-value pairs. Keys are strings. `TrieNode` would have a `value` field.
- **`TrieSet`:** Stores a set of unique strings. `TrieNode` would have an `is_end_of_word` flag. It's essentially a `TrieMap` where the value is a boolean or not explicitly stored if `is_end_of_word` suffices.

## Complexity
For a Trie storing $N$ keys, where $L_{avg}$ is the average key length and $L_{max}$ is the maximum key length:
- **Insertion:** $O(L_{avg})$ or $O(L_{max})$ for one key.
- **Search (for a key):** $O(L)$ where $L$ is the length of the key being searched.
- **Deletion:** $O(L)$.
- **Prefix-based operations (e.g., `startsWith`):** $O(P)$ where $P$ is the length of the prefix.
- **Space Complexity:** In the worst case, if there are no shared prefixes, it can be $O(\sum L_i)$ (sum of lengths of all keys), similar to storing strings directly. However, with significant prefix sharing, it's much better. The number of nodes is at most $\sum L_i$.

## 🛠️ Basic Operations (Conceptual for TrieMap)

### Insert (`put(key, value)`)
1. Start from the root.
2. For each character in the `key`:
   a. If the character does not exist as a child of the current node, create a new child node for it.
   b. Move to that child node.
3. After processing all characters, mark the final node as `is_end_of_word = True` and set its `value`.

### Search (`get(key)`)
1. Start from the root.
2. For each character in the `key`:
   a. If the character does not exist as a child of the current node, the `key` is not in the Trie. Return `null` or raise an error.
   b. Move to that child node.
3. After processing all characters, if the final node is marked `is_end_of_word`, return its `value`. Otherwise, the `key` is a prefix of another key but not a key itself.

### Starts With (`startsWith(prefix)`)
1. Start from the root.
2. For each character in the `prefix`:
   a. If the character does not exist as a child of the current node, no key starts with this `prefix`. Return `False`.
   b. Move to that child node.
3. If all characters in `prefix` are processed successfully, return `True`.

Labuladong's course typically delves into the full implementation in later sections or exercises, focusing on these principles first.

## 总结 (Summary)
- Trie (Prefix Tree) is an N-ary tree optimized for string storage and prefix-based operations.
- Nodes represent characters, and paths from the root represent prefixes or full keys.
- Offers space savings for common prefixes and fast $O(L)$ prefix operations.
- Key methods include `insert`, `search`, `startsWith`, `keysWithPrefix`, etc.
- `TrieSet` is a simplified `TrieMap` for storing sets of strings.

---
Parent: [[Interview/Concept/Data Structures/Tree/index|Trees (Data Structure)]]
Next: [[Interview/Concept/Data Structures/Trie/01 - TrieMap and TrieSet Implementation|TrieMap and TrieSet Implementation]] (Placeholder for Labuladong's typical follow-up on implementation)
Related: [[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|N-ary Trees]], [[Interview/Concept/Programming Languages/Python/05 - Python Dict (Hash Map) for Interviews|Hash Maps]] (for comparison)
