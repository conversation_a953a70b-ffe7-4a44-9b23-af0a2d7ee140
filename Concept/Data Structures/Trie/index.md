---
tags: [index, concept/data_structures, concept/tree, concept/trie]
aliases: [Trie Index, Prefix Tree Index]
---

# Trie (Prefix Tree) Concepts

This section covers concepts and implementations related to Tries.

## Core Concepts:
- [[Interview/Concept/Data Structures/Trie/00 - Trie - Introduction and Principles|Trie - Introduction and Principles]]

## Implementations and Problems:
- `[[Interview/Concept/Data Structures/Trie/01 - TrieMap and TrieSet Implementation|TrieMap and TrieSet Implementation]]` (Placeholder for Labuladong's typical implementation note)
- `[[Interview/Practice/LeetCode/LC208 - Implement Trie (Prefix Tree)|LC208 - Implement Trie (Prefix Tree)]]` (Placeholder for a common LeetCode problem)

## Visualization
```mermaid
graph TD
    TrieConcept["Trie Concepts"] --> IntroTrie["[[Interview/Concept/Data Structures/Trie/00 - Trie - Introduction and Principles|Introduction]]"]
    TrieConcept --> ImplementationsTrie["Implementations & Problems"]
    ImplementationsTrie --> ImplNote["(TrieMap/Set Implementation Note)"]
    ImplementationsTrie --> LC208["(LC208 Implement Trie)"]

    classDef main fill:#e6fff2,stroke:#00994d,stroke-width:2px;
    class TrieConcept main;
```

---
Parent: [[Interview/Concept/Data Structures/Tree/index|Trees (Data Structure)]]
