---
tags: [concept/data_structures, concept/tree, concept/trie, type/implementation, course/labuladong]
aliases: [<PERSON><PERSON>mplementation, TrieMap, TrieSet, 前缀树实现]
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/Trie 树代码实现.md|Trie 树代码实现 by Labuladong]].
> For Trie principles, see [[Interview/Concept/Data Structures/Trie/00 - Trie - Introduction and Principles|Trie - Introduction and Principles]].

# TrieMap and TrieSet: Implementation Details

This note details the common implementation structure for `TrieMap` (associating string keys with values) and `TrieSet` (storing a set of unique strings).

## 🌲 `TrieNode` Structure

The fundamental building block is the `TrieNode`.
```python
class TrieNode:
    def __init__(self, val=None): # val is for TrieMap, not strictly needed for TrieSet
        # For character sets like 'a'-'z', an array of size 26 can be used.
        # For generic characters, a dictionary is more flexible.
        self.children = {}  # Or [None] * 26 for fixed alphabet
        self.is_end_of_word = False # Flag for TrieSet, or indicates a key exists for TrieMap
        self.val = val # Stores the value associated with the key ending at this node (for TrieMap)
        # Optional: Store frequency of word, or frequency of prefix passing through node
        # self.freq = 0 # Number of words ending at this node
        # self.prefix_count = 0 # Number of words having this prefix
```
Labuladong's article mentions that for problems restricted to lowercase English letters (`a-z`), the `children` map can be replaced by an array of size 26 for efficiency, mapping `char_code = ord(char) - ord('a')`.

## `TrieMap<V>` Implementation Sketch

A `TrieMap` associates string keys with values of type `V`.

```python
class TrieMap:
    def __init__(self):
        self.root = TrieNode() # Root node is typically empty
        self._size = 0 # Number of key-value pairs

    def _char_to_index(self, char): # Example for fixed alphabet
        # return ord(char) - ord('a') # If children is an array for 'a'-'z'
        return char # If children is a dict

    def put(self, key: str, value) -> None:
        '''Inserts a key-value pair into the TrieMap.'''
        curr = self.root
        # self.root.prefix_count += 1 # If tracking prefix counts
        for char_code in key: # Iterate through characters of the key
            # char_idx = self._char_to_index(char_code) # For array-based children
            if char_code not in curr.children: # Or curr.children[char_idx] is None
                curr.children[char_code] = TrieNode()
            curr = curr.children[char_code]
            # curr.prefix_count += 1 # If tracking prefix counts

        if not curr.is_end_of_word: # If it's a new key
            self._size += 1
        curr.is_end_of_word = True
        curr.val = value
        # curr.freq +=1 # If tracking word frequencies

    def _get_node(self, key: str) -> TrieNode | None:
        '''Helper to find the node corresponding to the end of the key.'''
        curr = self.root
        for char_code in key:
            if char_code not in curr.children:
                return None
            curr = curr.children[char_code]
        return curr

    def get(self, key: str): # -> V | None
        '''Retrieves the value associated with a key.'''
        node = self._get_node(key)
        if node and node.is_end_of_word:
            return node.val
        return None # Or raise KeyError

    def containsKey(self, key: str) -> bool:
        '''Checks if the key exists in the TrieMap.'''
        node = self._get_node(key)
        return node is not None and node.is_end_of_word

    def remove(self, key: str): # -> V | None (optional: return removed value)
        '''Removes a key-value pair. This is more complex due to potential node cleanup.'''
        # Basic removal: just mark node.is_end_of_word = False and decrement size.
        # Advanced removal: prune nodes that no longer form part of any key.
        # Labuladong's initial implementation focus is usually on put/get/startsWith.
        node_to_remove_val_from = self._get_node(key)
        if node_to_remove_val_from and node_to_remove_val_from.is_end_of_word:
            node_to_remove_val_from.is_end_of_word = False
            removed_val = node_to_remove_val_from.val
            node_to_remove_val_from.val = None # Clear value
            # node_to_remove_val_from.freq -= 1 # If tracking frequency
            self._size -= 1
            # TODO: Implement pruning of unused nodes if necessary
            return removed_val
        return None


    def size(self) -> int:
        return self._size

    # Prefix-related methods
    def startsWith(self, prefix: str) -> bool: # For TrieSet, or 'hasKeyWithPrefix' for TrieMap
        '''Checks if any key starts with the given prefix.'''
        return self._get_node(prefix) is not None

    def keysWithPrefix(self, prefix: str) -> list[str]:
        '''Returns all keys in the TrieMap that start with the given prefix.'''
        results = []
        prefix_node = self._get_node(prefix)
        if prefix_node is None:
            return results
        
        # DFS from prefix_node to find all words
        self._collect_keys(prefix_node, list(prefix), results)
        return results

    def _collect_keys(self, node: TrieNode, current_path: list[str], results: list[str]):
        if node.is_end_of_word:
            results.append("".join(current_path))

        for char_code, child_node in node.children.items():
            # char = chr(ord('a') + char_idx) # If using array-based children
            current_path.append(char_code)
            self._collect_keys(child_node, current_path, results)
            current_path.pop() # Backtrack
```

## `TrieSet` Implementation Sketch

A `TrieSet` is simpler as it only stores keys, not values.
```python
class TrieSet:
    def __init__(self):
        self.root = TrieNode() # Value in TrieNode will be ignored or always None
        self._size = 0

    def add(self, key: str) -> None:
        curr = self.root
        for char_code in key:
            if char_code not in curr.children:
                curr.children[char_code] = TrieNode()
            curr = curr.children[char_code]
        
        if not curr.is_end_of_word: # Only increment size if it's a new word
            curr.is_end_of_word = True
            self._size += 1

    def contains(self, key: str) -> bool:
        curr = self.root
        for char_code in key:
            if char_code not in curr.children:
                return False
            curr = curr.children[char_code]
        return curr.is_end_of_word

    def remove(self, key: str) -> None:
        # Similar to TrieMap.remove, set is_end_of_word to False and decrement size.
        # Pruning logic can be added.
        node = self._get_node_for_set(key) # (Helper like _get_node in TrieMap)
        if node and node.is_end_of_word:
            node.is_end_of_word = False
            self._size -= 1
            # TODO: Pruning
    
    def _get_node_for_set(self, key:str) -> TrieNode | None:
        curr = self.root
        for char_code in key:
            if char_code not in curr.children:
                return None
            curr = curr.children[char_code]
        return curr


    def size(self) -> int:
        return self._size
    
    # startsWith and keysWithPrefix methods would be similar to TrieMap,
    # but _collect_keys would not involve values.
```

## 总结 (Summary)
- `TrieMap` and `TrieSet` are built upon `TrieNode`s.
- Each `TrieNode` typically has a dictionary or array for children and a flag for end-of-word. `TrieMap` nodes also store values.
- Core operations (`put`/`add`, `get`/`contains`, `remove`) involve traversing the Trie character by character.
- Prefix-based operations are a key strength of Tries.
- Optimizations for specific character sets (e.g., array for `a-z`) can improve performance.

---
Parent: [[Interview/Concept/Data Structures/Trie/00 - Trie - Introduction and Principles|Trie - Introduction and Principles]]
Next: [[Interview/Practice/Topic Exercises/Trie_Tree_Exercises_Index|Trie Tree Exercises Index]]
