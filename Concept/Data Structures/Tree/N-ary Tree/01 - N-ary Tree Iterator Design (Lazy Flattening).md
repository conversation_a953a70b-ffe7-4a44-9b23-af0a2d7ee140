---
tags: [concept/data_structures, concept/tree, concept/n_ary_tree, concept/iterator, pattern/lazy_evaluation, pattern/dfs]
aliases: [N-ary Tree Iterator, Lazy N-ary Tree Flattening, Iterative N-ary Tree Traversal for Iterators]
---

> [!NOTE] Source Annotation
> This concept is derived from problems like [[Interview/Practice/LeetCode/LC341 - Flatten Nested List Iterator|LC341 - Flatten Nested List Iterator]], which effectively deals with iterating over a flattened N-ary tree structure. The core idea is lazy expansion using a stack.

# N-ary Tree Iterator Design (Lazy Flattening)

Designing an iterator for an N-ary tree (or a structure that behaves like one, such as nested lists) often involves "flattening" the tree to produce a sequence of its node values (typically leaf nodes or all nodes in a specific order like pre-order). A "lazy" approach is preferred for efficiency, especially with large or deep trees, as it avoids processing the entire tree upfront.

## 🎯 Core Problem
Given an N-ary tree structure (e.g., nodes with a value and a list of children, or nested lists), implement an iterator with `hasNext()` and `next()` methods to traverse its elements (usually integers stored in the structure) in a flattened sequence.

## ✨ Lazy Expansion with a Stack (Simulating DFS)

The key idea is to use a stack to manage the traversal process, performing a Depth-First Search (DFS) iteratively. The stack holds references to nodes or sub-lists that still need to be processed.

**Conceptual Algorithm:**

1.  **Iterator State:**
    - `stack`: Stores elements or iterators/lists to be processed. Elements are typically pushed in a way that allows LIFO (Last-In, First-Out) processing to achieve DFS traversal (e.g., pushing children of a list in reverse order).

2.  **Constructor (`__init__(root_elements)`):**
    - Initialize the `stack` with the top-level elements (e.g., the initial list of `NestedInteger` objects, or the children of the root of an N-ary tree).
    - To process elements from left to right, push them onto the stack in **reverse order**.

3.  **`hasNext()` Method (The "Workhorse"):**
    - This method ensures that if there is a next element to be yielded, it's an actual data item (e.g., an integer) and is readily available (e.g., at the top of the stack or in a peeked variable).
    - **Loop while `stack` is not empty:**
        a. **Peek** at the top element of the stack (`current_item`).
        b. **If `current_item` is a data item (e.g., an integer):**
           - An item is ready. Return `True`. (Do not pop it yet; `next()` will).
        c. **If `current_item` is a collection/list/non-leaf node that needs further expansion:**
           i. **Pop** `current_item` from the stack (as we are now processing/expanding it).
           ii. Get its children/sub-elements.
           iii. **Push** these children/sub-elements onto the stack **in reverse order** of their intended processing. This ensures the first child is at the top of the stack next.
    - If the loop finishes (stack becomes empty) without finding a data item at the top, return `False`.

4.  **`next()` Method:**
    - **Precondition:** `hasNext()` must have returned `True` (meaning a data item is ready).
    - **Pop** the data item from the stack (which `hasNext()` ensured was a processable data item).
    - Return the value of this data item.

## 🧩 Example: Flattening Nested Lists (like LC341)

If `stack` stores `NestedInteger` objects:
- `hasNext()`:
  - While stack is not empty:
    - `top = stack[-1]` (peek)
    - If `top.isInteger()`: return `True`
    - Else (it's a list): `popped_list_ni = stack.pop()`. `sub_list = popped_list_ni.getList()`. Push elements of `sub_list` (from last to first) onto `stack`.
  - Return `False`.
- `next()`:
  - `integer_ni = stack.pop()`. Return `integer_ni.getInteger()`.

```tikz
\begin{tikzpicture}[
    stack_item/.style={rectangle, draw, fill=blue!10, minimum height=0.6cm, minimum width=1.5cm, font=\sffamily\small},
    stack_base/.style={draw, thick, minimum width=1.7cm},
    operation/.style={rectangle, draw, fill=yellow!20, rounded corners, text width=4cm, align=center, font=\sffamily\scriptsize},
    arrow/.style={->, >=stealth, thick}
]

\node[stack_item] (item1) at (0,0) {List [C, D]};
\node[stack_item, below=0.1cm of item1] (item2) {B (int)};
\node[stack_item, below=0.1cm of item2] (item3) {List [A]};
\node[stack_base, below=0.05cm of item3] (base) {};
\node at (0, -2.5) {Initial Stack (elements pushed in reverse)};

% hasNext() iteration 1
\node[operation] (op1) at (5, 1) {`hasNext()` Iteration 1:\\Peek: List [C,D] (is list)\\Pop List [C,D]\\Push D, then C (reverse)};
\draw[arrow] (0.8,0) -- (op1);

\node[stack_item] (s_item1) at (10,1.5) {D (int)};
\node[stack_item, below=0.1cm of s_item1] (s_item2) {C (int)};
\node[stack_item, below=0.1cm of s_item2] (s_item3) {B (int)};
\node[stack_item, below=0.1cm of s_item3] (s_item4) {List [A]};
\node[stack_base, below=0.05cm of s_item4] (s_base1) {};
\node at (10, -0.5) {Stack after expanding List [C,D]};

% hasNext() iteration 2 (assuming D was next, now C is top)
\node[operation] (op2) at (5, -2) {`hasNext()` Iteration (after D processed):\\Peek: C (is int)\\Return True};
\draw[arrow] (10.8,-0.8) -- (op2);

\end{tikzpicture}
```

## 👍 Advantages of Lazy Expansion
- **Memory Efficiency:** Only keeps necessary elements/iterators on the stack. Avoids creating a large flattened list in memory upfront.
- **Time Efficiency for Partial Iteration:** If the consumer only iterates through a few elements, we don't waste time processing the entire structure.
- **Handles Potentially Infinite Structures (with care):** If the structure could be virtually infinite (e.g., a generator of N-ary tree nodes), lazy evaluation is essential.

## ⚙️ Variations
- **Stack Content:** The stack can hold direct elements, iterators for sub-lists, or state tuples `(node, child_index_to_process_next)`. The choice depends on the complexity of the N-ary node structure and traversal needs.
- **Order:** This DFS-based stack approach naturally yields a pre-order like traversal of the flattened elements. If a different order (like BFS/level-order) is needed for the flattened sequence, a queue would be used instead of a stack for the core expansion logic, though iterator design with BFS can be more complex to make "lazy" for `hasNext`.

## 总结 (Summary)
- Lazy expansion using a stack is a powerful pattern for creating iterators over N-ary tree-like structures.
- The `hasNext()` method typically contains the core logic to ensure the next element is ready and is an actual data item (not a collection to be further expanded).
- This approach is memory-efficient and suitable for large or deeply nested structures.
- It's a practical application of simulating DFS iteratively to achieve a specific traversal goal.

---
Parent: [[Interview/Concept/Data Structures/Tree/N-ary Tree/index|N-ary Tree Concepts]]
Related: [[Interview/Practice/LeetCode/LC341 - Flatten Nested List Iterator|LC341 - Flatten Nested List Iterator]], [[Interview/Concept/Algorithms/Tree Traversal/02 - N-ary Tree Recursive Traversal (DFS)|N-ary Tree DFS]], [[Interview/Concept/Data Structures/Stack/index|Stacks]]
