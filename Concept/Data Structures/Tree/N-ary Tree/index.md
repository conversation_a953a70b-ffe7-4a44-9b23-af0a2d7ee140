---
tags: [index, concept/data_structures, concept/tree, concept/n_ary_tree]
aliases: [N-ary Tree Concepts Index, Multiway Tree Index]
---

# N-ary Tree Concepts

This section covers concepts related to N-ary Trees (Multiway Trees).

## Core Concepts:
- [[Interview/Concept/Data Structures/Tree/N-ary Tree/01 - N-ary Tree Iterator Design (Lazy Flattening)|N-ary Tree Iterator Design (Lazy Flattening)]]

- [[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|N-ary Tree - Introduction]]
  - Node Structure
  - Forest

## Traversal Algorithms (see also [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal Algorithms]]):
- [[Interview/Concept/Algorithms/Tree Traversal/02 - N-ary Tree Recursive Traversal (DFS)|Recursive Traversal (DFS: Pre-order, Post-order)]]
- [[Interview/Concept/Algorithms/Tree Traversal/03 - N-ary Tree Level-Order Traversal (BFS)|Level-Order Traversal (BFS)]]

## Visualization
```mermaid
graph TD
    NT["N-ary Tree Concepts"] --> Intro["[[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|Introduction]]"]

    NT --> Traversals["Traversal (see [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal]])"]
    Traversals --> DFS_NT["[[Interview/Concept/Algorithms/Tree Traversal/02 - N-ary Tree Recursive Traversal (DFS)|Recursive (DFS)]]"]
    Traversals --> BFS_NT["[[Interview/Concept/Algorithms/Tree Traversal/03 - N-ary Tree Level-Order Traversal (BFS)|Level-Order (BFS)]]"]

    classDef main fill:#e6f2ff,stroke:#aaccff,stroke-width:2px;
    class NT main;
    Intro --> IteratorDesign["[[Interview/Concept/Data Structures/Tree/N-ary Tree/01 - N-ary Tree Iterator Design (Lazy Flattening)|Iterator Design]]"]
```

---
Parent: [[Interview/Concept/Data Structures/Tree/index|Trees (Data Structure)]]