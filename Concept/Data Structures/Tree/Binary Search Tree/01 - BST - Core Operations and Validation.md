---
tags: [concept/data_structures, concept/tree, concept/bst, type/operations, pattern/bst_validation, pattern/bst_search, pattern/bst_insert, pattern/bst_delete, course/labuladong]
aliases: [BST Basic Operations, Validate BST, Search in BST, Insert into BST, Delete from BST, 二叉搜索树基本操作]
---
> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/手把手刷二叉树算法/二叉搜索树心法（基操篇）.md]].
> This note covers fundamental operations for Binary Search Trees (BSTs): validation, search, insertion, and deletion.

| [[Interview/Concept/Data Structures/Tree/Binary Search Tree/00 - BST - Introduction and Operations|Prev: BST Intro]] | [[Interview/Concept/Data Structures/Tree/Binary Search Tree/index|Back to BST Index]] | [[Interview/Concept/Data Structures/Tree/Binary Search Tree/Problem Solving Patterns/00 - BST Problem Solving Patterns Index|Next: BST Problem Patterns]] |

# Binary Search Tree: Core Operations and Validation

This note details the implementation of fundamental operations for Binary Search Trees (BSTs):
1.  Validating if a given binary tree is a BST.
2.  Searching for an element in a BST.
3.  Inserting an element into a BST.
4.  Deleting an element from a BST.

These operations leverage the core BST property: for any node `x`, all keys in `x.left` are less than `x.key`, and all keys in `x.right` are greater than `x.key`.

## 1. Validate Binary Search Tree (LC98)
[[Interview/Practice/LeetCode/LC98 - Validate Binary Search Tree|LC98 - Validate Binary Search Tree]]
A naive check comparing a node only with its direct children is insufficient. The entire left subtree must be less than the node, and the entire right subtree must be greater.

**Correct Approach:** Pass down range constraints (`min_val`, `max_val`) for each node.
- For `root`, its value must be between `min_val` and `max_val`.
- For `root.left`, its value must be between `min_val` and `root.val`.
- For `root.right`, its value must be between `root.val` and `max_val`.

```python
# class TreeNode:
#     def __init__(self, val=0, left=None, right=None): self.val = val; self.left = left; self.right = right

class SolutionValidateBST:
    def isValidBST(self, root: TreeNode) -> bool:
        return self._is_valid_bst_recursive(root, float('-inf'), float('inf'))

    def _is_valid_bst_recursive(self, node: TreeNode, lower_bound: float, upper_bound: float) -> bool:
        if not node:
            return True # An empty tree is a valid BST
        
        if not (lower_bound < node.val < upper_bound):
            return False # Node's value violates constraints
        
        # Recursively check left and right subtrees with updated bounds
        return (self._is_valid_bst_recursive(node.left, lower_bound, node.val) and
                self._is_valid_bst_recursive(node.right, node.val, upper_bound))
```
Labuladong visualizer: `div_validate-binary-search-tree`.

## 2. Search in a Binary Search Tree (LC700)
[[Interview/Practice/LeetCode/LC700 - Search in a Binary Search Tree|LC700 - Search in a Binary Search Tree]]
Leverage the BST property to decide whether to go left or right.

```python
class SolutionSearchBST:
    def searchBST(self, root: TreeNode, val: int) -> TreeNode | None:
        if not root:
            return None
        
        if val == root.val:
            return root
        elif val < root.val:
            return self.searchBST(root.left, val)
        else: # val > root.val
            return self.searchBST(root.right, val)
```
This is $O(H)$ time complexity, where $H$ is tree height ($O(\log N)$ for balanced, $O(N)$ for skewed). Labuladong visualizer: `div_search-in-a-binary-search-tree`.

## 3. Insert into a Binary Search Tree (LC701)
[[Interview/Practice/LeetCode/LC701 - Insert into a Binary Search Tree|LC701 - Insert into a Binary Search Tree]]
Find the correct empty spot for the new value while maintaining BST properties. The function should return the (potentially modified) root of the subtree.

```python
class SolutionInsertBST:
    def insertIntoBST(self, root: TreeNode | None, val: int) -> TreeNode | None:
        if not root:
            return TreeNode(val) # Found the spot, insert new node
        
        if val < root.val:
            root.left = self.insertIntoBST(root.left, val)
        elif val > root.val: # Assuming no duplicates as per typical BST or problem statement
            root.right = self.insertIntoBST(root.right, val)
        # If val == root.val, do nothing (or handle as per problem if duplicates allowed)
        
        return root # Return the (possibly modified) root of this subtree
```
Labuladong visualizer: `div_insert-into-a-binary-search-tree`.

## 4. Delete Node in a BST (LC450)
[[Interview/Practice/LeetCode/LC450 - Delete Node in a BST|LC450 - Delete Node in a BST]]
This is the most complex basic operation.
1.  **Find the node to delete.**
2.  **Handle deletion based on number of children:**
    *   **Case 1: Node is a leaf (0 children).** Simply remove it (return `None` to parent).
        Visual: `![](/algo/images/bst/bst_deletion_case_1.png)`
    *   **Case 2: Node has 1 child.** Replace node with its child.
        Visual: `![](/algo/images/bst/bst_deletion_case_2.png)`
    *   **Case 3: Node has 2 children.**
        - Find its in-order successor (smallest value in its right subtree) OR in-order predecessor (largest value in its left subtree).
        - Replace the node's value with the successor's/predecessor's value.
        - Recursively delete the successor/predecessor from its original position (it will fall into Case 1 or 2).
        Visual: `![](/algo/images/bst/bst_deletion_case_3.png)` (shows using min from right subtree)

```python
class SolutionDeleteBST:
    def _get_min_node(self, node: TreeNode) -> TreeNode:
        # Smallest node is the leftmost node in a BST
        current = node
        while current and current.left:
            current = current.left
        return current

    def deleteNode(self, root: TreeNode | None, key: int) -> TreeNode | None:
        if not root:
            return None

        if key < root.val:
            root.left = self.deleteNode(root.left, key)
        elif key > root.val:
            root.right = self.deleteNode(root.right, key)
        else: # Found the node to delete (root.val == key)
            # Case 1: Node is a leaf or has only one child
            if not root.left:
                return root.right # If left is null, right child (or null) takes its place
            if not root.right:
                return root.left  # If right is null, left child (or null) takes its place
            
            # Case 3: Node has two children
            # Find the in-order successor (smallest in the right subtree)
            min_larger_node = self._get_min_node(root.right)
            # Copy successor's value to this node
            root.val = min_larger_node.val
            # Delete the in-order successor from the right subtree
            root.right = self.deleteNode(root.right, min_larger_node.val)
            
        return root
```
Labuladong visualizer: `div_delete-node-in-a-bst`.
Labuladong emphasizes modifying pointers rather than just `node.val` for general applicability where node data might be complex. The Python solution above with `root.val = min_larger_node.val` is a common simplification for LeetCode. A pointer-based swap would involve more complex pointer rewiring.

## 总结 (Summary)
- BST operations fundamentally rely on the "left < root < right" invariant.
- **Validation:** Recursively check if node values fall within `(min_bound, max_bound)` inherited from ancestors.
- **Search, Insert, Delete:** These operations typically involve traversing down the tree, making a decision at each node to go left or right. The time complexity is $O(H)$.
- **Deletion** is the most intricate, especially when the node to be deleted has two children, requiring finding a successor/predecessor to maintain BST properties.
- For operations that modify the tree structure (insert, delete), recursive functions should return the new root of the modified subtree to correctly update parent pointers (e.g., `root.left = deleteNode(root.left, key)`).

---
| [[Interview/Concept/Data Structures/Tree/Binary Search Tree/00 - BST - Introduction and Operations|Prev: BST Intro]] | [[Interview/Concept/Data Structures/Tree/Binary Search Tree/index|Back to BST Index]] | [[Interview/Concept/Data Structures/Tree/Binary Search Tree/Problem Solving Patterns/00 - BST Problem Solving Patterns Index|Next: BST Problem Patterns]] |
