---
tags: [concept/data_structures, concept/tree, concept/bst, concept/balanced_bst, type/implementation, course/labuladong]
aliases: [TreeMap Implementation, TreeSet Implementation, Ordered Map, Ordered Set, 红黑树实现TreeMap]
---

> [!NOTE] Source Annotation
> Content conceptualized from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/TreeMap_TreeSet 代码实现.md|TreeMap_TreeSet 代码实现 by Labuladong]].
> For principles, see [[Interview/Concept/Data Structures/Tree/Binary Search Tree/00 - BST - Introduction and Operations|BST Introduction]] and potentially [[Interview/Concept/Data Structures/Tree/Red-Black Tree/index|Red-Black Tree Principles]].

# TreeMap and TreeSet: Implementation Principles

`TreeMap` and `TreeSet` are ordered map and set data structures, respectively. Unlike hash-based maps/sets, they maintain their elements in a sorted order based on keys. This ordering allows for efficient range queries, finding successor/predecessor, and ordered iteration.

These structures are typically implemented using **balanced binary search trees (BSTs)**, most commonly Red-Black Trees or AVL Trees, to guarantee $O(\log N)$ time complexity for most operations.

## 🌲 Core Idea: Balanced Binary Search Tree

-   **TreeMap:**
    -   Each node in the balanced BST stores a key-value pair.
    -   Keys are ordered according to the BST property.
    -   Operations like `put`, `get`, `remove`, `containsKey` involve traversing the BST, taking $O(\log N)$ time due to the tree's balance.
    -   Additionally supports operations like `firstKey()`, `lastKey()`, `floorKey()`, `ceilingKey()`, and range queries (e.g., `subMap()`), all benefiting from the sorted structure.
-   **TreeSet:**
    -   Stores unique elements in sorted order.
    -   Can be implemented as a wrapper around a `TreeMap`, where elements of the set are keys in the `TreeMap`, and the map's values are a dummy constant (similar to how [[Interview/Concept/Data Structures/Hash Set/00 - Hash Set - Principles and Implementation|HashSet can wrap HashMap]]).
    -   Operations `add`, `remove`, `contains` are $O(\log N)$.
    -   Supports ordered iteration and finding elements like `first()`, `last()`, `floor()`, `ceiling()`.

Labuladong's note on "TreeMap/TreeSet 代码实现" likely points to the underlying BST implementation rather than providing a full from-scratch Red-Black Tree implementation, as that is very complex. For interview purposes, understanding that they *use* balanced BSTs and their resulting complexities/capabilities is often sufficient, unless specifically asked to implement a simpler BST that could form their basis.

## Conceptual Implementation (Simplified BST Basis)

While a full balanced BST is complex, a basic BST illustrates the principles:

```python
# Simplified TreeNode for a BST-based Map/Set
class BSTNode:
    def __init__(self, key, value=None): # value is for TreeMap
        self.key = key
        self.value = value
        self.left = None
        self.right = None
        # For balanced trees, additional fields like color (Red-Black) or height (AVL) are needed.

# Conceptual operations (not a full TreeMap/Set class)

def bst_put(root, key, value): # For TreeMap
    if not root:
        return BSTNode(key, value)
    if key < root.key:
        root.left = bst_put(root.left, key, value)
    elif key > root.key:
        root.right = bst_put(root.right, key, value)
    else: # key == root.key
        root.value = value # Update value for existing key
    # Balancing logic would go here for AVL/Red-Black trees
    return root

def bst_get(root, key): # For TreeMap
    if not root:
        return None # Or raise error
    if key < root.key:
        return bst_get(root.left, key)
    elif key > root.key:
        return bst_get(root.right, key)
    else: # key == root.key
        return root.value

# For TreeSet, 'value' in BSTNode would be a dummy or ignored.
# `bst_put` for TreeSet would not update if key exists, only insert if new.
```
Standard library implementations (like Java's `TreeMap`/`TreeSet`) are highly optimized and use robust balanced tree algorithms. Python's standard library does not have a direct equivalent of `TreeMap`/`TreeSet` built-in that is as commonly used as `dict` or `set`. Third-party libraries or custom implementations are needed for true $O(\log N)$ ordered map/set behavior in Python. (Python's `dict` preserves insertion order since 3.7 but is not key-sorted like a TreeMap).

## Operations and Complexity (for Balanced BST based TreeMap/TreeSet)

-   **Add/Put:** $O(\log N)$
-   **Remove:** $O(\log N)$
-   **Contains/Get:** $O(\log N)$
-   **Size:** $O(1)$ (if size is maintained)
-   **Min/Max Key (First/Last):** $O(\log N)$ (traverse to leftmost/rightmost node)
-   **Floor/Ceiling Key:** $O(\log N)$
-   **Iteration:** $O(N)$ to iterate all elements (in sorted order). `next()` for iterator is $O(\log N)$ amortized in some implementations.

## Use Cases
-   When sorted order of keys/elements is required.
-   When range queries (e.g., find all keys between X and Y) are needed.
-   Implementing data structures that require finding floor/ceiling values efficiently.
-   Calendars, event schedulers.

## 总结 (Summary)
- `TreeMap` and `TreeSet` provide ordered associative array and set functionalities, typically with $O(\log N)$ performance for core operations.
- They are usually implemented using balanced binary search trees like Red-Black Trees.
- Their key advantage over hash-based maps/sets is the maintenance of sorted order, enabling efficient order-based queries and iteration.
- For interviews, understanding their performance characteristics and when to use them is more common than implementing them from scratch (unless it's a basic BST).

---
Parent: [[Interview/Concept/Data Structures/Tree/Binary Search Tree/index|Binary Search Tree Concepts]]
Related: [[Interview/Concept/Data Structures/Hash Map/00 - Hash Map - Core Principles|Hash Map (for comparison)]], [[Interview/Concept/Data Structures/Tree/Red-Black Tree/index|Red-Black Trees]]
