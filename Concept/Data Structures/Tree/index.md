---
tags: [index, concept/data_structures, concept/tree]
aliases: [Tree Data Structures Index]


## Visualization
```mermaid
graph TD
    TreeDS["Tree Data Structures"] --> BT["[[Interview/Concept/Data Structures/Tree/Binary Tree/index|Binary Trees]]"]
    TreeDS --> NT["[[Interview/Concept/Data Structures/Tree/N-ary Tree/index|N-ary Trees]]"]
    TreeDS --> <PERSON><PERSON>["[[Interview/Concept/Data Structures/Trie/index|Tries]]"]
    TreeDS --> Heap["[[Interview/Concept/Data Structures/Heap/index|Heaps]]"]
    TreeDS --> SegTree["[[Interview/Concept/Data Structures/Tree/Segment Tree/index|Segment Trees]]"]
    TreeDS --> AlgoLink["Algorithms (see [[Interview/Concept/Algorithms/Tree Traversal/index|Tree Traversal]])"]

    BT --> BTIntro["[[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Intro & Types]]"]
    NT --> NTIntro["[[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|Intro]]"]
    Trie --> TrieIntro["[[Interview/Concept/Data Structures/Trie/00 - Trie - Introduction and Principles|Intro & Principles]]"]
    Heap --> HeapIntro["[[Interview/Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization|Intro & Principles]]"]
    SegTree --> SegTreeIntro["[[Interview/Concept/Data Structures/Tree/Segment Tree/00 - Segment Tree - Principles and Visualization|Intro & Principles]]"]

    classDef main fill:#ccffcc,stroke:#006400,stroke-width:2px;
    class TreeDS main;
```

---
Parent: [[Interview/Concept/Data Structures/index|Data Structures Index]]
