---
tags: [concept/tree, topic/diameter, algorithm/dfs, pattern/tree_traversal, topic/graph_theory]
aliases: [Tree Diameter, Longest Path in Tree, Tree Width, Diameter Algorithm]
---

# Tree Diameter

## 🎯 Core Concept

**Tree Diameter** is the length of the longest path between any two nodes in a tree. This path represents the "widest span" across the tree structure and is a fundamental property used in many tree algorithms.

```tikz
\begin{tikzpicture}[
    treenode/.style={circle, draw, minimum size=0.8cm, font=\sffamily\small, fill=blue!30},
    diameter_node/.style={treenode, fill=red!40},
    diameter_edge/.style={very thick, red, line width=2pt},
    normal_edge/.style={thick},
    label_box/.style={rectangle, draw, fill=yellow!20, font=\sffamily\small, text width=3cm, align=center}
]

% Example tree with diameter highlighted
\node[treenode] (root) at (4, 4) {A};
\node[diameter_node] (b) at (2, 2.5) {B};
\node[treenode] (c) at (6, 2.5) {C};
\node[diameter_node] (d) at (1, 1) {D};
\node[diameter_node] (e) at (3, 1) {E};
\node[treenode] (f) at (5, 1) {F};
\node[diameter_node] (g) at (7, 1) {G};

% Normal edges
\draw[normal_edge] (root) -- (c);
\draw[normal_edge] (c) -- (f);

% Diameter path
\draw[diameter_edge] (root) -- (b);
\draw[diameter_edge] (b) -- (d);
\draw[diameter_edge] (b) -- (e);
\draw[diameter_edge] (c) -- (g);

\node[label_box] at (9, 3) {
    \textbf{Diameter Path:}\\
    D → B → A → C → G\\
    Length: 4 edges\\
    \\
    \textbf{Key Property:}\\
    Longest possible\\
    path in the tree
};

\end{tikzpicture}
```

## 🔍 Key Properties

### Formal Definition
- **Diameter**: Maximum distance between any two nodes
- **Distance**: Number of edges in the path between nodes
- **Path**: Unique path between any two nodes in a tree (no cycles)

### Important Characteristics
1. **Uniqueness**: Every tree has exactly one diameter value
2. **Multiple paths**: May have multiple paths achieving the diameter
3. **Endpoints**: Diameter endpoints are always leaf nodes (in most cases)
4. **Center**: Diameter path has one or two center nodes

## 💡 Diameter Algorithms

### 1. **Naive Approach: All Pairs Shortest Path**

```python
def diameter_naive(tree):
    """O(n²) approach - check all pairs"""
    max_distance = 0
    nodes = get_all_nodes(tree)
    
    for i in range(len(nodes)):
        for j in range(i + 1, len(nodes)):
            distance = shortest_path(nodes[i], nodes[j])
            max_distance = max(max_distance, distance)
    
    return max_distance
```

**Complexity:** O(n²) time, O(n) space

### 2. **Two DFS Approach (For General Trees)**

```tikz
\begin{tikzpicture}[
    step_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4cm, align=left, minimum height=1.5cm},
    arrow/.style={->, thick, blue}
]

\node[step_box] (step1) at (0, 4) {
    \textbf{Step 1: First DFS}\\
    • Start from any node\\
    • Find the farthest node\\
    • This is one diameter endpoint
};

\node[step_box] (step2) at (5, 4) {
    \textbf{Step 2: Second DFS}\\
    • Start from endpoint found\\
    • Find farthest node again\\
    • Distance = diameter length
};

\node[step_box] (proof) at (0, 2) {
    \textbf{Why This Works:}\\
    • Diameter endpoints are\\
    • always farthest from each other\\
    • First DFS finds one endpoint
};

\node[step_box] (complexity) at (5, 2) {
    \textbf{Complexity:}\\
    • Time: O(n)\\
    • Space: O(h)\\
    • Works for any tree structure
};

\draw[arrow] (step1) -- (step2);
\draw[arrow] (step2) -- (complexity);
\draw[arrow] (complexity) -- (proof);
\draw[arrow] (proof) -- (step1);

\end{tikzpicture}
```

```python
def diameter_two_dfs(tree):
    """O(n) approach for general trees"""
    
    def dfs_farthest(start):
        """Find farthest node from start and its distance"""
        visited = set()
        max_dist = 0
        farthest_node = start
        
        def dfs(node, dist):
            nonlocal max_dist, farthest_node
            if node in visited:
                return
            visited.add(node)
            
            if dist > max_dist:
                max_dist = dist
                farthest_node = node
            
            for neighbor in tree[node]:
                dfs(neighbor, dist + 1)
        
        dfs(start, 0)
        return farthest_node, max_dist
    
    # Step 1: Find one endpoint
    any_node = next(iter(tree))
    endpoint1, _ = dfs_farthest(any_node)
    
    # Step 2: Find diameter
    endpoint2, diameter = dfs_farthest(endpoint1)
    
    return diameter
```

### 3. **Single DFS Approach (For Binary Trees)**

```python
def diameter_single_dfs(root):
    """O(n) approach for binary trees - like LC543"""
    max_diameter = 0
    
    def height(node):
        nonlocal max_diameter
        if not node:
            return 0
        
        left_height = height(node.left)
        right_height = height(node.right)
        
        # Diameter through current node
        current_diameter = left_height + right_height
        max_diameter = max(max_diameter, current_diameter)
        
        return 1 + max(left_height, right_height)
    
    height(root)
    return max_diameter
```

## 🌟 Applications

### 1. **Network Analysis**
- **Communication Networks**: Find maximum latency path
- **Social Networks**: Measure network "width"
- **Transportation**: Longest route in road/rail networks

### 2. **Tree Algorithms**
- **Tree Decomposition**: Find tree centers and balanced cuts
- **Load Balancing**: Distribute work based on tree structure
- **Visualization**: Optimal tree layout and positioning

### 3. **Problem Solving Patterns**
- **[[LC543 - Diameter of Binary Tree]]**: Classic diameter calculation
- **LC1245. Tree Diameter**: Diameter in general trees
- **LC687. Longest Univalue Path**: Constrained diameter problems

## 🧠 Advanced Concepts

### Tree Centers
Every tree has 1 or 2 center nodes that minimize the maximum distance to any other node:

```python
def find_tree_centers(tree):
    """Find center(s) of tree - nodes on diameter path"""
    # Use diameter algorithm to find diameter path
    # Centers are middle node(s) of this path
    pass
```

### Diameter in Weighted Trees
For trees with edge weights, diameter becomes the maximum weighted path:

```python
def weighted_diameter(tree, weights):
    """Find diameter in weighted tree"""
    def dfs_weighted(start):
        # Similar to two-DFS but accumulate weights
        pass
    
    return dfs_weighted(any_node)
```

### Dynamic Diameter
Maintaining diameter as tree structure changes:
- **Edge Addition**: May increase diameter
- **Edge Removal**: May decrease diameter
- **Node Addition/Removal**: Affects tree structure

## 🎯 Complexity Analysis

| Algorithm | Time | Space | Tree Type | Notes |
|-----------|------|-------|-----------|-------|
| Naive | O(n²) | O(n) | Any | Check all pairs |
| Two DFS | O(n) | O(h) | Any | Most general |
| Single DFS | O(n) | O(h) | Binary | Most efficient for binary trees |
| Weighted | O(n) | O(h) | Weighted | Handle edge weights |

Where:
- n = number of nodes
- h = height of tree

## 🔗 Related Concepts

- **[[Post-Order Traversal]]**: Used in single DFS approach
- **[[Tree Height]]**: Related to diameter calculation
- **[[Graph Diameter]]**: Generalization to graphs
- **[[Tree Decomposition]]**: Uses diameter for balanced cuts
- **[[Longest Path Problem]]**: Diameter is longest path in trees

Tree diameter is a fundamental concept that appears in many tree algorithms and provides insights into tree structure and properties!
