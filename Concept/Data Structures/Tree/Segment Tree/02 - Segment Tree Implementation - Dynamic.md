---
tags: [concept/data_structures, concept/tree, concept/segment_tree, type/implementation_variant, course/labuladong]
aliases: [Dynamic Segment Tree Implementation, 线段树动态开点]
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/优化：实现动态线段树.md|优化：实现动态线段树 by Labuladong]].
> This note builds upon the [[Interview/Concept/Data Structures/Tree/Segment Tree/01 - Segment Tree Implementation - Basic|Basic Segment Tree Implementation]].

# Segment Tree: Dynamic Implementation (Dynamic Node Creation)

A Dynamic Segment Tree addresses the memory limitation of standard array-based or pre-allocated node-based segment trees when dealing with very large underlying ranges (e.g., indices up to $10^9$) where only a small fraction of the range is actually queried or updated. Instead of building the entire tree upfront, nodes are created "on-demand" as needed.

## 🌲 Core Idea: "动态开点" (Dynamic Point Creation)

- Nodes in the segment tree are not pre-allocated.
- A node is created only when an `update` operation needs to pass through its corresponding range, or a `query` needs to explore it.
- Child pointers (`left`, `right`) in a `SegmentTreeNode` are initially `None`. They are instantiated only when that child's sub-range is accessed.

This requires a node-based (linked) implementation. The array-based segment tree cannot be easily made dynamic in this way.

## `DynamicSegmentTreeNode` Structure
Same as `SegmentTreeNode` from the basic implementation:
```python
class DynamicSegmentTreeNode: # Same as SegmentTreeNode, renamed for clarity
    def __init__(self, start, end, val=0, left=None, right=None):
        self.start = start
        self.end = end
        self.val = val # Aggregated value
        self.left = left
        self.right = right
        self.lazy_tag = 0 # For lazy propagation, if needed
```

## `DynamicSegmentTree` Implementation

The `_build` method is no longer used to construct the whole tree. The root might represent a very large range initially. Node creation happens within `update` and `query`.

```python
class DynamicSegmentTree:
    def __init__(self, range_min_val: int, range_max_val: int):
        # The tree logically covers [range_min_val, range_max_val]
        # but no nodes are created initially except possibly root.
        # Or, root can be None and created on first update.
        # For simplicity, let's assume root is created if needed, or we manage a null root.
        self.root = None 
        # Store overall range if needed, or pass it down.
        # For some problems (like LC307), an initial array defines the range,
        # but for pure dynamic ST, we might only have overall bounds.
        self.global_min = range_min_val
        self.global_max = range_max_val

    # Helper to ensure a node for a range [start, end] exists, creating if necessary.
    # This is often integrated directly into update/query.
    def _ensure_node(self, node_ref_parent_attr, start, end):
        # This logic is tricky. Usually, node creation happens during traversal.
        pass

    def update(self, arr_idx_to_update: int, val_change: int): # Or set to new_val
        '''Updates value at arr_idx_to_update by val_change (or sets to new_val).'''
        # If setting to new_val, val_change would be new_val - old_val, 
        # or query old_val first, then update. Simpler to pass new_val.
        # Let's assume `update` sets arr_idx_to_update to value `val_change`.
        
        # If root is None, the initial call to _update_recursive will create it.
        self.root = self._update_recursive(self.root, self.global_min, self.global_max, 
                                           arr_idx_to_update, val_change)

    def _update_recursive(self, node: DynamicSegmentTreeNode | None, 
                          node_range_start: int, node_range_end: int,
                          arr_idx_to_update: int, new_val_at_idx: int) -> DynamicSegmentTreeNode:
        
        # If the current node doesn't exist for this range, create it.
        if node is None:
            node = DynamicSegmentTreeNode(node_range_start, node_range_end)

        # Base case: leaf node for the target index
        if node_range_start == node_range_end:
            # Assuming leaf node corresponds to arr_idx_to_update
            node.val = new_val_at_idx # Or node.val += val_change for increments
            return node

        # If a lazy tag exists, push it down before further processing
        # self._push_down(node) # (If lazy propagation is implemented)

        mid = node_range_start + (node_range_end - node_range_start) // 2
        
        if arr_idx_to_update <= mid:
            # Child node is created if it's None by the recursive call
            node.left = self._update_recursive(node.left, node_range_start, mid, 
                                               arr_idx_to_update, new_val_at_idx)
        else:
            node.right = self._update_recursive(node.right, mid + 1, node_range_end,
                                                arr_idx_to_update, new_val_at_idx)
            
        # Recompute current node's value from children
        node.val = 0
        if node.left: node.val += node.left.val
        if node.right: node.val += node.right.val
        return node

    def query(self, query_left: int, query_right: int) -> int:
        return self._query_recursive(self.root, self.global_min, self.global_max,
                                     query_left, query_right)

    def _query_recursive(self, node: DynamicSegmentTreeNode | None,
                         node_range_start: int, node_range_end: int,
                         query_left: int, query_right: int) -> int:
        # If current node doesn't exist, it means this part of range has no values (or default value)
        if node is None:
            return 0 # Or identity for the operation

        # Case 1: Node's range completely outside query range
        if node_range_start > query_right or node_range_end < query_left:
            return 0
        
        # Case 2: Node's range completely inside query range
        if query_left <= node_range_start and node_range_end <= query_right:
            return node.val

        # If a lazy tag exists, push it down before querying children
        # self._push_down(node) # (If lazy propagation is implemented)

        # Case 3: Node's range partially overlaps
        mid = node_range_start + (node_range_end - node_range_start) // 2
        left_sum = self._query_recursive(node.left, node_range_start, mid, query_left, query_right)
        right_sum = self._query_recursive(node.right, mid + 1, node_range_end, query_left, query_right)
        
        return left_sum + right_sum

    # _push_down method for lazy propagation would be added here if needed.
```
**Note on `_update_recursive` return:** It returns the node reference. This is useful so that when a child node is created in a recursive call (e.g., `node.left` was `None`, becomes `new_child_node`), the parent's `node.left` attribute is correctly assigned to this new child.

Labuladong's article for dynamic segment tree (LC307 context): `DynamicSegmentTree` class has methods `query(root, qL, qR)` and `update(root, i, val)`. The root is passed around, or managed as an instance variable.

## Complexity
- **Time Complexity:**
    - `update` (point update): $O(\log K)$, where $K$ is the size of the overall range (`global_max - global_min + 1`).
    - `query` (range query): $O(\log K)$.
- **Space Complexity:** $O(M \log K)$, where $M$ is the number of distinct points that have been updated or queried. Each update/query might create $O(\log K)$ nodes along a path. If $M$ updates/queries touch distinct paths, this is the space.

## Use Cases
- When the range of indices is very large (e.g., coordinates, time stamps) but the number of actual data points or queries is relatively small.
- Online competitive programming problems with large coordinate ranges.
- Situations where pre-allocating a $4N$-sized array for a segment tree is not feasible due to memory limits for $N$.

## 总结 (Summary)
- Dynamic Segment Tree creates nodes on-demand, making it suitable for very large ranges with sparse data.
- It requires a linked (node-based) implementation.
- `update` and `query` operations still maintain $O(\log K)$ time complexity (where K is total range size).
- Space complexity depends on the number of activated nodes, roughly proportional to number of updates/queries times $\log K$.
- This is a key optimization for specific scenarios where standard segment trees are too memory-intensive.

---
Parent: [[Interview/Concept/Data Structures/Tree/Segment Tree/01 - Segment Tree Implementation - Basic|Basic Segment Tree Implementation]]
Next: [[Interview/Concept/Data Structures/Tree/Segment Tree/03 - Segment Tree Implementation - Lazy Propagation|Lazy Propagation Segment Tree]]
Related Problems: Problems requiring range queries on large, sparse coordinate systems. (LC307 can be solved with basic or dynamic if range is huge)
