---
tags: [concept/data_structures, concept/tree, concept/segment_tree, type/implementation_variant, course/labuladong, concept/lazy_propagation]
aliases: [Lazy Propagation Segment Tree, Segment Tree Range Update, 线段树懒更新]
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/优化：实现懒更新线段树.md|优化：实现懒更新线段树 by Labuladong]].
> This builds upon [[Interview/Concept/Data Structures/Tree/Segment Tree/02 - Segment Tree Implementation - Dynamic|Dynamic Segment Tree Implementation]] and [[Interview/Concept/Data Structures/Tree/Segment Tree/01 - Segment Tree Implementation - Basic|Basic Segment Tree Implementation]].

# Segment Tree: Implementation with Lazy Propagation

Lazy Propagation is an optimization for Segment Trees that allows for efficient **range updates** (e.g., add a value to all elements in `nums[i...j]`, or set all elements in `nums[i...j]` to a value) in $O(\log N)$ time, rather than performing $N$ point updates.

## 😴 Core Idea: Lazy Tags

- When a range update fully covers the interval represented by a segment tree node, instead of propagating the update immediately to all its children (and down to the leaves), we "lazily" mark the current node with a **lazy tag**.
- This tag signifies that all elements in this node's interval need to be updated.
- The node's aggregated value (`node.val`) is updated immediately based on the range update and its interval size.
- The lazy tag is "pushed down" to its children only when:
    1. A query needs to access one of its children.
    2. Another update operation needs to partially update one of its children's intervals.

## `SegmentTreeNode` Structure with Lazy Tag

```python
class LazySegmentTreeNode: # Or add lazy_tag to existing SegmentTreeNode
    def __init__(self, start, end, val=0, left=None, right=None):
        self.start = start
        self.end = end
        self.val = val   # Aggregated value (e.g., sum)
        self.left = left
        self.right = right
        
        # Lazy tag: Stores the pending update value for this node's interval.
        # For range sum with range add: lazy_tag stores the value to add to each element.
        # For range sum with range set: lazy_tag stores the value to set for each element.
        # Needs a way to indicate "no lazy tag" vs "lazy tag is 0".
        # Option 1: Use None for no tag.
        # Option 2: For range add, tag is additive. For range set, might need separate 'is_set' flag.
        # Labuladong's example for sum/range add:
        self.lazy_add_tag = 0 # Value to add to all elements in [start, end]

        # For range set:
        # self.lazy_set_val = None # If not None, all elements in [start,end] are set to this
```
Labuladong's article distinguishes between `IncrSegmentTree` (range add) and `AssignSegmentTree` (range set/update).

## `LazySegmentTree` Implementation (Focus on Range Add for Sum)

This example shows a dynamic segment tree structure (nodes created on demand) with lazy propagation for range sum and range add operations.

```python
# Using DynamicSegmentTreeNode from the dynamic ST note, but renamed here for context
class TreeNodeWithLazy:
    def __init__(self, start, end, val=0):
        self.start, self.end, self.val = start, end, val
        self.left, self.right = None, None
        self.lazy_add = 0 # Pending value to add to children

class LazySegmentTreeRangeAdd:
    def __init__(self, range_min, range_max):
        # Root not strictly needed here if update/query handles None root.
        # For dynamic trees, it's often implicit.
        # If we need an explicit root for range [min, max]:
        self.root = TreeNodeWithLazy(range_min, range_max) # Optional: Can be None too
        self.global_min = range_min
        self.global_max = range_max


    def _push_down(self, node: TreeNodeWithLazy):
        '''Propagates lazy tag from node to its children.'''
        if node is None or node.lazy_add == 0: # No tag or no node
            return
        if node.start == node.end: # Leaf node, no children to push to
            node.lazy_add = 0 # Clear tag after applying to itself (already done in update)
            return

        mid = node.start + (node.end - node.start) // 2
        
        # Ensure children exist (dynamic node creation)
        if node.left is None:
            node.left = TreeNodeWithLazy(node.start, mid)
        if node.right is None:
            node.right = TreeNodeWithLazy(mid + 1, node.end)
            
        # Apply lazy tag to left child
        node.left.val += node.lazy_add * (node.left.end - node.left.start + 1)
        node.left.lazy_add += node.lazy_add # Accumulate tag

        # Apply lazy tag to right child
        node.right.val += node.lazy_add * (node.right.end - node.right.start + 1)
        node.right.lazy_add += node.lazy_add # Accumulate tag
        
        # Clear lazy tag on current node
        node.lazy_add = 0

    def range_add(self, add_left: int, add_right: int, delta: int):
        '''Adds delta to all elements in original array's range [add_left, add_right].'''
        self.root = self._range_add_recursive(self.root, self.global_min, self.global_max, 
                                              add_left, add_right, delta)

    def _range_add_recursive(self, node: TreeNodeWithLazy | None,
                             node_s: int, node_e: int, # Current node's range
                             add_l: int, add_r: int, delta: int) -> TreeNodeWithLazy:
        if node is None: # Dynamically create node
            node = TreeNodeWithLazy(node_s, node_e)

        # If update range completely covers node's range
        if add_l <= node_s and node_e <= add_r:
            node.val += delta * (node_e - node_s + 1) # Update node's sum
            node.lazy_add += delta # Apply/accumulate lazy tag
            return node
        
        # Partial overlap or no overlap for current node, push down its lazy tag first
        self._push_down(node)
        
        mid = node_s + (node_e - node_s) // 2
        if add_l <= mid: # Overlap with left child
            node.left = self._range_add_recursive(node.left, node_s, mid, add_l, add_r, delta)
        if add_r > mid: # Overlap with right child
            node.right = self._range_add_recursive(node.right, mid + 1, node_e, add_l, add_r, delta)
            
        # Recompute node's value from children
        node.val = 0
        if node.left: node.val += node.left.val
        if node.right: node.val += node.right.val
        return node

    def query(self, query_left: int, query_right: int) -> int:
        '''Queries sum for range [query_left, query_right].'''
        return self._query_recursive(self.root, self.global_min, self.global_max, 
                                     query_left, query_right)

    def _query_recursive(self, node: TreeNodeWithLazy | None,
                         node_s: int, node_e: int, # Current node's range
                         q_l: int, q_r: int) -> int:
        if node is None: return 0 # Range not covered by any updates, sum is 0

        # If query range is outside node's range
        if node_s > q_r or node_e < q_l:
            return 0
            
        # If query range completely covers node's range
        if q_l <= node_s and node_e <= q_r:
            return node.val
            
        # Partial overlap, push down lazy tag before querying children
        self._push_down(node)
        
        mid = node_s + (node_e - node_s) // 2
        left_sum, right_sum = 0, 0
        # Query children (they are guaranteed to exist if _push_down was called on a non-leaf)
        # but defensive check for None is safer if _push_down isn't always comprehensive
        if q_l <= mid:
            left_sum = self._query_recursive(node.left, node_s, mid, q_l, q_r)
        if q_r > mid:
            right_sum = self._query_recursive(node.right, mid + 1, node_e, q_l, q_r)
            
        return left_sum + right_sum

```
Labuladong's article discusses specific implementations for `IncrSegmentTree` (range add) and `AssignSegmentTree` (range set/update). The logic for `_push_down` and how tags/values are updated differs based on the operation (add vs. set). For range set, a set tag might override an add tag, or tags might need careful composition.

## Complexity
- **Range Update (`range_add` / `range_set`):** $O(\log K)$, where $K$ is size of overall range.
- **Range Query (`query`):** $O(\log K)$.
- **Space Complexity:** $O(M \log K)$ for dynamic tree with $M$ influential updates/queries, or $O(K)$ for array-based full tree.

## 总结 (Summary)
- Lazy propagation optimizes range updates on segment trees to $O(\log N)$.
- Updates are "cached" at higher-level nodes using lazy tags and pushed down to children only when necessary.
- The `_push_down` function is critical for applying parent's lazy tag to children and clearing parent's tag.
- Logic for updating node values and composing lazy tags depends on the specific operation (e.g., range add, range set, range min/max).
- Enables efficient solutions for problems requiring frequent range updates and queries.

---
Parent: [[Interview/Concept/Data Structures/Tree/Segment Tree/02 - Segment Tree Implementation - Dynamic|Dynamic Segment Tree]]
Next: [[Interview/Practice/Topic Exercises/Segment_Tree_Exercises_Index|Segment Tree Exercises Index]]
Related Problems: [[Interview/Practice/LeetCode/LC307 - Range Sum Query - Mutable|LC307]] (can be extended for range updates), various competitive programming problems.
