---
tags: [index, concept/data_structures, concept/tree, concept/segment_tree, concept/range_query]
aliases: [Segment Tree Index, Interval Tree Index]
---

# Segment Tree Concepts

This section covers concepts, implementations, and applications of Segment Trees.

## Core Concepts:
- [[Interview/Concept/Data Structures/Tree/Segment Tree/00 - Segment Tree - Principles and Visualization|Segment Tree - Principles and Visualization]]
  - Range Queries (Sum, Min/Max)
  - Point Updates
  - Dynamic Segment Trees
  - Lazy Propagation for Range Updates

## Implementations:
- `[[Interview/Concept/Data Structures/Tree/Segment Tree/01 - Segment Tree Implementation - Basic|Segment Tree Implementation - Basic]]` (Placeholder)
- `[[Interview/Concept/Data Structures/Tree/Segment Tree/02 - Segment Tree with Lazy Propagation|Segment Tree with Lazy Propagation]]` (Placeholder)

## Visualization
```mermaid
graph TD
    SegTConcept["Segment Tree Concepts"] --> IntroSegT["[[Interview/Concept/Data Structures/Tree/Segment Tree/00 - Segment Tree - Principles and Visualization|Introduction & Principles]]"]
    IntroSegT --> Ops["Queries & Updates"]
    IntroSegT --> DynamicSegT["Dynamic Segment Trees"]
    IntroSegT --> LazyProp["Lazy Propagation"]
    
    SegTConcept --> ImplementationsSegT["Implementations"]
    ImplementationsSegT --> BasicImpl["(Basic Implementation)"]
    ImplementationsSegT --> LazyImpl["(Lazy Propagation Implementation)"]

    classDef main fill:#e6faff,stroke:#007acc,stroke-width:2px;
    class SegTConcept main;
```

---
Parent: [[Interview/Concept/Data Structures/Tree/index|Trees (Data Structure)]]
