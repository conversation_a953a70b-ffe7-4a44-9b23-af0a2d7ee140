---
tags: [concept/data_structures, concept/tree, concept/segment_tree, type/implementation, course/labuladong]
aliases: [Segment Tree Basic Implementation, 线段树基本实现]
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/基本线段树的代码实现.md|基本线段树的代码实现 by Labuladong]].
> For Segment Tree principles, see [[Interview/Concept/Data Structures/Tree/Segment Tree/00 - Segment Tree - Principles and Visualization|Segment Tree - Principles and Visualization]].

# Segment Tree: Basic Implementation (Array-based and Node-based)

This note details the basic implementation of a Segment Tree, covering both array-based and node-based (linked) approaches for range queries (e.g., sum) and point updates.

## 🌲 `SegmentTreeNode` Structure (for Node-based/Linked Implementation)

```python
class SegmentTreeNode:
    def __init__(self, start, end, val=0, left=None, right=None):
        self.start = start # Start of the interval for this node
        self.end = end   # End of the interval for this node
        self.val = val   # Aggregated value (e.g., sum, min, max) for [start, end]
        self.left = left # Left child node
        self.right = right # Right child node
        # For lazy propagation, add: self.lazy_tag = 0 (or appropriate default)
```

## Array-based Segment Tree Implementation

This implementation uses an array (typically 1-indexed for easier parent/child calculation, or 0-indexed with adjusted formulas) to store the tree. A tree for an array of size `N` needs about `4N` space.

```python
class SegmentTreeArray:
    def __init__(self, nums: list[int]):
        self.n = len(nums)
        if self.n == 0:
            self.tree = []
            return
            
        # Size of tree array typically 4*n for safety, or calculate precisely.
        # For 0-indexed original array, if tree is 0-indexed:
        # Max index needed can be around 2*N if N is power of 2,
        # but 4N is safer for general N.
        self.tree = [0] * (4 * self.n) 
        self.original_nums = nums # Keep a copy or reference if needed
        self._build(0, 0, self.n - 1) # Build tree: tree_idx, range_start, range_end

    def _build(self, tree_node_idx: int, arr_start: int, arr_end: int):
        '''Builds segment tree for nums[arr_start...arr_end] at tree[tree_node_idx].'''
        if arr_start == arr_end: # Leaf node
            self.tree[tree_node_idx] = self.original_nums[arr_start]
            return
        
        mid = arr_start + (arr_end - arr_start) // 2
        left_child_idx = 2 * tree_node_idx + 1
        right_child_idx = 2 * tree_node_idx + 2
        
        self._build(left_child_idx, arr_start, mid)
        self._build(right_child_idx, mid + 1, arr_end)
        
        # Combine results from children (e.g., sum)
        self.tree[tree_node_idx] = self.tree[left_child_idx] + self.tree[right_child_idx]

    def query(self, query_left: int, query_right: int) -> int:
        '''Queries sum for range [query_left, query_right] in original array.'''
        if self.n == 0: return 0
        return self._query_recursive(0, 0, self.n - 1, query_left, query_right)

    def _query_recursive(self, tree_node_idx: int, node_range_start: int, node_range_end: int, 
                         query_left: int, query_right: int) -> int:
        # Case 1: Node's range is completely outside query range
        if node_range_start > query_right or node_range_end < query_left:
            return 0 # Or appropriate identity for the operation (e.g., float('inf') for min)

        # Case 2: Node's range is completely inside query range
        if query_left <= node_range_start and node_range_end <= query_right:
            return self.tree[tree_node_idx]

        # Case 3: Node's range partially overlaps query range
        mid = node_range_start + (node_range_end - node_range_start) // 2
        left_child_idx = 2 * tree_node_idx + 1
        right_child_idx = 2 * tree_node_idx + 2
        
        left_sum = self._query_recursive(left_child_idx, node_range_start, mid, query_left, query_right)
        right_sum = self._query_recursive(right_child_idx, mid + 1, node_range_end, query_left, query_right)
        
        return left_sum + right_sum

    def update(self, arr_idx_to_update: int, new_val: int):
        '''Updates element at arr_idx_to_update to new_val in original array & segment tree.'''
        if self.n == 0: return
        # Optional: Update original_nums if maintained
        # self.original_nums[arr_idx_to_update] = new_val 
        self._update_recursive(0, 0, self.n - 1, arr_idx_to_update, new_val)

    def _update_recursive(self, tree_node_idx: int, node_range_start: int, node_range_end: int,
                          arr_idx_to_update: int, new_val: int):
        if node_range_start == node_range_end: # Leaf node reached
            self.tree[tree_node_idx] = new_val
            return

        mid = node_range_start + (node_range_end - node_range_start) // 2
        left_child_idx = 2 * tree_node_idx + 1
        right_child_idx = 2 * tree_node_idx + 2

        if arr_idx_to_update <= mid: # Update is in left child's range
            self._update_recursive(left_child_idx, node_range_start, mid, arr_idx_to_update, new_val)
        else: # Update is in right child's range
            self._update_recursive(right_child_idx, mid + 1, node_range_end, arr_idx_to_update, new_val)
            
        # After child update, recompute current node's value
        self.tree[tree_node_idx] = self.tree[left_child_idx] + self.tree[right_child_idx]

```

## Node-based (Linked) Segment Tree Implementation

This uses the `SegmentTreeNode` class.
```python
# SegmentTreeNode class defined above

class SegmentTreeLinked:
    def __init__(self, nums: list[int]):
        if not nums:
            self.root = None
        else:
            self.root = self._build(nums, 0, len(nums) - 1)

    def _build(self, nums: list[int], start: int, end: int) -> SegmentTreeNode | None:
        if start > end:
            return None
        if start == end:
            return SegmentTreeNode(start, end, nums[start])
        
        mid = start + (end - start) // 2
        node = SegmentTreeNode(start, end)
        node.left = self._build(nums, start, mid)
        node.right = self._build(nums, mid + 1, end)
        
        # Combine results from children
        node.val = 0 # Default if children are None
        if node.left: node.val += node.left.val
        if node.right: node.val += node.right.val
        return node

    def query(self, query_left: int, query_right: int) -> int:
        return self._query_recursive(self.root, query_left, query_right)

    def _query_recursive(self, node: SegmentTreeNode | None, query_left: int, query_right: int) -> int:
        if not node: return 0
        
        # Case 1: Node's range is completely outside query range
        if node.start > query_right or node.end < query_left:
            return 0

        # Case 2: Node's range is completely inside query range
        if query_left <= node.start and node.end <= query_right:
            return node.val

        # Case 3: Node's range partially overlaps
        # No need to calculate mid, node stores its range
        left_sum = self._query_recursive(node.left, query_left, query_right)
        right_sum = self._query_recursive(node.right, query_left, query_right)
        
        return left_sum + right_sum

    def update(self, arr_idx_to_update: int, new_val: int):
        self._update_recursive(self.root, arr_idx_to_update, new_val)

    def _update_recursive(self, node: SegmentTreeNode | None, arr_idx_to_update: int, new_val: int):
        if not node: return
        
        if node.start == node.end: # Leaf node
            if node.start == arr_idx_to_update:
                node.val = new_val
            return

        # Determine which child's range contains the index
        mid = node.start + (node.end - node.start) // 2 # Mid of node's range
        if arr_idx_to_update <= mid:
            self._update_recursive(node.left, arr_idx_to_update, new_val)
        else:
            self._update_recursive(node.right, arr_idx_to_update, new_val)
        
        # Recompute current node's value after child update
        node.val = 0
        if node.left: node.val += node.left.val
        if node.right: node.val += node.right.val
```

Labuladong's article mentions [[Interview/Practice/LeetCode/LC307 - Range Sum Query - Mutable|LC307 Range Sum Query - Mutable]] as an application.

## Array vs. Linked Implementation
- **Array:** Simpler indexing, potentially better cache locality. Fixed size unless resizing logic is added.
- **Linked:** More flexible for dynamic segment trees, potentially more memory overhead per node due to pointers.

## 总结 (Summary)
- Basic Segment Tree supports $O(\log N)$ point updates and range queries.
- Can be implemented using arrays (common for fixed-size inputs) or linked nodes.
- `_build` is $O(N)$. `query` and `update` are $O(\log N)$.
- This structure is foundational for more advanced versions like those with lazy propagation or dynamic node creation.

---
Parent: [[Interview/Concept/Data Structures/Tree/Segment Tree/00 - Segment Tree - Principles and Visualization|Segment Tree Principles]]
Next: [[Interview/Concept/Data Structures/Tree/Segment Tree/02 - Segment Tree Implementation - Dynamic|Dynamic Segment Tree Implementation]]
Related Problems: [[Interview/Practice/LeetCode/LC307 - Range Sum Query - Mutable|LC307]]
