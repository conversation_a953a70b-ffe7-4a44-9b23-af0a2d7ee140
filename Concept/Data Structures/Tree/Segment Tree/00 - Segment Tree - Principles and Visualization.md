---
tags: [concept/data_structures, concept/tree, concept/segment_tree, type/introduction, concept/range_query]
aliases: [Segment Tree Basics, Interval Tree, 线段树原理]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/二叉树结构的种种变换/线段树核心原理及可视化.md]].
> Labuladong introduces Segment Tree as a derivative of [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Binary Tree]] structures, designed for efficient range queries and updates.

# Segment Tree: Principles and Visualization

A Segment Tree is a versatile tree data structure, typically a [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|binary tree]], used for storing information about intervals or segments. Each node in the segment tree represents an interval (or segment) of an array or range. It is particularly efficient for range query problems (e.g., sum of elements in a range, minimum/maximum in a range) and range update problems.

## 🌲 Core Concept

- **Structure:** A segment tree is a (usually) balanced binary tree.
    - **Leaf Nodes:** Represent individual elements of the original array or elementary intervals.
    - **Internal Nodes:** Represent the union (or aggregation) of the intervals of its children. For example, if a node represents the interval `[L, R]`, its left child might represent `[L, M]` and its right child `[M+1, R]`, where `M` is the midpoint. The value stored in the internal node is an aggregate (e.g., sum, min, max) of the values in its children's intervals.
- **Efficiency:** Allows for range queries and range updates in $O(\log N)$ time, where $N$ is the size of the original array/range.

**Visualization (from Labuladong's examples):**
For an array `nums = [1, 3, 5, 7, 9, 11]`:
A segment tree for sums might look like this:

```mermaid
graph TD
    R_0_5["[0,5]\nSum=36"] --> L_0_2["[0,2]\nSum=9"]
    R_0_5 --> R_3_5["[3,5]\nSum=27"]

    L_0_2 --> LL_0_1["[0,1]\nSum=4"]
    L_0_2 --> LR_2_2["[2,2]\nSum=5 (Leaf)"]
    
    R_3_5 --> RL_3_4["[3,4]\nSum=16"]
    R_3_5 --> RR_5_5["[5,5]\nSum=11 (Leaf)"]

    LL_0_1 --> LLL_0_0["[0,0]\nSum=1 (Leaf)"]
    LL_0_1 --> LLR_1_1["[1,1]\nSum=3 (Leaf)"]

    RL_3_4 --> RLL_3_3["[3,3]\nSum=7 (Leaf)"]
    RL_3_4 --> RLR_4_4["[4,4]\nSum=9 (Leaf)"]

    style R_0_5 fill:#f9f,stroke:#333,stroke-width:2px
    style LR_2_2 fill:#lightgreen,stroke:#333
    style RR_5_5 fill:#lightgreen,stroke:#333
    style LLL_0_0 fill:#lightgreen,stroke:#333
    style LLR_1_1 fill:#lightgreen,stroke:#333
    style RLL_3_3 fill:#lightgreen,stroke:#333
    style RLR_4_4 fill:#lightgreen,stroke:#333
```

## 🛠️ Key Operations

1.  **Build:** Constructing the segment tree from an initial array. This typically takes $O(N)$ time.
    - Each leaf node takes the value of an array element.
    - Each internal node's value is computed from its children (e.g., `parent.sum = left_child.sum + right_child.sum`).

2.  **Query (Range Query):** Finding an aggregate value over a given range `[queryL, queryR]`.
    - Traverses the tree. If a node's interval is completely within the query range, its value is used.
    - If a node's interval partially overlaps, recurse on its children.
    - If a node's interval is completely outside, it's ignored.
    - Complexity: $O(\log N)$.

3.  **Update (Point Update):** Modifying the value of a single element in the original array and updating the segment tree accordingly.
    - Update the corresponding leaf node.
    - Propagate changes upwards to affected parent nodes.
    - Complexity: $O(\log N)$.

Labuladong's visualizer for a basic segment tree demonstrates `query` and `update` (point update).

## Variations and Enhancements

### 1. Dynamic Segment Tree (Implicit Segment Tree / Dynamic Node Allocation)
- **Problem Solved:** Standard segment trees built on an array require space proportional to $N$. If $N$ is very large (e.g., $10^9$) but only a few points/ranges are actually used/updated, pre-allocating the full tree is infeasible.
- **Solution ("Dynamic开点" - Dynamic Node Creation):** Nodes in the segment tree are created only when they are needed (e.g., during an update operation on a specific point that falls within a node's range that hasn't been created yet).
- **Implementation:** Child pointers in nodes are initially `null`. They are instantiated when that part of the range is first accessed.
- Labuladong's visualizer for "dynamic segment tree" shows this "on-demand" node creation.

### 2. Segment Tree with Lazy Propagation (for Range Updates)
- **Problem Solved:** Updating a range of elements (e.g., add `val` to all elements in `nums[i...j]`) efficiently. A naive approach of $k$ point updates would be $O(k \log N)$, which is too slow if $k$ is large.
- **Solution ("Lazy Propagation"):** When a range update applies to a node's entire interval, instead of propagating the update all the way to the leaves immediately, store a "lazy tag" at that node. This tag indicates a pending update for its entire sub-interval.
    - The node's aggregate value is updated based on the range update.
    - The lazy tag is propagated down to children only when necessary (e.g., when a query needs to access a child, or another update passes through this node).
- **Complexity for Range Update:** $O(\log N)$.
- Labuladong's visualizer for "lazy update segment tree" shows how updates are cached in internal nodes and pushed down during queries.

## 🚀 Use Cases
Segment trees are powerful for problems involving:
- Range Sum Queries (RSQ)
- Range Minimum/Maximum Queries (RMQ)
- Range Updates (e.g., add a value to all elements in a range, set all elements in a range to a value)
- Geometric problems (e.g., counting points in a rectangle)
- Problems where data can be aggregated associatively over intervals.

## Array vs. Pointer-based Implementation
- **Array-based:** Simpler for full segment trees. Parent/child indices are calculated arithmetically. Requires about $2N$ to $4N$ array space.
- **Pointer-based (Nodes with `left`/`right` children):** More flexible for dynamic segment trees and can be more intuitive for understanding the tree structure.

## 总结 (Summary)
- Segment Tree is a binary tree for efficient range queries and updates on an array.
- Each node represents an interval and stores an aggregate value for that interval.
- Basic operations (build, query, point update) are $O(\log N)$.
- **Dynamic Segment Trees** use on-demand node creation for sparse data over large ranges.
- **Lazy Propagation** enables efficient $O(\log N)$ range updates.
- It's a fundamental data structure in competitive programming and for optimizing certain types of data analysis tasks.

---
Parent: [[Interview/Concept/Data Structures/Tree/index|Trees (Data Structure)]]
Next: [[Interview/Concept/Data Structures/Tree/Segment Tree/01 - Segment Tree Implementation - Basic|Segment Tree Implementation - Basic]] (Placeholder for Labuladong's typical follow-up on implementation)
Related: [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Binary Trees]], [[Interview/Concept/Algorithms/Divide and Conquer/index|Divide and Conquer]] (as the query/update logic often follows this paradigm)
