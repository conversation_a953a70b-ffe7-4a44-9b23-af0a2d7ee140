This index covers fundamental data structures, their properties, operations, and common use cases in algorithms and interviews.

## Core Data Structures:
- `[[Interview/Concept/Data Structures/Array/index|Arrays & Dynamic Arrays]]` (Placeholder)
- `[[Interview/Concept/Data Structures/Linked List/index|Linked Lists]]` (Placeholder)
- `[[Interview/Concept/Data Structures/Stack/index|Stacks]]`- [[Interview/Concept/Data Structures/Stack/Applications/index|Stack Applications]]
- `[[Interview/Concept/Data Structures/Queue/index|Queues]]`- [[Interview/Concept/Data Structures/Queue/Applications/index|Queue Applications]]
- `[[Interview/Concept/Data Structures/Deque/index|Deques]]` (Placeholder)
- `[[Interview/Concept/Data Structures/Hash Table/index|Hash Tables (Hash Maps & Hash Sets)]]` (General, covered by specific implementations below)
  - `[[Interview/Concept/Data Structures/Hash Map/index|Hash Maps (Dictionaries)]]` (Placeholder)
  - `[[Interview/Concept/Data Structures/Hash Set/index|Hash Sets]]` (Placeholder)
- [[Interview/Concept/Data Structures/Tree/index|Trees (General)]]
  - `[[Interview/Concept/Data Structures/Tree/Binary Tree/index|Binary Trees]]` (Placeholder)
  - `[[Interview/Concept/Data Structures/Tree/Binary Search Tree/index|Binary Search Trees (BST)]]` (Placeholder)
  - `[[Interview/Concept/Data Structures/Tree/N-ary Tree/index|N-ary Trees]]` (Placeholder)
- [[Interview/Concept/Data Structures/Heap/index|Heaps (Priority Queues)]]
- `[[Interview/Concept/Data Structures/Graph/index|Graphs]]` (Placeholder)
- [[Interview/Concept/Data Structures/Trie/index|Tries (Prefix Trees)]]


## Visualization of Data Structures
```mermaid
graph TD
    DS["Data Structures"] --> Arrays["(Arrays)"]
    DS --> LinkedLists["(Linked Lists)"]
    DS --> Stacks["[[Interview/Concept/Data Structures/Stack/index|Stacks]]"]
    DS --> Queues["[[Interview/Concept/Data Structures/Queue/index|Queues]]"]
    DS --> HashTables["(Hash Tables)"]
    DS --> Trees["[[Interview/Concept/Data Structures/Tree/index|Trees]]"]
    DS --> Heaps["[[Interview/Concept/Data Structures/Heap/index|Heaps]]"]
    DS --> Graphs["(Graphs)"]
    DS --> Tries["[[Interview/Concept/Data Structures/Trie/index|Tries]]"]

    Stacks --> StackApps["[[Interview/Concept/Data Structures/Stack/Applications/index|Applications]]"]
    Queues --> QueueApps["[[Interview/Concept/Data Structures/Queue/Applications/index|Applications]]"]
    HashTables --> HashMap["(Hash Map)"]
    HashTables --> HashSet["(Hash Set)"]
    Trees --> BT["[[Interview/Concept/Data Structures/Tree/Binary Tree/index|Binary Tree]]"]
    Trees --> NT["[[Interview/Concept/Data Structures/Tree/N-ary Tree/index|N-ary Tree]]"]
    Trees --> SegT["[[Interview/Concept/Data Structures/Tree/Segment Tree/index|Segment Tree]]"]
    Trees --> BST["(BST)"]


    classDef ds_cat fill:#fafad2,stroke:#b8860b,stroke-width:2px;
    class DS, Arrays, LinkedLists, Stacks, Queues, HashTables, Trees, Heaps, Graphs, Tries ds_cat;
```
