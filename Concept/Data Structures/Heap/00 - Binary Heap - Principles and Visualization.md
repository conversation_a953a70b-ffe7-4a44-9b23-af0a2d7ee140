---
tags: [concept/data_structures, concept/tree, concept/heap, concept/priority_queue, type/introduction]
aliases: [Binary Heap, Priority Queue Basics, 二叉堆原理, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/二叉树结构的种种变换/二叉堆核心原理及可视化.md]].
> Labuladong presents Binary Heap as an extension of [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Binary Tree]] structures, used for dynamic sorting and implementing Priority Queues.

# Binary Heap: Principles and Visualization

A Binary Heap is a specialized tree-based data structure that satisfies the heap property. It's typically implemented as a [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Complete Binary Tree]] and can be efficiently stored in an array. Heaps are commonly used to implement Priority Queues.

## 🌲 Core Concepts

1.  **Structure Property: Complete Binary Tree**
    - A binary heap is always a complete binary tree. This means all levels are fully filled except possibly the last level, which is filled from left to right.
    - This structure allows the heap to be stored compactly in an array, where parent-child relationships can be determined by index arithmetic:
        - For a 0-indexed array `arr`:
            - Parent of node at `i`: `(i-1) // 2`
            - Left child of node at `i`: `2*i + 1`
            - Right child of node at `i`: `2*i + 2`

2.  **Heap Property (Order Property):**
    - **Min-Heap:** For every node `X`, the value of `X` is less than or equal to the values of its children. This implies the minimum element is always at the root.
    - **Max-Heap:** For every node `X`, the value of `X` is greater than or equal to the values of its children. This implies the maximum element is always at the root.

Labuladong's visualizer often demonstrates these properties dynamically.

```mermaid
graph TD
    subgraph "Min-Heap Example"
        direction TB
        R1[1] --> C1_1[3]
        R1 --> C1_2[2]
        C1_1 --> G1_1[7]
        C1_1 --> G1_2[4]
        C1_2 --> G1_3[5]
        C1_2 --> G1_4[6]
    end
    subgraph "Max-Heap Example"
        direction TB
        R2[10] --> C2_1[8]
        R2 --> C2_2[9]
        C2_1 --> G2_1[3]
        C2_1 --> G2_2[2]
        C2_2 --> G2_3[5]
        C2_2 --> G2_4[1]
    end
```

## 🛠️ Key Operations: Swim and Sink

To maintain the heap property after insertions or deletions, two primary operations are used:

1.  **Swim (or Sift-Up, Heapify-Up, Bubble-Up, Percolate-Up):**
    - **Purpose:** When a new element is added to the heap (usually at the next available spot at the bottom to maintain completeness) or an element's value is decreased (in a min-heap) / increased (in a max-heap), it might violate the heap property with its parent.
    - **Action:** The element "swims" up the tree by repeatedly comparing itself with its parent and swapping if the heap property is violated, until its correct position is found or it reaches the root.
    - **Visualization (Min-Heap, adding 0 to `[1,2,3]`):**
        - Initial heap (array `[1,2,3]`): `1 -> (2,3)`
        - Add 0: `[1,2,3,0]`. Tree: `1 -> (2,3), 2 -> (0, None)`
        - 0 swims: `0` vs parent `2`. Swap. `[1,0,3,2]`. Tree: `1 -> (0,3), 0 -> (2,None)`
        - 0 swims: `0` vs parent `1`. Swap. `[0,1,3,2]`. Tree: `0 -> (1,3), 1 -> (2,None)`
        - 0 is at root, or heap property restored.

2.  **Sink (or Sift-Down, Heapify-Down, Bubble-Down, Percolate-Down):**
    - **Purpose:** When the root element is removed (common in `pop` operations from a priority queue) or an element's value is increased (in a min-heap) / decreased (in a max-heap), it might violate the heap property with its children.
    - **Action:** The element (often, the last element in the heap is moved to the root after a pop) "sinks" down the tree. It's repeatedly compared with its children. In a min-heap, it's swapped with the smaller child if it's larger than that child. This continues until it's in a valid position (smaller than its children or it becomes a leaf).
    - **Visualization (Min-Heap, removing root 1 from `[1,2,3,4,5]` and moving 5 to root):**
        - Initial: `1 -> (2,3), 2->(4,5)`
        - Remove 1. Move 5 (last element) to root. Array: `[5,2,3,4]`. Tree: `5 -> (2,3), 2->(4,None)`
        - 5 sinks: `5` vs children `2,3`. Smaller child is `2`. Swap `5` with `2`. Array: `[2,5,3,4]`. Tree: `2 -> (5,3), 5->(4,None)`
        - 5 sinks: `5` vs child `4`. Swap `5` with `4`. Array: `[2,4,3,5]`. Tree: `2 -> (4,3), 4->(5,None)`
        - 5 is a leaf or heap property restored.

Labuladong's visualizer interactively shows these `sink` and `swim` operations.

## 🚀 Applications

1.  **Priority Queue:**
    - Heaps are the most common way to implement priority queues.
    - `insert` operation: Add element to end, then `swim`. $O(\log N)$.
    - `extractMin` (Min-Heap) / `extractMax` (Max-Heap): Remove root, replace with last element, then `sink`. $O(\log N)$.
    - `peekMin/Max`: $O(1)$.
    - A detailed implementation is usually covered in [[Interview/Concept/Data Structures/Heap/01 - Binary Heap - Priority Queue Implementation|Priority Queue Implementation]].

2.  **Heap Sort:**
    - An in-place sorting algorithm with $O(N \log N)$ time complexity.
    - **Phase 1 (Heapify):** Build a max-heap from the input array. $O(N)$.
    - **Phase 2 (Sortdown):** Repeatedly swap the root (max element) with the last element of the heap, reduce heap size by one, and `sink` the new root to restore heap property. $O(N \log N)$.

## Complexity of Heap Operations
Assuming $N$ elements in the heap:
- **Insert:** $O(\log N)$ (due to swim)
- **Delete Min/Max (Pop):** $O(\log N)$ (due to sink)
- **Peek Min/Max:** $O(1)$
- **Build Heap (Heapify an array of N elements):** $O(N)$ (amortized, more efficient than $N$ insertions)

## 总结 (Summary)
- Binary Heap is a complete binary tree satisfying the min-heap or max-heap property.
- Efficiently implemented using an array.
- Core operations `swim` (up) and `sink` (down) maintain heap property, both $O(\log N)$.
- Primary applications: Priority Queues and Heap Sort.
- `peek` is $O(1)$, `insert/delete` are $O(\log N)$.

---
Parent: [[Interview/Concept/Data Structures/Tree/index|Trees (Data Structure)]]
Next: [[Interview/Concept/Data Structures/Heap/01 - Binary Heap - Priority Queue Implementation|Binary Heap - Priority Queue Implementation]]
Related: [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|Binary Trees]], [[Interview/Concept/Algorithms/Sorting/Heap Sort|Heap Sort]]
