---
tags: [index, concept/data_structures, concept/tree, concept/heap, concept/priority_queue]
aliases: [Heap Index, Priority Queue Index]
---

# Heap and Priority Queue Concepts

This section covers concepts related to Heaps and their primary application as Priority Queues.

## Core Concepts:
  - [[Interview/Concept/Data Structures/Heap/02 - Two Heaps for Median (MedianFinder)|Two Heaps for Median (MedianFinder)]]
- [[Interview/Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization|Binary Heap - Principles and Visualization]]
  - Min-Heap / Max-Heap
  - Swim and Sink operations

## Implementations:
- `[[Interview/Concept/Data Structures/Heap/01 - Binary Heap - Priority Queue Implementation|Binary Heap - Priority Queue Implementation]]` (Placeholder for Labuladong's typical implementation note)

## Related Algorithms:
- `[[Interview/Concept/Algorithms/Sorting/Heap Sort|Heap Sort]]` (Placeholder)

## Visualization
```mermaid
graph TD
    HeapConcept["Heap Concepts"] --> IntroHeap["[[Interview/Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization|Introduction & Principles]]"]
    IntroHeap --> MinMax["Min-Heap / Max-Heap"]
    IntroHeap --> SwimSink["Swim / Sink Operations"]
    
    HeapConcept --> ImplementationsHeap["Implementations"]
    ImplementationsHeap --> PQImpl["(Priority Queue Implementation)"]
    
    HeapConcept --> AlgoHeap["Related Algorithms"]
    AlgoHeap --> HeapSort["(Heap Sort)"]

    classDef main fill:#ffe6e6,stroke:#cc0000,stroke-width:2px;
    class HeapConcept main;
```

---
Parent: [[Interview/Concept/Data Structures/Tree/index|Trees (Data Structure)]] 
(Or [[Interview/Concept/Data Structures/index|Data Structures Index]] if considered more top-level than just a tree type)
