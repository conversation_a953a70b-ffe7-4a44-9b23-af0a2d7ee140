---
tags: [concept/data_structures, concept/heap, type/algorithm, pattern/two_heaps, course/labuladong]
aliases: [<PERSON>n Finder, Two Heaps for Median, Data Stream Median, 数据流的中位数, 双堆求中位数]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/拓展：两个二叉堆实现中位数算法.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/拓展：两个二叉堆实现中位数算法.md|拓展：两个二叉堆实现中位数算法 by Labuladong]].
> This note explains the two-heap technique for efficiently finding the median from a data stream.

# Median Finder: Using Two Binary Heaps

Finding the median of a data stream in real-time is a classic problem that requires a data structure capable of efficiently adding numbers and quickly retrieving the median. While sorting the entire stream is too slow, and simple BSTs lack efficient rank queries, a clever approach using two binary heaps provides an elegant solution. This is the core of [[Interview/Practice/LeetCode/LC295 - Find Median from Data Stream|LeetCode 295 - Find Median from Data Stream]].

## 🎯 The Challenge of Finding Medians in a Stream

-   **Naive Sorting:** If we sort the data after each `addNum` operation, `addNum` becomes $O(N \log N)$ or $O(N)$ (if inserting into sorted list), and `findMedian` is $O(1)$. Too slow for `addNum`.
-   **Balanced BST (e.g., AVL/Red-Black Tree):** `addNum` is $O(\log N)$. However, standard BSTs don't directly provide an $O(1)$ or $O(\log N)$ way to find the k-th element (which is needed for the median). Augmenting nodes with subtree sizes can achieve this, but it's complex to implement. Java's `TreeSet` doesn't offer this "select by rank" API. Also, `TreeSet` doesn't allow duplicates, which median calculation might require.

## 💡 The Two-Heap Solution

The core idea is to maintain two heaps:
1.  A **Max-Heap (`small_half_max_heap`)**: Stores the smaller half of the numbers seen so far. The top of this heap is the largest number in the smaller half.
2.  A **Min-Heap (`large_half_min_heap`)**: Stores the larger half of the numbers seen so far. The top of this heap is the smallest number in the larger half.

```tikz
\begin{tikzpicture}[
    heap_box/.style={rectangle, draw, rounded corners, fill=blue!10, minimum width=3cm, minimum height=2cm, align=center, font=\sffamily\small},
    arrow/.style={->, thick, red},
    label_style/.style={font=\sffamily\bfseries\small}
]

\node[heap_box] (max_heap) at (0,0) {
    Max-Heap (`small_half_max_heap`)\\
    Stores smaller half of numbers\\
    Largest of smalls at top: $max_s$
};

\node[heap_box] (min_heap) at (5,0) {
    Min-Heap (`large_half_min_heap`)\\
    Stores larger half of numbers\\
    Smallest of larges at top: $min_l$
};

\node[label_style] at (2.5, 1) {Median};
\draw[arrow] (max_heap.east) -- (min_heap.west) node[midway, above, font=\sffamily\tiny] {$max_s \le min_l$};

\node at (2.5, -1.5) [draw, fill=yellow!20, text width=7cm, align=center, font=\sffamily\scriptsize]
    {All numbers in Max-Heap $\le$ All numbers in Min-Heap. Median is either $max_s$, $min_l$, or their average.};
\end{tikzpicture}
```

**Maintaining Balance:**
To ensure we can easily find the median, we need to keep the two heaps balanced in size:
- The sizes of `small_half_max_heap` and `large_half_min_heap` should differ by at most 1.
- Convention:
    - If total numbers `N` is odd, one heap will have one more element than the other. Let's say `small_half_max_heap` can be larger or equal. (Or `large_half_min_heap` if total odd, depending on which root is the median). The article implies `small_half_max_heap` can be larger.
    - If `N` is odd, let `small_half_max_heap` have $\lceil N/2 \rceil$ elements and `large_half_min_heap` have $\lfloor N/2 \rfloor$ elements. The median is then `small_half_max_heap.top()`.
    - Alternatively (and more common for implementation simplicity for odd `N`): keep `large_half_min_heap` size equal to or one greater than `small_half_max_heap`. If `large` is larger, its top is median.

Labuladong's logic:
- Maintain `small_half_max_heap.size() == large_half_min_heap.size()` OR `small_half_max_heap.size() == large_half_min_heap.size() + 1`.
  - If sizes are equal (total even numbers), median is `(small_half_max_heap.top() + large_half_min_heap.top()) / 2`.
  - If `small_half_max_heap` is larger (total odd numbers), median is `small_half_max_heap.top()`.

## 🛠️ Operations

### `addNum(num)`
1.  **Initial Placement & Invariant Check:**
    -   Add `num` to `small_half_max_heap`.
    -   To maintain the invariant that all elements in `small_half_max_heap` are $\le$ all elements in `large_half_min_heap`, take the largest element from `small_half_max_heap` (its top) and move it to `large_half_min_heap`.
    This ensures `num` is correctly placed relative to the two halves and largest of smalls is transferred.
    ```python
    # small_half_max_heap.push(num)
    # large_half_min_heap.push(small_half_max_heap.pop())
    ```

2.  **Balance Sizes:**
    -   After the above, `large_half_min_heap` might have grown too large relative to `small_half_max_heap`.
    -   If `large_half_min_heap.size() > small_half_max_heap.size()`: Move the smallest element from `large_half_min_heap` (its top) to `small_half_max_heap`.
    ```python
    # if len(large_half_min_heap) > len(small_half_max_heap):
    #     small_half_max_heap.push(large_half_min_heap.pop())
    ```
    Labuladong's article's logic for `addNum` (which leads to `small` heap being potentially larger or equal):
    1. If `small_half_max_heap` is empty or `num <= small_half_max_heap.top()`: Add `num` to `small_half_max_heap`.
    2. Else: Add `num` to `large_half_min_heap`.
    3. Balance heaps:
       - If `small_half_max_heap.size() < large_half_min_heap.size()`: Move from `large_half_min_heap` to `small_half_max_heap`.
       - If `small_half_max_heap.size() > large_half_min_heap.size() + 1`: Move from `small_half_max_heap` to `large_half_min_heap`.

    A slightly more common and arguably simpler way to implement `addNum` which naturally handles the invariant and balancing:
    1. Add `num` to `small_half_max_heap`.
    2. Then, pop from `small_half_max_heap` and add to `large_half_min_heap`. (This ensures `num` passes through `small` and the largest of `small` goes to `large`).
    3. If `len(small_half_max_heap) < len(large_half_min_heap)` (if `small` is supposed to be equal or one larger): pop from `large_half_min_heap` and add to `small_half_max_heap`.

Let's stick to the logic from the article for the MedianFinder example to ensure `small` is larger or equal.
Python's `heapq` is a min-heap. To simulate a max-heap, store negative numbers.

### `findMedian()`
-   If `len(small_half_max_heap) > len(large_half_min_heap)` (odd number of elements): Median is `small_half_max_heap.top()`.
-   If `len(small_half_max_heap) == len(large_half_min_heap)` (even number of elements): Median is `(small_half_max_heap.top() + large_half_min_heap.top()) / 2.0`.

## Python Implementation (MedianFinder for LC295)

```python
import heapq

class MedianFinder:
    def __init__(self):
        # Max-heap for the smaller half (store negative numbers for min-heap simulation)
        self.small_half_max_heap = [] 
        # Min-heap for the larger half
        self.large_half_min_heap = []

    def addNum(self, num: int) -> None:
        # Add to max-heap (small half), then balance by moving largest of small to min-heap (large half)
        heapq.heappush(self.small_half_max_heap, -num) # Push negative for max-heap behavior
        
        # Ensure largest of small_half is moved to large_half
        largest_of_small = -heapq.heappop(self.small_half_max_heap)
        heapq.heappush(self.large_half_min_heap, largest_of_small)
        
        # Balance sizes: ensure small_half_max_heap has equal or one more element than large_half_min_heap
        # This is the balancing rule from the Labuladong article example if small_half_max_heap is to be larger/equal
        # If after moving largest_of_small to large_half_min_heap, small_half_max_heap becomes smaller:
        if len(self.small_half_max_heap) < len(self.large_half_min_heap):
            smallest_of_large = heapq.heappop(self.large_half_min_heap)
            heapq.heappush(self.small_half_max_heap, -smallest_of_large)
        
        # Alternative balancing for the example MedianFinder in the question which seems to prefer `large` being equal or 1 larger:
        # If we pushed to small, then small.pop() to large:
        # Now large might have 2 more than small, or small might have 0 and large has 1.
        # if len(self.large_half_min_heap) > len(self.small_half_max_heap) + 1:
        #    heapq.heappush(self.small_half_max_heap, -heapq.heappop(self.large_half_min_heap))


    def findMedian(self) -> float:
        # If small_half_max_heap has more elements, its top is the median (odd total)
        if len(self.small_half_max_heap) > len(self.large_half_min_heap):
            return float(-self.small_half_max_heap[0]) # Top of max-heap (negated)
        else: # Sizes are equal (even total)
            # Median is average of max of small_half and min of large_half
            return (-self.small_half_max_heap[0] + self.large_half_min_heap[0]) / 2.0

# Example usage (from LeetCode problem description):
# medianFinder = MedianFinder()
# medianFinder.addNum(1)    # arr = [1]
# medianFinder.addNum(2)    # arr = [1, 2]
# medianFinder.findMedian() # return 1.5
# medianFinder.addNum(3)    # arr[1, 2, 3]
# medianFinder.findMedian() # return 2.0
```
*Self-correction on `addNum` to match Labuladong's stated balance condition (`small.size == large.size` or `small.size == large.size + 1`):*
```python
import heapq

class MedianFinder:
    def __init__(self):
        self.small_half_max_heap = []  # stores smaller half, max-heap (negate values)
        self.large_half_min_heap = []  # stores larger half, min-heap

    def addNum(self, num: int) -> None:
        # Goal: small_half_max_heap.size() is either equal to or one greater than large_half_min_heap.size()

        if not self.small_half_max_heap or num <= -self.small_half_max_heap[0]:
            heapq.heappush(self.small_half_max_heap, -num)
        else:
            heapq.heappush(self.large_half_min_heap, num)
        
        # Balance the heaps
        if len(self.small_half_max_heap) < len(self.large_half_min_heap):
            # large_half_min_heap has more elements than it should relative to small_half_max_heap
            heapq.heappush(self.small_half_max_heap, -heapq.heappop(self.large_half_min_heap))
        elif len(self.small_half_max_heap) > len(self.large_half_min_heap) + 1:
            # small_half_max_heap has too many elements
            heapq.heappush(self.large_half_min_heap, -heapq.heappop(self.small_half_max_heap))

    def findMedian(self) -> float:
        if len(self.small_half_max_heap) > len(self.large_half_min_heap):
            # Odd number of elements, median is top of small_half_max_heap
            return float(-self.small_half_max_heap[0])
        else: # len(self.small_half_max_heap) == len(self.large_half_min_heap)
            # Even number of elements, median is average of tops of both heaps
            return (-self.small_half_max_heap[0] + self.large_half_min_heap[0]) / 2.0

```
The corrected `addNum` first places the new number into the appropriate heap based on its value relative to the current "median boundary" (approximated by `-self.small_half_max_heap[0]`). Then, it rebalances the heap sizes.

## Complexity Analysis
-   **`addNum(num)`**:
    -   Heap push/pop operations: $O(\log N)$.
    -   Total: $O(\log N)$.
-   **`findMedian()`**:
    -   Accessing heap top(s): $O(1)$.
    -   Total: $O(1)$.
-   **Space Complexity:** $O(N)$ to store all numbers in the two heaps.

## 总结 (Summary)
- The two-heap approach (max-heap for smaller half, min-heap for larger half) provides an efficient way to find medians in a data stream.
- `addNum` involves inserting into one heap and potentially rebalancing by moving an element to the other, all in $O(\log N)$ time.
- `findMedian` takes $O(1)$ time by looking at the top(s) of the heap(s).
- This method is a clever application of [[Interview/Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization|binary heaps]] (Priority Queues).

---
Parent: [[Interview/Concept/Data Structures/Heap/index|Heap and Priority Queue Concepts]]
Related Problem: [[Interview/Practice/LeetCode/LC295 - Find Median from Data Stream|LC295 - Find Median from Data Stream]]
