---
tags: [concept/data_structures, concept/custom_design, type/algorithm, pattern/oop_design, pattern/heap, pattern/merge_k_sorted_lists, course/labuladong]
aliases: [Design Twitter, Twitter Timeline Algorithm, 面向对象设计朋友圈]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/设计朋友圈时间线功能.md
---

> [!NOTE] Source Annotation
> Content adapted from [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典数据结构设计/设计朋友圈时间线功能.md|设计朋友圈时间线功能 by Labuladong]].
> This note explains the design of a simplified Twitter-like system, focusing on [[Interview/Practice/LeetCode/LC355 - Design Twitter|LeetCode 355 - Design Twitter]].

# Design Twitter-like Timeline Functionality

Designing a system like Twitter's timeline involves managing users, their posts, follow relationships, and generating a chronologically sorted news feed. [[Interview/Practice/LeetCode/LC355 - Design Twitter|LeetCode 355 - Design Twitter]] asks for such a system.

## 🎯 Core Requirements (LC355)
- `postTweet(userId, tweetId)`: User posts a new tweet.
- `getNewsFeed(userId)`: Retrieve the 10 most recent tweets from people the user follows (including self). Tweets must be ordered from most recent to oldest.
- `follow(followerId, followeeId)`: User `followerId` starts following `followeeId`.
- `unfollow(followerId, followeeId)`: User `followerId` stops following `followeeId`.

## 🧱 Object-Oriented Design (OOP)

A common approach is to use classes to model the entities:

### 1. `Tweet` Class
- Stores information about a single tweet.
- Attributes:
    - `id`: The tweet's unique ID.
    - `time`: A timestamp indicating when it was posted (global, incrementing).
    - `next`: A pointer to the previous tweet by the same user (forms a singly linked list of a user's tweets, newest first).
```python
# class Tweet:
#     def __init__(self, id: int, time: int):
#         self.id = id
#         self.time = time
#         self.next = None # Points to the previous tweet by this user
```
Visual: `![](/algo/images/design-twitter/tweet.jpg)`

### 2. `User` Class
- Stores information about a single user.
- Attributes:
    - `id`: The user's unique ID.
    - `followed`: A set of user IDs that this user follows. Crucially, a user always follows themselves.
    - `head`: A pointer to the head of their tweet linked list (their most recent tweet).
- Methods:
    - `__init__(userId)`: Initializes user, adds self to `followed`.
    - `follow(followeeId)`: Adds `followeeId` to `self.followed`.
    - `unfollow(followeeId)`: Removes `followeeId` from `self.followed` (cannot unfollow self).
    - `post(tweetId, global_timestamp)`: Creates a new `Tweet` object, adds it to the front of this user's tweet list (updates `self.head`).
```python
# class User:
#     def __init__(self, userId: int):
#         self.id = userId
#         self.followed = {userId} # User follows self by default
#         self.head = None # Head of user's tweet linked list

#     def follow(self, followeeId: int):
#         self.followed.add(followeeId)

#     def unfollow(self, followeeId: int):
#         if followeeId != self.id: # Cannot unfollow self
#             self.followed.discard(followeeId) # Use discard to avoid error if not present

#     def post(self, tweetId: int, timestamp: int):
#         twt = Tweet(tweetId, timestamp)
#         twt.next = self.head # New tweet points to old head
#         self.head = twt       # New tweet becomes new head
```
Visual: `![](/algo/images/design-twitter/user.jpg)`

### 3. `Twitter` Class (Main System)
- Manages users and the global timestamp.
- Attributes:
    - `userMap`: A hash map (`userId -> User_object`) for quick lookup of users.
    - `timestamp`: A global, static integer, incremented with each post.
- Methods (implementing the API):
    - `__init__()`: Initializes `userMap` and `timestamp`.
    - `_ensure_user_exists(userId)`: Helper to create a `User` object if one doesn't exist for `userId`.
    - `postTweet(userId, tweetId)`: Ensures user exists, calls `user.post()`, increments global timestamp.
    - `follow(followerId, followeeId)`: Ensures both users exist, calls `follower.follow(followeeId)`.
    - `unfollow(followerId, followeeId)`: Ensures follower exists, calls `follower.unfollow(followeeId)`.
    - `getNewsFeed(userId)`: This is the most complex part.

## 📰 `getNewsFeed(userId)` Algorithm: Merge K Sorted Lists

The core challenge is generating the news feed efficiently and correctly ordered.
1.  **Identify Sources:** Get the set of `userIds` that `userId` follows (from `userMap[userId].followed`).
2.  **Collect Tweet Lists:** For each followed user, get the head of their tweet linked list (e.g., `userMap[followed_id].head`). Each of these lists is sorted by time (newest first).
3.  **Merge K Sorted Lists:** We now have `k` sorted linked lists of tweets (where `k` is the number of followed users). We need to merge them into a single list, sorted by time (newest first), and take the top 10.
    - This is a classic "Merge K Sorted Lists" problem, solvable efficiently using a **Min-Heap (Priority Queue)**.
    - The Priority Queue will store `Tweet` objects. Since Python's `heapq` is a min-heap, and we want newest first (largest timestamp), we can store `(-time, tweet_object)` or define `__lt__` in `Tweet` to sort by time descending. Labuladong's Java solution uses a custom comparator for the `PriorityQueue` to order by `tweet.time` descending.

**`getNewsFeed` Steps:**
1.  If `userId` not in `userMap`, return empty list.
2.  Get `followed_user_ids = self.userMap[userId].followed`.
3.  Initialize a Min-Priority Queue `pq`. (For Python, store `(-tweet.time, tweet_id, tweet_object_ref_for_next)` to simulate max-heap by time). A simpler way is to store `Tweet` objects directly if `Tweet` class implements `__lt__` for heap ordering.
    ```python
    # In Python, to use heapq as a max-heap for time:
    # class Tweet:
    #   ...
    #   def __lt__(self, other): return self.time > other.time # For max-heap behavior with heapq
    # Then push Tweet objects directly. Or store (-time, tweet_id, tweet_obj_itself)
    ```
4.  For each `uid` in `followed_user_ids`:
    -   If `self.userMap[uid].head` exists, add `self.userMap[uid].head` (the first tweet object) to `pq`.
5.  Initialize `news_feed_result = []`.
6.  While `pq` is not empty AND `len(news_feed_result) < 10`:
    a.  Pop the `tweet_with_max_time` from `pq`.
    b.  Add `tweet_with_max_time.id` to `news_feed_result`.
    c.  If `tweet_with_max_time.next` (the next older tweet from the same user) exists, add it to `pq`.
7.  Return `news_feed_result`.

Visual for merge: `![](/algo/images/design-twitter/merge.gif)`

## Complexity Analysis
-   `N`: Number of users, `M`: Number of tweets, `K`: Avg number of users someone follows.
-   `postTweet`: $O(1)$.
-   `follow`/`unfollow`: $O(1)$.
-   `getNewsFeed`:
    -   Adding initial heads to PQ: $O(K \log K)$.
    -   Loop runs at most 10 times. Each pop/push from PQ is $O(\log K)$.
    -   Total: $O(K \log K)$ for up to 10 results. If we needed all tweets, it would be $O(M_{total\_tweets} \log K)$.
-   Space: $O(N + M)$ for storing users and all tweets. `getNewsFeed` uses $O(K)$ for PQ.

## 总结 (Summary)
- Designing Twitter's timeline involves OOP for `Tweet` and `User` objects.
- Key data structures:
    - `HashMap` for `userId -> User` mapping.
    - `Set` for `user.followed` list.
    - Linked list for each user's tweets (`user.head`).
    - Min-Priority Queue (Heap) for merging tweet lists in `getNewsFeed`.
- The `getNewsFeed` operation is essentially a "Merge K Sorted Lists" problem, efficiently solved with a heap.
- A global timestamp is used to order tweets chronologically.

---
Parent: [[Interview/Concept/Data Structures/Custom Design/index|Custom Data Structure Design]]
Related Problem: [[Interview/Practice/LeetCode/LC355 - Design Twitter|LC355 - Design Twitter]]
Related Concepts: [[Interview/Concept/Data Structures/Heap/00 - Binary Heap - Principles and Visualization|Heaps/Priority Queues]], [[Interview/Concept/Algorithms/Linked List/Merging Sorted Lists|Merge K Sorted Lists (Pattern)]]
