---
tags: [concept/data_structures, concept/stack, type/technique, pattern/monotonic_stack, course/labuladong]
aliases: [Monotonic Stack, 单调栈]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典队列_栈算法/单调栈算法模板解决三道例题.md
---

> [!NOTE] Source Annotation
> Content adapted from Labuladong's article "单调栈算法模板解决三道例题" (Monotonic Stack Algorithm Template for Three Examples).
> Source: [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典队列_栈算法/单调栈算法模板解决三道例题.md]]

# Monotonic Stack Pattern

A Monotonic Stack is a [[Interview/Concept/Data Structures/Stack/index|Stack]] data structure where the elements are always maintained in a specific order, either monotonically increasing or monotonically decreasing. This property makes it useful for efficiently solving problems that involve finding the "next greater element," "previous smaller element," etc., for elements in a sequence.

## 🎯 Core Idea

The fundamental principle of a monotonic stack is to maintain the monotonic property by selectively popping elements from the stack before pushing a new element.

**Analogy (Labuladong's Height Example for Next Greater Element):**
Imagine people of different heights standing in a line. For each person, find the next person to their right who is taller.
- If you process people from right to left and use a stack:
    - When considering person `nums[i]`:
        - Pop everyone from the stack shorter than or equal to `nums[i]` (they are blocked by `nums[i]` and cannot be the "next greater" for elements further to the left that are shorter than `nums[i]`).
        - If the stack is now empty, `nums[i]` has no taller person to its right.
        - Else, the person at the top of the stack is `nums[i]`'s next greater element.
        - Push `nums[i]` onto the stack (it might be the next greater for elements to its left).
This naturally maintains a monotonically decreasing stack (from bottom to top).

## 🛠️ Template: Next Greater Element

This template finds the next greater element for each element in an array `nums`. The result array `res` will store these, or -1 if no greater element exists.

```python
def nextGreaterElement_template(nums: list[int]) -> list[int]:
    n = len(nums)
    res = [0] * n  # To store results
    stack = []     # Monotonically decreasing stack (stores elements or indices)

    # Iterate from right to left
    for i in range(n - 1, -1, -1):
        # While stack is not empty and current element nums[i] >= stack top
        # (If stack stores elements directly)
        while stack and nums[i] >= stack[-1]:
            stack.pop() # Pop elements smaller than or equal to current

        # Stack top is now the next greater element (or stack is empty)
        res[i] = stack[-1] if stack else -1

        # Push current element onto stack
        stack.append(nums[i])

    return res

# Example: nums = [2,1,2,4,3] -> res = [4,2,4,-1,-1]
# Stack (stores elements):
# i=4 (3): stack=[], res[4]=-1. stack=[3]
# i=3 (4): stack=[3]. 4>3, pop 3. stack=[]. res[3]=-1. stack=[4]
# i=2 (2): stack=[4]. res[2]=4. stack=[4,2] (top is 2)
# i=1 (1): stack=[4,2]. 1<2. res[1]=2. stack=[4,2,1]
# i=0 (2): stack=[4,2,1]. 2>1, pop 1. stack=[4,2]. 2==2, pop 2. stack=[4]. res[0]=4. stack=[4,2]
```
Labuladong's visualization `![](/algo/images/monotonic-stack/1.jpeg)` illustrates this.

**Complexity:** $O(N)$. Each element is pushed and popped at most once.

## Variants and Applications

### 1. Next Greater Element (Problems)
-   [[Interview/Practice/LeetCode/LC496 - Next Greater Element I|LC496 - Next Greater Element I]]: Find NGE for elements of `nums1` within `nums2`. Compute NGE for all of `nums2` first using the template, then use a map for lookups.
-   [[Interview/Practice/LeetCode/LC503 - Next Greater Element II|LC503 - Next Greater Element II]]: Handles circular arrays. The trick is to conceptually "double" the array (e.g., iterate `2*n-1` down to `0`, using `nums[i % n]`). The stack stores elements from the "first pass" to find NGEs for elements in the "second pass" (which are actually elements from the start of the array wrapping around).
    Labuladong's visualization `![](/algo/images/monotonic-stack/2.jpeg)` shows the doubled array concept.

### 2. Daily Temperatures (LC739)
-   [[Interview/Practice/LeetCode/LC739 - Daily Temperatures|LC739 - Daily Temperatures]]: Given daily temperatures, find how many days to wait for a warmer temperature.
-   This is a "next greater element" problem, but instead of the value, we need the *index difference*.
-   The stack should store *indices* of temperatures, not the temperatures themselves.
-   When processing `temperatures[i]`:
    -   While stack is not empty and `temperatures[i] >= temperatures[stack.top_index]`: Pop from stack.
    -   If stack is empty, `res[i] = 0`.
    -   Else, `res[i] = stack.top_index - i`.
    -   Push `i` onto stack.

### 3. Other Variations (from `【练习】单调栈的几种变体及经典习题.md`)
The exercise list placeholder `【练习】单调栈的几种变体及经典习题.md` suggests further variations:
-   **Next Smaller Element / Previous Greater Element / Previous Smaller Element:** Modify the comparison (`<` vs `>`) and iteration direction (left-to-right vs. right-to-left).
-   **Handling Duplicates (Strictly Greater/Smaller):** Adjust comparison (e.g., `nums[i] > stack[-1]` for strictly greater).

## 总结 (Summary)
- Monotonic Stack maintains elements in increasing or decreasing order.
- It's highly effective for finding "next/previous greater/smaller" type elements in a sequence, typically in $O(N)$ time.
- Iterate through the array (forward or backward depending on "next" vs "previous").
- Before pushing the current element, pop elements from the stack that violate the monotonic property *relative to the current element*.
- The element at the stack top after popping (or lack thereof) provides the answer for the current element. Then, push the current element (or its index).
- Crucial for problems like Next Greater Element, Daily Temperatures, Largest Rectangle in Histogram, etc.

---
Parent: [[Interview/Concept/Data Structures/Stack/Applications/index|Stack Applications]]
Next: (Could be specific Monotonic Stack problem like Largest Rectangle in Histogram, or more variations)
Related: [[Interview/Practice/LeetCode/LC496 - Next Greater Element I|LC496]], [[Interview/Practice/LeetCode/LC503 - Next Greater Element II|LC503]], [[Interview/Practice/LeetCode/LC739 - Daily Temperatures|LC739]]
