---
tags: [concept/data_structures, concept/stack, concept/queue, type/technique, pattern/data_structure_design]
aliases: [Implement Stack using Queues, Implement Queue using Stacks, 栈和队列的相互实现]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典队列_栈算法/队列实现栈以及栈实现队列.md
---

> [!NOTE] Source Annotation
> Content adapted from Labuladong's article "队列实现栈以及栈实现队列" (Implement Stack using Queues and Queue using Stacks).
> Source: [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典队列_栈算法/队列实现栈以及栈实现队列.md]]

# Stack and Queue Inter-conversion

This note explores how to implement the functionality of a stack using queues, and a queue using stacks. These are common interview questions that test understanding of these fundamental data structures.

## 1. Implement Queue using Stacks (LC232, <PERSON><PERSON><PERSON> Offer 09)

A queue follows First-In, First-Out (FIFO) order, while a stack follows Last-In, First-Out (LIFO) order. To simulate a queue using stacks, we typically use two stacks: `s1` (for input/push) and `s2` (for output/pop).

```mermaid
graph LR
    subgraph "Two Stacks for Queue"
        InputData["Input Data"] -- Push --> S1["Stack s1 (Input)"]
        S1 -- "Transfer (when s2 empty)" --> S2["Stack s2 (Output)"]
        S2 -- Pop/Peek --> OutputData["Output Data (FIFO)"]
    end
```

**Core Idea:**
-   **`push(x)`:** Add element `x` to `s1`. This is $O(1)$.
-   **`pop()` / `peek()`:**
    -   If `s2` is not empty, `s2.pop()` or `s2.peek()` gives the oldest element (FIFO).
    -   If `s2` is empty, transfer all elements from `s1` to `s2`. This reverses their order, making `s2` hold elements in FIFO order. Then, perform `pop` or `peek` on `s2`.
        - The transfer takes $O(N)$ time, but it only happens when `s2` is empty. Each element is moved from `s1` to `s2` at most once.

**Amortized Complexity:**
-   `push`: $O(1)$.
-   `pop`, `peek`: Amortized $O(1)$. Although a single `pop` or `peek` can take $O(N)$ if a transfer occurs, over a sequence of $N$ operations, the total time is $O(N)$, making the average cost per operation $O(1)$.

**Related Problems:**
-   [[Interview/Practice/LeetCode/LC232 - Implement Queue using Stacks|LC232 - Implement Queue using Stacks]]
-   剑指 Offer 09. 用两个栈实现队列 (Equivalent to LC232)

Labuladong's visualization for this concept (`![](/algo/images/stack-queue/4.jpg)`) shows `s1` elements being poured into `s2`, reversing their order.

## 2. Implement Stack using Queues (LC225)

A stack (LIFO) can be implemented using one or two queues (FIFO).

### Using One Queue
- **`push(x)`:** Add `x` to the queue. Then, to make `x` the "top" (front of the queue for LIFO pop), rotate all other elements in the queue by dequeuing them and re-enqueuing them behind `x`.
    - Example: Queue `[1, 2]`. `push(3)`. `q.add(3)` -> `[1,2,3]`.
    - Rotate: `q.add(q.popleft())` (move 1) -> `[2,3,1]`.
    - Rotate: `q.add(q.popleft())` (move 2) -> `[3,1,2]`. Now 3 is at front.
    - Complexity: `push` is $O(N)$, `pop/top` is $O(1)$.
- **`pop()`:** `queue.popleft()`. $O(1)$.
- **`top()`:** `queue[0]`. $O(1)$.

Labuladong's visualization (`![](/algo/images/stack-queue/5.jpg)` and `![](/algo/images/stack-queue/6.jpg)`) demonstrates this rotation strategy. The newly pushed element is moved to the front of the queue.

### Using Two Queues (Alternative, often less efficient for some ops)
-   **`push(x)`:** Add `x` to `q1`. $O(1)$.
-   **`pop()`:**
    -   Move all but the last element from `q1` to `q2`.
    -   The last element removed from `q1` is the result.
    -   Swap `q1` and `q2` (so `q2` becomes the new `q1` for future pushes).
    -   Complexity: `pop` is $O(N)$. `top` is also $O(N)$. `push` is $O(1)$.

The single queue approach is generally preferred for a more balanced complexity across operations if `push` being expensive is acceptable.

**Related Problems:**
-   [[Interview/Practice/LeetCode/LC225 - Implement Stack using Queues|LC225 - Implement Stack using Queues]]

## 总结 (Summary)
- Implementing a queue with two stacks leverages one stack for input and another for output (transferring elements reverses order for FIFO). Amortized $O(1)$ for all operations.
- Implementing a stack with one queue involves rotating elements on push to maintain LIFO order at the queue's front. `push` is $O(N)$, `pop/top` are $O(1)$.
- These problems test understanding of stack (LIFO) and queue (FIFO) fundamental properties.

---
Parent: [[Interview/Concept/Data Structures/Stack/Applications/index|Stack Applications]], [[Interview/Concept/Data Structures/Queue/Applications/index|Queue Applications]]
Related: [[Interview/Concept/Data Structures/Stack/index|Stacks]], [[Interview/Concept/Data Structures/Queue/index|Queues]]
