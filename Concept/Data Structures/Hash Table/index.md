---
tags: [index, concept/data_structures, concept/hash_table, concept/hash_map, concept/hash_set]
aliases: [Hash Table Index, HashMap Index, HashSet Index, 哈希表索引]
---

# Hash Table Structures (Hash Maps & Hash Sets)

This section delves into the principles, implementations, and variations of hash tables, which are fundamental data structures for efficient key-based lookups. Hash tables form the basis for Hash Maps (dictionaries) and Hash Sets.

## Core Principles:
- [[Interview/Concept/Data Structures/Hash Map/00 - Hash Map - Core Principles|Hash Map - Core Principles]]
    - Hash Function
    - Hash Collisions
    - Load Factor & Rehashing
    - Key Immutability

## Collision Resolution Techniques:
- **Chaining (Separate Chaining):**
    - [[Interview/Concept/Data Structures/Hash Map/01 - Hash Map - Chaining (Separate Chaining)|Hash Map - Chaining (Principles & Implementation)]]
- **Open Addressing:**
    - [[Interview/Concept/Data Structures/Hash Map/02a - Hash Map - Open Addressing Principles and Challenges|Open Addressing - Principles & Challenges]] (Focus on Linear Probing difficulties like deletion)
    - [[Interview/Concept/Data Structures/Hash Map/02 - Hash Map - Open Addressing (Linear Probing)|Open Addressing - Linear Probing Implementation]]

## Variations and Enhanced Hash Maps:
- [[Interview/Concept/Data Structures/Hash Map/03 - LinkedHashMap - Maintaining Insertion Order|LinkedHashMap - Maintaining Insertion Order]]
- [[Interview/Concept/Data Structures/Hash Map/04 - ArrayHashMap - Random Key Access|ArrayHashMap - O(1) Random Key Access]]

## Hash Sets:
- [[Interview/Concept/Data Structures/Hash Set/00 - Hash Set - Principles and Implementation|Hash Set - Principles and Implementation]] (Typically a wrapper around HashMap)

## Visualization
```mermaid
graph TD
    HT["Hash Table Concepts"] --> Core["[[Interview/Concept/Data Structures/Hash Map/00 - Hash Map - Core Principles|Core Principles]]"]
    Core --> HF["Hash Function"]
    Core --> HC["Hash Collisions"]
    Core --> LF["Load Factor & Rehashing"]
    Core --> KI["Key Immutability"]

    HT --> CollisionRes["Collision Resolution"]
    CollisionRes --> Chaining["[[Interview/Concept/Data Structures/Hash Map/01 - Hash Map - Chaining (Separate Chaining)|Chaining]]"]
    CollisionRes --> OpenAddr["Open Addressing"]
    OpenAddr --> OpenAddrPrinc["[[Interview/Concept/Data Structures/Hash Map/02a - Hash Map - Open Addressing Principles and Challenges|Principles & Challenges]]"]
    OpenAddr --> LinProbImpl["[[Interview/Concept/Data Structures/Hash Map/02 - Hash Map - Open Addressing (Linear Probing)|Linear Probing Impl.]]"]


    HT --> Variants["Variations/Enhancements"]
    Variants --> LinkedHM["[[Interview/Concept/Data Structures/Hash Map/03 - LinkedHashMap - Maintaining Insertion Order|LinkedHashMap (Order)]]"]
    Variants --> ArrayHM["[[Interview/Concept/Data Structures/Hash Map/04 - ArrayHashMap - Random Key Access|ArrayHashMap (RandomKey)]]"]

    HT --> HS["Hash Sets"]
    HS --> HSPrinc["[[Interview/Concept/Data Structures/Hash Set/00 - Hash Set - Principles and Implementation|Principles & Impl.]]"]

    classDef main fill:#fff8dc,stroke:#b8860b,stroke-width:2px;
    class HT main;
```

---
Parent: [[Interview/Concept/Data Structures/index|Data Structures Index]]
