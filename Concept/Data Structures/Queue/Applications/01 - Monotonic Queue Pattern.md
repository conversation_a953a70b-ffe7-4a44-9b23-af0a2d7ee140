---
tags: [concept/data_structures, concept/queue, type/technique, pattern/monotonic_queue, pattern/sliding_window, course/labuladong]
aliases: [Monotonic Queue, 单调队列]
source_file_path: Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典队列_栈算法/单调队列结构解决滑动窗口问题.md
---

> [!NOTE] Source Annotation
> Content adapted from Labuladong's article "单调队列结构解决滑动窗口问题" (Monotonic Queue Structure for Sliding Window Problems).
> Source: [[Interview/labuladong 的算法笔记/markdown_export_本站简介/第一章、经典数据结构算法/经典队列_栈算法/单调队列结构解决滑动窗口问题.md]]

# Monotonic Queue Pattern

A Monotonic Queue is a specialized [[Interview/Concept/Data Structures/Queue/index|Queue]] (often implemented with a [[Interview/Concept/Data Structures/Deque/index|Deque]]) that maintains its elements in either monotonically increasing or decreasing order. This structure is particularly useful for solving problems that require finding the maximum or minimum element within a sliding window in $O(1)$ time per window position, leading to an overall $O(N)$ solution for sliding window problems.

## 🎯 Core Idea: Maintaining Order and Extremes

The Monotonic Queue efficiently supports these operations:
-   `push(val)`: Add `val` to the rear of the queue while maintaining the monotonic property.
-   `pop(val)`: Remove `val` from the front of the queue (if it's indeed the front element).
-   `max()` / `min()`: Get the maximum/minimum element currently in the queue in $O(1)$ time. (This is the key advantage).

**How `push(val)` maintains monotonicity (for a decreasing monotonic queue to find max):**
-   When `val` is to be pushed:
    -   While the queue is not empty and `val` is greater than the element at the *rear* of the queue (`q.back()`):
        -   Pop from the rear (`q.pop_back()`). These smaller elements are "dominated" by `val` and will never be the maximum if `val` is present and to their right.
    -   Push `val` to the rear.
- This ensures the queue elements are in decreasing order from front to back. The front element (`q.front()`) is always the maximum.
Labuladong's image `![](/algo/images/monotonic-queue/3.png)` illustrates this: "体重大的会把前面体重不足的压扁 (Heavier people squash lighter ones in front)".

**How `pop(val)` works (for a decreasing monotonic queue):**
-   When the sliding window moves and an element `val` leaves the window from the left:
    -   If `val` is currently the maximum element in the window (i.e., `val == q.front()`):
        -   Pop from the front of the monotonic queue (`q.pop_front()`).
-   The element `val` might have already been "squashed" during a previous `push` operation and may not be in the monotonic queue at all, or not at its front. The check `val == q.front()` handles this.

## 🛠️ API for Sliding Window Maximum (Conceptual)

Labuladong describes a `MonotonicQueue` class for this:
```python
# Conceptual MonotonicQueue for finding maximums (stores elements themselves)
# Uses a deque internally.
import collections

class MonotonicQueueMax:
    def __init__(self):
        self.q = collections.deque() # Stores elements in decreasing order

    def push(self, val: int):
        # Remove elements smaller than val from the rear
        while self.q and val > self.q[-1]:
            self.q.pop()
        self.q.append(val)

    def pop(self, val: int):
        # If val is the current max (front of queue), remove it
        if self.q and val == self.q[0]:
            self.q.popleft()

    def max(self) -> int:
        if self.q:
            return self.q[0] # Front element is always the max
        return -1 # Or raise error for empty
```

## Sliding Window Maximum Framework (using Monotonic Queue)
For a problem like [[Interview/Practice/LeetCode/LC239 - Sliding Window Maximum|LC239 - Sliding Window Maximum]]:

```python
# Conceptual framework
# def maxSlidingWindow(nums: list[int], k: int) -> list[int]:
#     window = MonotonicQueueMax()
#     results = []
#     
#     for i in range(len(nums)):
#         # Add nums[i] to window
#         window.push(nums[i])
#         
#         # If window has reached size k
#         if i >= k - 1:
#             # Record current window's maximum
#             results.append(window.max())
#             
#             # Remove element nums[i - k + 1] (which is now leaving window from left)
#             window.pop(nums[i - k + 1])
#             
#     return results
```
Labuladong's image `![](/algo/images/monotonic-queue/1.png)` shows the window sliding and the `MonotonicQueue` state.

**Complexity:**
- Each element in `nums` is pushed onto and popped from the monotonic queue at most once.
- `push` involves a `while` loop, but each element enters and leaves this loop's scope once in total.
- Overall time complexity: $O(N)$.
- Space complexity: $O(K)$ for the monotonic queue (it stores at most `k` elements).

## Variations and Considerations
- **Monotonic Increasing Queue (for `min()`):** Modify `push` to pop elements *greater* than `val` from the rear. The front will be the minimum.
- **Storing Indices:** Sometimes, it's better to store indices in the monotonic queue instead of values, especially if values can be duplicate and you need to distinguish them or if the problem relates to index positions.
- **General Implementation:** [[Interview/Concept/Data Structures/Queue/Applications/02 - Monotonic Queue Universal Implementation|Monotonic Queue Universal Implementation]] might involve storing `(value, count)` or handling more complex scenarios.

## 总结 (Summary)
- Monotonic Queue maintains elements in sorted order (increasing or decreasing).
- `push` operation ensures monotonicity by removing elements from the rear that would violate it.
- `pop` operation removes an element from the front if it matches the element leaving the sliding window and is currently the extreme.
- Allows $O(1)$ retrieval of max/min in a sliding window.
- Leads to $O(N)$ solutions for many sliding window min/max problems.

---
Parent: [[Interview/Concept/Data Structures/Queue/Applications/index|Queue Applications]]
Next: [[Interview/Concept/Data Structures/Queue/Applications/02 - Monotonic Queue Universal Implementation|Monotonic Queue Universal Implementation]] (placeholder based on Labuladong's structure)
Related: [[Interview/Practice/LeetCode/LC239 - Sliding Window Maximum|LC239]], [[Interview/Concept/Algorithms/Sliding Window/00 - Sliding Window - Core Framework|Sliding Window Framework]]
