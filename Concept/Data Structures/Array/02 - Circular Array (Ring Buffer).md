---
tags: [concept/data_structures, concept/array, concept/circular_array, type/technique, pattern/ring_buffer]
aliases: [<PERSON> Buffer, Cyclic Buffer, 环形数组]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/数组链表的种种变换/环形数组技巧及实现.md]].
> The Circular Array technique allows $O(1)$ additions/removals at both ends of a queue-like structure implemented with an array.

# Circular Array (Ring Buffer) Technique

A Circular Array, also known as a Ring Buffer or Cyclic Buffer, is a data structure that uses a fixed-size array as if it were connected end-to-end, forming a circle. This technique is particularly useful for implementing queues (especially [[Interview/Concept/Data Structures/Deque/index|Deques (Double-Ended Queues)]]) or buffers where elements are added and removed from both ends efficiently.

## 🔄 Core Principle: Modulo Arithmetic

A standard array is linear. To make it behave like a circle, we use the modulo operator (`%`). When an index goes past one end of the array, it "wraps around" to the other end.

- **Physical Storage:** A standard, linear array of fixed `capacity`.
- **Logical View:** A circle where `array[capacity-1]` is followed by `array[0]`.

**Key Components for Implementation:**
1.  `array`: The underlying fixed-size array.
2.  `capacity`: The total size of the `array`.
3.  `head` (or `start`, `front`): Index pointing to the first logical element.
4.  `tail` (or `end`, `rear`): Index pointing to the position *after* the last logical element (i.e., where the next element would be inserted at the end).
5.  `count` (or `size`): Number of actual elements stored. This helps distinguish between a full and empty buffer when `head == tail`.

```python
# Conceptual iteration over a circular array
# arr = [1, 2, 3, 4, 5] (capacity 5)
# Assume head=0, count=5
# current_idx = head
# for _ in range(count * 2): # Iterate twice around
#     print(arr[current_idx])
#     current_idx = (current_idx + 1) % len(arr) 
# Output: 1 2 3 4 5 1 2 3 4 5
```
The expression `(index + 1) % capacity` handles forward wrap-around, and `(index - 1 + capacity) % capacity` handles backward wrap-around (the `+ capacity` ensures the result is non-negative before modulo if `index-1` is negative).

## ⚙️ Operations (e.g., for a Deque)

Let's consider implementing a deque using a circular array.

### `addFirst(value)` (Add to Head)
1.  If `count == capacity` (full): Resize the array (or throw an error if fixed size).
2.  Decrement `head`: `head = (head - 1 + capacity) % capacity`.
3.  Place `value` at `array[head]`.
4.  Increment `count`.
   - Time Complexity: $O(1)$ (amortized if resizing is involved).

### `addLast(value)` (Add to Tail)
1.  If `count == capacity` (full): Resize or error.
2.  Place `value` at `array[tail]`.
3.  Increment `tail`: `tail = (tail + 1) % capacity`.
4.  Increment `count`.
   - Time Complexity: $O(1)$ (amortized).

### `removeFirst()` (Remove from Head)
1.  If `count == 0` (empty): Error.
2.  Retrieve value from `array[head]`.
3.  (Optional: Clear `array[head]`, e.g., set to `None`).
4.  Increment `head`: `head = (head + 1) % capacity`.
5.  Decrement `count`.
6.  (Optional: Resize down if `count` is very small compared to `capacity`).
   - Time Complexity: $O(1)$ (amortized).

### `removeLast()` (Remove from Tail)
1.  If `count == 0` (empty): Error.
2.  Decrement `tail`: `tail = (tail - 1 + capacity) % capacity`.
3.  Retrieve value from `array[tail]`.
4.  (Optional: Clear `array[tail]`).
5.  Decrement `count`.
6.  (Optional: Resize down).
   - Time Complexity: $O(1)$ (amortized).

**Labuladong's Visualizer (`div_cycle-array-example`):**
The visualization shows how `start` (head) and `end` (tail) pointers move.
- `addLast`: `end` moves right (wraps around).
- `addFirst`: `start` moves left (wraps around).
- `removeLast`: `end` moves left.
- `removeFirst`: `start` moves right.

The actual elements are in the segment from `start` up to (but not including) `end`, considering wrap-around. The number of elements is `count`.

## Example Implementation (Python - Labuladong's `CycleArray`)

Labuladong provides a `CycleArray` class that implements these operations, including resizing.

```python
class CycleArray:
    def __init__(self, initial_capacity=1): # Default capacity 1, will resize on first add
        self.arr = [None] * initial_capacity
        self.capacity = initial_capacity
        self.start = 0  # Inclusive index of the first element
        self.end = 0    # Exclusive index of the position after the last element
        self.count = 0  # Number of elements in the array

    def _resize(self, new_capacity):
        new_arr = [None] * new_capacity
        for i in range(self.count):
            new_arr[i] = self.arr[(self.start + i) % self.capacity]
        self.arr = new_arr
        self.capacity = new_capacity
        self.start = 0
        self.end = self.count # After copying, elements are [0...count-1]

    def add_first(self, val):
        if self.count == self.capacity:
            self._resize(2 * self.capacity if self.capacity > 0 else 1)
        
        self.start = (self.start - 1 + self.capacity) % self.capacity
        self.arr[self.start] = val
        self.count += 1

    def add_last(self, val):
        if self.count == self.capacity:
            self._resize(2 * self.capacity if self.capacity > 0 else 1)
            
        self.arr[self.end] = val
        self.end = (self.end + 1) % self.capacity
        self.count += 1
    
    def remove_first(self):
        if self.count == 0:
            raise IndexError("remove from empty CycleArray")
        val = self.arr[self.start]
        self.arr[self.start] = None # Help GC
        self.start = (self.start + 1) % self.capacity
        self.count -= 1
        if self.count > 0 and self.count == self.capacity // 4:
            self._resize(self.capacity // 2)
        return val

    def remove_last(self):
        if self.count == 0:
            raise IndexError("remove from empty CycleArray")
        self.end = (self.end - 1 + self.capacity) % self.capacity
        val = self.arr[self.end]
        self.arr[self.end] = None # Help GC
        self.count -= 1
        if self.count > 0 and self.count == self.capacity // 4:
            self._resize(self.capacity // 2)
        return val

    def get_first(self):
        if self.count == 0:
            raise IndexError("get from empty CycleArray")
        return self.arr[self.start]

    def get_last(self):
        if self.count == 0:
            raise IndexError("get from empty CycleArray")
        # end is exclusive, so actual last element is at (end - 1)
        return self.arr[(self.end - 1 + self.capacity) % self.capacity]
        
    def is_full(self): # More accurately, needs resize
        return self.count == self.capacity

    def is_empty(self):
        return self.count == 0

    def get_size(self):
        return self.count
```
**Note on `start` and `end` definition (Labuladong's specific implementation):**
- `start`: Points to the first valid element (inclusive).
- `end`: Points to the position *after* the last valid element (exclusive).
- When `start == end`, the array can be either full or empty. `count` distinguishes this.
  - If `count == 0` and `start == end`, it's empty.
  - If `count == capacity` and `start == end` (after `end` wraps around and meets `start`), it's full.

## 🤔 Labuladong's "Thinking Question"
> Why don't standard library dynamic arrays use circular array techniques if they can achieve $O(1)$ add/remove at the front?

- **Complexity Trade-off for `get(index)`/`set(index, value)`:**
    - Standard dynamic arrays (like Python `list` or Java `ArrayList`) provide $O(1)$ access to `array[i]`.
    - In a circular array, `get(logical_index_k)` requires mapping `k` to `physical_index = (head + k) % capacity`. This is still $O(1)$ but involves extra arithmetic.
    - More importantly, operations like `insert(logical_index_k, value)` or `remove(logical_index_k)` in the *middle* of a circular array would still require shifting elements, similar to a linear array, leading to $O(N)$ complexity for these middle operations.
- **Simplicity and Common Use Cases:** Standard dynamic arrays are optimized for $O(1)$ append/pop at the *end* and $O(1)$ random access. Adding/removing from the front is less common for general array usage and is typically $O(N)$. For frequent front operations, structures like `collections.deque` (Python) or `LinkedList`/`ArrayDeque` (Java) are preferred, and they often use circular arrays or linked lists internally.
- **Cache Performance:** Linear access patterns in standard arrays can sometimes offer better cache performance than the potentially scattered access due to wrap-around in circular arrays, though this is nuanced.
- **API Design:** Standard array/list APIs prioritize `get/set` by index and append/pop at one end. A circular array fundamentally changes the indexing scheme if it tries to maintain $O(1)$ at both ends for addition/removal.

In essence, circular arrays are a specialized optimization for deque-like behavior, not a universal replacement for standard dynamic arrays when arbitrary indexed access and middle insertions/deletions are primary concerns.

## 总结 (Summary)
- Circular array technique uses modulo arithmetic to treat a linear array as circular.
- It maintains `head` and `tail` (or `start`/`end`) pointers to manage the logical segment of stored elements.
- Enables $O(1)$ amortized time for adding/removing elements at both ends.
- Commonly used to implement Deques and fixed-size buffers (e.g., in producer-consumer scenarios).
- Resizing logic (doubling capacity when full, halving when sparsely filled) is similar to standard dynamic arrays to maintain amortized $O(1)$ for additions/removals.

---
Parent: [[Interview/Concept/Data Structures/Array/index|Arrays and Dynamic Arrays]]
Previous: [[Interview/Concept/Data Structures/Array/01 - Dynamic Array - Principles and Amortized Analysis|Dynamic Array - Principles]]
Next: [[Interview/Concept/Data Structures/Array/03 - Skip List - Principles|Skip List Principles]]
Related: [[Interview/Concept/Data Structures/Queue/index|Queues]], [[Interview/Concept/Data Structures/Deque/index|Deques]], [[Interview/Concept/Programming Languages/Python/03 - Python Deque for Interviews|Python Deque]]
