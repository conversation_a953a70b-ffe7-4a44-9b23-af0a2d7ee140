---
tags: [index, concept/data_structures, concept/array]
aliases: [Array Data Structure Index, Dynamic Array Index]
---

# Arrays and Dynamic Arrays

This section covers concepts related to static arrays, dynamic arrays (lists), and array-based techniques.

## Core Array Concepts:
- [[Interview/Concept/Data Structures/Array/00 - Array - Introduction (Static vs Dynamic)|Array - Introduction (Static vs Dynamic)]]
- [[Interview/Concept/Data Structures/Array/01 - Dynamic Array - Principles and Amortized Analysis|Dynamic Array - Principles and Amortized Analysis]]
  - Resizing strategy
  - Amortized $O(1)$ for appends

## Array-Based Techniques & Variations:
- [[Interview/Concept/Data Structures/Array/02 - Circular Array (Ring Buffer)|Circular Array (Ring Buffer)]]
  - $O(1)$ add/remove at both ends
- [[Interview/Concept/Data Structures/Array/03 - Skip List - Principles|Skip List - Principles]] (Uses linked lists with array-like levels for indexing)
  - $O(\log N)$ search, insert, delete on average

## Visualization
```mermaid
graph TD
    ArrConcept["Array Concepts"] --> IntroArr["[[Interview/Concept/Data Structures/Array/00 - Array - Introduction (Static vs Dynamic)|Introduction]]"]
    ArrConcept --> DynArr["[[Interview/Concept/Data Structures/Array/01 - Dynamic Array - Principles and Amortized Analysis|Dynamic Array]]"]
    DynArr --> Amortized["Amortized Analysis"]

    ArrConcept --> Techniques["Techniques & Variations"]
    Techniques --> CircArr["[[Interview/Concept/Data Structures/Array/02 - Circular Array (Ring Buffer)|Circular Array]]"]
    Techniques --> SkipL["[[Interview/Concept/Data Structures/Array/03 - Skip List - Principles|Skip List]]"]

    classDef main fill:#fef5e7,stroke:#d35400,stroke-width:2px;
    class ArrConcept main;
```

---
Parent: [[Interview/Concept/Data Structures/index|Data Structures Index]]
