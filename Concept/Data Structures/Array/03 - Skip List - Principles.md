---
tags: [concept/data_structures, concept/list, concept/skip_list, type/principles, concept/probabilistic_data_structure]
aliases: [Skip List Basics, 跳表原理]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/数组链表的种种变换/跳表核心原理.md]].
> This note introduces the core principles of Skip Lists as a way to optimize search in linked lists.

# Skip List: Core Principles

A Skip List is a probabilistic data structure that allows for efficient search, insertion, and deletion operations (average $O(\log N)$) in a sorted sequence of elements, similar to balanced trees (like AVL or Red-Black trees), but with a simpler implementation. It essentially augments a sorted [[Interview/Concept/Data Structures/Linked List/index|linked list]] with multiple layers of "express lanes" or "skip pointers."

## 🔗 The Problem with Simple Linked Lists

- A standard sorted linked list requires $O(N)$ time for search, as one might need to traverse up to $N$ nodes.
- Insertion and deletion, once the position is found, are $O(1)$, but finding the position is $O(N)$.

## 🏎️ Core Idea: Multi-Level Express Lanes

A skip list builds multiple levels of linked lists on top of the base sorted linked list.
- **Level 0:** The base sorted linked list containing all elements.
- **Level 1:** A sparser linked list containing a subset of elements from Level 0. Each node in Level 1 also exists in Level 0 and has a pointer to the next node in Level 1, effectively "skipping" some nodes in Level 0.
- **Level 2:** An even sparser list, skipping over more nodes from Level 1 (and thus even more from Level 0).
- ... and so on, up to a maximum level (often related to $\log N$).

**Visualization (Labuladong's Example Style):**
```
Original List: a -> b -> c -> d -> e -> f -> g -> h -> i -> j -> k (indices 0-10)

Skip List Structure:
Level 3 (Highest): 0 ----------------------- 8 <USER> <GROUP>   (Nodes: a, i, k)
Level 2:           0 ----------- 4 <USER> <GROUP> ------ 10   (Nodes: a, e, i, k)
Level 1:           0 ----- 2 ----- 4 ----- 6 ----- 8 ------ 10   (Nodes: a, c, e, g, i, k)
Level 0 (Base):    a--b--c--d--e--f--g--h--i--j--k   (All nodes)
(Numbers represent indices/keys for simplicity, nodes are a,b,c...)
```
In a proper skip list node, each node would have an array of `forward` pointers, one for each level it participates in.

**Search Operation (e.g., find element `h` at index 7):**
1.  Start at the `head` node of the highest level (Level 3).
2.  Traverse right on Level 3: `a` (idx 0). Next is `i` (idx 8). Since `7 < 8`, `h` must be between `a` and `i`.
3.  Drop down to Level 2 at node `a` (idx 0).
4.  Traverse right on Level 2: From `a`, next is `e` (idx 4). `7 > 4`. Move to `e`.
    From `e`, next is `i` (idx 8). `7 < 8`. `h` is between `e` and `i`.
5.  Drop down to Level 1 at node `e` (idx 4).
6.  Traverse right on Level 1: From `e`, next is `g` (idx 6). `7 > 6`. Move to `g`.
    From `g`, next is `i` (idx 8). `7 < 8`. `h` is between `g` and `i`.
7.  Drop down to Level 0 at node `g` (idx 6).
8.  Traverse right on Level 0: From `g`, next is `h` (idx 7). Found `h`.

The search path "skips" across levels, significantly reducing the number of nodes visited compared to a linear scan.

## 🎲 Probabilistic Level Assignment

When a new node is inserted, its level (how many "express lanes" it participates in) is determined probabilistically.
- A common method: Start at Level 0. Flip a coin. If heads, promote the node to Level 1 and flip again. Continue until tails or max level is reached.
- This ensures that, on average, Level $k$ has about half the nodes of Level $k-1$, leading to an $O(\log N)$ height.

## Node Structure (Conceptual)
```python
class SkipNode:
    def __init__(self, key, value, level):
        self.key = key
        self.value = value
        # Array of forward pointers, one for each level this node is on
        # self.forward[i] points to next node at level i
        self.forward = [None] * (level + 1) 
```

## Complexity
- **Search:** Average $O(\log N)$, Worst $O(N)$ (highly unlikely with proper randomization).
- **Insertion:** Average $O(\log N)$, Worst $O(N)$.
- **Deletion:** Average $O(\log N)$, Worst $O(N)$.
- **Space:** Average $O(N)$ (each element is in Level 0; probabilistically, few elements are in many higher levels, total pointers are about $2N$). Worst $O(N \log N)$ (if a node gets promoted to many levels, or max_level is high).

## 👍 Advantages
- Simpler to implement than balanced trees (e.g., Red-Black Trees, AVL Trees).
- Good average-case performance, comparable to balanced trees.
- Naturally supports concurrent modifications with some lock-free or fine-grained locking strategies.

## 👎 Disadvantages
- Performance is probabilistic, not strictly guaranteed $O(\log N)$ in the worst-case (though extremely rare).
- Can use more memory than some balanced trees due to storing multiple forward pointers per node.

## Labuladong's Key Points
- Skip lists use space-for-time trade-off by adding index layers.
- Search involves traversing from highest, sparsest level downwards.
- Insertion/deletion requires updating pointers across multiple levels.
- Suitable for implementing ordered maps/sets, similar to BSTs, but often simpler to code.

## 总结 (Summary)
- Skip List is a probabilistic data structure that uses multiple layers of linked lists to achieve $O(\log N)$ average time for search, insert, and delete.
- Nodes are promoted to higher "express" levels probabilistically.
- It offers performance comparable to balanced trees but is generally considered simpler to implement.
- A good example of trading space (for extra pointers) for time.

---
Parent: [[Interview/Concept/Data Structures/Array/index|Arrays and Dynamic Arrays]] (as a list variant, though often used like an ordered map) 
Alternatively, could be under [[Interview/Concept/Data Structures/Linked List/index|Linked Lists]] or its own top-level Skip List folder.
Previous: [[Interview/Concept/Data Structures/Array/02 - Circular Array (Ring Buffer)|Circular Array]]
Next: [[Interview/Concept/Data Structures/Queue/00 - Queue - Principles and Array_List Implementations|Queue Principles]] (Following Labuladong's basic data structures sequence)
