---
tags: [concept/data_structures, concept/graph, concept/union_find, concept/dsu, type/introduction, concept/connectivity]
aliases: [Union-Find Basics, Disjoint Set Union, DSU, 并查集原理]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/图结构基础及算法概览/Union Find 并查集原理.md]].
> Labuladong introduces Union-Find as a derivative of [[Interview/Concept/Data Structures/Tree/Binary Tree/00 - Binary Tree - Introduction and Types|tree structures]], optimized for dynamic connectivity problems in undirected graphs.

# Union-Find (Disjoint Set Union - DSU): Principles and Motivation

The Union-Find (or Disjoint Set Union/DSU) data structure is designed to efficiently manage a collection of disjoint sets. It's particularly powerful for solving dynamic connectivity problems in (undirected) graphs: determining if two nodes are in the same connected component, merging components, and counting components.

## ❓ The Dynamic Connectivity Problem

Given a set of $N$ elements (often representing nodes in a graph), we want to support two main operations:
1.  **`union(p, q)`:** Connect elements `p` and `q`. If they are already connected, do nothing. This effectively merges the sets (or connected components) containing `p` and `q`.
2.  **`connected(p, q)`:** Determine if `p` and `q` are in the same set (connected component).
Additionally, we might want:
3.  **`count()`:** Return the number of disjoint sets (connected components).

**Example Scenario (from Labuladong):**
Imagine 10 nodes (0-9) initially all disconnected. Each is its own connected component.
- `union(0, 1)`: Nodes 0 and 1 become connected. Components: {0,1}, {2}, ..., {9}. Count = 9.
- `union(1, 2)`: Nodes 0, 1, 2 become connected (due to transitivity: 0-1, 1-2 => 0-2). Components: {0,1,2}, {3}, ..., {9}. Count = 8.
- `connected(0, 2)`: Returns `true`.
- `connected(0, 5)`: Returns `false`.

**Connectivity Properties:**
- **Reflexive:** `p` is connected to `p`.
- **Symmetric:** If `p` is connected to `q`, then `q` is connected to `p`.
- **Transitive:** If `p` is connected to `q`, and `q` is connected to `r`, then `p` is connected to `r`.
These define an equivalence relation, and the disjoint sets are equivalence classes.

## Why Standard Graph Traversals Are Not Ideal for *Dynamic* Connectivity

If we model the problem using a standard [[Interview/Concept/Data Structures/Graph/01 - Graph - Representation (Adjacency List, Adjacency Matrix)|graph representation]] (e.g., adjacency list):
- **`union(p, q)`:** Add an edge between `p` and `q`. This is $O(1)$ for adjacency lists.
- **`connected(p, q)`:** Requires a [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|DFS]] or [[Interview/Concept/Algorithms/Graph Traversal/01 - Graph BFS Traversal|BFS]] starting from `p` to see if `q` is reachable. This is $O(V+E)$ time.
- **`count()`:** Requires traversing the entire graph to identify all connected components, also $O(V+E)$.

If there are many `connected` or `count` queries interspersed with `union` operations, the $O(V+E)$ cost per query becomes too slow. The Union-Find data structure aims to make `union` and `connected` operations nearly constant time (amortized $O(\alpha(N))$, where $\alpha$ is the inverse Ackermann function, which is extremely slow-growing and practically constant for all realistic $N$).

## 🌲 Union-Find as a Forest of Trees

The core idea of Union-Find is to represent each set (connected component) as a tree structure.
- Each element is a node in one of these trees.
- Each tree has a unique **root** node that serves as the representative (or identifier) of the set.
- To check if `p` and `q` are connected, we find the roots of the trees containing `p` and `q`. If the roots are the same, `p` and `q` are in the same set and thus connected.
- To union sets containing `p` and `q`, we find their respective roots. If the roots are different, we make one root a child of the other, effectively merging the two trees (and thus the two sets).

**Data Representation:**
A simple way to represent this forest of trees is using a **parent array**:
- `parent[i]` stores the parent of element `i`.
- If `parent[i] == i`, then `i` is a root node.

**Initial State:**
Initially, each element `i` is in its own set, so `parent[i] = i` for all `i`. The number of components `count` is $N$.

```tikz
\begin{tikzpicture}[
    tn/.style={circle, draw, font=\sffamily\small, minimum size=6mm, inner sep=1pt},
    arr/.style={->, >=stealth, thick}
]
% Initial state: 0, 1, 2, 3 are separate components (roots)
\node[tn] (n0) at (0,0) {0}; \node[below=0.1cm of n0, font=\tiny] {p[0]=0};
\node[tn] (n1) at (1.5,0) {1}; \node[below=0.1cm of n1, font=\tiny] {p[1]=1};
\node[tn] (n2) at (3,0) {2}; \node[below=0.1cm of n2, font=\tiny] {p[2]=2};
\node[tn] (n3) at (4.5,0) {3}; \node[below=0.1cm of n3, font=\tiny] {p[3]=3};
\node at (2.25, -1) {Initial: Each node is its own root. count = 4.};

% After union(0,1) -> parent[1]=0
\begin{scope}[yshift=-2.5cm]
    \node[tn] (u0) at (0,0) {0}; \node[below=0.1cm of u0, font=\tiny] {p[0]=0};
    \node[tn] (u1) at (0,-1) {1}; \node[below=0.1cm of u1, font=\tiny] {p[1]=0};
    \draw[arr] (u1) -- (u0); % 1 points to 0
    
    \node[tn] (u2) at (3,0) {2}; \node[below=0.1cm of u2, font=\tiny] {p[2]=2};
    \node[tn] (u3) at (4.5,0) {3}; \node[below=0.1cm of u3, font=\tiny] {p[3]=3};
    \node at (2.25, -2) {After union(0,1): {0,1}, {2}, {3}. count = 3.};
\end{scope}

% After union(2,3) -> parent[3]=2
\begin{scope}[yshift=-5.5cm]
    \node[tn] (v0) at (0,0) {0};
    \node[tn] (v1) at (0,-1) {1};
    \draw[arr] (v1) -- (v0);
    
    \node[tn] (v2) at (3,0) {2}; \node[below=0.1cm of v2, font=\tiny] {p[2]=2};
    \node[tn] (v3) at (3,-1) {3}; \node[below=0.1cm of v3, font=\tiny] {p[3]=2};
    \draw[arr] (v3) -- (v2); % 3 points to 2
    \node at (2.25, -2) {After union(2,3): {0,1}, {2,3}. count = 2.};
\end{scope}

% After union(0,2) -> e.g. parent[root_of_2_component (2)] = root_of_0_component (0) => parent[2]=0
\begin{scope}[yshift=-8.5cm]
    \node[tn] (w0) at (1.5,0) {0}; \node[below=0.1cm of w0, font=\tiny] {p[0]=0};
    \node[tn] (w1) at (0.75,-1) {1}; \node[below=0.1cm of w1, font=\tiny] {p[1]=0};
    \node[tn] (w2) at (2.25,-1) {2}; \node[below=0.1cm of w2, font=\tiny] {p[2]=0};
    \node[tn] (w3) at (2.25,-2) {3}; \node[below=0.1cm of w3, font=\tiny] {p[3]=2};
    \draw[arr] (w1) -- (w0);
    \draw[arr] (w2) -- (w0);
    \draw[arr] (w3) -- (w2);
    \node at (2.25, -3) {After union(0,2) (roots 0 and 2): {0,1,2,3}. count = 1.};
\end{scope}
\end{tikzpicture}
```

### Core Operations (Conceptual)

1.  **`find(p)`:** Returns the representative (root) of the set containing `p`.
    - Start at `p`. While `parent[p] != p`, move up: `p = parent[p]`.
    - The final `p` is the root.
2.  **`union(p, q)`:**
    - `rootP = find(p)`
    - `rootQ = find(q)`
    - If `rootP != rootQ` (they are in different sets):
        - Set `parent[rootQ] = rootP` (or `parent[rootP] = rootQ`). Make one root child of other.
        - Decrement `count`.
3.  **`connected(p, q)`:**
    - Return `find(p) == find(q)`.
4.  **`count()`:** Returns the current value of `count`.

## ⚠️ The Problem with Naive Union-Find

The `find` operation, as described above, can be slow if the trees become tall and skewed (like a linked list). In the worst case, `find` can take $O(N)$ time. If `union` calls `find` twice, `union` can also be $O(N)$.
Labuladong's visualization panel `div_uf-native-issue` demonstrates this: repeatedly unioning adjacent elements like `union(0,1), union(1,2), union(2,3), ...` can create a linked-list-like tree, degrading performance.

## Optimizations (Key to Efficiency)

Two main optimizations make Union-Find nearly constant time:
1.  **Path Compression (路径压缩):** During `find(p)`, after finding the root, make all nodes on the path from `p` to the root point directly to the root. This flattens the tree.
2.  **Union by Size/Rank (按大小/秩合并):** When unioning two trees, make the root of the smaller tree a child of the root of the larger tree. "Size" refers to the number of nodes in the tree. "Rank" is an upper bound on the height of the tree. This helps keep trees shorter.

These optimizations, when used together, lead to the amortized $O(\alpha(N))$ complexity. The details of these optimizations and their impact are usually covered when discussing the [[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/01 - Union-Find - Implementation with Optimizations|implementation of Union-Find]].

Labuladong's visualization panels typically show different versions:
- Unoptimized (shows the skewing problem).
- With Path Compression.
- With Union by Size/Rank.
- With both optimizations.

## 总结 (Summary)
- Union-Find (DSU) efficiently solves dynamic connectivity problems.
- It represents sets as a forest of trees, using a parent array.
- Core operations: `find` (get set representative/root), `union` (merge sets), `connected` (check if in same set).
- Naive implementation can lead to $O(N)$ `find` operations due to skewed trees.
- **Optimizations (Path Compression and Union by Size/Rank) are crucial** for achieving nearly constant time (amortized $O(\alpha(N))$) performance.
- The structure does not explicitly store the graph's edges, only connectivity information.

---
Parent: [[Interview/Concept/Data Structures/index|Data Structures Index]] (or [[Interview/Concept/Data Structures/Graph/index|Graph Concepts]] if seen as a graph algorithm tool)
Next: [[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/01 - Union-Find - Implementation with Optimizations|Union-Find - Implementation with Optimizations]]
Related: [[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|N-ary Trees]], [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal]] (for alternative connectivity checking)
