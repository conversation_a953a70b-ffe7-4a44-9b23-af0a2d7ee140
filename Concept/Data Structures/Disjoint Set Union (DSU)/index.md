---
tags: [index, concept/data_structures, concept/dsu, concept/union_find, concept/graph]
aliases: [DSU Index, Union-Find Index, Disjoint Set Union Index]
---

# Disjoint Set Union (DSU) / Union-Find

This section covers the Union-Find data structure, also known as Disjoint Set Union (DSU), used for managing disjoint sets and solving dynamic connectivity problems.

## Core Concepts & Implementation:
- [[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/00 - Union-Find - Principles and Motivation|Union-Find - Principles and Motivation]]
  - Dynamic Connectivity Problem
  - Representation as a Forest
  - Challenges with Naive Implementation
- `[[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/01 - Union-Find - Implementation with Optimizations|Union-Find - Implementation with Optimizations]]` (Placeholder)
  - Path Compression
  - Union by Size/Rank

## Applications:
- <PERSON><PERSON><PERSON>'s algorithm for Minimum Spanning Trees
- Detecting cycles in undirected graphs
- Network connectivity problems

## Visualization
```mermaid
graph TD
    DSUConcept["DSU / Union-Find"] --> Principles["[[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/00 - Union-Find - Principles and Motivation|Principles & Motivation]]"]
    DSUConcept --> Implementation["(Implementation with Optimizations)"]
    
    Principles --> ProbDef["Dynamic Connectivity"]
    Principles --> ForestRep["Forest Representation"]
    Principles --> NaiveIssues["Naive Implementation Issues"]
    
    Implementation --> PathComp["(Path Compression)"]
    Implementation --> UnionBySizeRank["(Union by Size/Rank)"]

    DSUConcept --> Apps["Applications"]
    Apps --> Kruskal["(Kruskal's MST)"]
    Apps --> CycleDetect["(Cycle Detection)"]
    

    classDef main fill:#fff0e6,stroke:#ff8c00,stroke-width:2px;
    class DSUConcept main;
```

---
Parent: [[Interview/Concept/Data Structures/index|Data Structures Index]]
