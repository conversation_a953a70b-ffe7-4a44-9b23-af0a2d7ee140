---
tags: [concept/data_structures, concept/hash_table, concept/hash_set, type/principles, type/implementation_detail]
aliases: [HashSet Basics, 哈希集合原理]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/哈希表的原理及实现/哈希集合的原理及代码实现.md]].
> Labuladong explains that a HashSet is essentially a wrapper around a HashMap where only keys matter.

# Hash Set: Principles and Implementation

A Hash Set is a data structure that stores a collection of unique elements, providing efficient average-time complexity for adding elements, removing elements, and checking for membership (i.e., if an element is present in the set).

## 🔗 Core Idea: A Wrapper Around Hash Map

The simplest and most common way to understand and implement a Hash Set is by using a [[Interview/Concept/Data Structures/Hash Map/00 - Hash Map - Core Principles|Hash Map]] as its underlying storage.
- **Keys Only:** A hash set is concerned only with the presence or absence of elements (which act as keys). The "values" associated with these keys in the underlying hash map are typically ignored or are a dummy constant value.
- **Uniqueness:** Since hash maps store unique keys, a hash set naturally stores unique elements.

**Relationship:**
`HashSet<Element>` can be implemented using `HashMap<Element, DummyValue>`
- `add(element)` in HashSet $\rightarrow$ `put(element, DUMMY_CONSTANT)` in HashMap.
- `contains(element)` in HashSet $\rightarrow$ `containsKey(element)` in HashMap.
- `remove(element)` in HashSet $\rightarrow$ `remove(element)` in HashMap.

The `DUMMY_CONSTANT` can be any fixed value, like `True`, `object()`, or a special placeholder, as its actual value is not used by the set logic.

## 🛠️ Operations and Complexity

Since a hash set is typically a thin wrapper around a hash map, its operations inherit the complexities of the underlying hash map:

- **`add(element)`:** Adds an element to the set. If the element is already present, the set remains unchanged.
    - Average Time Complexity: $O(1)$ (amortized, due to potential rehashing of the underlying map).
    - Worst-Case Time Complexity: $O(N)$ (if many hash collisions occur or rehashing is slow).
- **`remove(element)`:** Removes an element from the set. If the element is not present, typically does nothing or returns a status.
    - Average Time Complexity: $O(1)$.
    - Worst-Case Time Complexity: $O(N)$.
- **`contains(element)` / Membership Test:** Checks if an element is present in the set.
    - Average Time Complexity: $O(1)$.
    - Worst-Case Time Complexity: $O(N)$.
- **`size()`:** Returns the number of unique elements in the set.
    - Time Complexity: $O(1)$.

## Python `set`
Python's built-in `set` type is a highly optimized hash set implementation.
```python
# Example usage of Python's set
my_set = set()

my_set.add(10)
my_set.add(20)
my_set.add(10) # Adding duplicate, set remains {10, 20}

print(my_set)  # Output: {10, 20} or {20, 10} (order not guaranteed)
print(10 in my_set) # Output: True
print(30 in my_set) # Output: False

my_set.remove(20) # Removes 20
# my_set.remove(50) # Would raise KeyError if 50 not in set

my_set.discard(10) # Removes 10 if present, no error if not
my_set.discard(50) # No error

print(len(my_set)) # Output: 0 (if 10 and 20 were removed)
```

## Simplified Implementation Example (using a Python dict as the underlying HashMap)

This illustrates the wrapper concept.

```python
class MyHashSet:
    def __init__(self):
        # Use a dictionary as the underlying hash map.
        # The values in the dictionary don't matter for a set,
        # so we can use a dummy value like True.
        self._map = {} 
        self._dummy_value = True 

    def add(self, key: int): # Assuming integer elements for simplicity
        self._map[key] = self._dummy_value

    def remove(self, key: int):
        if key in self._map:
            del self._map[key]
            
    def contains(self, key: int) -> bool:
        return key in self._map

    def is_empty(self) -> bool:
        return len(self._map) == 0

    def size(self) -> int:
        return len(self._map)

# Example Usage:
# s = MyHashSet()
# s.add(1)
# s.add(2)
# print(s.contains(1))  # True
# print(s.contains(3))  # False
# s.add(2) # No change
# print(s.size()) # 2
# s.remove(1)
# print(s.contains(1)) # False
```

## 👍 Advantages of Hash Sets
- **Fast Operations:** Average $O(1)$ for add, remove, contains.
- **Uniqueness:** Automatically handles uniqueness of elements.

## 👎 Disadvantages / Considerations
- **Order:** Standard hash sets do not maintain any specific order of elements (though Python's `set` might show consistent order for small sets or specific CPython versions due to implementation details, it's not guaranteed across all inputs or Python versions/implementations for larger sets). If order is needed, an ordered set structure (like `TreeSet` or Python's `dict` keys view in 3.7+ combined with list conversion) would be required.
- **Memory:** Requires memory for the underlying hash map structure.
- **Hashable Elements:** Elements stored in a hash set must be hashable (immutable or have a consistent hash value).

## 🚀 Use Cases
- Checking for duplicates in a collection (e.g., [[Interview/Practice/LeetCode/LC217 - Contains Duplicate|LC217 - Contains Duplicate]]).
- Efficiently tracking visited states in graph traversals or search algorithms.
- Implementing set theory operations (union, intersection, difference) if the language's set type supports them directly.
- Any scenario requiring fast membership testing for a collection of unique items.

## 总结 (Summary)
- A Hash Set stores unique elements and provides fast `add`, `remove`, and `contains` operations (average $O(1)$).
- It's typically implemented as a wrapper around a Hash Map, where elements of the set are keys in the map, and map values are dummy/ignored.
- Python's built-in `set` is a robust and efficient hash set implementation.

---
Parent: [[Interview/Concept/Data Structures/Hash Table/index|Hash Tables (Hash Maps & Hash Sets)]]
Previous: [[Interview/Concept/Data Structures/Hash Map/04 - ArrayHashMap - Random Key Access|ArrayHashMap]] (or other HashMap variant)
Related: [[Interview/Concept/Data Structures/Hash Map/00 - Hash Map - Core Principles|Hash Map Principles]], [[Interview/Concept/Programming Languages/Python/06 - Python Set (Hash Set) for Interviews|Python Set]]
