---
tags: [index, concept/data_structures, concept/graph]
aliases: [Graph Data Structure Index, Graph Concepts]
---

# Graph Data Structures and Concepts

This section covers graph data structures, their representations, and fundamental concepts. Graph traversal algorithms are primarily in [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal Algorithms]].

## Core Concepts:
- [[Interview/Concept/Data Structures/Graph/00 - Graph - Introduction and Basic Terms|Graph - Introduction and Basic Terms]]
  - Directed vs. Undirected Graphs
  - Weighted vs. Unweighted Graphs
  - Degree, In-degree, Out-degree
  - Dense vs. Sparse Graphs
- [[Interview/Concept/Data Structures/Graph/01 - Graph - Representation (Adjacency List, Adjacency Matrix)|Graph Representation]]
  - Adjacency List
  - Adjacency Matrix

## Related Algorithms & Structures:
- [[Interview/Concept/Algorithms/Graph Traversal/index|Graph Traversal Algorithms (DFS, BFS)]]
- [[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/00 - Graph Shortest Path Algorithms - Overview|Shortest Path Algorithms Overview]]
- [[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/index|Union-Find (for connectivity)]]

## Visualization
```mermaid
graph TD
    GraphDS["Graph Data Structures"] --> IntroTerms["[[Interview/Concept/Data Structures/Graph/00 - Graph - Introduction and Basic Terms|Introduction & Terms]]"]
    GraphDS --> Representation["[[Interview/Concept/Data Structures/Graph/01 - Graph - Representation (Adjacency List, Adjacency Matrix)|Representation]]"]
    
    IntroTerms --> Terminology["(Directed, Weighted, Degree, etc.)"]
    Representation --> AdjList["Adjacency List"]
    Representation --> AdjMatrix["Adjacency Matrix"]

    GraphDS --> AlgoLink["Related Algorithms"]
    AlgoLink --> Traversal["[[Interview/Concept/Algorithms/Graph Traversal/index|Traversal (DFS/BFS)]]"]
    AlgoLink --> ShortestPath["[[Interview/Concept/Algorithms/Graph Traversal/Shortest Path/00 - Graph Shortest Path Algorithms - Overview|Shortest Paths]]"]
    AlgoLink --> DSU_Link["[[Interview/Concept/Data Structures/Disjoint Set Union (DSU)/index|Union-Find]]"]
    

    classDef main fill:#e6f0ff,stroke:#4d79ff,stroke-width:2px;
    class GraphDS main;
```

---
Parent: [[Interview/Concept/Data Structures/index|Data Structures Index]]
