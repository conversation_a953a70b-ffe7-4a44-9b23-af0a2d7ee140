---
tags: [concept/data_structures, concept/graph, type/implementation_detail, concept/adjacency_list, concept/adjacency_matrix]
aliases: [Graph Data Structures, Adjacency List, Adjacency Matrix, 图的表示]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/图结构基础及算法概览/图结构的通用代码实现.md]].
> This note discusses common ways to represent [[Interview/Concept/Data Structures/Graph/00 - Graph - Introduction and Basic Terms|Graph structures]] in code.

# Graph Representation: Adjacency List and Adjacency Matrix

To work with graphs algorithmically, we need a way to represent them in memory. The two most common methods are the adjacency list and the adjacency matrix. Labuladong's article "图结构的通用代码实现" (Common Code Implementation for Graph Structures) describes these.

## 1. Adjacency List

An adjacency list represents a graph as an array (or map) of lists. For each vertex `u`, `Adj[u]` stores a list of all vertices `v` such that there is an edge from `u` to `v`.

- **Structure:**
    - If there are $V$ vertices, `Adj` is an array of size $V$.
    - Each `Adj[u]` is a list containing `u`'s neighbors.
    - For weighted graphs, the list can store pairs `(neighbor_vertex, weight)`.

- **Example (Undirected, Unweighted Graph):**
    ```
    0 -- 1
    |  / |
    3 -- 2
    ```
    Adjacency List:
    `0: [1, 3]`
    `1: [0, 2, 3]`
    `2: [1, 3]`
    `3: [0, 1, 2]`

- **Python Implementation (Conceptual):**
    ```python
    class GraphAdjList:
        def __init__(self, num_vertices, is_directed=False):
            self.num_vertices = num_vertices
            self.is_directed = is_directed
            self.adj = [[] for _ in range(num_vertices)] # List of lists for neighbors

        def add_edge(self, u, v, weight=None): # weight is optional
            # For weighted, store tuples: (v, weight)
            entry_u = (v, weight) if weight is not None else v
            self.adj[u].append(entry_u)
            if not self.is_directed:
                entry_v = (u, weight) if weight is not None else u
                self.adj[v].append(entry_v)
        
        def get_neighbors(self, u):
            return self.adj[u] # Returns list of neighbors (or (neighbor,weight) tuples)

        def __str__(self):
            s = ""
            for i in range(self.num_vertices):
                s += f"{i}: {self.adj[i]}\n"
            return s
    ```

- **Space Complexity:** $O(V+E)$, where $V$ is the number of vertices and $E$ is the number of edges. (For undirected graph, each edge appears twice, $2E$).
- **Time Complexity for Operations:**
    - Check if edge `(u, v)` exists: $O(\text{degree}(u))$ in worst case (scan `Adj[u]`).
    - Iterate over neighbors of `u`: $O(\text{degree}(u))$.
    - Add edge: $O(1)$ (append to list).
- **Pros:** Space-efficient for sparse graphs (where $E \ll V^2$). Easy to iterate over neighbors.
- **Cons:** Checking for a specific edge can be slower than adjacency matrix for dense graphs.

## 2. Adjacency Matrix

An adjacency matrix represents a graph as a $V \times V$ matrix `Mat`, where $V$ is the number of vertices.
- **Structure:**
    - `Mat[i][j] = 1` (or `True`) if there is an edge from vertex `i` to vertex `j`.
    - `Mat[i][j] = 0` (or `False`) otherwise.
    - For weighted graphs, `Mat[i][j]` can store the weight of the edge. If no edge, it can store $\infty$ or a special value.
    - For undirected graphs, the matrix is symmetric (`Mat[i][j] = Mat[j][i]`).

- **Example (Same Undirected, Unweighted Graph):**
    ```
       0 1 2 3
    0: 0 1 0 1
    1: 1 0 1 1
    2: 0 1 0 1
    3: 1 1 1 0
    ```

- **Python Implementation (Conceptual):**
    ```python
    class GraphAdjMatrix:
        def __init__(self, num_vertices, is_directed=False, is_weighted=False):
            self.num_vertices = num_vertices
            self.is_directed = is_directed
            self.is_weighted = is_weighted
            # Initialize with 0 (unweighted) or infinity (weighted, if no edge)
            default_val = float('inf') if is_weighted else 0
            self.matrix = [[default_val] * num_vertices for _ in range(num_vertices)]
            if not is_weighted: # For unweighted, diagonal can be 0
                 for i in range(num_vertices): self.matrix[i][i] = 0


        def add_edge(self, u, v, weight=1): # Default weight 1 for unweighted
            val_to_set = weight if self.is_weighted else 1
            self.matrix[u][v] = val_to_set
            if not self.is_directed:
                self.matrix[v][u] = val_to_set
        
        def get_neighbors(self, u): # Less direct than adjacency list
            neighbors = []
            for v_idx in range(self.num_vertices):
                is_connected_unweighted = (not self.is_weighted and self.matrix[u][v_idx] == 1)
                is_connected_weighted = (self.is_weighted and self.matrix[u][v_idx] != float('inf') and u != v_idx)
                if is_connected_unweighted or is_connected_weighted:
                    if self.is_weighted:
                        neighbors.append((v_idx, self.matrix[u][v_idx]))
                    else:
                        neighbors.append(v_idx)
            return neighbors
            
        def has_edge(self, u, v):
            if self.is_weighted:
                return self.matrix[u][v] != float('inf') and u != v
            return self.matrix[u][v] == 1

        def __str__(self):
            s = ""
            for row in self.matrix:
                s += str(row) + "\n"
            return s
    ```

- **Space Complexity:** $O(V^2)$, regardless of the number of edges.
- **Time Complexity for Operations:**
    - Check if edge `(u, v)` exists: $O(1)$.
    - Iterate over neighbors of `u`: $O(V)$ (scan entire row).
    - Add edge: $O(1)$.
- **Pros:** Fast to check for a specific edge. Can be good for dense graphs. Some algorithms are easier to implement with matrices (e.g., Floyd-Warshall).
- **Cons:** Space-inefficient for sparse graphs. Iterating over neighbors is $O(V)$.

## Labuladong's Visualization
The visualization panel mentioned in "图结构的通用代码实现" would typically show:
- A logical view of the graph (nodes and edges).
- The corresponding adjacency list representation.
- The corresponding adjacency matrix representation.

This allows comparing how the same graph structure translates to different storage methods.

```tikz
\begin{tikzpicture}[font=\sffamily\small]
    % Logical Graph
    \node[circle, draw] (g0) at (0,2) {0};
    \node[circle, draw] (g1) at (2,2) {1};
    \node[circle, draw] (g2) at (0,0) {2};
    \node[circle, draw] (g3) at (2,0) {3};
    \draw (g0) -- (g1); \draw (g0) -- (g2); \draw (g1) -- (g2); \draw (g1) -- (g3); \draw (g2) -- (g3);
    \node at (1, 2.8) {Logical Graph};

    % Adjacency List
    \node at (-3,1.5) [align=left, draw, rounded corners, fill=blue!5, text width=2.5cm] {
        \textbf{Adjacency List:}\\
        0: [1, 2]\\
        1: [0, 2, 3]\\
        2: [0, 1, 3]\\
        3: [1, 2]
    };

    % Adjacency Matrix
    \node at (5,1) [align=center, draw, rounded corners, fill=green!5, text width=3cm] {
        \textbf{Adjacency Matrix:}\\
        \begin{tabular}{c|cccc}
          & 0 & 1 & 2 & 3 \\ \hline
        0 & 0 & 1 & 1 & 0 \\
        1 & 1 & 0 & 1 & 1 \\
        2 & 1 & 1 & 0 & 1 \\
        3 & 0 & 1 & 1 & 0 \\
        \end{tabular}
    };
\end{tikzpicture}
```

## Choosing a Representation
- For **sparse graphs** ($E$ is close to $V$), **adjacency lists** are generally preferred due to space efficiency and faster neighbor iteration.
- For **dense graphs** ($E$ is close to $V^2$), **adjacency matrices** can be viable, offering $O(1)$ edge existence checks.
- Most real-world graphs are sparse.
- Some algorithms may lend themselves better to one representation over the other.

## 总结 (Summary)
- **Adjacency List:** Array of lists. `Adj[u]` stores neighbors of `u`. Space $O(V+E)$. Good for sparse graphs.
- **Adjacency Matrix:** $V \times V$ matrix. `Mat[i][j]` indicates edge presence/weight. Space $O(V^2)$. Good for dense graphs, $O(1)$ edge check.
- The choice of representation depends on graph density, memory constraints, and the operations frequently performed.

---
Parent: [[Interview/Concept/Data Structures/Graph/00 - Graph - Introduction and Basic Terms|Graph Introduction]]
Next: [[Interview/Concept/Algorithms/Graph Traversal/00 - Graph DFS Traversal|Graph DFS Traversal]]
