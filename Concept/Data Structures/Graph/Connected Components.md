---
tags: [concept/graph, topic/connectivity, algorithm/dfs, algorithm/bfs, algorithm/union_find]
aliases: [Connected Components, Graph Components, Connectivity Analysis, Component Detection]
---

# Connected Components

## 🎯 Core Concept

**Connected Components** are maximal sets of vertices in a graph where every vertex is reachable from every other vertex within the same component. In an undirected graph, this means there's a path between any two vertices in the same component.

```tikz
\begin{tikzpicture}[
    vertex/.style={circle, draw, minimum size=0.8cm, font=\sffamily\small},
    comp1/.style={vertex, fill=blue!30},
    comp2/.style={vertex, fill=red!30},
    comp3/.style={vertex, fill=green!30},
    edge/.style={thick},
    component_boundary/.style={rectangle, draw, dashed, thick}
]

% Component 1
\node[comp1] (a) at (0, 2) {A};
\node[comp1] (b) at (1.5, 3) {B};
\node[comp1] (c) at (1.5, 1) {C};
\draw[edge] (a) -- (b);
\draw[edge] (a) -- (c);
\draw[edge] (b) -- (c);
\draw[component_boundary, blue] (-0.5, 0.5) rectangle (2, 3.5);

% Component 2
\node[comp2] (d) at (4, 2.5) {D};
\node[comp2] (e) at (5.5, 2.5) {E};
\draw[edge] (d) -- (e);
\draw[component_boundary, red] (3.5, 2) rectangle (6, 3);

% Component 3 (isolated)
\node[comp3] (f) at (8, 2) {F};
\draw[component_boundary, green] (7.5, 1.5) rectangle (8.5, 2.5);

% Labels
\node at (0.75, 0) {\small Component 1};
\node at (4.75, 1.5) {\small Component 2};
\node at (8, 1) {\small Component 3};

\end{tikzpicture}
```

## 🔍 Key Properties

### Formal Definition
- **Connected Component**: A maximal connected subgraph
- **Maximal**: Cannot add more vertices while maintaining connectivity
- **Connected**: Path exists between any two vertices in the component

### Important Characteristics
1. **Partition**: Components partition the vertex set (no overlap)
2. **Equivalence Relation**: "Reachability" is transitive
3. **Component Count**: Determines graph connectivity structure
4. **Bridge Edges**: Edges between components don't exist

## 💡 Detection Algorithms

### 1. Depth-First Search (DFS)

```tikz
\begin{tikzpicture}[
    step_box/.style={rectangle, draw, rounded corners, fill=blue!20, font=\sffamily\small, text width=4cm, align=left, minimum height=1.5cm},
    arrow/.style={->, thick, blue}
]

\node[step_box] (step1) at (0, 4) {
    \textbf{1. Initialize}\\
    • Mark all vertices unvisited\\
    • Component count = 0
};

\node[step_box] (step2) at (5, 4) {
    \textbf{2. For each unvisited vertex}\\
    • Start new DFS\\
    • Increment component count
};

\node[step_box] (step3) at (0, 2) {
    \textbf{3. DFS Traversal}\\
    • Mark current vertex visited\\
    • Recursively visit neighbors
};

\node[step_box] (step4) at (5, 2) {
    \textbf{4. Component Complete}\\
    • DFS finishes one component\\
    • Continue with next unvisited
};

\draw[arrow] (step1) -- (step2);
\draw[arrow] (step2) -- (step4);
\draw[arrow] (step4) -- (step3);
\draw[arrow] (step3) -- (step1);

\end{tikzpicture}
```

**Implementation:**
```python
def find_connected_components_dfs(graph):
    visited = set()
    components = []
    
    def dfs(vertex, component):
        if vertex in visited:
            return
        visited.add(vertex)
        component.append(vertex)
        
        for neighbor in graph[vertex]:
            dfs(neighbor, component)
    
    for vertex in graph:
        if vertex not in visited:
            component = []
            dfs(vertex, component)
            components.append(component)
    
    return components
```

### 2. Breadth-First Search (BFS)

```python
def find_connected_components_bfs(graph):
    visited = set()
    components = []
    
    for start_vertex in graph:
        if start_vertex in visited:
            continue
            
        # BFS for one component
        component = []
        queue = deque([start_vertex])
        visited.add(start_vertex)
        
        while queue:
            vertex = queue.popleft()
            component.append(vertex)
            
            for neighbor in graph[vertex]:
                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append(neighbor)
        
        components.append(component)
    
    return components
```

### 3. Union-Find ([[Disjoint Set Union]])

```python
class UnionFind:
    def __init__(self, n):
        self.parent = list(range(n))
        self.rank = [0] * n
        self.components = n
    
    def find(self, x):
        if self.parent[x] != x:
            self.parent[x] = self.find(self.parent[x])
        return self.parent[x]
    
    def union(self, x, y):
        px, py = self.find(x), self.find(y)
        if px == py:
            return False
        
        if self.rank[px] < self.rank[py]:
            px, py = py, px
        
        self.parent[py] = px
        if self.rank[px] == self.rank[py]:
            self.rank[px] += 1
        
        self.components -= 1
        return True

def find_connected_components_uf(n, edges):
    uf = UnionFind(n)
    for u, v in edges:
        uf.union(u, v)
    
    # Group vertices by component
    components = defaultdict(list)
    for i in range(n):
        root = uf.find(i)
        components[root].append(i)
    
    return list(components.values())
```

## 🌟 Applications

### 1. Network Analysis
- **Social Networks**: Friend groups, communities
- **Computer Networks**: Network partitions, isolated subnets
- **Transportation**: Connected road systems, flight routes

### 2. Problem Solving Patterns
- **[[LC2492 - Minimum Score of a Path Between Two Cities]]**: Find minimum edge in component
- **LC547. Number of Provinces**: Count connected components
- **LC200. Number of Islands**: 2D connected components
- **LC323. Number of Connected Components**: Basic component counting

### 3. Graph Properties
- **Connectivity Testing**: Is graph connected?
- **Bridge Detection**: Edges whose removal increases components
- **Articulation Points**: Vertices whose removal increases components

## 🧠 Advanced Concepts

### Strongly Connected Components (Directed Graphs)
For directed graphs, we need **strongly connected components** where every vertex can reach every other vertex following directed edges.

**Algorithms:**
- **Kosaraju's Algorithm**: Two DFS passes
- **Tarjan's Algorithm**: Single DFS with low-link values

### Dynamic Connectivity
Maintaining connected components as edges are added/removed:
- **Union-Find**: Efficient for edge additions
- **Link-Cut Trees**: Handle both additions and deletions
- **Dynamic Connectivity**: Advanced data structures

## 🎯 Complexity Analysis

| Algorithm | Time | Space | Best For |
|-----------|------|-------|----------|
| DFS | O(V + E) | O(V) | Simple implementation |
| BFS | O(V + E) | O(V) | Level-by-level exploration |
| Union-Find | O(E α(V)) | O(V) | Dynamic edge additions |

Where α is the inverse Ackermann function (practically constant).

## 🔗 Related Concepts

- **[[Graph Traversal]]**: DFS and BFS for exploration
- **[[Union-Find (Disjoint Set Union)]]**: Efficient connectivity queries
- **[[Minimum Spanning Tree]]**: Connects all components optimally
- **[[Graph Connectivity]]**: Broader connectivity concepts

Connected components are fundamental to understanding graph structure and solving many graph-related problems efficiently!
