---
tags: [concept/data_structures, concept/graph, type/introduction, concept/graph_terms]
aliases: [Graph Basics, Graph Terminology, 图论基础, 图结构术语]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/图结构基础及算法概览/图论中的基本术语.md]] and [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/图结构基础及算法概览/图结构的通用代码实现.md]].
> Labuladong presents Graph structures as an extension of [[Interview/Concept/Data Structures/Tree/N-ary Tree/00 - N-ary Tree - Introduction|N-ary Trees]], with fewer restrictions on connections.

# Graph: Introduction and Basic Terms

A graph is a fundamental data structure consisting of a set of **vertices** (or nodes) and a set of **edges** that connect pairs of vertices. Graphs are used to model relationships and networks in a wide variety of applications.

## 🌲 Core Components

1.  **Vertices (Nodes):** The fundamental units of a graph. Each vertex typically has a unique identifier.
2.  **Edges:** Connections between pairs of vertices. Edges can represent relationships, pathways, etc.

## Key Characteristics of Edges & Graphs

### 1. Directionality
-   **Directed Graph (Digraph):** Edges have a direction. An edge `(u, v)` means there's a connection from `u` to `v`, but not necessarily from `v` to `u`.
    ```tikz
    \begin{tikzpicture}[tn/.style={circle, draw, font=\sffamily\bfseries, minimum size=6mm, inner sep=1pt}, arr/.style={->, >=stealth, thick}]
    \node[tn] (v1) at (0,0) {1};
    \node[tn] (v2) at (2,1) {2};
    \node[tn] (v3) at (2,-1) {3};
    \draw[arr] (v1) -- (v2) node[midway, above, sloped, font=\tiny]{};
    \draw[arr] (v2) -- (v3) node[midway, right, font=\tiny]{};
    \draw[arr] (v3) -- (v1) node[midway, below, sloped, font=\tiny]{};
    \node at (1,-2) [text width=3cm, align=center, font=\tiny\sffamily]{Directed Graph Example};
    \end{tikzpicture}
    ```
    > Labuladong's example: `![](/algo/images/graph/unweighted-directed-graph.jpg)`

-   **Undirected Graph:** Edges have no direction. An edge `{u, v}` implies a symmetric connection; if `u` is connected to `v`, then `v` is connected to `u`.
    - Can be thought of as a directed graph where every edge `(u,v)` implies an edge `(v,u)`.
    ```tikz
    \begin{tikzpicture}[tn/.style={circle, draw, font=\sffamily\bfseries, minimum size=6mm, inner sep=1pt}, line/.style={thick}]
    \node[tn] (v1) at (0,0) {1};
    \node[tn] (v2) at (2,1) {2};
    \node[tn] (v3) at (2,-1) {3};
    \draw[line] (v1) -- (v2) node[midway, above, sloped, font=\tiny]{};
    \draw[line] (v2) -- (v3) node[midway, right, font=\tiny]{};
    \draw[line] (v3) -- (v1) node[midway, below, sloped, font=\tiny]{};
    \node at (1,-2) [text width=3cm, align=center, font=\tiny\sffamily]{Undirected Graph Example};
    \end{tikzpicture}
    ```
    > Labuladong's example: `![](/algo/images/graph/unweighted-undirected-graph.jpg)`

### 2. Edge Weights
-   **Weighted Graph:** Each edge has an associated numerical value, called a weight or cost. This can represent distance, time, capacity, etc.
    ```tikz
    \begin{tikzpicture}[tn/.style={circle, draw, font=\sffamily\bfseries, minimum size=6mm, inner sep=1pt}, arr/.style={->, >=stealth, thick}]
    \node[tn] (v1) at (0,0) {1};
    \node[tn] (v2) at (2,0) {2};
    \draw[arr] (v1) -- node[midway, above, font=\tiny]{5} (v2);
    \node at (1,-1) [text width=3cm, align=center, font=\tiny\sffamily]{Weighted Edge Example (1 to 2, cost 5)};
    \end{tikzpicture}
    ```
    > Labuladong shows weighted directed and undirected graphs.
    > `![](/algo/images/graph/weighted-directed-graph.jpg)`
    > `![](/algo/images/graph/weighted-undirected-graph.jpg)`

-   **Unweighted Graph:** Edges do not have weights, or all edges are considered to have a uniform weight (e.g., 1).

## 📐 Degree of a Vertex

-   **Undirected Graph:** The **degree** of a vertex is the number of edges incident to it.
    - Example: In Labuladong's `unweighted-undirected-graph.jpg`, node 1 has degree 2, node 4 has degree 4.
-   **Directed Graph:**
    - **In-degree:** The number of edges pointing *to* the vertex.
    - **Out-degree:** The number of edges originating *from* the vertex.
    - Example: In Labuladong's `unweighted-directed-graph.jpg`, node 3 has in-degree 2 and out-degree 1.

## 🔢 Relationship Between Edges and Vertices (Simple Graphs)

-   **Simple Graph:** A graph with no self-loops (edges from a vertex to itself) and no multiple edges between the same pair of vertices. Most algorithmic problems assume simple graphs unless specified.
    > Labuladong's `![](/algo/images/graph/simple-graph.jpg)` illustrates self-loops and multiple edges.
-   Let $V$ be the number of vertices and $E$ be the number of edges.
-   In a simple undirected graph, the maximum number of edges is $E_{max} = V(V-1)/2$, which is $O(V^2)$.
-   **Dense Graph:** $E$ is close to $O(V^2)$.
-   **Sparse Graph:** $E$ is much smaller than $O(V^2)$, often closer to $O(V)$.

## 💾 Representing Graphs

Common ways to represent graphs in code:

1.  **Adjacency List:**
    - For each vertex, store a list of its adjacent vertices (and weights, if applicable).
    - Space: $O(V+E)$.
    - Efficient for sparse graphs and for iterating over neighbors.
    - Example: `adj = {0: [(1,w1), (2,w2)], 1: [(3,w3)], ...}`

2.  **Adjacency Matrix:**
    - A $V \times V$ matrix where `matrix[i][j]` stores information about the edge from vertex `i` to vertex `j` (e.g., 1 if edge exists, 0 otherwise, or weight if weighted).
    - Space: $O(V^2)$.
    - Efficient for dense graphs and for checking if an edge `(i,j)` exists ($O(1)$).
    - Inefficient for iterating over neighbors if a vertex has few neighbors.

Labuladong's "图结构的通用代码实现" provides example implementations, which will be detailed in [[Interview/Concept/Data Structures/Graph/01 - Graph - Representation (Adjacency List, Adjacency Matrix)|Graph Representation]]. The visualization panel mentioned also shows these.

## 💡 Graph as an Extension of Trees
- Trees are special types of graphs (connected, acyclic).
- Graphs are more general: can have cycles, be disconnected, and edges can be directed or undirected more freely.
- [[Interview/Concept/Algorithms/Tree Traversal/index|Tree traversal algorithms (DFS, BFS)]] form the basis for graph traversal algorithms, with the main addition being the need to handle cycles (e.g., using a `visited` set).

## 总结 (Summary)
- Graphs consist of vertices and edges.
- Edges can be directed/undirected and weighted/unweighted.
- Vertex degree (in-degree/out-degree for digraphs) describes connectivity.
- Graphs can be dense or sparse. Simple graphs have no self-loops or multiple edges.
- Common representations include adjacency lists (good for sparse graphs) and adjacency matrices (good for dense graphs).
- Understanding these basic terms is crucial for studying graph algorithms.

---
Parent: [[Interview/Concept/Data Structures/index|Data Structures Index]]
Next: [[Interview/Concept/Data Structures/Graph/01 - Graph - Representation (Adjacency List, Adjacency Matrix)|Graph Representation]]
