---
tags: [concept/data_structures, concept/hash_table, concept/hash_map, concept/array, type/hybrid_structure, concept/randomized_algorithms]
aliases: [ArrayHashMap, Randomized HashMap, HashMap with Random Key]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/哈希表结构的种种变换/用数组加强哈希表（ArrayHashMap）.md]].
> This note explains how to augment a hash map with an array to support $O(1)$ random key retrieval.

# ArrayHashMap: $O(1)$ Random Key Access

A standard [[Interview/Concept/Data Structures/Hash Map/00 - Hash Map - Core Principles|Hash Map]] provides $O(1)$ average time for `put`, `get`, and `remove`. However, getting a *random key* from it typically requires converting keys to a list ($O(N)$) and then picking a random element. An `ArrayHashMap` (a term used by Labuladong, similar to an "Indexed HashMap" or "Randomized HashMap") is a composite data structure that allows fetching a random key in $O(1)$ time, while preserving $O(1)$ for other standard map operations.

## 🎯 The Challenge: $O(1)$ `randomKey()`

The goal is to add a `randomKey()` method to a hash map interface that returns a uniformly random key in $O(1)$ time.
- Directly picking from a hash map's internal table is problematic due to:
    - **Empty slots (nulls):** The underlying array of a hash map isn't densely packed.
    - **Collisions:** If using chaining, a slot might contain multiple keys. Simple random index selection won't be uniform across keys.
    - **Non-uniformity with probing:** If trying to resolve empty slots by probing, it biases selection.

## 🛠️ Core Idea: HashMap + Dynamic Array

The `ArrayHashMap` combines:
1.  **A standard Hash Map (`map`):** Stores `key -> index_in_array` mappings. This allows $O(1)$ lookup of a key's position in the auxiliary array.
2.  **A Dynamic Array (`keys_array`):** Stores all the keys currently in the map, in a dense (no gaps) manner. This allows $O(1)$ random access by index.

```mermaid
graph LR
    subgraph "ArrayHashMap Internals"
        DataArray["Dynamic Array (keys_array)\nStores actual keys densely\n[keyA, keyB, keyC, ...]"]
        IndexMap["Hash Map (map)\nStores key -> index_in_DataArray\n{ keyA: 0, keyB: 1, keyC: 2, ... }"]
        
        DataArray <-.-> IndexMap
    end
```

## 👣 How Operations Work

Let `keys_array` be the dynamic array and `map` be the hash map (`key -> index_in_keys_array`).

### `put(key, value)`
(Assuming `ArrayHashMap` also stores values; Labuladong's example focuses on keys for `randomKey`, but usually, a value is associated. For simplicity, let's imagine the value is stored alongside the key in `keys_array` or in a parallel `values_array`, or the primary `map` stores `key -> (value, index_in_keys_array)`).

1.  **Check if `key` exists in `map`:**
    - If `key` exists: Update its value. The `keys_array` and `map` (for indices) don't need to change.
    - If `key` does not exist:
        a. Add `key` to the end of `keys_array`. Let its new index be `new_idx = len(keys_array) - 1`.
        b. Store the mapping in the hash map: `map[key] = new_idx`.
        c. (Store value associated with the key).

### `get(key)`
1.  (Retrieve value using the primary hash map that might store `key -> value` directly, or use `map[key]` to find an index into a parallel `values_array`). This is standard $O(1)$ hash map get. The `keys_array` and `key -> index_in_keys_array` map are primarily for `remove` and `randomKey`.

### `remove(key)` (The Tricky Part for $O(1)$)
This is where the cleverness lies to maintain $O(1)$ complexity. Simple removal from an array is $O(N)$.
1.  **Check if `key` exists in `map`:** If not, do nothing.
2.  **Get `idx_to_remove = map[key]`**: This is the index of the `key` in `keys_array`.
3.  **Get `last_key = keys_array.back()`**: The last key in the dynamic array.
4.  **Move `last_key` to `idx_to_remove`**: `keys_array[idx_to_remove] = last_key`.
5.  **Update `map` for `last_key`**: `map[last_key] = idx_to_remove`.
6.  **Remove `key` from `map`**: `del map[key]`.
7.  **Remove last element from `keys_array`**: `keys_array.pop_back()`. (This is $O(1)$ for dynamic arrays).

**Why this works:** By swapping the element to be removed with the *last* element in the array and then popping the last element, we ensure the array remains dense and the removal from the array is $O(1)$. The hash map updates ensure consistency.

**Visualization of `remove("B")` from `keys_array = ["A", "B", "C", "D"]`, `map = {"A":0, "B":1, "C":2, "D":3}`:**
1. `key_to_remove = "B"`. `idx_to_remove = map["B"] = 1`.
2. `last_key = keys_array[3] = "D"`.
3. `keys_array[1] = "D"`. (`keys_array` becomes `["A", "D", "C", "D"]`).
4. `map["D"] = 1`. (`map` becomes `{"A":0, "B":1, "C":2, "D":1}`).
5. `del map["B"]`. (`map` becomes `{"A":0, "C":2, "D":1}`).
6. `keys_array.pop_back()`. Removes the original "D" at the end. (`keys_array` becomes `["A", "D", "C"]`).
Final state: `keys_array = ["A", "D", "C"]`, `map = {"A":0, "D":1, "C":2}`.

### `randomKey()`
1.  If `keys_array` is empty, return an error or `None`.
2.  Generate a random integer `rand_idx` from `0` to `len(keys_array) - 1`.
3.  Return `keys_array[rand_idx]`.
This is $O(1)$ because random access in an array is $O(1)$ and `len()` is $O(1)$.

## Example Implementation (Python - Conceptual for `randomKey` aspect)
This focuses on the key management part for `randomKey`. Values would be managed by an associated hash map.
```python
import random

class ArrayHashMapWithRandomKey:
    def __init__(self):
        self.keys_list = []  # Stores keys for O(1) random access
        self.key_to_index_map = {} # Maps key to its index in keys_list
        # self.value_map = {} # Actual key -> value storage

    def put(self, key, value):
        # self.value_map[key] = value # Store/update value
        if key not in self.key_to_index_map:
            self.keys_list.append(key)
            self.key_to_index_map[key] = len(self.keys_list) - 1
        # If key exists, its position in keys_list and key_to_index_map is unchanged

    def remove(self, key):
        if key not in self.key_to_index_map:
            return # Or raise error

        # del self.value_map[key] # Remove value

        # O(1) removal from keys_list:
        idx_to_remove = self.key_to_index_map[key]
        last_key = self.keys_list[-1]

        # Move last_key to the position of the key being removed
        self.keys_list[idx_to_remove] = last_key
        self.key_to_index_map[last_key] = idx_to_remove

        # Remove the key from map and list (now it's the last element)
        self.keys_list.pop()
        del self.key_to_index_map[key]
        
    def get_random_key(self):
        if not self.keys_list:
            return None # Or raise error
        return random.choice(self.keys_list) # random.choice is O(1) for lists

    # Other methods like get(key), containsKey(key) would use self.value_map
    # and self.key_to_index_map respectively.
```
This is similar to the structure required for LeetCode problem 380 "Insert Delete GetRandom O(1)".

## 总结 (Summary)
- An `ArrayHashMap` (or Indexed/Randomized HashMap) uses a hash map and a dynamic array in tandem.
- **Hash Map:** Maps `key -> index_in_array`.
- **Dynamic Array:** Stores keys densely, enabling $O(1)$ access to a random key by generating a random index.
- The `remove` operation is made $O(1)$ by swapping the element to delete with the last element of the array and then popping the last element.
- This structure adds $O(1)$ `randomKey` functionality while maintaining $O(1)$ average time for standard `put`, `get`, `remove`.

---
Parent: [[Interview/Concept/Data Structures/Hash Map/00 - Hash Map - Core Principles|Hash Map - Core Principles]]
Previous: [[Interview/Concept/Data Structures/Hash Map/03 - LinkedHashMap - Maintaining Insertion Order|LinkedHashMap]]
Related: [[Interview/Concept/Data Structures/Array/index|Dynamic Arrays]], [[Interview/Practice/LeetCode/LC380 - Insert Delete GetRandom O(1)|LC380 - Insert Delete GetRandom O(1)]] (problem utilizing this pattern)
