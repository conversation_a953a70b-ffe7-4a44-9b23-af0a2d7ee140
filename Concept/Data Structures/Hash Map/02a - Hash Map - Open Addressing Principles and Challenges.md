---
tags: [concept/data_structures, concept/hash_table, concept/hash_map, concept/collision_resolution, type/principles, concept/open_addressing]
aliases: [Open Addressing Basics, Linear Probing Challenges, 线性探查法难点]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/哈希表的原理及实现/线性探查法的两个难点.md]].
> This note focuses on the principles and challenges of Open Addressing, particularly Linear Probing.

# Hash Map: Open Addressing - Principles and Challenges

Open Addressing is a method for resolving hash collisions in [[Interview/Concept/Data Structures/Hash Map/00 - Hash Map - Core Principles|Hash Maps]]. Unlike [[Interview/Concept/Data Structures/Hash Map/01 - Hash Map - Chaining (Separate Chaining)|Chaining]], where colliding elements are stored in an external data structure (like a linked list) at the bucket, open addressing stores all entries directly within the hash table's main array itself. When a collision occurs (the target slot is already occupied), the algorithm probes for alternative empty slots in a systematic way.

## 🔍 Principle of Open Addressing

1.  **Hash and Initial Probe:** When inserting/searching for a key `k`, first compute its hash index `idx = hash(k) % table_capacity`. This is the initial slot to check.
2.  **Probing Sequence:** If `table[idx]` is occupied by a *different* key (for insertion) or doesn't match `k` (for search/deletion), a probing sequence is followed to find the next slot to inspect. Common probing strategies include:
    - **Linear Probing:** Try `idx+1`, `idx+2`, `idx+3`, ... (wrapping around the table if necessary).
    - **Quadratic Probing:** Try `idx+1^2`, `idx+2^2`, `idx+3^2`, ...
    - **Double Hashing:** Use a second hash function to determine the step size for probing.
3.  **Operation:**
    - **Insertion (`put`):** Probe until an empty slot is found. Insert the new key-value pair there. If a slot with the same key is found during probing, update its value.
    - **Retrieval (`get`):** Probe until the key is found or an empty slot is encountered. An empty slot signifies the key is not in the table (assuming no deletions, or deletions handled with special markers).
    - **Deletion (`remove`):** This is tricky. Simply marking a slot as empty can break probe sequences for other keys.

## ⚠️ Key Challenges with Open Addressing (especially Linear Probing)

Labuladong's article "线性探查法的两个难点" (Two Difficulties of Linear Probing) highlights specific challenges, particularly with deletion.

### Challenge 1: Clustering
- **Primary Clustering (Linear Probing):** Keys that hash to nearby slots tend to form long runs of occupied slots. This increases average probe lengths, degrading performance. If `k1` hashes to `i`, and `k2` hashes to `i`, `k2` goes to `i+1`. If `k3` hashes to `i` OR `i+1`, it probes further, extending the cluster.
- **Secondary Clustering (Quadratic Probing):** Keys hashing to the same initial slot follow the same probe sequence. Less severe than primary clustering.

### Challenge 2: Deletion ("The Empty Slot Problem")
- If we simply mark `table[idx]` as `null` (empty) upon deleting a key:
    - Subsequent `get` operations for a key `k'` (that originally collided with `table[idx]` and was placed further along the probe sequence) might incorrectly stop at the now-empty `table[idx]` and conclude `k'` is not present.

**Illustration (Problem with Simple Deletion):**
Assume `hash(k1)=i`, `hash(k2)=i`.
1. `put(k1, v1)`: `table[i] = (k1,v1)`
2. `put(k2, v2)`: `table[i]` is full, linear probe. `table[i+1] = (k2,v2)`
3. `remove(k1)`: If we set `table[i] = null`.
4. `get(k2)`: `hash(k2)=i`. `table[i]` is `null`. Algorithm incorrectly stops and returns "not found" for `k2`.

**Solutions to the Deletion Problem:**

1.  **Lazy Deletion (Tombstones):**
    - Instead of setting a slot to `null`, mark it with a special "deleted" or "tombstone" marker.
    - **`put`:** Treats tombstones as empty slots and can overwrite them.
    - **`get` / `remove`:** Must probe *past* tombstones, as the desired key might be further along.
    - **Drawback:** Tombstones fill up the table, reducing effective capacity and potentially requiring more frequent rehashing or periodic cleanup if not managed.

2.  **Re-probing / Shifting Elements (More Complex):**
    - When deleting an element at `table[idx]`, examine subsequent elements in its probe chain.
    - If an element `table[j]` (where `j > idx` in the probe chain) could have been placed at `table[idx]` (i.e., `hash(key_at_j) <= idx` after wrapping, or more precisely, its "ideal" slot is `idx` or earlier in the original chain starting from `idx`), then move `table[j]` to `table[idx]`.
    - Repeat this process to fill the "hole" by shifting subsequent elements that belong earlier in the probe chain. This is complex to implement correctly and efficiently.
    - Labuladong's article likely discusses variations of this "re-fill the hole" strategy.

**Simplified Pseudocode Logic (from "线性探查法的两个难点") for `put` and `get` in Linear Probing:**
(Assuming `table` has fixed size 10 and `hash(key) = key % 10`)

```python
# Conceptual from Labuladong's simplified scenario
# class KVNode: def __init__(self, key, value): self.key, self.value = key, value

# table = [None] * 10
# def hash_func(key): return key % 10

def put(key, value, table):
    index = hash_func(key)
    original_index = index
    
    while table[index] is not None and table[index].key != key:
        index = (index + 1) % len(table) # Linear probe with wrap-around
        if index == original_index: # Table is full
            raise Exception("HashMap is full") 
            
    table[index] = KVNode(key, value) # Insert or update

def get(key, table):
    index = hash_func(key)
    original_index = index
    
    while table[index] is not None:
        if table[index].key == key:
            # And not a tombstone if using lazy deletion
            return table[index].value 
        index = (index + 1) % len(table)
        if index == original_index: # Cycled through table
            return -1 # Not found
    return -1 # Found empty slot, key not present
```
The actual `remove` implementation is where the complexity (tombstones or re-probing) arises, which is the focus of Labuladong's "难点" (difficult points).

## 👍 Advantages of Open Addressing
1.  **No Pointers, Better Cache Performance:** All data is stored directly in the array, potentially leading to better cache locality compared to chasing pointers in chaining.
2.  **Less Memory Overhead (Potentially):** No need to store `next` pointers for linked lists.

## 👎 Disadvantages of Open Addressing
1.  **Clustering:** Can significantly degrade performance.
2.  **Deletion is Complex:** Requires careful handling (tombstones or re-probing).
3.  **Load Factor Must Be < 1:** The table can actually fill up. Performance degrades sharply as load factor approaches 1. Rehashing is critical.
4.  **Hash Function More Sensitive:** Performance is more sensitive to the quality of the hash function and probing strategy than chaining.

## 总结 (Summary)
- Open addressing stores all entries in the main hash table array, probing for empty slots on collision.
- Linear probing is simple but prone to primary clustering.
- **Deletion is a major challenge:** Simply emptying a slot can break probe chains. Solutions involve lazy deletion (tombstones) or more complex element shifting.
- While potentially offering better cache performance, open addressing requires careful design for probing, deletion, and rehashing to maintain good performance.

---
Parent: [[Interview/Concept/Data Structures/Hash Map/00 - Hash Map - Core Principles|Hash Map - Core Principles]]
Next: [[Interview/Concept/Data Structures/Hash Map/02 - Hash Map - Open Addressing (Linear Probing)|Hash Map - Open Addressing (Implementation Details)]]
Related: [[Interview/Concept/Data Structures/Array/index|Arrays]]
