---
tags: [concept/data_structures, concept/hash_table, concept/hash_map, type/principles, concept/hashing]
aliases: [HashMap Basics, Hash Table Fundamentals, 哈希表原理]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/哈希表的原理及实现/哈希表核心原理.md]].
> Labuladong emphasizes distinguishing between the Map interface (abstract key-value mapping) and specific implementations like HashMap.

# Hash Map: Core Principles

A Hash Map (often called HashMap or Hashtable) is a data structure that implements an associative array abstract data type, a structure that can map keys to values. It uses a hash function to compute an index into an array of buckets or slots, from which the desired value can be found.

## 📌 Basic Principle: Array Enhancement

At its core, a hash map can be thought of as an enhanced [[Interview/Concept/Data Structures/Array/index|array]].
- Arrays provide $O(1)$ access using integer indices.
- Hash maps aim for $O(1)$ access using keys of various types (strings, numbers, custom objects).

This is achieved by:
1.  **Hash Function:** A function that converts a key into an integer index. This index determines where the key-value pair should be stored in an underlying array (often called the `table`).
2.  **Underlying Array (`table`):** Stores the key-value pairs (or references to them) at the computed indices.

**Conceptual Pseudocode:**
```python
class MyHashMap:
    def __init__(self, capacity=1000): # Example capacity
        self.table = [None] * capacity
        self.capacity = capacity

    def _hash(self, key_obj) -> int:
        # Simplified hash function for illustration
        # In real implementations, key_obj.hashCode() or similar is used,
        # then mapped to table index.
        # For simplicity, let's assume key_obj is an integer for this conceptual hash
        if isinstance(key_obj, int):
            return key_obj % self.capacity 
        # A real hash function would handle various types and distribute well.
        # Python's built-in hash() for hashable types is complex and effective.
        # return hash(key_obj) % self.capacity 
        raise TypeError("Key must be int for this simplified example")


    def put(self, key, value): # O(1) on average
        index = self._hash(key)
        # This is simplified; collision handling is needed.
        self.table[index] = (key, value) 

    def get(self, key): # O(1) on average
        index = self._hash(key)
        # Simplified; needs collision handling and key comparison.
        if self.table[index] is not None and self.table[index][0] == key:
            return self.table[index][1]
        return None 

    def remove(self, key): # O(1) on average
        index = self._hash(key)
        # Simplified; needs collision handling.
        if self.table[index] is not None and self.table[index][0] == key:
            self.table[index] = None
```

## 🔑 Key Concepts and Considerations

### 1. Uniqueness of Keys
- In a hash map, **keys must be unique**. Each key maps to at most one value.
- Values, however, can be duplicated.
- This is analogous to array indices being unique.

### 2. Hash Function
The hash function is crucial. It must:
- **Be Deterministic:** For the same key, it must always produce the same hash value (and thus the same index).
- **Be Efficient:** Compute in $O(1)$ time. If the hash function is slow, the hash map operations will be slow.
- **Distribute Keys Uniformly:** Ideally, it should map different keys to different indices as much as possible to minimize collisions.

**Converting Keys to Integers:**
- Many objects have a built-in `hashCode()` method (Java) or can be processed by a `hash()` function (Python) that converts them into an integer. This integer might be positive or negative.

**Ensuring Non-Negative Indices and Bounds:**
- `hashCode` can be negative. A common way to make it non-negative is to mask off the sign bit (e.g., `h = h & 0x7fffffff` in Java for a 32-bit integer). Using `abs()` can be problematic if `h` is `Integer.MIN_VALUE`.
- The resulting integer must be mapped to a valid index within the `table` array. This is typically done using the modulo operator: `index = positive_hash % table.length`.
- Performance-critical implementations might use bitwise operations instead of modulo if `table.length` is a power of two (e.g., `index = positive_hash & (table.length - 1)`).

### 3. Hash Collisions
- **Definition:** A hash collision occurs when two different keys map to the same index in the `table`.
- **Inevitability:** Collisions are unavoidable because the number of possible keys is often much larger than the number of available array slots (Pigeonhole Principle).
- **Resolution Strategies:**
    - **Chaining (拉链法):** Each slot in the `table` points to a linked list (or other data structure like a balanced binary tree for many collisions) of key-value pairs that hash to that slot. See [[Interview/Concept/Data Structures/Hash Map/01 - Hash Map - Chaining (Separate Chaining)|Chaining]].
    - **Open Addressing (开放寻址法):** If a slot is occupied, probe for the next available slot in a systematic way (e.g., linear probing, quadratic probing, double hashing). See [[Interview/Concept/Data Structures/Hash Map/02 - Hash Map - Open Addressing (Linear Probing)|Open Addressing]].

```mermaid
graph TD
    subgraph "Hash Collision Resolution"
        direction LR
        Collision["Hash Collision (key1, key2 -> same index)"]
        Collision --> Chaining["Chaining (Linked List at index)"]
        Collision --> OpenAddressing["Open Addressing (Probe for next slot)"]
    end
```
Labuladong's diagram: `![](/algo/images/ds-basic/hash-collision.jpeg)` illustrates these two.

### 4. Load Factor and Rehashing (Resizing)
- **Load Factor ($\alpha$):** A measure of how full the hash map is. $\alpha = \frac{\text{number of stored elements (size)}}{\text{number of slots in table (capacity)}}$.
- **Impact of Load Factor:**
    - Higher load factor $\rightarrow$ increased probability of collisions $\rightarrow$ degraded performance (approaching $O(N)$ in worst-case for lookups if all keys collide).
- **Rehashing (Resizing):** When the load factor exceeds a certain threshold (e.g., 0.75 in Java's HashMap), the hash map is resized:
    1. A new, larger `table` array is created (e.g., double the size).
    2. All existing key-value pairs are re-inserted into the new table. This involves recalculating their hashes based on the new table size.
- Rehashing is an $O(N)$ operation (where $N$ is current number of elements), but it happens infrequently enough that the *amortized* cost of `put` operations remains $O(1)$.

### 5. Immutability of Keys
- **Keys used in a hash map should be immutable.** If a key's state changes after it's inserted such that its hash code also changes, the hash map might not be able to locate the key-value pair anymore.
- **Example:** If a list object is used as a key, and elements are added to/removed from the list after it's put into the map, its hash code (if based on content) will change. Subsequent `get(list_key)` calls might fail.
    ```python
    # Python example illustrating the problem with mutable keys
    # (Note: Python lists are unhashable by default and cannot be dict keys.
    # This is a conceptual illustration of why immutability is important.)
    # Consider a custom mutable object:
    class MutableKey:
        def __init__(self, data):
            self.data = data
        def __hash__(self): # Hash depends on mutable data
            return hash(tuple(self.data)) # Make it hashable for example
        def __eq__(self, other):
            return isinstance(other, MutableKey) and self.data == other.data

    # my_map = {}
    # key1 = MutableKey([1, 2])
    # my_map[key1] = "value1"
    # print(my_map.get(key1)) # "value1"
    
    # key1.data.append(3) # Mutate the key
    # print(hash(key1)) # Hash code changes
    # print(my_map.get(key1)) # Likely None, as hash is different. Original entry is lost.
    ```
- Standard immutable types like strings, numbers, and tuples (of immutable types) are safe as keys.
- For custom objects, if used as keys, ensure their `hashCode()` (or `__hash__` in Python) and `equals()` (or `__eq__`) methods are based on immutable fields.

### 6. Iteration Order
- **Generally Unordered:** Standard hash maps (like Java's `HashMap` before Java 8's TreeMap tie-breaking for collisions, or Python's `dict` before 3.7) do not guarantee any specific iteration order for keys. The order can change after rehashing.
- **Ordered Variants:**
    - `LinkedHashMap` (Java) or Python `dict` (3.7+): Maintain insertion order. Achieved by [[Interview/Concept/Data Structures/Hash Map/03 - LinkedHashMap - Maintaining Insertion Order|augmenting the hash map with a doubly linked list]].
    - `TreeMap` (Java) or sorted dictionary structures: Maintain keys in sorted order (natural or custom comparator). Implemented using balanced binary search trees (e.g., Red-Black trees).

### 7. Why Not to Modify During Iteration
- Modifying a hash map (adding/removing elements) while iterating over it can lead to undefined behavior or runtime errors (e.g., `ConcurrentModificationException` in Java).
- This is because modifications might trigger rehashing, changing the underlying structure the iterator is working with.
- If modification during iteration is needed, use iterators that support it (e.g., `iterator.remove()`) or collect elements to modify/remove first and then perform operations after iteration.

## 💡 Interview Questions Summary (from Labuladong)

1.  **Why $O(1)$ for basic operations?**
    - Relies on $O(1)$ hash function and effective collision resolution. Amortized $O(1)$ due to occasional rehashing.

2.  **Why does iteration order change?**
    - Rehashing changes table size, which changes `hash(key) % new_size`, thus relocating elements.

3.  **Is it always $O(1)$?**
    - No. Worst-case (many collisions, or slow hash function for key type like `ArrayList`) can be $O(N)$.

4.  **Why immutable keys?**
    - If a key's hash code changes after insertion due to mutation, the map can't find it, leading to lost data and bugs.

## 总结 (Summary)
- Hash maps provide average $O(1)$ time for `get`, `put`, `remove` by using a hash function to map keys to array indices.
- Key components: hash function, underlying array (`table`), collision resolution strategy.
- Critical considerations: hash function quality, collision handling (chaining/open addressing), load factor, rehashing, and key immutability.
- Standard hash maps are generally unordered; specialized versions (LinkedHashMap, TreeMap) provide ordering.

---
Parent: [[Interview/Concept/Data Structures/Hash Table/index|Hash Tables (Hash Maps & Hash Sets)]]
Next: [[Interview/Concept/Data Structures/Hash Map/01 - Hash Map - Chaining (Separate Chaining)|Hash Map - Chaining]]
