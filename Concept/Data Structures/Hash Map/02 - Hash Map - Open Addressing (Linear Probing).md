---
tags: [concept/data_structures, concept/hash_table, concept/hash_map, concept/collision_resolution, type/implementation, concept/open_addressing, concept/linear_probing]
aliases: [Linear Probing Implementation, Open Addressing HashMap, 线性探查法实现]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/哈希表的原理及实现/线性探查法的两种代码实现.md]].
> This note details implementations for linear probing, addressing challenges discussed in [[Interview/Concept/Data Structures/Hash Map/02a - Hash Map - Open Addressing Principles and Challenges|Open Addressing Principles and Challenges]].

# Hash Map: Open Addressing with Linear Probing (Implementation)

This note provides Python implementations for a Hash Map using open addressing with linear probing, demonstrating two approaches to handle deletions: lazy deletion (tombstones) and element shifting (re-probing). These build upon the [[Interview/Concept/Data Structures/Hash Map/02a - Hash Map - Open Addressing Principles and Challenges|principles and challenges of open addressing]].

## 🛠️ Simplified <PERSON><PERSON><PERSON> (from Labuladong)
For clarity, we'll use the simplified setup:
- Keys and values are `int`.
- `hash(key) = key % capacity`.
- Fixed `capacity` (no dynamic resizing in these simplified examples).
- `-1` returned by `get` if key not found.

```python
# Helper class for key-value entries
class KVNode:
    def __init__(self, key, value):
        self.key = key
        self.value = value
        # In a real scenario with tombstones, we might add a flag here
        # or use a special TOMBSTONE object.

# Special object to mark deleted slots for lazy deletion
TOMBSTONE = object()
```

## Approach 1: Lazy Deletion (Tombstones)

In this approach, when an element is removed, its slot is marked with a `TOMBSTONE` object instead of being set to `None`.

```python
class LinearProbingHashMap_LazyDeletion:
    def __init__(self, capacity=10):
        self.table = [None] * capacity
        self.capacity = capacity
        self.size = 0 # Number of actual key-value pairs, not tombstones

    def _hash(self, key: int) -> int:
        return key % self.capacity

    def put(self, key: int, value: int):
        if self.size >= self.capacity / 2: # Example: Rehash if half full (ignoring tombstones for simplicity)
            # In a full implementation, rehashing would occur here.
            # Rehashing should also clear tombstones.
            # For this simplified example, we assume no rehashing to focus on probing.
            pass # print("Consider rehashing, table getting full of items/tombstones.")


        start_index = self._hash(key)
        current_index = start_index
        first_tombstone_index = -1

        while self.table[current_index] is not None:
            # If we find the key, update its value
            if self.table[current_index] is not TOMBSTONE and self.table[current_index].key == key:
                self.table[current_index].value = value
                return

            # Remember the first tombstone encountered for potential insertion
            if self.table[current_index] is TOMBSTONE and first_tombstone_index == -1:
                first_tombstone_index = current_index
            
            current_index = (current_index + 1) % self.capacity
            if current_index == start_index: # Cycled through table
                # This state (full table with no matching key or reusable tombstone)
                # should ideally be handled by rehashing before it's reached.
                # If no tombstones were found for reuse, and table is full of active items.
                if first_tombstone_index == -1:
                     raise Exception("HashMap is full and no tombstones to reuse.")
                # Otherwise, we will use the first_tombstone_index found
                break 
        
        # Insert at first available tombstone or empty slot
        insert_idx = first_tombstone_index if first_tombstone_index != -1 else current_index
        
        if self.table[insert_idx] is None or self.table[insert_idx] is TOMBSTONE:
            if self.table[insert_idx] is None: # Completely new slot
                 pass # No specific action if it was None
            if self.table[insert_idx] is TOMBSTONE: # Reusing a tombstone
                 pass # No specific action for size if reusing tombstone, already accounted for deletion
            
            self.table[insert_idx] = KVNode(key, value)
            self.size += 1 # Increment size only for new key, not for update or tombstone reuse (if not previously counted)
                          # Size tracking with tombstones can be tricky; simpler is count non-None, non-TOMBSTONE
                          # For this example, size increases on new KVNode placement where slot was None/TOMBSTONE
        else:
            # This case should ideally be prevented by rehashing or proper full check
            # If we are here, means table is full of active elements and no key match or tombstone found.
             raise Exception("HashMap is full (put).")


    def get(self, key: int) -> int:
        start_index = self._hash(key)
        current_index = start_index

        while self.table[current_index] is not None:
            # If it's not a tombstone and keys match
            if self.table[current_index] is not TOMBSTONE and self.table[current_index].key == key:
                return self.table[current_index].value
            
            current_index = (current_index + 1) % self.capacity
            if current_index == start_index: # Cycled through table
                return -1 # Key not found
        
        return -1 # Found a None slot, key definitely not present

    def remove(self, key: int):
        start_index = self._hash(key)
        current_index = start_index

        while self.table[current_index] is not None:
            if self.table[current_index] is not TOMBSTONE and self.table[current_index].key == key:
                self.table[current_index] = TOMBSTONE # Mark as deleted
                self.size -= 1
                return
            
            current_index = (current_index + 1) % self.capacity
            if current_index == start_index: # Cycled through table
                return # Key not found
        # Key not found
```
**Explanation (Lazy Deletion):**
- **`put`:** Probes for an empty (`None`) slot or an existing slot with the same key. It can reuse a `TOMBSTONE` slot for insertion.
- **`get`:** Probes for the key. It skips over `TOMBSTONE` slots because the actual key might be further down the probe sequence. The search stops only if `None` (a truly empty slot) is found or the table is fully scanned.
- **`remove`:** Finds the key and replaces its slot with `TOMBSTONE`. `size` is decremented.
- **Challenges:** The table can fill up with tombstones, degrading performance for `get` (longer probes) and `put` (fewer truly empty slots). Rehashing should ideally clear out tombstones by reinserting only active elements.

## Approach 2: Deletion with Element Shifting (Re-probing on Deletion)

This method avoids tombstones. When an element `table[idx]` is deleted, the algorithm checks subsequent elements in the probe chain. If any of those elements `table[j]` would have hashed to `idx` or an earlier position in the *current* cluster (had `table[idx]` been empty initially), they are shifted back to fill the gap. This is complex.

Labuladong's article likely describes the logic for this shifting. A simplified interpretation: after setting `table[idx] = None`, you re-insert all elements `table[j]` in the same cluster that appear after `idx`.

```python
class LinearProbingHashMap_ShiftingDeletion:
    def __init__(self, capacity=10):
        self.table = [None] * capacity
        self.capacity = capacity
        self.size = 0

    def _hash(self, key: int) -> int:
        return key % self.capacity

    def _find_slot(self, key: int, for_insertion=False):
        # Helper to find slot for key or first empty/target slot for insertion
        start_index = self._hash(key)
        current_index = start_index
        
        while True:
            slot_content = self.table[current_index]
            if slot_content is None: # Empty slot
                return current_index, False # Returns index, and 'found_key' = False
            if slot_content.key == key: # Key found
                return current_index, True # Returns index, and 'found_key' = True
            
            # If for_insertion=True, we could optimize to return first empty slot
            # but for get/remove, we must continue until None or full cycle.

            current_index = (current_index + 1) % self.capacity
            if current_index == start_index: # Cycled through
                return current_index, False # Table full or key not found after full scan
                                           # For insertion, this means table is full.

    def put(self, key: int, value: int):
        # Simplified: No explicit rehashing check for this example to focus on probing.
        # In a real system, check load factor: if (self.size + 1) / self.capacity > THRESHOLD: rehash()

        index, found = self._find_slot_for_put(key) # A specialized find for put
        
        if found or self.table[index] is None: # Key found (update) or empty slot (insert)
            if self.table[index] is None: # New key
                self.size +=1
            self.table[index] = KVNode(key, value)
        else: # Table is full
            raise Exception("HashMap is full (put).")

    def _find_slot_for_put(self, key: int):
        # Finds slot for key. If key not present, finds first empty slot.
        # Returns (index, key_already_exists_bool)
        start_index = self._hash(key)
        current_index = start_index
        
        while self.table[current_index] is not None:
            if self.table[current_index].key == key:
                return current_index, True # Key exists at this index
            current_index = (current_index + 1) % self.capacity
            if current_index == start_index: # Cycled, table is full of non-matching keys
                raise Exception("HashMap is full, cannot put.")
        return current_index, False # Found an empty slot

    def get(self, key: int) -> int:
        index, found = self._find_slot(key)
        if found:
            return self.table[index].value
        return -1

    def remove(self, key: int):
        index_to_delete, found = self._find_slot(key)
        if not found:
            return # Key not in map

        self.table[index_to_delete] = None # Mark as empty
        self.size -= 1

        # Re-probe and shift elements to fill the gap
        current_index = (index_to_delete + 1) % self.capacity
        while self.table[current_index] is not None:
            node_to_rehash = self.table[current_index]
            self.table[current_index] = None # Temporarily remove it for re-putting
            self.size -=1 # Adjust size as put will increment it
            
            # Re-insert this node. This will find its correct new spot.
            # This is a simplified re-put. More optimized shifting is possible
            # by checking if the node's ideal spot was before or at index_to_delete.
            self.put(node_to_rehash.key, node_to_rehash.value)
            
            current_index = (current_index + 1) % self.capacity
            # Stop if we cycle back to the original deletion point or an empty slot that breaks the cluster
            if current_index == index_to_delete or self.table[current_index] is None and self._hash(node_to_rehash.key if self.table[current_index] is None else self.table[current_index].key) == current_index :
                 # if next slot is empty AND it's its "natural" hash position, then cluster ends.
                 # This condition is a bit tricky to get right for stopping re-probing.
                 # A simpler stop is if current_index == index_to_delete (cycled fully) OR
                 # table[current_index] is None (end of cluster).
                 # However, the original condition from many textbooks is to rehash items in the cluster
                 # whose *original* hash position is at or before the deleted slot.
                 # For simplicity here, we re-put everything in the current contiguous block.
                 pass # The loop condition table[current_index] is not None handles stopping.
```
**Explanation (Element Shifting):**
- **`_find_slot`:** A helper to locate a key or determine where it would be.
- **`put`:** Finds the slot. If key exists, updates. If empty slot found, inserts. If table full, error (needs rehashing).
- **`get`:** Uses `_find_slot`. If found, returns value, else -1.
- **`remove`:**
    1. Finds the slot of the key to delete. If not found, returns.
    2. Sets `table[index_to_delete] = None`. Decrements size.
    3. **Rehashing Cluster:** Iterates through the "cluster" of elements immediately following the now-empty slot. For each element in this cluster, it's temporarily removed and then re-inserted using `put`. `put` will find its correct new position, potentially filling the `index_to_delete` slot or another earlier slot if appropriate.
- **Challenges:** Correctly and efficiently identifying which elements *must* be shifted is complex. The simple "re-put everything in the cluster" is easier to implement but might do more work than strictly necessary. A more optimized shift would only move an element `X` from `slot_j` to `slot_i` (where `i` is the new hole) if `X`'s ideal hash position is `<= i` (considering wrap-around).

## Visualizations (from Labuladong's Panels)
Labuladong's article typically includes interactive visualizations for these processes.
- **Lazy Deletion (`TOMBSTONE`):** The panel would show slots changing state from `KVNode` to `TOMBSTONE` on remove, and `TOMBSTONE` being overwritten on `put`.
- **Shifting Deletion:** The panel would show elements moving backward to fill the gap created by a `remove`.

## Comparison of Deletion Strategies

| Feature             | Lazy Deletion (Tombstones)          | Element Shifting / Re-probing        |
|---------------------|-------------------------------------|--------------------------------------|
| **Complexity (`remove`)** | $O(1)$ to mark after find ($O(L_{probe})$) | Potentially $O(L_{cluster})$ or worse if cluster is re-inserted |
| **Complexity (`get`/`put`)** | Can degrade if many tombstones (longer probes) | Generally better if shifting keeps clusters compact |
| **Space**           | Tombstones occupy space             | No tombstones, better space utilization |
| **Implementation**  | Simpler                             | More complex to implement correctly  |
| **Rehashing**       | Can clear tombstones                | Also benefits from rehashing         |

## 总结 (Summary)
- Linear probing is an open addressing strategy that probes `idx, idx+1, idx+2, ...` on collision.
- **Lazy Deletion (Tombstones):** Marks deleted slots. `get` probes past tombstones. Simpler but can lead to performance degradation from too many tombstones.
- **Element Shifting:** Removes an element and then re-hashes/shifts subsequent elements in the probe cluster to fill the gap. More complex but avoids tombstones.
- Both methods require careful handling of probe sequences and termination conditions, especially with table wrap-around.
- Efficient rehashing is crucial for open addressing to maintain good performance as the load factor increases or tombstones accumulate.

---
Parent: [[Interview/Concept/Data Structures/Hash Map/02a - Hash Map - Open Addressing Principles and Challenges|Open Addressing Principles and Challenges]]
Next: [[Interview/Concept/Data Structures/Hash Map/03 - LinkedHashMap - Maintaining Insertion Order|LinkedHashMap]]
Related: [[Interview/Concept/Data Structures/Array/index|Arrays]]
