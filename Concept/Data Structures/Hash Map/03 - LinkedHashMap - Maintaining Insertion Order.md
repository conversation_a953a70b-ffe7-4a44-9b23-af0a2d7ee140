---
tags: [concept/data_structures, concept/hash_table, concept/hash_map, concept/linked_list, type/hybrid_structure, concept/ordered_map]
aliases: [LinkedHashMap, Ordered HashMap, Insertion Order Map]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/哈希表结构的种种变换/用链表加强哈希表（LinkedHashMap）.md]].
> This note explains how combining a hash map with a doubly linked list allows for maintaining insertion order.

# LinkedHashMap: Maintaining Insertion Order

A standard [[Interview/Concept/Data Structures/Hash Map/00 - Hash Map - Core Principles|Hash Map]] does not guarantee any specific order for its keys when iterated. However, a common requirement is to retrieve keys in the order they were inserted. The `LinkedHashMap` data structure achieves this by augmenting a standard hash map with a [[Interview/Concept/Data Structures/Linked List/index|doubly linked list]].

## 🔗 Core Idea: HashMap + Doubly Linked List

A `LinkedHashMap` internally uses two data structures:
1.  **A standard Hash Map:** This provides the fast $O(1)$ average time complexity for `put`, `get`, and `remove` operations based on the key's hash. It stores `key -> Node` mappings, where `Node` is an entry in the doubly linked list.
2.  **A Doubly Linked List:** This list runs through all the entries in the map, maintaining them in the desired order (e.g., insertion order or access order for LRU caches). Each node in this list typically stores the `key`, `value`, and pointers `prev`/`next` to connect it within the linked list.

```mermaid
graph LR
    subgraph "LinkedHashMap Internals"
        HM["Hash Map\n(key -> DoublyLinkedListNode)"]
        DLL["Doubly Linked List\n(maintains order)"]
        
        HM -.-> DLL
        DLL -.-> HM
        
        DLL_Node["Node in DLL:\n- key\n- value\n- prev_ptr\n- next_ptr\n(also stored or referenced by HashMap)"]
    end
```

## 👣 How Operations Work (Insertion Order)

### `put(key, value)`
1.  **Hash Map Check:**
    - If `key` already exists in the hash map:
        - Update the value in the corresponding node (which is part of the linked list).
        - If access order is being maintained (like in an LRU cache), move this node to the end of the linked list. For simple insertion order, its position in the list doesn't change on update.
    - If `key` does not exist in the hash map:
        - Create a new node containing `(key, value)`.
        - Add this new node to the **end** of the doubly linked list.
        - Store the `key` and a reference to this new linked list node in the hash map: `hashMap.put(key, newNode)`.

### `get(key)`
1.  Use the hash map to find the node associated with `key` in $O(1)$ average time: `node = hashMap.get(key)`.
2.  If found, return `node.value`.
3.  (If maintaining access order for LRU: move `node` to the end of the linked list.)

### `remove(key)`
1.  Use the hash map to find the node associated with `key`: `node = hashMap.get(key)`.
2.  If found:
    - Remove `node` from the doubly linked list (update `prev` and `next` pointers of its neighbors). This is $O(1)$ if you have direct access to the node.
    - Remove `key` from the hash map. This is $O(1)$ average.

### Iteration (e.g., `keys()`, `values()`, `items()`)
- Iterate through the doubly linked list from head to tail. This will yield elements in their insertion order.

## ✨ Advantages
- **Combines Fast Lookup with Order:** Provides $O(1)$ average time for `get`, `put`, `remove` (from the hash map part) while also allowing iteration in a predictable order (from the linked list part).
- **LRU Cache Implementation:** `LinkedHashMap` is a common choice for implementing [[Interview/Concept/Algorithms/Caching/LRU Cache|LRU (Least Recently Used) Caches]]. In this mode, upon accessing (`get`) or inserting (`put` for a new key) an element, that element's node is moved to the "most recently used" end of the linked list. When the cache is full and a new element needs to be added, the element at the "least recently used" end (other end of the list) is evicted.

## Python's `dict` (Since 3.7)
As Labuladong mentions, Python's built-in `dict` objects, starting from version 3.7 (and as an implementation detail in CPython 3.6), maintain insertion order. This behavior is similar to a `LinkedHashMap`. Internally, modern Python dictionaries achieve this through optimizations in their hash table implementation, often involving a compact array to store insertion order alongside the main hash table structure.

## Golang's `map` Iteration
Labuladong also points out that Golang's `map` intentionally randomizes iteration order. This is a language design choice to prevent developers from relying on any specific order, as the internal order of a standard hash map can change (e.g., due to rehashing) and is not guaranteed.

## Visualization (Conceptual)
Suppose we `put` keys in order: "a", "b", "c".

**Hash Map part:**
- `map["a"] -> NodeA`
- `map["b"] -> NodeB`
- `map["c"] -> NodeC`

**Doubly Linked List part:**
`HEAD <--> NodeA <--> NodeB <--> NodeC <--> TAIL`

**Node Structure (Conceptual):**
- `NodeA = {key:"a", value:val_a, prev:HEAD, next:NodeB}`
- `NodeB = {key:"b", value:val_b, prev:NodeA, next:NodeC}`
- `NodeC = {key:"c", value:val_c, prev:NodeB, next:TAIL}`

```tikz
\begin{tikzpicture}[
    hm_slot/.style={draw, rectangle, minimum width=2cm, minimum height=0.8cm, fill=blue!10},
    dll_node/.style={draw, rectangle, minimum width=1.5cm, minimum height=1cm, fill=green!10, rounded corners, text centered},
    ptr/.style={->, >=stealth, thick},
    dll_link/.style={<->, dashed, thick, blue}
]

% HashMap representation (simplified)
\node[hm_slot] (hm_a) at (0,2) {hash("a") $\rightarrow$ (ref to NodeA)};
\node[hm_slot] (hm_b) at (0,1) {hash("b") $\rightarrow$ (ref to NodeB)};
\node[hm_slot] (hm_c) at (0,0) {hash("c") $\rightarrow$ (ref to NodeC)};

% Doubly Linked List representation
\node[dll_node] (dll_head) at (3,1) {HEAD};
\node[dll_node] (node_a) at (5,1) {Node A (key="a")};
\node[dll_node] (node_b) at (7,1) {Node B (key="b")};
\node[dll_node] (node_c) at (9,1) {Node C (key="c")};
\node[dll_node] (dll_tail) at (11,1) {TAIL};

% Links from HashMap to DLL nodes (conceptual)
\draw[ptr, red, dashed, shorten >=2pt, shorten <=2pt] (hm_a.east) .. controls (2,2) and (3,1.5) .. (node_a.north);
\draw[ptr, red, dashed, shorten >=2pt, shorten <=2pt] (hm_b.east) -- (node_b.west);
\draw[ptr, red, dashed, shorten >=2pt, shorten <=2pt] (hm_c.east) .. controls (2,0) and (3,0.5) .. (node_c.south);

% DLL links
\draw[dll_link] (dll_head.east) -- (node_a.west);
\draw[dll_link] (node_a.east) -- (node_b.west);
\draw[dll_link] (node_b.east) -- (node_c.west);
\draw[dll_link] (node_c.east) -- (dll_tail.west);

\node at (5.5, -0.5) [draw, fill=yellow!10, rounded corners, text width=8cm, align=center]
    {LinkedHashMap: Hash Map for $O(1)$ access, Doubly Linked List for maintaining insertion order. Iteration follows the DLL.};

\end{tikzpicture}
```

## 总结 (Summary)
- `LinkedHashMap` combines a hash map and a doubly linked list.
- The hash map provides fast key-based lookups, insertions, and deletions.
- The doubly linked list maintains the entries in insertion order (or access order for LRU).
- Iteration over a `LinkedHashMap` follows the order defined by the linked list.
- This structure is useful when both fast access and predictable iteration order are required, and is a key component in implementing LRU caches.

---
Parent: [[Interview/Concept/Data Structures/Hash Map/00 - Hash Map - Core Principles|Hash Map - Core Principles]]
Next: [[Interview/Concept/Data Structures/Hash Map/04 - ArrayHashMap - Random Key Access|ArrayHashMap]]
Related: [[Interview/Concept/Data Structures/Linked List/index|Doubly Linked Lists]], [[Interview/Concept/Algorithms/Caching/LRU Cache|LRU Cache]]
