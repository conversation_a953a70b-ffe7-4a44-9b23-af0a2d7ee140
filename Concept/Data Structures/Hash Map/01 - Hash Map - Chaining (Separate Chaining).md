---
tags: [concept/data_structures, concept/hash_table, concept/hash_map, concept/collision_resolution, type/implementation_detail]
aliases: [Separate Chaining, HashMap Chaining, 拉链法]
---

> [!NOTE] Source Annotation
> Content adapted from [[labuladong 的算法笔记/markdown_export_本站简介/基础：数据结构及排序精讲/哈希表的原理及实现/用拉链法实现哈希表.md]].
> This note focuses on the Chaining method for hash collision resolution.

# Hash Map: Chaining (Separate Chaining) for Collision Resolution

Chaining is a common technique to resolve hash collisions in [[Interview/Concept/Data Structures/Hash Map/00 - Hash Map - Core Principles|Hash Maps]]. The core idea is that each slot (or bucket) in the hash table's underlying array can store multiple key-value pairs. This is typically achieved by having each slot point to a [[Interview/Concept/Data Structures/Linked List/index|linked list]] of entries that hash to that slot.

## 🔗 Principle of Chaining

1.  **Hash Calculation:** When a key-value pair `(k, v)` is to be inserted, a hash function computes an index `idx = hash(k) % table_capacity`.
2.  **Bucket Access:** The slot `table[idx]` is accessed.
3.  **List Operation:**
    - **Insertion (`put`):**
        - If `table[idx]` is empty (e.g., `null`), a new linked list is created, and `(k, v)` becomes its first element.
        - If `table[idx]` already points to a linked list, traverse this list:
            - If key `k` is found in an existing node, update its value to `v`.
            - If key `k` is not found after traversing the list, append a new node `(k, v)` to the list (often at the beginning for $O(1)$ insertion into the list itself).
    - **Retrieval (`get`):**
        - Access `table[idx]`. If it's empty, the key is not found.
        - Otherwise, traverse the linked list at `table[idx]`, comparing the target key with the key in each node. If a match is found, return its value. If the end of the list is reached without a match, the key is not found.
    - **Deletion (`remove`):**
        - Access `table[idx]`. If it's empty, nothing to delete.
        - Traverse the linked list. If the key is found, remove that node from the list.

**Labuladong's Visual Example Structure (Simplified):**
Suppose `hash(key1) = i`, `hash(key2) = i`, `hash(key3) = j`.
```
Table:
...
table[i] --> [ (key1, val1) ] --> [ (key2, val2) ] --> null
table[i+1] --> null
...
table[j] --> [ (key3, val3) ] --> null
...
```

## Visualization (from Labuladong's article)

The Labuladong article mentions a visualization panel. A static representation:

```tikz
\begin{tikzpicture}[
    bucket/.style={draw, rectangle, minimum height=3cm, minimum width=1.5cm, label=below:#1},
    node_style/.style={draw, rectangle, fill=blue!10, minimum height=7mm, minimum width=1.2cm, rounded corners},
    link/.style={->, >=stealth}
]

% Table buckets
\node[bucket=idx 0] (b0) at (0,0) {};
\node[bucket=idx 1] (b1) at (2,0) {};
\node[bucket=idx 2] (b2) at (4,0) {};
\node (dots) at (5.5,0) {...};
\node[bucket=idx N-1] (bN) at (7,0) {};

% Example: Collision at index 1
% hash(keyA) = 1, hash(keyB) = 1, hash(keyC) = 1
\node[node_style] (n1A) at (2, 1.0) {k_A, v_A};
\node[node_style] (n1B) at (2, 0.0) {k_B, v_B};
\node[node_style] (n1C) at (2, -1.0) {k_C, v_C};

\draw[link] (b1.north) ++(0, -0.2) -- (n1A.north); % Pointer from bucket to head of list
\draw[link] (n1A.south) -- (n1B.north);
\draw[link] (n1B.south) -- (n1C.north);
\draw (n1C.south) -- ++(0,-0.2) node[below] {null};

% Example: Single entry at index 2
\node[node_style] (n2X) at (4, 0.5) {k_X, v_X};
\draw[link] (b2.north) ++(0, -0.2) -- (n2X.north);
\draw (n2X.south) -- ++(0,-0.2) node[below] {null};

\node at (3.5, -2.5) [draw, fill=yellow!10, rounded corners, text width=6cm, align=center]
    {Each table slot points to a linked list of (key, value) pairs that hashed to that slot.};
\end{tikzpicture}
```

## Simplified Implementation Logic (Conceptual Python)

Labuladong provides a simplified implementation focusing on `int` keys and values, and a simple modulo hash function to illustrate core concepts. This helps focus on the chaining mechanism.

```python
class ListNode: # For the linked list in each bucket
    def __init__(self, key, value, next_node=None):
        self.key = key
        self.value = value
        self.next = next_node

class ChainingHashMap:
    def __init__(self, capacity=10): # Simplified fixed capacity
        self.table = [None] * capacity
        self.capacity = capacity
        self.size = 0 # Number of key-value pairs

    def _hash(self, key: int) -> int:
        return key % self.capacity

    def put(self, key: int, value: int):
        index = self._hash(key)
        
        # Traverse the list at table[index]
        current = self.table[index]
        while current:
            if current.key == key:
                current.value = value # Update existing key
                return
            current = current.next
        
        # Key not found, add new node to the front of the list
        new_node = ListNode(key, value, self.table[index])
        self.table[index] = new_node
        self.size += 1
        # Note: Real implementations would check load factor and rehash if needed.

    def get(self, key: int) -> int:
        index = self._hash(key)
        current = self.table[index]
        while current:
            if current.key == key:
                return current.value
            current = current.next
        return -1 # Key not found (Labuladong's simplified return for not found)

    def remove(self, key: int):
        index = self._hash(key)
        current = self.table[index]
        prev = None
        
        while current:
            if current.key == key:
                if prev: # Node to remove is not the head
                    prev.next = current.next
                else: # Node to remove is the head
                    self.table[index] = current.next
                self.size -= 1
                return
            prev = current
            current = current.next
        # Key not found, nothing to remove
```

## 👍 Advantages of Chaining
1.  **Simple to Implement:** Conceptually easier than open addressing.
2.  **Load Factor Can Exceed 1:** Since lists can grow, the number of elements can exceed the table size. However, performance degrades as lists get longer.
3.  **Deletion is Straightforward:** Removing an element from a linked list is easy.

## 👎 Disadvantages of Chaining
1.  **Memory Overhead:** Pointers in linked lists consume extra memory.
2.  **Cache Performance:** Traversing linked lists can lead to poor cache locality, as list nodes might be scattered in memory. Open addressing often has better cache performance due to contiguous probing.
3.  **Performance Degradation:** If hash function is poor or load factor is too high, list lengths increase, and operations degrade towards $O(N)$ in the worst-case (all keys hash to the same slot).

## Performance Considerations
- **Average Case (Good Hash Function, Low Load Factor):** $O(1 + \alpha)$, where $\alpha$ is the load factor. If $\alpha$ is kept small (e.g., constant), this is $O(1)$.
- **Worst Case:** $O(N)$ if all keys hash to the same bucket, effectively turning the hash map into a single linked list.

> [!INFO] Optimization in Java 8+
> Java's `HashMap` optimizes chaining. If a linked list in a bucket becomes too long (e.g., exceeds `TREEIFY_THRESHOLD`, typically 8), it converts the list into a balanced binary search tree (specifically, a Red-Black tree). This improves worst-case lookup in that bucket from $O(L)$ (list length) to $O(\log L)$.

## 总结 (Summary)
- Chaining (Separate Chaining) resolves hash collisions by storing all entries that hash to the same bucket in an independent collection, typically a linked list.
- `put`, `get`, and `remove` operations involve hashing to find the bucket, then operating on the linked list within that bucket.
- It's a conceptually simple and robust collision resolution strategy.
- Performance relies on a good hash function and managing the load factor to keep linked lists short.

---
Parent: [[Interview/Concept/Data Structures/Hash Map/00 - Hash Map - Core Principles|Hash Map - Core Principles]]
Next: [[Interview/Concept/Data Structures/Hash Map/02 - Hash Map - Open Addressing (Linear Probing)|Hash Map - Open Addressing]]
Related: [[Interview/Concept/Data Structures/Linked List/index|Linked Lists]]
